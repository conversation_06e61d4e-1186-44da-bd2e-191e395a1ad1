import ModelFactory from "../model-factory"
let viewId = window.localStorage.getItem("viewId")

// ****************权限组基础接口*******************
// 获取权限组列表
const getPermissionList = code => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/template/auth/group/query-searchPage`,
    method: "post",
    customHeader:{viewId:viewId,code:code}
    //   contentType: 'application/x-www-form-urlencoded',
});
//新增权限组
const addPermission =code=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/template/auth/group/create`,
    method: "post",
    customHeader:{viewId:viewId,code:code}
})
//修改权限组
const updetaPermission=code => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/template/auth/group/update`,
    method: "post",
    customHeader:{viewId:viewId,code:code}
})
//删除权限组
const deletePermission = id => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/template/auth/group/delete/${id.oid}`,
    method: "delete",
    customHeader:{viewId:viewId,code:id.code}
})
// 获取权限列表数据（不带分页）
const getPermisssionData = code =>ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/template/auth/list`,
    method: "post",
    customHeader:{viewId:viewId,code:code}
})
//获取权限列表数据带分页 
const getPermisssionPageList = code => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/template/auth/query-searchPage`,
    method: "post",
    customHeader:{viewId:viewId,code:code}
})
/**
 * type 类型
 * query 查询
 * update 更新
 * create 新增
 */
//权限清单
const savePermisssionList = type =>ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v2/template/auth/permissionConfig/${type} `,
    method: type=='delete'?"delete":"post"
})
//删除权限
const deletePermisssionList = oid => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/template/auth/delete-permissions/${oid.oid}`,
    method: "delete",
    customHeader:{viewId:viewId,code:oid.code}
})
export {
    getPermissionList,
    addPermission,
    updetaPermission,
    deletePermission,
    getPermisssionData,
    getPermisssionPageList,
    savePermisssionList,
    deletePermisssionList
}