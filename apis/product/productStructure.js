import ModelFactory from 'jw_apis/model-factory';

// *************** 产品结构树相关接口 ***************
// 获取产品结构节点树
const fetchProductTree = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/structure/searchTree`,
    method: 'post',
})

// 获取树节点最大层级
const fetchMaxLevel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/structure/maxLevel`,
    method: 'get',
})

// 获取树节点展开层级
const fetchOpenLevel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/structure/getTreeNodeIdsWithLimit`,
    method: 'post',
})

// 获取子节点
const fetchChildNodes = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/structure/searchChildrenByStructure`,
    method: 'get',
})

// 新建产品结构节点
const createProductNode = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/structure/create`,
    method: 'post',
})

// 子节点类型
const fetchChildNodeModel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/model/flapTree`,
    method: 'get',
})

// 重命名产品结构节点
const renameProductNode = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/structure/rename`,
    method: 'post',
})

// 删除产品结构节点
const deleteProductNode = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/structure/delete`,
    method: 'post',
})

// 获取关联对象
const fetchObjects = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/fuzzyPage`,
    method: 'post',
})
const dynamicSearchModel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/dynamic/api/findNodeWithPage`,
    method: 'post'
})

const relationCreationModel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/dynamic/relationship/createMultiWithCheckKey`,
    method: 'post'
})

const relationDeletionModel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/dynamic/api/deleteMultiRelationByOid`,
    method: 'post'
})
const relationSearchModel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/dynamic/relationship/searchSecByRelationNameWithPage`,
    method: 'get'
})

// 为节点添加关联对象
const addRelation = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/structure/batchRel`,
    method: 'post',
})


// *************** 特性清单相关接口 ***************
// 获取查询节点
const fetchStructureNode = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/featuremanage/searchAllStructureNode`,
    method: 'get',
})

// 获取产品节点的特性列表
const fetchFeatures = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/featuremanage/searchFeatures`,
    method: 'get',
})

// 创建特性
const addFeatures = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/featuremanage/createFeature`,
    method: 'post',
})

// 更新特性
const updateFeature = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/featuremanage/updateFeature`,
    method: 'post',
})

// 删除特性
const deleteFeature = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/featuremanage/batchDelFeature`,
    method: 'post',
})

// 创建特性下的标签值
const addLabelValue = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/featuremanage/createLabelValue`,
    method: 'post',
})

// 删除特性下的标签值
const deleteLabelValue = (oid,contextOid) => {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.configMicroService}/featuremanage/deleteLabelValue?oid=${oid}&contextOid=${contextOid}`,
        method: 'post',
    })
}

// 导入特性
const importFeatureFile = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/featuremanage/import`,
    method: 'post',
})

// 导出特性
const exportFeatureFile = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/featuremanage/exportExcel`,
    method: 'post',
})


// *************** 特性值相关接口 ***************
// 获取子节点特性值
const fetchFeatureValue = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/feature/value/search`,
    method: 'get',
})

// 新建特性值
const createFeatureValue = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/feature/value/create`,
    method: 'post',
})

// 更新特性值
const updateFeatureValue = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/feature/value/update`,
    method: 'post',
})

// 删除特性值
const deleteFeatureValue = (oid) => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/feature/value/delete?oid=${oid}`,
    method: 'post',
})

// 获取节点的所有特性
const fetchFeatureByNode = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/featuremanage/searchParentNodeFeatures`,
    method: 'get',
})

// 获取特性对应的特性值
const fetchValueByFeature = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/featuremanage/searchLabelValueByFeature`,
    method: 'get',
})


// *************** 特性规则相关接口 ***************
// 获取规则列表
const fetchRuleList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.ruleEngineServer}/rule/searchByOid`,
    method: 'get',
})

// 获取特性规则列表
const searchByRule = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.ruleEngineServer}/rule/searchByRuleContextOid`,
    method: 'get',
})

// 获取新建规则时的指定节点
const fetchRulesNodes = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/featuremanage/searchAllStructureNode`,
    method: 'get',
})

// 获取新建规则时的特性
const fetchRulesFeatures = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/featuremanage/searchAllFeatureByNode`,
    method: 'get',
})

// 获取新建规则时的特性值
const fetchRulesFValues = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/featuremanage/searchLabelValueByFeature`,
    method: 'get',
})

// 创建普通规则
const createGeneralRules = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/rule/create`,
    method: 'post',
})

// 修改普通规则
const updateGeneralRules = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/rule/update`,
    method: 'post',
})

// 创建高级规则
const createSeniorRules = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.ruleEngineServer}/rule/createDrl`,
    method: 'post',
})

// 修改高级规则
const updateSeniorRules = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.ruleEngineServer}/rule/updateDrl`,
    method: 'post',
})

// 删除规则
const deleteRule = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.ruleEngineServer}/rule/batchDelete`,
    method: 'post',
})

// 启用/启用规则
const batchEnable = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.ruleEngineServer}/rule/batchUpdateRuleEnabel`,
    method: 'post',
})


// *************** 创建实例相关接口 ***************
// 获取创建实例的选配清单
const fetchConditionInstance = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/structure/optionList/build`,
    method: 'get',
})

// 根据选择值动态获取选配单
const fetchOptionalInstance = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/structure/optionList/dynamic/check`,
    method: 'post',
})

// 生成物料清单
const createOptionalInstance = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/structure/matching/calculation`,
    method: 'post',
})

// 生成实例
const createInstance = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/structure/endProduct/generate`,
    method: 'post',
})


// *************** 需求管理相关接口 ***************
// 获取节点的需求列表
const fetchRequirementList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/requirement/search`,
    method: 'get',
})

// 添加节点的需求
const addRequirement = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/requirement/add`,
    method: 'post',
})

// 删除节点的需求
const deleteRequirement = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/requirement/batchDelete`,
    method: 'post',
})

// 更新节点的需求
const updateRequirement = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/requirement/update`,
    method: 'post',
})

// 获取分类
const fetchRequirementType = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.dictServer}/dictValueDefinition/searchByDictCode/requirement_type`,
    method: 'get',
})


// *************** 任务管理相关接口 ***************
// 获取节点的任务列表
const fetchTaskList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/task/search`,
    method: 'get',
})

// 添加任务
const addTask = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/task/add`,
    method: 'post',
})

// 删除任务
const deleteTask = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/task/batchDelete`,
    method: 'post',
})

// 更新任务
const updateTask = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/task/update`,
    method: 'post',
})

const getAllTemplate = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/dynamic/instance/template/getAll`,
    method: "get",
});



export {
    getAllTemplate,
    fetchProductTree,
    fetchMaxLevel,
    fetchOpenLevel,
    fetchChildNodes,
    createProductNode,
    fetchChildNodeModel,
    renameProductNode,
    deleteProductNode,
    fetchObjects,
    dynamicSearchModel,
    relationCreationModel,
    relationDeletionModel,
    relationSearchModel,
    addRelation,

    fetchStructureNode,
    fetchFeatures,
    addFeatures,
    updateFeature,
    deleteFeature,
    addLabelValue,
    deleteLabelValue,
    importFeatureFile,
    exportFeatureFile,

    fetchRuleList,
    searchByRule,
    fetchRulesNodes,
    fetchRulesFeatures,
    fetchRulesFValues,
    createGeneralRules,
    updateGeneralRules,
    createSeniorRules,
    updateSeniorRules,
    deleteRule,
    batchEnable,

    fetchFeatureValue,
    createFeatureValue,
    fetchFeatureByNode,
    fetchValueByFeature,
    updateFeatureValue,
    deleteFeatureValue,

    createOptionalInstance,
    fetchConditionInstance,
    fetchOptionalInstance,
    createInstance,

    fetchRequirementList,
    addRequirement,
    deleteRequirement,
    updateRequirement,
    fetchRequirementType,

    fetchTaskList,
    addTask,
    deleteTask,
    updateTask,
}
