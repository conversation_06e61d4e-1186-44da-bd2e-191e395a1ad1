import ModelFactory from '../model-factory';

// 获取产品库列表
const fetchProductList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomServer}/part/searchProduct`,
    method: 'post',
})
//新建产品库时初始化数据
const createInitializeData =  ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomServer}/part/searchProduct`,
    method: 'post',
})
//将获取到的数据传递给后端获取数据权限
const getDataRueture =  ModelFactory.create({
    url: `${Jw.gateway}//${Jw.permissions}/v1/template/filter/data`,
    method: 'post',
})
export {   
    fetchProductList,
    createInitializeData,
    getDataRueture
}
