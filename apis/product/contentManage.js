import ModelFactory from "jw_apis/model-factory"

//启动工作流--添加(模拟)
const createWorkFlowApi = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.authServer}/authentication/access-token`,
    method: "post",     
})

//获取选择对象列表--(模拟)
const searchObjectListApi = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.authServer}/authentication/access-token`,
    method: "get",     
})

//删除工作流列表--(模拟)
const deleteObjectListApi = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.authServer}/authentication/access-token`,
    method: "get",     
})

//获取工作流列表--(模拟)
const searchWorkListApi = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.authServer}/authentication/access-token`,
    method: "get",     
})


const search = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.docServer}/doc-mgmt/searchDriveWithPage`,
    method: "post",     
})


const searchProduct = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomServer}/part/searchProduct`,
    method: "post",     
})


  // 获取模型列表
  const searchModelList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.workflowServer}/workflow/model/search`,
    method: "post",
  });

  // 工作流审核
  const fetchWorkflowVerify = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baseServer}/workflow/process-definitions/getProcessActList`,
    method: "get",
  });


  //获取对应绑定的模型   
  const searchProcessDef = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baseServer}/workflow/searchProcessDef`,
    method: "get",
  });


  //搜索用户  
  const searchUser = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountServer}/v2/user/searchByKeywordAndPaging`,
    method: "get",
  });


 //启动工作流
 const fetchStartWorkflow = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.workbenchWorkflowService}/workflow/runtime/startProcessInstanceWithCreateRelation`,
    method: "post",
  });

  //批量处理工作流   
  const fetchStartListWorkflow = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.workbenchWorkflowService}/workflow/runtime/startProcessWithMultiModel`,
    method: "post",
  });


//修订时调用的工作流接口
const fetchStartReviseWorkflow = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baseServer}/workflow/searchProcessByName`,
    method: "get",
});


export {
    createWorkFlowApi,
    searchObjectListApi,
    searchWorkListApi,
    deleteObjectListApi,
    search,
    searchProduct,
    searchModelList,
    fetchWorkflowVerify,
    searchProcessDef,
    searchUser,
    fetchStartWorkflow,
    fetchStartListWorkflow,
    fetchStartReviseWorkflow
}