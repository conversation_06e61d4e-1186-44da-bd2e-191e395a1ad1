import ModelFactory from "jw_apis/model-factory"

//查找变更列表(模拟)
const searchChangeListApi = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.authServer}/authentication/access-token`,
    method: "post",     
})


 //获取类型
 const searchBaseType = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.dictServer}/dictValueDefinition/searchByDictCode/ecr-type`,
    method: "get",
  });

  //获取紧急程序
  const searchEmergency = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.dictServer}/dictValueDefinition/searchByDictCode/ecr-emergency`,
    method: "get",
  });

  //获取关联问题
  const searchAssociated = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.issueServer}/v1/issue/fullTextSearch`,
    method: "post",
  });


export {
    searchChangeListApi,
    searchBaseType,
    searchEmergency,
    searchAssociated
}
