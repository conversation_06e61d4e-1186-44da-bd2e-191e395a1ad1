
import ModelFactory from "jw_apis/model-factory"
// ******************************************  ******************************************

// 获取白名单列表
const getIPConfigList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v1/whiteList/search`,
    method: "get",
});

/**
 * 白名单 增、删、改
 * @param {String} type 'create' | 'delete' | 'update'
 * @returns 
 */
 const ipconfig_C_D_U = data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v1/whiteList/${data.type=='delete'?data.type+'/'+data.oid :data.type}`,
    method: data.type == 'delete' ? "delete" : "post",
   
});
//获取白名单开关状态
const getIpconfigStatus= data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v1/whiteList/getWhiteListState`,
    method: "get",
   
});
//更改白名单开关状态
const SetIpconfigStatus = data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v1/whiteList/updateWhiteListState?state=${data.state}`,
    method: "post",
   
});
export {
    getIPConfigList,
    getIpconfigStatus,
    SetIpconfigStatus,
    ipconfig_C_D_U,
}