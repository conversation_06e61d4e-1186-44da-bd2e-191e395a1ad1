
import ModelFactory from "jw_apis/model-factory";
let viewId = window.localStorage.getItem("viewId")

// 获取产品型谱列表
const getList = data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/productCatalog/searchProductCatalogTree`,
    method: "get",
    customHeader: { viewId: viewId }
});

// 新增产品型谱
const addItem = data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/productCatalog/create`,
    method: "post",
    customHeader: { viewId: viewId }
});


// 删除产品型谱
const deleteItem = data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/productCatalog//delete/${data.oid}`,
    method: "post",
    customHeader: { viewId: viewId }
});

// 编辑产品型谱
const updateItem = data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/productCatalog/rename`,
    method: "post",
    customHeader: { viewId: viewId }
});

// 获取产品型谱详情
const getDetails = data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/productCatalog/findDetails`,
    method: "get",
    customHeader: { viewId: viewId }
});


export {
    getList,
    addItem,
    deleteItem,
    updateItem,
    getDetails,
}