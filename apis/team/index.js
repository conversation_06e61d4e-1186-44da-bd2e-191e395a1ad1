import ModelFactory from '../model-factory';

//获取团队列表
// const libraryTableModel = ModelFactory.create({
//   url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/search/all`,
//   method: "get",
// });

const libraryTableModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/pdmFindAllTeam`,
  method: "get",
});

// 新增团队
const createTeamModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/create`,
  method: "post",
});

// 更新团队
const updateTeamModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/update`,
  method: "post",
})

// 删除团队
const deleteTeamModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/delete`,
  method: "post",
})

// 查询当前团队下的角色
const fetchTeamRole = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/templaterole/search/all`,
  method: "get",
})

// 绑定角色
const addRoleModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/assign/create/batch/teamTemplate/teamtemplaterole`,
  method: "post",
});

// 绑定用户
const bindUserModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/assign/add/batch/user/teamtemplaterole`,
  method: "post",
});

// 获取当前角色下的用户
const getRoleUserList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/templaterole/users`,
  method: "get",
});

// 团队另存为
const saveAsModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/saveas`,
  method: "post",
});

export {
  libraryTableModel,
  createTeamModel,
  deleteTeamModel,
  addRoleModel,
  bindUserModel,
  getRoleUserList,
  saveAsModel,
  fetchTeamRole,
  updateTeamModel
}
