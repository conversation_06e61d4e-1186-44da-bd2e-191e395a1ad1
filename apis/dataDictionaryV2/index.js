import ModelFactory from "jw_apis/model-factory";

//列表数 search 搜索
export const getDataConfigList = (search) => {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.customerServer}/dataDictionary/treeList`,
    method: 'get'
  }).execute({search})
}

/**
 * 
 * @param {*} params 
 * @returns 
 * oid
 * code
 * displayName 显示名称
 * enable 是否启用 boolean
 * dataType  (group|dictionary|data)   数据类型只能是group,dictionary,data等值，分组，字典，数据
 * parentCode 上级节点code
 */
//保存接口
export const postDataConfigSubmit = (params) => {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.customerServer}/dataDictionary/save`,
    method: 'post'
  }).execute(params)
}

//删除
export const deleteDataConfig = (oid) => {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.customerServer}/dataDictionary/delete`,
    method: 'get'
  }).execute({oid})
}

//切换启用禁用状态
export const toggleEnable = (oid, enable) => {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.customerServer}/dataDictionary/toggleEnable`,
    method: 'get'
  }).execute({oid, enable})
}

//下载excel模板
export const exportExcelTemp = () => {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.customerServer}/dataDictionary/exportExcelTemp`,
    method: 'get',
    responseType: "blob"
  }).execute()
}

//导出excel，search 搜索条件
export const exportExcel = (search) => {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.customerServer}/dataDictionary/exportExcel`,
    method: 'get',
    responseType: "blob"
  }).execute({search})
}

//导入 file， 文件
export const importExcel = (data) => {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.customerServer}/dataDictionary/importExcel`,
    method: 'post'
  }).execute(data)
}