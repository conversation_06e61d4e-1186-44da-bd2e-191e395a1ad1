import ModelFactory from "jw_apis/model-factory";
const SERVER_DOMAIN = {
    Part: `${Jw.gateway}/${Jw.partBomMicroServer}/part`,
    Document: `${Jw.gateway}/${Jw.docMicroServer}/document`,
    MCAD: `${Jw.gateway}/${Jw.cadService}/mcad`,
    ECAD: `${Jw.gateway}/${Jw.cadService}/ecad`,
    DocumentTemplateMaster: `${Jw.gateway}/${Jw.docMicroServer}/documentTemplate`,
    Delivery: `${Jw.gateway}/${Jw.productDeliveryServer}/delivery`,
  };

  // 文件夹目录
  const fetchfolderTree = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/folder/searchTree`,
    method: "get",
  });

  //批量创建文件夹
  const createFolderTree = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/folder/createTree`,
    method: "post",
  })

const createFolderTreeWithContainerTeam = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.customerServer}/pdm-folder/createTree`,
    method: "post",
})

  // 获取该条数据可操作下拉列表
  const getDropdownList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
    method: "post",
  });
  // 检入
  const part_checkIn = (type) =>
    ModelFactory.create({
      url: `${SERVER_DOMAIN[type]}/checkIn`,
      method: "post",
    });
  // 检出
  const part_checkOut = (type) =>
    ModelFactory.create({
      url: `${SERVER_DOMAIN[type]}/checkOut`,
      method: "post",
    });
  // 撤销检出
  const part_undocheckout = (type) =>
    ModelFactory.create({
      url: `${SERVER_DOMAIN[type]}/cancelCheckOut`,
      method: "post",
    });
  // 移动
  const part_move = (type) =>
    ModelFactory.create({
      url: `${SERVER_DOMAIN[type]}/move`,
      method: "post",
    });
  // 重命名
  const part_rename = (type, oid, name) =>
    ModelFactory.create({
      url: `${SERVER_DOMAIN[type]}/rename?oid=${oid}&name=${name}`,
      method: "post",
    });
  // 另存
  const part_saveAs = (type) =>
    ModelFactory.create({
      url: `${SERVER_DOMAIN[type]}/copy`,
      method: "post",
    });
  // 查询可设置的状态
  const searchStatusList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/lifecycle/findByOid`,
    method: "get",
  });
  // 设置状态
  const part_setStatus = (type) =>
    ModelFactory.create({
      url: `${SERVER_DOMAIN[type]}/setStatus`,
      method: "post",
    });
  // 修订
  const part_revise = (type) =>
    ModelFactory.create({
      url: `${SERVER_DOMAIN[type]}/revise`,
      method: "post",
    });
  // 删除
  const part_delete = (type) =>
    ModelFactory.create({
      url: `${SERVER_DOMAIN[type]}/batchDeleteByOid`,
      method: "post",
    });
  // 获取历史版本列表
  const getHistoryList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/findHistory`,
    method: "get",
  });

  /**
   * 获取版本规则
   */
  const findVersionRule = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/model/assign/findVersionRule`,
    method: 'get'
  })


  // 查询容器下的基线列表
  const getBaselineList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/model/query`,
    method: "post",
  });
  // 创建基线
  const createBaseline = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/model/create`,
    method: "post",
  });
  // 添加对象前 校验
  const checkBeforeLink = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/checkBeforeLink`,
    method: "post",
  });
  // 添加对象
  const batchLink = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/batchLink`,
    method: "post",
  });
  // 搜索对象
  const searchModelTable = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/fuzzyPage`,
    method: "post",
  });

  // 权限查询
  const checkPermission = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
    method: "post",
  });

  // 查询容器下的交付清单
  const getDeliveryLeafNodes = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.productDeliveryServer}/delivery/findLeafNodes`,
    method: "get",
  });

  // 添加实例对象
  const batchAddInstance = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.productDeliveryServer}/delivery/batchAddInstance`,
    method: "post",
  });

//下发设计任务通知接口
const releaseDesignTaskApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/demo/pdm/releaseDesignTask`,
  method: "post",
})

//查询结构
const findMCADSecBom = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.cadService}/mcad/useTree/findSecBom`,
  method: "get",
});

export {
    fetchfolderTree,
    getDropdownList,
    part_checkIn,
    part_checkOut,
    part_undocheckout,
    part_move,
    part_rename,
    part_saveAs,
    searchStatusList,
    part_setStatus,
    part_revise,
    part_delete,
    getHistoryList,
    getBaselineList,
    createBaseline,
    checkBeforeLink,
    batchLink,
    searchModelTable,
    checkPermission,
    getDeliveryLeafNodes,
    batchAddInstance,
    findVersionRule,
    releaseDesignTaskApi,
    findMCADSecBom,
    createFolderTree,
    createFolderTreeWithContainerTeam
}
