/*
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-11 13:40:35
 * @LastEditTime: 2022-04-26 13:45:04
 * @LastEditors: <EMAIL>
 */
import ModelFactory from "jw_apis/model-factory"

// 根据库查询可变更对象
const searchCanChangeInfoBy = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/change/searchCanChangeInfoBy`,
  method: "post"
});

// 获取变更对象的受影响对象
const findInfluenceInfo = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/change/findInfluenceInfo`,
  method: "post"
});

// 校验对象能否加到变更单中
const checkChangeInfo = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.changeServer}/change/checkChange`,
    method: "post"
  });

// 获取变更对象的基本信息
const searchInfoByOidAndModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/change/searchInfoByOidAndModel`,
  method: "post"
});

// 创建变更单
const createChangeApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/change/create`,
  method: "post"
});

// 更新变更单基本信息
const updateEcrBasicInfoApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/change/updateEcrBasicInfo`,
  method: "post"
});

// 查询变更单变更对象
const findChangeInfoApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/change/findChangeInfo`,
  method: "post"
});

// 查询变更单相关对象
const searchRelevantByECRApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/change/searchRelevantByECR`,
  method: "get"
});

// 更新变更单变更对象
const updateChangeInfoApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/change/updateChangeInfo`,
  method: "post"
});

// 获取变更对象的变更方案
const findChangeSchemeApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/change/findChangeScheme`,
  method: "post"
});

// 获取变更对象和变更方案的对比结果
const findChangeSchemeCompareApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/change/findChangeSchemeCompare`,
  method: "post"
});

// 创建方案
const createChangeSchemeApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/change/createChangeScheme`,
  method: "post"
});


// 删除ECR
const deleteECRApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/change/deleteECR`,
  method: "get"
});

// 根据EC类型查询当前EC详细信息
const findWithCrumbs = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/change/findWithCrumbs`,
  method: "get"
});

export {
  searchCanChangeInfoBy,
  findInfluenceInfo,
  checkChangeInfo,
  searchInfoByOidAndModel,
  createChangeApi,
  findChangeInfoApi,
  findChangeSchemeApi,
  updateEcrBasicInfoApi,
  deleteECRApi,
  createChangeSchemeApi,
  updateChangeInfoApi,
  findChangeSchemeCompareApi,
  searchRelevantByECRApi,
  findWithCrumbs
}