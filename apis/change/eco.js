/*
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-22 14:58:37
 * @LastEditTime: 2022-04-26 13:44:53
 * @LastEditors: <EMAIL>
 */
import ModelFactory from "jw_apis/model-factory"

// 创建ECO
const createEcoApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/eco/create`,
  method: "post"
});

// 查询ECo变更对象
const findEcoChangeInfoApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/eco/findChangeInfo`,
  method: "post"
});

// 查询ECo变更对象
const findEcoInfluenceInfoApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/eco/findInfluenceInfo`,
  method: "post"
});

// 更新ECO变更对象
const updateEcoChangeInfoApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/eco/updateChangeInfo`,
  method: "post"
});

// 更新ECO基本信息
const updateECOBasicInfoApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/eco/updateECOBasicInfo`,
  method: "post"
});

// 影响对象变更
const dealInfluenceInfoApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/eco/dealInfluenceInfo`,
  method: "post"
});

//ECO分发页面查询变更对象
const findSendChangeInfoApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/eco/findSendChangeInfo`,
  method: "get"
});

//ECO分发页面查询变更对象
const searchRelevantByECOApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/eco/searchRelevantByECO`,
  method: "get"
});

export {
  createEcoApi,
  findEcoChangeInfoApi,
  findEcoInfluenceInfoApi,
  updateEcoChangeInfoApi,
  dealInfluenceInfoApi,
  updateECOBasicInfoApi,
  findSendChangeInfoApi,
  searchRelevantByECOApi,
}