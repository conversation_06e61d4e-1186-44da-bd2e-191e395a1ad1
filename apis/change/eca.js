/*
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-22 17:33:14
 * @LastEditTime: 2022-04-26 13:44:29
 * @LastEditors: <EMAIL>
 */
import ModelFactory from "jw_apis/model-factory"

// 创建ECA
const createEcaApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/eca/create`,
  method: "post"
});

// 更新ECA的基本信息
const updateECABasicInfoApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/eca/updateECABasicInfo`,
  method: "post"
});

//查找ECA的变更对象
const findEcaChangeInfoApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/eca/findChangeInfo`,
  method: "post"
});

//确认ECA
const confirmChangeApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/eca/confirmChange`,
  method: "get"
});

export{
  createEcaApi,
  findEcaChangeInfoApi,
  updateECABasicInfoApi,
  confirmChangeApi,
}