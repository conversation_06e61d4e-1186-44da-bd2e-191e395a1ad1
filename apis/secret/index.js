import ModelFactory from "jw_apis/model-factory"
let viewId = window.localStorage.getItem("viewId")

//获取密级列表
const getSecretList =code=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.securityService}/secret/search`,
    method: "get",
    customHeader:{viewId:viewId,code:code}
})

//保存|新建 密级配置
const saveSecret = code => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.securityService}/secret/save`,
    method: "post"   
})
//删除配置
const deleteSecret = oid => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.securityService}/secret/delete/${oid.oid}`,
    method: "delete",
    
    
})
//获取密级配置筛选列表
const getSecretSet = oid => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.securityService}/secret/updateSys/${oid.oid}`,
    method: "post",
    customHeader:{viewId:viewId,code:oid.code}     
})
//启用三元管理
const threeEnable  = oid => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v1/role/enableTernaryUser`,
    method: "post",
    customHeader:{viewId:viewId,code:oid}     
})
//获取三元管理的状态
const threStatus = oid => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v1/role/findTernaryUserState`,
    method: "post",
    customHeader:{viewId:viewId,code:oid}     
})
// 禁用三元管理
const disableEnable = oid => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v1/role/disableTernaryUser`,
    method: "post",
    customHeader:{viewId:viewId,code:oid}     
})

//获取三元管理的设置状态
const getEnableStatus = data=>ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v1/role/findTernaryUserState`,
    method: "post",
    customHeader:{viewId:viewId,code:oid}     
})



export {
    getSecretList,
    saveSecret,
    deleteSecret,
    getSecretSet,
    threeEnable,
    disableEnable,
    getEnableStatus,
    threStatus
}