import ModelFactory from "jw_apis/model-factory";

// 创建
export function create(data){
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/effectivity/create`,
    method: 'post'
  }).execute(data)
}

// 修改
export function update(data){
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/effectivity/update`,
    method: 'post'
  }).execute(data)
}

// 删除
export function deleted(oid){
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/effectivity/deleted?oid=${oid}`,
    method: 'get'
  }).execute()
}

// 查询partbom的有效性配置
export function findByPartBomOid(partbomOid){
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/effectivity/findByPartBomOid?partbomOid=${partbomOid}`,
    method: 'get'
  }).execute()
}

//批量查询历史有效性
export function findBatchPartOids(list){
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/effectivity/findBatchPartOids`,
    method: 'post'
  }).execute(list)
}

