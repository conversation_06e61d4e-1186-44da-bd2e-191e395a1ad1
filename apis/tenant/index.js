import ModelFactory from "jw_apis/model-factory"
let viewId = window.localStorage.getItem("viewId")

//获取三员管理的列表
const getThreeUserRole = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v1/role/getThreeAdminIp`,
    method: "get", 
    customHeader:{viewId:viewId}
})
//绑定三员IP地址
const updateThreeAdminIp = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v1/role/updateThreeAdminIp`,
    method: "post", 
    customHeader:{viewId:viewId}
})
//删除三员IP地址
const deleteThreeAdminIp =data=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v1/role/deleteThreeAdminIp/${data.oid}`,
    method: "delete", 
    customHeader:{viewId:viewId}
})
//创建IP绑定
const createThreeAdminIp = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v1/role/createThreeAdminIp`,
    method: "post", 
    customHeader:{viewId:viewId}
})

// 禁用三元管理
const disableEnable = oid => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/role/disableTernaryUser`,
    method: "post",
    customHeader:{viewId:viewId,code:oid}     
})

//启用三元管理
const threeEnable  = oid => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v1/role/enableTernaryUser`,
    method: "post",
    customHeader:{viewId:viewId,code:oid}     
})
//获取三元管理的状态
const threStatus = oid => ModelFactory.create({
    // url: `${Jw.gateway}/${Jw.permissionServer}/role/findTernaryUserState`,
    url: `${Jw.gateway}/${Jw.permissionServer}/v1/role/findTernaryUserState`,
    method: "post",
    customHeader:{viewId:viewId,code:oid}     
})
export{
    getThreeUserRole,
    updateThreeAdminIp,
    createThreeAdminIp,
    deleteThreeAdminIp,
    disableEnable,
    threeEnable,
    threStatus
}