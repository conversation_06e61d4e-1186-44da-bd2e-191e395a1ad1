import ModelFactory from "../model-factory"
import { getCookie } from "jw_utils/cookie"

/** 新建权限模版 type值为⬇
 *  update (修改)  
 *  create（新增） 
 *  delete（删除）*/
const secretTemplate = type => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/template/data/${type.type}${type.type == 'delete' ? '': ''}`,
    method: `${type.type==='delete'?'delete':'post'}`,
    customHeader:{viewId:viewId,code:type.code}
})

//权限模版列表
const getTemplateList =code=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/template/data/list`,
    method: "post",
    customHeader:{viewId:viewId,code:code}
})

//数据权限列表
const getDataList = code => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/template/data/query-data-by-template`,
    method: "post",
    customHeader:{viewId:viewId,code:code}
})
//查找子级的对象类型
const getFatherType =code=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baseServer}/meta/model/readAllParentModel`,
    method: 'get',
    customHeader:{viewId:viewId,code:code}
})
//查找子级的对象类型
const getChildrenType= oid => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baseServer}/meta/modelTomodel/searchChildModelByOid/${oid.oid}`,
    method: 'get',
    customHeader:{viewId:viewId,code:oid.code}
})

//获取权限组列表 
const groupArrList =code => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/template/auth/group/list`,
    method: "get",
    customHeader:{viewId:viewId,code:code}
})
/** 数据权限列表操作
 * create 新增
 * update 修改
 * delete 删除
 */
 const operationDataList =data=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/template/data/${data.type}-data-permissions`,
    method: "post",
    customHeader:{viewId:viewId,code:data.code}
})
//根据模型对象获取生命周期
const modalObjectDate =name=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/template/data/query-lifecycle-template-by-name/${name.name}`,
    method: "get",
    customHeader:{viewId:viewId,code:name.code}
})
//根据模版生命周期去查找对应的状态列表
const modalStatusList = oid=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baseServer}/lifecycle/template/searchByOid/%7Boid%7D?oid=${oid.data}`,
    method: "get",
    customHeader:{viewId:viewId,code:oid.code}
})
//删除数据权限 
const  deleteDataCreater =data=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/template/data/delete-data-permissions-by-id`,
    method: "delete",
    customHeader:{viewId:viewId,code:data}
})
//根据id集合去查询权限组的权限列表
const  getGroupListPromisson =data=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/template/auth/group/query-permission-by-group-ids`,
    method: "post",
    customHeader:{viewId:viewId,code:data}
})

//根据对象类型的父级去查询子集的数据

const getParentList = data=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/template/data/get-bom-parent-permissions?modelName=${data.name}&permissionsTemplateId=${data.templateId}&lifecycleStateLabel=${data.label||''}`,
    method: "get",
    customHeader:{viewId:viewId,code:data.code}
})







/**
 * 重构后的接口
 */
//获取角色列表
const getUserList = code => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountMicroServer}/role/search/cp/keyword/page`,
    method: 'post',
})

//获取生命周期状态
const getLifeStatus = code => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/lifecycle/status/enable/fuzzy`,
    method: 'get',
})

//模糊查询所有顶点类型
const getFuzzy = code => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/model/fuzzy`,
    method: 'get',
})

//通过父模型oid查询
const getFuzzyByFrom = code => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/model/fuzzyByFrom`,
    method: 'get',
})

//新建权限策略保存
const savePermission = code => ModelFactory.create({
    url: `http://**************:19442/permission-service/v1/permission/policy/create`,
    method: 'post',
})

//根据分页获取权限策略
const getPagePermission = code => ModelFactory.create({
    url: `http://**************:19442/permission-service/v1/permission/policy/query`,
    method: 'post',
})

//修改权限策略
const updatePermission = code => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v1/permission/policy/update`,
    method: 'post',
})

//删除权限策略
const  deletePermission = code => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v1/permission/policy/delete`,
    method: 'delete',
})
//权限策略种权限清单包含关系
const  relationshipContains = code => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v2/template/auth/permissionConfig/queryPermissionMap`,
    method: 'get',
})
export {
    secretTemplate,
    getTemplateList,
    getDataList,
    operationDataList,
    groupArrList,
    getFatherType,
    getChildrenType,
    modalObjectDate,
    modalStatusList,
    deleteDataCreater,
    getGroupListPromisson,
    getParentList,
    //重构后
    getUserList,
    getLifeStatus,
    getFuzzy,
    getFuzzyByFrom,
    savePermission,
    getPagePermission,
    updatePermission,
    deletePermission,
    relationshipContains
}