/**
 * 创建Model 工厂
 *
 * <AUTHOR>
 * @date 2018-12-03
 */

import inherit from 'jw_common/inherit'
import AbstractModel from 'jw_apis/abstract'

export default {

  create({
    url,
    param,
    method,
    dataFormator,
    contentType,
    customHeader
  }) {

    let modelInstance = new (inherit(AbstractModel,{
      initialization() {

        method && (this.type = method)
        contentType && (this.contentType = contentType)
        this.url = url
        this.oringalUrl = url
      },

      initParam(param) {

        if(param) {

          if(this.getType() === 'GET') {
            this.setQuery(param)
          }else{
            this.setParam(param)
          }
        }
      },

      dataFormat(data) {
        if(_.isFunction(dataFormator)) {
          return dataFormator(data)
        }

        return data.result || data.data
      },

      execute(param,isCached) {

        this.initParam(param)
        return this.exec(isCached)
      },

      executeRestful(restfulApi, isCached) {

        this.url = this.oringalUrl + restfulApi
        return this.exec(isCached)
      },
      getExtraHeaders(){
        return Object.assign({},customHeader)
      }
    }))

    modelInstance.initParam(param)
    return modelInstance
  },

  createForm({
    url,
    param,
    method,
    dataFormator
  }) {

    return this.create({
      url,
      param,
      method,
      dataFormator,
      contentType: AbstractModel.APPLICATION_URLENCODED
    })
  }
}
