
import ModelFactory from "../model-factory"
let viewId = window.localStorage.getItem("viewId")

// 获取结构树列表
const getList =data=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.dictServer}/group/search`,
    method: "post",
    customHeader:{viewId:viewId}
});

// // 新增结构树
// const addItem =data=> ModelFactory.create({
//     url: `${Jw.gateway}/productCatalog/product/create`,
//     method: "post",
//     customHeader:{viewId:viewId}
// });


// // 删除结构树
// const deleteItem =data=> ModelFactory.create({
//     url: `${Jw.gateway}/productCatalog/product/deleteByOid`,
//     method: "post",
//     customHeader:{viewId:viewId}
// });

// // 编辑结构树
// const updateItem =data=> ModelFactory.create({
//     url: `${Jw.gateway}/productCatalog/product/rename`,
//     method: "post",
//     customHeader:{viewId:viewId}
// });

// // 获取结构树详情
// const getDetails =data=> ModelFactory.create({
//     url: `${Jw.gateway}/productCatalog/product/findDetails`,
//     method: "post",
//     customHeader:{viewId:viewId}
// });


export {
    getList,
    // addItem,
    // deleteItem,
    // updateItem,
    // getDetails,
}