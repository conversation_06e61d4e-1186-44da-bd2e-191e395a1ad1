

import ModelFactory from "../model-factory"
let viewId = window.localStorage.getItem("viewId")
// ****************************************** 视图 ******************************************
// 获取视图列表
const getViewList =data=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/viewPermission/list`,
    method: "get",
    customHeader:{viewId:viewId}
    //   contentType: 'application/x-www-form-urlencoded',
});

// 根据 oid 获取 视图详情
const getViewDetail = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/viewPermission/searchById`,
    method: "get",
    customHeader:{viewId:viewId}
    //   contentType: 'application/x-www-form-urlencoded',
});

/**
 * 视图 增、删、改
 * @param {String} data 'create' | 'delete' | 'update'
 * @returns 
 */
const view_C_D_U = data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/viewPermission/${data.type=='delete'?'delete/'+data.oid:data.type}`,
    method:data.type=="delete"?"delete":"post",
    customHeader:{viewId:viewId,code:data.code}
    //   contentType: 'application/x-www-form-urlencoded',
});


// ****************************************** 功能模块 ******************************************

/**
 * 功能模块 增、删、改
 * @param {String} data 'create' | 'delete' | 'update'
 * @returns 
 */
 const viewGroup_C_D_U = data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/viewGroup/${data.type}${data.type == 'delete' ? '/' + data.oid : ''}`,
    method: data.type=="delete"?"delete":"post",
    customHeader:{viewId:viewId,code:data.code}
    //   contentType: 'application/x-www-form-urlencoded',
});


// ****************************************** 菜单按钮 ******************************************

// 根据 视图oid 获取 功能模块（菜单）-按钮 列表
const getViewGroupList =data=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/menuPermission/menuList`,
    method: "get",
    customHeader:{viewId:viewId,code:data}
    //   contentType: 'application/x-www-form-urlencoded',
});

// 分页、关键词 搜索 功能模块-菜单 列表
const searchModuleMenuList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/menuPermission/search`,
    method: "post",
    customHeader:{viewId:viewId}
   //   contentType: 'application/x-www-form-urlencoded',
});

/**
 * 菜单按钮 增、删、改
 * @param {String} type 'create' | 'delete' | 'update'
 * @returns 
 */
const menu_C_D_U = data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/menuPermission/${data.type}${data.type == 'delete' ? '/'+ data.oid : ''}`,
    method: data.type=="delete"?"delete":"post",
    customHeader:{viewId:viewId,code:data.code}
    //   contentType: 'application/x-www-form-urlencoded',
});

//获取视图id
const getViewPromissonId = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/viewPermission/search`,
    method: "get",
    customHeader:{viewId:viewId}
});
//视图权限获取用户所拥有的菜单列表
const getUserMenuPromisson = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/viewPermission/allMenu`,
    method: "get",
    customHeader:{viewId:window.localStorage.getItem('viewId')}
});

//获取用户的按钮菜单
const getUserPermission= view => ModelFactory.create({
    // url: `${Jw.gateway}/${Jw.permissions}/v1/user/auth/details`,
    url: `${Jw.gateway}/${Jw.permissions}/v1/viewPermission/allMenuButton?viewId=${viewId}`,
    method: "get",
    customHeader:{viewId:viewId}
});

const getUserDetails= view => ModelFactory.create({
    // url: `${Jw.gateway}/${Jw.permissions}/v1/user/auth/details`,
    url: `${Jw.gateway}/${Jw.permissions}/v1/user/auth/details?viewId=${viewId}`,
    method: "get",
    customHeader:{viewId:viewId}
});

//修改视图排序
const updataSortData= data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/viewPermission/updateSort?type=${data.type}&sort=${data.sort}&oid=${data.oid}`,
    method: "post",
    customHeader:{viewId:viewId,code:data.code}
});

//删除视图模版


//编辑视图模版
export {
    getViewList,
    getViewDetail,
    view_C_D_U,
    viewGroup_C_D_U,
    menu_C_D_U,
    getViewGroupList,
    getUserMenuPromisson,
    getViewPromissonId,
    getUserPermission,
    updataSortData,
    getUserDetails
}