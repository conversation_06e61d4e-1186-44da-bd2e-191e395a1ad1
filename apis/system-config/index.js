import ModelFactory from '../model-factory';

//查询工具端模型属性
export const mappingList = (modelCode) => {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.sysconfigServer}/model/attribute/mapping/query`,
    method: 'post'
  }).execute({businessType: 'CreateMCAD', modelBCode: modelCode})
}

//保存属性映射
export const savePropMapping = (param) => {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.sysconfigServer}/model/attribute/mapping/save`,
    method: 'post'
  }).execute(param)
}