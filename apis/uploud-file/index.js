import ModelFactory from "../model-factory"
let viewId = window.localStorage.getItem("viewId")

//导入文件
const uploadFile = code=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.ieconfigService}/import/importExcelWithCheck?code=${code}`,
    method: "post",
    customHeader:{viewId:viewId}
})
//导出文件
const downloadFile = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/featuremanage/exportExcel`,
    method: "post",
    customHeader:{viewId:viewId}
})

//角色导入
const userUploadFile = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.configMicroService}/featuremanage/import`,
    method: "post",
    customHeader:{viewId:viewId}
})
export{
    uploadFile,
    userUploadFile,
    downloadFile
}