import ModelFactory from "../model-factory"
let viewId = window.localStorage.getItem("viewId")

// 分页查询密码策略
const getPasswordStrategy = code => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.securityService}/passwordPolicy/list`,
    method: "get",
    customHeader:{viewId:viewId,code:code}
    //   contentType: 'application/x-www-form-urlencoded',
});
//保存密码策略
const savePassword = data=>ModelFactory.create({
    url: `${Jw.gateway}/${Jw.securityService}/passwordPolicy/save`,
    method: "post",
    customHeader:{viewId:viewId,code:data}
    //   contentType: 'application/x-www-form-urlencoded',
});
//更新密码策略
const updatePassword = data=>ModelFactory.create({
    url: `${Jw.gateway}/${Jw.securityService}/passwordPolicy/save`,
    method: "post",
    customHeader:{viewId:viewId,code:data}
    //   contentType: 'application/x-www-form-urlencoded',
});
export {
    getPasswordStrategy,
    savePassword,
    updatePassword
}