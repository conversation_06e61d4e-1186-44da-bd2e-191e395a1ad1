import ModelFactory from "jw_apis/model-factory";

//创建
export const create = function(data){
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/effectivityDefinition/create`,
    method: 'post'
  }).execute(data)
}

//修改
export const update = function(data){
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/effectivityDefinition/update`,
    method: 'post'
  }).execute(data)
}

//查询当前容器下的有效性规则
export const findByContainerOid = function (containerOid){
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/effectivityDefinition/findByContainerOid?containerOid=${containerOid}`,
    method: 'get'
  }).execute()
}

//删除
export const deleted = function(oid){
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/effectivityDefinition/deleted?oid=${oid}`,
    method: 'post'
  }).execute({oid: oid})
}