import ModelFactory from 'jw_apis/model-factory';

// 产品容器列表
const fetchContainerList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/container/product/search`,
    method: 'post',
})
// 新增产品容器列表
const createContainer = ModelFactory.create({
    url: `${Jw.gateway}/customer/customerContainer/product/create`,///${Jw.gateway}/${Jw.containerService}/container/product/create
    method: 'post',
})

// 删除产品容器
const deleteContainer = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/container/product/batchDelete`,
    method: 'post',
})

// 更新产品容器
const updateContainer = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/container/product/update`,
    method: 'post',
})

// 产品容器详情
const detailContainer = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/container/product/findDetail`,
    method: 'get',
})

// 产品容器模板列表
const fetchContainerTemplate = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/containerTemplate/fuzzySearch`,
    method: 'post',
})

// 型谱列表
const fetchSpectrumTree = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/productCatalog/searchProductCatalogTree`,
    method: 'get',
})

//获取团队列表
// const libraryTableModel = ModelFactory.create({
//     url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/search/all`,
//     method: "get",
// });

const libraryTableModel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountMicroServer}/team/pdmFindAllTeam`,
    method: "get",
});

// 新增团队
const createTeamModel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/create`,
    method: "post",
});

// 更新团队
const updateTeamModel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/update`,
    method: "post",
})

// 删除团队
const deleteTeamModel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/delete`,
    method: "post",
})

// 查询当前团队下的角色
const fetchTeamRole = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/team/searchTeamRole`,
    method: "get",
})

// 查询当前团队下的角色
const getTeamRole = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.containerService}/team/fuzzyContent?teamOid=${params.oid}&searchKey=${params.searchKey}`,
        method: "get",
    }).execute()
}
// 绑定角色
const addRoleModel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/team/bindRole`,
    method: "post",
});

// 绑定用户
const bindUserModel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/team/bindUser`,
    method: "post",
});

// 获取当前角色下的用户
const getRoleUserList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountMicroServer}/team/templaterole/users`,
    method: "get",
});

// 团队另存为
const saveAsModel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/saveas`,
    method: "post",
});


export {
    fetchContainerList,
    createContainer,
    fetchContainerTemplate,
    fetchSpectrumTree,
    deleteContainer,
    detailContainer,
    updateContainer,
    libraryTableModel,
    createTeamModel,
    deleteTeamModel,
    addRoleModel,
    bindUserModel,
    getRoleUserList,
    saveAsModel,
    fetchTeamRole,
    getTeamRole,
    updateTeamModel
}
