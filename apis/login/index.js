import ModelFactory from "jw_apis/model-factory"

//手机号登录
const phoneLoginlApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.authServer}/authentication/loginByPhone`,
  method: "post",
  contentType: 'application/x-www-form-urlencoded',
})

//发送验证码
const sendCodelApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountServer}/v2/user/sms/getAuthCodeByPhone`,
  method: "get",
})

//账号登录
const accoutLoginApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.authServer}/authentication/login`,
  method: "post",
  contentType: 'application/x-www-form-urlencoded',
})






export {
  phoneLoginlApi,
  sendCodelApi,
  accoutLoginApi,
}