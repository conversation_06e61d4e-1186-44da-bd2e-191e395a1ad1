import ModelFactory from "jw_apis/model-factory"

// 任务创建
export function jobCreate(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/task/create`,
    method: 'post'
  }).execute(data)
}

// 任务创建
export function jobUpdate(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/task/update`,
    method: 'post'
  }).execute(data)
}
// 任务删除
export function jobDelete(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/task/delete`,
    method: 'get'
  }).execute(data)
}

// 获取任务详情
export function getJobDetail(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/task/findDetail`,
    method: 'get'
  }).execute(data)
}
// 查询迁移任务实例的数据迁移规则配置
export function findDataMgRuleConf(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/task/findDataMgRuleConf`,
    method: 'get'
  }).execute(data)
}


// 任务列表
export function jobFindList(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/task/find`,
    method: 'get'
  }).execute(data)
}
// 执行迁移
export function jobExecute(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/task/migrate/actuate?oid=${data.oid}`,
    method: 'post'
  }).execute({})
}
// 错误处理
export function exceptionHandling(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/task/migrate/failedBack?oid=${data.oid}`,
    method: 'post'
  }).execute({})
}



