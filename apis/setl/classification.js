import ModelFactory from "jw_apis/model-factory"

// 分类创建
export function classificationCreate(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/task/classification/create`,
    method: 'post'
  }).execute(data)
}

// 分类创建
export function classificationUpdate(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/task/classification/update`,
    method: 'post'
  }).execute(data)
}
// 分类删除
export function classificationDelete(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/task/classification/delete`,
    method: 'get'
  }).execute(data)
}
// 分类列表
export function classificationFindList(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/task/classification/find`,
    method: 'post'
  }).execute(data)
}

// 模糊搜索列表
export function classificationFuzzy(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/task/classification/fuzzy`,
    method: 'get'
  }).execute(data)
}

// 分类详情
export function classificationFindByOid(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/task/classification/findByOid`,
    method: 'get'
  }).execute(data)
}



