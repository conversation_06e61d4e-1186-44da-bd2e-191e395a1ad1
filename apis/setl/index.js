import ModelFactory from "jw_apis/model-factory"
// http://iamgateway.dev.jwis.cn/swagger-ui.html?urls.primaryName=setl-server#/
// 获取源系统信息选项
export function findSupportedSourceSys(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/sys/supported/sourceSysName`,
    method: 'get'
  }).execute(data)
}
// 获取源系统版本选项
export function findSupportedSourceSysVersion(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/sys/supported/sourceSysVersion`,
    method: 'get'
  }).execute(data)
}
// 获取目标系统信息选项
export function findSupportedTargetSysName(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/sys/supported/targetSysName`,
    method: 'get'
  }).execute(data)
}
// 获取目标系统版本选项
export function findSupportedTargetSysVersion(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/sys/supported/targetSysVersion`,
    method: 'get'
  }).execute(data)
}
// 获取数据库信息选项
export function findSupportedDatabaseName(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/sys/supported/databaseName`,
    method: 'get'
  }).execute(data)
}
// 获取数据库版本选项
export function findSupportedDatabaseVersion(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/sys/supported/databaseVersion`,
    method: 'get'
  }).execute(data)
}

// 获取租户选项
export function findSupportedTenant(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/sys/supported/operate/findTenant`,
    method: 'post'
  }).execute(data)
}

// 获取容器选项
export function findSupportedContainer(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/sys/supported/operate/findContainer`,
    method: 'post'
  }).execute(data)
}
// 测试数据库链接
export function checkDbLink(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/sys/supported/operate/checkDbLink`,
    method: 'post'
  }).execute(data)
}



export * from './classification'
export * from './migrationScripts'
export * from './job'
export * from './log'


