import ModelFactory from "jw_apis/model-factory"

// 迁移脚本创建
export function migrationScriptCreate(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/script/mgmt/create`,
    method: 'post'
  }).execute(data)
}
// 迁移脚本更新
export function migrationScriptUpdate(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/script/mgmt/update`,
    method: 'post'
  }).execute(data)
}
// 迁移脚本详情
export function getMigrationScriptInfo(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/script/mgmt/findByOid`,
    method: 'get'
  }).execute(data)
}

// 迁移脚本删除
export function migrationScriptDelete(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/script/mgmt/delete`,
    method: 'get'
  }).execute(data)
}

// 迁移脚本列表
export function getMigrationScriptList(data) {
  return ModelFactory.create({
    url: `${Jw.service}/${Jw.setlServer}/script/mgmt/findByMgTaskClsOid`,
    method: 'get'
  }).execute(data)
}

