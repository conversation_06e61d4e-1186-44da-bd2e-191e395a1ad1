import ModelFactory from "../model-factory"
let viewId = window.localStorage.getItem("viewId")


//获取Access-token
const fetchAccessToken = ModelFactory.create({
url: `${Jw.gateway}/${Jw.authServer}/authentication/access-token`,
method: "get", 
customHeader:{viewId:viewId}

})

//获取用户信息
const fetchCurrentUser = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountServer}/v2/user/searchByOid`,
      method: "get",
      customHeader:{viewId:viewId}
    })


//修改用户信息
const updateApi = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountServer}/v2/user/update`,
      method: "post",
      customHeader:{viewId:viewId}
})

// 获取用户列表
const userListApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountServer}/v2/user/searchByKeywordAndPaging`,
  method: "get",
  customHeader:{viewId:viewId}
});

// 添加用户到群组中
const updateUserApi = ModelFactory.create({
  url: `${Jw.gateway}//${Jw.baseServer}/dynamic/instance/team/addUserToGroup`,
  method: "post",
  customHeader:{viewId:viewId}
});

// 从群组中删除用户
const deleteUserApi = ModelFactory.create({
  url: `${Jw.gateway}//${Jw.baseServer}/dynamic/instance/team/deleteUserFormGroup`,
  method: "post",
  customHeader:{viewId:viewId}
});

// 组别列表
const fetchGroupListApi = ModelFactory.create({
  url: `${Jw.gateway}//${Jw.baseServer}/group/search`,
  method: "get",
  customHeader:{viewId:viewId}
})

// 
const updateGroupUserApi = ModelFactory.create({
  url: `${Jw.gateway}//${Jw.baseServer}/dynamic/instance/team/addGroup`,
  method: "post",
  customHeader:{viewId:viewId}
})

const deleteGroup = ModelFactory.create({
  url: `${Jw.gateway}//${Jw.baseServer}/dynamic/instance/team/deleteGroup`,
  method: 'post'
})


export {
    fetchAccessToken,
    fetchCurrentUser,
    updateApi,
    userListApi,
    updateUserApi,
    deleteUserApi,
    fetchGroupListApi,
    updateGroupUserApi,
    deleteGroup
}