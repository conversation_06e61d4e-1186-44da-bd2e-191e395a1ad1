import ModelFactory from "jw_apis/model-factory"
// let viewId = window.localStorage.getItem("viewId")

//查询用户列表
const getSearchSecretlist = code => ModelFactory.create({
    url:`${Jw.gateway}/${Jw.accountMicroServer}/user/search/keyword/page`,
    method: "get",
})

//修改用户密级
const updetaSecret = code => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountMicroServer}/user/update`,
    method: "post",
    // customHeader:{viewId:viewId,code:code}     
})

//设置系统密级
const getSecretSeystem = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v1/secret/findBySys`,
    method: "get",
    // customHeader:{viewId:viewId}     
})

//获取用户密级
const configDefaultSecret = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/v1/secret/findByDef`,
    method: "get",
    // customHeader:{viewId:viewId}  /   
})
export {
    getSearchSecretlist,
    updetaSecret,
    getSecretSeystem,
    configDefaultSecret
}
