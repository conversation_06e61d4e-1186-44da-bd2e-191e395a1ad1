
import ModelFactory from "../model-factory"
let viewId = window.localStorage.getItem("viewId")

// 获取产品模板列表
const getList =data=> ModelFactory.create({
    // url: `${Jw.gateway}/${Jw.containerCatalogService}/container/fuzzySearch`,
    url: `${Jw.gateway}/${Jw.containerService}/containerTemplate/fuzzySearch`,
    method: "post",
    customHeader:{viewId:viewId}
});

// 新增产品模板
const addItem =data=> ModelFactory.create({
    // url: `${Jw.gateway}/${Jw.containerCatalogService}/container/create`,
    url: `${Jw.gateway}/${Jw.containerService}/containerTemplate/create`,
    method: "post",
    customHeader:{viewId:viewId}
});


// 删除产品模板
const deleteItem =data=> ModelFactory.create({
    // url: `${Jw.gateway}/${Jw.containerCatalogService}/container/deleteTemplate/${data.oid}`,
    url: `${Jw.gateway}/${Jw.containerService}/containerTemplate/deleteTemplate/${data.oid}`,
    method: "post",
    customHeader:{viewId:viewId}
});

// 编辑产品模板
const updateItem =data=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/containerTemplate/update`,
    method: "post",
    customHeader:{viewId:viewId}
});


export {
    getList,
    addItem,
    deleteItem,
    updateItem,
}