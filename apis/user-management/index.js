import ModelFactory from "jw_apis/model-factory"
let viewId = window.localStorage.getItem("viewId")

//获取角色列表
const getRoleList= data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/group/search`,
    method: "get",
    customHeader:{viewId:viewId,code:data.code}
})
//获取角色列表
const getUserList= data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/group/searchByOid?oid=${data.oid}&page=${data.page}&size=${data.size}&searchKey=${data.searchKey}`,
    method: "get",
    customHeader:{viewId:viewId,code:data.code}
})     
//获取角色列表
const getsearchUserList= data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/group/searchUser?oid=${data.oid}&page=${data.page}&size=${data.size}&searchKey=${data.searchKey}`,
    method: "get",
    customHeader:{viewId:viewId,code:data.code}
})
//获取用户列表
const getUserListData= data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountServer}/v2/user/searchByKeywordAndPaging`,
    method: "get",
    customHeader:{viewId:viewId,code:data.code}
})  
//为角色分配用户
const setUserData= data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baseServer}/groupLink/create`,
    method: "post",
    customHeader:{viewId:viewId,code:data.code}
})  
//获取角色里的用户
const deleteUserData= data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baseServer}/groupLink/delete`,
    method: "post",
    customHeader:{viewId:viewId,code:data.code}
}) 

//创建角色
const createUserData= data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/group/create`,
    method: "post",
    customHeader:{viewId:viewId,code:data.code}
}) 

//删除角色
const deleteUser= data => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/group/delete/${data[0]}`,
    method: "delete",
    customHeader:{viewId:viewId,code:data.code}
}) 
//修改角色
const updateUser = data =>ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissions}/v1/group/update`,
    method: "post",
    customHeader:{viewId:viewId,code:data}
}) 


/**
 * 重构最新API接口获取角色
 */
//获取角色列表/${Jw.permissionServer}/viewPermission/search ///${Jw.permissionServer}/viewPermission/search?page=1&size=10&searchKey=
const getDataListArr =data=>ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountMicroServer}/role/search/cp/keyword/page`,
    // url: `${Jw.gateway}/${Jw.permissionServer}/viewPermission/search?page=1&size=10&searchKey=`,
    // method: "get"
    method: "post"
}) 

//创建角色
const createUsers =data=>ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountMicroServer}/role/create`,
    // url: `${Jw.gateway}/${Jw.permissionServer}/viewPermission/search?page=1&size=10&searchKey=`,
    // method: "get"
    method: "post"
}) 

//编辑角色
const updateUsers =data=>ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountMicroServer}/role/update`,
    // url: `${Jw.gateway}/${Jw.permissionServer}/viewPermission/search?page=1&size=10&searchKey=`,
    // method: "get"
    method: "post"
}) 
//开关角色
const setUserDisable =data=>ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountMicroServer}/role/disable?disable=${data.disable}&roleOid=${data.roleOid}`,
    // url: `${Jw.gateway}/${Jw.permissionServer}/viewPermission/search?page=1&size=10&searchKey=`,
    // method: "get"
    method: "post"
}) 

//获取当前角色是否是管理员
const searchRoleIsSystem = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountMicroServer}/user/systemadmin`,
    // url: `${Jw.gateway}/${Jw.permissionServer}/viewPermission/search?page=1&size=10&searchKey=`,
    method: "get"
}) 
export {
    getRoleList,
    getUserList,
    getUserListData,
    setUserData,
    deleteUserData,
    createUserData,
    deleteUser,
    updateUser,
    getsearchUserList,
    // 重构后
    getDataListArr,
    createUsers,
    updateUsers,
    setUserDisable,
    searchRoleIsSystem
}