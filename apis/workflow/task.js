/**
 * 工作流 我的任务
 * 
 * <AUTHOR>
 * @date 2018-05-02
 */
 import { formatDate } from "jw_utils/moment-date";
import inherit from 'jw_common/inherit'
 import AbstractModel from 'jw_apis/abstract'

let URL = `${Jw.gateway}/${Jw.workflowServer}/workflow/task/{taskId}`

let Task = inherit(AbstractModel,{
 initialization() {

   this.contentType = AbstractModel.APPLICATION_URLENCODED
   this.url = ''
   this.type = 'get'
 },

 dataFormat(data) {

  if(data.createTime) {
    data.createTime = formatDate(data.createTime,'YYYY-MM-DD HH:mm')
  }

  if(data.dueTime) {
    data.dueTime = formatDate(data.dueTime,'YYYY-MM-DD HH:mm')
  }
  
  return data
 },

 isSuccess(result) {

  return !_.isEmpty(result.data)
 },

 execute(taskId) {

  this.url = URL.replace('{taskId}',taskId)
  return this.exec()
 }
})

export default new Task()