import ModelFactory from "jw_apis/model-factory"

//获取任务信息
const getRegulationTree = ModelFactory.create({ //列表数据
    url: `${Jw.gateway}/workflow-poc/regulation/regulation-category`,
    method: "get"
  });
  const getRegulations = ModelFactory.create({ //列表数据
    url: `${Jw.gateway}/workflow-poc/regulation/regulation-page`,
    method: "get"
  });



  const taskModel = ModelFactory.create({ //任务列表
    url: `${Jw.gateway}/${Jw.workflowServer}/workflow/task/tasks`,
    method: "post",
    contentType: 'application/x-www-form-urlencoded'
  });
  
  export {
    getRegulationTree,
    getRegulations
  }