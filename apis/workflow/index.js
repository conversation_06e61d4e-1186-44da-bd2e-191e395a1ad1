import ModelFactory from "jw_apis/model-factory"

//获取任务信息
const searchInstance = ModelFactory.create({ //列表数据
    url: `${Jw.gateway}/${Jw.baseServer}/workflow/instance/searchInstance`,
    method: "post"
  });



  const taskModel = ModelFactory.create({ //任务列表
    url: `${Jw.gateway}/${Jw.workflowServer}/workflow/task/tasks`,
    method: "post",
    contentType: 'application/x-www-form-urlencoded'
  });
  
    export {
        searchInstance,
        taskModel
    }