/**
 * 工作流 我的任务
 * 
 * <AUTHOR>
 * @date 2018-05-02
 */
 

 import inherit from 'jw_common/inherit'
 import AbstractModel from 'jw_apis/abstract'

let URL = `${Jw.gateway}/${Jw.workflowServer}/workflow/task/routing/{taskId}`

let TaskRoute = inherit(AbstractModel,{
 initialization() {

   //this.contentType = AbstractModel.APPLICATION_URLENCODED
   this.url = ''
   this.type = 'get'
 },

 dataFormat(data) {

  return data.data
 },

//  isSuccess(result) {

//   return !_.isEmpty(result.data)
//  },

 execute(taskId) {

  this.url = URL.replace('{taskId}',taskId)
  return this.exec()
 }
})

export default new TaskRoute()