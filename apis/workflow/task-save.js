/**
 * 任务处理保存
 * 
 * <AUTHOR>
 * @date 2018-05-02
 */
 
 import inherit from 'jw_common/inherit'
 import AbstractModel from 'jw_apis/abstract'

let TaskSave = inherit(AbstractModel,{
 initialization() {

   this.url = `${Jw.gateway}/${Jw.workflowServer}/workflow/task/finishTask`
   this.type = 'post'
 },

 dataFormat(data) {

  return data.data
 },

// assignee (string, optional),
// comment (string, optional),
// localVariables (object, optional),
// processInstanceId (string, optional),
// taskId (string, optional),
// variables (object, optional)
 execute(task) {

  this.setParam(task)
  return this.exec()
 }
})

export default new TaskSave()