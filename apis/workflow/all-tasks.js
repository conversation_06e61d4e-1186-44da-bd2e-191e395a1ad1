/**
 * 工作流 我的任务
 * 
 * <AUTHOR>
 * @date 2018-05-02
 */

 import {formatDate} from "jw_utils/moment-date";
 import inherit from 'jw_common/inherit'
 import AbstractModel from 'jw_apis/abstract'
 
 let MyTasks = inherit(AbstractModel,{
  initialization() {
 
    this.contentType = AbstractModel.APPLICATION_URLENCODED
    this.url = `${Jw.gateway}/${Jw.workflowServer}/workflow/task/tasks`
    this.type = 'post'
  },
 
  dataFormat(data) {
 
   _.each(data.data,(row)=>{
 
     if(row.createTime) {
       row.createTime = formatDate(row.createTime,'YYYY-MM-DD HH:mm')
     }
 
     if(row.dueDate) {
       row.dueDate = formatDate(row.createTime,'YYYY-MM-DD HH:mm')
     }
   })
 
   return data
  },
 
  isSuccess(result) {
 
   return result.data && _.isArray(result.data.data)
  },
 
  execute(param) {
   
   this.setParam(param)
   return this.exec()
  }
 })
 
 export default new MyTasks()