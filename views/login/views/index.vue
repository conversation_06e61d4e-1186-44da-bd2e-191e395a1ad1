<template>
<!-- 
  @description: 登录组件
  @param { loginTypes                    | Array   } 可用登录类型, ["phone", "account", "qrCode"]
  @param { formValidtor                  | Object   } 登录表单自定义校验
    formValidtor[phone] { phone          | Array  } 登录表单手机号自定义校验 （校验对象构造与ant-desgin校验规则一致）
    formValidtor[account] { account      | Array  } 登录表单账号自定义校验
    formValidtor[code] { code            | Array  } 登录表单验证码自定义校验
    formValidtor[password] { password    | Array  } 登录表单密码自定义校验
  @param { wxLogin                       | Object   } 微信登录参数
    wxLogin[appid] { appid               | String  } 微信登录appid
    wxLogin[scope] { scope               | String  } 微信登录scope
    wxLogin[redirect_uri] { redirect_uri | String  } 微信登录redirect_uri
  @param { copyRightText                 | String  } 底部版权信息
  @param { serviceUrl                    | String   } 服务协议链接
  @param { privacyUrl                    | String  } 隐私协议链接
  
  fn
  Jw.logout             // 登出

  todo
  i18n替换
  默认校验规则抽出
-->
  <div class="jw-login">
    <div class="login-main">
      <div class="login-main-left" @click="clickTriggerSystem">
        <!-- 登录页左边区域 -->
        <loginInfo></loginInfo>
      </div>
      <div class="login-main-right">
        <!-- 登录页表单区域 -->
        <loginFill :types="loginTypes" :wxLogin="wxLogin" :formValidtor="formValidtor" :serviceUrl="serviceUrl" :privacyUrl="privacyUrl" :errorMsg="errorMsg"></loginFill>
      </div>
    </div>
    <div class="copy-right-text">{{copyRightText}}</div>

    <a-modal v-model="showSystemUrl" title="修改服务器地址" ok-text="确认" cancel-text="取消" @ok="showSystemUrl = false">
      <a-input v-model.trim="systemUrl"></a-input>
    </a-modal>
  </div>
</template>

<script>
import { accountLogoutApi } from "../apis/index";
import { delCookie } from "jw_utils/cookie"

import loginInfo from "../components/loginInfo";
import loginFill from "../components/loginFill";
export default {
  name: "login",
  props: {
    loginTypes: {
      type: Array,
      default: () => ["phone", "account", "qrCode"],
    },
    wxLogin: {
      type: Object,
      default: () => ({
        appid: Jw.workWXCorpID,
        agentid:Jw.workWXAgentID,
        redirect_uri: Jw.workWXRedirectUri
      }),
    },
    copyRightText: {
      type: String,
      // default: `${this.$t('login_default_version_text')}`,
    },
    serviceUrl: String,
    privacyUrl: String,
    formValidtor: Object,
    errorMsg: String
  },
  components: {
    loginInfo,
    loginFill,
  },
  created(){
    // 全局注入登出方法
    Jw.logout=this.logout
    delCookie('token')
  },
  data() {
    return {
      clickTwice: 0,
      showSystemUrl: false,
      systemUrl: localStorage.getItem("systemUrl") ? localStorage.getItem("systemUrl") : Jw.gateway,
    };
  },
  watch: {
    systemUrl: function(val){
      if(val){
        localStorage.setItem("systemUrl", val)
        Jw.gateway = val
      }else{
        localStorage.removeItem("systemUrl")
      }
    }
  },
  methods: {
    logout() {
      const { account } = Jw.getUser()
      accountLogoutApi
        .execute({account})
        .then(()=>{
          delCookie('token')
          //清理所有cookie
          Jw.jumpToLogin();
        })
        .catch((err)=>{
          this.$error(err.msg)
        })
    },
    clickTriggerSystem(){
      this.clickTwice += 1
      if(this.clickTwice > 5){
        this.showSystemUrl = true
        this.clickTwice = 0
      }
    }

  },
};
</script>

<style scoped lang="less">
.jw-login {
  background: url("../img/login-bg.png") 0 / cover fixed;
  width: 100%;
  height: 100%;
  position: absolute;
  .copy-right-text {
    height: 22px;
    line-height: 22px;
    font-size: 14px;
    color: fade(@white, 85);
    width: 100%;
    text-align: center;
    position: absolute;
    bottom: 35px;
  }
}
.login-main {
  width: 1200px;
  height: 580px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -600px;
  margin-top: -290px;
  display: flex;
  z-index: 10;
  box-shadow: @box-shadow-base;
  .login-main-left {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 820px;
    height: 580px;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    background: fade(@white, 45);
    overflow: hidden;
    padding: 60px 0 0 60px;
    box-sizing: border-box;
    &::before {
      background: url("../img/login-bg.png") 0 / cover fixed;
      content: "";
      position: absolute;
      top: -100%;
      bottom: 0;
      left: -100%;
      right: 0;
      filter: blur(30px);
      z-index: -1;
      width: 300%;
      height: 300%;
    }
  }
  .login-main-right {
    width: 389px;
    height: 580px;
    background: @white;
    border-radius: 8px;
    margin-left: -9px;
    position: absolute;
    box-sizing: border-box;
    top: 0;
    right: 0;
  }
}
</style>