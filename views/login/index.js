/*
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2021-12-30 15:35:18
 * @LastEditTime: 2022-01-07 18:28:20
 * @LastEditors: <EMAIL>
 */

import Login from "./views"
import perApp from "views/perApp"
import { useBeforeStart, useAfterAppMounted } from "jw_common/hook"
import {setLoginToken} from 'jw_utils/cookie'
import { weiXinLogin } from "./apis/index";
import Vue from 'vue'
import languages from './locale'
/**
 * @description: 
 * @param {Object} props 登录组件参数
 * @param {Array}  props.loginTypes 可用登录类型,类型展示顺序与参数顺序一致 ["phone", "account", "qrCode"]
 * @param {Object} props.formValidtor 登录表单自定义校验(具体查看上述Login组件)
 * @param {Object} props.wxLogin 微信登录参数
 * @param {String} props.copyRightText 底部版权信息
 * @param {String} props.serviceUrl 服务协议链接
 * @param {String} props.privacyUrl 隐私协议链接
 * @example
 * init({loginTypes:["phone"]})
 */

export default function(props={}) {
  Jw.login = "/#/login"
  useAfterAppMounted(()=>{
    // 重写全局跳转登录方法，改写Jw.logout内跳转登录逻辑，不推荐单独使用
    Jw.jumpToLogin = function() {
      window.location.reload()
    }
  })

  const checkWorkWeiXinLogin = async () => {
    return new Promise(function (resolve, reject) {
        const hash = location.hash
        const flag = 'customTargetUrl'
        if (hash.includes(flag)) {
            const targetUrl = hash.substring(hash.indexOf(flag) + flag.length + 1)
            const search = location.search
            const code = search.substring(search.indexOf('code=') + 5, search.indexOf('&state=WWLogin'))
            weiXinLogin.execute({code: code}).then((res) => {
                res['customTargetUrl'] = targetUrl
                resolve(res)
            }).catch(e => {                
                reject(e)
            })
        } else {
            resolve({})
        }
    })
}


  useBeforeStart(()=>{
    let overrideJwStart = Jw.start
    Jw.start = function() {
      // 重写脚手架start方法，将展示登录页作为Jw.start的checkAuthFailHandler(鉴权失败回调)
      overrideJwStart(async function(err) {

        const res = await checkWorkWeiXinLogin()
        console.log('WorkWeiXinLogin result=', res)
        
        if (res?.accesstoken) {
          setLoginToken('token', res.accesstoken)
          window.open(res.customTargetUrl, '_self')
        }else{
          if(location.hash.indexOf('#/perApp')===0){
            new Vue({
              el: '#app',
              components: { perApp },
              render:function(h){
                return h('perApp',{
                  props : {errorMsg : err && err.msg }
                })
              }
            })
          }else{
            new Vue({
              el: '#app',
              components: { Login },
              render:function(h){
                return h('Login',{
                  props : {errorMsg : err && err.msg }
                })
              }
            })
          }
          
        }
      })
    }
    Vue.prototype.$t = function (code) {
      return languages[code] || code;
    };
  })
}