<template>
  <div>
    <iframe sandbox="allow-scripts allow-top-navigation" scrolling="no" width="320" height="324" frameBorder="0" allowTransparency="true" :src="setSrc"></iframe>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  computed: {
    setSrc() {
      var redirect_uri = encodeURIComponent(this.redirect_uri);
      var workWXURI = 'https://login.work.weixin.qq.com/wwlogin/sso/login/'
      var _url = `${workWXURI}?appid=${this.appid}&redirect_uri=${redirect_uri}&state=${this.state}&login_type=${this.login_type}&agentid=${this.agentid}`;
      return _url;
    },
  },
  props: {
    appid: String,
    agentid: String,
    redirect_uri: String,
    state: {
      type: String,
      default: "WWLogin",
    },
    login_type:{
      type: String,
      default: "CorpApp",
    }
  },
};
</script>
