<!--
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2021-12-15 14:39:22
 * @LastEditTime: 2022-01-19 13:59:27
 * @LastEditors: <EMAIL>
-->
<template>
  <div class="login-main-info">
    <div class="login-icon">
      <img v-if="appLogo.startsWith('config')" style="height:60px" :src="appLogo" />
      <jw-icon v-else :type="appLogo" class="app-icon"></jw-icon>
      <h6 class="app-title">{{title}}</h6>
    </div>
    <div class="login-descrie">
      <p>{{describe}}</p>
    </div>
    <div class="swiper-list">
      <a-carousel autoplay>
        <div v-for="(item,i) in list" :key="i"><img :src="item.img" /></div>
        <!-- <div><img src="../img/swiper-5.png" /></div>
        <div><img src="../img/swiper-6.png" /></div>  -->
      </a-carousel>
    </div>
  </div>
</template>

<script>
import { Carousel } from "ant-design-vue";
import { jwIcon } from "jw_frame";
export default {
  name: "loginInfo",
  components: {
    ACarousel: Carousel,
    jwIcon
  },
  data() {
    return {
      title: Jw.title,
      appLogo: Jw.appIcon,
      describe:this.$t(Jw.describe||'login_bright_spot')
    };
  },
  computed: {
    // brightSpot () {
    //   switch (Jw?.appName) {
    //     case 'ppm': return 'login_bright_spot_ppm'
    //     default: return 'login_bright_spot'
    //   }
    // }
  },
  created() {
    const imgMap = {
      mpm: [
        {
          img: require("../img/mpm-1.png")
        }
      ],
      pdm: [
        {
          img: require("../img/pdm-login.png")
        }
      ],
      ppm: [
        {
          img: require("../img/ppm-login.png")
        }
      ],
      "mpm-dm": [
        {
          img: require("../img/pdm-1.png")
        },
        {
          img: require("../img/pdm-2.png")
        }
      ],
      default: [
        {
          img: require("../img/pdm-1.png")
        },
        {
          img: require("../img/pdm-2.png")
        }
      ]
    };
    this.list = imgMap[Jw?.appName||"default"];
  }
};
</script>

<style lang="less" scoped>
.swiper-list /deep/.ant-carousel .slick-dots li button {
  display: block;
  width: 8px;
  height: 8px;
  padding: 0;
  color: transparent;
  font-size: 0;
  background: fade(@primary-color, 30);
  border: 0;
  outline: none;
  cursor: pointer;
  transition: all 0.5s;
  border-radius: 4px;
  opacity: 1;
}

.swiper-list /deep/.ant-carousel .slick-dots li.slick-active button {
  width: 24px;
  background: @primary-color;
}

.dotsClass {
  border-radius: 50%;
}
.swiper-list {
  margin-top: 29px;
  width: 100%;
}
.login-descrie p {
  height: 20px;
  line-height: 20px;
  padding: 15px 0;
  font-size: 18px;
  color: fade(@black, 65);
}
.login-descrie {
  border-top: 1px solid @border-color-base;
}
.login-icon {
  margin-bottom: 15px;
  height: 32px;
  display: flex;
  align-items: center;
  .app-icon {
    font-size: 60px;
  }
  .app-title {
    margin: 0 0 0 12px;
    color: @text-color;
    font-weight: 600;
    font-size: 20px;
  }
}
.login-main-info {
  display: flex;
  flex-direction: column;
  width: 692px;
}
</style>
