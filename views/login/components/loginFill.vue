<template>
  <div class="main-body">
    <!-- 传统登录与微信登录切换区域 -->
    <div class="weima" @click="toggleLogin" v-if="isShowWxLogin">
      <a-tooltip placement="left">
        <!-- <template slot="title">
          <span v-if="flagval">企业微信</span>
          <span v-else>账号登录</span>
        </template> -->
        <span v-if="flagval"><img src="../img/weima.png" /></span>
        <span v-else><img src="../img/computer.png" /></span>
      </a-tooltip>
    </div>
    <div class="login-wrap" v-if="flagval">
      <div class="login-use">{{$t('login_welcome')}}</div>
      <div class="login-agreement">
        <!-- 登录即代表你已阅读并同意<a :href="serviceUrl">服务协议</a>和<a :href="privacyUrl">隐私政策</a> -->
      </div>
      <!-- 手机号与账号登录切换tab -->
      <div class="login-switch">
        <li class="login-switch-tab" :class="{ activer: activeTab == item.key }" @click="switchTab(item)" v-for="item of loginTabs" :key="item.key">
          {{ item.name }}
        </li>
      </div>
      <!-- 手机号登录表单 -->
      <!-- <div class="form-list" v-show="activeTab==='phone'">
        <a-form-model ref="phoneForm" :model="phoneForm" :rules="phoneFormRule" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item ref="phone" prop="phone">
            <a-input :placeholder="$t('txt_input')"  v-model="phoneForm.phone" class="input-phone" @focus="focus">
              <a-select slot="addonBefore" default-value="+86" class="select-phone">
                <a-select-option value="+86">+86</a-select-option>
              </a-select>
            </a-input>
          </a-form-model-item>

          <a-form-model-item ref="authCode" prop="authCode">
            <a-input-search placeholder="请注意查收你的验证码" v-model="phoneForm.authCode" @focus="focus" @search="fetchSendcode">
              <a-button slot="enterButton" :disabled="codedisabled" class="code-btn" :class="{light:phoneForm.phone.length>0}">{{ btntxt }}</a-button>
            </a-input-search>
          </a-form-model-item>

          <a-form-model-item>
            <a-button type="primary" :class="{ activer: !nousedisabled }" class="login-btn" @click="fetchLoginBtnPho">登录使用</a-button>
          </a-form-model-item>
        </a-form-model>
      </div> -->
      <!-- 账号密码登录表单 -->
      <div class="form-list" v-show="activeTab==='account'">
        <a-form-model ref="accountForm" :model="accountForm" :rules="accountFormRule" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-model-item ref="account" prop="account">
            <a-input :placeholder="$t('login_account_placeholder')" @focus="getfocus" v-model.trim="accountForm.account" />
          </a-form-model-item>

          <a-form-model-item ref="password" prop="password">
            <a-input-password class="login-int" @focus="getfocus" v-model="accountForm.password" :placeholder="$t('login_passowrd_placeholder')" />
          </a-form-model-item>
          <a-form-model-item>
            <a-button type="primary" :class="{ activer: !disabled }" class="login-btn" @click="fetchLoginBtn">{{$t('login_use')}}</a-button>
          </a-form-model-item>
        </a-form-model>
      </div>
    </div>
    <!-- 微信登录 -->
    <div class="login-wrap" v-else>
      <div class="login-use">{{$t('login_code_login')}}</div>
      <div class="login-agreement">
        {{$t('login_code_login_tip')}}
      </div>
      <div id="wxbox" class="qr-code">
        <wxlogin :appid="wxLogin.appid" :agentid="wxLogin.agentid" :redirect_uri="wxLogin.redirect_uri"></wxlogin>
      </div>
    </div>
    <!-- 底部其他链接 -->
    <div class="footer">
      <!-- <div>
        <span>还没有账号？</span>
        <a href="">立即注册</a>
      </div> -->
      <span>{{$t('login_forget_password')}}</span>
    </div>
  </div>
</template>

<script>
import commonStore from "jw_stores/common";
import { phoneLoginlApi, sendCodelApi, accoutLoginApi } from "../apis/index";
import wxlogin from "./wxlogin";
import { setLoginToken ,delCookie} from "jw_utils/cookie";


const phoneValid = (rule, value, callback) => {
  const reg = /^1[3|4|5|7|8|9][0-9]\d{8}$/;
  if (reg.test(value)) {
    callback();
  } else {
    return callback(new Error("请输入正确的手机号"));
  }
};
// 默认的手机号校验
const phoneValidtor = [{ validator: phoneValid, trigger: "blur" }];
// 默认的验证码校验
const codeValidtor = [
  {
    pattern: new RegExp(/^[1-9]\d*$/, "g"),
    message: "验证码为数字",
    trigger: "blur"
  },
  { len: 6, message: "验证码长度为6", trigger: "blur" }
];
// 默认的账号校验
const accountValidtor = [
  {
    min: 3,
    max: 25,
    message: "长度在 3 到 25 个字符",
    trigger: "blur",
  },
];

export default {
  name: "loginFill",
  props: {
    types: Array,
    wxLogin: Object,
    serviceUrl: String,
    privacyUrl: String,
    formValidtor: {
      type: Object,
      default: () => ({
        phone: [],
        account: [],
        code: [],
        password: []
      })
    },
    errorMsg: String
  },
  data() {
    // 支持的登录方式
    // this.LOGIN_TYPES = [
    //   {
    //     key: "account",
    //     name: this.$t('login_account_login')
    //   },
    //   {
    //     key: "phone",
    //     name: "手机号"
    //   },

    //   {
    //     key: "qrCode",
    //     name: "微信二维码"
    //   }
    // ];
    return {
      codedisabled: false,
      time: 60,
      btntxt: "获取验证码",
      flagval: true,
      nousedisabled: true,
      disabled: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 24 },
      activeTab: "",
      LOGIN_TYPES : [
      {
        key: "account",
        name: this.$t('login_account_login')
      },
      {
        key: "phone",
        name: "手机号"
      },

      {
        key: "qrCode",
        name: "微信二维码"
      }
    ],
      phoneForm: {
        phone: "",
        authCode: "",
        select: 1
      },
      accountForm: {
        account: "",
        password: ""
      }
    };
  },
  computed: {
    loginTypes() {
      let types = [];
      if (Array.isArray(this.types)) {
        this.types.forEach(type => {
          const loginType = this.LOGIN_TYPES.find(t => t.key === type);
          if (loginType) {
            types.push(loginType);
          }
        });
        if (types.length === 0) {
          types = [this.LOGIN_TYPES[0]];
        }
      } else {
        types = [this.LOGIN_TYPES[0]];
      }
      return types;
    },
    loginTabs() {
      return this.loginTypes.filter(type => type.key == "account");
    },
    isShowWxLogin() {
      return this.loginTypes.find(t => t.key === "qrCode");
    },
    accountFormRule() {
      const rule = {
        account: [
          {
            required: true,
            message: this.$t("login_account_valid"),
            trigger: "blur"
          }
        ],
        password: [
          {
            required: true,
            message: this.$t("login_password_valid"),
            trigger: "blur"
          }
        ]
      };
      let { account: accountRule, password: passwordRule } = this.formValidtor;
      // 未自定义规则使用默认规则
      if (!Array.isArray(accountRule) || accountRule.length === 0) {
        accountRule = [
          {
            min: 3,
            max: 25,
            message: this.$t("msg_length_of_length"),
            trigger: "blur"
          }
        ];
      }
      if (!Array.isArray(passwordRule)) {
        passwordRule = [];
      }
      rule.account = rule.account.concat(this.formValidtor.account);
      rule.password = rule.password.concat(this.formValidtor.password);

      return rule;
    },
    phoneFormRule() {
      const rule = {
        phone: [{ required: true, message: "手机号不能为空", trigger: "blur" }],
        authCode: [
          { required: true, message: "验证码不能为空", trigger: "blur" }
        ]
      };
      let { phone: phoneRule, code: codeRule } = this.formValidtor;
      // 未自定义规则使用默认规则
      if (!Array.isArray(phoneRule) || phoneRule.length === 0) {
        phoneRule = phoneValidtor;
      }
      if (!Array.isArray(codeRule) || codeRule.length === 0) {
        codeRule = codeValidtor;
      }
      rule.phone = rule.phone.concat(this.formValidtor.phone);
      rule.authCode = rule.authCode.concat(this.formValidtor.code);
      return rule;
    }
  },
  components: {
    wxlogin
  },
  created() {
    this.bindEnterEventClick = this.onEnterEventClick.bind(this);
    document.addEventListener("keydown", this.bindEnterEventClick);
    this.activeTab = this.loginTabs[0].key;
    if(this.errorMsg){
      this.$error(this.errorMsg);
    }
  },
  methods: {
    onEnterEventClick(e) {
      e = window.event || e;

      if (e.code == "Enter" || e.code == "enter") {
        this.fetchLoginBtn();
      }
    },
    //手机号登录
    fetchLoginBtnPho() {
      this.$refs.phoneForm.validate(valid => {
        if (valid) {
          phoneLoginlApi
            .execute(this.form)
            .then(data => {
              setLoginToken("token", data.accesstoken);
              window.location.hash = "#/";
              window.location.reload();
            })
            .catch(err => {
              if (err.msg) {
                this.$error(err.msg);
              }
            });
        } else {
          return false;
        }
      });
    },
    //账号登录
    fetchLoginBtn() {
      this.$refs.accountForm.validate(valid => {
        if (valid) {
          const originalUrl = sessionStorage.getItem('redirectUrl');
          if (!originalUrl) {
            sessionStorage.setItem('redirectUrl', window.location.href); // 保存当前页面的URL
          }
          accoutLoginApi.setQuery(this.accountForm)
          accoutLoginApi
            .execute(this.accountForm)
            .then(data => {
              console.log("登录数据：", data)
              if (data.tips) {
                commonStore.set("tips", data.tips);
              } else {
                commonStore.set("tips", null);
              }
              delCookie("tenantOid");
              delCookie("tenantAlias");

              setLoginToken("token", data.accesstoken || data);
              // 获取之前保存的原始URL
              const storedUrl = sessionStorage.getItem('redirectUrl');
              const redirectUrl = (storedUrl && storedUrl.includes('login')) ? '#/' : (storedUrl || '#/');// 如果没有保存原始URL，则重定向到首页
              sessionStorage.removeItem('redirectUrl'); // 清除保存的URL
              // 重定向到原始链接
              window.location.href = redirectUrl;
              // window.location.hash = "#/";
              window.location.reload();
              //  this.$router.push("/dashboard");
            })
            .catch(err => {
              if (err.msg) {
                this.$error(err.msg);
              }
            });
        } else {
          return false;
        }
      });
    },
    //获取验证码
    fetchSendcode() {
      this.$refs.phoneForm.validateField("phone", err => {
        if (!err) {
          let parma = {
            phone: this.phoneForm.phone,
            flag: "login"
          };
          sendCodelApi
            .execute(parma)
            .then(() => {
              this.$success("发送成功");
              this.timer();
            })
            .catch(err => {
              this.$error(err.msg);
            });
        }
      });
    },
    timer() {
      if (this.time > 0) {
        this.codedisabled = true;
        this.time--;
        this.btntxt = this.time + "S";
        setTimeout(this.timer, 1000);
      } else {
        this.time = 0;
        this.btntxt = "重新获取";
        this.codedisabled = false;
      }
    },
    toggleLogin() {
      return;
      let appid = this.wxLogin.appid
      let agentid = this.wxLogin.agentid
      let redirect_uri = encodeURIComponent(this.wxLogin.redirect_uri);
      let workWXURI = 'https://login.work.weixin.qq.com/wwlogin/sso/login/'
      let state = 'WWLogin'
      let login_type = 'CorpApp'
      var _url = `${workWXURI}?appid=${appid}&agentid=${agentid}&redirect_uri=${redirect_uri}&state=${state}&login_type=${login_type}`;
      window.open(_url,'_blank')
      return;
      this.flagval = !this.flagval;
    },
    getfocus() {
      this.disabled = false;
    },
    focus() {
      this.nousedisabled = false;
    },
    switchTab(item) {
      this.activeTab = item.key;
      // 重置表单
      if (item.key == "account") {
        this.$refs.phoneForm.resetFields();
      } else {
        this.$refs.accountForm.resetFields();
      }
      this.disabled = true;
      this.nousedisabled = true;
    }
  },
  destroyed() {
    document.removeEventListener("keydown", this.bindEnterEventClick);
  }
};
</script>

<style lang="less" scoped>
.main-body {
  position: relative;
  height: 100%;
  padding: 60px 24px 0;
}
.login-wrap {
  position: relative;
  height: 100%;
}
.weima-text {
  width: 72px;
  height: 32px;
  background: @heading-color;
  box-shadow: @box-shadow-base;
  border-radius: 2px;
  margin-top: 29px;
  margin-right: -14px;
}
.weima-text label {
  font-size: 14px;
  color: @white;
  text-align: center;
  display: block;
  height: 32px;
  line-height: 32px;
}
.weima {
  position: absolute;
  right: 0;
  top: 0;
  display: flex;
}
.weima span {
  display: block;
  width: 85px;
  height: 85px;
  cursor: pointer;
}

.login-use {
  font-size: 20px;
  color: @heading-color;
  height: 28px;
  line-height: 28px;
  font-weight: 700;
}
.login-agreement {
  margin: 8px 0 16px 0;
  height: 22px;
  font-size: 14px;
  line-height: 22px;
  color: fade(black, 45);
}
.login-switch {
  display: flex;
  .login-switch-tab {
    margin-right: 34px;
    height: 46px;
    line-height: 46px;
    box-sizing: border-box;
    list-style-type: none;
    font-size: 14px;
    color: @text-color;
    cursor: pointer;
    &.activer {
      border-bottom: 2px solid @primary-color;
      color: @heading-color;
      font-weight: 700;
    }
  }
}

.form-list {
  margin-top: 16px;
  height: 367px;
  position: relative;
  /deep/.ant-form-item {
    margin-bottom: 6px;
    &:last-child {
      margin: 12px 0 0;
    }
    .ant-input {
      font-size: 14px;
      // border-color: rgba(30, 32, 42, 0.15);
    }
    .select-phone {
      width: 74px;
      .ant-select-selection-selected-value {
        color: @primary-color;
      }
    }
  }
  .has-error {
    .code-btn {
      border-color: #f5222d;
    }
  }
}
.login-int {
  line-height: unset;
}
.input-phone {
  /deep/.ant-input-group {
  }
  /deep/.ant-input-group-addon {
    background: transparent;
  }
}
.login-btn {
  width: 100%;
  background: rgba(37, 94, 215, 0.35);
  color: #fff;
  cursor: not-allowed;
  border: none;
}
.login-btn.activer {
  background: rgba(37, 94, 215, 0.35);
  cursor: pointer;
  background: @primary-color;
}
.code-btn {
  width: 102px;
  font-size: 14px;
  color: rgba(30, 32, 42, 0.25);
  background: rgb(246, 246, 247);
  &.light {
    color: @primary-color;
  }
}
.qr-code {
  width: 320px;
  height: 320px;
  margin-left: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.footer {
  position: absolute;
  display: flex;
  width: 100%;
  padding: 0 24px;
  left: 0;
  height: 22px;
  bottom: 30px;
  line-height: 22px;
  justify-content: space-between;
  font-size: 14px;
  color: rgba(30, 32, 42, 0.65);
  a {
    margin-left: 8.5px;
    font-size: 14px;
    color: @primary-color;
  }
  .erweima {
    bottom: 36px;
  }
}
</style>
