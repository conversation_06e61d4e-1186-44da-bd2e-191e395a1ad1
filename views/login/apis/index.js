import ModelFactory from "jw_apis/model-factory"
let serverName=Jw.appName=='npi'?Jw.authServer:Jw.accountMicroServer
let gateway=Jw.loginGateway||Jw.gateway
//手机号登录
const phoneLoginlApi = ModelFactory.create({
  url: `${gateway}/${serverName}/authentication/loginByPhone`,
  method: "post",
  contentType: 'application/x-www-form-urlencoded',
})

//发送验证码
const sendCodelApi = ModelFactory.create({
  url: `${gateway}/${serverName}/v2/user/sms/getAuthCodeByPhone`,
  method: "get",
})

//账号登录
const accoutLoginApi = ModelFactory.create({
  url: `${gateway}/${serverName}/authentication/login`,
  method: "post"  
})

//登出
const accountLogoutApi = ModelFactory.create({
  url: `${gateway}/${serverName}/authentication/logout`,
  method: "post",
  contentType: 'application/x-www-form-urlencoded',
})

const weiXinLogin = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.iamServer}/authentication/weiXinLogin`,
  method: "get",
})

export {
  phoneLoginlApi,
  sendCodelApi,
  accoutLoginApi,
  accountLogoutApi,
  weiXinLogin
}