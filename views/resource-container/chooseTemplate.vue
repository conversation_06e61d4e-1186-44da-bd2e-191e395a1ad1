<template>
  <a-modal
    v-model.trim="visible"
    :title="$t('txt_resources_temp_center')"
    width="1058px"
    :footer="null"
    @ok="handleOk"
    @onCancel="handleCancel"
    @cancel="handleCancel"
  >
    <div>
      <!-- <a-button type="primary" @click="handleAdd">新建</a-button> -->
      <a-input-search
        v-model.trim="searchKey"
        :placeholder="$t('search_text')"
        style="width: 200px"
        @change="delaySearch"
      />
    </div>
    <a-spin :spinning="cardSpinning">
      <div class="template-content">
        <!-- <div class="template-title">
        <p
          class="title-item"
          v-for="(item, key) in templateTitle"
          :key="key"
          @click="handleType(item)"
        >
          {{ item.name }}
        </p>
      </div> -->
        <div class="template-list">
           <div class="list-item" key="none">
          <div class="item-title">{{$t('默认模板')}} </div>
          <div class="item-text">{{$t('默认模板')}} </div>
          <div class="item-hover">
            <a-button
              class="hover-primary"
              type="primary"
              @click="handleChoose(emptyTemplate)"
            >
            {{$t('txt_use_template')}}  
            </a-button>
            <!-- <a-button @click="handleAdd">预览模板</a-button> -->
          </div>
        </div>
          <div class="list-item" v-for="(item, key) in templateList" :key="key">
            <div class="item-title">{{ item.name }}</div>
            <div class="item-text">
              {{ item.description }}
            </div>
            <div class="item-hover">
              <a-button
                class="hover-primary"
                type="primary"
                @click="handleChoose(item)"
              >
              {{$t('txt_use_template')}}  
              </a-button>
              <!-- <a-button @click="handleAdd">预览模板</a-button> -->
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import { fetchContainerTemplate } from "apis/resource-container";
export default {
  props: {
    visible: Boolean,
  },
  data() {
    return {
      cardSpinning: true,
      // 空模板数据
      emptyTemplate: { oid: "" },
      searchKey: "",
      isVisible: this.visible,
      categoryType: "resource",
      templateTitle: [
        { name: this.$t('txt_product_temp'), key: "product" },
        { name: this.$t('txt_resources_temp'), key: "resource" },
        // { name: "文档模板", key: "3" },
      ],
      templateList: [
        {
          title: this.$t('txt_null_temp'),
          text: this.$t('txt_desc_target'),
          key: "1",
        },
      ],
    };
  },
  created() {
    this.delaySearch = _.debounce(this.onSearch, 500);
  },
  methods: {
    // 输入回调刷新表格数据
    onSearch() {
      this.getContainerTemplate();
    },
    handleType(item) {
      this.categoryType = item.key;
      this.getContainerTemplate();
    },
    // 获取产品容器模板
    getContainerTemplate() {
      let { categoryType, searchKey } = this;
      let param = {
        category: categoryType,
        index: 1,
        searchKey: searchKey,
        size: 100000,
      };
      this.cardSpinning = true;
      fetchContainerTemplate
        .execute(param)
        .then((data) => {
          console.log("产品模板列表", data);
          this.templateList = data.rows.filter(
            (item) => item.disabled === false
          );
          this.cardSpinning = false;
        })
        .catch((err) => {
          this.tableLoading = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    handleChoose(row) {
      this.$emit("chooseAfter", row);
    },
    handleOk() {
      this.$emit("showChoose", false);
    },
    handleCancel() {
      this.$emit("showChoose", false);
    },
    handleAdd() {},
  },
};
</script>
<style lang="less" scoped>
/deep/.ant-modal .ant-modal-content .ant-modal-header{
  padding-bottom: 0;
  height: auto;
}
/deep/.ant-modal-body{
  padding-top: 0;
}
/deep/.ant-modal-close-x{
  color: #000000;
}
.template-content {
  margin-top: 16px;
  height: 600px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  .template-title {
    padding-right: 12px;
    width: 240px;
    height: 100%;
    border-right: 1px solid rgba(30, 32, 42, 0.15);
    .title-item {
      margin-bottom: 2px;
      padding-left: 12px;
      height: 42px;
      line-height: 42px;
      font-size: 14px;
      color: rgba(30, 32, 42, 0.85);
      cursor: pointer;
      &:hover {
        background: rgba(30, 32, 42, 0.06);
        border-radius: 4px;
      }
    }
  }
  .template-list {
    height: 100%;
    flex: 1;
    .list-item {
      position: relative;
      margin-right: 16px;
      margin-bottom: 16px;
      float: left;
      width: 240px;
      height: 113px;
      padding: 20px;
      background: rgba(30, 32, 42, 0.04);
      border-radius: 4px;
      cursor: pointer;
      &:nth-of-type(4n+0){
        margin-right: 0;
      }
      &:hover {
        .item-hover {
          display: flex;
        }
      }
      .item-title {
        margin-bottom: 16px;
        font-size: 14px;
        color: rgba(30, 32, 42, 0.85);
      }
      .item-text {
        font-size: 12px;
        color: rgba(30, 32, 42, 0.45);
        text-align: justify;
      }
      .item-hover {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        display: none;
        align-items: center;
        justify-content: center;
        background: rgba(30, 32, 42, 0.45);
        border-radius: 4px 4px 0 4px 4px;
        border-radius: 4px 4px 0 4px 4px;
        .hover-primary {
          margin-right: 12px;
        }
      }
    }
  }
}
</style>