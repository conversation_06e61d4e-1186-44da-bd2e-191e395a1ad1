<template>

	<div class="all-background">
		<div
			class="title-box-statement"
			style="text-align: right; margin-bottom: 20px"
		>
			<span>
				<span slot="title">三员权限开关</span>
				<a-switch
					@change="switchClick"
					v-model.trim="switchStatus"
					:disabled="switchStatus"
					checked-children="启用"
					un-checked-children="禁用"
					default-checked
				/>
			</span>
		</div>
		<jw-table
			:columns="columns"
			:toolbars="toolbars"
			:data-source="tableData"
			:pagerConfig="pagerConfig"
			@onPageChange="onPageChange"
			@onSizeChange="onSizeChange"
			@onOperateClick="onOperateClick"
			@onToolClick="onToolClick"
			@onToolInput="onToolInput"
			@checkbox-chang="onSelectChange"
			:selectedRows.sync="selectedRows"
		>
		</jw-table>

		<formModal
			:title="modalAction == 'create' ? '编辑IP绑定' : '编辑IP绑定'"
			:data-source="formModalData"
			:visible.sync="modalVisible"
			@confirm="modalCallback"
			:confirmLoadingStatus="confirmLoadingStatus"
		/>
	</div>
</template>

<script>
import formModal from "components/form-modal.vue"
import { jwTable, jwModalForm } from "jw_frame"

import {
	getThreeUserRole,
	updateThreeAdminIp,
	deleteThreeAdminIp,
	createThreeAdminIp,
	threStatus,
	threeEnable
} from "../../apis/tenant/index"

export default {
	name: "todoTasks",
	inject: ["setBreadcrumb", "addBreadcrumb"],
	components: {
		formModal,
		jwTable,
		jwModalForm,
    // jwToolbar, jwPage
	},
	data() {
		return {
			route: this.$route.path,
			pagination: false,
			switchStatus: false,
			modalVisible: false,
			confirmLoadingStatus: false,
			modalAction: "create",
			permissionData: [],
			pages: {
				pageIndex: 1,
				pageSize: 10,
				searchKey: null,
				val: null,
			},
			columns: [
				{
					title: "管理员角色",
					key: "roleName", // 点击回调判断唯一值
					field: "roleName",
				},
				{
					title: "管理员账号",
					key: "userName",
					field: "userName",
				},
				{
					title: this.$t('txt_description'),
					key: "description",
					field: "description",
				},
				{
					title: "绑定IP地址",
					key: "ip",
					field: "ip",
				},
				{
					field: "operation",
					btns: [
						{
							title: "编辑",
							key: "editor",
							icon: "jwi-iconedit",
						},
						// {
						// 	title: "删除",
						// 	key: "delete",
						// 	icon: "jwi-icondelete",
						// },
						// {
						// 	title: "国际化",
						// 	key: "international",
						// 	icon: "jwi-iconinternet",
						// },
					],
				},
			],
			selectedRows: [],
			pageSize: 10,
			total: 0,
			currentPage: 1,
			oid: "",

			tableData: [],
			fullHeight: document.documentElement.clientHeight - 104,
			form: {
				nameLike: "",
				active: false,
				isDueDate: false,
				order: "desc",
				sort: "createTime",
				processInstanceNameLike: "",
			},
			addParams: {},
			pagerConfig: {
				current: 1,
				pageSize: 20,
				total: 0,
			}, //分页配置
			formModalData: [
				{
					label: "管理员角色",
					type: "input",
					value: "",
					block: true,
					disabled: true,
					prop: "adminUser",
				},
				{
					label: "管理员账号",
					block: true,
					type: "input",
					value: "",
					disabled: true,
					prop: "adminAccount",
				},

				{
					label: "绑定IP地址",
					type: "input",
					block: true,
					placeholder: "请输入IP地址",
					codeType: "ipCode",
					required: false,
					value: "",
					prop: "ipAddress",
				},
			],
		}
	},
	methods: {
		initBreadcrumb() {
			let breadcrumbData = [
				{ name: "系统密级", path: "/security-system" },
			]
			this.setBreadcrumb(breadcrumbData)
		},
		//获取三元管理下的用户列表
		getThreeUserRole() {
			getThreeUserRole.execute().then((res) => {
				this.pagerConfig.total = res.length
				this.tableData = res
			})
		},
		//获取三元管理初始状态
		getThreeStatus() {
			threStatus(this.$route.path)
				.execute()
				.then((res) => {
					this.switchStatus = res
				})
		},
		switchClick(e) {
			console.log(e)
			threeEnable()
				.execute()
				.then((res) => {
					this.$success("设置成功")
				})
				.catch((err) => {
					this.$error("操作失败，请重试！")
				})
		},

		//编辑
		editorClick(row) {
			this.$set(this.formModalData, 0, {
				...this.formModalData[0],
				value: row.roleName,
			})
			this.$set(this.formModalData, 1, {
				...this.formModalData[1],
				value: row.userName,
			})
			this.$set(this.formModalData, 2, {
				...this.formModalData[2],
				value: row.ip || "",
			})
			this.oid = row
			this.modalVisible = true
		},
		//确认提交
		modalCallback(modal) {
			console.log(this.oid)
			this.confirmLoadingStatus = true
			if (!modal.ipAddress&&this.oid.ip) {
				let query = {
					oid: this.oid.oid,
					ip: modal.ipAddress,
				}
				deleteThreeAdminIp(query)
					.execute(query)
					.then((res) => {
						this.$success("修改成功")
						this.modalVisible = false
						this.confirmLoadingStatus = false
						this.getThreeUserRole()
					})
					.catch((err) => {
						this.confirmLoadingStatus = false
						this.$error(err.msg || "保存失败，请重试")
					})
			} else {
				if (this.oid.ip) {
					let query = {
						oid: this.oid.oid,
						ip: modal.ipAddress,
					}
					//更新IP绑定
					updateThreeAdminIp
						.execute(query)
						.then((res) => {
							this.$success("保存成功")
							this.modalVisible = false
							this.confirmLoadingStatus = false
							this.getThreeUserRole()
						})
						.catch((err) => {
							this.confirmLoadingStatus = false
							this.$error(err.msg || "保存失败，请重试")
						})
				} else {
					let parmes = {
						roleId: this.oid.roleId,
						roleName: this.oid.roleName,
						userId: this.oid.userId,
						userName: this.oid.userName,
						ip: modal.ipAddress,
					}
					//创建IP绑定
					createThreeAdminIp
						.execute(parmes)
						.then((res) => {
							this.$success("保存成功")
							this.modalVisible = false
							this.confirmLoadingStatus = false
							this.getThreeUserRole()
						})
						.catch((err) => {
							this.confirmLoadingStatus = false
							this.$error(err.msg || "保存失败，请重试")
						})
				}
			}
		},

		onSelectChange(args) {
			console.log(args)
		},
		// 工具栏点击回调
		onToolClick({ key }) {
			console.log(key)
			this.addParams = {}
			const that = this
			if (key === "create") {
				if (this.tabKey == 2) {
					this.modalVisible = true
				} else {
					that.addVisible = true
				}
			} else if (key === "compare") {
				//
			} else if (key === "delete") {
				// this.getUnitManagementList()
			}
		},
		// 工具栏输入回调
		onToolInput({ key }, value) {
			console.log(value)
			if (key === "search") {
				this.pages.searchKey = value
				this.searchKey = value
				// this.getUnitManagementList()
			}
		},
		onOperateClick(key, row) {
			console.log(key, row)
			const that = this

			if (key === "editor") {
				this.$set(this.formModalData, 0, {
					...this.formModalData[0],
					value: row.roleName,
				})
				this.$set(this.formModalData, 1, {
					...this.formModalData[1],
					value: row.userName,
				})
				this.$set(this.formModalData, 2, {
					...this.formModalData[2],
					value: row.ip || "",
				})
				this.oid = row
				this.modalVisible = true
			} else if (key === "delete") {
				this.$confirm({
					title: "提示",
					content: `确认删除‘${row.name}’吗？`,
					okText: "确认",
					cancelText: "取消",
					onOk() {
						that.onDelete(row.oid)
					},
				})
			}
		},
		// handleCurrentChange(page, size) {
		// 	this.pages.pageIndex = page
		// 	this.pages.pageSize = size
		// 	this.currentPage = page
		// 	this.getPermissionList()
		// },

		// handleSizeChange(curren, size) {
		// 	this.pages.pageIndex = curren
		// 	this.pages.pageSize = size
		// 	this.currentPage = curren
		// 	this.getPermissionList()
		// },
		//分页操作
		onPageChange(page, pageSize) {
			this.pages = {
				currentPage: page,
				size: pageSize,
				searchKey: this.pages.searchKey,
			}

			this.pagerConfig.current = page
			this.pagerConfig.pageSize = pageSize
			this.getPermissionList()
		},
		onSizeChange(pageSize, page) {
			this.pages = {
				currentPage: page,
				size: pageSize,
				searchKey: this.pages.searchKey,
			}
			this.pagerConfig.current = page
			this.pagerConfig.pageSize = pageSize

			this.getPermissionList()
		},
	},

	mounted() {},
	created() {
		this.initBreadcrumb()
		this.getThreeUserRole()
		this.getThreeStatus()
	},
	computed: {
		toolbars() {
			return [
				{
					name: "搜索",
					position: "before",
					display: "input",
					value: this.searchKey,
					allowClear: true,
					placeholder: "名称、描述",
					prefixIcon: "jwi-search",
					key: "search",
				},
			]
		},
	},
}
</script>

<style lang="less" scoped>
.all-background {
	height: 100%;
	background: #fff;
	// box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
	border-radius: 4px;
	padding: 20px;
	::v-deep.ant-row {
		height: 100%;
		.ant-col {
			height: 100%;
		}
		.main-info {
			height: 100%;
		}
	}
}
.maintable {
	padding: 0px 20px 15px 20px;
	box-sizing: border-box;
	flex: 1;
	overflow-y: auto;
}
.baselist-info {
	padding: 20px;
	display: flex;
	flex-direction: column;
	margin: 5px;
	background: #fff;
	/* background: var(--light); */
	box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
	border-radius: 4px;
}
.all-background {
	position: relative;
}
.title-box-statement {
	position: absolute;
	right: 20px;
	top: 20px;
	width: 200px;
	margin-top: 5px;
	z-index: 9999;
}
</style>
