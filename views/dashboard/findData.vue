<template>
  <a-card class="search shadow-wrap" :body-style="{ padding: 0, height: '100%' }" v-loading="loading">
    <div class="total-table">
      <a-tabs v-model.trim="tabActive" class="_mpm-tabs" @change="onchangeTab">
        <a-tab-pane v-for="item in _tabs" :tab="item.name" :key="item.key" style="height: 100%;">
          <div class="content" v-if="listdata.length">
            <div class="content-item" v-for="(item,index) in listdata" :key="index">
              <div>
                <jw-icon :type="iconMap[item.modelDefinition]" class="icon" v-if="tabActive == 'collect'"/>
                <span class="describe">{{ item.name || item.bizName }}</span>
              </div>
              <div v-if="tabActive == 'collect'">
                <span class="time">{{ formatDateFn(item.updateDate) }}</span>
                <i class="jwi-iconstar-full collected icon" :title="$t('btn_cancel_collect')" v-if="item.collection"/>
                <i class="jwi-iconstar" v-else/>
              </div>
              <div v-else>
                <span class="time">{{ formatDateFn(item.updateDate) }}</span>
              </div>
            </div>
          </div>
          <div class="content" v-else>
            <div class="tempty">暂无数据</div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-card>
</template>

<script>
import btnMore from "./btnMore";
import { formatDate } from "jw_utils/moment-date";
import ModelFactory from "jw_apis/model-factory";
// 最近浏览
const browsingHistory = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.sysconfigServer}/homepage/query/browsingHistory`,
  method: "get",
});
// 最近更新
const recentUpdates = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.sysconfigServer}/homepage/query/jwiRecentUpdates`,
  method: "get",
});
// 我检出的
const recentCheckOut = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.sysconfigServer}/homepage/query/jwiRecentCheckOut`,
  method: "get",
});
// 我的收藏
const findLatelyFavorites = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.mpmLibraryServer}/container/findLatelyFavorites`,
  method: "get",
});

const apiMap = {
  "view" : browsingHistory,
  "update" : recentUpdates,
  "checkOut" : recentCheckOut,
  "collect" : findLatelyFavorites,
}
export default {
  name: "findData",
  components: {
    btnMore
  },
  data() {
    this._initData();
    return {
      tabActive: "collect",
      loading:false,
      listdata: [
        // {
        //   name: "汽车动力系统，发动机安装图纸 1",
        //   time: "2021-12-15 00:38:26",
        //   oid: 1
        // },
        // {
        //   name: "汽车动力系统，发动机安装图纸 2",
        //   time: "2021-12-15 00:38:26",
        //   oid: 2
        // },
        // {
        //   name: "汽车动力系统，发动机安装图纸 3",
        //   time: "2021-12-15 00:38:26",
        //   oid: 3
        // },
        // {
        //   name: "汽车动力系统，发动机安装图纸 4",
        //   time: "2021-12-15 00:38:26",
        //   oid: 4
        // },
        // {
        //   name: "汽车动力系统，发动机安装图纸 5",
        //   time: "2021-12-15 00:38:26",
        //   oid: 5
        // },
        // {
        //   name: "汽车动力系统，发动机安装图纸 6",
        //   time: "2021-12-15 00:38:26",
        //   oid: 6
        // },
        // {
        //   name: "汽车动力系统，发动机安装图纸 7",
        //   time: "2021-12-15 00:38:26",
        //   oid: 7
        // },
        // {
        //   name: "汽车动力系统，发动机安装图纸 8",
        //   time: "2021-12-15 00:38:26",
        //   oid: 8
        // }
      ],
      iconMap: {
        Resource: "#jwi-ziyuan",
        Param: "#jwi-censhu",
        Part: "#jwi-fuliao",
        Document: "#jwi-wendang",
        Terminology: "#jwi-shuyu",
        Formula: "#jwi-guize",
        BasicRule: "#jwi-chengpin",
        Operation: "#jwi-gongxu",
        ProcessPlan: "#jwi-gongyiduixiang",
        Part: "#jwi-chanpinjiegou",
        BasicContainer:"jwi-jichuku",
        OperationContainer:"jwi-gongxuku",
        ProcessPlanContainer: "#jwi-gongyiguicheng",
        ProductContainer:"#jwi-chanpinku",
        Accessories:"#jwi-mbom"
      },
    };
  },
  computed: {},
  watch: {},
  methods: {
    formatDateFn(date) {
      return formatDate(date);
    },
    onSearch() {},
    onchangeTab(value) {
      console.log(value)
      this.listdata = []
      this.getList(value);
    },
    getList(tabActive){
      this.loading = true;
      let api = apiMap[tabActive]
      let param = api== findLatelyFavorites ? { size:8 }  : {account : Jw.getUser().account}
      api.execute(param)
        .then(res => {
          if(res){
            this.listdata = res
            this.loading = false;
          }
        })
        .catch(err => {
          this.$error(err.msg || this.$t("msg_failed"));
          this.loading = false;
        });
    },
    _initData() {
      this._tabs = [
        // { name: "最近浏览", key: "view" },
        { name: "最近更新", key: "update" },
        { name: "我检出的", key: "checkOut" },
        { name: "我的收藏", key: "collect" }
      ];
    }
  },
  created() {
    this.getList("collect");
  },
  mounted() {}
};
</script>

<style lang="less" scoped>
.ant-card {
  height: 100%;
}
.search {
  padding: 0 0 15px 0;
  .module-title {
    height: 60px;
    color: rgba(30, 32, 42, 0.85);
    display: flex;
    justify-content: space-between;
    .title {
      height: 24px;
      margin: 19px 0 17px 0;
    }
  }
  .select {
    display: flex;
  }
  .total-table {
    // height: calc(~"100% - 40px");
    height: 100%;
    .content {
      height: 100%;
      padding: 10px;
      overflow: auto;
      position: relative;
      &::-webkit-scrollbar {
        width: 0;
      }
      .content-item {
        display: flex;
        height: 40px;
        justify-content: space-between;
        align-items: center;
        .icon {
          margin: 0 16px;
        }
      }
      .content-item:hover {
        background: rgba(30, 32, 42, 0.06);
        border-radius: 5px;
      }
    }
  }
  .tempty{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  /deep/ ._mpm-tabs .ant-tabs-nav {
    margin-left: 2px;
  }
}
</style>
