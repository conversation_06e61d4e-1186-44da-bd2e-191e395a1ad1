<template>
  <a-card
    class="task shadow-wrap"
    :body-style="{ padding: 0, height: '100%' }"
  >
    <div class="total-table">
      <a-tabs
        v-model.trim="tabActive"
        @change="onchangeTab"
      >
        <a-button
          slot="tabBarExtraContent"
          type="link"
          @click='handleMore'
        >
          {{$t('txt_more')}}
        </a-button>
        <a-tab-pane
          v-for="item in _tabs"
          :tab="item.name"
          :key="item.key"
        >
          <!-- <div class="table-box">
            <jw-table :columns="columns" :dataSource="tableData" :showPage='false'> </jw-table>
          </div> -->
          <jw-table
            :columns="columns"
            :dataSource="tableData"
            :showPage='false'
          >
            <template #name="{ row }">
              <a-button
                type="link"
                @click="routerLink(row)"
              >{{row.name}}</a-button>
            </template>
          </jw-table>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-card>
</template>

<script>
import btnMore from "./btnMore";
import ModelFactory from "jw_apis/model-factory";
import { getCookie } from "jw_utils/cookie";
// 获取任务列表
const tasksModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/task/tasks`,
  method: "post",
});

// 获取已完成任务
const completeData = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/history/historic-task`,
  method: "post",
});
const user = Jw.getUser();
export default {
  name: "toDoTask",
  components: {
    btnMore,
  },
  data() {
    this._tabs = [
      { name: this.$t("txt_to_do_tasks"), key: "wait" },
      { name: this.$t("txt_completed_task"), key: "complete" },
    ];
    return {
      tabActive: "wait",
      tableData: [],
    };
  },
  computed: {
    columns() {
      if (this.tabActive == "wait") {
        return [
          {
            field: "name",
            title: this.$t("task_name"),
            minWidth: 150,
            slots: {
              default: "name",
            },
          },
          {
            field: "processInstanceName",
            title: this.$t("txt_to_process"),
            minWidth: 150,
          },
          {
            field: "description",
            title: this.$t("txt_operation_guide"),
            minWidth: 150,
          },
          {
            field: "createTime",
            title: this.$t("txt_create_date"),
            minWidth: 240,
            formatter: "date",
          },
          {
            field: "dueDate",
            title: this.$t("txt_stop_time"),
            minWidth: 240,
            formatter: "date",
          },
        ];
      } else {
        return [
          {
            field: "name",
            title: this.$t("task_name"),
            minWidth: 150,
            slots: {
              default: "name",
            },
          },
          {
            field: "processInstanceName",
            title: this.$t("txt_to_process"),
            minWidth: 150,
          },
          {
            field: "description",
            title: this.$t("txt_operation_guide"),
            minWidth: 150,
          },
          {
            field: "createTime",
            title: this.$t("txt_create_date"),
            minWidth: 240,
            formatter: ({ cellValue, column, row, rowIndex }) => {
              if (
                cellValue &&
                cellValue.split(".") &&
                cellValue.split(".").length &&
                cellValue.split(".")[0].split("T") &&
                cellValue.split(".")[0].split("T").length
              )
                return cellValue.split(".")[0].split("T").join(" ");
              else return "";
            },
          },
          {
            field: "dueDate",
            title: this.$t("txt_stop_time"),
            minWidth: 240,
            formatter: ({ cellValue, column, row, rowIndex }) => {
              if (
                cellValue &&
                cellValue.split(".") &&
                cellValue.split(".").length &&
                cellValue.split(".")[0].split("T") &&
                cellValue.split(".")[0].split("T").length
              )
                return cellValue.split(".")[0].split("T").join(" ");
              else return "";
            },
          },
        ];
      }
    },
  },
  created() {
    this.getTasksList();
  },
  methods: {
    getTasksList() {
      let params = {
        assignee: Jw.getUser().account,
        index: 1,
        size: 10,
        // nameLike: this.wait.nameLike,
      };
      tasksModel.execute(params).then((res) => {
        this.tableData = res.rows;
      });
    },
    getCompleteData() {
      let params = {
        taskAssignee: Jw.getUser().account,
        index: 1,
        size: 10,
        // taskNameLike: this.complete.nameLike,
        finished: true,
        tenantId: getCookie("tenantOid"),
      };
      completeData.execute(params).then((res) => {
        this.tableData = res.rows;
      });
    },
    handleMore() {
      this.$router.push("/wait-tasks");
    },
    routerLink(row) {
      // if (text.row.assignee === Jw.getUser().account   ) {
      //   this.routerLinkWait(text, record);
      // } else {
      //   this.routerLinkComplete(text, record);
      // }
      this.$router.push({
        path: "/process-task",
        query: {
          taskId: row.id,
          processInstanceId: row.processInstanceId,
        },
      });
    },
    onchangeTab() {
      if (this.tabActive === "wait") {
        this.getTasksList();
      } else {
        this.getCompleteData();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.ant-card {
  height: 100%;
  border: 0;
  margin: 0 8px 0px 16px;
}
.task {
  border-radius: 8px;
  box-shadow: 0 1px 2px -2px #e4e4e4, 0 3px 6px 0 #e4e4e4,
    0 5px 12px 4px #e4e4e4;
  .total-table {
    height: 100%;
    .ant-tabs {
      display: flex;
      flex-direction: column;
      height: 100%;
      /deep/.ant-tabs-content {
        height: 100%;
      }
      .jw-table {
        padding: 0 20px 20px;
      }
    }
  }
}
</style>
