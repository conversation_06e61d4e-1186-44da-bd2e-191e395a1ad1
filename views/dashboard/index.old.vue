<!--
 * @Author: barry
 -->
<template>
  <div class="home-panel">
    <div class="mpm-home">
      <img :src="getLogoUrl()" title="">
      <span>{{title}}</span>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  created() {},
  data() {
    return {
      title: Jw.title,
    };
  },

  methods: {
    getLogoUrl() {
      if (Jw.logoUrl) {
        return Jw.logoUrl;
      }else {
        return require("assets/image/logo.png");
      }
    }
  }
};
</script>

<style lang="less">
.home-panel {
  display: flex;
  height: 100%;
  color: #909399;
  font-size: 80px;
  flex-direction: column;
  justify-content: center;
  background: #fff;
  .mpm-home {
    display: flex;
    justify-content: center;
    > img {
      display: inline-block;
      width: 600px;
    }
    span {
      font-size: 80px;
      margin: 20px 0 0 20px;
    }
  }
}
</style>