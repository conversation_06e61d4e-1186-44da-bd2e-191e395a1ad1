<template>
  <div>
    <div class="table-card" v-for="item in allData" :key="item.name">
      <div class="card-title">
        <div class="title">{{ item.name }}</div>
        <div class="more" @click="seeMore">查看更多</div>
      </div>
      <div class="table-box">
        <jw-table
          :columns="item.columns"
          :dataSource="item.tableData"
          :showPage="false"
        >
        </jw-table>
        <a-card class="select-card" :body-style="{ padding: 0 }">
          <div class="select-title">
            <div class="select-num">已选中 3 项</div>
            <div class="clear" @click="clear">清空</div>
          </div>
          <div class="select-item" v-for="item in selectdata" :key="item.id">
            <div>
              <jw-icon type="#jwi-gongyiduixiang" class="icon" />
              <span class="describe">{{ item.describe }}</span>
            </div>
            <div class="close-circle">
              <a-icon type="close-circle" />
            </div>
          </div>
        </a-card>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      allData: [
        {
          name: "产品库 20",
          columns: [
            {
              field: "name",
              title: "产品库名称",
            },
            {
              field: "description",
              title: "描述",
            },

            {
              field: "updateBy",
              title: "变更数",
            },
            {
              field: "updateDate",
              title: "负责人",
            },
            {
              field: "createDate",
              title: "创建时间",
            },
            {
              field: "modelIcon",
              title: "操作",
            },
          ],
          tableData: [
            {
              oid: "59278dff-9bd7-451e-b8a0-480294de65d0",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "6bd41c6e-dce5-4c1f-9d66-05fc9ebf3cf2",
              name: "库1",
              description: null,
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "bfe6f2b8-8482-4696-88f5-743f2c4c5131",
              collection: false,
            },
            {
              oid: "dafb75ab-b5ba-4ac8-8a1b-5c8a7f5efb3a",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "鼓风机",
              description: "鼓风机",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
            {
              oid: "c7ac9a5d-4f04-432b-abd6-7fed3965f015",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "Product004",
              description: "Product004",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
            {
              oid: "0f8893be-6e82-4189-810c-19e58517a817",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "Product003",
              description: "Product003",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
            {
              oid: "ef654953-577b-4cb9-8ae6-fc2077e184a3",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "Product002",
              description: "Product002",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
          ],
        },
        {
          name: "对象 10",
          columns: [
            {
              field: "name",
              title: "对象名称",
            },
            {
              field: "description",
              title: "编码",
            },

            {
              field: "updateBy",
              title: "类型",
            },
            {
              field: "updateDate",
              title: "版本",
            },
            {
              field: "modelIcon",
              title: "操作",
            },
          ],
          tableData: [
            {
              oid: "59278dff-9bd7-451e-b8a0-480294de65d0",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "6bd41c6e-dce5-4c1f-9d66-05fc9ebf3cf2",
              name: "库1",
              description: null,
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "bfe6f2b8-8482-4696-88f5-743f2c4c5131",
              collection: false,
            },
            {
              oid: "dafb75ab-b5ba-4ac8-8a1b-5c8a7f5efb3a",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "鼓风机",
              description: "鼓风机",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
            {
              oid: "c7ac9a5d-4f04-432b-abd6-7fed3965f015",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "Product004",
              description: "Product004",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
            {
              oid: "0f8893be-6e82-4189-810c-19e58517a817",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "Product003",
              description: "Product003",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
            {
              oid: "ef654953-577b-4cb9-8ae6-fc2077e184a3",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "Product002",
              description: "Product002",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
          ],
        },
        {
          name: "变更 20",
          columns: [
            {
              field: "name",
              title: "名称",
            },
            {
              field: "description",
              title: "编码",
            },

            {
              field: "updateBy",
              title: "类型",
            },
            {
              field: "updateDate",
              title: "紧急程度",
            },
            {
              field: "createBy",
              title: "状态",
            },
            {
              field: "modelIcon",
              title: "创建",
            },
            {
              field: "createDate",
              title: "创建时间",
            },
          ],
          tableData: [
            {
              oid: "59278dff-9bd7-451e-b8a0-480294de65d0",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "6bd41c6e-dce5-4c1f-9d66-05fc9ebf3cf2",
              name: "库1",
              description: null,
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "bfe6f2b8-8482-4696-88f5-743f2c4c5131",
              collection: false,
            },
            {
              oid: "dafb75ab-b5ba-4ac8-8a1b-5c8a7f5efb3a",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "鼓风机",
              description: "鼓风机",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
            {
              oid: "c7ac9a5d-4f04-432b-abd6-7fed3965f015",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "Product004",
              description: "Product004",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
            {
              oid: "0f8893be-6e82-4189-810c-19e58517a817",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "Product003",
              description: "Product003",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
            {
              oid: "ef654953-577b-4cb9-8ae6-fc2077e184a3",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "Product002",
              description: "Product002",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
          ],
        },
        {
          name: "基线 20",
          columns: [
            {
              field: "name",
              title: "名称",
            },
            {
              field: "description",
              title: "分类",
            },
            {
              field: "createDate",
              title: "最后修改时间",
            },
            {
              field: "updateBy",
              title: "生命周期",
            },
            {
              field: "modelIcon",
              title: "操作",
            },
          ],
          tableData: [
            {
              oid: "59278dff-9bd7-451e-b8a0-480294de65d0",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "6bd41c6e-dce5-4c1f-9d66-05fc9ebf3cf2",
              name: "库1",
              description: null,
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "bfe6f2b8-8482-4696-88f5-743f2c4c5131",
              collection: false,
            },
            {
              oid: "dafb75ab-b5ba-4ac8-8a1b-5c8a7f5efb3a",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "鼓风机",
              description: "鼓风机",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
            {
              oid: "c7ac9a5d-4f04-432b-abd6-7fed3965f015",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "Product004",
              description: "Product004",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
            {
              oid: "0f8893be-6e82-4189-810c-19e58517a817",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "Product003",
              description: "Product003",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
            {
              oid: "ef654953-577b-4cb9-8ae6-fc2077e184a3",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "Product002",
              description: "Product002",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
          ],
        },
        {
          name: "资源库 20",
          columns: [
            {
              field: "name",
              title: "资源库名称",
            },
            {
              field: "description",
              title: "描述",
            },

            {
              field: "updateBy",
              title: "变更数",
            },
            {
              field: "updateDate",
              title: "负责人",
            },
            {
              field: "createDate",
              title: "创建时间",
            },
            {
              field: "modelIcon",
              title: "操作",
            },
          ],
          tableData: [
            {
              oid: "59278dff-9bd7-451e-b8a0-480294de65d0",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "6bd41c6e-dce5-4c1f-9d66-05fc9ebf3cf2",
              name: "库1",
              description: null,
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "bfe6f2b8-8482-4696-88f5-743f2c4c5131",
              collection: false,
            },
            {
              oid: "dafb75ab-b5ba-4ac8-8a1b-5c8a7f5efb3a",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "鼓风机",
              description: "鼓风机",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
            {
              oid: "c7ac9a5d-4f04-432b-abd6-7fed3965f015",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "Product004",
              description: "Product004",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
            {
              oid: "0f8893be-6e82-4189-810c-19e58517a817",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "Product003",
              description: "Product003",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
            {
              oid: "ef654953-577b-4cb9-8ae6-fc2077e184a3",
              type: "BasicLibrary",
              modelDefinition: "BasicLibrary",
              modelIcon: "处理",
              markForDelete: false,
              createBy: "已完成",
              createDate: "2021-06-20 10:35",
              updateBy: "变更请求审核",
              updateDate: "内容",
              tenantOid: "72cfb5a1-bfc3-4cd6-a520-b4ab6cb0cfb3",
              name: "Product002",
              description: "Product002",
              open: true,
              containerOid: null,
              containerType: null,
              catalogType: null,
              catalogOid: null,
              levelForSecrecy: 0,
              teamTemplateOid: "e6c89b50-0a17-4fce-9953-27d097528029",
              collection: false,
            },
          ],
        },
      ],
      selectdata: [
        {
          describe: "DOC000001,火箭尾段技术规格说明书，Design，A,1",
          id: 1,
        },
        {
          describe: "DOC000001,火箭尾段技术规格说明书，Design，A,1",
          id: 2,
        },
        {
          describe: "DOC000001,火箭尾段技术规格说明书，Design，A,1",
          id: 3,
        },
      ],
    };
  },
  methods: {
    seeMore() {},
    clear() {},
  },
};
</script>

<style lang="less" scoped>
.table-card {
  margin-bottom: 20px;
  .card-title {
    height: 24px;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      font-weight: 500;
      font-size: 16px;
      color: rgba(30, 32, 42, 0.85);
    }
    .more {
      font-weight: 400;
      font-size: 14px;
      color: #255ed7;
      cursor: pointer;
    }
  }
  .table-box {
    height: 280px;
    position: relative;
  }
  .select-card {
    position: absolute;
    top: 0;
    right: 0;
    width: 510px;
    height: 213px;
    background: #ffffff;
    box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08),
      0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);
    border-radius: 4px;
    padding: 18px 16px 21px 16px;
    .select-title {
      height: 24px;
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .select-num {
        font-weight: 500;
        font-size: 16px;
        color: rgba(30, 32, 42, 0.45);
      }
      .clear {
        font-weight: 400;
        font-size: 14px;
        color: #255ed7;
        cursor: pointer;
      }
    }
    .select-item {
      width: 478px;
      height: 40px;
      background: rgba(30, 32, 42, 0.04);
      border-radius: 4px;
      margin-bottom: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .icon {
        margin: 0 8px 0 21px;
      }
      .close-circle {
        margin-right: 16px;
      }
      .close-circle /deep/.anticon {
        font-size: 16px;
      }
    }
  }
}
</style>
