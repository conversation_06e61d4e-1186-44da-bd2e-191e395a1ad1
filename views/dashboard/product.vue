<template>
    <div class="product shadow-wrap">
		<Echart title="我参与的产品" :options="options" :height="height100/2-48">
			<template slot="tool">
				<btn-more></btn-more>
			</template>
		</Echart>
  </div>
</template>

<script>
import Echart from 'components/echart';
import btnMore from './btnMore';
export default {
	name: 'dbProduct',
	props: [
        'height100',
    ],
    components: {
		Echart,
    	btnMore,
	},
    data() {
        return {
            options: {},
        };
    },
    computed: {},
    watch: {},
    methods: {

    },
    created() {

    },
    mounted() {

    },
}
</script>

<style lang="less" scoped>
/deep/ .jw-echart .jw-echart-header {
  min-height: 40px;
  border-bottom: 0;
  color: rgba(30, 32, 42, 0.85);
  font-weight: 700;
}
</style>
