<template>
	<div class="change shadow-wrap">
		<Echart title="变更统计" :options="options" :height="height100/2-48">
			<template slot="tool">
				<btn-more></btn-more>
			</template>
			<template slot="footer">
				<div class="update-legend flex flex-column justify-center" :style="{height:height100/2-48+'px'}">
					<div class="legend-item" v-for="item in updateData" :key="item.name">
						<div class="flex justify-between align-center">
							<div class="flex justify-between align-center font-700 color65">
								<span class="legend-color" :style="{background:item.color}"></span>
								<span>{{item.name}}</span>
							</div>
							<div class="font-12 text-left">
								<div class="color45">数量</div>
								<div class="color85 font-700">{{item.value}}</div>
							</div>
							<div class="font-12 text-left">
								<div class="color45">占比</div>
								<div class="color85 font-700">{{item.percent}}</div>
							</div>
						</div>
					</div>
				</div>
			</template>
		</Echart>
	</div>
</template>

<script>
import Echart from 'components/echart';
import btnMore from './btnMore';
export default {
	name: 'dbUpdate',
	props: [
        'height100',
    ],
	components: {
		Echart,
		btnMore,
	},
	data() {
		return {
			dots: [],
			updateData: [
				{value: 52, name: '进行中', percent: '52%', color: '#75a4f0'},
				{value: 30, name: '已完成', percent: '30%', color: '#8ad459'},
				{value: 3, name: '中止', percent: '3%', color: '#f69c41'},
				{value: 15, name: '取消', percent: '15%', color: '#6d7e9d'},
			],
			options: {
				dimensions: ['count'],
				keys: ['进行中', '已完成', '中止', '取消'],
				source: {
					count: [52, 30, 3, 15],
				},
				color: ['#75a4f0', '#8ad459', '#f69c41', '#6d7e9d'],
				tooltip: {
					trigger: 'item',
					formatter: function(params) {
						let dotColor = `<span style="display:inline-block;margin-right:5px;
							width:8px;height:8px;border-radius:4px;
							background-color:${params.color}"></span>`;
						return (`<div style="font-size:12px;color:rgba(30,32,42,0.45)">${params.name}</div>
							<div style="font-size:12px;color:#000;">${dotColor} ${params.seriesName}
							<span style="margin-left:30px;">${params.percent}%</span></div>`)
					},
				},
				series: [
					{
						name: '数量占比',
						type: 'pie',
						radius: '60%',
						label: {
							formatter: ['{b|{b}}', '{d|{d}个}'].join('\n'),
							// formatter: '{dot|●}{b|{b}}\n{d|{d}个}',
							rich: {
								// dot: {
								// 	fontSize: 16,
								// 	padding: [0, 5],
								// },
								b: {
									color: '#000',
								},
								d: {
									color: 'rgba(30,32,42,0.45)',
									padding: [5, 0, 0, 0]
								}
							},
						},
					},
				],
			},
		};
	},
	computed: {},
	watch: {},
	methods: {},
	created() {},
	mounted() {},
};
</script>

<style lang="less" scoped>
/deep/.jw-echart .jw-echart-header {
  min-height: 40px;
  border-bottom: 0;
  color: rgba(30, 32, 42, 0.85);
  font-weight: 700;
}
/deep/.ant-spin-container {
	display: flex;
	justify-content: space-between;
	& > div {
		width: 50%;
	}
	.update-legend {
		width: 40%;
		margin-right: 10px;
		overflow: auto;
		&::-webkit-scrollbar{
            width: 0;
        }
		.legend-item {
			padding: 8px 10px;
			margin: 5px;
			border: 1px solid rgba(30, 32, 42, 0.15);
			border-radius: 4px;
			&:hover {
				border: 1px solid #4b80e3;
			}
			.legend-color {
				display: inline-block;
				margin-right: 8px;
				width: 16px;
				height: 16px;
				border-radius: 50%;
			}
		}
	}
}
</style>
