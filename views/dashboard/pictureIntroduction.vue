<template>
  <a-card :body-style="{ padding: 0, height: '100%' }" class="advert">
    <div class="img">
      <div style="padding:10px 0 0 10px">
        <span style="font-size:26px;color:#fff">{{$t('txt_product_data_mannange')}}</span>
        <span style="font-size:16px;color:#fff">{{$t('txt_jw_desc_start')}}</span>
      </div>
      <div class='details'>
        {{$t('txt_jw_desc')}}
      </div>
    </div>
  </a-card>
</template>

<script>
export default {
  name: "pictureIntroduction",
  data() {
    return {};
  },
  computed: {},
  watch: {},
  methods: {},
  created() {},
  mounted() {},
};
</script>

<style lang="less" scoped>
.ant-card.advert {
  padding: 0 8px 0px 16px;
  height: 140px;
  border: 0;
  .img {
    padding: 30px 0 14px 38px;
    width: 100%;
    height: 100%;
    background: url("../../assets/image/home.png") no-repeat;
    background-size: cover;
    background-position: center center;
    .details {
      font-size: 14px;
      color: #fefefe;
      margin-top: 5px;
      padding: 0 0 0 10px;
      width: 50%;
      overflow: hidden;
      text-overflow: ellipsis;
      opacity: 0.65;
      // white-space: nowrap;
    }
  }
}
</style>
