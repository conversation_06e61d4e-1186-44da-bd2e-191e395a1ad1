<template>
  <a-card
    class="news shadow-wrap"
    :body-style="{ padding: 0, height: '100%' }"
  >
    <div class="module-title font-16 font-500">
      <div class="title">{{ $t("txt_recent_browse") }}</div>
      <!-- <btn-more></btn-more> -->
    </div>
    <div class="new-wrap">
      <div class="new-wrap-scroll">
        <div
          class="new-item"
          v-for="(item,index) in browsingHistoryList"
          :key="index"
        >
          <div
            class="font-14 pointer text-ellipsis"
            @click="goToDetails(item)"
          >
            <!-- [Workflow] 变更任务1001变更批准 -->
            {{ item.bizName }}, {{ item.bizNumber
						}}{{ item.displayVersion ? "，" : "" }} {{ item.displayVersion }}
          </div>
          <div class="new-item-time font-12 color65 text-ellipsis">
            {{formatDateFn(item.createDate)}}
          </div>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script>
import btnMore from "./btnMore";
import ModelFactory from "jw_apis/model-factory";
import { formatDate } from "jw_utils/moment-date";
// 最近浏览
const browsingHistory = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.sysconfigServer}/homepage/query/browsingHistory`,
  method: "get",
});
export default {
  name: "latestNews",
  props: ["height100"],
  components: {
    btnMore,
  },
  data() {
    return {
      browsingHistoryList: [],
    };
  },
  computed: {},
  watch: {},
  methods: {
    formatDateFn(date) {
      return formatDate(date);
    },
    getList() {
      browsingHistory
        .execute({ account: Jw.getUser().account })
        .then((res) => {
          if (res) {
            this.browsingHistoryList = res;
            console.log("res", res);
          }
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    goToDetails(item) {
      let row = JSON.parse(JSON.stringify(item));
      row.oid = row.bizOid;
      row.type = row.bizType;
      console.log("row", row);
      Jw.jumpToDetail(row);
    },
  },
  created() {
    this.getList();
  },
  mounted() {},
};
</script>

<style lang="less" scoped>
.ant-card {
  height: 100%;
}
.news {
  box-shadow: 0 1px 2px -2px #e4e4e4, 0 3px 6px 0 #e4e4e4,
    0 5px 12px 4px #e4e4e4;
  border-radius: 8px;
  padding: 0 21px 0 20px;
  .module-title {
    font-weight: 500;
    height: 60px;
    color: rgba(30, 32, 42, 0.85);
    display: flex;
    justify-content: space-between;
    .title {
      height: 24px;
      margin: 19px 0 17px 0;
    }
  }
  .new-wrap {
    height: calc(~"100% - 60px");
    overflow: auto;
    .new-wrap-scroll {
      margin-left: 3px;
      border-left: 1px solid rgba(30, 32, 42, 0.06);
    }
    &::-webkit-scrollbar {
      width: 0;
    }
    .new-item {
      position: relative;
      width: calc(~'100% - 10px');
      padding: 12px;
      margin-left: 10px;
      // margin-bottom: 8px;
      &:before {
        content: "";
        display: inline-block;
        position: absolute;
        top: 50%;
        left: -14px;
        width: 8px;
        height: 8px;
        border-radius: 4px;
        background: #6395fa;
      }
      .new-item-time {
        margin-bottom: 3px;
      }
    }
  }
}
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.pointer {
  cursor: pointer;
}
</style>
