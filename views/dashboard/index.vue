<template>
	<div style="background-color: #fff; height: 100%" class="_mpm-base-panel dashboard-panel">
		<div class="content">
			<!-- 自定义布局项 -->
			<grid-layout
				:layout.sync="layout"
				:col-num="24"
				:row-height="39"
				:is-draggable="true"
				:is-resizable="true"
				:is-mirrored="false"
				:vertical-compact="true"
				:margin="[8, 8]"
				:use-css-transforms="true"
				@layout-updated="layoutUpdatedEvent"
			>
				<grid-item
					v-for="item in layout"
					:x="item.x"
					:y="item.y"
					:w="item.w"
					:h="item.h"
					:i="item.i"
					:minW="item.minW"
					:key="item.i"
					@resized="resizedEvent"
				>
					<pictureIntroduction
						v-if="item.name === '图片介绍'"
						:id="item.i"
					></pictureIntroduction>
					<!-- <findData v-if="item.name === '查找数据'" :id="item.i"></findData> -->
					<toDoTask v-if="item.name === '待办任务'" :id="item.i"></toDoTask>
					<latestNews
						v-if="item.name === '最近浏览'"
						:id="item.i"
					></latestNews>
				</grid-item>
			</grid-layout>
		</div>
		<SearchDialog ref="ref_search_dialog"></SearchDialog>
	</div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory"
import { jwToolbar } from "jw_frame"
import { GridLayout, GridItem } from "vue-grid-layout"
import latestNews from "./latestNews"
import findData from "./findData.vue"
import toDoTask from "./toDoTask.vue"
import pictureIntroduction from "./pictureIntroduction.vue"
import { initPanelsLayout } from "./layout"
import SearchDialog from "./searchDialog.vue"
// 初始化用户登录权限
const initUserPermission = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/v1/permission/policy/initPermission`,
  method: "get",
})
export default {
	name: "dashboard",
	inject: ["setBreadcrumb", "addBreadcrumb"],
	components: {
		jwToolbar,
		GridLayout,
		GridItem,
		pictureIntroduction,
		findData,
		toDoTask,
		latestNews,
		SearchDialog,
	},
	data() {
		// this._initCommonData()
		return {
			pictureFlag: false,
			layoutRes: [],
			layout: [],
		}
	},
	computed: {},
	watch: {},
	methods: {
		initUserPermission(){
      initUserPermission
      .execute()
        .then(data => {})
        .catch(err => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
		_initCommonData() {
			let a = window.localStorage.getItem("index_") || null
			if (a == 1) {
				this._pageTitle = 0
				this.settabTitle()
			}else if(a==2){
				this._pageTitle = this.$t("page_index_tab")
				window.localStorage.setItem("index_", 1)
			}
			 else {
				this._pageTitle = this.$t("page_index_tab")
				window.localStorage.setItem("index_", 1)
			}
			console.log("this._pageTitle", this._pageTitle)
		},
		settabTitle() {
			setTimeout(() => {
				let breadcrumbData = [{ name: this.$t("page_index_tab") }]
				this.setBreadcrumb(breadcrumbData)
			}, 100)
		},
		// 初始化页面布局
		initPanel() {
			// const layout = JSON.parse(window.sessionStorage.getItem('layout'))
			// if (layout === null) {
			//   this.layout = initPanelsLayout
			// } else {
			//   this.layout = layout
			// }
			this.layout = initPanelsLayout
		},
		layoutUpdatedEvent(newLayout) {
			console.log("Updated layout: ", newLayout)
			if (this.pictureFlag) {
				this.layoutRes = this.layout
				this.pictureFlag = false
			} else {
				this.layoutRes = newLayout
			}
			// 可将更新后的layout数据传给后端
			// this.layout = newLayout
			// window.sessionStorage.setItem('layout', JSON.stringify(this.layout))
		},
		resizedEvent(i, newH, newW, newHPx, newWPx) {
			if (i === 0) {
				console.log(
					"RESIZED i=" +
						i +
						", H=" +
						newH +
						", W=" +
						newW +
						", H(px)=" +
						newHPx +
						", W(px)=" +
						newWPx
				)
				if (newW / newH != 6) {
					if (newH > 4) newH = 4
					let tempW = 6 * newH
					let temp = JSON.parse(JSON.stringify(this.layout))
					temp[0].h = newH
					temp[0].w = tempW
					this.layout = temp
					this.pictureFlag = true
					console.log("this.layout", this.layout)
				}
			}
		},
		onToolClick() {
			this.$refs.ref_search_dialog.show({})
		},
	},
	created() {
    	this.setBreadcrumb([{ name: this.$t("page_index_tab") }]);
		this.initPanel();
		this.initUserPermission();
	},
	beforeDestroy() {
		// 防止重复调用接口处理
		// window.localStorage.setItem("index_",'')
	},
	mounted() {
		window.onbeforeunload = (e) => {
			//刷新时弹出提示
			// 防止重复调用接口处理
			window.localStorage.setItem("index_", "")
		}
	},
}
</script>

<style lang="less" scoped>
.dashboard-panel {
	.header {
		height: 40x;
		line-height: 40px;
		padding-left: 20px;
	}
	.dashboard-title {
		font-size: 20px;
		font-weight: 600;
	}
	.view-control {
		margin-left: 10px;
	}
	.content {
		height: 100%;
	}
}
</style>
