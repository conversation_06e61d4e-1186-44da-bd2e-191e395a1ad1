<template>
  <div v-if="init_visible">
    <a-modal
      class="search-dialog-warp"
      v-dialogDrag
      :title="title"
      :visible="visible"
      :confirm-loading="confirmLoading"
      width="70%"
      @cancel="handleCancel"
      footer=""
    >
      <div class="total">
        <a-input-search
          v-model.trim="searchData"
          placeholder="请输入搜索关键字"
          @search="onSearch"
          allow-clear
        />
        <div class="total-box">
          <a-card
            :body-style="{ padding: 0, height: '96px' }"
            class="history-card"
            v-show="historyFlag"
          >
            <div class="search-history">
              <div class="text">搜索历史</div>
              <div @click="deleteHistory">
                <jw-icon type="jwi-icondelete" />
              </div>
            </div>
            <div class="history-list">
              <div class="list-item" v-for="item in historyList" :key="item.name">
              {{item.name}}
              </div>
            </div>
          </a-card>
          <div class="total-box-left">
            <div class="more-conditions">更多条件</div>
            <div class="saved-conditions">
              <div class="conditions-top">
                <div class="text">我保存过的条件</div>
                <div class="clear">清空</div>
              </div>
              <div class="conditions-bottom">
                <div class="conditions-bottom-content">
                  条件：类型=资源库、产品库、文档、变更请求、部件、问题、需求、任务、通知：状态=全部
                </div>
                <div class="conditions-bottom-content">
                  条件：类型=资源库、产品库、文档、变更请求、部件、问题、需求、任务、通知：状态=全部
                  <div class="close-circle">
                    <a-icon type="close-circle" />
                  </div>
                </div>
              </div>
            </div>
            <div class="all-conditions">
              <div class="all-conditions-top">
                需满足以下<span class="all-text">全部</span>条件
              </div>
              <div class="all-conditions-center">
                  <a-select default-value="类型" style="width: 135px" @change="handleChange" mr8>
                    <a-select-option value="jack">
                      Jack
                    </a-select-option>
                    <a-select-option value="lucy">
                      Lucy
                    </a-select-option>
                  </a-select>
                  <a-select default-value="包含" style="width: 135px" @change="handleChange">
                    <a-select-option value="jack">
                      Jack
                    </a-select-option>
                    <a-select-option value="lucy">
                      Lucy
                    </a-select-option>
                  </a-select>
                  <a-input placeholder="成员" allow-clear class="conditions-input"/>
                  <div class="close-circle">
                      <a-icon type="close-circle" />
                  </div>
              </div>
              <div class="all-conditions-bottom">
                <a-button mr8>添加一条</a-button>
                <a-button mr8>保存搜索条件</a-button>
              </div>
            </div>
          </div>
          <div class="total-box-right">
            <a-tabs v-model.trim="activeComponent" class="_mpm-tabs" @change="onchangeTab">
              <a-tab-pane
                v-for="item in tabs"
                :tab="`${item.name}(${item.num})`"
                :key="item.key"
                style="height: 100%"
              >
                <div class="content">
                  <component :is="getComponent()" ></component>
                </div>
              </a-tab-pane>
            </a-tabs>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>
<script>
import Empty from "components/empty.vue";
import TableList from './component/tableList.vue'

const tableMap = {
  all:TableList,
  productLibrary: Empty,
};
export default {
  components: {
    TableList,
    Empty
  },
  data() {
    return {
      visible: false,
      init_visible: false,
      confirmLoading: false,
      title: "全局搜索",
      searchData: "",
      tabs: [
        { name: "全部", num: 0, key: "all" },
        { name: "产品库", num: 0, key: "productLibrary" },
        { name: "资源库", num: 0, key: "resourcePool" },
        { name: "任务", num: 0, key: "task" },
        { name: "特性", num: 0, key: "characteristic" },
        { name: "配置项", num: 0, key: "configurationItem" },
        { name: "变更", num: 0, key: "change" },
        { name: "基线", num: 0, key: "baseline" },
        { name: "通知", num: 0, key: "notice" },
      ],
      historyList: [
        { name: "产品" },
        { name: "动力系统" },
        { name: "荣耀10" },
        { name: "荣耀8" },
        { name: "P40" },
        { name: "40mm" },
      ],
      historyFlag: false,
      activeComponent: "all",
    };
  },
  methods: {
    show(options) {
      if (!this.init_visible) {
        this.init_visible = true;
      }
      let { title } = options;
      if (title) {
        this.title = title;
      }

      this.visible = true;

      return new Promise((resolve, reject) => {
        this.handleCancel = () => {
          this.hide();
          reject("cancel");
        };

        this.handleOk = () => {
          this.hide();
          resolve("ok");
        };
      });
    },
    hide() {
      this.visible = false;
    },
    onSearch() {
      this.historyFlag = true;
    },
    onchangeTab(key) {
      console.log('key',key);
      console.log('tabActive',this.activeComponent)
    },
    deleteHistory() {
      console.log("aaa");
      this.historyFlag = false;
    },
    handleChange(value) {
      console.log(`selected ${value}`);
    },
    getComponent() {
      return tableMap[this.activeComponent];
    },
  },
};
</script>
<style lang="less" scoped>
.search-dialog-warp {
  /deep/ .ant-modal-body {
    padding: 0 24px;
    height: 600px;
  }
  .total {
    height: 100%;
    position: relative;
    &-box {
      height: calc(~"100% - 32px");
      padding-top: 5px;
      display: flex;
      z-index: 1;
      &-left {
        width: 30%;
        border-right: 1px solid rgba(30, 32, 42, 0.15);
        .more-conditions {
          height: 45px;
          line-height: 45px;
          border-bottom: 1px solid rgba(30, 32, 42, 0.15);
          box-sizing: border-box;
        }
        .saved-conditions {
          margin: 10px 0;
          padding-right: 10px;
          .conditions-top {
            height: 22px;
            margin: 15.5px 0 9px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 400;
            font-size: 14px;
            color: rgba(30,32,42,0.45);
            .clear {
              // color: @majorPrimaryColor;
              color: #255ed7;
            }
          }
          .conditions-bottom-content {
            position: relative;
            height: 41px;
            padding: 0 5px 0 8px;
            margin-bottom: 9px;
            font-weight: 400;
            font-size: 12px;
            color: rgba(30, 32, 42, 0.65);
            line-height: 20px;
            border: 1px solid rgba(30, 32, 42, 0.15);
            background: rgba(30, 32, 42, 0.04);
            border-radius: 4px;
          }
        }
        .all-conditions {
          margin-top: 19px;
          padding-right: 10px;
          .all-conditions-top {
            height: 32px;
            line-height: 32px;
            margin-bottom: 8px;
            .all-text {
              display: inline-block;
              color: #255ed7;
              padding: 0 12px;
            }
          }
          .all-conditions-center{
            position: relative;
            height: 104px;
            background: rgba(30,32,42,0.04);
            border-radius: 4px;
            padding: 16px;
            margin-bottom: 16px;
            .conditions-input{
              width: 280px;
              margin-top: 8px;
            }
          }
          .all-conditions-bottom {
            display: flex;
          }
        }
      }
      &-right {
        width: 70%;
        .content{
          position: relative;
          height: 100%;
          padding: 15px 0 15px 19px;
          overflow: auto;
          &::-webkit-scrollbar {
            width: 0;
          }
        }
      }
    }
    .history-card {
      position: absolute;
      top: 36px;
      left: 0;
      z-index: 999;
      width: 100%;
      background-image: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 33%);
      box-shadow: 0 6px 16px -8px rgba(0,0,0,0.08), 0 9px 28px 0 rgba(0,0,0,0.05), 0 12px 48px 16px rgba(0,0,0,0.03);
      border-radius: 4px;
      padding: 0 16px;
      .search-history {
        height: 56px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .history-list {
        height: 40px;
        display: flex;
        .list-item{
          height: 22px;
          line-height: 22px;
          background: rgba(30,32,42,0.04);
          border: 1px solid rgba(30,32,42,0.15);
          border-radius: 4px;
          margin: 0 8px 19px 0;
          padding: 0 8px;
          font-weight: 400;
          font-size: 12px;
          color: rgba(30,32,42,0.65);
        }
      }
    }
  }
  .text {
    font-weight: 400;
    font-size: 14px;
    color: rgba(30, 32, 42, 0.45);
  }
  .close-circle{
    position: absolute;
    top: -9px;
    right: -5px;
  }
  .close-circle /deep/.anticon {
    font-size: 16px;
  }
  /deep/ .ant-tabs-nav .ant-tabs-tab {
    margin: 0 5px 0 0;
  }
  [mr8]{
    margin-right: 8px;
  }
}
</style>
