<template>
    <div class="collection shadow-wrap" :style="{height:height100+'px'}">
        <a-collapse accordion default-active-key="browse">
            <a-collapse-panel v-for="item in collapseData" :showArrow="false"
             :header="item.name" :key="item.component">
                <template v-if="item.component==='browse'">
                    <div slot="extra" @click="handleClick">
                        <a-radio-group class="browse-wrap" default-value="all" v-model.trim="browseType"
                            button-style="solid" @change="changeType">
                            <a-radio-button value="all">All</a-radio-button>
                            <a-radio-button v-for="item in browseIconList" :key="item" :value="item">
                            <svg class="jwifont" aria-hidden="true">
                                <use :xlink:href="item"></use>
                            </svg>
                            </a-radio-button>
                        </a-radio-group>
                    </div>
                    <div>
                        <a-input-search placeholder="请输入搜索关键字" style="width:100%" @search="onSearch" />
                        <div class="search-list">
                            <div v-if="isShowList" class="font-12 text-center">暂无数据</div>
                            <div v-else class="search-item width-full text-ellipsis"
                                v-for="item in browseList" :key="item.id">
                                <svg class="jwifont" aria-hidden="true">
                                    <use :xlink:href="item.type"></use>
                                </svg>
                                <span class="search-title font-14 color65 text-link" :title="item.info">{{item.info.length>17?item.info.slice(0,20)+'...':item.info}}</span>
                            </div>
                        </div>
                    </div>
                </template>
                <p v-else>{{ item.name }}</p>
            </a-collapse-panel>
        </a-collapse>
    </div>
</template>
<script>

export default {
    name: 'dbCollection',
    props:[
        'height100',
    ],
    components: {},
    data() {
        return {
            collapseData: [
                {name: '我的收藏', component: 'collection'},
                {name: '最近浏览', component: 'browse'},
                {name: '最近更新', component: 'update'},
                {name: '我检出的', component: 'checkout'},
            ],
            isShowList: false,
            browseType: 'all',
            browseIconList: [
                '#jwi-product',
                '#jwi-library',
                '#jwi-document',
                '#jwi-part-drawing',
            ],
            browseList: [
                {
                    id: 1,
                    type: '#jwi-product',
                    info: 'aa这里最多支持二十个字符的标题展示二十个字#这里最多支持二十个字符的标题展示二十个字',
                },
                {
                    id: 2,
                    type: '#jwi-library',
                    info: 'aa这里最多支持二十个字符的标题展示二十个字',
                },
                {
                    id: 3,
                    type: '#jwi-document',
                    info: 'bb这里最多支持二十个字符的标题展示二十个字#这里最多支持二十个字符的标题展示二十个字',
                },
                {
                    id: 4,
                    type: '#jwi-part-drawing',
                    info: 'bb这里最多支持二十个字符的标题展示二十个字',
                },
                {
                    id: 5,
                    type: '#jwi-product',
                    info: 'cc这里最多支持二十个字符的标题展示二十个字#这里最多支持二十个字符的标题展示二十个字',
                },
                {
                    id: 6,
                    type: '#jwi-library',
                    info: 'cc这里最多支持二十个字符的标题展示二十个字',
                },
                {
                    id: 7,
                    type: '#jwi-document',
                    info: 'cc这里最多支持二十个字符的标题展示二十个字#这里最多支持二十个字符的标题展示二十个字',
                },
                {
                    id: 8,
                    type: '#jwi-part-drawing',
                    info: 'dd这里最多支持二十个字符的标题展示二十个字',
                },
                {
                    id: 9,
                    type: '#jwi-product',
                    info: 'dd这里最多支持二十个字符的标题展示二十个字#这里最多支持二十个字符的标题展示二十个字',
                },
                {
                    id: 10,
                    type: '#jwi-library',
                    info: '这里最多支持二十个字符的标题展示二十个字',
                },
                {
                    id: 11,
                    type: '#jwi-document',
                    info: '这里最多支持二十个字符的标题展示二十个字#这里最多支持二十个字符的标题展示二十个字',
                },
                {
                    id: 12,
                    type: '#jwi-part-drawing',
                    info: '这里最多支持二十个字符的标题展示二十个字',
                },
                {
                    id: 13,
                    type: '#jwi-product',
                    info: '这里最多支持二十个字符的标题展示二十个字#这里最多支持二十个字符的标题展示二十个字',
                },
                {
                    id: 14,
                    type: '#jwi-library',
                    info: '这里最多支持二十个字符的标题展示二十个字',
                },
                {
                    id: 15,
                    type: '#jwi-document',
                    info: '这里最多支持二十个字符的标题展示二十个字#这里最多支持二十个字符的标题展示二十个字',
                },
                {
                    id: 16,
                    type: '#jwi-part-drawing',
                    info: '这里最多支持二十个字符的标题展示二十个字',
                },
            ],
            dftBrowseList: [],
        };
    },
    computed: {},
    watch: {},
    methods: {
        onSearch(val) {
            this.browseList = this.dftBrowseList;
            if (!val) {
                this.isShowList = false;
                this.browseList = this.dftBrowseList;
            } else {
                let list = [];
                this.browseList.forEach(item => {
                    if (item.info.indexOf(val) > -1) {
                        list.push(item);
                    }
                })
                if (list.length > 0) {
                    this.isShowList = false;
                    this.browseList = list;
                } else {
                    this.isShowList = true;
                }
            }
        },
        handleClick(e) {
            e.stopPropagation();
        },
        changeType(e) {
            console.log(e)
            console.log(e.target.value)
        },
    },
    created() {

    },
    mounted() {
        this.dftBrowseList = this.browseList;
    },
}
</script>

<style lang="less" scoped>
.ant-collapse {
    height: 100%;
    border: 0;
    background: var(--light);
    /deep/ .ant-collapse-header {
        font-size: 14px;
        font-weight: 700;
        color: rgba(30, 32, 42, 0.85);
    }
    .ant-collapse-item.ant-collapse-item-active {
        height: calc(100% - 139px);
        /deep/ .ant-collapse-content {
            width: 100%;
            height: calc(100% - 60px);
        }
        /deep/ .ant-collapse-content-box {
            height: 100%;
            & > div {
                height: 100%;
            }
        }
    }
}
.browse-wrap {
    margin-top: -3px;
    .jwifont {
        width: 16px;
        height: 16px;
        vertical-align: text-top;
    }
    .ant-radio-button-wrapper {
        width: 32px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        padding: 0;
        font-weight: normal;
    }
}
.search-list {
    height: calc(100% - 32px);
    margin-top: 10px;
    overflow: auto;
    &::-webkit-scrollbar{
        width: 0;
    }
    .jwifont {
        width: 16px;
        height: 16px;
        vertical-align: text-bottom;
    }
    .search-item {
        margin: 8px 0;
    }
    .search-title {
        margin-left: 5px;
        &:hover {
            color: #75a4f0;
            text-decoration: underline;
        }
    }
}
</style>
