<template>
  <div class="info-wrapper">
    <a-row>
      <a-col :span="24">
        <div class="content-item">
          <div class="label">{{ $t('txt_name') }}</div>
          <div class="value">{{ classification.name }}</div>
        </div>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="12">
        <div class="group-name"><span>{{ $t('txt_source_system_configuration') }}</span></div>
      </a-col>
    </a-row>
    <a-row :gutter="24">
      <a-col :span="12">
        <div class="content-item">
          <div class="label">{{ $t('txt_system') }}</div>
          <div class="value">{{ classification.sourceSystemName }}</div>
        </div>
      </a-col>
      <a-col :span="12">
        <div class="content-item">
          <div class="label">{{ $t('txt_system_version') }}</div>
          <div class="value">{{ classification.sourceSystemVersion }}</div>
        </div>
      </a-col>
    </a-row>
    <a-row :gutter="24">
      <a-col :span="12">
        <div class="content-item">
          <div class="label">{{ $t('txt_data_base') }}</div>
          <div class="value">{{ classification.sourceSystemDbType }}</div>
        </div>
      </a-col>
      <a-col :span="12">
        <div class="content-item">
          <div class="label">{{ $t('txt_data_base_version') }}</div>
          <div class="value">{{ classification.sourceSystemDbVersion }}</div>
        </div>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="12">
        <div class="group-name"><span>{{ $t('txt_target_system_configuration') }}</span></div>
      </a-col>
    </a-row>
    <a-row :gutter="24">
      <a-col :span="12">
        <div class="content-item">
          <div class="label">{{ $t('txt_system') }}</div>
          <div class="value">{{ classification.targetSystemName }}</div>
        </div>
      </a-col>
      <a-col :span="12">
        <div class="content-item">
          <div class="label">{{ $t('txt_system_version') }}</div>
          <div class="value">{{ classification.targetSystemVersion }}</div>
        </div>
      </a-col>
    </a-row>
    <a-row :gutter="24">
      <a-col :span="12">
        <div class="content-item">
          <div class="label">{{ $t('txt_data_base') }}</div>
          <div class="value">{{ classification.targetSystemDbType }}</div>
        </div>
      </a-col>
      <a-col :span="12">
        <div class="content-item">
          <div class="label">{{ $t('txt_data_base_version') }}</div>
          <div class="value">{{ classification.targetSystemDbVersion }}</div>
        </div>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="24">
        <div class="content-item">
          <div class="label">{{ $t('txt_description') }}</div>
          <div class="value">{{ classification.description }}</div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import { EventBus, EVENT_BUS_NAMESPACE } from 'common/event-bus'
import {
  classificationFindByOid,
} from "apis/setl"
export default {
  components: {
  },
  props: {
    oid: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      classification: {},
    };
  },
  watch: {
    oid: function (value) {
      this.findByOid(value)
    }
  },
  methods: {
    findByOid(oid) {
      return classificationFindByOid({ oid: oid })
        .then((data = {}) => {
          this.classification = data
        })
    },
  },
  created() {
    this.findByOid(this.oid)
  },
  mounted() {
    EventBus.$on(EVENT_BUS_NAMESPACE.SETL_CLASSFICATION_UP, ({oid}) => {
      this.findByOid(oid)
    })
  },
  destroyed() {
    EventBus.$off(EVENT_BUS_NAMESPACE.SETL_CLASSFICATION_UP)
  }
};
</script>

<style lang="less" scoped>
.info-wrapper {
  padding: 0 15px;
}

.group-name {
  padding: 15px 0;

  span {
    border-left: 2px solid #1890ff;
    padding-left: 5px;
  }
}

.content-item {
  line-height: 35px;
  display: flex;

  .label {
    padding-right: 10px;
    white-space: nowrap;
    &::after {
      content: ':';
    }
  }

  .value {
    flex: 1 1 auto;
    overflow: hidden;
    word-wrap:break-word;
  }
}</style>