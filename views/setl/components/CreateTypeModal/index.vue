<template>
  <div class="main">
    <a-modal v-model:visible="visibleProxy" :title="code === 'add' ? $t('txt_create_classification') : $t('txt_edit_classification')" :okText="$t('btn_submit')" :cancelText="$t('btn_cancel')"
      @ok="onCommitType">
      <a-form-model ref="formRef" :model="createTypeFormData" :rules="createTypeRules">
        <a-form-model-item has-feedback :label="$t('txt_name')" prop="name">
          <a-input v-model.trim="createTypeFormData.name" />
        </a-form-model-item>
        <a-row>
          <a-col :span="12">
            <div class="group-name"><span>{{ $t('txt_source_system_configuration') }}</span></div>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item :label="$t('txt_system')" prop="sourceSystemName">
              <a-select v-model.trim="createTypeFormData.sourceSystemName"
                @change="onSysValueChange('sourceSystemName', $event)" :options="optionsMap['sourceSystemName']">
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item :label="$t('txt_system_version')" prop="sourceSystemVersion">
              <a-select v-model.trim="createTypeFormData.sourceSystemVersion"
                @change="onSysValueChange('sourceSystemVersion', $event)"
                :options="optionsMap['sourceSystemVersion']"></a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item :label="$t('txt_data_base')" prop="sourceSystemDbType">
              <a-select v-model.trim="createTypeFormData.sourceSystemDbType"
                @change="onSysValueChange('sourceSystemDbType', $event)"
                :options="optionsMap['sourceSystemDbType']"></a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item :label="$t('txt_data_base_version')" prop="sourceSystemDbVersion">
              <a-select v-model.trim="createTypeFormData.sourceSystemDbVersion"
                @change="onSysValueChange('sourceSystemDbVersion', $event)"
                :options="optionsMap['sourceSystemDbVersion']"></a-select>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12">
            <div class="group-name"><span>{{ $t('txt_target_system_configuration') }}</span></div>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item :label="$t('txt_system')" prop="targetSystemName">
              <a-select v-model.trim="createTypeFormData.targetSystemName"
                @change="onSysValueChange('targetSystemName', $event)" :options="optionsMap['targetSystemName']">
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item :label="$t('txt_system_version')" prop="targetSystemVersion">
              <a-select v-model.trim="createTypeFormData.targetSystemVersion"
                @change="onSysValueChange('targetSystemVersion', $event)"
                :options="optionsMap['targetSystemVersion']"></a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item :label="$t('txt_data_base')" prop="targetSystemDbType">
              <a-select v-model.trim="createTypeFormData.targetSystemDbType"
                @change="onSysValueChange('targetSystemDbType', $event)"
                :options="optionsMap['targetSystemDbType']"></a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item :label="$t('txt_data_base_version')" prop="targetSystemDbVersion">
              <a-select v-model.trim="createTypeFormData.targetSystemDbVersion"
                @change="onSysValueChange('targetSystemDbVersion', $event)"
                :options="optionsMap['targetSystemDbVersion']"></a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item has-feedback :label="$t('txt_description')" prop="description">
          <a-textarea v-model.trim="createTypeFormData.description" :rows="4"/>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script>

import {
  findSupportedSourceSys,
  findSupportedSourceSysVersion,
  findSupportedTargetSysName,
  findSupportedTargetSysVersion,
  findSupportedDatabaseName,
  findSupportedDatabaseVersion,


  classificationCreate,
  classificationUpdate,
  classificationFindByOid
} from "apis/setl"



export default {

  components: {
  },
  model: {
    prop: 'visible',
    event: 'update:visible'
  },
  inject: ['getMgTaskClsOid'],
  props: {
    visible: {
      type: Boolean,
      default: false
    },

    code: {
      type: String,
      default: ''
    },

    onChange: {
      type: Function,
      default: () => { }
    }

  },
  data() {
    return {
      createTypeRules: {
        name: [
          { required: true, message: this.$t('msg_required'), },
          { max: 50, message: this.$t('txt_max_length_50'), },
        ],
        sourceSystemName: [
          { required: true, message: this.$t('msg_required'), },
        ],
        sourceSystemVersion: [
          { required: true, message: this.$t('msg_required'), },
        ],
        sourceSystemDbType: [
          { required: true, message: this.$t('msg_required'), },
        ],
        sourceSystemDbVersion: [
          { required: true, message: this.$t('msg_required'), },
        ],
        targetSystemName: [
          { required: true, message: this.$t('msg_required'), },
        ],
        targetSystemVersion: [
          { required: true, message: this.$t('msg_required'), },
        ],
        targetSystemDbType: [
          { required: true, message: this.$t('msg_required'), },
        ],
        targetSystemDbVersion: [
          { required: true, message: this.$t('msg_required'), },
        ],
      },
      createTypeFormData: {
        name: '',
        description: '',
        sourceSystemName: "",
        sourceSystemVersion: "",
        sourceSystemDbType: "",
        sourceSystemDbVersion: "",

        targetSystemName: "",
        targetSystemVersion: "",
        targetSystemDbType: "",
        targetSystemDbVersion: "",
      },
      optionsMap: {
        sourceSystemName: [],
        sourceSystemVersion: [],
        sourceSystemDbType: [],
        sourceSystemDbVersion: [],
        targetSystemName: [],
        targetSystemVersion: [],
        targetSystemDbType: [],
        targetSystemDbVersion: [],
      }
    };
  },
  computed: {
    visibleProxy: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
  },
  watch: {
    visible: function (value) {
      if (value) {
        if (this.code === 'edit') {
          this.findByOid()
            .then(() => {
              this.setAllOptions()
            })
        } else {
          this.createTypeFormData = {}
        }
      }

    }
  },
  methods: {
    findByOid() {
      return classificationFindByOid({ oid: this.getMgTaskClsOid() })
        .then((data = {}) => {
          this.createTypeFormData = data
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"))
        })
    },
    onCommitType() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          let api = this.code === 'edit' ? classificationUpdate : classificationCreate
          api({ ...this.createTypeFormData })
            .then((data) => {
              this.onChange(data.oid)
              this.$success(this.$t("msg_success"))
              this.visibleProxy = false
            })
            .catch((err) => {
              debugger
              this.$error(err.msg || this.$t("msg_failed"))
            })
        } else {
          return false;
        }
      });
      // this.visibleProxy = false
    },
    onSysValueChange(key, value) {
      this.linkage(key, value)
      // this.$nextTick(() => {
      //   this.linkage(key)
      // })


    },
    setAllOptions() {
      this.linkageConfig.forEach((item) => {
        let params = this.getOptionsParams(item.key)
        return item.getOptionsApi(params)
          .then((data) => {
            this.optionsMap[item.key] = data.map((item) => {
              return {
                value: item,
                label: item,
              }
            })
          })
      })
    },
    getOptionsParams(key) {
      let params = {}
      switch (key) {
        case 'sourceSystemName':
          break;
        case 'sourceSystemVersion':
          params.systemName = this.createTypeFormData.sourceSystemName
          break;
        case 'sourceSystemDbType':
          params.systemName = this.createTypeFormData.sourceSystemName
          params.systemVersion = this.createTypeFormData.sourceSystemVersion
          break;
        case 'sourceSystemDbVersion':
          params.systemName = this.createTypeFormData.sourceSystemName
          params.systemVersion = this.createTypeFormData.sourceSystemVersion
          params.databaseName = this.createTypeFormData.sourceSystemDbType
          break;
        case 'targetSystemName':
          params.sourceSysName = this.createTypeFormData.sourceSystemName
          params.sourceSysVersion = this.createTypeFormData.sourceSystemVersion
          break;
        case 'targetSystemVersion':
          params.sourceSysName = this.createTypeFormData.sourceSystemName
          params.sourceSysVersion = this.createTypeFormData.sourceSystemVersion
          params.targetSysName = this.createTypeFormData.targetSystemName
          break;
        case 'targetSystemDbType':
          params.systemName = this.createTypeFormData.targetSystemName
          params.systemVersion = this.createTypeFormData.targetSystemVersion
          break;
        case 'targetSystemDbVersion':
          params.systemName = this.createTypeFormData.targetSystemName
          params.systemVersion = this.createTypeFormData.targetSystemVersion
          params.databaseName = this.createTypeFormData.targetSystemDbType
          break;

        default:
          break;
      }
      return params
    },
    linkage(fieldName, value) {
      let fetchIndex = 0
      this.linkageConfig.forEach((item, index) => {
        
        if (item.key === fieldName) {
          fetchIndex = index + 1
        }

        if (fetchIndex === 0) {
          return
        }
        if (fetchIndex === this.linkageConfig.length) {
          this.$forceUpdate()
          return
        }
        if (fetchIndex === index) {
          this.createTypeFormData[item.key] = ''
          // this.optionsMap[item.key] = []
          let params = this.getOptionsParams(item.key)
          item.getOptionsApi(params)
            .then((data) => {
              this.optionsMap[item.key] = data.map((item) => {
                return {
                  value: item,
                  label: item,
                }
              })
            })
            .catch((err) => {
              this.$error(err.msg || this.$t("msg_failed"))
            })
        }
        if (fetchIndex < index) {
          this.createTypeFormData[item.key] = ''
          this.optionsMap[item.key] = []
        }
      })


    }
  },
  created() {
    this.linkageConfig = [
      {
        key: 'sourceSystemName',
        getOptionsApi: findSupportedSourceSys,
      },
      {
        key: 'sourceSystemVersion',
        getOptionsApi: findSupportedSourceSysVersion,
      },
      {
        key: 'sourceSystemDbType',
        getOptionsApi: findSupportedDatabaseName,
      },
      {
        key: 'sourceSystemDbVersion',
        getOptionsApi: findSupportedDatabaseVersion,
      },
      {
        key: 'targetSystemName',
        getOptionsApi: findSupportedTargetSysName,
      },
      {
        key: 'targetSystemVersion',
        getOptionsApi: findSupportedTargetSysVersion,
      },
      {
        key: 'targetSystemDbType',
        getOptionsApi: findSupportedDatabaseName,
      },
      {
        key: 'targetSystemDbVersion',
        getOptionsApi: findSupportedDatabaseVersion,
      },
    ]

    if (this.code === 'edit') {
      this.findByOid(this.oid)
        .then(() => {
          this.setAllOptions()
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"))
        })
    }


    findSupportedSourceSys({})
      .then((data) => {
        this.optionsMap['sourceSystemName'] = data.map((item) => {
          return {
            value: item,
            label: item,
          }
        })
      })
      .catch((err) => {
        this.$error(err.msg || this.$t("msg_failed"))
      })
  },
};
</script>

<style lang="less" scoped></style>