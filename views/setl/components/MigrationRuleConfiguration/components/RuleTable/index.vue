<template>
  <div>
    <div class="table-wrapper">
      <vxe-table ref="xTable1" :data="tableData" @checkbox-all="selectAllEvent" @checkbox-change="selectChangeEvent">
        <vxe-column v-if="code === 'select'" type="checkbox" width="40"></vxe-column>
        <vxe-column field="dataType" :title="$t('txt_data_type')">
        </vxe-column>
        <vxe-column field="schemaXML" :title="$t('schemaXML')">
          <template #default="{ row }"><a-tooltip><a @click="onDownload(row.schemaXML)">{{ row.schemaXML.name
          }}</a></a-tooltip>
          </template>
        </vxe-column>
        <vxe-column field="transformXML" :title="$t('transformXML')">
          <template #default="{ row }">
            <a-tooltip><a @click="onDownload(row.transformXML)">{{ row.transformXML.name }}</a></a-tooltip>
          </template></vxe-column>
        <vxe-column field="variableFile" :title="$t('variableFile')">
          <template #default="{ row }">
            <a-tooltip><a @click="onDownload(row.variableFile)">{{ row.variableFile.name }}</a></a-tooltip>
          </template></vxe-column>
        <vxe-column field="" :title="$t('txt_operation')">
          <template #default="{ row }">
            <a-tooltip>
              <template #title>{{ $t('btn_edit') }}</template>
              <span @click="onOperation('edit', row)"><jw-icon type="jwi-iconedit"></jw-icon></span>

            </a-tooltip>
            <a-popconfirm v-if="code === 'list'" :title="$t('txt_confirm_delete')" :ok-text="$t('btn_ok')"
              :cancel-text="$t('btn_cancel')" @confirm="onOperation('delete', row)">
              <a-tooltip>
                <template #title>{{ $t('btn_delete') }}</template>
                <span><jw-icon type="jwi-icondelete"></jw-icon></span>
              </a-tooltip>
            </a-popconfirm>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <rule-create-modal v-if="ruleCreateModalVisible" v-model:visible="ruleCreateModalVisible" code="edit"
      :record="current"></rule-create-modal>
  </div>
</template>
<script>
import util from 'jw_common/util';
import { EventBus, EVENT_BUS_NAMESPACE } from 'common/event-bus'
import RuleCreateModal from '../../components/RuleCreateModal'
import {
  migrationScriptCreate,
  migrationScriptUpdate,
  getMigrationScriptInfo,
  migrationScriptDelete,
  getMigrationScriptList,
  findDataMgRuleConf,
} from "apis/setl"

export default {
  components: {
    RuleCreateModal,
  },
  inject: ['getMgTaskClsOid'],
  props: {
    code: {
      type: String,
      default: 'list',  //list; select;
    },
    selectRowList: {
      type: Array,
      default: () => [],
    },
    strategyInfoTableList: {
      type: Array,
      default: () => [],
    },
    
    jobOid: {
      type: String,
      default: '',
    },
    onSelect: {
      type: Function,
      default: () => { },
    }
  },
  data() {
    return {
      tableData: [],
      ruleCreateModalVisible: false,
      current: {},
    }
  },
  methods: {
    onOperation(code, row) {
      switch (code) {
        case 'edit':
          this.onEdit(row)
          break;
        case 'delete':
          migrationScriptDelete({ oid: row.oid })
            .then((data) => {
              this.fetchTableData()
            })
            .catch((err) => {
              this.$error(err.msg || this.$t("msg_failed"))
            })
          break;

        default:
          break;
      }
    },
    onEdit(row) {
      this.current = row
      this.ruleCreateModalVisible = true
    },
    selectAllEvent({ checked }) {
      const records = this.$refs.xTable1.getCheckboxRecords()
      this.onSelect(records)
    },
    selectChangeEvent({ checked }) {
      const records = this.$refs.xTable1.getCheckboxRecords()
      this.onSelect(records)
    },
    onDownload(data) {
      util.download(
        `${Jw.gateway}/${Jw.fileMicroServer}/file/downloadByOid?fileOid=${data.oid}`
      );

      return false
    },
    fetchSelectTableData() {
      let params = {
        oid: this.jobOid
      }
      return findDataMgRuleConf(params)
        .then((data = []) => {
          this.tableData = data


          // this.$nextTick(() => {
          //   let rows = []
          //   data.forEach(element => {
          //     if (element.addFlag) {
          //       let row = this.$refs.xTable1.getRowById(element.oid)
          //       rows.push(row)
          //     }
          //   })
          //   this.$refs.xTable1.setCheckboxRow(rows, true)
          //   this.onSelect(rows)
          // })
          setTimeout(() => {
            this.doRowSelect()
          })

          return data;
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"))
        })
    },
    fetchTableData() {
      let params = {
        mgTaskClsOid: this.getMgTaskClsOid()
      }
      return getMigrationScriptList(params)
        .then((data) => {
          this.tableData = data
          // selectRowList
          setTimeout(() => {
            this.doRowSelect()
          })
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"))
        })
    },
    doRowSelect() {

     
      let rows = this.selectRowList.map((item) => {
        return this.$refs.xTable1.getRowById(item.oid)
      })
      this.$refs.xTable1.clearCheckboxRow()
      this.$refs.xTable1.setCheckboxRow(rows, true)
    }
  },
  created() {

    if (this.code === 'select' && this.jobOid) {
      // this.fetchSelectTableData()
      this.tableData = this.strategyInfoTableList
      setTimeout(() => {
            this.doRowSelect()
          })
    } else {
      this.fetchTableData()
    }

    // this.fetchTableData()
  },
  mounted() {
    EventBus.$on(EVENT_BUS_NAMESPACE.SETL_MIGRATION_RULE_CONFIGURATION_UP, (params, { code, callback }) => {
      if (code === 'add') {
        migrationScriptCreate(params)
          .then((data) => {
            this.$success(this.$t("msg_success"))
            callback()
            return this.fetchTableData()
          })
          .catch((err) => {
            this.$error(err.msg || this.$t("msg_failed"))
          })
      } else { // edit
        if (this.code === 'list') {
          migrationScriptUpdate(params)
            .then((data) => {
              this.$success(this.$t("msg_success"))
              callback()
              return this.fetchTableData()
            })
            .catch((err) => {
              this.$error(err.msg || this.$t("msg_failed"))
            })
        } else { //this.code ===  select
          // 直接修改TABLE数据
          callback()
          let records = this.$refs.xTable1.getCheckboxRecords()

          let index = this.tableData.findIndex((item) => {
            return item.oid === params.oid
          })

          this.tableData.splice(index, 1, params)
          this.$nextTick(() => {
            records = records.map((item) => {
              return this.$refs.xTable1.getRowById(item.oid)
            })
            this.$refs.xTable1.clearCheckboxRow()
            this.$refs.xTable1.setCheckboxRow(records, true)

            this.onSelect(records)
          })
        }
      }
    })
  },
  destroyed() {
    EventBus.$off(EVENT_BUS_NAMESPACE.SETL_MIGRATION_RULE_CONFIGURATION_UP)
  }
};
</script>

<style lang="less" scoped>
.table-wrapper {
  background-color: #fff;
}
</style>