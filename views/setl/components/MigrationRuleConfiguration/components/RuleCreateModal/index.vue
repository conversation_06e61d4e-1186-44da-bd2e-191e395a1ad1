<template>
  <div class="form-wrapper">
    <a-modal v-model:visible="visibleProxy" :title="code === 'add' ? $t('txt_create_rules') : $t('txt_edit_rules')"
      @ok="handleOk" :okText="$t('btn_submit')" :cancelText="$t('btn_cancel')">

      <a-form-model ref="formRef" layout="vertical" :rules="createRules" :model="formState">
        <a-form-model-item :label="$t('txt_data_type')" prop="dataType">
          <a-input v-model:value="formState.dataType" />
        </a-form-model-item>
        <a-form-model-item :label="$t('schemaXML')" prop="schemaXML">
          <jwUploadFile v-model.trim="formState.schemaXML" :maxSize="1024" :accept="'.xml'" />
        </a-form-model-item>
        <a-form-model-item :label="$t('transformXML')" prop="transformXML">
          <jwUploadFile v-model.trim="formState.transformXML" :maxSize="1024" :accept="'.xml'" />
        </a-form-model-item>
        <a-form-model-item :label="$t('variableFile')" prop="variableFile">
          <jwUploadFile v-model.trim="formState.variableFile" :maxSize="1024" :accept="'.json'" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script>
import { EventBus, EVENT_BUS_NAMESPACE } from 'common/event-bus'
import { getCookie } from "jw_utils/cookie";
// import { jwUploadFile } from 'jw_frame'
import jwUploadFile from 'components/uploadFile'
import {
  migrationScriptCreate,
  migrationScriptUpdate,
  getMigrationScriptInfo,
  migrationScriptDelete,
  getMigrationScriptList,
} from "apis/setl"

export default {
  components: {
    jwUploadFile,
  },
  model: {
    prop: 'visible',
    event: 'update:visible'
  },
  inject: ['getMgTaskClsOid'],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    code: {
      type: String,
      default: 'add' // edit
    },
    oid: {
      type: String,
      default: '',
    },
    record: {
      type: Object,
      default: null,
    },


  },
  data() {
    return {
      createRules: {
        dataType: [
          { required: true, message: this.$t('msg_required'), },
        ],
        schemaXML: [
          { required: true, message: this.$t('msg_required'), },
          { validator: this.validateFile },
        ],
        transformXML: [
          { required: true, message: this.$t('msg_required'), },
          { validator: this.validateFile },
        ],
        variableFile: [
          { required: true, message: this.$t('msg_required'), },
          { validator: this.validateFile },
        ],
      },
      files: [],
      isShouldRender: false,
      formState: {
        dataType: '',
        schemaXML: {},
        transformXML: {},
        variableFile: {},
      }
    };
  },
  computed: {
    visibleProxy: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
  },
  watch: {
    formState: {
      handler(val, oldVal) {
        this.$refs.formRef?.validate((err, values, info) => { })
      },
      deep: true
    },
  },
  methods: {
    validateFile(rule, value, callback) {
      if (value?.name) {
        callback()
      } else {
        callback(this.$t('txt_please_upload_the_file'))
      }
    },

    adjustData(data) {
      return {
        uid: data.oid,
        oid: data.oid,
        name: data.name,
        status: 'done',
        url: '',
      }
    },
    handleOk(e) {
      e.preventDefault();
      this.$refs.formRef.validate((err, values, info) => {
        if (!err) {
          return
        }

        let parmas = { mgTaskClsOid: this.getMgTaskClsOid(), dataType: 'Container', ...this.formState }


        EventBus.$emit(EVENT_BUS_NAMESPACE.SETL_MIGRATION_RULE_CONFIGURATION_UP,
          { ...parmas },
          {
            code: this.code,
            callback: () => {
              this.visibleProxy = false
            }
          }
        )


      });
    },

  },
  created() {
    if (this.code === 'edit') {
      this.formState = {
        oid: this.record.oid,
        dataType: this.record.dataType,
        schemaXML: this.adjustData(this.record.schemaXML),
        transformXML: this.adjustData(this.record.transformXML),
        variableFile: this.adjustData(this.record.variableFile),
      }
    }
  },
};
</script>

<style lang="less" scoped>
.form-wrapper {
  padding: 0 15px;

  .group-name {
    padding: 15px 0;
  }
}
</style>