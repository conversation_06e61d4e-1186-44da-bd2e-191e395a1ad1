<template>
  <div class="rule-wrapper">
    <div class="add-wrapper">
      <a-button class="btn" type="primary" @click="onCreateRule">
        <jw-icon type="jwi-iconadd"></jw-icon>
        {{ $t('txt_create_rules') }}
      </a-button>
    </div>
    <div class="table-container"><rule-table></rule-table></div>
    <rule-create-modal v-if="ruleCreateModalVisible" v-model:visible="ruleCreateModalVisible" code="add"
      ></rule-create-modal>
  </div>
</template>
<script>
import RuleCreateModal from './components/RuleCreateModal'
import RuleTable from './components/RuleTable'

import {
  migrationScriptCreate,
  getMigrationScriptInfo,
  migrationScriptDelete,
  getMigrationScriptList,
  jobFindList,
} from "apis/setl"


export default {
  components: {
    RuleCreateModal,
    RuleTable,
  },
  inject: ['getMgTaskClsOid'],
  data() {
    return {
      ruleCreateModalVisible: false,
    };
  },
  methods: {
    onCreateRule() {
      this.ruleCreateModalCode = 'add'
      this.ruleCreateModalVisible = true
    },

  },
  created() {

  },
};
</script>

<style lang="less" scoped>
.rule-wrapper {
  padding: 0 15px;

  .add-wrapper {
    overflow: hidden;
    padding-bottom: 15px;

    .btn {
      float: right;
    }
  }

  .table-container {
    position: relative;
    width: 100%;
    height: 500px;
  }
}</style>