<template>
  <div class="form-wrapper">
    <a-form-model ref="formRef" layout="horizontal" :model="createTypeFormData" :rules="createTypeRules">
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_name')" prop="name">
            <a-input v-model:value="createTypeFormData.name" />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_description')" prop="description">
            <a-input v-model:value="createTypeFormData.description" />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <div class="group-name">{{ $t('txt_source_system_configuration') }}</div>
        </a-col>
        <a-col :span="4" :offset="8"><a-button type="link" @click="checkDbLink('source', $event)">{{ $t('txt_test_link') }}</a-button></a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_system')" prop="sourceSystem.sysName">
            <a-input v-model:value="createTypeFormData.sourceSystem.sysName" disabled />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_system_version')" prop="sourceSystem.sysVersion">
            <a-input v-model:value="createTypeFormData.sourceSystem.sysVersion" disabled />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_data_base')" prop="sourceSystem.dbType">
            <a-input v-model:value="createTypeFormData.sourceSystem.dbType" disabled />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_data_base_version')" prop="sourceSystem.dbVersion">
            <a-input v-model:value="createTypeFormData.sourceSystem.dbVersion" disabled />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_data_base_url')" prop="sourceSystem.dbURL">
            <a-input v-model:value="createTypeFormData.sourceSystem.dbURL" />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_data_base_schema')" prop="sourceSystem.dbSchema">
            <a-input v-model:value="createTypeFormData.sourceSystem.dbSchema" />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_account')" prop="sourceSystem.dbAccount">
            <a-input v-model:value="createTypeFormData.sourceSystem.dbAccount" />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_account_login')" prop="sourceSystem.dbPassWord">
            <a-input v-model:value="createTypeFormData.sourceSystem.dbPassWord" />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <div class="group-name">{{ $t('txt_target_system_configuration') }}</div>
        </a-col>
        <a-col :span="4" :offset="8">
          <a-button type="link" @click="checkDbLink('target', $event)">{{ $t('txt_test_link') }}</a-button>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_system')" prop="targetSystem.sysName">
            <a-input v-model:value="createTypeFormData.targetSystem.sysName" disabled />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_system_version')" prop="targetSystem.sysVersion">
            <a-input v-model:value="createTypeFormData.targetSystem.sysVersion" disabled />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_data_base')" prop="targetSystem.dbType">
            <a-input v-model:value="createTypeFormData.targetSystem.dbType" disabled />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_data_base_version')" prop="targetSystem.dbVersion">
            <a-input v-model:value="createTypeFormData.targetSystem.dbVersion" disabled />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_data_base_url')" prop="targetSystem.dbURL">
            <a-input v-model:value="createTypeFormData.targetSystem.dbURL" />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_data_base_schema')" prop="targetSystem.dbSchema">
            <a-input v-model:value="createTypeFormData.targetSystem.dbSchema" />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_account')" prop="targetSystem.dbAccount">
            <a-input v-model:value="createTypeFormData.targetSystem.dbAccount" />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_account_login')" prop="targetSystem.dbPassWord">
            <a-input v-model:value="createTypeFormData.targetSystem.dbPassWord" />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>
<script>
import {
  findSupportedSourceSys,
  findSupportedSourceSysVersion,
  findSupportedTargetSysName,
  findSupportedTargetSysVersion,
  findSupportedDatabaseName,
  findSupportedDatabaseVersion,


  classificationCreate,
  checkDbLink,
  classificationFindByOid
} from "apis/setl"

export default {
  components: {
  },
  inject: ['getMgTaskClsOid'],
  props: {
    dataCollect: {
      type: Object,
      default: {},
    }
  },
  data() {
    return {
      createTypeRules: {
        name: [
          { required: true, message: this.$t('msg_required'), },
        ],
        'sourceSystem.sysName': [
          { required: true, message: this.$t('msg_required'), },
        ],
        'sourceSystem.sysVersion': [
          { required: true, message: this.$t('msg_required'), },
        ],
        'sourceSystem.dbType': [
          { required: true, message: this.$t('msg_required'), },
        ],
        "sourceSystem.dbVersion": [
          { required: true, message: this.$t('msg_required'), },
        ],

        "sourceSystem.dbURL": [
          { required: true, message: this.$t('msg_required'), },
        ],
        "sourceSystem.dbSchema": [
          { required: true, message: this.$t('msg_required'), },
        ],
        "sourceSystem.dbAccount": [
          { required: true, message: this.$t('msg_required'), },
        ],
        "sourceSystem.dbPassWord": [
          { required: true, message: this.$t('msg_required'), },
        ],


        "targetSystem.sysName": [
          { required: true, message: this.$t('msg_required'), },
        ],
        "targetSystem.sysVersion": [
          { required: true, message: this.$t('msg_required'), },
        ],
        "targetSystem.dbType": [
          { required: true, message: this.$t('msg_required'), },
        ],
        "targetSystem.dbVersion": [
          { required: true, message: this.$t('msg_required'), },
        ],

        "targetSystem.dbURL": [
          { required: true, message: this.$t('msg_required'), },
        ],
        "targetSystem.dbSchema": [
          { required: true, message: this.$t('msg_required'), },
        ],
        "targetSystem.dbAccount": [
          { required: true, message: this.$t('msg_required'), },
        ],
        "targetSystem.dbPassWord": [
          { required: true, message: this.$t('msg_required'), },
        ],
      },
      createTypeFormData: {...this.dataCollect.baseInfo},
      // createTypeFormData: {
      //   name: '',
      //   description: '',
      //   sourceSystem: {
      //     sysName: '',
      //     version: '',
      //     dbType: '',
      //     dbVersion: '',

      //     dbURL: 'jdbc:oracle:thin:@*************:1521:wind',
      //     dbSchema: 'wind',
      //     dbAccount: 'plm102',
      //     dbPassWord: 'plm102',
      //   },

      //   targetSystem: {
      //     sysName: '',
      //     sysVersion: '',
      //     dbType: '',
      //     dbVersion: '',

      //     dbURL: 'bolt://neo4j-ep.jwis.cn:7687',
      //     dbSchema: 'asda',
      //     dbAccount: 'plm',
      //     dbPassWord: 'plm123',
      //   },
      // },
    };
  },
  methods: {
    checkDbLink(code) {
      let parmas = {}
      if (code === 'source') {
        parmas = this.createTypeFormData.sourceSystem
      } else {
        parmas = this.createTypeFormData.targetSystem
      }

      checkDbLink(parmas)
        .then((data) => {
          this.$success(this.$t("txt_link_succeeded"))
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("txt_link_failure"))
        })
    },
    validate() {
      return this.$refs.formRef.validate()
        .then((valid, info) => {
          return { valid, info, data: this.createTypeFormData }
        })
    },
  },
  created() {
    
  },
};
</script>

<style lang="less" scoped>
/deep/ .ant-form-item{
  margin-bottom: 5px;
}
.form-wrapper {
  padding-top: 15px;

  .group-name {
    margin: 6px 0;
    padding-left: 5px;
    border-left: 3px solid #1890ff;
   
  }
}
</style>