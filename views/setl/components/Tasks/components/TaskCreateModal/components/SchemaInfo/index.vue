<template>
  <div class="form-wrapper">
    <a-form-model ref="formRef" layout="vertical" :model="createTypeFormData" :rules="createTypeRules">

      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_migration_plan')" prop="mgStrategyValue">
            <a-radio-group v-model:value="createTypeFormData.mgStrategyValue">
              <a-radio value="Tenant">
                {{ $t('txe_tenant') }}
              </a-radio>
              <a-radio value="Container">
                {{ $t('Container') }}
              </a-radio>
            </a-radio-group>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_plan_version')" prop="mgStrategyVersion">
            <a-select v-model.trim="createTypeFormData.mgStrategyVersion">
              <a-select-option value="latestBranchVersion">
                {{ $t('txt_latest_branch_version') }}
              </a-select-option>
              <a-select-option value="allVersion">
                {{ $t('txt_all_versions') }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_data_source_organization')" prop="sourceTenantOid">
            <a-select v-model:value="createTypeFormData.sourceTenantOid" :options="optionsMap['sourceTenant']" show-search
              :filterOption="filterOption" />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_target_library_organization')" prop="targetTenantOid">
            <a-select v-model:value="createTypeFormData.targetTenantOid" :options="optionsMap['targetTenant']" show-search
              :filterOption="filterOption" />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24" v-if="createTypeFormData.mgStrategyValue === 'Container'">
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_data_source_container')" prop="sourceContainerOid">
            <a-select v-model:value="createTypeFormData.sourceContainerOid" :options="optionsMap['sourceContainer']"
              show-search :filterOption="filterOption" />
          </a-form-model-item>
        </a-col>
        <!-- <a-col :span="12">
          <a-form-model-item :label="$t('txt_target_library_container')" prop="targetContainerOid">
            <a-select v-model:value="createTypeFormData.targetContainerOid" :options="optionsMap['targetContainer']" />
          </a-form-model-item>
        </a-col> -->
      </a-row>
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-model-item :label="$t('txt_migratable_data_objects')" prop="dataMgRuleConfs">
            <div class="table-container"><rule-table code="select" :selectRowList="createTypeFormData.dataMgRuleConfs" 
              :strategyInfoTableList="dataCollect.strategyInfoTableList"
                :onSelect="onTableSelect" :jobOid="oid"></rule-table>
            </div>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <!--     
    <div class="migration-obj">{{ $t('可迁移的数据对象') }}</div>
    <div class="table-container"><rule-table></rule-table></div> -->
  </div>
</template>
<script>
import RuleTable from 'views/setl/components/MigrationRuleConfiguration/components/RuleTable'
import {
  findSupportedSourceSys,
  findSupportedSourceSysVersion,
  findSupportedTargetSysName,
  findSupportedTargetSysVersion,
  findSupportedDatabaseName,
  findSupportedDatabaseVersion,


  classificationCreate,

  classificationFindByOid,
  findSupportedTenant,
  findSupportedContainer,
} from "apis/setl"

export default {
  components: {
    RuleTable,
  },
  inject: ['getMgTaskClsOid'],
  props: {
    dataCollect: {
      type: Object,
      default: {},
    },
    code: {
      type: String,
      default: 'add',
    },
    oid: {
      type: String,
      default: '',
    }
  },
  data() {
    return {
      createTypeRules: {
        mgStrategyValue: [
          { required: true, message: this.$t('msg_required'), },
        ],
        mgStrategyVersion: [
          { required: true, message: this.$t('msg_required'), },
        ],
        sourceTenantOid: [
          { required: true, message: this.$t('msg_required'), },
        ],
        targetTenantOid: [
          { required: true, message: this.$t('msg_required'), },
        ],
        sourceContainerOid: [
          { required: true, message: this.$t('msg_required'), },
        ],
        dataMgRuleConfs: [
          { required: true, message: this.$t('msg_required'), },
          {
            validator: (rule, value = [], callback) => {
              if (value.length === 0) {
                callback(new Error(this.$t("txt_enter_seleted_one")));
              } else {
                callback()
              }
            }, trigger: 'change'
          }
        ],
      },
      createTypeFormData: { 
        sourceContainerOid: '',
        mgStrategyValue: 'Tenant',
        mgStrategyVersion: "latestBranchVersion",
        ...this.dataCollect.strategyInfo 
      },
      // createTypeFormData: {
      //   mgStrategyValue: 'Tenant',
      //   mgStrategyVersion: "latestBranchVersion",
      //   sourceTenantOid: "",
      //   sourceContainerOid: "",
      //   targetTenantOid: "",
      //   targetContainerOid: "",
      //   dataMgRuleConfs: [],
      //   // [
      //   //   {
      //   //     "dataType": "Container",
      //   //     "schemaXML": { "name": "ContainerSchema.xml", "oid": "f433fbe9-bbab-4f77-a7d5-52f58a185b97" },
      //   //     "transformXML": { "name": "ContainerTransform.xml", "oid": "35651496-5a62-428f-8682-56e05d7d34ee" },
      //   //     "variableFile": { "name": "variableJSONFile.json", "oid": "2cbcb6fe-b242-4b91-b74e-1e422ae27d92" }
      //   //   }
      //   // ]
      // },
      optionsMap: {
        sourceTenant: [],
        sourceContainer: [],
        targetTenant: [],
        targetContainer: [],
      }
    };
  },
  watch: {
    "createTypeFormData.sourceTenantOid": function (value) {
      this.createTypeFormData.sourceContainerOid = ''
      this.fetchContainerOptions(this.dataCollect.baseInfo.sourceSystem, value, 'sourceContainer')
    },
    "createTypeFormData.targetTenantOid": function (value) {
      this.createTypeFormData.targetContainerOid = ''
      this.fetchContainerOptions(this.dataCollect.baseInfo.targetSystem, value, 'targetContainer')
    },
    "createTypeFormData.mgStrategyValue": function (value) {
      if (value === 'Container') {
        this.createTypeFormData.sourceContainerOid = ''
        if (this.createTypeFormData.sourceTenantOid) {
          this.fetchContainerOptions(this.dataCollect.baseInfo.sourceSystem, this.createTypeFormData.sourceTenantOid, 'sourceContainer')
        }

      } else {
        this.createTypeFormData.sourceContainerOid = ''
      }

    },
    formState: {
      createTypeFormData(val, oldVal) {
      },
      deep: true
    },
  },
  compute: {
  },
  methods: {
    validate() {
      return this.$refs.formRef.validate()
        .then((valid, info) => {

          return { valid, info, data: this.createTypeFormData }
        })
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    fetchTenantOptions(originData, key) {
      let params = { ...originData }
      // delete params.name;
      // delete params.version;
      findSupportedTenant(params)
        .then((data) => {
          this.optionsMap[key] = data.map((item) => {
            return {
              value: item.oid,
              label: `${item.name}`,
            }
          })
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"))
        })
    },
    fetchContainerOptions(originData, tenantOid, key) {
      let params = { ...originData, tenantOid }
      // delete params.name;
      // delete params.version;
      findSupportedContainer(params)
        .then((data) => {
          this.optionsMap[key] = data.map((item) => {
            return {
              value: item.oid,
              label: `${item.name}`,
            }
          })
          this.$forceUpdate()
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"))
        })
    },
    onTableSelect(records) {
      this.createTypeFormData.dataMgRuleConfs = records
    }
  },
  created() {
    this.fetchTenantOptions(this.dataCollect.baseInfo.sourceSystem, 'sourceTenant')
    this.fetchTenantOptions(this.dataCollect.baseInfo.targetSystem, 'targetTenant')
    if (this.createTypeFormData.mgStrategyValue === 'Container') {
      this.fetchContainerOptions(this.dataCollect.baseInfo.sourceSystem, this.createTypeFormData.sourceTenantOid, 'sourceContainer')
    }
  },
};
</script>

<style lang="less" scoped>
.form-wrapper {
  padding: 15px 15px;

  .group-name {
    padding: 15px 0;
  }
}

.migration-obj {
  padding: 15px 0;
}

.table-container {
  position: relative;
  width: 100%;
  min-height: 300px;
}
</style>