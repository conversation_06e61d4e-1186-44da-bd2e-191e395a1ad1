<template>
  <div class="form-wrapper">
    <a-form-model ref="formRef" layout="vertical" :model="createTypeFormData" :rules="createTypeRules">
     
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item prop="minioURL">
            <span slot="label">
              <span> {{ $t('txt_minio_url') }}</span>
            </span>
            <a-input v-model:value="createTypeFormData.minioURL"  />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item  prop="bucketName">
            <span slot="label">
              <span> {{ $t('txt_bucket_name') }}</span>
            </span>
            <a-input v-model:value="createTypeFormData.bucketName " />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>
<script>
import jwIcon from "@jw/jw-frame/packages/components/icon";
import {
  findSupportedSourceSys,
  findSupportedSourceSysVersion,
  findSupportedTargetSysName,
  findSupportedTargetSysVersion,
  findSupportedDatabaseName,
  findSupportedDatabaseVersion,


  classificationCreate,

  classificationFindByOid
} from "apis/setl"

export default {
  components: {
    jwIcon
  },
  inject: ['getMgTaskClsOid'],
  props: {
    dataCollect: {
      type: Object,
      default: {},
    }
  },
  data() {
    return {
      createTypeRules: {
        minioURL: [
          { required: true, message: this.$t('msg_required'), },
        ],
        bucketName: [
          { required: true, message: this.$t('msg_required'), },
        ],
      },
      createTypeFormData: {
        ...this.dataCollect.fileInfo
      },
      // createTypeFormData: {
      //   readerBatchSize: 100,
      //   writerBatchSize: 100,
      //   speedChannel: 0,
      //   speedByte: 0,
      //   errorLimitRecord: 0,
      //   errorLimitPercentage: 0,
      // },
    };
  },
  methods: {
    validate() {
      return this.$refs.formRef.validate()
        .then((valid, info) => {
          return {valid, info, data: this.createTypeFormData}
        })
    },
  },
  created() {
  },
};
</script>

<style lang="less" scoped>
.form-wrapper {
  padding: 15px 15px;

  .group-name {
    padding: 15px 0;
  }
}
</style>