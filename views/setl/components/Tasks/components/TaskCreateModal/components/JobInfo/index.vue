<template>
  <div class="form-wrapper">
    <a-form-model ref="formRef" layout="vertical" :model="createTypeFormData" :rules="createTypeRules">
      <a-row>
        <a-col :span="12">
          <div class="group-name">{{ $t('txt_source_system_configuration') }}</div>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item prop="readerBatchSize">
            <span slot="label">
              <span> {{ $t('txt_number_of_rows_read') }}</span>
              <a-tooltip :title="$t('txt_maximum_number_of_readers')">
                <jw-icon type="jwi-iconhelp-circle-full"></jw-icon>
              </a-tooltip>
            </span>
            <a-input-number :min="1" v-model:value="createTypeFormData.readerBatchSize"  />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item  prop="writerBatchSize">
            <span slot="label">
              <span> {{ $t('txt_number_of_rows_written') }}</span>
              <a-tooltip :title="$t('txt_maximum_number_of_writers')">
                <jw-icon type="jwi-iconhelp-circle-full"></jw-icon>
              </a-tooltip>
            </span>
            <a-input-number :min="1" v-model:value="createTypeFormData.writerBatchSize" />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item  prop="speedChannel">
            <span slot="label">
              <span> {{ $t('txt_number_of_pipes') }}</span>
              <a-tooltip :title="$t('txt_number_of_tasks_executed')">
                <jw-icon type="jwi-iconhelp-circle-full"></jw-icon>
              </a-tooltip>
            </span>
            <a-input-number :min="0" v-model:value="createTypeFormData.speedChannel" />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item  prop="speedByte">
            <span slot="label">
              <span> {{ $t('txt_transmission_speed') }}</span>
              <a-tooltip :title="$t('txt_transmission_speed_explain')">
                <jw-icon type="jwi-iconhelp-circle-full"></jw-icon>
              </a-tooltip>
            </span>
            <a-input-number  :min="0" v-model:value="createTypeFormData.speedByte"  />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item  prop="errorLimitRecord">
            <span slot="label">
              <span> {{ $t('txt_maximum_number_of_dirty_data') }}</span>
              <a-tooltip :title="$t('txt_maximum_number_of_dirty_data_explain')">
                <jw-icon type="jwi-iconhelp-circle-full"></jw-icon>
              </a-tooltip>
            </span>
            <a-input-number  :min="0" v-model:value="createTypeFormData.errorLimitRecord" />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item  prop="errorLimitPercentage">
            <span slot="label">
              <span> {{ $t('txt_dirty_data_percentage_threshold') }}</span>
              <a-tooltip :title="$t('txt_dirty_data_percentage_threshold_explain')">
                <jw-icon type="jwi-iconhelp-circle-full"></jw-icon>
              </a-tooltip>
            </span>
            <a-input-number  :min="0" v-model:value="createTypeFormData.errorLimitPercentage" />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>
<script>
import jwIcon from "@jw/jw-frame/packages/components/icon";
import {
  findSupportedSourceSys,
  findSupportedSourceSysVersion,
  findSupportedTargetSysName,
  findSupportedTargetSysVersion,
  findSupportedDatabaseName,
  findSupportedDatabaseVersion,


  classificationCreate,

  classificationFindByOid
} from "apis/setl"

export default {
  components: {
    jwIcon
  },
  inject: ['getMgTaskClsOid'],
  props: {
    dataCollect: {
      type: Object,
      default: {},
    }
  },
  data() {
    return {
      createTypeRules: {
        readerBatchSize: [
          { required: true, message: this.$t('msg_required'), },
        ],
        writerBatchSize: [
          { required: true, message: this.$t('msg_required'), },
        ],
        sourceSystemVersion: [
          { message: this.$t('msg_required'), },
        ],
      },
      createTypeFormData: {
        readerBatchSize: 10000,
        writerBatchSize: 10000,
        speedChannel: 1,
        ...this.dataCollect.jobInfo
      },
      // createTypeFormData: {
      //   readerBatchSize: 100,
      //   writerBatchSize: 100,
      //   speedChannel: 0,
      //   speedByte: 0,
      //   errorLimitRecord: 0,
      //   errorLimitPercentage: 0,
      // },
    };
  },
  methods: {
    validate() {
      return this.$refs.formRef.validate()
        .then((valid, info) => {
          return {valid, info, data: this.createTypeFormData}
        })
    },
  },
  created() {
  },
};
</script>

<style lang="less" scoped>
.form-wrapper {
  padding: 15px 15px;

  .group-name {
    padding: 15px 0;
  }
}
</style>