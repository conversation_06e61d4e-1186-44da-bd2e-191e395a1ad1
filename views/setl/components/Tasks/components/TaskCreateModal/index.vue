<template>
  <div class="step-wrapper">
    <a-modal v-model:visible="visibleProxy"
      :title="code === 'add' ? $t('txt_create_migration_task') : $t('txt_edit_migration_task')" width="800px">
      <template slot="footer">
        <a-button v-if="current > 0" key="pre" type="primary" @click="pre">
          {{ $t('btn_pre_step') }}
        </a-button>
        <a-button v-if="current < steps.length - 1" key="next" type="primary" @click="next">
          {{ $t('btn_next_step') }}
        </a-button>
        <a-button v-if="current === steps.length - 1" key="sure" type="primary" @click="done" :loading="loading">
          {{ $t('btn_submit') }}
        </a-button>
        <a-button key="cancel" @click="onCancel">
          {{ $t('btn_cancel') }}
        </a-button>
      </template>

      <a-spin :spinning="!isDataReady">
        <a-steps :current="current">
          <a-step v-for="item in steps" :key="item.title" :title="item.title" />
        </a-steps>
        <component v-if="isDataReady" :is="steps[current].component" ref="step" :dataCollect="dataCollect" :code="code"
          :oid="oid"></component>
      </a-spin>

    </a-modal>
  </div>
</template>
<script>
import { EventBus, EVENT_BUS_NAMESPACE } from 'common/event-bus'

import BaseInfo from './components/BaseInfo'
import FileInfo from './components/FileInfo'
import SchemaInfo from './components/SchemaInfo'
import JobInfo from './components/JobInfo'

import {
  findSupportedSourceSys,
  findSupportedSourceSysVersion,
  findSupportedTargetSysName,
  findSupportedTargetSysVersion,
  findSupportedDatabaseName,
  findSupportedDatabaseVersion,


  classificationCreate,

  classificationFindByOid,
  findSupportedTenant,
  findSupportedContainer,
  checkDbLink,

  jobCreate,
  jobUpdate,
  getJobDetail,
  findDataMgRuleConf,
  jobFindList,
  jobExecute,
  getLogList,
} from "apis/setl"


export default {
  components: {
  },
  model: {
    prop: 'visible',
    event: 'update:visible'
  },
  inject: ['getMgTaskClsOid'],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    code: {
      type: String,
      default: 'add', // edit
    },
    oid: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      current: 0,
      loading: false,
      steps: [
        {
          title: this.$t('change_fill_info'),
          component: BaseInfo,
        },
        {
          title: this.$t('txt_file_migration_configuration'),
          component: FileInfo,
        },
        {
          title: this.$t('txt_migration_scheme_configuration'),
          component: SchemaInfo,
        },
        {
          title: this.$t('txt_task_information_configuration'),
          component: JobInfo,
        },
      ],
      dataCollect: {
        baseInfo: {},
        fileInfo: {},
        strategyInfo: {},
        jobInfo: {},

        strategyInfoTableList: [],


      },
      isDataReady: false,
    };
  },
  computed: {
    visibleProxy: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
  },
  methods: {
    onCancel() {
      this.visibleProxy = false
    },
    pre() {
      this.onStepAdvance()
        .then((valid) => {
          if (valid) {
            this.current = Math.max(--this.current, 0)
          }
        })

    },
    next() {
      this.onStepAdvance()
        .then((valid) => {
          if (valid) {
            this.current = Math.min(++this.current, this.steps.length - 1)
          }
        })
    },
    done() {
      this.loading = true
      this.onStepAdvance()
        .then((valid) => {
          if (valid) {
            return this.doCreate(this.dataCollect)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    onStepAdvance() {
      return this.$refs.step.validate()
        .then(({ valid, info, data }) => {
          if (valid) {
            switch (this.current) {
              case 0:
                this.dataCollect.baseInfo = data
                break;
              case 1:
                this.dataCollect.fileInfo = data
                break;

              case 2:
                this.dataCollect.strategyInfo = data
                break;
              case 3:
                this.dataCollect.jobInfo = data
                break;

              default:
                break;
            }

          }
          return valid
        })
    },
    doCreate(source) {
      let params = this.echoToCommit(source)
      let api = this.code === 'edit' ? jobUpdate : jobCreate
      return api(params)
        .then((data) => {
          this.$success(this.$t("msg_success"))
          this.visibleProxy = false
          EventBus.$emit(EVENT_BUS_NAMESPACE.SETL_FRESH_JOB_TABLE, {})
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"))
        })
    },
    echoToCommit(source) {
      let result = {}
      for (const key in source) {
        if (Object.hasOwnProperty.call(source, key)) {
          const element = source[key];
          switch (key) {
            case 'baseInfo':

              result.oid = element.oid
              result.name = element.name
              result.description = element.description
              result.mgTaskClsOid = this.getMgTaskClsOid()
              result.sourceSystem = element.sourceSystem
              result.targetSystem = element.targetSystem
              break;
            case 'fileInfo':
              result.fileSystemConf = { ...element }
              break;
            case 'strategyInfo':

              let temp = {}
              result.mgStrategyConfs = []
              temp.name = 'CONTAINERSTRATEGY'
              temp.value = element.mgStrategyValue
              temp.strategyInfo = {
                "sourceTenantOid": element.sourceTenantOid,
                "targetTenantOid": element.targetTenantOid,
                "sourceContainerOid": element.sourceContainerOid,
                "targetContainerOid": element.targetContainerOid,
              }
              result.mgStrategyConfs.push(temp)

              temp = {}
              temp.name = 'VERSIONSTRATEGY'
              temp.value = element.mgStrategyVersion
              result.mgStrategyConfs.push(temp)

              result.dataMgRuleConfs = element.dataMgRuleConfs
              break;
            case 'jobInfo':
              result.mgJobConf = {
                ...element
              }
              break;

            default:
              break;
          }

        }
      }

      return result
    },
    commitToEcho(source) {
      let dataCollect = {
        baseInfo: {},
        fileInfo: {},
        strategyInfo: {},
        jobInfo: {},

        strategyInfoTableList: [],
      }

      dataCollect.baseInfo.oid = source.oid
      dataCollect.baseInfo.name = source.name
      dataCollect.baseInfo.description = source.description
      dataCollect.baseInfo.sourceSystem = source.sourceSystem
      dataCollect.baseInfo.targetSystem = source.targetSystem

      dataCollect.fileInfo = source.fileSystemConf || {}

      source.mgStrategyConfs.forEach((item, index) => {
        if (item.name === 'CONTAINERSTRATEGY') {
          dataCollect.strategyInfo.mgStrategyValue = item.value
          dataCollect.strategyInfo.sourceTenantOid = item.strategyInfo.sourceTenantOid
          dataCollect.strategyInfo.sourceContainerOid = item.strategyInfo.sourceContainerOid
          dataCollect.strategyInfo.targetTenantOid = item.strategyInfo.targetTenantOid
          dataCollect.strategyInfo.targetContainerOid = item.strategyInfo.targetContainerOid
        } else if (item.name === 'VERSIONSTRATEGY') {
          dataCollect.strategyInfo.mgStrategyVersion = item.value
        }
      })
      dataCollect.strategyInfo.dataMgRuleConfs = source.dataMgRuleConfs
      dataCollect.strategyInfoTableList = source.strategyInfoTableList


      dataCollect.jobInfo = source.mgJobConf

      return dataCollect
    }
  },
  created() {
    if (this.code === 'edit') {
      getJobDetail({ oid: this.oid })
        .then((data = {}) => {
          return findDataMgRuleConf({ oid: this.oid })
            .then((list = []) => {
              let _list = list.filter((item) => { return item.addFlag })
              data.dataMgRuleConfs = _list
              data.strategyInfoTableList = list
              return data
            })
        })
        .then((data = {}) => {

          this.dataCollect = this.commitToEcho(data)
          this.isDataReady = true

        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"))
        })
    } else { // add
      classificationFindByOid({ oid: this.getMgTaskClsOid() })
        .then((data = {}) => {

          this.dataCollect.baseInfo.sourceSystem = {
            // dbURL: '*****************************************',
            // dbSchema: 'wind',
            // dbAccount: 'plm102',
            // dbPassWord: 'plm102',
          }
          this.dataCollect.baseInfo.sourceSystem.sysName = data.sourceSystemName
          this.dataCollect.baseInfo.sourceSystem.sysVersion = data.sourceSystemVersion
          this.dataCollect.baseInfo.sourceSystem.dbType = data.sourceSystemDbType
          this.dataCollect.baseInfo.sourceSystem.dbVersion = data.sourceSystemDbVersion

          this.dataCollect.baseInfo.targetSystem = {
            // dbURL: 'bolt://neo4j-ep.jwis.cn:7687',
            // dbSchema: 'asda',
            // dbAccount: 'plm',
            // dbPassWord: 'plm123',
          }
          this.dataCollect.baseInfo.targetSystem.sysName = data.targetSystemName
          this.dataCollect.baseInfo.targetSystem.sysVersion = data.targetSystemVersion
          this.dataCollect.baseInfo.targetSystem.dbType = data.targetSystemDbType
          this.dataCollect.baseInfo.targetSystem.dbVersion = data.targetSystemDbVersion

          this.isDataReady = true
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"))
        })
    }
  },
};
</script>

<style lang="less" scoped>
.step-wrapper {
  padding: 0 15px;

  .group-name {
    padding: 15px 0;
  }
}
</style>