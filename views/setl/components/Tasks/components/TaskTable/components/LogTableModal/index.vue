<template>
  <a-modal id="log-modal" v-model:visible="visibleProxy" :title="$t('txt_migration_Log')" :dialog-style="{ top: '10px', }"
    width="1200px" :footer="null">
    <a-row>
      <a-col :span="20">
        <div class="table-container" v-for="item in tableDataInfo.dataList">
          <div :id="item.phase" class="anchor-label">{{ item.phase }}</div>
          <div class="table-wrapper">
            <vxe-table ref="xTable1" :data="item.list" show-overflow :row-config="{ height: '20px', keyField: 'oid', }">
              <vxe-column field="summary" :title="$t('txt_plan_name')" width="200px"></vxe-column>
              <vxe-column field="phase" :title="$t('txt_is_stage')" width="100px"></vxe-column>
              <vxe-column field="state" :title="$t('txt_status')" width="100px"></vxe-column>
              <vxe-column field="updateDate" :title="$t('txt_update_time')" width="150px">
                <template #default="{ row }">{{ formatData(row.updateDate) }}</template>
              </vxe-column>
              <vxe-column field="logContent" :title="$t('txt_describe')" show-overflow="ellipsis">
                <template #default="{ row }">
                  <a-tooltip  :title="row.logContent" placement="top" style="width: 100px; overflow: ellipsis;">
                    {{ row.logContent.slice(0, 50) + '...'  }}
                  </a-tooltip>
                </template>
              </vxe-column>
            </vxe-table>
          </div>
        </div>
      </a-col>
      <a-col :span="4">
        <div class="anchor-container">
          <a-anchor :getContainer="getContainer" @click="anchorJump">
            <a-anchor-link v-for="item in tableDataInfo.anchorList" :href="'#' + item.phase" :title="item.phase" />
          </a-anchor>
        </div>
      </a-col>
    </a-row>

  </a-modal>
</template>
<script>

import { EventBus, EVENT_BUS_NAMESPACE } from 'common/event-bus'
import { formatDate } from "jw_utils/moment-date";
import {
  getLogList,
} from "apis/setl"

export default {
  components: {
  },
  model: {
    prop: 'visible',
    event: 'update:visible'
  },
  inject: ['getMgTaskClsOid'],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    oid: {
      type: String,
      default: '',
    },
  },
  data() {

    return {
      taskCreateModalVisible: false,
      current: {},
      tableDataInfo: {
        dataList: [],
        anchorList: [],
      },
    }
  },
  computed: {
    visibleProxy: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
  },
  methods: {
    getPopupContainer() {
      return document.querySelector('#log-modal')
    },
    formatData(date) {
      return formatDate(date)
    },
    scrollToPhase(item) {
      let rowid = this.tableDataInfo.dataList[item.index].oid
      let row = this.$refs.xTable1.getRowById(rowid)
      this.$refs.xTable1.scrollToRow(row)
    },
    anchorJump(e, link) {
      e.preventDefault();
    },
    getContainer() {
      return document.querySelector('#log-modal .ant-modal-wrap')
    },
    buildSource(source) {
      let result = {
        dataList: [],
        anchorList: [],
      }
      let acc = 0
      for (const [key, items] of Object.entries(source)) {
        result.dataList.push({
          phase: key,
          list: items,
        })
        result.anchorList.push({
          phase: key,
          index: acc,
          number: items.length,
        })

        acc += items.length;
      }

      return result
    },
    fetchTableData() {
      getLogList({ migTaskOid: this.oid })
        .then((data = []) => {
          this.tableDataInfo = this.buildSource(data)
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"))
        })
    }
  },
  created() {
    this.fetchTableData()
  },
  mounted() {
  },
  unmounted() {

  }
};
</script>

<style lang="less" scoped>
.table-container {
  padding-bottom: 20px;
}

// .table-wrapper {
//   position: absolute;
//   width: 100%;
//   height: 100%;
//   overflow-y: scroll;
// }
.anchor-label {
  padding: 0 10px;
  margin: 10px 0;
  border-left: 3px solid #1890ff;
}

.anchor-container {
  margin: 40px 15px;
  padding: 0 5px;
  border-radius: 10px;
  overflow: hidden;
}

</style>