<template>
  <div class="table-wrapper">
    <vxe-table ref="xTable1" :loading="tableLoading" :data="tableData" @checkbox-all="selectAllEvent"
      @checkbox-change="selectChangeEvent">
      <vxe-column field="name" :title="$t('txt_plan_name')"></vxe-column>
      <vxe-column field="status" :title="$t('txt_status')" width="100"></vxe-column>
      <vxe-column field="updateDate" :title="$t('txt_update_time')">
        <template #default="{ row }">{{ formatData(row.updateDate) }}</template>
      </vxe-column>
      <vxe-column field="description" :title="$t('txt_describe')"></vxe-column>
      <vxe-column field="" :title="$t('txt_operation')" width="100">
        <template #default="{ row }">
          <a-popconfirm v-if="isDraft(row)" :title="$t('txt_you_sure_perform_task')" :ok-text="$t('btn_confirm')"
            :cancel-text="$t('btn_cancel')" @confirm="onOperation('excute', row)">
            <a-tooltip>
              <template #title>{{ $t('txt_start') }}</template>
              <span><jw-icon type="jwi-kaigong"></jw-icon></span>
            </a-tooltip>
          </a-popconfirm>
          <a-tooltip v-if="isFailed(row)">
            <template #title>{{ $t('txt_exception_handling') }}</template>
            <span @click="onOperation('exceptionHandling', row)"><jw-icon type="jwi-iconclear"></jw-icon></span>
          </a-tooltip>
          <a-tooltip v-if="isDraft(row)">
            <template #title>{{ $t('btn_edit') }}</template>
            <span @click="onOperation('edit', row)"><jw-icon type="jwi-iconedit"></jw-icon></span>
          </a-tooltip>
          <a-tooltip>
            <template #title>{{ $t('txt_problem_report_view') }}</template>
            <span @click="onOperation('log', row)"><jw-icon type="jwi-iconinfo-circle-full"></jw-icon></span>
          </a-tooltip>
          <a-popconfirm v-if="isDraft(row)" :title="$t('txt_confirm_delete')" :ok-text="$t('btn_ok')"
            :cancel-text="$t('btn_cancel')" @confirm="onOperation('delete', row)">
            <a-tooltip>
              <template #title>{{ $t('txt_delete') }}</template>
              <span><jw-icon class="operation-icon" type="jwi-icondelete"></jw-icon></span>
            </a-tooltip>

          </a-popconfirm>
        </template>
      </vxe-column>
    </vxe-table>
    <task-create-modal v-if="taskCreateModalVisible" v-model:visible="taskCreateModalVisible" code="edit"
      :oid="current.oid"></task-create-modal>
    <log-table-modal v-if="logTableModalVisible" v-model:visible="logTableModalVisible"
      :oid="current.oid"></log-table-modal>
  </div>
</template>
<script>
import { EventBus, EVENT_BUS_NAMESPACE } from 'common/event-bus'
import TaskCreateModal from '../TaskCreateModal'
import LogTableModal from './components/LogTableModal'
import { formatDate } from "jw_utils/moment-date";
//  draft, migrating, finished, normalFailed, migrateFailed;
import {
  jobFindList,
  jobExecute,
  jobDelete,
  exceptionHandling,
} from "apis/setl"

export default {
  components: {
    TaskCreateModal,
    LogTableModal,
  },
  inject: ['getMgTaskClsOid'],
  data() {

    return {
      taskCreateModalVisible: false,
      logTableModalVisible: false,
      current: {},
      tableLoading: false,
      tableData: [
      ]
    }
  },
  methods: {
    formatData(date) {
      return formatDate(date)
    },
    isDraft(data) {
      return data.status === 'draft'
    },
    isFailed(data) {
      return data.status === 'normalFailed' || data.status === 'migrateFailed'
    },
    onOperation(code, row) {
      this.current = row;

      switch (code) {
        case 'log':
          this.logTableModalVisible = true
          break;
        case 'delete':
          this.tableLoading = true
          jobDelete({ oid: row.oid })
            .then((data) => {
              return this.fetchTableData()
            })
            .catch((err) => {
              this.$error(err.msg || this.$t("msg_failed"))
            })
            .finally(() => {
              this.tableLoading = false
            })
          break;
        case 'exceptionHandling':
          this.tableLoading = true
          exceptionHandling({ oid: row.oid })
            .then((data) => {
            })
            .catch((err) => {
              this.$error(err.msg || this.$t("msg_failed"))
            })
            .finally(() => {
              return this.fetchTableData()
            })
            .finally(() => {
              this.tableLoading = false
            })
          break;

        case 'excute':
          this.tableLoading = true

          jobExecute({ oid: row.oid })


          return new Promise((resolve, reject) => {
            setTimeout(() => {
              resolve()
            }, 1000)
          })
            .finally(() => {
              return this.fetchTableData()
            })
            .finally(() => {
              this.tableLoading = false
            })


          // jobExecute({ oid: row.oid })
          //   .then((data) => {
          //   })
          //   .catch((err) => {
          //     this.$error(this.$t("msg_failed"))
          //   })
          //   .finally(() => {

          //   })
          //   .finally(() => {
          //     return this.fetchTableData()
          //   })
          //   .finally(() => {
          //     this.tableLoading = false
          //   })

          break;

        case 'edit':

          this.taskCreateModalVisible = true;
          break;

        default:
          break;
      }
    },
    selectAllEvent({ checked }) {
      const records = this.$refs.xTable1.getCheckboxRecords()
    },
    selectChangeEvent({ checked }) {
      const records = this.$refs.xTable1.getCheckboxRecords()
    },
    fetchTableData() {
      jobFindList({ mgTaskClsOid: this.getMgTaskClsOid() })
        .then((data = []) => {
          this.tableData = data
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"))
        })
    }
  },
  created() {
    this.fetchTableData()
  },
  mounted() {
    EventBus.$on(EVENT_BUS_NAMESPACE.SETL_FRESH_JOB_TABLE, param => {
      this.fetchTableData()
    })
  },
  unmounted() {
    EventBus.$off(EVENT_BUS_NAMESPACE.SETL_FRESH_JOB_TABLE)
  }
};
</script>

<style lang="less" scoped>
.table-wrapper {
  position: absolute;
  width: 100%;
  height: 100%;
}
</style>