<template>
  <div class="task-wrapper">
    <div class="add-wrapper">
      <a-button class="btn" type="primary" @click="onCreateTask">
        <jw-icon type="jwi-iconadd"></jw-icon>
        {{ $t('txt_create_migration_task') }}
      </a-button>
    </div>
    <div class="table-container"><task-table></task-table></div>
    <task-create-modal v-if="taskCreateModalVisible" v-model:visible="taskCreateModalVisible"></task-create-modal>
    
  </div>
</template>
<script>
import TaskCreateModal from './components/TaskCreateModal'

import TaskTable from './components/TaskTable'



import {
  jobFindList
} from "apis/setl"

export default {
  components: {
    TaskCreateModal,
    TaskTable,
  },
  inject: ['getMgTaskClsOid'],
  data() {
    return {
      taskCreateModalVisible: false,
    };
  },
  methods: {
    onCreateTask() {
      this.taskCreateModalVisible = true
    },
  },
  created() {
  },
};
</script>

<style lang="less" scoped>
.task-wrapper {
  padding: 0 15px;

  .add-wrapper{
    overflow: hidden;
    padding-bottom: 15px;
    .btn{
      float: right;
    }
  }
  .table-container{
    position: relative;
    width: 100%;
    height: 500px;
  }
}
</style>