<template>
  <div class="main">
    <div class="left">
      <div class="top">
        <div>{{ $t('txt_classify') }}</div>
        <a-button type="primary" shape="circle" size="small" @click="onCreatType">
          <jw-icon type="jwi-iconsubItem-add"></jw-icon>
        </a-button>
      </div>
      <a-menu mode="inline" v-model:selectedKeys="selectedKeys">
        <a-menu-item v-for="item in lassificationList" :key="item.oid">
          <template #icon>
            <PieChartOutlined />
          </template>
          <a-tooltip>
            <template slot="title">
              <span>{{ item.name }}</span>
            </template>
            <div class="type-name">{{ item.name }}</div>
          </a-tooltip>


          <div class="icon-wrapper">
            <span @click="onEdit(item.oid)"><jw-icon class="operation-icon" type="jwi-iconedit"></jw-icon></span>
            <a-popconfirm :title="$t('txt_confirm_delete')" :ok-text="$t('btn_ok')" :cancel-text="$t('btn_cancel')"
              @confirm="onDelete(item.oid)">
              <span><jw-icon class="operation-icon" type="jwi-icondelete"></jw-icon></span>
            </a-popconfirm>

          </div>
        </a-menu-item>
      </a-menu>
      <create-type-modal :code="typeCreateCode" :onChange="onTaskChange"
        v-model:visible="typeCreatevisible"></create-type-modal>

    </div>
    <div class="right" v-if="lassificationList.length > 0" :key="getMgTaskClsOid()">
      <div class="header">
        <a-tabs v-model:activeKey="activeKey">
          <a-tab-pane key="1" :tab="this.$t('txt_base_info')">
          </a-tab-pane>
          <a-tab-pane key="2" :tab="this.$t('txt_migration_scheme_configuration')">
          </a-tab-pane>
          <a-tab-pane key="3" :tab="this.$t('txt_migration_task')">
          </a-tab-pane>
        </a-tabs>
      </div>
      <div class="content">
        <transition name="jwi-fade-in" mode="out-in" appear>
          <component :is="contentComponent" :oid="oid"></component>
        </transition>
      </div>
    </div>
    <div class="right" v-else>
      <div class="no-data-con">
        <img src="./no-data.gif" alt="" />
        <div>{{ $t('txt_no_classification') }}
          <a-button type="link"  @click="onCreatType">
            {{ $t('btn_new_create') }}
          </a-button>
          {{ $t('txt_add_c') }}
        </div>
      </div>
      <!-- <div class="no-data">
        <p>{{ $t('txt_please_create_classification') }}</p>
        <a-button type="primary" @click="onCreatType">{{ $t('txt_create_classification') }}</a-button>
      </div> -->
    </div>
  </div>
</template>
<script>
import { EventBus, EVENT_BUS_NAMESPACE } from 'common/event-bus'
import CreateTypeModal from './components/CreateTypeModal'
import BaseInfo from './components/BaseInfo'
import MigrationRuleConfiguration from './components/MigrationRuleConfiguration'
import Tasks from './components/Tasks'

import {
  classificationDelete,
  classificationFuzzy,
  classificationFindByOid,
} from "apis/setl"

export default {
  components: {
    CreateTypeModal,
    BaseInfo,
    MigrationRuleConfiguration,
    Tasks,
  },
  inject: ["setBreadcrumb", "addBreadcrumb"],
  provide() {
    return {
      getMgTaskClsOid: this.getMgTaskClsOid
    }
  },
  data() {
    return {
      lassificationList: [],
      typeCreatevisible: false,
      typeCreateCode: 'add', // add ; edit
      createTypeFormData: {
        name: '',
      },
      selectedKeys: [],
      activeKey: '1'
    };
  },
  computed: {
    oid: function () {
      return this.selectedKeys.join()
    },
    contentComponent: function () {
      switch (this.activeKey) {
        case '1':
          return BaseInfo
        case '2':
          return MigrationRuleConfiguration
        case '3':
          return Tasks

        default:
          return BaseInfo
      }
    }
  },
  methods: {
    getMgTaskClsOid() {
      return this.selectedKeys.join()
    },
    onCreatType() {
      this.typeCreateCode = 'add'
      this.typeCreatevisible = true

    },
    onEdit(oid) {
      this.typeCreateCode = 'edit'
      this.typeCreatevisible = true
    },
    onDelete(oid) {
      classificationDelete({ oid: oid })
        .then((data) => {
          this.selectedKeys = []
          this.fetechClassificationList()
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"))
        })
    },
    onTaskChange(oid) {
      this.selectedKeys = [oid]
      EventBus.$emit(EVENT_BUS_NAMESPACE.SETL_CLASSFICATION_UP, {oid: oid})
      this.fetechClassificationList()
    },
    fetechClassificationList() {
      return classificationFuzzy({ searchKey: '' })
        .then((list = []) => {
          this.lassificationList = list
          if (this.selectedKeys.length === 0 && list?.[0].oid) {
            this.selectedKeys = [list[0].oid]
          }
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"))
        })
    }
  },
  created() {
    this.setBreadcrumb([{ name: this.$t("txt_data_migration") }])
    this.fetechClassificationList()
  },
};
</script>

<style lang="less" scoped>
// .main-wrapper {
//   display: flex;
//   flex-direction: column;

//   .header-left {
//     flex: 0 0 auto;
//   }

//   .main {
//     flex: 1 1 auto;
//   }
// }

.main {
  display: flex;
  width: 100%;
  background-color: #fff;
  height: 100%;
  overflow: hidden;

  .left {
    border-right: 1px solid #eee;
    flex: 0 0 300px;

    .top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #eee;
      padding: 10px 15px;
    }

    .icon-wrapper {
      float: right;
    }

    .operation-icon {
      display: none;
    }

    .type-name {
      display: inline-block;
      width: 200px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .ant-menu-item {
      &:hover {
        .operation-icon {
          display: inline-block;
        }
      }
    }



  }

  .group-name {
    padding: 15px 0;

    span {
      border-left: 2px solid #1890ff;
      padding-left: 5px;
    }
  }

  .right {
    flex: 1 1 auto;
    height: 100%;
    overflow-y: scroll;
    display: flex;
    flex-direction: column;

    .no-data {
      position: relative;
      top: 30%;
      text-align: center;

      p {
        padding: 20px 0;
      }
    }

    .no-data-con {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      img {
        width: 193px;
        margin-bottom: 25px;
      }

      span {
        color: #255ed7;
        cursor: pointer;
      }

      i {
        cursor: pointer;
      }

      .ant-btn-link {
        padding: 0;
        vertical-align: middle;
      }
    }

    .header {
      flex-shrink: 0;
    }

    .content {
      flex: 1 1 auto;
      overflow-y: scroll;
    }
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.5s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }

}

.jwi-fade-in-enter-active,
.jwi-fade-in-leave-active {
  transition: all .3s cubic-bezier(.55, 0, .1, 1);
}

.jwi-fade-in-enter,
.jwi-fade-in-leave-active {
  opacity: 0;
}</style>