<template>
<div  style="display:flex; justify-content:space-between; align-items:flex-start; width:100%; height:100vh;">
  <!-- 分组树-------------------------------------------------------------------------------------------------------------------------------------- -->
  <a-tree
    v-if="isShowTree"
    :tree-data="treeData"
    :selected-keys="selectedKeys"
    :replaceFields="{children:'children', title:'groupName', key:'id' }"
    default-expand-all
    block-node
    show-icon
    style="width:300px; height:100%; overflow:auto; border-right:1px solid #d9d9d9;"
  >
    <!-- 顶层前缀 -->
    <!-- slot没有传参，只能传递特定值 -->
    <span slot="titleSlot">
      <span  class="space-between">
        数据字典-分组
        <a-icon @click.stop="addVisible0=true" class="ceil-class" type="plus" />
      </span>
    </span>

    <!-- slot-scope必须配合slot="custom"使用，可以传递整条记录 -->
    <template slot="custom" slot-scope="record">
      <div @click="selectTreeItem(record)" class="space-between">
        <span>{{ record.groupName }}</span>

        <span>
          <a-icon @click.stop="treeEditBtn(record)" class="ceil-class" type="edit" />
          <span @click.stop="deleteTreeItem(record.id)" class="jwi-delete ceil-class"></span>
        </span>
      </div>
    </template>
  </a-tree>

  <!-- 新建分组 -->
  <jw-modal-form 
    :key="'1'"
    v-if="addVisible0"
    :visible.sync="addVisible0"
    :title="'新建分组'"
    :formDatas="{
      groupCode: '',
      groupName: ''
    }"
    :keys="[
      {  prop:'groupName', label:'名称', rules:[rules.required], props:{ is:'a-input', value:'groupName', placeholder:'请输入名称' } },
      {  prop:'groupCode', label:'编码', rules:[rules.required], props:{ is:'a-input', value:'groupCode', placeholder:'请输入名称' } },
    ]"
    @submit="addTreeItem"
  />

    <!-- 编辑分组 -->
  <jw-modal-form
    :key="'2'"
    v-if="editVisible0"
    :visible.sync="editVisible0"
    :title="'编辑分组'"
    :formDatas="editItem0"
    :keys="[
      {  prop:'groupName', label:'名称', rules:[rules.required], props:{ is:'a-input', value:'groupName', placeholder:'请输入名称' } },
      {  prop:'groupCode', label:'编码', rules:[rules.required], props:{ is:'a-input', value:'groupCode', placeholder:'请输入名称' } },
    ]"
    @submit="editTreeItem"
  />






<!-- 数据字典-定义------------------------------------------------------------------------------------------------------------------------------------------------------ -->
<div style="width:calc((100% - 300px) * 0.4); height:100%; overflow:auto; border-right:1px solid #d9d9d9; padding:0px 10px;">
  <div class="space-between" style="padding:20px 0px;">
    <span>数据字典-定义</span>
    <span>
      <a-input-search placeholder="编码、名称" style="width: 200px" @search="defSearch" />
      <a-button @click.stop="addVisible1=true" icon="plus" style="margin-left:10px;">添加</a-button>
    </span>
  </div>
  <!-- 定义 -->
  <a-table 
    :columns="columns" 
    :data-source="dataSource"
    :rowKey="record=>record.id"
    :pagination="{
      hideOnSinglePage:true,
      showSizeChanger:true,
      total:dataSource.length,
    }"
    :customRow="rowClick1"
  >
    <span slot="action" slot-scope="text, record" @click="selectDefItem(record)">
      <a-icon @click.stop="defEditBtn(record)" class="ceil-class" type="edit" />
      <span @click="deleteDefItem(record.id)" class="jwi-delete ceil-class"></span>
    </span>
  </a-table>
</div>



<!-- 新建定义 -->
  <jw-modal-form 
    :key="'3'"
    v-if="addVisible1"
    :visible.sync="addVisible1"
    :title="'新建定义'"
    :formDatas="{
      dictCode: '',
      dictName: '',
      dictGroupCode:groupCode,
    }"
    :keys="[
      {  prop:'dictName', label:'名称', rules:[rules.required], props:{ is:'a-input', value:'dictName', placeholder:'请输入名称' } },
      {  prop:'dictCode', label:'编码', rules:[rules.required], props:{ is:'a-input', value:'dictCode', placeholder:'请输入编码' } },
      // {  prop:'dictGroupCode', label:'groupCode', rules:[rules.required], placeholder:'请输入groupCode' },
    ]"
    @submit="addDefItem"
  />



<!-- 编辑定义 -->
  <jw-modal-form
    :key="'4'"
    v-if="editVisible1"
    :visible.sync="editVisible1"
    :title="'编辑定义'"
    :formDatas="editItem1"
    :keys="[
      {  prop:'dictName', label:'名称', rules:[rules.required], props:{ is:'a-input', value:'dictName', placeholder:'请输入名称' } },
      {  prop:'dictCode', label:'编码', rules:[rules.required], props:{ is:'a-input', value:'dictCode', placeholder:'请输入编码' } },
      // {  prop:'dictGroupCode', label:'groupCode', rules:[rules.required], placeholder:'请输入groupCode' },
    ]"
    @submit="editDefItem"
  />





<!-- 数据字典-值----------------------------------------------------------------------------------------------------------------------------------------------------------------------------- -->
<div style="width:calc((100% - 300px) * 0.6); padding:0px 10px;">
  <div class="space-between" style="padding:20px 0px;">
    <span>数据字典-值</span>
    <span>
      <a-input-search placeholder="编码、名称、English、简体、繁体" style="width: 200px" @search="valueSearch" />
      <a-button @click.stop="addVisible2=true" icon="plus" style="margin-left:10px;">添加</a-button>
    </span>
  </div>
  <!-- 值 -->
  <a-table 
    :columns="columns2" 
    :data-source="dataSource2"
    :rowKey="record=>record.id"
    :pagination="{
      hideOnSinglePage:true,
      showSizeChanger:true,
      total:dataSource.length,
    }"
  >
    <span slot="action" slot-scope="text, record">
      <a-icon @click.stop="valueEditBtn(record)" class="ceil-class" type="edit" />
      <span @click="deleteValueItem(record.id)" class="jwi-delete ceil-class"></span>
    </span>
  </a-table>
</div>


<!-- 新建值 -->
  <jw-modal-form 
    :key="'6'"
    v-if="addVisible2"
    :visible.sync="addVisible2"
    :title="'新建值'"
    :formDatas="{
      dictCode: '',
      dictName: '',
      dictGroupCode:groupCode,
    }"
    :keys="[
      {  prop:'valueCode', label:'code值', rules:[rules.required], props:{ is:'a-input', value:'valueCode', placeholder:'请输入code值' } },
      {  prop:'enValue', label:'英文', rules:[rules.required], props:{ is:'a-input', value:'enValue', placeholder:'请输入英文' } },
      {  prop:'zhValue', label:'简体', rules:[rules.required], props:{ is:'a-input', value:'zhValue', placeholder:'请输入简体' } },
      {  prop:'twValue', label:'繁体', rules:[rules.required], props:{ is:'a-input', value:'twValue', placeholder:'请输入繁体' } },
    ]"
    @submit="addValueItem"
  />



<!-- 编辑值 -->
  <jw-modal-form
    :key="'5'"
    v-if="editVisible2"
    :visible.sync="editVisible2"
    :title="'编辑值'"
    :formDatas="editItem2"
    :keys="[
      {  prop:'valueCode', label:'code值', rules:[rules.required], props:{ is:'a-input', value:'valueCode', placeholder:'请输入code值' } },
      {  prop:'enValue', label:'英文', rules:[rules.required], props:{ is:'a-input', value:'enValue', placeholder:'请输入英文' } },
      {  prop:'zhValue', label:'简体', rules:[rules.required], props:{ is:'a-input', value:'zhValue', placeholder:'请输入简体' } },
      {  prop:'twValue', label:'繁体', rules:[rules.required], props:{ is:'a-input', value:'twValue', placeholder:'请输入繁体' } },
    ]"
    @submit="editValueItem"
  />




</div>
</template>



<script>
import axios from 'axios'
import rules from '../../utils/rules.js'

import {getList} from '../../apis/dataDictionary/index.js'

export default {
  name: "jwDictionary",
  data() {
    return {
      baseUrl:'http://gateway.dev.jwis.cn',
      appName:'pdm',
      accesstoken:'eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJKV0lTIiwiaWF0IjoxNjQ1NDIzODUyLCJzdWIiOiJITEwiLCJleHAiOjE2NDYwMjg2NTIsInVzZXIiOnsib2lkIjoiMzE1ZTgyMjItZTA1Zi0xMWViLWE5YmEtMDAwYzI5NzI0NzkwIiwibmFtZSI6Iua1i-ivlei0puWPt0hMTCIsImFjY291bnQiOiJITEwiLCJ0ZW5hbnRPaWQiOiIzMTQzOTcyNi1lMDVmLTExZWItYTliYS0wMDBjMjk3MjQ3OTAiLCJ0ZW5hbnRBbGlhcyI6ImdnY2NmaWFoZSIsImNvbXBhbnlOYW1lIjoi5ryU56S65L2_55SoIiwiZGlzYWJsZWQiOmZhbHNlLCJhZG1pbiI6dHJ1ZSwicGxhdGZvcm1BZG1pbiI6ZmFsc2UsImljb24iOm51bGwsInNlY3JldElkIjoiMjU1ZThjMjE3NTJkNDBkNzk1MzQ2ZGFmM2NmMTAwOGIiLCJzZWNyZXRMZXZlbCI6bnVsbH0sImFjY291bnQiOiJITEwiLCJ1c2VyT2lkIjoiMzE1ZTgyMjItZTA1Zi0xMWViLWE5YmEtMDAwYzI5NzI0NzkwIn0.Jh7zX_aVXqARuJ13353p0p8nVOfGx9a5ybU1PzaC6-U',
  
  
      rules,
      

      isShowTree:true,   // 控制重新渲染树结构
      treeData:[
        {
          groupName: "数据字典-分组",
          id: "0",
          // scopedSlots: { title: "custom" },
          slots: { title:'titleSlot' },

          // children: [
          //   {
          //     groupName: "子级标题0-0",
          //     id: "0-0",
          //     scopedSlots: { title: "custom" },
          //     // slots: { icon: 'iconSlot',title:'titleSlot' },
          //   },
          // ],
        },
      ],
      selectedKeys:['0-0'],
      addVisible0:false,    // 控制分组的新建弹窗
      editVisible0:false,
      editItem0:{},  // 编辑弹窗的数据


      // isShowDefinition:true,
      dataSource:[
        // {  dictCode: 'xxx', dictName: 'xxx'  },
      ],
      columns:[
        { title:'编码', dataIndex: 'dictCode', key: 'dictCode' },
        { title: '名称', key: 'dictName', dataIndex: 'dictName' },
        { title: '操作', key: 'action', scopedSlots: { customRender: 'action' }, width:200, align:'center' },
      ],
      groupCode:'',
      addVisible1:false,    // 控制分组的新建弹窗
      editVisible1:false,
      editItem1:{},  // 编辑弹窗的数据
      rowClick1:record =>({
        on:{
          click:()=>{
            // console.log(record);
            this.selectDefItem(record)//触发methods中的方法
          }
        }
      }),


      dataSource2:[
        // {  valueCode: 'code1', name2: 'xxx', name3:'xxxx', name4:'xxxx'  },
      ],
      columns2:[
        { title:'编码', dataIndex: 'valueCode', key: 'valueCode' },
        { title: 'English', key: 'enValue', dataIndex: 'enValue' },
        { title: '简体', key: 'zhValue', dataIndex: 'zhValue' },
        { title: '繁体', key: 'twValue', dataIndex: 'twValue' },
        { title: '操作', key: 'action', scopedSlots: { customRender: 'action' }, width:200, align:'center' },
      ],
      dictCode:'',
      dictDefinitionCode:'',
      addVisible2:false,    // 控制分组的新建弹窗
      editVisible2:false,
      editItem2:{},  // 编辑弹窗的数据
    };
  },
  methods: {
    getListTree(){
      axios.request({
        url:this.baseUrl + '/dict/group/search',
        method:'get',
        headers:{
          accesstoken:this.accesstoken,
        },
      })
      .then(res=>{
        // console.log("getListTree res: ",res);
        if( res.data.code==0 && res.data.result.rows.length>0 ){
          // console.log("res.result.rows: ",res.data.result.rows);
          this.treeData[0].children = res.data.result.rows.map(c=>({
            ...c,
            scopedSlots: { title: "custom" },
          }));
          // console.log("this.treeData: ",this.treeData);

          // 重新渲染
          this.isShowTree = false;
          let timer = setTimeout(()=>{
            this.isShowTree = true;
            clearTimeout(timer);
          },10);

        }
      }).catch(error=>{
        console.log("error: ",error);
      });
    },
    addTreeItem(params){
      console.log('params: ',params);
      
      axios.request({
        url:this.baseUrl + '/dict/group/create',
        method:'post',
        headers:{
          accesstoken:this.accesstoken,
          appName:this.appName,
        },
        data:params,
      }).then(res=>{
        // console.log("res: ",res);
        if( res.data.code==0 ){
          this.$success('添加成功');
          this.addVisible0 = false;
          this.getListTree();
        }else{
          this.$error(res.data.message || '添加失败');
        }
      }).catch(error=>{
        console.log("error: ",error);
      });

    },
    deleteTreeItem(id){
      let _this = this;

      this.$confirm({
        title: '是否删除',
        content: '你确定要删除这条记录吗？',
        onOk() {
            console.log("删除-------");
            axios.request({
              url:_this.baseUrl + '/dict/group/deleteById/' + id,
              method:'post',
              headers:{
                accesstoken:_this.accesstoken,
              },
            }).then(res=>{
              // console.log("deleteTreeItem res: ",res);
              if( res.data.code==0 ){
                // 重新获取数据
                _this.getListTree();
                _this.$success('删除成功');
              }
              else{
                _this.$error(res.data.message);
              }
            }).catch(error=>{
              console.log("error: ",error);
            });
        },
        onCancel() {
          console.log('Cancel');
        },
      });

      
    },
    // 点击编辑按钮
    treeEditBtn({dataRef}){
      console.log("dataRef: ",dataRef);
      this.editItem0 = {
        groupCode: dataRef.groupCode,
        groupName: dataRef.groupName,
        id: dataRef.id,
      };
      this.editVisible0=true
    },
    editTreeItem(params){
      console.log('params: ',params);
      
      axios.request({
        url:this.baseUrl + '/dict/group/update',
        method:'post',
        headers:{
          accesstoken:this.accesstoken,
          appName:this.appName,
        },
        data:params,
      }).then(res=>{
        // console.log("res: ",res);
        if( res.data.code==0 ){
          this.$success('修改成功');
          this.editVisible0 = false;
          this.getListTree();
        }else{
          this.$error(res.data.message || '修改失败');
        }
      }).catch(error=>{
        console.log("error: ",error);
      });

    },
    // 选择树节点，更新定义表
    selectTreeItem({dataRef}){
      console.log("dataRef: ",dataRef,"选择树节点");
      this.getDefList(dataRef.groupCode);
      this.groupCode = dataRef.groupCode;
      // console.log("this.groupCode: ",this.groupCode);
    },


    getDefList(groupCode=this.groupCode,keyword=''){
      if(!groupCode) return false;

      axios.request({
        url:this.baseUrl + '/dict/dictDefinition/searchByGroupCode/' + groupCode,
        method:'get',
        headers:{
          accesstoken:this.accesstoken,
        },
        params:{
          currPage:1,
          pageSize:10000
        },
      }).then(res=>{
        // console.log("res: ",res);
        if( res.data.code==0 && res.data.data.length>=0 ){
          this.dataSource = res.data.data.filter(c=>(
            c.dictName.includes(keyword) || c.dictCode.includes(keyword)
          ));
          this.dataSource2 = [];
        }
      }).catch(error=>{
        console.log("error: ",error);
      });
    },
    // 搜索定义：过滤
    defSearch(value) {
      // console.log(value);
      this.getDefList(this.groupCode,value);
    },
    addDefItem(params){
      console.log('params: ',params);
      
      axios.request({
        url:this.baseUrl + '/dict/dictDefinition/create',
        method:'post',
        headers:{
          accesstoken:this.accesstoken,
          appName:this.appName,
        },
        data:params,
      }).then(res=>{
        // console.log("res: ",res);
        if( res.data.code==0 ){
          this.$success('添加成功');
          this.addVisible1 = false;
          this.getDefList();
        }else{
          this.$error(res.data.message || '添加失败');
        }
      }).catch(error=>{
        console.log("error: ",error);
      });

    },
    deleteDefItem(id){
      let _this = this;

      this.$confirm({
        title: '是否删除',
        content: '你确定要删除这条记录吗？',
        onOk() {
            console.log("删除-------");
            axios.request({
              url:_this.baseUrl + '/dict/dictDefinition/deleteById/' + id,
              method:'post',
              headers:{
                accesstoken:_this.accesstoken,
              },
            }).then(res=>{
              // console.log("res: ",res);
              if( res.data.code==0 ){
                // 重新获取数据
                _this.getDefList();
                _this.$success('删除成功');
              }
              else{
                _this.$error(res.data.message);
              }
            }).catch(error=>{
              console.log("error: ",error);
            });
        },
        onCancel() {
          console.log('Cancel');
        },
      });

      
    },
    // 点击编辑按钮
    defEditBtn(record){
      console.log("record: ",record);
      this.editItem1 = {
        dictCode: record.dictCode,
        dictName: record.dictName,
        id: record.id,
        dictGroupCode:record.dictGroupCode,
      };
      this.editVisible1=true
    },
    editDefItem(params){
      console.log('params: ',params);
      
      axios.request({
        url:this.baseUrl + '/dict/dictDefinition/update',
        method:'post',
        headers:{
          accesstoken:this.accesstoken,
          appName:this.appName,
        },
        data:params,
      }).then(res=>{
        // console.log("res: ",res);
        if( res.data.code==0 ){
          this.$success('修改成功');
          this.editVisible1 = false;
          this.getDefList();
        }else{
          this.$error(res.data.message || '修改失败');
        }
      }).catch(error=>{
        console.log("error: ",error);
      });

    },
    // 选择树节点，更新定义表
    selectDefItem(record){
      console.log("record: ",record,"选择定义");
      this.getValueList(record.dictCode);
      this.dictCode = record.dictCode;
      // console.log("this.dictCode: ",this.dictCode);
    },
    


    getValueList(dictCode=this.dictCode,keyword=''){
      if(!dictCode) return false;

      axios.request({
        url:this.baseUrl + '/dict/dictValueDefinition/searchByDictCode/' + dictCode,
        method:'get',
        headers:{
          accesstoken:this.accesstoken,
        },
        params:{
          currPage:1,
          pageSize:10000
        },
      }).then(res=>{
        // console.log("res: ",res);
        if( res.data.code==0 && res.data.data.length>=0 ){
          this.dataSource2 = res.data.data.map(c=>{
            let enValue = c.multiLanguageValue.find(currentValue=>currentValue.languageCode === "en-US") ? c.multiLanguageValue.find(currentValue=>currentValue.languageCode === "en-US").valueText : '';
            let zhValue = c.multiLanguageValue.find(currentValue=>currentValue.languageCode === "zh-CN") ? c.multiLanguageValue.find(currentValue=>currentValue.languageCode === "zh-CN").valueText : '';
            let twValue = c.multiLanguageValue.find(currentValue=>currentValue.languageCode === "zh-TW") ? c.multiLanguageValue.find(currentValue=>currentValue.languageCode === "zh-TW").valueText : '';

            // console.log("获取: ",enValue,zhValue,twValue);
            return {
              ...c,
              enValue,zhValue,twValue
            }
          })
          this.dataSource2 = this.dataSource2.filter(c=>(
            c.valueCode.includes(keyword) || c.enValue.includes(keyword) || c.zhValue.includes(keyword) || c.twValue.includes(keyword)
          ));
        }
      }).catch(error=>{
        console.log("error: ",error);
      });
    },
    // 搜索值：过滤
    valueSearch(value) {
      // console.log(value);
      this.getValueList(this.dictCode,value);
    },
    addValueItem(params){
      console.log('params: ',params);

      axios.request({
        url:this.baseUrl + '/dict/dictValueDefinition/create',
        method:'post',
        headers:{
          accesstoken:this.accesstoken,
          appName:this.appName,
        },
        data:{
          dictDefinitionCode: this.dictCode,
          multiLanguageValue : [
            {dictDefinitionCode: this.dictCode, languageCode: "en-US", valueText: params.enValue},
            {dictDefinitionCode: this.dictCode, languageCode: "zh-CN", valueText: params.zhValue},
            {dictDefinitionCode: this.dictCode, languageCode: "zh-TW", valueText: params.twValue},
          ],
          valueCode: params.valueCode,
        },
      }).then(res=>{
        // console.log("res: ",res);
        if( res.data.code==0 ){
          this.$success('添加成功');
          this.addVisible2 = false;
          this.getValueList();
        }else{
          this.$error(res.data.message || '添加失败');
        }
      }).catch(error=>{
        console.log("error: ",error);
      });

    },
    deleteValueItem(id){
      let _this = this;

      this.$confirm({
        title: '是否删除',
        content: '你确定要删除这条记录吗？',
        onOk() {
            // console.log("删除-------");
            axios.request({
              url:_this.baseUrl + '/dict/dictValueDefinition/deleteById/' + id,
              method:'post',
              headers:{
                accesstoken:_this.accesstoken,
              },
            }).then(res=>{
              // console.log("res: ",res);
              if( res.data.code==0 ){
                // 重新获取数据
                _this.getValueList();
                _this.$success('删除成功');
              }
              else{
                _this.$error(res.data.message);
              }
            }).catch(error=>{
              console.log("error: ",error);
            });
        },
        onCancel() {
          console.log('Cancel');
        },
      });

      
    },
    // 点击编辑按钮
    valueEditBtn(record){
      console.log("record: ",record);
      this.editItem2 = {
        ...record,
        valueCode: record.valueCode,
        valueText_0: record.enValue,
        valueText_1: record.zhValue,
        valueText_2: record.twValue,
      };
      this.editVisible2=true
    },
    editValueItem(params){
      console.log('params---: ',params);
      params.multiLanguageValue = params.multiLanguageValue.map(c=>({
          ...c,
          valueText:c.languageCode==="en-US" ? params.valueText_0 : c.languageCode==="zh-CN" ? params.valueText_1 : params.valueText_2,
        }))
      // console.log("params.multiLanguageValue--------： ",params.multiLanguageValue);
      
      axios.request({
        url:this.baseUrl + '/dict/dictValueDefinition/update',
        method:'post',
        headers:{
          accesstoken:this.accesstoken,
          appName:this.appName,
        },
        data:params,
      }).then(res=>{
        // console.log("res: ",res);
        if( res.data.code==0 ){
          this.$success('修改成功');
          this.editVisible2 = false;
          this.getValueList();
        }else{
          this.$error(res.data.message || '修改失败');
        }
      }).catch(error=>{
        console.log("error: ",error);
      });
    },

  },
  created(){
    this.getListTree();
  },
};
</script>

<style lang="less"  scoped>
.space-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.ceil-class {
  padding-left: 10px;
}
</style>