<template>
  <model
    :value="$route.params.oid"
    class="model-3d"
  ></model>
</template>

<script>
import model from "jw_engine/example/views/cad";
import { useEngineStore } from "jw_engine/stores/engine";
export default {
  components: {
    model
  },
  mounted(){
    useEngineStore().workbenchFactory["ASSEMBLY"] =
      require("../../components/engineConfig/assembly").workbench;
  }
};
</script>

<style lang="less" scoped>
.model-3d {
  height: 100%;
}
</style>