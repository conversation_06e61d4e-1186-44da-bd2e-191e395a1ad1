<template>
  <div class="table-page">
    <jw-table
      :columns="columns"
      :data-source="tableData"
      :showPage="false"
    >
      <template #systemDefault="{ row }">
        {{ row.systemDefault ? $t('btn_basic_attr') : $t('btn_exten_attr') }}
      </template>
      <template #toolname="{ row }">
        <div>
          <a-input v-model.trim="row.toolname" @blur="saveMappingData(row)"></a-input>
        </div>
      </template>
    </jw-table>
  </div>
</template>

<script>
import { mappingList, savePropMapping } from "apis/system-config";
export default {
  props: {
    propList: {
      type: Object,
    },
    model: {
      type: Object,
    }
  },
  data(){
    return {
      tableData: [],
      columns: [
        {
					title: this.$t('table_identifier'),
					field: "code",
				},
        {
					title: this.$t('tabel_name'),
					field: "displayName",
				},
        {
          title: this.$t('prop_type'),
          field: 'systemDefault',
          slots: {
            default: 'systemDefault'
          }
        },
        {
					title: this.$t('tool_prop_name'),
					field: "toolname",
          slots: {
            default: 'toolname',
          },
				},
      ]
    }
  },
  watch: {
    propList: function(val) {
      if(val){
        const { basic, extend } = val
        this.tableData.length = 0
        this.tableData.push(...basic)
        this.tableData.push(...extend)
        this.mappingListFun()
      }else{
        this.tableData.length = 0
      }
    }
  },
  methods: {
    //查询映射表
    mappingListFun(){
      mappingList(this.model.code).then((resp) => {
        if(resp){
          const { mapping } = resp
          this.tableData.forEach((row) => {
            mapping.forEach((mapp) => {
              if(row.code === mapp.modelBAttrCode){
                this.$set(row, 'toolname', mapp.modelAAttrCode)
              }
            })
            // if(!row.toolname){
            //   this.$set(row, 'toolname', row.displayName)
            // }
          })
        }
      })
    },
    //保存映射值
    saveMappingData(row) {
      let params = {
        modelBCode: this.model.code,
        businessType:"CreateMCAD",
        mapping:[]
      }
      params.mapping = this.tableData.filter((item) => item.toolname).map((item) => {
        return {
          modelAAttrCode: item.toolname,
          modelBAttrCode: item.code,
        }
      })
      savePropMapping(params)
    }
  },
  mounted(){

  }
}
</script>

<style lang="less" scoped>
.table-page{
  width: 100%;
  height: 100%;
}
</style>