<!--
* @Author:<EMAIL>
* @Date: 2022/06/08 11:38:18
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/06/08 11:38:18
* @Description: MCAD属性映射规则
-->
<template>
  <multipane class="custom-resizer mcad-attribute-mapping" layout="vertical">
    <!-- <div class="tree-model pane">
      <div v-for="item in MCADdata" :class="classesColor(item.code, item.oid)"
        @click="getAssemblyData(item.oid, item.code, 'cad')" :key="item.oid">
        <span>
          <span :title="item.name">{{ item.name }}</span>
          <a-icon type="check-circle" />
        </span>
        <a-icon type="right-square" />
      </div>
    </div> -->
    <div class="tree-model  pane">
      <a-tree :defaultExpandAll="false" :autoExpandParent="false" v-mode:selectedKeys="[leftPartState.treeState.selected.oid]" :tree-data="leftPartState.treeState.tree"
        :replaceFields="{ title: 'name', key: 'oid' }"
      >
        <template #title="{ name, code, oid}"><span @click="onLeftTreeSelect(code, oid)">{{ code }}</span></template>
      </a-tree>
    </div>
    <multipane-resizer></multipane-resizer>
    <multipane class="custom-resizer assembly-mapping pane" layout="vertical">
      <div class="assembly pane">
        <header>
          <span>{{ curTree.code }} </span>
        </header>
        <main>
          <div class="basic">
            <label>{{ $t('base_property') }}</label>
            <div>
              <a-tag :draggable="!item.color" v-for="item in assemblyData.basic" :key="item.oid" @dragover="vv"
                @dragend="dragend('basic', item.code)" :color="item.color">
                {{ item.name }}
              </a-tag>
            </div>
          </div>
          <div class="extend">
            <label>{{ $t('btn_exten_attr') }}</label>
            <div>
              <a-tag draggable v-for="item in assemblyData.extend" :key="item.oid" @dragover="vv"
                @dragend="dragend('extend', item.code)" :color="item.color">
                {{ item.name }}
              </a-tag>
            </div>
          </div>
        </main>
      </div>
      <multipane-resizer></multipane-resizer>
      <div class="mapping pane">
        <header>
          <div class="header-left">
            <span>Mapping</span>
            <span>({{ $t('lieft_mapping') }})</span>
          </div>
          <a-button type="primary" :loading="saveLoading" @click="saveMapping" v-show="saveButton">
            {{ $t('btn_save') }}
          </a-button>
        </header>
        <!-- <a-select v-model.trim="partCode" style="width: calc(50% - 8px)" @dropdownVisibleChange="dropdownVisibleChange"
          @change="handleChange">
          <a-select-option :value="item.oid" v-for="item in PARTdata" :key="item.oid">
            {{ item.code }}
          </a-select-option>
        </a-select> -->

        <a-tree-select style="width: 100%" v-mode:value="rightPartState.treeState.selected.oid"
          :replaceFields="{ title: 'name', key: 'oid', value: 'oid' }"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" :tree-data="rightPartState.treeState.tree"
          placeholder="Please select" tree-default-expand-all @change="onRightTreeChange">
          <template #title="{ key, title }"><span>{{ title }}</span></template>
        </a-tree-select>
        <main>
          <div class="basic">
            <label>{{ $t('base_property') }}</label>
            <div>
              <div class="assembly-node" v-for="item in mappingData.basic" :key="item.oid">
                <p>{{ item.name }}</p>
                <div @dragenter="dragenter(item)" @dragleave="dragleave">
                  <a-tag v-if="item.value" color="">
                    {{ item.value }}
                    <a-icon type="close" @click="clearValue(item)" />
                  </a-tag>
                </div>
              </div>
            </div>
          </div>
          <div class="extend">
            <label>{{ $t('btn_exten_attr') }}</label>
            <div>
              <div class="assembly-node" v-for="item in mappingData.extend" :key="item.oid">
                <p>{{ item.name }}</p>
                <div @dragenter="dragenter(item)" @dragleave="dragleave">
                  <a-tag draggable v-if="item.value" color="">
                    {{ item.value }}
                    <a-icon type="close" @click="clearValue(item)" />
                  </a-tag>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </multipane>
  </multipane>
</template>
<script>
import {
  getModelData,
  getAssemblyData,
  getMappingData,
  saveMapping,
  getMappingStatus,
} from "../api";
import { Multipane, MultipaneResizer } from "vue-multipane";
import { Modal } from "ant-design-vue";

function initPartionState() {
  return {
    treeState: {
      tree: [],
      selected: {},
    }, // 树形选项数据
    propertyState: {
      maps: {}, // 储存映射关系
      basic: [], // 基础属性
      extend: [], // 扩展属性
    },
  }
}

export default {
  name: "attribute-mapping",
  inject: ["setBreadcrumb", "addBreadcrumb"],

  components: {
    Multipane,
    MultipaneResizer,
  },
  data() {
    return {
      curTree: {},
      modelData: [],
      assemblyData: {
        basic: [],
        extend: [],
      },
      partCode: undefined,
      mappingData: {
        basic: [],
        extend: [],
      },
      dragenterAssembly: undefined,
      time: 0,
      saveButton: false,
      saveLoading: false,
      saveMappingOid: undefined,
      mappingStatus: [],
      initPartCode: undefined,
      initMappingData: {},
      showMapping: [],

      //-----//
      mode: 'cadToPart', // cadToPart, partToCad
      leftPartState: initPartionState(),
      rightPartState: initPartionState()
    };
  },
  computed: {
    classesColor() {
      return (code, oid) => [
        {
          node: true,
          select: this.curTree.code === code,
          tag: this.mappingStatus.some((p) => p.cadTypeOid === oid),
        },
      ];
    },
  },
  methods: {
    clearValue(item) {
      this.saveButton = true;
      let basicNode = this.assemblyData.basic.find(
        (p) => p.code === item.value
      );

      basicNode && (basicNode.color = "");
      let extendNode = this.assemblyData.extend.find(
        (p) => p.code === item.value
      );
      extendNode && (extendNode.color = "");
      item.value = "";
    },
    handleChange(value) {
      // if (this.initPartCode === value) {
      //   this.mappingData = {};
      //   $.extend(true, this.mappingData, {}, this.initMappingData);
      // } else {
      //   this.initMappingData = {};
      //   $.extend(true, this.initMappingData, {}, this.mappingData);
      //   this.mappingData.basic.forEach((element) => {
      //     element.value = "";
      //   });
      //   this.mappingData.extend.forEach((element) => {
      //     element.value = "";
      //   });
      // }
      this.saveButton = true;
      this.getMappingData();
    },
    dropdownVisibleChange(open) {
      open && this.$warning(this.$t("msg_selected_attr_clear"));
    },
    dragenter(e) {
      this.dragenterAssembly = e;
    },
    dragleave() {
      this.time = new Date();
    },
    dragend(name, code) {
      //如果是mac系统则过于精准加大时间间隔
      if (/(iPhone|iPad|iPod|iOS|Mac|AppleWebKit)/i.test(navigator.userAgent)) {
        if (new Date() - this.time > 1000) return;
      } else {
        if (new Date() - this.time > 200) return;
      }
      if (!this.dragenterAssembly.name) return;
      this.saveButton = true;
      let newNode =
        this.assemblyData["basic"].find((p) => p.code === code) ||
        this.assemblyData["extend"].find((p) => p.code === code);
      let oldNode =
        this.assemblyData["extend"].find(
          (p) => p.code === this.dragenterAssembly.value
        ) ||
        this.assemblyData["basic"].find(
          (p) => p.code === this.dragenterAssembly.value
        );
      oldNode && (oldNode.color = "");
      newNode && (newNode.color = "blue");

      let nodecur =
        this.mappingData["basic"].find(
          (p) => p.name === this.dragenterAssembly.name
        ) ||
        this.mappingData["extend"].find(
          (p) => p.name === this.dragenterAssembly.name
        );
      nodecur.value = newNode.name;
    },
    vv(e) {
      e.dataTransfer.effectAllowed = "move";
      e.dataTransfer.dropEffect = "move";
    },

    onLeftTreeSelect(code, oid) {
      this.leftPartState.treeState.selected.oid = oid
      this.leftPartState.treeState.selected.code = code

      getAssemblyData({
        modelCode: code,
      })
        .then((data) => {
          debugger
        })

    },
    onRightTreeChange(value, labels, extra) {
      this.rightPartState.treeState.selected.oid = value
      this.leftPartState.treeState.selected.code = labels[0]
    },
    init() {

      getModelData()
        .then((res) => {
          this.modelData = res;

          // 提取分类
          let cad = res.find((p) => p.code === "MCAD");
          let part = res.find((p) => p.code === "Part");
          if (this.mode === 'cadToPart') {
            this.leftPartState.treeState.tree = [cad]
            this.rightPartState.treeState.tree = [part]
          } else {
            this.leftPartState.treeState.tree = [part]
            this.rightPartState.treeState.tree = [cad]
          }

        })
        .catch((err) => { })
        .finally(() => { });
      this.getMappingStatus();
    },
    getMappingStatus() {
      getMappingStatus()
        .then((res) => {
          this.mappingStatus = res;
        })
        .catch((err) => { })
        .finally(() => { });
    },
    getAssemblyData(oid, code, type) {
      if (type === "cad") {
        this.curTree = { oid, code };
      } else {
        if (!this.partCode) {
          this.partCode = oid;
        }
      }
      getAssemblyData({
        modelCode: code,
      })
        .then((res) => {
          if (type === "cad") {
            this.assemblyData = {
              basic: res
                .filter((p) => p.systemDefault)
                .map((p) => {
                  return {
                    color: "",
                    ...p,
                  };
                }),
              extend: res
                .filter((p) => !p.systemDefault)
                .map((p) => {
                  return {
                    color: "",
                    ...p,
                  };
                }),
            };
          } else {
            let data = this.showMapping;
            this.mappingData = {
              basic: [],
              extend: [],
            };
            let basic = res
              .filter((p) => p.systemDefault)
              .map((p) => {
                let row = this.showMapping.find(
                  (r) => r.partAttrName === p.code
                );
                let hasVal = data && row && this.initPartCode === this.partCode;
                return {
                  name: p.code,
                  partAttrType: "basic",
                  value: hasVal ? row.cadAttrName : "",
                };
              });
            let extend = res
              .filter((p) => !p.systemDefault)
              .map((p) => {
                let row = this.showMapping.find(
                  (r) => r.partAttrName === p.code
                );
                let hasVal = data && row && this.initPartCode === this.partCode;
                return {
                  name: p.code,
                  partAttrType: "extension",
                  value: hasVal ? row.cadAttrName : "",
                };
              });
            this.mappingData = {
              basic,
              extend,
            };
            if (data?.length && this.initPartCode === this.partCode) {
              data.forEach((name) => {
                let basicNode = this.assemblyData.basic.find(
                  (p) => p.code === name.cadAttrName
                );
                basicNode && (basicNode.color = "blue");
                let extendNode = this.assemblyData.extend.find(
                  (p) => p.code === name.cadAttrName
                );
                extendNode && (extendNode.color = "blue");
              });
            } else {
              this.assemblyData.basic.forEach((p) => (p.color = ""));
              this.assemblyData.extend.forEach((p) => (p.color = ""));
            }
          }
        })
        .catch((err) => { })
        .finally(() => {
          if (type === "cad") {
            this.getMappingData(true);
          }
        });
    },
    getMappingData(bool) {
      let { oid, code } = this.curTree;
      this.showMapping = [];
      getMappingData({
        cadTypeOid: oid,
        cadType: code,
      })
        .then((res) => {
          this.saveMappingOid = res?.oid;
          if (bool) {
            this.initPartCode = this.partCode =
              res?.partTypeOid || this.PARTdata.at(0)?.oid;
          }
          this.showMapping = res?.mapping || [];
          // this.mappingData = this.foramtMappingData(res?.mapping || {});
        })
        .catch((err) => {
          this.$error(err.msg || error.message);
        })
        .finally(() => {
          let { oid, code } = this.PARTdata.find(
            (p) => p.oid === this.partCode
          );
          this.getAssemblyData(oid, code, "part");
        });
    },
    foramtMappingData(data) {
      let formatData = {
        basic: [],
        extend: [],
      };
      for (var key in data) {
        let has = this.assemblyData.basic.some((p) => p.name === data[key]);
        if (has) {
          let node = this.assemblyData.basic.find((p) => p.name === data[key]);
          node.color = "blue";
          formatData.basic.push({
            name: key,
            value: data[key],
          });
        } else {
          let node = this.assemblyData.extend.find((p) => p.name === data[key]);
          node.color = "blue";
          formatData.extend.push({
            name: key,
            value: data[key],
          });
        }
      }
      return formatData;
    },
    saveMapping(callBack) {
      this.saveLoading = true;
      let { oid: cadTypeOid, code: cadType } = this.curTree;
      let { oid: partTypeOid, code: partType } = this.PARTdata.find(
        (p) => p.oid === this.partCode
      );
      let mapping = [...this.mappingData.basic, ...this.mappingData.extend]
        .filter((p) => p.value)
        .map((element) => {
          let cadAttrType = this.assemblyData.basic.find(
            (p) => p.code === element.value
          );
          return {
            cadAttrName: element.value,
            cadAttrType: cadAttrType ? "basic" : "extension",
            partAttrName: element.name,
            partAttrType: element.partAttrType,
          };
        });
      let params = {
        cadType,
        cadTypeOid,
        partType,
        partTypeOid,
        mapping,
        oid: this.saveMappingOid,
      };
      saveMapping(params)
        .then((res) => {
          this.$success("保存成功");
          this.initPartCode = partTypeOid;
        })
        .catch((err) => {
          this.$error(err.msg || error.message);
        })
        .finally(() => {
          this.saveButton = false;
          this.saveLoading = false;
          if (typeof callBack === "function") {
            callBack();
          }
          this.getMappingStatus();
        });
    },
  },
  created() {
    this.init();
    this.setBreadcrumb([{ name: this.$t("msg_mcad_rules") }]);
  },
  beforeRouteLeave(to, from, next) {
    let that = this;
    if (this.saveButton) {
      let modal = Modal.confirm({
        content: this.$t("msg_from_save"),
        onOk() {
          that.saveMapping(() => {
            modal.destroy();
            next();
          });
        },
        cancelText: this.$t("btn_cancel"),
        okText: this.$t("btn_save"),
        onCancel() {
          that.saveButton = false;
          modal.destroy();
          next();
        },
      });
    } else {
      next();
    }
  },
};
</script>
<style lang="less">
.mcad-attribute-mapping {
  height: 100%;
  border-top: 1px solid #eee;
  background-color: #fff;

  .custom-resizer {
    height: 100%;
  }

  .multipane-resizer {
    z-index: 1;
    margin: 0;
    left: 0;
    position: relative;
    background: #eee;

    // margin-left: 0px !important;
    &:before {
      display: block;
      content: "";
      width: 3px;
      height: 40px;
      position: absolute;
      top: 50%;
      left: 50%;
      margin-top: -20px;
      margin-left: -1.5px;
      border-left: 1px solid #ccc;
      border-right: 1px solid #ccc;
    }

    &:hover {
      &:before {
        border-color: #999;
      }
    }
  }

  .tree-model {
    width: 20%;
    padding: 15px;
    margin-right: 5px;
    overflow: auto;

    .node {
      height: 50px;
      background-color: #1e202a0a;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
      cursor: pointer;

      &.tag>span i {
        display: block;
      }

      >span {
        white-space: nowrap;
        width: 20px;
        flex-grow: 1;
        display: flex;
        margin-right: 20px;

        i {
          color: #2dcb81ff;
          font-size: 16px;
          display: none;
        }

        span {
          overflow: hidden;
          margin-right: 15px;
          display: inline-block;
          text-overflow: ellipsis;
        }
      }

      i {
        font-size: 16px;
      }

      &:not(:last-child) {
        margin-bottom: 10px;
      }

      &:hover,
      &.select {
        background-color: #f0f7ff;
      }
    }
  }

  .assembly-mapping {
    flex-grow: 1;
    width: 20px;
    height: 100%;

    .assembly {
      padding: 15px;
      width: 40%;

      >header {
        font-weight: 700;
        font-size: 18px;
        color: rgba(30, 32, 42, 0.85);
        line-height: 30px;
        height: 30px;
        margin-bottom: 15px;

        >span {
          padding-left: 15px;
        }

        &:before {
          position: absolute;
          content: "*";
          color: #fff;
          // left: 20px;
          height: 30px;
          // top: 9px;
          border-left: 4px solid #255ed7;
        }
      }

      >main {
        user-select: none;

        label {
          font-weight: 400;
          font-size: 12px;
          color: rgba(30, 32, 42, 0.45);
          line-height: 20px;
          margin-bottom: 10px;
          display: block;
        }

        .ant-tag {
          margin-bottom: 10px;
          cursor: pointer !important;

          &:focus {
            cursor: pointer;
          }
        }
      }
    }

    .mapping {
      flex-grow: 1;
      width: 20px;
      padding: 15px;
      display: flex;
      flex-direction: column;

      >header {
        line-height: 30px;
        height: 30px;
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;

        >.header-left {
          >span:first-child {
            font-weight: 700;
            font-size: 18px;
            color: rgba(30, 32, 42, 0.85);
            padding-left: 15px;
          }
        }

        &:before {
          position: absolute;
          content: "*";
          color: #fff;
          // left: 20px;
          height: 30px;
          // top: 9px;
          border-left: 4px solid #255ed7;
        }
      }

      main {
        height: 20px;
        flex-grow: 1;
        overflow: auto;

        >div {
          >label {
            font-weight: 400;
            font-size: 12px;
            color: rgba(30, 32, 42, 0.45);
            line-height: 20px;
            margin: 10px 0;
            display: block;
          }

          >div {
            display: flex;
            flex-wrap: wrap;

            .assembly-node {
              width: 45%;

              &:not(:last-child) {
                flex-grow: 1;
              }

              &:last-child {
                width: calc(~"50% - 8px");
              }

              &:nth-child(odd) {
                margin-right: 16px;
              }

              >p {
                margin: 10px 0;
                font-size: 16px;
              }

              >div {
                height: 32px;
                line-height: 29px;
                border: 1px solid #eee;
                border-radius: 4px;
                padding-left: 8px;
              }
            }
          }
        }
      }
    }
  }
}
</style>