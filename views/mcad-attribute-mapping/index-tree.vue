
<template>
  <a-tabs v-model:tabActiveKey="tabActiveKey">
    <a-tab-pane key="1" tab="Tab 1">
      <attribut-map></attribut-map>

    </a-tab-pane>
    <a-tab-pane key="2" tab="Tab 2" force-render>Content of Tab Pane 2</a-tab-pane>
    <a-tab-pane key="3" tab="Tab 3">Content of Tab Pane 3</a-tab-pane>
  </a-tabs>
</template>
<script>
import {
  getModelData,
  getAssemblyData,
  getMappingData,
  saveMapping,
  getMappingStatus,
} from "./api";
import AttributMap from "./components/attribut-map.vue";
import { Modal } from "ant-design-vue";
export default {
  name: "mcad-attribute-mapping",
  inject: ["setBreadcrumb", "addBreadcrumb"],

  components: {
    AttributMap,
  },
  data() {
    return {
      tabActiveKey: '1',

      curTree: {},
      modelData: [],
      assemblyData: {
        basic: [],
        extend: [],
      },
      partCode: undefined,
      mappingData: {
        basic: [],
        extend: [],
      },
      dragenterAssembly: undefined,
      time: 0,
      saveButton: false,
      saveLoading: false,
      saveMappingOid: undefined,
      mappingStatus: [],
      initPartCode: undefined,
      initMappingData: {},
      showMapping: [],
    };
  },
  computed: {
    
  },
  methods: {
    
  },
  created() {
    // this.init();
    this.setBreadcrumb([{ name: this.$t("msg_mcad_rules") }]);
  },
};
</script>
<style lang="less">

</style>