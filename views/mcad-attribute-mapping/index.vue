<template>
  <div class="main-page-area">
    <a-tabs default-active-key="1">
      <a-tab-pane key="1" :tab="$t('msg_mcad_part_rules')">
        <div class="changearea">
          <cadpartchange :cadtopart="true" />
        </div>
      </a-tab-pane>
      <a-tab-pane key="2" :tab="$t('msg_part_mcad_rules')">
        <div class="changearea">
          <cadpartchange :cadtopart="false" />
        </div>
      </a-tab-pane>
      <a-tab-pane key="3" :tab="$t('tool_prop_mapping')">
        <div class="changearea">
          <cadpartchange :cadtopart="true" :tooltable="true"/>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import Cadpartchange from "./cadpartchange.vue";
export default {
  data() {
    return {};
  },
  components: {
    Cadpartchange,
  },
  inject: ["setBreadcrumb", "addBreadcrumb"],
  created() {
    this.setBreadcrumb([{ name: this.$t("msg_mcad_rules") }]);
  },
  methods: {},
};
</script>

<style lang="less" scoped>
.main-page-area {
  background-color: #fff;
}

.changearea {
  height: calc(~"100vh - 135px");
}
</style>