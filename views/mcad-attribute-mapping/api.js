/*
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-03-25 10:01:30
 * @LastEditTime: 2022-03-25 10:01:30
 * @LastEditors: <EMAIL>
 */

// import Vue from 'vue'
import { param } from "jquery"
import ModelFactory from "jw_apis/model-factory"

/**
 * 获取模型数据
 * @param {*} params 
 * @returns 
 */
const getModelData = function () {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.foundationServer}/model/searchTree`,
        method: "get",
    }).execute({
        searchKey:""
    })
}
/**
 * 获取模型属性
 * @param {*} params 
 * @returns 
 */
 const getAssemblyData = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.foundationServer}/model/property/findByModel/withConstraint`,
        method: "get",
    }).execute(params)
}

/**
 * 获取模型映射关系
 * @param {*} params 
 * @returns 
 */
 const getMappingData = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.cadService}/mcad/query/attributeMapping`,
        method: "get",
    }).execute(params)
}
/**
 * 保存模型
 * @param {*} params 
 * @returns 
 */
 const saveMapping  = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.cadService}/mcad/save/attributeMapping`,
        method: "post",
    }).execute(params)
}
const getMappingStatus  = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.cadService}/mcad/query/allAttributeMapping`,
        method: "get",
    }).execute(params)
}

//获取part转cad映射关系
export const getPartToCadMapper = function (param){
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.cadService}/mcad/query/partToCad/attributeMapping`,
        method: 'get'
    }).execute(param)
}

export {
    getModelData,
    getAssemblyData,
    getMappingData,
    saveMapping,
    getMappingStatus
}