<template>
	<div class="base-panel page-container">
		<filter-compoment
			ref="filterCompoment"
			:statisData="statisData"
			:filterSelect="filterSelect"
			@setQueryDate="setQueryDate"
		/>
		<statis-grid :statisData="statisData" />
		<chart-statis :chartData="chartData" :filterSelect="filterSelect" />
		<my-problem ref="myProblem" />
	</div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { jwAvatar, jwIcon, jwModalForm } from "jw_frame";
import filterCompoment from "./filter-compoment";
import statisGrid from "./statis-grid";
import chartStatis from "./chart-statis";
import myProblem from "./my-problem";
import moment from "moment";

// 图表数据统计
const fetchPreviewData = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.changeServer}/pr/issue/searchByData`,
	method: "post",
});

// 问题管理统计维度
const fetchFilterSelect = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.sysconfigServer}/preferences/setting/query-config-value`,
	method: "get",
});

export default {
	name: "overall-Preview",
	components: {
		jwAvatar,
		jwIcon,
		jwModalForm,
		filterCompoment,
		statisGrid,
		chartStatis,
		myProblem,
	},
	data() {
		return {
			startDate: null,
			endDate: null,
			defaultStartDate: "",
			defaultEndDate: new Date(),
			statisData: {},
			chartData: {},
			filterSelect: [],
			figureList: ["priority", "issueType"],
		};
	},
	computed: {},
	created() {},
	mounted() {},
	methods: {
		moment,
		init() {
			this.initDefaultStartDate();
			this.fetchFilterSelect();
			this.fetchPreviewData();
			this.$refs.myProblem && this.$refs.myProblem.reFetchData();
		},
		selectOptionFilter(filterSelect) {
			let value = ["priority", "issueType"];
			let valueList = filterSelect.map((item) => item.value);
			let disList = [];
			valueList.forEach((item) => {
				if (!value.includes(item)) {
					disList.push(item);
				}
			});
			if (value.length === 2) {
				for (let i in filterSelect) {
					for (let j in disList) {
						if (filterSelect[i].value === disList[j]) {
							filterSelect[i].disabled = true;
						}
					}
				}
			} else {
				for (let i in filterSelect) {
					filterSelect[i].disabled = false;
				}
			}
			console.log(filterSelect);
			return filterSelect;
		},
		fetchFilterSelect() {
			let params = {
				name: "PR_Statistics_Dimension",
			};
			fetchFilterSelect
				.execute(params)
				.then((data) => {
					console.log("问题管理统计维度---------------", data);
					this.filterSelect = this.selectOptionFilter(data);
				})
				.catch((err) => {
					this.$error(err.msg || this.$t("msg_failed"));
				});
		},
		// 图表数据
		fetchPreviewData() {
			let { figureList, startDate, endDate } = this;
			let dataParams = [].concat(["priority"], figureList);
			let params = {
				figureList: Array.from(new Set(dataParams)),
				startDate: startDate,
				endDate: endDate,
			};
			fetchPreviewData
				.execute(params)
				.then((data) => {
					console.log("图表数据---------------", data);
					this.chartData = { ...data }; // 图表数据
					this.statisData = { ...data }; // 栅格数据
				})
				.catch((err) => {
					console.error(err)
					// this.$error(err.msg || this.$t("msg_failed"));
				});
		},
		initDefaultStartDate() {
			let date = new Date(),
				y = date.getFullYear(),
				m = date.getMonth();
			let defaultStartDate = new Date(y, m, 1);
			let defaultEndDate = new Date(
				new Date(new Date().toLocaleDateString()).getTime() +
					24 * 60 * 60 * 1000 -
					1
			);
			this.startDate = Date.parse(defaultStartDate);
			this.endDate = Date.parse(defaultEndDate);
		},
		setQueryDate(figureList) {
			let { filterCompoment } = this.$refs;
			let { dateValue, defaultValue } = filterCompoment;
			if (dateValue) {
				this.startDate = dateValue[0].format("x");
				this.endDate = dateValue[1].format("x");
			} else {
				this.startDate = defaultValue[0].format("x");
				this.endDate = defaultValue[1].format("x");
			}
			this.figureList = figureList;
			this.fetchPreviewData();
		},
	},
};
</script>

<style lang="less" scoped>
.page-container {
	width: 100%;
	padding: 0 24px;
	.product-config {
		display: flex;
		justify-content: flex-start;
		flex-wrap: nowrap;
		height: calc(~"100% - 53px");
	}
}
</style>
