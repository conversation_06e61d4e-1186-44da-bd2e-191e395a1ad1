<template>
	<div style="margin-top: 17px; height: 48px">
		<a-row
			:gutter="24"
			style="
				margin-left: 0;
				margin-right: 0;
				display: flex;
				align-items: flex-start;
				justify-content: flex-start;
			"
		>
			<a-col class="gutter-row">
				<span class="title">{{ $t('txt_problem_my_create') }}</span>
				<span class="count">{{ countData[`我创建的`] || 0 }}</span>
			</a-col>
			<a-col class="gutter-row">
				<span class="title">{{ $t('txt_problem_close') }}</span>
				<span class="count">{{ countData[`已关闭`] || 0 }}</span>
			</a-col>
			<a-col class="gutter-row">
				<span class="title"> {{ $t('txt_has_cancel') }}</span>
				<span class="count">{{ countData[`已取消`] || 0 }}</span>
			</a-col>
			<a-col class="gutter-row">
				<span class="title"> {{ $t('txt_problem_accepted') }}</span>
				<span class="count">{{ countData[`已接受`] || 0 }}</span>
			</a-col>
		</a-row>
	</div>
</template>
<script>
import ModelFactory from "jw_apis/model-factory";
import { jwAvatar, jwIcon, jwModalForm } from "jw_frame";

export default {
	name: "overall-Preview",
	components: {
		jwAvatar,
		jwIcon,
		jwModalForm,
	},
	props: ["statisData"],
	data() {
		return {
			countData: {},
			titleList: [],
		};
	},
	computed: {},
	watch: {
		statisData(val) {
			this.countData = { ...val };
			this.filterTitle(val);
		},
	},
	created() {},
	mounted() {},
	methods: {
		filterTitle(statisData) {
			let titleList = [];
			for (let key in statisData) {
				if (!Array.isArray(statisData[key])) {
					titleList.push(key);
				}
			}
			this.titleList = titleList;
		},
	},
};
</script>
<style lang="less" scoped>
.gutter-row {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 48px;
	margin-right: 16px;
	background: rgba(30, 32, 42, 0.04);
	border-radius: 4px;
	font-weight: 500;
	cursor: pointer;
	&:last-of-type {
		margin-right: 0;
	}
	.title {
		font-size: 14px;
		color: rgba(30, 32, 42, 0.65);
	}
	.count {
		font-size: 20px;
		color: rgba(30, 32, 42, 0.85);
	}
}
</style>