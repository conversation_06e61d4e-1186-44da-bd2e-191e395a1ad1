<template>
	<div class="chart-content">
		<div class="chart-container" style="margin-right: 18px">
			<div v-if="distriList.length > 0">
				<p class="chart-title">{{ $t('txt_problem_urgency') }}</p>
				<div class="chart-priview" style="height: 260px">
					<div class="pend-class" ref="main" id="main"></div>
					<ul class="count-number">
						<li
							class="count-item"
							v-for="(item, key) in distriList"
							:key="key"
							:style="{
								backgroundColor: distriColor[key],
							}"
						>
							<span class="title">
								{{ item.name }}
							</span>
							<span class="number">{{ item.value }}</span>
						</li>
					</ul>
				</div>
			</div>
			<div v-else class="no-data">{{ $t('msg_nodata') }}</div>
		</div>
		<div class="chart-container">
			<div v-if="barData.length > 0">
				<p class="chart-title">{{ `${currentBarTitle}` }} {{$t('txt_problem_urgency_name')}}</p>
				<div id="barBox" ref="barBox" style="height: 260px"></div>
			</div>
			<div v-if="barData.length == 0" class="no-data">{{ $t('msg_nodata') }}</div>
		</div>
	</div>
</template>

<script>
import * as echarts from "echarts";
export default {
	name: "chart-statis",
	components: {},
	props: ["chartData", "filterSelect"],
	data() {
		return {
			dataInfo: {},
			urgentList: [],
			distriList: [],
			distriColor: ["#FFC2C3", "#FFE4BD", "#AAF2C9", "rgba(75, 128, 227, .3)"],
			barLabel: [],
			barData: [],
			currentBarTitle: "",
			pieCount: 0,
		};
	},
	computed: {},
	watch: {
		chartData(val) {
			console.log(val);
			this.filterData(val);
		},
	},
	created() {},
	mounted() {
		console.log(`dataInfo--------------------`, this.dataInfo);
	},
	methods: {
		filterData(data) {
			let urgentList = [];
			let distriList = [];
			let barLabel = [];
			let barData = [];
			let pieCount = 0;
			let { filterSelect } = this;
			console.log(`filterSelect`, filterSelect);
			for (let key in data) {
				let filterObj = filterSelect.filter(
					(item) => item.name == key && item.disabled != true
				)[0];
				console.log(`filterObj`, filterObj);
				console.log(`key`, key);
				if (key != "紧急程度" && filterObj && filterObj.name == key) {
					console.log(`key`, key);
					this.currentBarTitle = key;
					for (let childKey in data[key][0]) {
						barLabel.push(childKey);
						barData.push(data[key][0][childKey]);
						urgentList.push({ name: childKey, value: data[key][0][childKey] });
					}
				}
				if (key == "紧急程度") {
					console.log(data[key][0]);
					for (let childKey in data[key][0]) {
						pieCount += data[key][0][childKey];
						distriList.push({ name: childKey, value: data[key][0][childKey] });
					}
				}
			}
			console.log("distriList", distriList);
			this.distriList = distriList || [];
			this.urgentList = urgentList || [];
			this.barLabel = barLabel || [];
			this.barData = barData || [];
			this.pieCount = pieCount;
			this.$nextTick(() => {
				this.initEcharts();
				this.initBar();
			});
		},
		initEcharts() {
			// 问题优先级分布
			let { distriList, distriColor, pieCount } = this;
			const option = {
				title: {
					text: `${pieCount}\n${this.$t('txt_problem_total')}`,
					left: "center",
					top: "center",
				},
				series: [
					{
						type: "pie",
						data: distriList,
						center: ["50%", "50%"],
						radius: ["60%", "80%"],
						label: {
							normal: {
								formatter: "{b}:{c}",
								overflow: "none",
							},
						},
						labelLine: {
							normal: {
								show: true,
							},
						},
					},
				],
				color: ["#FF707C", "#FFBA6B", "#69C833", "rgba(75, 128, 227, .3)"],
			};
			if (this.$refs.main) {
				var myChart = echarts.init(this.$refs.main); //图标初始化
				myChart.setOption(option); // 渲染页面
				window.onresize = function () {
					myChart.resize();
				};
			}
		},
		initBar() {
			// 问题分布
			let { urgentList, barLabel, barData } = this;
			const returnTitle = (value) => (
				<a-tooltip placement="bottom">
					<template slot="title">
						<span>{value.length > 3 ? value.slice(0, 3) + "..." : value}</span>
					</template>
					{value.length > 3 ? value.slice(0, 3) + "..." : value}
				</a-tooltip>
			);
			let _end = 0;
			if (barLabel.length === 1) {
				_end = 0;
			} else if (barLabel.length > 7) {
				_end = 6;
			} else {
				_end = 100;
			}
			//  获取数据
			const option = {
				xAxis: {
					data: barLabel,
					axisLabel: {
						interval: 0, //横轴信息全部显示
						margin: 5, //刻度标签与轴线之间的距离
						formatter: function (value) {
							//超出部分隐藏显示
							return value.length > 3 ? value.slice(0, 4) + "..." : value;
						},
						// textStyle: {
						// 	fontSize: 9, //横轴字体大小
						// 	color: "#000000", //颜色
						// },
					},
				},
				axisTick: {
					alignWithLabel: true,
				},
				yAxis: { minInterval: 1 },
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'shadow',
					}
				},
				dataZoom: [
					{
						show: barLabel.length > 10,
						minInterval: 1,
						atartValue: 0,
						endValue: 0,
						minValueSpan: _end,
						maxValueSpan: _end,
						height: 10,
						zoomLock: true,
						moveHandleStyle: {
							opacity: 0,
						}
					},
				],
				series: [
					{
						type: "bar",
						data: barData,
						barWidth: "20%",
						barMaxWidth: "20%",
						itemStyle: {
							normal: {
								label: {
									show: true, //开启显示数值
									position: "top", //数值在上方显示
									textStyle: {
										//数值样式
										color: "#333", //字体颜色
										fontSize: 16, //字体大小
									},
								},
							},
						},
						labelLine: {
							normal: {
								show: true,
							},
						},
					},
				],
			};
			if (this.$refs.barBox) {
				var myChart = echarts.init(this.$refs.barBox); //图标初始化
				myChart.setOption(option); // 渲染页面
				window.onresize = function () {
					myChart.resize();
				};
			}
		},
	},
};
</script>

<style lang="less" scoped>
.no-data {
	display: flex;
	align-items: center;
	justify-content: center;
	color: rgb(46, 199, 201);
	height: 300px;
}
.page-container {
	margin-top: 16px;
	width: 100%;
	padding: 0;
}
.chart-content {
	display: flex;
	align-items: flex-start;
	justify-content: flex-start;
}
.chart-container {
	margin-top: 16px;
	flex: 1;
	background: rgba(30, 32, 42, 0.04);
	.chart-title {
		padding: 0 16px;
		height: 40px;
		line-height: 40px;
		font-weight: 500;
		font-size: 14px;
		color: rgba(30, 32, 42, 0.85);
	}
}
.chart-priview {
	flex: 1;
	display: flex;
	align-content: flex-start;
	justify-content: flex-start;
	.pend-class {
		width: 65%;
	}
	.count-number {
		height: 260px;
		padding: 0 16px;
		width: 35%;
		display: flex;
		align-items: flex-start;
		flex-wrap: wrap;
		justify-content: flex-start;
		overflow-y: auto;
		.count-item {
			margin-bottom: 12px;
			width: 100%;
			height: 50px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 16px;
			background: #fff0f0;
			// border: 1px solid #ffc2c3;
			border-radius: 4px;
			.title {
				font-weight: 400;
				font-size: 14px;
				color: rgba(30, 32, 42, 0.45);
			}
			.number {
				font-weight: 500;
				font-size: 22px;
				color: rgba(30, 32, 42, 0.85);
			}
		}
	}
}
.polyline {
	fill: none;
	stroke: #000000;
	stroke-width: 2px;
	stroke-dasharray: 5px;
}
.no-distriData {
	width: 100%;
	color: rgb(46, 199, 201);
	line-height: 200px;
	text-align: center;
}
.no-urgentData {
	width: 100%;
	height: 298px;
	color: rgb(75, 128, 227);
	line-height: 200px;
	text-align: center;
}
</style>
