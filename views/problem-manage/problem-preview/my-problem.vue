<template>
	<div>
		<!-- <a-divider style="margin:16px 0 0 0" /> -->
		<div class="problem-filter">
			<span class="title">{{ $t('txt_problem_my') }}</span>
			<a-select
				v-model.trim="issueType"
				style="min-width: 102px"
				@change="handleChange"
			>
				<a-select-option value="creator"> {{ $t('txt_problem_my_set') }} </a-select-option>
				<a-select-option value="owner"> {{ $t('txt_problem_my_responsible') }} </a-select-option>
				<a-select-option value="creatorOrOwner"> {{ $t('txt_problem_my') }} </a-select-option>
			</a-select>
		</div>
		<div class="problem-list">
			<jw-table
				ref="table"
				height="335px"
				:columns="columns"
				:data-source.sync="tableData"
				:pagerConfig="pagerConfig"
				@onOperateClick="onOperateClick"
				@onPageChange="onPageChange"
				@onSizeChange="onSizeChange"
			>
				<template #number="{ row }">
					<div class="name-wrap">
						<div class="name-con" @click="handleProblemDetail(row)">
							<jwIcon type="#jwi-linwu"></jwIcon>
							<span class="name-item">{{ row.name }}</span>
						</div>
					</div>
				</template>
				<template #problemStatus="{ row }">
					<div class="name-wrap">
						<a-tag>{{ $t(row.lifecycleStatus) }}</a-tag>
					</div>
				</template>
				<template #proposedBySlot="{ row }">
					<div class="name-wrap">
						<jw-avatar
							v-if="row.proposedBy"
							tag
							show-name
							:data="row.proposedBy"
						/>
					</div>
				</template>
				<template #personLiableSlot="{ row }">
					<div class="name-wrap">
						<jw-avatar
							v-if="row.personLiable"
							tag
							show-name
							:data="row.personLiable"
						/>
					</div>
				</template>
				<template #createDate="{ row }">
					{{ row.records[0].createDate }}
				</template>
				<template slot="tool-before-end">
					<span class="name-wrap"> {{ $t('txt_problem_have_count') + pagerConfig.total + $t('txt_problem_have_count_end') }} </span>
				</template>
			</jw-table>
		</div>
	</div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { jwAvatar, jwIcon, jwModalForm } from "jw_frame";
import { setOwner } from "apis/baseapi";
// 我的问题
const fetchIssueList = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.changeServer}/pr/issue/searchByCurrentUser`,
	method: "post",
});
export default {
	name: "my-problem",
	components: { jwAvatar, jwIcon, jwModalForm },
	data() {
		return {
			tableData: [],
			issueType: "creatorOrOwner",
			//分页配置
			pagerConfig: {
				current: 1,
				pageSize: 20,
				total: 0,
			},
		};
	},
	computed: {
		columns() {
			return [
				{
					field: "name",
					title: this.$t('txt_problem_feild_name'),
					minWidth: "150px",
					slots: {
						default: "number",
					},
				},
				{
					field: "number",
					title: this.$t('txt_problem_feild_number'),
				},
				{
					field: "priority",
					title: this.$t('change_emergency'),
				},
				{
					field: "containerName",
					title: this.$t('tabel_context'),
				},
				{
					field: "issueType",
					title: this.$t('txt_problem_type'),
				},
				{
					field: "proposedBy",
					title: this.$t('txt_problem_feild_pressent'),
					slots: {
						default: "proposedBySlot",
					},
				},
				{
					field: "personLiable",
					title: this.$t('txt_dead_user'),
					slots: {
						default: "personLiableSlot",
					},
				},
				{
					field: "proposeDate",
					title: this.$t('txt_preblem_feild_press_time'),
					// formatter: "date",
				},
				{
					field: "closeDate",
					title: this.$t('txt_problem_feild_close_time'),
					formatter: "date",
				},
				{
					field: "lifecycleStatus",
					title: this.$t('txt_status'),
					slots: {
						default: "problemStatus",
					},
				},
			];
		},
		toolbars() {
			return [
				{
					name: this.$t("btn_new_create"),
					key: "add",
					type: "primary",
					click: this.jumbTo,
				},
				{
					display: "input",
					key: "search",
					value: this.searchKey,
					input: this.onSearchInput,
				},
			];
		},
	},
	created() {},
	mounted() {},
	methods: {
		//分页操作
		onPageChange(page, pageSize) {
			this.pagerConfig.current = page;
			this.pagerConfig.pageSize = pageSize;
			this.reFetchData();
		},
		onSizeChange(pageSize, page) {
			this.pagerConfig.current = page;
			this.pagerConfig.pageSize = pageSize;
			this.reFetchData();
		},
		handleProblemDetail(row) {
			this.$router.push({
				name: `problem-detail`,
				path: `/problem-detail`,
				query: {
					oid: row.oid,
					tabActive: "prev",
				},
			});
			localStorage.setItem("issueTab", "1");
		},
		reFetchData() {
			this.fetchTable();
		},
		fetchTable() {
			let { searchKey, issueType } = this;
			let {
				pagerConfig: { current, pageSize },
			} = this;
			let params = {
				searchKey: searchKey,
				index: current,
				size: pageSize,
				type: issueType,
			};
			return fetchIssueList
				.execute(params)
				.then((data) => {
					console.log("我的问题", data);
					this.tableData = data.rows;
					this.pagerConfig.total = data.count;
					return { data: data.rows || data, total: data.count };
				})
				.catch((err) => {
					console.log(err);
					this.$error(err.msg || this.$t("msg_failed"));
				});
		},
		handleChange(value) {
			console.log(`selected ${value}`);
			this.issueType = value;
			this.reFetchData();
		},
		onOperateClick(item, row) {
			let key = item.code || item;
			if (key === "details") {
				this.onOpenECRDetail(row);
			} else if (key === "startProcess") {
				startECRWorkflow
					.execute({
						ecrOid: row.oid,
					})
					.then((res) => {
						this.currentRecord = row;
						this.processVisible = true;
					})
					.catch((err) => {
						this.$error(err.msg);
					});
			} else if (key === "updateOwner") {
				this.$refs["user-modal"]
					.show({
						type: "User",
					})
					.then((res) => {
						setOwner
							.execute({
								oid: row.oid,
								type: row.type,
								ownerAccount: res.account,
							})
							.then((res) => {
								this.$success(this.$t("msg_save_success"));
								this.reFetch();
							})
							.catch((err) => {
								this.$error(err.msg);
							});
					});
			} else if (key === "delete") {
				this.onDelete(row);
			}
		},
	},
};
</script>

<style lang="less" scoped>
.name-con {
	color: #255ed7;
	cursor: pointer;
}
.problem-filter {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 60px;
	.title {
		font-weight: 500;
		font-size: 14px;
		color: rgba(30, 32, 42, 0.85);
	}
}
</style>
