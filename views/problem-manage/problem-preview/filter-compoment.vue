<template>
	<div class="filter-contianer">
		<a-select
			default-value="lucy"
			style="min-width: 102px; visibility: hidden"
			@change="handleChange"
		>
			<a-select-option value="jack"> {{$t('txt_problem_by_day')}} </a-select-option>
			<a-select-option value="lucy"> {{$t('txt_problem_by_week')}} </a-select-option>
			<a-select-option value="Yiminghe"> {{ $t('txt_problem_by_month') }} </a-select-option>
		</a-select>
		<div class="select-date">
			<a-button class="date-btn" @click="preDate">
				<jw-icon type="jwi-iconchevron-left" />
			</a-button>
			<a-range-picker
				class="select-date-picker"
				v-model.trim="dateValue"
				:allowClear="false"
				:separator="'-'"
				:default-value="defaultValue"
				:format="dateFormat"
				@change="dateChange"
			/>
			<a-button style="margin-right:8px;" class="date-btn" @click="nextDate">
				<jw-icon type="jwi-iconchevron-right" />
			</a-button>
			<a-button @click="backCurrentMonth">{{ $t('txt_problem_return_month') }}</a-button>
		</div>
		<div class="filter-select" id="filterSelect" style="float: right">
			<a-select
				style="min-width: 180px"
				:placeholder="$t('txt_problem_statistics')"
				mode="multiple"
				v-model.trim="figureList"
				:defaultValue="['priority', 'issueType']"
				:getPopupContainer="getContainer"
				@change="handleChange"
			>
				<a-select-option
					v-for="item in filterSelect"
					:disabled="item.disabled || item.value == 'priority'"
					:key="item.value"
				>
					{{ item.name }}
				</a-select-option>
			</a-select>
		</div>
	</div>
</template>

<script>
//接口
import ModelFactory from "jw_apis/model-factory";
import { jwAvatar, jwIcon } from "jw_frame";
import locale from "ant-design-vue/es/date-picker/locale/zh_CN";
import moment from "moment";
import "moment/locale/zh-cn";
moment.locale("zh-cn");
export default {
	name: "filter-compoment",
	components: { jwAvatar, jwIcon },
	props: ["statisData", "filterSelect"],
	data() {
		return {
			defaultOption: {},
			figureList: ["priority", "issueType"],
			dateValue: null,
			defaultStartDate: "",
			defaultEndDate: new Date(
				new Date(new Date().toLocaleDateString()).getTime() +
					24 * 60 * 60 * 1000 -
					1
			),
			dateFormat: "YYYY/MM/DD",
			monthFormat: "YYYY/MM",
			dateFormatList: ["DD/MM/YYYY", "DD/MM/YY"],
			defaultValue: [],
			locale: {
				lang: {
					placeholder: this.$t('txt_select_date'),
					today: this.$t('today_text'),
					yearFormat: "YYYY" + this.$t("txt_problem_year"),
					dateFormat: "YYYY-MM-DD",
					dayFormat: "DD",
					dateTimeFormat: "YYYY-MM-DD HH:mm:ss",
				},
			},
			offsetDays: 2678400 * 1000,
		};
	},
	computed: {},
	watch: {
		filterSelect(val) {
			if (val) {
				val.map((item) => {
					if (item.value == "priority") {
						item.disabled = true;
					}
					return item;
				});
			}
		},
	},
	created() {
		this.initDefaultStartDate();
	},
	mounted() {},
	methods: {
		moment,
		getContainer() {
			return document.getElementById("filterSelect");
		},
		preDate() {
			let { dateValue, defaultValue, figureList } = this;
			let duration = moment.duration({ days: -1 });
			// 没有值得时候从默认值取
			if (dateValue) {
				this.dateValue = [
					moment(dateValue[0].add(duration), this.dateFormat),
					moment(dateValue[1].add(duration), this.dateFormat),
				];
			} else {
				this.dateValue = [
					moment(defaultValue[0].add(duration), this.dateFormat),
					moment(defaultValue[1].add(duration), this.dateFormat),
				];
			}
			console.log(this.dateValue[0].format("x"));
			console.log(this.dateValue[1].format("x"));
			this.$emit("setQueryDate", figureList);
		},
		nextDate() {
			let { dateValue, defaultValue, figureList } = this;
			let duration = moment.duration({ days: 1 });
			// 没有值得时候从默认值取
			if (dateValue) {
				this.dateValue = [
					moment(dateValue[0].add(duration), this.dateFormat),
					moment(dateValue[1].add(duration), this.dateFormat),
				];
			} else {
				this.dateValue = [
					moment(defaultValue[0].add(duration), this.dateFormat),
					moment(defaultValue[1].add(duration), this.dateFormat),
				];
			}
			this.$emit("setQueryDate", figureList);
		},
		initDefaultStartDate() {
			let date = new Date(),
				y = date.getFullYear(),
				m = date.getMonth();
			let defaultStartDate = new Date(y, m, 1);
			let defaultEndDate = new Date(
				new Date(new Date().toLocaleDateString()).getTime() +
					24 * 60 * 60 * 1000 -
					1
			);
			this.defaultStartDate = defaultStartDate;
			this.defaultEndDate = defaultEndDate;
			this.defaultValue = [
				moment(defaultStartDate, this.dateFormat),
				moment(defaultEndDate, this.dateFormat),
			];
		},
		handleChange(value, option) {
			console.log(value);
			const { filterSelect } = this;
			let valueList = filterSelect.map((item) => item.value);
			let disList = [];
			valueList.forEach((item) => {
				if (!value.includes(item)) {
					disList.push(item);
				}
			});
			if (value.length === 2) {
				for (let i in filterSelect) {
					for (let j in disList) {
						if (filterSelect[i].value === disList[j]) {
							filterSelect[i].disabled = true;
						}
					}
				}
			} else {
				for (let i in filterSelect) {
					filterSelect[i].disabled = false;
				}
			}
			this.figureList = value;
			this.$emit("setQueryDate", value);
		},
		disabledDate(dates, partial) {
			console.log(dates[0]);
			console.log(dates);
			console.log(partial);
			return dates[0] < moment(Date.now() - 1 * 24 * 3600 * 1000);
		},
		dateChange(dates, dateStrings) {
			console.log(dates);
			let { figureList } = this;
			this.dateValue = dates;
			this.$emit("setQueryDate", figureList);
		},
		backCurrentMonth() {
			let { figureList } = this;
			this.dateValue = [
				moment(this.defaultStartDate, this.dateFormat),
				moment(this.defaultEndDate, this.dateFormat),
			];
			this.$emit("setQueryDate", figureList);
		},
	},
};
</script>

<style lang="less" scoped>
.filter-contianer {
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	.filter-select {
		position: absolute;
		right: 0;
		top: 0px;
	}
}
.date-btn {
	width: 32px;
	height: 32px;
	padding: 0;
	text-align: center;
}
/deep/.ant-calendar-picker-input.ant-input {
	width: 200px;
	font-weight: 500;
	font-size: 14px;
	color: rgba(30, 32, 42, 0.85);
	border: 0;
	padding: 4px 0 4px 8px;
	box-shadow: none;
}
.select-date {
	display: flex;
	align-items: center;
}
/deep/.ant-calendar-picker:focus .ant-calendar-picker-input:not(.ant-input-disabled) {
	box-shadow: none;
}
</style>
