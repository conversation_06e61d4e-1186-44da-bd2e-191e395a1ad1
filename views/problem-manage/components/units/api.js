/*
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-03-25 10:01:30
 * @LastEditTime: 2022-03-25 10:01:30
 * @LastEditors: <EMAIL>
 */

import Vue from 'vue'


const EventBus = new Vue();
import ModelFactory from "jw_apis/model-factory";
import { getCookie } from "jw_utils/cookie";

EventBus.data = {
    name: ""
}

/**
 * 获取待办任务
 * @param {*} params 
 * @returns 
 */
const getWaitData = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/task/tasks`,
        method: "post",
    }).execute(params)
}

/**
 * 获取已完成任务
 * @param {*} params 
 * @returns 
 */
const getCompleteData = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/history/historic-task`,
        method: "post",
    }).execute(params)
}

/**
 * 任务详情
 * @param {*} params 
 * @returns 
 */
const getTaskDetails = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/history/detail`,
        method: "get",
    }).execute(params)
}


const getTaskHistory = function (taskId) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/history/task/operate/history?taskId=${taskId}`,
        method: "get",
    }).execute()
}

/**
 * 懒加载子数据
 * @param {*} oid 
 * @returns 
 */
const searchTeamUser = function (oid, params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.containerService}/team/searchTeamUser?teamRoleOid=${oid}`,
        method: "get",
    }).execute(params)
}
/**
 * 流程历史
 * @param {*} params 
 * @returns 
 */
const getTaskComment = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/history/historic-taskComment`,
        method: "post",
    }).execute(params)
}

/**
 * 获取流程概览图
 * @param {*} processInstanceId 
 * @returns 
 */
const getDiagramImg = function (params) {
    if (params.type)
        return `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/history/process-instances/${params.id}/diagram?time=${new Date().getTime()}&appName=${Jw.appName}`
    else return `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/repository/process-definitions/image/byModel?processModelId=${params.id}&appName=${Jw.appName}&tenantId=${getCookie("tenantOid")}&time=${new Date().getTime()}`
}

/**
 * 流程委派
 * @param {*} params 
 * @returns 
 */
const setDelegate = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/runtime/task/delegate`,
        method: "post",
    }).execute(params)
}

/**
 * 任务流程状态设置
 * @param {*} params 
 * @returns 
 */
const finishTask = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/task/finishTask`,
        method: "post",
    }).execute(params)
}

/**
 * 保存任务流程
 * @param {*} params 
 * @returns 
 */
const saveTask = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/task/saveTask`,
        method: "post",
    }).execute(params)
}

/**
 * 流程详情查看
 * @param {*} params 
 * @returns 
 */
const findDetail = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/findDetail`,
        method: "get",
    }).execute(params)
}
export {
    EventBus,
    getWaitData,
    getCompleteData,
    getTaskDetails,
    getTaskComment,
    getDiagramImg,
    setDelegate,
    finishTask,
    saveTask,
    findDetail,
    getTaskHistory,
    searchTeamUser
}