<template>
	<div>
		<a-dropdown
			placement="bottomRight"
			overlayClassName="operation-dropdown-overlay"
			:trigger="['click']"
			@visibleChange="(visible) => dropdownVisibleChange(visible, currentRow)"
		>
			<a-button type="primary">
				<i class="jwi-iconmenu-application" style="margin-right: 10px"></i>
				&nbsp;&nbsp;&nbsp;&nbsp;{{ $t("txt_operation") }}
				<a-icon type="down" />
			</a-button>
			<a-menu
				slot="overlay"
				@click="({ key }) => onOperateClick({ key, currentRow })"
			>
				<a-spin
					v-if="currentRow.loading_status == 'loading'"
					style="margin: 20px 65px"
				/>
				<a-menu-item
					v-else-if="currentRow.loading_status == 'failed'"
					style="margin: 20px auto"
				>
					{{ $t("txt_get_failure") }}
				</a-menu-item>
				<template v-else v-for="(item, index) in currentRow.operationList">
					<a-menu-divider v-if="item.permissionKey == 'delete'" :key="index" />
					<a-menu-item
						v-if="item.name != $t('page_model_mannange')"
						:key="item.code"
						:disabled="item.status === 'disable'"
					>
						<span :class="item.icon" style="margin-right: 8px"></span>
						{{ $t(item.internationalizationKey) }}
					</a-menu-item>
				</template>
			</a-menu>
		</a-dropdown>
		<!-- 设置状态/修订/删除 -->
		<base-color-modal
			:title="$t('txt_set_status')"
			:width="992"
			:visible.sync="operationVisible"
			dialog-class="operation-modal"
			:ok-text="$t('btn_ok') + $t('btn_set_s')"
			@ok="modalOk"
			:okBtnLoading="okBtnLoading"
			@cancel="operationCancel"
			:body-style="{ height: '545px', overflowY: 'scroll' }"
		>
			<!-- 设置状态 -->
			<div
				class="setStatus-container"
				:style="{
					width:
						statusList.length * 120 > 992 - 24 * 2
							? statusList.length * 120 + 'px'
							: '100%',
				}"
			>
				<div
					v-for="(item, index) in statusList"
					:key="item.code"
					:class="[
						'item-box',
						{
							current: item.code == currentRecord.lifecycleStatus,
							active: currentRow.new_lifecycleStatus == item.code,
						},
					]"
					@click="$set(currentRow, 'new_lifecycleStatus', item.code)"
				>
					<div class="circle">
						<div v-if="index != 0" class="line left-arrow"></div>
						<div
							v-if="index != statusList.length - 1"
							class="line right-arrow"
						></div>
					</div>
					<div class="status">
						<a-tag style="margin: 0 auto">{{ item.displayName }}</a-tag>
					</div>
					<!-- 当前状态 -->
					<div v-if="item.code == currentRecord.lifecycleStatus" class="text">
						<span
							class="jwi-iconflag"
							style="color: #f6445a; margin-right: 4px"
						></span>
						{{ $t("txt_current_state") }}
					</div>
					<div v-else class="text">{{ $t("txt_set_current") }}</div>
				</div>
			</div>
		</base-color-modal>
		<start-process-modal
			:visible="processVisible"
			:pageCode="pageCode"
			:detailInfo="currentRow"
			:objectDetailsData="objectDetailsData"
			@close="closeProcessModal"
			@getTableData="fetchTable"
		></start-process-modal>
	</div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { jwAvatar, jwIcon, jwModalForm } from "jw_frame";
import baseColorModal from "components/base-color-modal.vue";
import startProcessModal from "../problem-list/start-process-modal";

// 问题管理列表
const fetchContainerList = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.changeServer}/pr/issue/search`,
	method: "post",
});

// 查询可设置的状态
const searchStatusList = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.foundationServer}/lifecycle/findByOid`,
	method: "get",
});

// 设置状态
const part_setStatus = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.changeServer}/pr/issue/setStatus`,
	method: "post",
});

// 获取该条数据可操作下拉列表
const getDropdownList = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
	method: "post",
});

// 创建变更申请单查询
const fetchRoleChange = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.sysconfigServer}/preferences/setting/query-config-value`,
	method: "get",
});

export default {
	name: "oproblem-detail",
	inject: ["setBreadcrumb"],
	props: ["currentRow"],
	components: { jwIcon, baseColorModal, startProcessModal },
	data() {
		return {
			configStatus: "",
			pageCode: "",
			objectDetailsData: {},
			processVisible: false,
			operationVisible: false,
			visible: false,
			trigger: ["hover"],
			okBtnLoading: false,
			statusList: [],
			currentRecord: {},
			operationList: [],
			editStatus: [],
		};
	},
	computed: {},
	created() {
		this.setBreadcrumb([]);
		this.fetchRoleChange();
		this.getEditStatus();
	},
	mounted() {},
	methods: {
		// 查询PR_Edit_Status，查看当前是否
		getEditStatus() {
			let params = {
				name: "PR_Edit_Status",
			};
			fetchRoleChange
				.execute(params)
				.then((data) => {
					console.log("editStatus---------------", data);
					this.editStatus = data.map((item) => item.value);
				})
				.catch((err) => {
					this.$error(err.msg || this.$t("msg_failed"));
				});
		},
		/**
		 * 获取当前变更申请单权限  需要关联的字段：
		 *
		 *  当前问题的状态 lifecycleStatus 和首选项中的状态都是Closed
		 *  没有创建变更申请单的权限
		 *
		 *
		 * */
		fetchRoleChange() {
			let params = {
				name: "PR_Apply_Status",
			};
			fetchRoleChange
				.execute(params)
				.then((data) => {
					this.configStatus = data[0].value;
					this.$emit("setConfigStatus", data[0].value);
					console.log("问题变更申请单状态配置---------------", data);
				})
				.catch((err) => {
					this.$error(err.msg || this.$t("msg_failed"));
				});
		},
		fetchTable() {
			this.$emit("getTableData");
		},
		closeProcessModal() {
			this.processVisible = false;
		},
		operationCancel() {
			delete this.currentRow.new_lifecycleStatus;
			this.operationVisible = false;
		},
		modalOk() {
			const { modelDefinition, oid, type, masterType } = this.currentRecord;
			const { new_lifecycleStatus } = this.currentRow;
			this.okBtnLoading = true;
			part_setStatus
				.execute({
					modelInfo: {
						modelDefinition,
						oid,
						type,
					},
					status: new_lifecycleStatus || this.currentRecord.lifecycleStatus,
				})
				.then((res) => {
					this.$success(this.$t("msg_success"));
					delete this.currentRow.new_lifecycleStatus;
					this.operationVisible = false;
					this.$emit("getTableData");
				})
				.catch((err) => {
					this.$error(err.msg || this.$t("msg_failed"));
				})
				.finally(() => {
					this.okBtnLoading = false;
				});
		},
		searchStatusList(row) {
			searchStatusList
				.execute({
					oid: row.lifecycleOid,
				})
				.then((res) => {
					this.statusList = (res || { context: {} }).context.states || [];
					this.operationVisible = true;
				})
				.catch((err) => {
					console.error(err);
					this.$error(err.msg || this.$t("msg_failed"));
				});
		},
		onCloseModal() {
			this.visible = false;
		},
		onStartProcess(row) {
			this.objectDetailsData = {
				id: row.containerOid,
				type: row.containerType,
			};
			this.currentRecord = { ...row };
			this.processVisible = true;
		},
		onOperateClick(item, row) {
			console.log(item);
			let { key } = item;
			let { currentRow } = this;
			this.currentRecord = currentRow;
			if (key === "details") {
				this.handleProblemDetail(currentRow);
			} else if (key === "edit") {
				// this.handleProblemDetail(currentRow);
				this.$emit("handleEdit", true);
			} else if (key === "startProcess") {
				this.onStartProcess(currentRow);
			} else if (key === "report") {
				this.handleProblemReport(currentRow);
			} else if (key === "delete") {
				this.onDelete(currentRow);
			} else if (key === "setStatus") {
				this.searchStatusList(currentRow);
			} else if (key === "createChange") {
				this.handleCreateChange(currentRow);
			}
		},
		// 获取 row  part 操作下拉列表
		dropdownVisibleChange(visible, row) {
			let { configStatus, editStatus } = this;
			if (visible && !row.operationList) {
				this.$set(row, "loading_status", "loading");
				getDropdownList
					.execute({
						viewCode: "ISSUEINSTANCE",
						objectOid: row.oid,
					})
					.then((data) => {
						data.map((item) => {
							if (item.code === "createChange") {
                item.status =
                    !editStatus.includes(row.lifecycleStatus) &&
                    item.status == "enable"
                        ? "enable"
                        : "disable";
							}
							if (item.code === "edit" || item.code === "setStatus") {
								item.status =
									!editStatus.includes(row.lifecycleStatus) &&
									item.status == "enable"
										? "enable"
										: "disable";
							}
							if (item.code === "report") {
								item.status =
									row.lifecycleStatus == "Closed" && item.status == "enable"
										? "enable"
										: "disable";
							}
							return item;
						});
						this.$set(row, "operationList", data);
						this.$set(row, "loading_status", "done");
					})
					.catch((err) => {
						this.$error(err.msg || this.$t("msg_failed"));
						this.$set(row, "loading_status", "failed");
					});
			}
		},
		handleCreateChange(row) {
			this.$router.push({
				name: "ecr-create",
				path: `/change-management/ecr/create/${row.oid}`,
				query:{
					containerOid:row.containerOid,
                    containerType:row.containerType,
					issue:1,
					name:row.name
				},
				params: {
					oid: row.oid,
					tabActive: this.$route.query.tabActive,
				},
			});
		},
		handleProblemReport(row) {
			// this.$router.push({
			// 	name: "problem-report",
			// 	path: "/problem-report",
			// 	query: {
			// 		oid: row.oid,
			// 		tabActive: this.$route.query.tabActive,
			// 	},
			// });
			this.$emit("switchReport");
		},
		handleProblemDetail(row) {
			this.$router.push({
				name: `problem-detail`,
				path: `/problem-detail`,
				query: {
					oid: row.oid,
					tabActive: this.$route.query.tabActive,
				},
			});
		},
	},
};
</script>

<style lang="less" scoped>
.detail-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 60px;
	min-height: 60px;
	line-height: 60px;
	padding: 0 24px;
	background: #fff;
	.left {
		.back-icon {
			margin-right: 10px;
			cursor: pointer;
			i {
				font-size: 20px;
			}
		}
		.title {
			font-weight: 500;
			font-size: 20px;
			color: rgba(30, 32, 42, 0.85);
		}
	}
	.right {
		margin-right: 100px;
	}
}
.page-container {
	width: 100%;
	padding-left: 24px;
}
.product-config {
	height: calc(~"800px - 53px");
}
/deep/.jw-toolbar-panel .jw-toolbar-box {
	.sub-types-title {
		justify-content: space-between;
		z-index: 5;
		border-right: 0;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
	}
	button + .sub-types-title {
		margin-right: 2px;
	}
	.sub-types-title + span {
		margin-left: -2px;
		width: 240px;
		input {
			padding-left: 10px;
			border-top-left-radius: 0;
			border-bottom-left-radius: 0;
		}
	}
}
.name-con {
	color: #255ed7;
	cursor: pointer;
}

.operation-modal {
	.setStatus-container {
		display: flex;
		justify-content: center;
		margin-top: 300px;

		.item-box {
			//   width: 120px;
			height: 142px;
			padding: 20px 12px 8px;
			border-radius: 5px;
			margin-right: 12px;
			cursor: pointer;
			transition: all 0.3s;
			flex-basis: 120px;
			border: 1px solid transparent;

			.circle {
				width: 16px;
				height: 16px;
				margin: 0 auto;
				border: 12px solid #a4c9fc;
				border-radius: 50%;
				box-sizing: content-box;
				position: relative;

				.line {
					position: absolute;
					top: 6px;
				}

				.left-arrow,
				.right-arrow {
					height: 1px;
					background: rgba(30, 32, 42, 0.15);
				}
				.left-arrow {
					width: 38px;
					left: -58px;

					&::after {
						content: "";
						display: block;
						position: absolute;
						right: -8px;
						top: -4px;
						width: 0;
						height: 0;
						border-top: 4px solid transparent;
						border-left: 8px solid rgba(30, 32, 42, 0.15);
						border-bottom: 4px solid transparent;
					}
				}
				.right-arrow {
					width: 42px;
					right: -58px;

					&::after {
						content: "";
						display: block;
						position: absolute;
						left: -8px;
						top: -4px;
						width: 8px;
						height: 8px;
						background: rgba(30, 32, 42, 0.15);
						border-radius: 50%;
					}
				}
			}
			.status {
				text-align: center;
				margin-top: 8px;
			}
			.text {
				text-align: center;
				opacity: 0;
				transition: all 0.3s;
				font-size: 12px;
				white-space: nowrap;
			}

			&:not(.current) {
				.text {
					margin-top: 10px;
					height: 32px;
					line-height: 32px;
					background: #ffffff;
					border: 1px solid rgba(30, 32, 42, 0.15);
					border-radius: 4px;
				}
			}
			&:hover {
				background: rgba(30, 32, 42, 0.02);
				border-color: rgba(30, 32, 42, 0.15);

				.text {
					opacity: 1;
				}
			}
			&.current {
				background: #f0f7ff;
				border: 1px solid #a4c9fc;

				.text {
					opacity: 1;
					margin-top: 15px;
					height: 22px;
				}
			}
			&.active {
				background: rgba(30, 32, 42, 0.02);
				border-color: rgba(30, 32, 42, 0.15);

				.text {
					opacity: 1;
				}
			}
		}
	}
}
</style>

