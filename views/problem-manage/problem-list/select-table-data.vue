<template>
  <!-- 选择工作流对象 -->
  <div>
    <!-- 要选择的对象 -->
    <select-list-table
      ref="select-list-table"
      :selectList="selectList"
      :modalSelectedRows.sync="modalSelectedRows"
      @addrelationrow="addrelationrow"
      :height="450"
      :showtoolbar="true"
      :showAdd="showAdd"
      @openselectdialog="searchVisible = true"
    />

    <!-- 选择关联对象 -->
    <select-relation-dialog
      :visible.sync="relationshow"
      :rowData="relationRowData"
      :defaultSelect="defaultRelationSelect"
      @changeselect="changerelationselect"
    />

    <search-engine-content-dialog
      :visible.sync="searchVisible"
      @confirm="confirmList"
    />
  </div>
</template>

<script>
import SelectListTable from "./batch-operator-dialog/batch-opertaorcomm/select-list-table.vue";
import SelectRelationDialog from "./batch-operator-dialog/batch-opertaorcomm/select-relation-dialog.vue";
import SearchEngineContentDialog from "./search-engine-content-dialog.vue";
export default {
  props: {
    selectedRows: {
      type: Array,
      default: () => [],
    },
    defaultTableList: {
      type: Array,
      default: () => []
    },
    showAdd: {
      type: Boolean,
      default: true,
    },
  },
  components: {
    SelectListTable,
    SelectRelationDialog,
    SearchEngineContentDialog,
  },
  data() {
    return {
      selectList: [],
      modalSelectedRows: [],
      relationshow: false,
      relationRowData: {},
      defaultRelationSelect: [],
      searchVisible: false,
    };
  },
  watch: {
    modalSelectedRows(val) {
      this.$emit("update:selectedRows", val);
    },
    defaultTableList: {
      immediate: false,
      handler: function(val){
        this.$nextTick(() => {
          this.setTableList(val)
        })
      }
    }
  },
  mounted() {
    this.$refs["select-list-table"].inittabledata(true);
    if(this.defaultTableList.length > 0){
      this.setTableList(this.defaultTableList)
    }
  },
  methods: {
    addrelationrow(row, datalist) {
      this.relationRowData = Object.assign({}, row);
      this.initRelationSelect(datalist);
      this.relationshow = true;
    },
    //初始化默认选中项
    initRelationSelect(datalist) {
      let selectDataList = datalist.filter(
        (item) => item.selectParendOid === this.relationRowData.oid
      );
      if (selectDataList) {
        this.defaultRelationSelect = selectDataList.map((item) => {
          let exam = Object.assign({}, item);
          delete exam.selectParendOid;
          return exam;
        });
      }
    },
    confirmList(list) {
      this.changerelationselect(list, null);
      this.$nextTick(() => {
        //默认勾选所有
        if(this.$refs['select-list-table']){
          this.modalSelectedRows = this.$refs['select-list-table'].dataList
        }
      })
    },
    setTableList(val){
      this.$refs['select-list-table'].dataList = val
      this.confirmList(val)
    },
    //已添加select
    changerelationselect(val, selectParendOid) {
      this.$refs["select-list-table"].changerelationselect(
        val,
        selectParendOid
      );
    },
  },
};
</script>

<style>
</style>