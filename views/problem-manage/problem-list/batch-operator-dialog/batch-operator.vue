<template>
  <div>
    <a-dropdown>
      <a-tooltip placement="top">
        <template #title>
          <span>{{ $t("btn_batch_operation") }}</span>
        </template>
        <a-button>
          <jw-icon type="jwi-iconlayers"></jw-icon>
        </a-button>
      </a-tooltip>

      <a-menu slot="overlay">
        <a-spin v-if="loading" style="margin: 20px 65px" />
        <a-menu-item v-if="!loading" @click="validSelect('move')">
          <span class="jwi-iconmove" style="margin-right: 8px"></span>
          {{ $t("txt_mobile") }}
        </a-menu-item>
        <a-menu-item v-if="!loading" @click="validSelect('saveAs')">
          <span class="jwi-iconsave-as" style="margin-right: 8px"></span>
          {{ $t("txt_save") }}
        </a-menu-item>
        <a-menu-item v-if="!loading" @click="validSelect('process')">
          <span
            class="jwi-iconSelect-processing-1"
            style="margin-right: 8px"
          ></span>
          {{ $t("txt_initiate_process") }}
        </a-menu-item>
        <a-menu-item v-if="!loading" @click="validSelect('addBaseLine')">
          <span class="jwi-iconflag" style="margin-right: 8px"></span>
          {{ $t("txt_add_to_baseline") }}
        </a-menu-item>
      </a-menu>
    </a-dropdown>

    <select-list-table-dialog
      ref="select-list-table-dialog"
      :type="actionType"
      :selectList.sync="selectList"
      :visible.sync="showdialog"
      @batchOperator="batchOperator"
      @batchProcess="batchInitiateProcess"
      @batchBaseLine="batchAddBaseLine"
    />

    <start-process-modal
      :visible.sync="processVisible"
      ref="start-process-modal"
      :pageCode="'processManage'"
      :detailInfo="{}"
      @close="processVisible = false"
      @getTableData="resetProcessModal"
    ></start-process-modal>
  </div>
</template>

<script>
import SelectListTableDialog from "./batch-opertaorcomm/select-list-table-dialog.vue";
import StartProcessModal from "../product-content/process-manage/start-process-modal.vue";
import {
  batchMoveAction,
  batchSaveAs,
  batchCreateThenStart,
  batchLink,
} from "./api/index";
export default {
  components: { SelectListTableDialog, StartProcessModal },
  props: {
    selectList: {
      type: Array,
      default: () => [],
    },
    containerOid: {
      type: String,
    },
    containerModel: {
      type: String,
    },
    containerType: {
      type: String,
    },
  },
  data() {
    return {
      loading: false,
      showdialog: false,
      actionType: "",
      processVisible: false,
    };
  },
  watch: {
    selectList: function (val) {
      console.log("选择项改变", val);
    },
  },
  methods: {
    //清除默认选择数据
    clearSelect() {
      this.$emit("update:selectList", []);
    },
    //列表弹窗loading开启
    listloading() {
      this.$refs["select-list-table-dialog"].btnloading = true;
    },
    //列表弹窗loading关闭
    listcloseloading() {
      this.$refs["select-list-table-dialog"].btnloading = false;
    },
    //验证是否有选择项
    validSelect(actionType) {
      if (this.selectList && this.selectList.length > 0) {
        this.actionType = actionType;
        //验证通过打开弹窗
        if (actionType === "process") {
          //发起流程弹出其他页面
          this.$refs["start-process-modal"].defaultTableList = JSON.parse(
            JSON.stringify(this.selectList)
          );
          this.processVisible = true;
        } else {
          this.showdialog = true;
        }
        return true;
      } else {
        this.$warning(this.$t("txt_pls_data"));
        return false;
      }
    },
    resetProcessModal() {},
    //批量操作
    batchOperator(selectList, treeData, splitcode, beforcode) {
      switch (this.actionType) {
        case "move":
          //批量移动
          this.batchMoveExecute(selectList, treeData);
          break;
        case "saveAs":
          //批量另存为
          let paramObj = this.batchOperatorType(selectList);
          for (const param in paramObj) {
            if (Object.hasOwnProperty.call(paramObj, param)) {
              const element = paramObj[param];
              this.batchOtherSave(
                param,
                element,
                treeData,
                splitcode,
                beforcode
              );
            }
          }
          break;
        default:
          break;
      }
    },

    //按不同类型执行移动
    batchMoveExecute(list, tree) {
      this.listloading();
      batchMoveAction(list, tree)
        .then((resp) => {
          this.$success(this.$t("msg_success"));
          this.showdialog = false;
          this.clearSelect();
          //重新加载行
          this.$emit("reloadpage");
        })
        .catch((e) => {
          console.error(e);
          this.$error(e.msg || this.$t("msg_failed"));
        })
        .finally(() => {
          this.listcloseloading();
        });
    },

    //根据不同类型分类
    batchOperatorType(selectList) {
      let res = {};
      selectList.forEach((item) => {
        if (res[item.masterType]) {
          res[item.masterType].push(item);
        } else {
          res[item.masterType] = [item];
        }
      });
      return res;
    },

    //批量另存为
    batchOtherSave(type, list, treeData, splitcode, beforcode) {
      this.listloading();
      batchSaveAs(type, list, treeData, splitcode, beforcode)
        .then((resp) => {
          this.$success(this.$t("msg_success"));
          this.showdialog = false;
          this.clearSelect();
          //重新加载行
          this.$emit("reloadpage");
        })
        .catch((e) => {
          console.error(e);
          this.$error(e.msg || this.$t("msg_failed"));
        })
        .finally(() => {
          this.listcloseloading();
        });
    },
    //批量发起流程
    batchInitiateProcess(selectList, val) {
      this.listloading();
      this.$refs["select-list-table-dialog"].processVisible = false;
      batchCreateThenStart(selectList, val, {
        catalogOid: this.containerOid,
        catalogType: this.containerType,
        containerOid: this.containerOid,
        containerType: this.containerType,
      })
        .then((resp) => {
          this.$success(this.$t("msg_success"));
          this.clearSelect();
          this.showdialog = false;
        })
        .catch((e) => {
          console.error(e);
          this.$error(e.msg);
        })
        .finally(() => {
          this.listcloseloading();
        });
    },
    //批量添加至基线
    batchAddBaseLine(oid, selectList) {
      this.listloading();
      batchLink(oid, selectList)
        .then((resp) => {
          this.$success(this.$t("msg_success"));
          this.showdialog = false;
          this.clearSelect();
        })
        .catch((e) => {
          console.log(e);
          this.$error(e.msg);
        })
        .finally(() => {
          this.listcloseloading();
        });
    },
  },
};
</script>

<style lang="less" scoped>
</style>