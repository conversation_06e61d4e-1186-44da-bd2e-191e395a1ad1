<template>
  <div>
    <a-modal
      v-model.trim="visibleshow"
      :title="title"
      width="80%"
      :okText="okText"
      :cancelText="$t('btn_cancel')"
      @ok="confirmbtn"
      :confirmLoading="btnloading"
    >
      <select-list-table
        ref="select-list-table"
        :selectList="selectList"
        :modalSelectedRows.sync="modalSelectedRows"
        :type="type"
        :splitcode="splitcode"
        :beforcode="beforcode"
        @addrelationrow="addrelationrow"
      />
    </a-modal>

    <!-- 选择关联对象 -->
    <select-relation-dialog
      :visible.sync="relationshow"
      :rowData="relationRowData"
      :defaultSelect="defaultRelationSelect"
      @changeselect="changerelationselect"
      @removeSelect="removeSelect"
    />

    <!-- 批量移动复用 -->
    <create-part-dialog
      ref="create-part-dialog"
      :batchOperatorType="type"
      @batchOperator="batchOperator"
    />

    <!-- 发起流程 -->
    <!-- <start-process-modal
      :visible.sync="processVisible"
      :pageCode="'objectProcess'"
      :detailInfo="{}"
      :batchProcess="true"
      @submit="startProcess"
      @close="processVisible = false"
    ></start-process-modal> -->

    <!-- 添加至基线 -->
    <SelectBaseLine
      @batchBaseLine="batchBaseLine"
      :visible.sync="baseLineVisible"
    />
  </div>
</template>

<script>
import StartProcessModal from "/views/product-content/process-manage/start-process-modal";
import SelectRelationDialog from "./select-relation-dialog.vue";
import CreatePartDialog from "../../../components/dropdowncomponents/create-part-dialog.vue";
import SelectBaseLine from "./select-base-line.vue";
import SelectListTable from "./select-list-table.vue";
import { findConf } from "../api/index";
export default {
  components: {
    SelectRelationDialog,
    CreatePartDialog,
    StartProcessModal,
    SelectBaseLine,
    SelectListTable,
  },
  props: {
    type: {
      type: String,
    },
    selectList: {
      type: Array,
      default: () => [],
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      title: this.$t("txt_batch_operation"),
      okText: this.$t("btn_ok"),
      //收集对象弹窗
      relationshow: false,
      relationRowData: {},

      defaultRelationSelect: [],

      btnloading: false,

      processVisible: false,

      baseLineVisible: false,

      // table选中项
      modalSelectedRows: [],

      splitcode: "_",
      beforcode: "*",
    };
  },
  created() {
    this.loadconfig();
  },
  watch: {
    visibleshow: function (val) {
      if (this.$refs["select-list-table"]) {
        this.$refs["select-list-table"].inittabledata(val);
      }
      if (val) {
        this.init();
        this.clearnewname();
      }
    },
    modalSelectedRows: function (val) {
      console.log("改变选择项", val);
    },
  },
  computed: {
    visibleshow: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
  methods: {
    //清理已改名称
    clearnewname() {
      this.selectList.forEach((item) => {
        delete item.newname;
      });
      if (this.$refs["select-list-table"]) {
        this.$refs["select-list-table"].rownewnames = [];
      }
    },
    //加载首选项配置
    loadconfig() {
      findConf("Naming_Rules").then((resp) => {
        let befor = resp.find((item) => item.value === "saveas_befor");
        if (befor) {
          this.beforcode = befor.value;
        }
        let after = resp.find((item) => item.value === "saveas_after");
        if (after) {
          this.splitcode = after.value;
        }
      });
    },
    addrelationrow(row, datalist) {
      this.relationRowData = Object.assign({}, row);
      this.initRelationSelect(datalist);
      this.relationshow = true;
    },
    //初始化默认选中项
    initRelationSelect(datalist) {
      let selectDataList = datalist.filter(
        (item) => item.selectParendOid === this.relationRowData.oid
      );
      if (selectDataList) {
        this.defaultRelationSelect = selectDataList.map((item) => {
          let exam = Object.assign({}, item);
          delete exam.selectParendOid;
          return exam;
        });
      }
    },
    startProcess(val) {
      this.$emit("batchProcess", this.modalSelectedRows, val);
    },

    batchBaseLine(oid) {
      this.$emit("batchBaseLine", oid, this.modalSelectedRows);
    },

    onCloseProcessModal() {
      console.log("关闭发起流程");
    },
    //已添加select
    changerelationselect(val, selectParendOid) {
      this.$refs["select-list-table"].changerelationselect(
        val,
        selectParendOid
      );
    },
    //支持收集相关对象删除功能
    removeSelect(rows) {
      if (this.$refs["select-list-table"]) {
        rows.forEach((item) => {
          this.$refs["select-list-table"].removerow(item);
        });
      }
    },
    //点击确认选择行
    confirmbtn() {
      if (this.modalSelectedRows.length == 0) {
        this.$warning(this.$t("txt_pls_data"));
        return;
      }
      console.log("点击确认选择行", this.modalSelectedRows);
      let rownewnames = this.$refs["select-list-table"].rownewnames;
      this.modalSelectedRows.forEach((item) => {
        if (rownewnames[item.oid]) {
          item.newname = rownewnames[item.oid];
        }
      });

      switch (this.type) {
        case "move": {
          this.$refs["create-part-dialog"].init({});
          break;
        }
        case "saveAs": {
          this.$refs["create-part-dialog"].init({});
          break;
        }
        case "process": {
          //批量发起流程
          this.processVisible = true;
          break;
        }
        case "addBaseLine": {
          this.baseLineVisible = true;
          break;
        }
        default:
          break;
      }
    },

    batchOperator(treeSelect) {
      this.$emit(
        "batchOperator",
        this.modalSelectedRows,
        treeSelect,
        this.splitcode,
        this.beforcode
      );
    },
    init() {
      switch (this.type) {
        case "move":
          //移动
          this.title = this.$t("txt_move_obj");
          this.okText = this.$t("txt_select_location");
          break;

        case "saveAs": {
          this.title = this.$t("txt_saveas_obj");
          this.okText = this.$t("txt_select_location");
          break;
        }
        case "process": {
          this.title = this.$t("txt_initiate_process");
          this.okText = this.$t("txt_baseline_model");
          break;
        }
        case "addBaseLine": {
          this.title = this.$t("txt_add_to_baseline");
          this.okText = this.$t("txt_seletct_baseline");
          break;
        }
        default:
          break;
      }
    },
    fetchModalTable() {
      console.log("加载table");
    },
  },
};
</script>

<style lang="less" scoped>
</style>