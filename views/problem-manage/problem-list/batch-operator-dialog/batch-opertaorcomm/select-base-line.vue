<template>
  <div>
    <a-modal
      :visible.sync="visibleshow"
      :title="$t('txt_add_to_baseline')"
      :confirm-loading="loading"
      @ok="confirm"
      @cancel="visibleshow = false"
      :okText="$t('btn_done')"
      :cancelText="$t('btn_cancel')"
    >
      <a-radio-group
        v-model.trim="pageselect"
        :default-value="pageselect"
        button-style="solid"
      >
        <a-radio-button :value="true">
          {{ $t("txt_existing_baseline") }}
        </a-radio-button>
        <a-radio-button :value="false">
          {{ $t("txt_create_baseline") }}
        </a-radio-button>
      </a-radio-group>

      <a-form-model ref="form" class="formmodel" :model="formdata">
        <div v-if="pageselect">
          <!-- 已有基线 -->
          <a-form-model-item :label="$t('tabel_context')" prop="contextId">
            <a-tree-select
              v-model.trim="formdata.contextId"
              show-search
              :placeholder="$t('txt_select_context')"
              :tree-data="treeData"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :filterTreeNode="filterTreeNode"
            >
              <span slot="titleName" slot-scope="{ name }">
                <jw-icon style="margin-right: 4px" type="#jwi-chanpin" />
                {{ name }}
              </span>
            </a-tree-select>
          </a-form-model-item>

          <a-form-model-item
            :label="$t('txt_seletct_baseline')"
            prop="baseLineOid"
            :rules="[
              { required: true, message: $t('msg_select'), trigger: 'change' },
            ]"
          >
            <a-select
              v-model.trim="formdata.baseLineOid"
              :placeholder="$t('txt_befor_choose_context')"
              showSearch
              :filterOption="filterOption"
            >
              <a-select-option
                :value="item.oid"
                v-for="(item, index) in baseLineSelect"
                :key="index"
              >
                <jw-icon
                  style="margin-right: 4px; color: rgb(37, 94, 215)"
                  type="jwi-iconbaseline"
                ></jw-icon>
                {{ item.number + "," + item.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </div>
        <div v-else>
          <!-- 新建基线 -->
          <a-form-model-item
            :label="$t('txt_baseline_name')"
            prop="name"
            :rules="[
              { required: true, message: $t('msg_select'), trigger: 'change' },
            ]"
          >
            <a-input
              :placeholder="$t('txt_input')"
              v-model.trim="formdata.name"
              :maxLength="100"
            />
          </a-form-model-item>

          <a-form-model-item
            :label="$t('txt_security')"
            prop="levelForSecrecy"
            :rules="[
              { required: true, message: $t('msg_select'), trigger: 'change' },
            ]"
          >
            <a-select
              v-model.trim="formdata.levelForSecrecy"
              :placeholder="$t('msg_select')"
            >
              <a-select-option
                v-for="(item, index) in leaveSelectList"
                :key="index"
                :value="item.val"
              >
                {{ item.txt }}
              </a-select-option>
            </a-select>
          </a-form-model-item>

          <a-form-model-item :label="$t('txt_baseline_type')" prop="detailType">
            <a-select
              v-model.trim="formdata.detailType"
              :placeholder="$t('msg_select')"
            >
              <a-select-option
                v-for="(item, index) in baseLineType"
                :key="index"
                :value="item"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-model-item>

          <a-form-model-item :label="$t('txt_description')" prop="description">
            <a-textarea
              v-model.trim="formdata.description"
              :placeholder="$t('msg_input')"
              :auto-size="{ minRows: 3, maxRows: 5 }"
            >
            </a-textarea>
          </a-form-model-item>

          <div class="otherprop">
            <a-form-model-item
              :label="$t('btn_exten_attr') + '1'"
              prop="Extendpro"
              class="subformmodel"
            >
              <a-input v-model.trim="formdata.Extendpro"></a-input>
            </a-form-model-item>
            <a-form-model-item
              :label="$t('btn_exten_attr') + 'A'"
              prop="ExtendedAttr_A"
              class="subformmodel"
            >
              <a-input
                v-model.trim="formdata.ExtendedAttr_A"
              ></a-input>
            </a-form-model-item>
          </div>
        </div>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {
  folderTree,
  getBaselineList,
  getLevelList,
  getBaseLinePermission,
  createBaseline,
} from "../api/index";
export default {
  props: {
    visible: {
      type: Boolean,
    },
  },
  computed: {
    visibleshow: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
  data() {
    return {
      loading: false,
      //当前选择基线或新建基线
      pageselect: true,
      formdata: {
        contextId: null,
        Extendpro: '1',
        ExtendedAttr_A: 'A'
      },

      value: null,

      scopedSlots: {
        // custom title
        title: "titleName",
      },

      treeData: [],
      baseLineSelect: [],
      leaveSelectList: [],
      baseLineType: ["功能基线", "分配基线", "产品基线"],

      containerOid: "",
      containerType: "",
    };
  },
  watch: {
    "formdata.contextId": function (val) {
      this.loadbaseLineData();
    },
  },
  created() {
    this.loadfolderTree();
    this.getLevelListFun();
    this.getBaseLinePermissionFun();
    const { oid, type } = this.$route.query;
    this.containerOid = oid;
    this.containerType = type;
  },
  methods: {
    getLevelListFun() {
      getLevelList({})
        .then((resp) => {
          this.leaveSelectList = [...resp];
        })
        .catch((e) => {
          console.error(e);
        });
    },
    confirm() {
      if (this.pageselect) {
        this.saveToBaseLine(this.formdata.baseLineOid);
      } else {
        this.createBaseLine();
      }
    },

    //创建基线
    createBaseLine() {
      this.$refs["form"].validate().then((valid) => {
        if (valid) {
          this.loading = true;
          let params = Object.assign({}, this.formdata);
          params.extensionContent = {
            ExtendedAttr_A: params.ExtendedAttr_A,
            Extendpro: params.Extendpro,
          };
          createBaseline(params, this.containerOid, this.containerType)
            .then((resp) => {
              //执行保存
              this.visibleshow = false
              this.saveToBaseLine(resp.oid)
            })
            .catch((e) => {
              console.error(e);
              this.$error(e.msg);
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },

    //保存至基线
    saveToBaseLine(oid) {
      this.$refs["form"].validate().then((valid) => {
        if (valid) {
          this.visibleshow = false
          this.$emit("batchBaseLine", oid);
        }
      });
    },

    loadfolderTree() {
      folderTree({ containerModel: this.$route.query.modelDefinition })
        .then((resp) => {
          this.treeData = resp.map((item) => {
            return {
              ...item,
              key: item.oid,
              value: item.oid,
              scopedSlots: this.scopedSlots,
              children: null,
            };
          });
        })
        .catch((e) => {
          console.error(e);
        });
    },
    filterTreeNode(inputValue, treeNode) {
      const { name } = treeNode.data.props;
      return name.includes(inputValue);
    },
    filterOption(inpuvalue, obj) {
      const { value } = obj.componentOptions.propsData;
      let select = this.baseLineSelect.find((item) => item.oid === value);
      if (select) {
        const { name, number } = select;
        return name.includes(inpuvalue) || number.includes(inpuvalue);
      } else {
        return false;
      }
    },
    loadbaseLineData() {
      let selectTree = this.treeData.find(
        (item) => item.oid === this.formdata.contextId
      );
      if (selectTree) {
        getBaselineList(selectTree.catalogOid)
          .then((resp) => {
            const { rows } = resp;
            this.baseLineSelect = [...rows];
          })
          .catch((e) => {
            console.error(e);
          });
      }
    },
    getBaseLinePermissionFun() {
      const { oid, type } = this.$route.query;
      getBaseLinePermission({
        objectOid: oid,
        objectType: type,
        viewCode: "BASELINEOPERATION",
      })
        .then((resp) => {
          console.log("页面权限", resp);
        })
        .catch((e) => {
          console.error(e);
        });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .ant-form-item-label {
  display: block;
  margin: 0;
  padding: 0 0 8px;
  line-height: 1.5;
  white-space: initial;
  text-align: left;
}

.formmodel {
  margin-top: 20px;
}

.otherprop {
  display: flex;
  justify-content: space-between;
}

.subformmodel {
  width: 49%;
}
</style>