<template>
  <div class="main-page">
    <div class="toolbar" v-if="showtoolbar && showAdd">
      <a-button type="primary" @click="addobject">{{
        $t("txt_add_object")
      }}</a-button>
    </div>
    <jw-table
      ref="jwtable"
      :data-source.sync="dataList"
      :columns="modalColumns"
      :showPage="false"
      :height="height"
      :disableCheck="disableCheckFun"
    >
      <template #thumbSlot="{ row }">
        <keep-alive>
          <div>
            <div
              class="thumb-view"
              :title="$t('txt_filePreview')"
              v-if="validThumbType(row)"
            >
              <jwIcon type="#jwi-PDFwendang" />
            </div>
            <cad-thumbnail
              v-if="validcadthumb(row)"
              :currentRow="row"
            ></cad-thumbnail>
          </div>
        </keep-alive>
      </template>

      <template #numberSlot="{ row }">
        <span>
          <jwIcon :type="numbericonType(row)" />
          <span style="color: #255ed7">{{ row.number }}</span>
          <span class="mainobject" v-if="!row.selectParendOid">{{
            $t("txt_object_main")
          }}</span>
        </span>
      </template>

      <template #nameSlot="{ row }">
        <div>
          <div
            v-if="
              type === 'saveAs' &&
              (row.masterType === 'MCAD' || row.masterType === 'Part')
            "
            class="namegroup"
          >
            <div>{{ beforcode + (row.cname || row.name) + splitcode }}</div>
            <a-input v-model.trim="rownewnames[row.oid]" :maxLength="100"></a-input>
          </div>
          <div v-else>
            {{ row.cname || row.name }}
          </div>
        </div>
      </template>

      <template #operation="{ row }">
        <div class="btn-group">
          <a-tooltip placement="top" :title="$t('txt_collecting_Objects')">
            <span @click="addrelationrow(row)">
              <jw-icon type="jwi-chanpinxingpu"></jw-icon>
            </span>
          </a-tooltip>

          <a-tooltip placement="top" :title="$t('btn_delete')">
            <span @click="removerow(row)">
              <jw-icon type="jwi-icondelete"></jw-icon>
            </span>
          </a-tooltip>
        </div>
      </template>
    </jw-table>
  </div>
</template>

<script>
import cadThumbnail from "components/cad-thumbnail.vue";
export default {
  props: {
    selectList: {
      type: Array,
      default: () => [],
    },
    modalSelectedRows: {
      type: Array,
      default: () => [],
    },
    relationshow: {
      type: Boolean,
    },
    type: {
      type: String,
    },
    height: {
      type: Number,
      default: 600,
    },
    showtoolbar: {
      type: Boolean,
      default: false,
    },
    splitcode: {
      type: String,
    },
    beforcode: {
      type: String,
    },
    showAdd: {
      type: Boolean,
      default: true,
    },
  },
  components: {
    cadThumbnail,
  },
  data() {
    return {
      dataList: [],
      rownewnames: {},
    };
  },
  watch: {
    dataList: function (val, oldval) {
      if (val.length > oldval.length) {
        this.selectDefault();
      }
    },
  },
  computed: {
    modalSelectedRowsValue: {
      get() {
        return this.modalSelectedRows;
      },
      set(val) {
        this.$emit("update:modalSelectedRows", val);
      },
    },
    modalColumns() {
      return [
        {
          field: "number",
          title: this.$t("txt_number_of"),
          sortable: true,
          slots: {
            default: "numberSlot",
          },
        },
        {
          field: "name",
          title: this.$t("txt_name"),
          sortable: true,
          slots: {
            default: "nameSlot",
          },
        },
        {
          field: "lifecycleStatus",
          title: this.$t("txt_plan_lifecycle"),
          sortable: true,
        },
        {
          field: "displayVersion",
          title: this.$t("txt_plan_version"),
          sortable: true,
        },
        {
          // 操作列定义
          field: "operation", //关键字
          title: this.$t("txt_operation"),
          slots: {
            default: "operation",
          },
        },
      ];
    },
  },
  created() {
    this.dataList = [...this.selectList];
  },
  methods: {
    //验证禁用的数据
    disableCheckFun({ row }) {
      if (this.type === "move" && row.lockOwnerAccount) {
        //检出数据不能移动
        return true;
      }
      return false;
    },
    //新增数据默认选中
    selectDefault() {
      this.modalSelectedRowsValue = this.dataList.filter(
        (item) => !this.disableCheckFun({ row: item })
      );
    },
    addobject() {
      this.$emit("openselectdialog");
    },
    inittabledata(val) {
      if (val) {
        this.dataList = [...this.selectList];
      } else {
        this.dataList = [];
      }
    },
    //打开收集对象
    addrelationrow(row) {
      this.$emit("addrelationrow", row, this.dataList);
    },
    changerelationselect(val, selectParendOid) {
      let selectData = val.map((item) => {
        return {
          ...item,
          selectParendOid,
        };
      });
      let dataListOids = this.dataList.map((item) => item.oid);
      let needAddList = selectData.filter(
        (item) => !dataListOids.includes(item.oid)
      );
      this.dataList = [...this.dataList, ...needAddList];
    },
    //删除
    removerow(row) {
      let selectIndex = this.dataList.findIndex((item) => item.oid === row.oid);
      if (selectIndex !== -1) {
        this.dataList.splice(selectIndex, 1);
      }

      //清理已选择项
      let chooseIndex = this.modalSelectedRows.findIndex(
        (item) => item.oid === row.oid
      );
      if (chooseIndex !== -1) {
        this.modalSelectedRows.splice(chooseIndex, 1);
      }
    },
    numbericonType(row) {
      return row.modelDefinition == "CADDrawing" ||
        row.modelDefinition == "PCB" ||
        row.modelDefinition == "Schematic"
        ? "#jwi-lianjian"
        : row.modelIcon;
    },

    validcadthumb(row) {
      return (
        row.thumbnailOid &&
        row.modelDefinition != "CADDrawing" &&
        row.modelDefinition != "PCB" &&
        row.modelDefinition != "Schematic"
      );
    },

    validThumbType(row) {
      return (
        row.modelDefinition == "CADDrawing" ||
        row.modelDefinition == "PCB" ||
        row.modelDefinition == "Schematic"
      );
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.vxe-table--render-default .vxe-body--column:not(.col--ellipsis) {
  padding: 0;
  .vxe-cell {
    padding: 0;
  }
}

.toolbar {
  margin-bottom: 10px;
}

.thumb-view {
  display: flex;
  justify-content: center;
  font-size: 25px;
  cursor: pointer;
}

.btn-group {
  display: flex;
  justify-content: space-around;
}

.btn-group span {
  cursor: pointer;
}
.mainobject {
  background: #f0f7ff;
  border: 1px solid #a4c9fc;
  border-radius: 4px;
  font-size: 12px;
  color: #255ed7;
  padding: 1px 2px 1px 2px;
}

.namegroup {
  display: flex;
  align-items: center;
}
</style>