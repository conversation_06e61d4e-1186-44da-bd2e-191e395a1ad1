<template>
	<div class="base-panel page-container">
		<jw-table
			ref="table"
			height="400px"
			:columns="columns"
			:data-source.sync="tableData"
			:pagerConfig="pagerConfig"
			:showPage="false"
			disableCheck="disableCheck"
			:selectedRows.sync="selectedRows"
			:toolbars="toolbars"
			@checkbox-change="checkboxChange"
			@onToolClick="onToolClick"
			@onToolInput="onToolInput"
			@onOperateClick="onOperateClick"
			@onPageChange="onPageChange"
			@onSizeChange="onSizeChange"
		>
			<template #number="{ row }">
				<div class="name-wrap">
					<div class="name-con" @click="handleProblemDetail(row)">
						<jwIcon :type="row.modelIcon"></jwIcon>
						<span class="name-item">{{ row.name }}</span>
					</div>
				</div>
			</template>
			<template #problemStatus="{ row }">
				<div class="name-wrap">
					<a-tag>{{ row.status ? $t('txt_problem_received') : $t('txt_problem_received') }}</a-tag>
				</div>
			</template>
			<template #handlerUser="{ row }">
				<div class="name-wrap">
					<jw-avatar tag show-name :data="row" />
				</div>
			</template>
		</jw-table>
	</div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { jwAvatar, jwIcon, jwModalForm } from "jw_frame";
import { setOwner } from "apis/baseapi";
// 产品容器列表
const fetchContainerList = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.containerService}/container/product/search`,
	method: "post",
});
export default {
	name: "overall-Preview",
	inject: ["setBreadcrumb"],
	props: ["dataSource"],
	components: { jwAvatar, jwIcon, jwModalForm },
	data() {
		return {
			selectedRows: [],
			tableData: [],
			//分页配置
			pagerConfig: {
				current: 1,
				pageSize: 20,
				total: 0,
			},
		};
	},
	created() {},
	computed: {
		columns() {
			return [
				{
					field: "name",
					title: this.$t('txt_name'),
					width: "240px",
					slots: {
						default: "number",
					},
				},
				{
					field: "name",
					title: this.$t('txt_number'),
					width: "130px",
				},
				{
					field: "changeType.txt",
					title: this.$t('txt_version'),
					width: "70px",
				},
				{
					field: "createDate",
					title: this.$t('txt_lifecycle'),
					width: "140px",
					slots: {
						default: "problemStatus",
					},
				},
				{
					field: "createBy",
					title: this.$t('txt_major'),
					width: "140px",
				},
				{
					field: "createDate",
					title: this.$t('txt_problem_field'),
					formatter: "date",
					width: "140px",
				},
			];
		},
		toolbars() {
			return [];
		},
	},
	watch: {
		dataSource(val) {
			if (val) {
				this.tableData = [...val];
			}
		},
	},
	mounted() {},
	methods: {
		checkboxChange(selectedRows) {
			console.log(selectedRows);
			this.$emit("setAdded", [...selectedRows.records]);
		},
		reFetchData() {
			// this.fetchTable({ current: 1, pageSize: 20 });
		},
		//分页操作
		onPageChange(page, pageSize) {
			this.pages = {
				currentPage: page,
				size: pageSize,
				searchKey: this.searchKey,
			};

			this.pagerConfig.current = page;
			this.pagerConfig.pageSize = pageSize;
			this.reFetchData();
		},
		onSizeChange(pageSize, page) {
			this.pages = {
				currentPage: page,
				size: pageSize,
				searchKey: this.searchKey,
			};
			this.pagerConfig.current = page;
			this.pagerConfig.pageSize = pageSize;

			this.reFetchData();
		},
		// 工具栏点击回调
		onToolClick(item) {
			if (item.key === "create") {
			} else if (item.key === "switch") {
				// 切换列表
				this.switchType = !this.switchType;
			} else if (item.key === "delete") {
				// console.log("this.selectedRows", this.selectedRows);
				this.fetchDelete(this.selectedRows);
			}
		},
		// 工具栏输入回调
		onToolInput({ key }, value) {
			// console.log(value);
			if (key === "search") {
				this.searchKey = value;
				this.delaySearch();
			}
		},
		handleProblemDetail() {
			// 跳转到ECR
			// this.$router.push({
			// 	name: "problem-detail",
			// 	path: "/problem-detail",
			// });
		},
		onOperateClick(item, row) {
			let key = item.code || item;
			if (key === "details") {
				this.handleProblemDetail(row);
			} else if (key === "startProcess") {
				startECRWorkflow
					.execute({
						ecrOid: row.oid,
					})
					.then((res) => {
						this.currentRecord = row;
						this.processVisible = true;
					})
					.catch((err) => {
						this.$error(err.msg);
					});
			} else if (key === "updateOwner") {
				this.$refs["user-modal"]
					.show({
						type: "User",
					})
					.then((res) => {
						setOwner
							.execute({
								oid: row.oid,
								type: row.type,
								ownerAccount: res.account,
							})
							.then((res) => {
								this.$success(this.$t("msg_save_success"));
								this.reFetch();
							})
							.catch((err) => {
								this.$error(err.msg);
							});
					});
			} else if (key === "delete") {
				this.onDelete(row);
			}
		},
		fetchTable({ current, pageSize }) {
			let params = {};
			return fetchContainerList
				.execute(params)
				.then((data) => {
					this.pagerConfig.total = data.count;
					return { data: data.rows || data, total: data.count };
				})
				.catch((err) => {
					// this.$error(err.msg || this.$t("msg_failed"));
				});
		},
	},
};
</script>

<style lang="less" scoped>
.page-container {
	margin-top: 16px;
	width: 100%;
}
/deep/.jw-toolbar-panel .jw-toolbar-box {
	.sub-types-title {
		justify-content: space-between;
		z-index: 5;
		border-right: 0;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
	}
	button + .sub-types-title {
		margin-right: 2px;
	}
	.sub-types-title + span {
		margin-left: -2px;
		width: 240px;
		input {
			padding-left: 10px;
			border-top-left-radius: 0;
			border-bottom-left-radius: 0;
		}
	}
}
</style>
