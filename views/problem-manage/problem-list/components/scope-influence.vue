<template>
  <div style="width: 100%">
    <a-form-model
      ref="addForm"
      class="form-container"
      layout="vertical"
      :model="form"
      :rules="rules"
    >
      <a-form-model-item :label="$t('txt_problem_stock')" prop="stock">
        <a-input-number
          style="width: 100%"
          v-model.trim="form.stock"
          allow-clear
          :placeholder="$t('txt_input')"
        />
      </a-form-model-item>
      <a-form-model-item :label="$t('txt_problem_cost')" prop="cost">
        <a-input-number
          style="width: 100%"
          v-model.trim="form.cost"
          allow-clear
          :placeholder="$t('txt_input')"
        />
      </a-form-model-item>
      <a-form-model-item :label="$t('txt_problem_weight')" prop="weight">
        <a-input-number
          style="width: 100%"
          v-model.trim="form.weight"
          allow-clear
          :placeholder="$t('txt_input')"
        />
      </a-form-model-item>
      <a-form-model-item :label="$t('txt_problem_process')" prop="hasTechnology">
        <a-radio-group v-model.trim="form.hasTechnology">
          <a-radio :value="true">{{ $t('txt_yes') }}</a-radio>
          <a-radio :value="false">{{ $t('txt_no') }}</a-radio>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item :label="$t('txt_problem_manufacturing')" prop="hasManufacture">
        <a-radio-group v-model.trim="form.hasManufacture">
          <a-radio :value="true">{{ $t('txt_yes') }}</a-radio>
          <a-radio :value="false">{{ $t('txt_no') }}</a-radio>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item :label="$t('txt_problem_service')" prop="hasAfterSales">
        <a-radio-group v-model.trim="form.hasAfterSales">
          <a-radio :value="true">{{ $t('txt_yes') }}</a-radio>
          <a-radio :value="false">{{ $t('txt_no') }}</a-radio>
        </a-radio-group>
      </a-form-model-item>
      <!-- <a-form-model-item class="form-item-btns">
				<a-button type="primary" @click="onCreate"> 新建 </a-button>
				<a-button class="form-btn-cancel" @click="onCancel"> 取消 </a-button>
			</a-form-model-item> -->
    </a-form-model>
  </div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
// 添加售后产品
const createMember = ModelFactory.create({
  url: `${Jw.licenceMicroService}/customer/create`,
  method: "post",
});

export default {
  name: "addMemberModal",
  props: ["visible"],
  data() {
    return {
      form: {
        stock: 1,
        cost: 1,
        weight: 1,
        hasTechnology: false,
        hasManufacture: false,
        hasAfterSales: false,
      },
      rules: {
        stock: [
          {
            required: true,
            message: this.$t('txt_problem_customer_name'),
            trigger: "change",
          },
        ],
        cost: [
          {
            required: true,
            message: this.$t('txt_problem_customer_code'),
            trigger: "change",
          },
        ],
        weight: [
          {
            required: true,
            message: this.$t('txt_problem_contact'),
            trigger: "change",
          },
        ],
        hasTechnology: [
          {
            required: true,
            message: this.$t('txt_problem_contact_information'),
            trigger: "change",
          },
        ],
        hasManufacture: [
          {
            required: true,
            message: this.$t('txt_problem_contact_email'),
            trigger: "change",
          },
        ],
        hasAfterSales: [
          {
            required: true,
            message: this.$t('txt_problem_contact_email'),
            trigger: "change",
          },
        ],
      },
      productList: [],
    };
  },
  mounted() {},
  methods: {
    handleSelectChange(e) {},
    onCreate() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          createMember
            .execute(this.form)
            .then((res) => {
              this.$success(this.$t("msg_save_success"));
              this.onCancel();
              this.$emit("getTableData");
            })
            .catch((err) => {
              if (err.msg) {
                this.$error(err.msg);
              }
            });
        } else {
          return false;
        }
      });
    },
    onCancel() {
      this.$refs.addForm.resetFields();
      this.$refs.addForm.clearValidate();
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
</style>
