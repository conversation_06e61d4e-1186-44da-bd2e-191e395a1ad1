<template>
	<div class="add-object">
		<div class="search-container">
			<div class="search-item">
				<a-input :placeholder="$t('search_text')" />
				<a-button style="margin-left: 9px" @click="handleCollection">
					{{ $t('txt_collecting_Objects') }}
				</a-button>
			</div>
			<object-table :dataSource="colletionObjList" @setAdded="setAdded" />
			<!-- <a-tabs default-active-key="1" @change="callback">
				<a-tab-pane key="1" tab="Part"> <object-table /> </a-tab-pane>
				<a-tab-pane key="2" tab="BOM" force-render> BOM </a-tab-pane>
				<a-tab-pane key="3" tab="CAD" force-render> CAD </a-tab-pane>
			</a-tabs> -->
		</div>
		<div class="isadded-object">
			<div class="added-title">
				{{ $t('txt_added') + '3' + $t('txt_item') }} <span class="clear-btn">{{ $t('txt_empty') }}</span>
			</div>
			<div class="added-container">
				<ul class="added-list">
					<li class="added-item" v-for="(item, index) in addList" :key="index">
						<div>
							<jw-icon type="#jwi-chengpin" />
							<span class="name">
								{{
									item.name.length > 8
										? `${item.name.substr(0, 8)}...`
										: item.name
								}}
							</span>
							, {{ item.version }}
						</div>
						<span class="clear-item">
							<jw-icon type="jwi-iconclose-circle-full" />
						</span>
					</li>
				</ul>
			</div>
		</div>
		<jw-search-engine-modal
			:title="$t('txt_add_baseline_object')"
			only-search-object
			:visible.sync="globalSearchVisible"
			:ok-btn-loading="okBtnLoading"
			:model-list="modelList"
			@ok="addObjectOk"
		/>
	</div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { jwAvatar, jwIcon, jwModalForm, jwSearchEngineModal } from "jw_frame";
import objectTable from "./object-table";
export default {
	name: "overall-Preview",
	inject: ["setBreadcrumb"],
	components: { jwIcon, objectTable, jwSearchEngineModal },
	data() {
		return {
			okBtnLoading: false,
			colletionObjList: [],
			modelList: [
				{
					name: this.$t("txt_part"),
					code: "PartIteration",
				},
				{
					name: this.$t("txt_document"),
					code: "DocumentIteration",
				},
				{
					name: "MCAD",
					code: "MCADIteration",
				},
				{
					name: "ECAD",
					code: "ECADIteration",
				},
			],
			globalSearchVisible: false, // 添加对象--搜索
			addList: [],
		};
	},
	created() {
		this.setBreadcrumb([]);
	},
	computed: {},
	mounted() {},
	methods: {
		setAdded(addList) {
			this.addList = addList;
		},
		callback(key) {
			// console.log(key);
		},
		handleCollection() {
			console.log(this);
			this.globalSearchVisible = true;
		},
		// 添加基线对象
		addObjectOk(selectedRows) {
			this.colletionObjList = [...selectedRows];
			this.globalSearchVisible = false;
		},
	},
};
</script>



<style lang="less" scoped>
/deep/.ant-tabs-nav .ant-tabs-tab {
	padding-left: 0;
	padding-right: 0;
}
.add-object {
	display: flex;
	justify-content: flex-start;
	height: 100%;
	.search-container {
		flex: 1;
		padding: 0 16px 0 0;
		border-right: 1px solid rgba(30, 32, 42, 0.15);
	}
	.isadded-object {
		width: 390px;
		padding: 0 16px;
	}
}
.search-item {
	display: flex;
	justify-content: flex-start;
}
.added-title {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	flex-wrap: wrap;
	.clear-btn {
		font-weight: 400;
		font-size: 14px;
		color: #255ed7;
		cursor: pointer;
	}
}
.added-container {
	margin-top: 16px;
	.added-list {
		height: 600px;
		overflow-y: auto;
		padding-bottom: 24px;
		.added-item {
			margin: 8px 0;
			display: flex;
			justify-content: space-between;
			padding: 0 16px;
			height: 48px;
			line-height: 48px;
			background: rgba(30, 32, 42, 0.02);
			border: 1px solid rgba(30, 32, 42, 0.15);
			border-radius: 6px;
			font-weight: 500;
			font-size: 14px;
			color: rgba(30, 32, 42, 0.65);
			.name {
				margin-left: 8px;
			}
			.clear-item {
				cursor: pointer;
			}
		}
	}
}
</style>