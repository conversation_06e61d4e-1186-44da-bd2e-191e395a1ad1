<template>
  <div class="base-panel page-container">
    <jw-table v-if="tableData" ref="table" class="product-config" :columns="columns" :data-source.sync="tableData" :pagerConfig="pagerConfig" :toolbars="toolbars" @onToolClick="onToolClick" @onToolInput="onToolInput" @onOperateClick="onOperateClick" @onPageChange="onPageChange" @onSizeChange="onSizeChange">
      <template slot="tool-after-end">
        <a-tooltip placement="top">
          <template #title>
            <span>{{ $t("btn_export") }}</span>
          </template>
          <a-button :loading="exportLoading" @click="onExport">
            <jw-icon type="jwi-iconexport" />
          </a-button>
        </a-tooltip>
      </template>
      <template #name="{ row }">
        <div class="name-wrap">
          <div class="name-con" @click="handleProblemDetail(row)">
            <jwIcon type="#jwi-linwu"></jwIcon>
            <span class="name-item">{{ row.name }}</span>
          </div>
        </div>
      </template>
      <template #proposedBySlot="{ row }">
        <jw-avatar v-if="row.proposedBy" tag show-name :data="row.proposedBy" />
      </template>
      <template #personLiableSlot="{ row }">
        <jw-avatar v-if="row.personLiable" tag show-name :data="row.personLiable" />
      </template>
      <template #createDate="{ row }">
        {{ row.records[0].createDate }}
      </template>
      <template slot="tool-before-end">
        <span>
          {{ $t("txt_problem_have_count") + pagerConfig.total + $t("txt_problem_have_count_end") }}
        </span>
      </template>
      <template #operation="{ row }">
        <a-dropdown placement="bottomRight" overlayClassName="operation-dropdown-overlay" :trigger="['click']" @visibleChange="(visible) => dropdownVisibleChange(visible, row)">
          <span class="jwi-iconellipsis" style="cursor: pointer"></span>
          <a-menu slot="overlay" @click="({ key }) => onOperateClick(key, row)">
            <a-spin v-if="row.loading_status == 'loading'" style="margin: 20px 65px" />
            <a-menu-item v-else-if="row.loading_status == 'failed'" style="margin: 20px auto">
              {{ $t("txt_get_failure") }}
            </a-menu-item>
            <template v-else v-for="(item, index) in row.operationList">
              <a-menu-divider v-if="item.permissionKey == 'delete'" :key="index" />
              <a-menu-item v-if="item.name != $t('page_model_mannange')" :key="item.code" :disabled="item.status === 'disable'">
                <span :class="item.icon" style="margin-right: 8px"></span>
                {{ $t(item.internationalizationKey) }}
              </a-menu-item>
            </template>
          </a-menu>
        </a-dropdown>
      </template>
    </jw-table>
    <create-problem :visible="visible" @close="onCloseModal" @getTableData="fetchTable"></create-problem>
    <!-- 设置状态/修订/删除 -->
    <base-color-modal :title="$t('txt_set_status')" :width="992" :visible.sync="operationVisible" dialog-class="operation-modal" :ok-text="$t('btn_ok') + $t('btn_set_s')" @ok="modalOk" :okBtnLoading="okBtnLoading" @cancel="operationCancel" :body-style="{ height: '545px', overflowY: 'scroll' }">
      <!-- 设置状态 -->
      <div class="setStatus-container" :style="{
          width:
            statusList.length * 120 > 992 - 24 * 2
              ? statusList.length * 120 + 'px'
              : '100%',
        }">
        <div v-for="(item, index) in statusList" :key="item.code" :class="[
            'item-box',
            {
              current: item.code == currentRecord.lifecycleStatus,
              active: currentRow.new_lifecycleStatus == item.code,
            },
          ]" @click="$set(currentRow, 'new_lifecycleStatus', item.code)">
          <div class="circle">
            <div v-if="index != 0" class="line left-arrow"></div>
            <div v-if="index != statusList.length - 1" class="line right-arrow"></div>
          </div>
          <div class="status">
            <a-tag style="margin: 0 auto">{{ item.displayName }}</a-tag>
          </div>
          <!-- 当前状态 -->
          <div v-if="item.code == currentRecord.lifecycleStatus" class="text">
            <span class="jwi-iconflag" style="color: #f6445a; margin-right: 4px"></span>
            {{ $t("txt_current_state") }}
          </div>
          <div v-else class="text">{{ $t("txt_set_current") }}</div>
        </div>
      </div>
    </base-color-modal>
    <start-process-modal :visible="processVisible" :pageCode="pageCode" :detailInfo="currentRow" :objectDetailsData="objectDetailsData" @close="closeProcessModal" @getTableData="fetchTable"></start-process-modal>
  </div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { jwAvatar, jwIcon, jwModalForm } from "jw_frame";
import { getCookie } from "jw_utils/cookie";
import baseColorModal from "components/base-color-modal.vue";
import createProblem from "./create-problem";
import startProcessModal from "./start-process-modal";
import Row_Store from "jw_stores/instance-info";
// 问题管理列表
const fetchContainerList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/pr/issue/search`,
  method: "post"
});

// 查询可设置的状态
const searchStatusList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/lifecycle/findByOid`,
  method: "get"
});

// 设置状态
const part_setStatus = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/pr/issue/setStatus`,
  method: "post"
});

// 问题管理查询下拉
const fetchFilterSelect = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.sysconfigServer
  }/preferences/setting/query-config-value`,
  method: "get"
});

// 获取该条数据可操作下拉列表
const getDropdownList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
  method: "post"
});

// 创建变更申请单查询
const fetchRoleChange = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.sysconfigServer
  }/preferences/setting/query-config-value`,
  method: "get"
});

// 问题管理列表导出功能
const exportIssueList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/pr/issue/export`,
  method: "get"
});

export default {
  name: "overall-Preview",
  inject: ["setBreadcrumb"],
  components: {
    jwAvatar,
    jwIcon,
    jwModalForm,
    createProblem,
    baseColorModal,
    startProcessModal
  },
  data() {
    return {
      exportLoading: false,
      configStatus: "",
      pageCode: "",
      objectDetailsData: {},
      processVisible: false,
      currentRow: {},
      currentRecord: {},
      statusList: [],
      operationVisible: false,
      okBtnLoading: false,
      visible: false,
      tableData: [],
      // 全部数据配置
      subTypes: [
        "PartIteration",
        "DocumentIteration",
        "MCADIteration",
        "ECADIteration"
      ],
      subTypesTitle: this.$t("txt_all"),
      //分页配置
      searchKey: "",
      pagerConfig: {
        current: 1,
        pageSize: 20,
        total: 0
      },
      filterSelect: [],
      searchType: "all",
      editStatus: []
    };
  },
  computed: {
    columns() {
      return [
        {
          field: "name",
          title: this.$t("txt_problem_feild_name"),
          minWidth: 150,
          slots: {
            default: "name"
          }
        },
        {
          field: "number",
          title: this.$t("txt_problem_feild_number")
        },
        {
          field: "containerName",
          title: this.$t("tabel_context")
        },
        {
          field: "cn_jwis_type",
          title: this.$t("txt_problem_type"),
          formatter: ({ row }) => {
            return this.$t(row.extensionContent?.cn_jwis_type)
          }
        },
        {
          field: "priority",
          title: this.$t("change_emergency")
        },
        {
          field: "proposedBy",
          title: this.$t("txt_problem_feild_pressent"),
          slots: {
            default: "proposedBySlot"
          }
        },
        {
          field: "personLiable",
          title: this.$t("txt_dead_user"),
          slots: {
            default: "personLiableSlot"
          }
        },
        {
          field: "proposeDate",
          title: this.$t("txt_preblem_feild_press_time")
          // formatter: "date",
        },
        {
          field: "closeDate",
          title: this.$t("txt_problem_feild_close_time"),
          formatter: "date"
        },
        {
          field: "lifecycleStatus",
          title: this.$t("txt_status"),
          cellRender: {
            name: "tag"
          }
        },
        {
          field: "operation",
          title: this.$t("txt_operation"),
          slots: {
            default: "operation"
          },
          maxShowBtn: 1,
          btns: [
            {
              icon: "jwi-iconedit",
              title: this.$t("btn_edit"),
              key: "edit"
            },
            {
              icon: "jwi-iconstate-set",
              title: this.$t("txt_set_status"),
              key: "setStatus"
            },
            {
              icon: "jwi-iconactivation",
              title: this.$t("txt_start_process"),
              key: "startProcess"
            },
            {
              icon: "jwi-iconbiangeng",
              title: this.$t("txt_problem_request"),
              key: "createChange"
            },
            {
              icon: "jwi-yanshou",
              title: this.$t("txt_problem_view_report"),
              key: "report"
            }
          ]
        }
      ];
    },
    toolbars() {
      return [
        {
          name: this.$t("btn_new_create"),
          key: "create",
          type: "primary"
        },
        {
          name: this.subTypesTitle,
          display: "dropdown",
          position: "before",
          class: "sub-types-title",
          key: "allData",
          menuList: this.filterSelect
        },
        {
          name: this.$t("btn_search"),
          position: "before",
          display: "input",
          value: this.searchKey,
          allowClear: false,
          placeholder: this.$t("search_text"),
          prefixIcon: "jwi-search",
          key: "search"
        }
      ];
    }
  },
  watch: {
    currentRecord: {
      immediate: true,
      handler(val) {
        this.currentRow = _.cloneDeep(val);
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    /**
     * 导出
     */
    onExport() {
      this.exportLoading = true;
      const accesstoken = getCookie("token");
      fetch(`${Jw.gateway}/${Jw.changeServer}/pr/issue/export`, {
        method: "post",
        body: JSON.stringify({
          tenantOid: getCookie("tenantOid")
        }),
        headers: {
          "Content-Type": "application/json;charset=utf8",
          appName: Jw.appName,
          accesstoken,
          tenantOid: getCookie("tenantOid"),
          tenantAlias: getCookie("tenantAlias")
        }
      })
        .then(response => {
          return response.blob();
        })
        .then(res => {
          let url = window.URL.createObjectURL(
            new Blob([res], {
              type: "application/vnd.ms-excel"
            })
          );
          let link = document.createElement("a");
          link.href = url;
          link.setAttribute(
            "download",
            this.$t("txt_problem_management") + ".xlsx"
          );
          document.body.appendChild(link);
          link.click();
          this.exportLoading = false;
        })
        .catch(err => {
          this.exportLoading = false;
          this.$error(err.msg || this.$t("txt_export_failure"));
        });
    },
    init() {
      this.delaySearch = _.debounce(this.reFetchData, 500);
      this.reFetchData();
      this.fetchFilterSelect();
      this.fetchRoleChange();
      this.getEditStatus();
    },
    // 查询PR_Edit_Status，查看当前是否
    getEditStatus() {
      let params = {
        name: "PR_Edit_Status"
      };
      fetchRoleChange
        .execute(params)
        .then(data => {
          console.log("editStatus---------------", data);
          this.editStatus = data.map(item => item.value);
        })
        .catch(err => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    // 获取 row  part 操作下拉列表
    dropdownVisibleChange(visible, row) {
      this.currentRow = { ...row };
      let { configStatus, editStatus } = this;
      if (visible && !row.operationList) {
        this.$set(row, "loading_status", "loading");
        getDropdownList
          .execute({
            viewCode: "ISSUEINSTANCE",
            objectOid: row.oid
          })
          .then(data => {
            data.map(item => {
              if (item.code === "createChange") {
                item.status =
                  configStatus == row.lifecycleStatus && item.status == "enable"
                    ? "enable"
                    : "disable";
              }
              if (item.code === "edit" || item.code === "setStatus" || item.code === "startProcess") {
                item.status =
                  !editStatus.includes(row.lifecycleStatus) &&
                  item.status == "enable"
                    ? "enable"
                    : "disable";
              }
              if (item.code === "report") {
                item.status =
                  row.lifecycleStatus == "Closed" && item.status == "enable"
                    ? "enable"
                    : "disable";
              }
              return item;
            });
            this.$set(row, "operationList", data);
            this.$set(row, "loading_status", "done");
          })
          .catch(err => {
            this.$error(err.msg || this.$t("msg_failed"));
            this.$set(row, "loading_status", "failed");
          });
      }
    },
    fetchFilterSelect() {
      let params = {
        name: "PR_Find_DropDown"
      };
      fetchFilterSelect
        .execute(params)
        .then(data => {
          console.log("问题管理查询下拉---------------", data);
          this.filterSelect = data.map((item, key) => {
            return { name: item.name, key: item.value };
          });
        })
        .catch(err => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    closeProcessModal() {
      this.processVisible = false;
    },
    operationCancel() {
      delete this.currentRow.new_lifecycleStatus;
      this.operationVisible = false;
    },
    modalOk() {
      const { modelDefinition, oid, type, masterType } = this.currentRecord;
      const { new_lifecycleStatus } = this.currentRow;
      this.okBtnLoading = true;
      part_setStatus
        .execute({
          modelInfo: {
            modelDefinition,
            oid,
            type
          },
          status: new_lifecycleStatus || this.currentRecord.lifecycleStatus
        })
        .then(res => {
          this.$success(this.$t("msg_success"));
          delete this.currentRow.new_lifecycleStatus;
          this.operationVisible = false;
          this.reFetchData();
        })
        .catch(err => {
          this.$error(err.msg || this.$t("msg_failed"));
        })
        .finally(() => {
          this.okBtnLoading = false;
        });
    },
    searchStatusList(row) {
      searchStatusList
        .execute({
          oid: row.lifecycleOid
        })
        .then(res => {
          this.statusList = (res || { context: {} }).context.states || [];
          this.operationVisible = true;
        })
        .catch(err => {
          console.error(err);
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    onCloseModal() {
      this.visible = false;
    },
    onStartProcess(row) {
      this.objectDetailsData = {
        id: row.containerOid,
        type: row.containerType
      };
      this.currentRecord = { ...row };
      this.processVisible = true;
    },
    reFetchData() {
      this.fetchTable({ current: 1, pageSize: 20 });
    },
    //分页操作
    onPageChange(page, pageSize) {
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;
      this.reFetchData();
    },
    onSizeChange(pageSize, page) {
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;
      this.reFetchData();
    },
    // 工具栏点击回调
    onToolClick(item) {
      if (item.key === "create") {
        this.visible = true;
      } else {
        this.subTypesTitle = item.name;
        if (item.key === "all") {
          this.subTypes = [
            "PartIteration",
            "DocumentIteration",
            "MCADIteration",
            "ECADIteration"
          ];
        } else {
          this.subTypes = [item.key];
        }
        this.pagerConfig = {
          current: 1,
          pageSize: 20,
          total: 0
        };
        this.searchType = item.key;
        this.delaySearch();
      }
    },
    // 工具栏输入回调
    onToolInput({ key }, value) {
      // console.log(value);
      if (key === "search") {
        this.searchKey = value;
        this.delaySearch();
      }
    },
    onOperateClick(item, row) {
      console.log(row);
      let key = item.code || item;
      console.log("key", key);
      this.currentRecord = { ...row };
      this.currentRow = { ...row };
      if (key === "details") {
        this.handleProblemDetail(row);
      } else if (key === "edit") {
        this.handleProblemEdit(row);
      } else if (key === "startProcess") {
        this.onStartProcess(row);
      } else if (key === "report") {
        this.handleProblemReport(row);
      } else if (key === "delete") {
        this.onDelete(row);
      } else if (key === "setStatus") {
        this.searchStatusList(row);
      } else if (key === "createChange") {
        this.handleCreateChange(row);
      }
    },
    /**
     * 获取当前变更申请单权限  需要关联的字段：
     *
     *  当前问题的状态 lifecycleStatus 和首选项中的状态都是Closed
     *  没有创建变更申请单的权限
     *
     *
     * */
    fetchRoleChange() {
      let params = {
        name: "PR_Apply_Status"
      };
      fetchRoleChange
        .execute(params)
        .then(data => {
          this.configStatus = data[0].value;
          console.log("问题变更申请单状态配置---------------", data);
        })
        .catch(err => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    handleCreateChange(row) {
      let {locationInfo}=row
      Row_Store.set(locationInfo.containerModelDefinition,{...locationInfo,containerName:row.containerName,oid:locationInfo.containerOid,type: "Container",modelDefinition:locationInfo.containerModelDefinition});
      this.$router.push({
        name: "ecr-create",
        path: `/change-management/ecr/create/${row.oid}`,
        query:{
          oid:locationInfo.containerOid,
          modelDefinition:locationInfo.containerModelDefinition,
          tabActive: "list",
          issue:1,
          name:row.name
        },
        params: {
          oid: row.oid,
        },
      });
    },
    handleProblemReport() {
      let { currentRecord } = this;
      // this.$router.push({
      // 	name: "problem-report",
      // 	path: "/problem-report",
      // 	query: {
      // 		oid: currentRecord.oid,
      // 		tabActive: "list",
      // 	},
      // });
      this.$router.push({
        name: `problem-detail`,
        path: `/problem-detail`,
        query: {
          oid: currentRecord.oid,
          tabActive: "list",
          isReport: true
        }
      });
    },
    handleProblemDetail(row) {
      this.$router.push({
        name: `problem-detail`,
        path: `/problem-detail`,
        query: {
          oid: row.oid,
          tabActive: "list"
        }
      });
    },
    handleProblemEdit(row) {
      this.$router.push({
        name: `problem-detail`,
        path: `/problem-detail`,
        query: {
          oid: row.oid,
          edit: true,
          tabActive: "list"
        }
      });
    },
    fetchTable() {
      let { searchKey, pagerConfig, searchType } = this;
      let { current, pageSize } = pagerConfig;
      let params = {
        index: current,
        size: pageSize,
        value: searchKey.trim(),
        typeEnum: searchType
      };
      return fetchContainerList
        .execute(params)
        .then(data => {
          console.log(data);
          this.tableData = data.rows;
          this.pagerConfig.total = data.count;
          return { data: data.rows || data, total: data.count };
        })
        .catch(err => {
          // this.$error(err.msg || this.$t("msg_failed"));
        });
    }
  }
};
</script>

<style lang="less" scoped>
.page-container {
  width: 100%;
  padding: 0 20px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  .toolbar > div {
    position: static !important;
    padding: 0 !important;
    &:last-child {
      display: none;
    }
  }
  div.table {
    height: 20px;
    flex-grow: 1;
  }
}
.product-config {
  width: 100%;
}
/deep/.jw-toolbar-panel .jw-toolbar-box {
  .sub-types-title {
    justify-content: space-between;
    z-index: 5;
    border-right: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  button + .sub-types-title {
    margin-right: 2px;
  }
  .sub-types-title + span {
    margin-left: -2px;
    width: 240px;
    input {
      padding-left: 10px;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}
.name-con {
  color: #255ed7;
  cursor: pointer;
}

.name-wrap {
  display: flex;
  justify-content: space-between;
  .name-con {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    .name-item {
      color: #255ed7;
      cursor: pointer;
    }
  }
}

.operation-modal {
  .setStatus-container {
    display: flex;
    justify-content: center;
    margin-top: 300px;

    .item-box {
      //   width: 120px;
      height: 142px;
      padding: 20px 12px 8px;
      border-radius: 5px;
      margin-right: 12px;
      cursor: pointer;
      transition: all 0.3s;
      flex-basis: 120px;
      border: 1px solid transparent;

      .circle {
        width: 16px;
        height: 16px;
        margin: 0 auto;
        border: 12px solid #a4c9fc;
        border-radius: 50%;
        box-sizing: content-box;
        position: relative;

        .line {
          position: absolute;
          top: 6px;
        }

        .left-arrow,
        .right-arrow {
          height: 1px;
          background: rgba(30, 32, 42, 0.15);
        }
        .left-arrow {
          width: 38px;
          left: -58px;

          &::after {
            content: "";
            display: block;
            position: absolute;
            right: -8px;
            top: -4px;
            width: 0;
            height: 0;
            border-top: 4px solid transparent;
            border-left: 8px solid rgba(30, 32, 42, 0.15);
            border-bottom: 4px solid transparent;
          }
        }
        .right-arrow {
          width: 42px;
          right: -58px;

          &::after {
            content: "";
            display: block;
            position: absolute;
            left: -8px;
            top: -4px;
            width: 8px;
            height: 8px;
            background: rgba(30, 32, 42, 0.15);
            border-radius: 50%;
          }
        }
      }
      .status {
        text-align: center;
        margin-top: 8px;
      }
      .text {
        text-align: center;
        opacity: 0;
        transition: all 0.3s;
        font-size: 12px;
        white-space: nowrap;
      }

      &:not(.current) {
        .text {
          margin-top: 10px;
          height: 32px;
          line-height: 32px;
          background: #ffffff;
          border: 1px solid rgba(30, 32, 42, 0.15);
          border-radius: 4px;
        }
      }
      &:hover {
        background: rgba(30, 32, 42, 0.02);
        border-color: rgba(30, 32, 42, 0.15);

        .text {
          opacity: 1;
        }
      }
      &.current {
        background: #f0f7ff;
        border: 1px solid #a4c9fc;

        .text {
          opacity: 1;
          margin-top: 15px;
          height: 22px;
        }
      }
      &.active {
        background: rgba(30, 32, 42, 0.02);
        border-color: rgba(30, 32, 42, 0.15);

        .text {
          opacity: 1;
        }
      }
    }
  }
}
</style>
