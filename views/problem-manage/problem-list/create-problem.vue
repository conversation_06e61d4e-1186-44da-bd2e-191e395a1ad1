<template>
  <a-modal
    :visible="visible"
    :title="$t('txt_problem_new_issue')"
    width="67%"
    :mask-closable="false"
    :footer="null"
    @cancel="onCancel"
  >
    <div class="steps-wrap">
      <div :class="['step-item', currentStep == 1 ? 'is-current' : '']">
        <div class="step-num">1</div>
        <div class="step-title">{{ $t("txt_problem_input_info") }}</div>
        <div>
          <i class="jwi-arrow-right"></i>
        </div>
      </div>
      <div :class="['step-item', currentStep == 2 ? 'is-current' : '']">
        <div class="step-num">2</div>
        <div class="step-title">{{ $t("txt_problem_add_obj") }}</div>
      </div>
      <div :class="['step-item', currentStep == 3 ? 'is-current' : '']">
        <div class="step-num">3</div>
        <div class="step-title">选择工作流模板</div>
      </div>
      <!-- <div :class="['step-item', currentStep == 3 ? 'is-current' : '']">
				<div class="step-num">3</div>
				<div class="step-title">{{ $t("txt_proble_affect_area") }}</div>
			</div> -->
    </div>
    <div class="step-body" v-show="currentStep == 1">
      <a-form-model
        ref="ref_model_form"
        :model="modelData"
        :label-position="'right'"
      >
        <a-form-model-item
          :label="$t('txt_product_rq')"
          prop="containerName"
          :rules="{
            required: true,
            message: $t('msg_select'),
            trigger: 'change',
          }"
        >
          <a-select
            v-model.trim="modelData.containerName"
            allowClear
            show-search
            option-filter-prop="children"
            :placeholder="$t('msg_select')"
            :filter-option="filterOption"
            @change="handleSelectContainer"
          >
            <a-select-option
              v-for="item in productList"
              :key="item.oid"
              :value="item.oid"
            >
              <jw-icon style="margin-right: 8px" type="#jwi-chanpin" />
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <!-- <a-form-model-item
          :label="$t('txt_problem_type')"
          prop="issueType"
          :rules="{
            required: true,
            message: $t('txt_problem_choose_type'),
            trigger: 'change',
          }"
        >
          <a-select
            v-model.trim="modelData.issueType"
            allowClear
            :placeholder="$t('msg_select')"
            @change="handleIssueType"
          >
            <a-select-option
              v-for="item in issueTypeList"
              :key="item.oid"
              :value="item.name"
            >
              {{ $t(item.name) }}
            </a-select-option>
          </a-select>
        </a-form-model-item> -->
      </a-form-model>
      <jw-layout-builder
        v-if="modelData.issueType"
        ref="ref_appBuilder"
        type="Model"
        :layoutName="'create'"
        :modelName="modelData.issueType"
        :instanceData=insData
      >
        <template #sourceSlot="{ formData }">
          <a-row :gutter="15">
            <a-col :span="12">
              <a-select
                v-model.trim="source"
                allowClear
                :placeholder="$t('msg_select')"
                @change="sourceChange"
              >
                <a-select-option
                  v-for="item in sourceList"
                  :key="item.name"
                  :value="item.name"
                >
                  {{ $t(item.name) }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :span="12">
              <a-input
                v-if="source === '其它'"
                v-model="sourceRemark"
              ></a-input>
            </a-col>
          </a-row>
        </template>
      </jw-layout-builder>
    </div>
    <div class="step-body" style="overflow-y: hidden" v-show="currentStep == 2">
      <select-table-data
        :selectedRows.sync="selectedRows"
        :defaultTableList="defaultTableList"
      />
    </div>
    <workflow-template
      v-if="currentStep == 3"
      ref="workflow"
      :currentRow="currentRow"
      :data="problemData"
    ></workflow-template>
    <div class="foot-btns">
      <a-button
        v-if="currentStep == 3 || currentStep == 2"
        class="form-foot-btn"
        @click="onPrevious(2)"
      >
        {{ $t("btn_pre_step") }}
      </a-button>
      <a-button v-if="currentStep == 1" type="primary" @click="onNextTo(2)">
        {{ $t("btn_next_step") }}
      </a-button>
      <a-button
        v-if="currentStep == 2"
        class="form-foot-btn"
        type="primary"
        @click="onNextTo(3)"
      >
        {{ $t("btn_next_step") }}
      </a-button>
      <a-button
        v-if="currentStep == 3"
        :loading="saveLoading"
        type="primary"
        class="form-foot-btn"
        @click="onStart('save')"
      >
        启动工作流
      </a-button>
      <a-button class="form-foot-btn" @click="onCancel">
        {{ $t("btn_cancel") }}
      </a-button>
    </div>
    <jw-user-modal-v2 ref="user-modal" :isCheckbox="ischeckBox" />
  </a-modal>
</template>

<script>
import {
  jwLayoutBuilder,
  jwUserModalV2,
  jwSearchEngineContent,
} from "jw_frame";
import { getCookie } from "jw_utils/cookie";
import ModelFactory from "jw_apis/model-factory";
import addObject from "./components/add-object";
import scopeInfluence from "./components/scope-influence";
import SelectTableData from "./select-table-data.vue";
import workflowTemplate from "./workflow-template";

const getSourceList = (params) =>
  ModelFactory.create({
    url: `${Jw.gateway}/${Jw.sysconfigServer}/preferences/setting/query-config-value`,
    method: "get",
  }).execute(params);

// 产品容器列表
const fetchContainerList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/container/product/searchByUser?name=`,
  method: "get",
});

// 获取所有带版本对象
const fetchAllPart = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/fuzzyPage`,
  method: "post",
});

/**
 * 获取流程模板
 */
const findWorkflow = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/model/assign/findWorkflow`,
  method: "get",
});

// 新增问题
const createProblem = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/pr/issue/issueCreate`,
  method: "post",
});

// 获取问题类型
const fetchIssue = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/model/flapTree?modelName=Issue`,
  method: "get",
});

export default {
  name: "addworkflowModal",
  components: {
    jwLayoutBuilder,
    jwSearchEngineContent,
    jwUserModalV2,
    addObject,
    scopeInfluence,
    SelectTableData,
    workflowTemplate,
  },
  props: [
    "visible",
    "pageCode",
    "detailInfo",
    "objectDetailsData",
    "selectList",
  ],
  data() {
    return {
      rangeInstanceData: {
        hasTechnology: false,
        hasManufacture: false,
        hasAfterSales: false,
      },
      insData:{proposeDate:new Date().getTime()},
      schemaRule: {
        weight: [
          {
            message: this.$t("txt_problem_valid_weight"),
            pattern: /^(0|[1-9]+[0-9]*)(.[0-9]{1,3})?$/,
            max: 13,
            min: 1,
            validator: (rule, value, cb) =>
              this.validatorNumber(value)
                ? cb(this.$t("txt_problem_valid_weight"))
                : cb(),
          },
        ],
        stock: [
          {
            message: this.$t("txt_problem_valid_correctly"),
            pattern: /^(0|[1-9]+[0-9]*)(.[0-9]{1,3})?$/,
            max: 13,
            min: 1,
            validator: (rule, value, cb) =>
              this.validatorNumber(value)
                ? cb(this.$t("txt_problem_valid_correctly"))
                : cb(),
          },
        ],
        cost: [
          {
            message: this.$t("txt_problem_valid_price"),
            pattern: /^(0|[1-9]+[0-9]*)(.[0-9]{1,3})?$/,
            max: 13,
            min: 1,
            validator: (rule, value, cb) =>
              this.validatorNumber(value)
                ? cb(this.$t("txt_problem_valid_price"))
                : cb(),
          },
        ],
      },
      //选择项
      selectedRows: [],
      //默认选项table
      defaultTableList: [],
      currentStep: 0,
      form: {
        productContainer: undefined,
        modelDefinition: "ProcessOrder",
      },
      rules: {
        productContainer: [
          {
            required: true,
            message: this.$t("txt_please_container"),
            trigger: "change",
          },
        ],
        modelDefinition: [
          {
            required: true,
            message: this.$t("txt_select_from_type"),
            trigger: "change",
          },
        ],
      },
      containerName: "",
      productList: [],
      conOpts: [],
      containerInfo: {},
      typeOpts: [],
      instanceData: {},
      searchKey: "",
      workflowList: [],
      onlyWorkflowModelId: "",
      teamTemp: undefined,
      step1Params: {},
      submitParams: {},
      ischeckBox: true,
      saveLoading: false,
      startLoading: false,
      updateLoading: false,
      updateStartLoading: false,
      modelDefinitionLoading: false,
      // 选择子模型
      modelData: {
        issueType: "Issue",
        containerName: this.$route.query.oid,
      },
      issueTypeList: [],
      currentRow: {},
      bizObjects: [],
      isCreated: false,
      source:"",
      sourceRemark: "",
      sourceList: [],
      problemData: {},
    };
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.getSourceList();
          this.getContainerList();
          this.fetchIssue();
          this.currentStep = 1;
        }
      },
      immediate: true,
    },
    currentStep: {
      async handler(val) {
        if (val == 1) {
        } else if (val == 2) {
        } else if (val == 3) {
        }
      },
      immediate: true,
    },
    selectList: {
      handler(val) {
        this.defaultTableList = val;
      },
      immediate: true,
    },
    selectedRows(val) {
      this.currentRow = val[0] || {};
    },
  },
  computed: {
    workflowListData() {
      if (this.onlyWorkflowModelId) {
        return this.workflowList.filter(
          (p) => p.id == this.onlyWorkflowModelId
        );
      } else {
        return this.workflowList || [];
      }
    },
  },
  filters: {
    filterName: function (name) {
      if (!name) {
        return name;
      }
      // 是否含有中文
      const hasCh = /[\u4E00-\u9FA5]/.test(name);
      let showName = "";
      if (hasCh) {
        // 用户 含有中文取后两个字符
        showName = name.slice(-2);
      } else {
        // 没有中文
        showName = name.slice(-2).toLocaleUpperCase();
      }
      return showName;
    },
  },
  created() {
    // this.getSourceList();
  },
  mounted() {},
  methods: {
    sourceChange() {
      this.sourceRemark = "";
    },
    getSourceList() {
      getSourceList({ name: "problemSource" }).then((res) => {
        this.sourceList = res || [];
      });
    },
    changeBuild(data) {
      this.$nextTick(() => {
        let el = document.querySelectorAll(".cn_jwis_type")[0];
        let label = document.querySelectorAll(".cn_jwis_type label")[0];
        if (el) {
          if (data.source === "其它") {
            el.style.display = "block";
          } else {
            el.style.display = "none";
          }
          label.style.opacity = 0;
        }
      });
    },
    validatorNumber(value) {
      let numberExp = /^(0|[1-9]+[0-9]*)(.[0-9]{1,3})?$/;
      if (
        value.length > 10 ||
        value <= 0 ||
        isNaN(value) ||
        !numberExp.test(value)
      ) {
        return true;
      } else {
        return false;
      }
    },
    hasChange(e, key) {
      this[key] = e.target.value;
    },
    filterOption: (input, option) => {
      return (
        option.componentOptions.children[1].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    fetchIssue() {
      fetchIssue
        .execute()
        .then((data) => {
          this.issueTypeList = data;
        })
        .catch((err) => {
          this.$error(err.msg);
        });
    },
    handleSelectContainer(productOid) {
      console.log(productOid);
      this.modelData.containerName = productOid || "";
    },
    handleIssueType(value) {
      console.log(value);
      this.modelData.issueType = value || "";
      console.log(this.modelData);
    },
    getContainerList() {
      fetchContainerList
        .execute({
          index: 1,
          size: 1000,
          searchKey: "",
        })
        .then((data) => {
          this.productList = data;
        })
        .catch((err) => {
          this.$error(err.msg);
        });
    },
    onChangeConList(val) {
      this.containerInfo = this.conOpts.find((item) => item.oid === val) || {};
    },
    onNextTo(val) {
      // 校验问题基本信息
      if (val == 2) {
        let { containerName, $refs } = this;
        let ref_appBuilder = $refs.ref_appBuilder;
        let modelForm = this.$refs.ref_model_form;
        modelForm
          .validate()
          .then(() => {
            ref_appBuilder &&
              ref_appBuilder
                .validate()
                .then((res) => {
                  this.currentStep = 2;
                })
                .catch((err) => {
                  console.log(err);
                });
          })
          .catch((err) => {});
      }
      if (val == 3) {
        let { selectedRows } = this;
        if (selectedRows.length == 0) {
          this.$error(this.$t("txt_problem_add_obj_pl"));
        } else {
          let noRel = this.selectedRows.find((item) => {
            return item.lifecycleStatus != "Released" && item.lifecycleStatus != "released";
          });
          if (noRel) {
            return this.$error(
              `${noRel.number}数据状态为未发布，不能提交问题报告`
            );
          }
          this.handleSubmit();
        }
      }
    },
    onStart() {
      this.saveLoading = true;
      this.$refs.workflow
        .onStart("start")
        .then(() => {
          this.saveLoading = false;
          this.$emit("getTableData");
          this.saveLoading = false;
          this.onCancel();
        })
        .catch((err) => {
          this.saveLoading = false;
        });
    },
    onPrevious(val) {
      let currentStep = this.currentStep;
      this.currentStep = currentStep == 3 ? 2 : 1;
    },
    onSave(flag) {
      // let ref_appBuilder_schema = this.$refs.ref_appBuilder_schema;
      // ref_appBuilder_schema
      //   .validate()
      //   .then(res => {
      //     this.handleSubmit();
      //   })
      //   .catch(err => {
      //     console.log(err);
      //   });
    },
    handleSubmit() {
      if (this.isCreated) {
        this.currentStep = 3;
        return;
      }
      let {
        $refs: { scopeInfluence, addObject, ref_appBuilder, workflow },
        selectedRows,
        modelData: { containerName, issueType },
      } = this;
      let value = ref_appBuilder.getValue();
      // let valueSchema = ref_appBuilder_schema.getValue();
      let { productList } = this;
      let containerInfo = productList.filter(
        (item) => item.oid == containerName
      )[0];
      let locationInfo = {};
      if (containerInfo) {
        (containerName = containerInfo.name || ""),
          (locationInfo = {
            containerOid: containerInfo.folder.containerOid,
            containerType: containerInfo.folder.containerType,
            containerModelDefinition:
              containerInfo.folder.containerModelDefinition,
            catalogOid: containerInfo.folder.catalogOid,
            catalogType: containerInfo.folder.catalogType,
          });
      }
      if(value.extensionContent){
        value.extensionContent.source=this.sourceRemark||this.source
      }else{
        value.extensionContent={
          source:this.sourceRemark||this.source
        }
      }
      let params = {
        issueType,
        containerName,
        ...value,
        locationInfo,
        issueList: selectedRows,
      };
      // 提交参数
      createProblem
        .execute(params)
        .then((data) => {
          this.isCreated = true;
          this.currentStep = 3;
          this.problemData = data || {};
          console.log(data, "prodedd");
          this.$emit("getTableData");
        })
        .catch((err) => {
          this.$error(err.msg);
        });
    },
    onCancel() {
      let {
        $refs: { scopeInfluence, addObject, ref_appBuilder },
      } = this;
      let user = { name: Jw.getUser().name, account: Jw.getUser().account };
      this.currentStep = 0;
      this.step1Params = {};
      ref_appBuilder && ref_appBuilder.clear();
      ref_appBuilder &&
        ref_appBuilder.setValue({
          proposedBy: user,
          personLiable: user,
          proposeDate:new Date().getTime()
        });
      // scopeInfluence.form = {};
      this.selectedRows = [];
      this.defaultTableList = [];
      this.source=""
      this.sourceRemark=""
      this.isCreated = false;
      if (!this.$route.query.oid) {
        this.containerName = "";
        this.modelData.containerName = "";
      } else {
        this.modelData.containerName = this.$route.query.oid;
      }
      this.$emit("close");
    },
    findWorkflow() {
      let params = {
        containerOid: getCookie("tenantOid"),
        modelCode: this.detailInfo.modelDefinition || this.detailInfo.type,
      };
      findWorkflow
        .execute(params)
        .then((res) => {
          if (res) {
            this.onlyWorkflowModelId = res.workflowModelId;
          }
        })
        .catch((err) => {
          this.$error(err.msg);
        })
        .finally(() => {
          this.onNextTo3();
        });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.ant-form-vertical .is-validating .ant-form-explain {
  display: none;
}
/deep/.layout-grid-generated {
  width: 100%;
}
.steps-wrap {
  display: flex;
  .step-item {
    display: flex;
    justify-content: center;
    width: 50%;
    padding: 4px 0;
    background: rgba(30, 32, 42, 0.04);
    .step-num {
      width: 24px;
      height: 24px;
      line-height: 24px;
      margin-right: 8px;
      text-align: center;
      color: #9ca1ad;
      background: #c6cbd7;
      border-radius: 50%;
    }
    .step-title {
      line-height: 24px;
    }
    &.is-current {
      background: #a4c9fc;
      .step-num {
        color: #fff;
        background: #265dd8;
      }
      .step-title {
        color: #505d77;
      }
    }
  }
}
.step-body {
  height: 500px;
  margin-top: 20px;
  overflow-x: hidden;
  overflow-y: auto;
}
.step-body3 {
  display: flex;
  .step3-left {
    width: 30%;
    padding-right: 2px;
    .search-input {
      width: 60%;
    }
    .radio-wrap {
      height: 460px;
      margin-top: 8px;
      overflow: auto;
    }
    .ant-radio-group {
      width: 100%;
    }
    .ant-radio-wrapper {
      display: block;
      line-height: 32px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .step3-right {
    width: 70%;
    padding: 0 2px 0 16px;
    color: rgba(30, 32, 42, 0.85);
    border-left: 1px solid rgba(30, 32, 42, 0.15);
    overflow: auto;
    .right-title {
      font-size: 16px;
      color: rgba(30, 32, 42, 0.85);
      font-weight: 700;
    }
    .right-tip {
      margin: 5px 0;
      font-size: 12px;
      color: rgba(30, 32, 42, 0.45);
    }
    .right-process-img {
      width: 100%;
      height: 300px;
      background: rgba(30, 32, 42, 0.04);
      border: 1px solid rgba(30, 32, 42, 0.15);
      border-radius: 5px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .right-temp-wrap {
      margin: 15px 0;
      .ant-select {
        width: 60%;
        margin-top: 8px;
      }
    }
    .right-handler-wrap {
      .handler-item {
        margin-bottom: 16px;
        .item-head {
          margin-bottom: 8px;
          i {
            margin-left: 8px;
          }
        }
        .item-body {
          display: flex;
        }
        .handler-add-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          min-width: 32px;
          height: 32px;
          margin-right: 8px;
          background: #f0f7ff;
          border: 1px solid #a4c9fc;
          border-radius: 50%;
          cursor: pointer;
        }
        .handlers {
          display: flex;
          flex-wrap: wrap;
        }
        .handler {
          position: relative;
          display: flex;
          align-items: center;
          padding: 3px 8px;
          margin: 0 8px 8px 0;
          background: rgba(30, 32, 42, 0.04);
          border: 1px solid transparent;
          border-radius: 4px;
          cursor: pointer;
          .close-icon {
            position: absolute;
            top: -13px;
            right: -8px;
            visibility: hidden;
          }
          .avatar {
            width: 24px;
            height: 24px;
            margin-right: 8px;
            img {
              width: 100%;
              height: 100%;
              border-radius: 50%;
            }
            .ant-avatar {
              width: 24px;
              height: 24px;
              line-height: 24px;
            }
          }
          &:hover {
            background: #f0f7ff;
            border: 1px solid #a4c9fc;
            .close-icon {
              visibility: visible;
            }
          }
        }
      }
    }
  }
}
.foot-btns {
  margin: 15px 0 0;
  text-align: right;
  .form-foot-btn {
    margin-left: 5px;
  }
}
</style>
