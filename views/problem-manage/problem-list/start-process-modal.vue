<template>
  <a-modal
    :visible="visible"
    :title="$t('txt_initiating_workflow')"
    width="80%"
    :mask-closable="false"
    :footer="null"
    @cancel="onCancel"
  >
    <div class="steps-wrap">
      <div :class="['step-item', currentStep == 2 ? 'is-current' : '']">
        <div class="step-num">1</div>
        <div class="step-title">{{ $t("txt_select_work") }}</div>
        <div><i class="jwi-arrow-right"></i></div>
      </div>
      <div :class="['step-item', currentStep == 3 ? 'is-current' : '']">
        <div class="step-num">2</div>
        <div class="step-title">{{ $t("txt_select_temp") }}</div>
      </div>
    </div>
    <div class="step-body" v-show="currentStep == 1">
      <a-form-model
        ref="addForm"
        class="form-container"
        layout="vertical"
        :model="form"
        :rules="rules"
      >
        <a-row :gutter="10">
          <a-col :span="12" v-if="pageCode === 'myProcess'">
            <a-form-model-item
              :label="$t('txt_product_rq')"
              prop="productContainer"
            >
              <a-select
                v-model.trim="form.productContainer"
                :placeholder="$t('msg_select')"
                @focus="getContainerList"
                @change="onChangeConList"
              >
                <a-select-option
                  v-for="val in conOpts"
                  :key="val.oid"
                  :value="val.oid"
                >
                  {{ val.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item
              :label="$t('txt_from_type')"
              prop="modelDefinition"
            >
              <a-select
                v-model.trim="form.modelDefinition"
                :placeholder="$t('msg_select')"
                @focus="getSubModel"
                :loading="modelDefinitionLoading"
              >
                <a-select-option
                  v-for="val in typeOpts"
                  :key="val.oid"
                  :value="val.name"
                >
                  {{ val.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <jw-layout-builder
        v-if="visible && form.modelDefinition"
        ref="ref_appBuilder"
        type="Model"
        :layoutName="'create'"
        :modelName="form.modelDefinition"
        :instanceData="instanceData"
      >
      </jw-layout-builder>
    </div>
    <div class="step-body" v-show="currentStep == 2">
      <!-- <jw-search-engine-content
				ref="addObjectContent"
				:pageCode="pageCode"
				:onlySearchObject="true"
				:model-list="modelList"
			/> -->
      <select-table-data
        :showAdd="false"
        :selectedRows.sync="selectedRows"
        :defaultTableList="defaultTableList"
      />
    </div>
    <div class="step-body step-body3" v-show="currentStep == 3">
      <div class="step3-left">
        <a-input-search
          v-model.trim="searchKey"
          class="search-input"
          allow-clear
          :placeholder="$t('search_text')"
          @search="onSearch"
        />
        <div class="radio-wrap">
          <a-radio-group v-model.trim="selectWorkflow" @change="onChangeRadio">
            <a-radio
              v-for="item in workflowListData"
              :key="item.key"
              :value="item"
            >
              {{ item.name }}
            </a-radio>
          </a-radio-group>
        </div>
      </div>
      <div class="step3-right">
        <div class="right-title">{{ selectWorkflow.name }}</div>
        <div class="right-tip">{{ selectWorkflow.metaInfo }}</div>
        <div class="right-process-img">
          <img :src="workflowImageUrl" alt="" />
        </div>
        <!-- <div class="right-temp-wrap">
          <div>{{ $t("txt_team_temp") }}</div>
          <a-select
            v-model.trim="teamTemp"
            :placeholder="$t('msg_select')"
            show-search
            :filter-option="filterOption"
            allowClear
            @change="getRoleWithUser"
          >
            <a-select-option
              v-for="val in teamTempOpts"
              :key="val.oid"
              :value="val.oid"
            >
              {{ val.name }}
            </a-select-option>
          </a-select>
        </div> -->
        <div class="right-handler-wrap" v-show="variableList.length > 0">
          <div
            class="handler-item"
            v-for="item in variableList"
            :key="item.name"
          >
            <div class="item-head">
              {{ item.displayName }}<i class="jwi-iconrefresh"></i>
            </div>
            <div class="item-body">
              <div class="handler-add-btn" @click="onAddUser(item)">
                <i class="jwi-iconuser-add"></i>
              </div>
              <div class="handlers" v-show="!!item.users.length">
                <div
                  class="handler"
                  v-for="val in item.users"
                  :key="val.account"
                >
                  <i
                    class="jwi-iconclose-circle-full close-icon"
                    @click="onDeleteUser(item.users, val)"
                  ></i>
                  <div class="avatar">
                    <img v-if="val.avatar" :src="val.avatar" alt="" />
                    <a-avatar v-else>{{ val.name | filterName }}</a-avatar>
                  </div>
                  <div>{{ val.name }}（{{ val.account }}）</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="foot-btns">
      <a-button v-if="currentStep == 1" type="primary" @click="onNextTo2">{{
        $t("btn_next_step")
      }}</a-button>

      <!-- <a-button v-if="currentStep==2" 
                      class="form-foot-btn"
                      @click="onPrevious(1)" 
                      :disabled="pageCode === 'objectProcess'">{{$t('btn_pre_step')}} </a-button> -->
      <a-button
        v-if="currentStep == 2"
        class="form-foot-btn"
        type="primary"
        @click="onNextTo3"
        >{{ $t("btn_next_step") }}
      </a-button>

      <a-button
        v-if="currentStep == 3"
        class="form-foot-btn"
        @click="onPrevious(2)"
        :disabled="pageCode === 'objectProcess'"
        >{{ $t("btn_pre_step") }}
      </a-button>
      <a-button
        v-if="currentStep == 3 && !detailInfo.oid"
        :loading="saveLoading"
        class="form-foot-btn"
        @click="onSave('save')"
        >{{ $t("txt_save_drafts") }}</a-button
      >
      <a-button
        v-if="currentStep == 3 && !detailInfo.oid"
        :loading="startLoading"
        class="form-foot-btn"
        type="primary"
        @click="onStart('start')"
        >{{ $t("txt_start_process") }}</a-button
      >

      <a-button
        v-if="currentStep == 3 && detailInfo.oid"
        :loading="updateLoading"
        class="form-foot-btn"
        @click="onSave('update')"
        :disabled="pageCode === 'objectProcess'"
        >{{ $t("txt_save_drafts") }}</a-button
      >
      <a-button
        v-if="currentStep == 3 && detailInfo.oid"
        :loading="updateStartLoading"
        class="form-foot-btn"
        type="primary"
        @click="onStart('updateStart')"
        >{{ $t("txt_start_process") }}</a-button
      >

      <a-button class="form-foot-btn" @click="onCancel">{{
        $t("btn_cancel")
      }}</a-button>
    </div>
    <jw-user-modal-v2 ref="user-modal" :isCheckbox="ischeckBox" />
  </a-modal>
</template>

<script>
import {
  jwLayoutBuilder,
  jwUserModalV2,
  jwSearchEngineContent,
} from "jw_frame";
import { getCookie } from "jw_utils/cookie";
import ModelFactory from "jw_apis/model-factory";
import SelectTableData from "./select-table-data.vue";

// 产品容器列表
const fetchContainerList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/container/product/search`,
  method: "post",
});

// 获取申请单类型
const fetchSubModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/model/flapSubTree`,
  method: "get",
});

// 获取所有带版本对象
const fetchAllPart = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/fuzzyPage`,
  method: "post",
});

// 查询已部署的最新流程模板
// const fetchDeployed = ModelFactory.create({
// 	url: `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/model/deployed/latest/fuzzy`,
// 	method: "get",
// });
const fetchDeployed = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/workflow-micro/workflow/model/deployed/latest`,
  method: "post",
});

// 获取流程图
const fetchProcessImage = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/repository/process-definitions/image/byKey`,
  method: "get",
});

// 获取团队
const fetchTeam = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/pdmFindAllTeam`,
  method: "get",
});

// 获取需要配置的角色
const fetchVariables = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/repository/start/variables`,
  method: "get",
});

// 获取角色对应的用户
const fetchRoleWithUser = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/templaterole/search/roleWithUser`,
  method: "get",
});
const findAutoRole = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/process/team/autoUser`,
  method: "post"
});
// 保存草稿
const createProcess = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/create`,
  method: "post",
});

// 创建并启动流程
const createThenStartProcess = ModelFactory.create({
  url: `${Jw.gateway}/customer/ding/task/createSendTask`,
  method: "post",
});

// 更新流程单
const updateProcess = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/updateAll`,
  method: "post",
});

// 更新并启动流程
const updateThenStartProcess = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/updateAllThenStart`,
  method: "post",
});

// 获取流程单详情
const fetchDetails = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/findDetail`,
  method: "get",
});

/**
 * 获取流程模板
 */
const findWorkflow = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/model/assign/findWorkflow`,
  method: "get",
});
export default {
  name: "addworkflowModal",
  components: {
    jwLayoutBuilder,
    jwSearchEngineContent,
    jwUserModalV2,
    SelectTableData,
  },
  props: [
    "visible",
    "pageCode",
    "detailInfo",
    "objectDetailsData",
    "batchProcess",
  ],
  data() {
    return {
      //选择项
      selectedRows: this.detailInfo.issueList || [],
      //默认选项table
      defaultTableList: this.detailInfo.issueList || [],
      currentStep: 0,
      form: {
        productContainer: undefined,
        modelDefinition: "ProcessOrder",
      },
      rules: {
        productContainer: [
          {
            required: true,
            message: this.$t("txt_please_container"),
            trigger: "change",
          },
        ],
        modelDefinition: [
          {
            required: true,
            message: this.$t("txt_select_from_type"),
            trigger: "change",
          },
        ],
      },
      conOpts: [],
      containerInfo: {},
      typeOpts: [],
      modelList: [
        {
          name: this.$t("txt_part"),
          code: "PartIteration",
        },
        {
          name: this.$t("txt_document"),
          code: "DocumentIteration",
        },
        {
          name: "MCAD",
          code: "MCADIteration",
        },
        {
          name: "ECAD",
          code: "ECADIteration",
        },
        {
          name: this.$t("txt_baseline"),
          code: "Baseline",
        },
      ],
      instanceData: {},
      searchKey: "",
      workflowList: [],
      selectWorkflow: {},
      workflowImageUrl: "",
      onlyWorkflowModelId: "",
      teamTemp: undefined,
      teamTempOpts: [],
      variableList: [],
      step1Params: {},
      submitParams: {},
      ischeckBox: true,
      saveLoading: false,
      startLoading: false,
      updateLoading: false,
      updateStartLoading: false,
      modelDefinitionLoading: false,
    };
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.defaultTableList = this.detailInfo.issueList;
          if (this.pageCode === "objectProcess") {
            this.form.modelDefinition = "ProcessOrder";
            this.step1Params.name = this.detailInfo.name;
            this.findWorkflow();
          }
          if (this.detailInfo && this.detailInfo.oid) {
            this.currentStep = 3;
          } else {
            this.currentStep = 2;
          }
        }
      },
      immediate: true,
    },
    currentStep: {
      async handler(val) {
        if (val == 1) {
          this.getContainerList();
          this.getSubModel();
        } else if (val == 2) {
          // this.$nextTick(() => {
          // 	this.$refs.addObjectContent.init();
          // });
        } else if (val == 3) {
          await this.getDeployed();
          this.getTeam();
          if (
            this.detailInfo &&
            this.detailInfo.oid &&
            this.pageCode !== "objectProcess"
          ) {
            // this.getDetails(this.detailInfo.oid);
          } else {
            this.getVariables();
          }
        }
      },
      immediate: true,
    },
  },
  computed: {
    workflowListData() {
      if (this.onlyWorkflowModelId) {
        return this.workflowList.filter(
          (p) => p.id == this.onlyWorkflowModelId
        );
      } else {
        return this.workflowList || [];
      }
    },
  },
  filters: {
    filterName: function (name) {
      if (!name) {
        return name;
      }
      // 是否含有中文
      const hasCh = /[\u4E00-\u9FA5]/.test(name);
      let showName = "";
      if (hasCh) {
        // 用户 含有中文取后两个字符
        showName = name.slice(-2);
      } else {
        // 没有中文
        showName = name.slice(-2).toLocaleUpperCase();
      }
      return showName;
    },
  },
  mounted() {},
  methods: {
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    async getDetails(oid) {
      let res = await fetchDetails.execute({ oid: oid });
      this.form.productContainer = res.containerOid;
      this.form.modelDefinition = res.modelDefinition;
      this.instanceData = res;
      this.step1Params = res;
      this.containerInfo = {
        oid: res.containerOid,
        type: res.containerType,
      };
      this.$refs.addObjectContent.selectedRows = res.bizObjects;
      this.selectWorkflow = this.workflowListData.find(
        (item) => item.id == res.processModelId
      );
      this.workflowImageUrl = `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/repository/process-definitions/image/byKey?key=${this.selectWorkflow.key}&tenantId=${this.selectWorkflow.tenantId}&appName=${Jw.appName}`;
      this.variableList = res.teamRoleWithUsers;
    },
    getContainerList() {
      fetchContainerList
        .execute({
          index: 1,
          size: 1000,
          searchKey: "",
        })
        .then((data) => {
          this.conOpts = data.rows;
        })
        .catch((err) => {
          this.$error(err.msg);
        });
    },
    onChangeConList(val) {
      this.containerInfo = this.conOpts.find((item) => item.oid === val) || {};
    },
    getSubModel() {
      this.modelDefinitionLoading = true;
      fetchSubModel
        .execute({
          modelName: "ProcessOrder",
        })
        .then((res) => {
          this.typeOpts = res;
        })
        .catch((err) => {
          this.$error(err.msg);
        })
        .finally(() => {
          this.modelDefinitionLoading = false;
        });
    },
    searchModelTable(params) {
      return fetchAllPart.execute(params);
    },
    async getDeployed() {
      let res = await fetchDeployed.execute({
        searchKey: this.searchKey,
        models: [
          { modelType: this.detailInfo.modelDefinition, status: this.detailInfo.lifecycleStatus },
        ],
      });
      //如果不是变更申请 过滤掉变更流程
      if (this.detailInfo.type != "ECR" && !_.isEmpty(res)) {
        res = res.filter((item) => !item.name.includes("变更"));
      }
      this.workflowList = res;
      if (this.workflowListData.length > 0) {
        this.selectWorkflow = this.workflowListData[0];
        this.workflowImageUrl = `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/repository/process-definitions/image/byKey?key=${this.selectWorkflow.key}&tenantId=${this.selectWorkflow.tenantId}&appName=${Jw.appName}`;
        this.getVariables();
      } else {
        this.selectWorkflow = {
          name: "",
        };
      }
    },
    getTeam() {
      fetchTeam
        .execute({
          containerOid: getCookie("tenantOid"),
          containerType: "Tenant",
          keyword: "",
          pageNum: 1,
          pageSize: 1000,
        })
        .then((res) => {
          this.teamTempOpts = res;
        })
        .catch((err) => {
          this.$error(err.msg);
        });
    },
    getVariables() {
      fetchVariables
        .execute({
          deploymentId: this.selectWorkflow.deploymentId,
        })
        .then((res) => {
          this.variableList = res.map((item) => {
            if (item.name === "owner") {
              item.users = [Jw.getUser()];
            } else {
              item.users = [];
            }
            return item;
          });
          this.findAutoRoleAct()
        })
        .catch((err) => {
          this.$error(err.msg);
        });
    },
    getRoleWithUser() {
      if (!this.teamTemp) {
        this.variableList.forEach((item) => {
          item.users = [];
        });
        return;
      }
      fetchRoleWithUser
        .execute({
          teamTemplateOid: this.teamTemp,
        })
        .then((res) => {
          this.variableList.forEach((item) => {
            const info = res.find((val) => val.name == item.name);
            item.users = info ? info.users : [];
          });
        })
        .catch((err) => {
          this.variableList = [];
          this.$error(err.msg);
        });
    },
    onNextTo2() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          let appBuilder = this.$refs.ref_appBuilder;
          appBuilder &&
            appBuilder.validate().then(() => {
              let params = appBuilder.getValue();
              this.step1Params = params;
              this.currentStep = 2;
            });
        } else {
          return false;
        }
      });
    },
    onNextTo3() {
      this.currentStep = 3;
    },
    onPrevious(val) {
      this.currentStep = val;
    },
    onSave(flag) {
      this.getParams();
      let api = createProcess;
      // if (this.detailInfo.oid && this.pageCode !== "objectProcess") {
      // 	this.submitParams.oid = this.detailInfo.oid;
      // 	api = updateProcess;
      // } else {
      // 	api = createProcess;
      // }
      this[flag + "Loading"] = true;
      api
        .execute(this.submitParams)
        .then((res) => {
          this.$success(this.$t("msg_success"));
          this.$emit("getTableData");
          this.onCancel();
          this[flag + "Loading"] = false;
        })
        .catch((err) => {
          this[flag + "Loading"] = false;
          this.$error(err.msg);
        });
    },
    onStart(flag) {
      this.getParams();
      let userFlag = this.submitParams.teamContent.every((item) => {
        return item.users.length > 0;
      });
      if (!userFlag) {
        this.$warning(this.$t("txt_add_users"));
        return;
      }
      let api = createThenStartProcess;
      // if (this.detailInfo.oid && this.pageCode !== "objectProcess") {
      // 	this.submitParams.oid = this.detailInfo.oid;
      // 	api = updateThenStartProcess;
      // } else {
      // 	api = createThenStartProcess;
      // }
      this[flag + "Loading"] = true;

      //是否是批量操作
      if (this.batchProcess) {
        this.$emit("submit", this.submitParams);
      } else {
        api
          .execute(this.submitParams)
          .then((res) => {
            this.$success(this.$t("msg_success"));
            this.$emit("getTableData", { current: 1, pageSize: 20 });
            this.onCancel();
            this[flag + "Loading"] = false;
          })
          .catch((err) => {
            this[flag + "Loading"] = false;
            this.$error(err.msg);
          });
      }
    },
    getParams() {
      let teamContent = this.variableList.map((item) => {
        return {
          roleName: item.name,
          name: item.displayName,
          users: item.users.map((val) => val.account),
          userAccounts:item.users.map((val) => val.account)
        };
      });
      let id = "",
        type = "";
      // if (this.pageCode === "processManage") {
      // 	id = this.$route.query.oid;
      // 	type = this.$route.query.type;
      // } else if (this.pageCode === "objectProcess") {
      // 	id = this.detailInfo.containerOid;
      // 	type = this.detailInfo.containerType;
      // } else if (this.pageCode === "myProcess") {
      // 	id = this.containerInfo.oid;
      // 	type = this.containerInfo.type;
      // } else if (this.pageCode === "processRecord") {
      // 	id = this.objectDetailsData.containerOid;
      // 	type = this.objectDetailsData.containerType;
      // }
      let { detailInfo } = this;
      this.submitParams = {
        locationInfo: {
          catalogOid: detailInfo.catalogOid,
          catalogType: detailInfo.catalogType || "Folder",
          containerOid: detailInfo.containerOid,
          containerType: detailInfo.containerType,
        },
        modelDefinition: this.form.modelDefinition,
        bizObjects: [this.detailInfo],
        processModelId: this.selectWorkflow.id,
        teamContent: teamContent,
        name: this.selectWorkflow.name,
        extensionContent: {
          docInfo: {},
        },
        // ...this.step1Params,
      };
    },
    onSearch() {
      this.getDeployed();
    },
    onChangeRadio() {
      this.teamTemp = undefined;
      this.variableList = [];
      this.workflowImageUrl = `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/repository/process-definitions/image/byKey?key=${this.selectWorkflow.key}&tenantId=${this.selectWorkflow.tenantId}&appName=${Jw.appName}`;
      this.getVariables();
    },
    onAddUser(item) {
      this.$refs["user-modal"]
        .show({
          type: "User",
        })
        .then((data) => {
          let oids = item.users.map((item) => item.oid);
          data.forEach((val) => {
            if (!oids.includes(val.oid)) {
              item.users.push(val);
            }
          });
        });
    },
    findAutoRoleAct() {
      findAutoRole.execute([this.detailInfo]).then(resp => {
        this.variableList.forEach(row => {
          let role = resp.find(item => item.name === row.name);
          if (role && _.isEmpty(row.users)) {
            row.users = role.users;
          } 
        });
      });
    },
    onDeleteUser(list, item) {
      let index = list.indexOf(item);
      list.splice(index, 1);
    },
    onCancel() {
      this.currentStep = 0;
      this.step1Params = {};
      this.instanceData = {};
      this.$refs.addForm && this.$refs.addForm.resetFields();
      this.$refs.addForm && this.$refs.addForm.clearValidate();
      this.$refs.ref_appBuilder && this.$refs.ref_appBuilder.clearValidate();
      // this.$refs.addObjectContent.reset();
      this.teamTemp = undefined;
      this.variableList = [];
      this.submitParams = {};
      this.$emit("close");
    },
    findWorkflow() {
      let params = {
        containerOid: getCookie("tenantOid"), // this.detailInfo.containerOid
        modelCode: this.detailInfo.modelDefinition || this.detailInfo.type,
      };
      findWorkflow
        .execute(params)
        .then((res) => {
          if (res) {
            this.onlyWorkflowModelId = res.workflowModelId;
          }
        })
        .catch((err) => {
          this.$error(err.msg);
        })
        .finally(() => {
          this.onNextTo3();
        });
    },
  },
};
</script>

<style lang="less" scoped>
.steps-wrap {
  display: flex;
  .step-item {
    display: flex;
    justify-content: center;
    width: 50%;
    padding: 4px 0;
    background: rgba(30, 32, 42, 0.04);
    .step-num {
      width: 24px;
      height: 24px;
      line-height: 24px;
      margin-right: 8px;
      text-align: center;
      color: #9ca1ad;
      background: #c6cbd7;
      border-radius: 50%;
    }
    .step-title {
      line-height: 24px;
    }
    &.is-current {
      background: #a4c9fc;
      .step-num {
        color: #fff;
        background: #265dd8;
      }
      .step-title {
        color: #505d77;
      }
    }
  }
}
.step-body {
  height: 500px;
  margin-top: 20px;
  overflow-x: hidden;
  overflow-y: auto;
}
.step-body3 {
  display: flex;
  .step3-left {
    width: 30%;
    padding-right: 2px;
    .search-input {
      width: 60%;
    }
    .radio-wrap {
      height: 460px;
      margin-top: 8px;
      overflow: auto;
    }
    .ant-radio-group {
      width: 100%;
    }
    .ant-radio-wrapper {
      display: block;
      line-height: 32px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .step3-right {
    width: 70%;
    padding: 0 2px 0 16px;
    color: rgba(30, 32, 42, 0.85);
    border-left: 1px solid rgba(30, 32, 42, 0.15);
    overflow: auto;
    .right-title {
      font-size: 16px;
      color: rgba(30, 32, 42, 0.85);
      font-weight: 700;
    }
    .right-tip {
      margin: 5px 0;
      font-size: 12px;
      color: rgba(30, 32, 42, 0.45);
    }
    .right-process-img {
      width: 100%;
      height: 300px;
      background: rgba(30, 32, 42, 0.04);
      border: 1px solid rgba(30, 32, 42, 0.15);
      border-radius: 5px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .right-temp-wrap {
      margin: 15px 0;
      .ant-select {
        width: 60%;
        margin-top: 8px;
      }
    }
    .right-handler-wrap {
      .handler-item {
        margin-bottom: 16px;
        .item-head {
          margin-bottom: 8px;
          i {
            margin-left: 8px;
          }
        }
        .item-body {
          display: flex;
        }
        .handler-add-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          min-width: 32px;
          height: 32px;
          margin-right: 8px;
          background: #f0f7ff;
          border: 1px solid #a4c9fc;
          border-radius: 50%;
          cursor: pointer;
        }
        .handlers {
          display: flex;
          flex-wrap: wrap;
        }
        .handler {
          position: relative;
          display: flex;
          align-items: center;
          padding: 3px 8px;
          margin: 0 8px 8px 0;
          background: rgba(30, 32, 42, 0.04);
          border: 1px solid transparent;
          border-radius: 4px;
          cursor: pointer;
          .close-icon {
            position: absolute;
            top: -13px;
            right: -8px;
            visibility: hidden;
          }
          .avatar {
            width: 24px;
            height: 24px;
            margin-right: 8px;
            img {
              width: 100%;
              height: 100%;
              border-radius: 50%;
            }
            .ant-avatar {
              width: 24px;
              height: 24px;
              line-height: 24px;
            }
          }
          &:hover {
            background: #f0f7ff;
            border: 1px solid #a4c9fc;
            .close-icon {
              visibility: visible;
            }
          }
        }
      }
    }
  }
}
.foot-btns {
  margin: 15px 0 0;
  text-align: right;
  .form-foot-btn {
    margin-left: 5px;
  }
}
</style>
