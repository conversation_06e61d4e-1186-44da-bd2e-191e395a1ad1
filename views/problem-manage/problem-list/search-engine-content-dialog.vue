<template>
	<a-modal
		v-model.trim="visibleshow"
		:title="$t('txt_select_object')"
		:okText="$t('btn_ok')"
		:cancelText="$t('btn_cancel')"
		@ok="confirm"
		width="80%"
	>
		<div class="modalclass">
			<jw-search-engine-content
				ref="addObjectContent"
				:pageCode="'objectProcess'"
				:onlySearchObject="true"
				:model-list="modelList"
				:checkRowKeys="checkRowKeys"
			/>
		</div>
	</a-modal>
</template>

<script>
import { jwSearchEngineContent } from "jw_frame";
export default {
	components: {
		jwSearchEngineContent,
	},
	props: {
		visible: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			initflag: false,
			checkRowKeys: [],
			modelList: [
				{
					name: this.$t("txt_part"),
					code: "PartIteration",
				},
				{
					name: this.$t("txt_document"),
					code: "DocumentIteration",
				},
				{
					name: "MCAD",
					code: "MCADIteration",
				},
				{
					name: "ECAD",
					code: "ECADIteration",
				},
				{
					name: this.$t("txt_baseline"),
					code: "Baseline",
				},
			],
		};
	},

	computed: {
		visibleshow: {
			get() {
				return this.visible;
			},
			set(val) {
				this.$emit("update:visible", val);
			},
		},
	},
	mounted() {},
	watch: {
		visibleshow: function (val) {
			if (val) {
				this.$nextTick(() => {
					if (this.$refs["addObjectContent"] && !this.initflag) {
						this.initflag = true;
						this.$refs["addObjectContent"].init();
					}
				});
			}
		},
	},
	methods: {
		confirm() {
			this.$emit("confirm", this.$refs["addObjectContent"].selectedRows);
			this.$refs["addObjectContent"].init();
			this.$refs["addObjectContent"].clearSelectedRows();
			this.$refs["addObjectContent"].tableData = [];
			this.$refs["addObjectContent"].searchKey = "";
			this.visibleshow = false;
		},
	},
};
</script>

<style lang="less" scoped>
.modalclass {
	height: 550px;
}
</style>