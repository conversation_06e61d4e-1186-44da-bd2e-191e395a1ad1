<template>
	<div class="problem-object">
		<div class="about-object">
			<p class="title">{{ $t('txt_problem_obj_problem') }}</p>
			<ul class="object-list">
				<li
					v-for="(item, key) in objectList"
					:key="key"
					@click="findInfluenceInfo(item)"
					:class="[
						'object-item',
						'line-clamp',
						{ active: currentItem === item.oid },
					]"
				>
					<span class="item-icon">
						<jwIcon :type="item.modelIcon"></jwIcon>
					</span>
					<a-tooltip placement="right">
						<template slot="title">
							<span>
								{{ item.name }},{{ item.number }},{{ item.displayVersion }}
							</span>
						</template>
						{{ item.name }},{{ item.number }},{{ item.displayVersion }}
					</a-tooltip>
				</li>
			</ul>
		</div>
		<about-content
			:currentRow="currentRow"
			:currentObj="currentObj"
			:contentHeight="contentHeight"
		/>
	</div>
</template>

<script>
import { jwIcon } from "jw_frame";
import aboutContent from "./about-content";


export default {
	name: "problem-object",
	components: { jwIcon, aboutContent },
	props: ["currentRow", "contentHeight"],
	data() {
		return {
			currentItem: "",
			objectList: [],
			currentObj: {},
		};
	},
	computed: {},
	watch: {
		currentRow(val) {
			if (val) {
				this.objectList = val.issueList;
				this.currentObj = val.issueList[0];
				this.currentItem = val.issueList[0].oid;
			}
		},
	},
	created() {},
	mounted() {},
	methods: {
		findInfluenceInfo(item) {
			this.currentObj = { ...item };
			this.currentItem = item.oid;
		},
	},
};
</script>

<style lang="less" scoped>
/deep/.ant-tabs-top-bar.ant-tabs-bar {
	margin-bottom: 0 !important;
}
.problem-object {
	display: flex;
	align-items: flex-start;
	justify-content: flex-start;
	height: calc(88.5vh);
	.about-object {
		height: 100%;
		padding: 0 16px;
		width: 300px;
		border-right: 1px solid rgba(30, 32, 42, 0.15);
		.title {
			margin-top: 16px;
		}
	}
	.object-list {
		margin-top: 19px;
		.object-item {
			display: flex;
			align-items: center;
			justify-content: flex-start;
			width: 100%;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			padding-left: 12px;
			height: 40px;
			font-weight: 400;
			font-size: 14px;
			color: rgba(30, 32, 42, 0.65);
			cursor: pointer;
			&:hover {
				background: rgba(30, 32, 42, 0.08);
				border-radius: 4px;
			}
			.item-icon {
				margin-right: 6px;
			}
		}
		.active {
			background: rgba(30, 32, 42, 0.08);
			border-radius: 4px;
		}
	}
}
.about-content {
	padding: 16px;
	.title {
		font-weight: 500;
		font-size: 14px;
		color: rgba(30, 32, 42, 0.85);
	}
}
</style>
