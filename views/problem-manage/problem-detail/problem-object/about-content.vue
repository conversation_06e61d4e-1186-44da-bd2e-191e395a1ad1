<template>
	<div class="about-content">
		<p class="common-title">
			<span class="title-name">{{ $t('txt_problem_obj_affect') }}</span>
		</p>
		<jw-table
			ref="table"
			class="product-config"
			panel
			:height="contentHeight || '700px'"
			:columns="columns"
			:data-source.sync="influenceInfo"
			:showPage="false"
			@onPageChange="onPageChange"
			@onSizeChange="onSizeChange"
		>
			<template #name="{ row }">
				<div class="name-wrap">
					<div class="name-con" @click="handleProblemDetail(row)">
						<span class="name-item">{{ row.name }}</span>
					</div>
				</div>
			</template>
			<template #relationSlot="{ row }">
				<span>{{ row.relationshipName }}</span>
			</template>
		</jw-table>
	</div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { jwIcon } from "jw_frame";
import { setOwner } from "apis/baseapi";
// 产品容器列表
const fetchContainerList = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.containerService}/container/product/search`,
	method: "post",
});

// 查询结构类型
const findByAppliedType = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.sysconfigServer}/collectionRule/findByAppliedType`,
	method: "get",
});

// 查询结构类型和相关对象
const findRelatedObject = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.foundationServer}/instance/relatedObject/fuzzy`,
	method: "post",
});

export default {
	name: "about-content",
	components: { jwIcon },
	props: ["currentRow", "currentObj", "contentHeight"],
	data() {
		return {
			expandRowKeys: [],
			influenceInfo: [],
			form: {
				...this.currentRow,
			},
			tableData: [],
			// 全部数据配置
			subTypes: [
				"PartIteration",
				"DocumentIteration",
				"MCADIteration",
				"ECADIteration",
			],
			subTypesTitle: this.$t("txt_all_datas"),
			//分页配置
			pagerConfig: {
				current: 1,
				pageSize: 20,
				total: 0,
			},
		};
	},
	computed: {
		columns() {
			return [
				{
					field: "name",
					title: this.$t('txt_name'),
					slots: {
						default: "name",
					},
				},
				{
					field: "number",
					title: this.$t('txt_number'),
				},
				{
					field: "relationName",
					title: this.$t('txt_relation'),
					slots: {
						default: "relationSlot",
					},
				},
				{
					field: "type",
					title: this.$t('txt_type'),
				},
				{
					field: "displayVersion",
					title: this.$t('txt_plan_version'),
				},
				{
					field: "lifecycleStatus",
					title: this.$t('txt_lifecycle'),
				},
			];
		},
		toolbars() {
			return [
				{
					name: this.$t("btn_new_create"),
					key: "add",
					type: "primary",
				},
				{
					name: this.subTypesTitle,
					display: "dropdown",
					position: "before",
					class: "sub-types-title",
					key: "allData",
					menuList: [
						{
							name: this.$t("txt_all_datas"),
							key: "all",
						},
						{
							name: this.$t("txt_part"),
							key: "PartIteration",
						},
						{
							name: this.$t("txt_document"),
							key: "DocumentIteration",
						},
						{
							prefixIcon: "jwi-search",
							name: this.$t("txt_structure_CAD"),
							key: "MCADIteration",
						},
						{
							name: this.$t("txt_electronic_CAD"),
							key: "ECADIteration",
						},
					],
				},
				{
					name: this.$t("btn_search"),
					position: "before",
					display: "input",
					value: this.searchKey,
					allowClear: false,
					placeholder: this.$t("search_text"),
					prefixIcon: "jwi-search",
					key: "search",
				},
				{
					name: "",
					key: "add",
					type: "text",
					position: "before",
					slots: {
						default: "showTotal",
					},
				},
			];
		},
	},
	watch: {
		currentRow(val) {
			if (val) {
				this.currentRow = val;
			}
		},
		currentObj(val) {
			this.findInfluenceInfo(val);
		},
	},
	created() {},
	mounted() {},
	methods: {
		loadTreeChild(row) {
			let { currentObj, influenceInfo } = this;
			let params = {
				...row,
				mainObjectOid: currentObj.oid,
			};
			findRelatedObject
				.execute(params)
				.then((data) => {
					data.map((item) => {
						item.relationshipName = row.relationshipName;
						item.relationshipName = row.relationshipName;
						return item;
					});
					let obj = {};
					let countData = influenceInfo
						.concat([...data])
						.reduce(function (item, next) {
							obj[next.oid] ? "" : (obj[next.oid] = true && item.push(next));
							return item;
						}, []);
					this.influenceInfo = countData;
				})
				.catch((err) => {
					console.log(err);
				});
		},
		findInfluenceInfo(item) {
			console.log("当前问题详情", this.currentRow);
			let params = {
				appliedType: `${item.modelDefinition}_Issue_Related_Object`,
				mainObjectType: item.modelDefinition,
			};
			findByAppliedType
				.execute(params)
				.then((data) => {
					data.map((item) => {
						this.loadTreeChild(item);
						return item;
					});
				})
				.catch((err) => {
					console.log(err);
					this.$error(err.msg || this.$t("msg_failed"));
				});
		},
		reFetchData() {
			this.fetchTable({ current: 1, pageSize: 20 });
		},
		//分页操作
		onPageChange(page, pageSize) {
			this.pages = {
				currentPage: page,
				size: pageSize,
				searchKey: this.searchKey,
			};

			this.pagerConfig.current = page;
			this.pagerConfig.pageSize = pageSize;
			this.reFetchData();
		},
		onSizeChange(pageSize, page) {
			this.pages = {
				currentPage: page,
				size: pageSize,
				searchKey: this.searchKey,
			};
			this.pagerConfig.current = page;
			this.pagerConfig.pageSize = pageSize;

			this.reFetchData();
		},
		// 工具栏点击回调
		onToolClick(item) {
			if (item.key === "create") {
			} else if (item.key === "switch") {
				// 切换列表
				this.switchType = !this.switchType;
			} else if (item.key === "delete") {
				// console.log("this.selectedRows", this.selectedRows);
				this.fetchDelete(this.selectedRows);
			} else if (
				item.key === "all" ||
				item.key === "PartIteration" ||
				item.key === "DocumentIteration" ||
				item.key === "MCADIteration" ||
				item.key === "ECADIteration"
			) {
				this.subTypesTitle = item.name;
				if (item.key === "all") {
					this.subTypes = [
						"PartIteration",
						"DocumentIteration",
						"MCADIteration",
						"ECADIteration",
					];
				} else {
					this.subTypes = [item.key];
				}
				this.pagerConfig = {
					current: 1,
					pageSize: 20,
					total: 0,
				};
				this.reFetchData();
			}
		},
		// 工具栏输入回调
		onToolInput({ key }, value) {
			// console.log(value);
			if (key === "search") {
				this.searchKey = value;
				this.delaySearch();
			}
		},
		handleProblemDetail() {
			// 跳转到对象
			// this.$router.push({
			// 	name: "problem-detail",
			// 	path: "/problem-detail",
			// });
		},
		onOperateClick(item, row) {
			let key = item.code || item;
			if (key === "details") {
				this.handleProblemDetail(row);
			} else if (key === "startProcess") {
				startECRWorkflow
					.execute({
						ecrOid: row.oid,
					})
					.then((res) => {
						this.currentRecord = row;
						this.processVisible = true;
					})
					.catch((err) => {
						this.$error(err.msg);
					});
			} else if (key === "updateOwner") {
				this.$refs["user-modal"]
					.show({
						type: "User",
					})
					.then((res) => {
						setOwner
							.execute({
								oid: row.oid,
								type: row.type,
								ownerAccount: res.account,
							})
							.then((res) => {
								this.$success(this.$t("msg_save_success"));
								this.reFetch();
							})
							.catch((err) => {
								this.$error(err.msg);
							});
					});
			} else if (key === "delete") {
				this.onDelete(row);
			}
		},
		fetchTable({ current, pageSize }) {
			let params = {};
			return fetchContainerList
				.execute(params)
				.then((data) => {
					return { data: data.rows || data, total: data.count };
				})
				.catch((err) => {
					// this.$error(err.msg || this.$t("msg_failed"));
				});
		},
	},
};
</script>

<style lang="less" scoped>
/deep/.ant-advanced-search-form .ant-form-item {
	margin-bottom: 10px;
}
.about-content {
	flex: 1;
	padding: 16px;
	width: calc(~"100% - 300px");
	.title {
		margin-bottom: 16px;
		font-weight: 500;
		font-size: 14px;
		color: rgba(30, 32, 42, 0.85);
	}
}
.common-title {
	margin-bottom: 20px;
	padding-left: 8px;
	border-left: 3px solid #255ed7;
	.title-name {
		margin-right: 8px;
		font-weight: 500;
		font-size: 16px;
		color: rgba(30, 32, 42, 0.85);
	}
	.title-collspan {
		cursor: pointer;
		font-weight: 400;
		font-size: 12px;
		color: #255ed7;
	}
}
</style>
