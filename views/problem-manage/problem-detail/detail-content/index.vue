<template>
	<div class="base-panel page-container">
		<div class="basic-info">
			<p class="common-title">
				<span class="title-name">{{$t('txt_base_info')}}</span>
				<span class="title-collspan" @click="handleColl('baseInfo')">
					{{ collspanList.baseInfo ?  $t('txt_pack_up') : $t('txt_an') }}
				</span>
			</p>
			<jw-layout-builder v-if="collspanList.baseInfo" ref="ref_appBuilder" type="Model" :layoutName="showEdit ? 'update' : 'show'" :modelName="'Issue'" :instanceData="currentRow">
        <template #sourceSlot="{formData}">
            <a-row :gutter="15">
              <a-col :span="12">
                <a-select
                :disabled="!showEdit"
                  v-model.trim="source"
                  allowClear
                  :placeholder="$t('msg_select')"
                  @change="sourceChange"
                >
                  <a-select-option
                    v-for="item in sourceList"
                    :key="item.name"
                    :value="item.name"
                  >
                    {{ $t(item.name) }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="12">
                <a-input :disabled="!showEdit" v-if="source==='其它'" v-model="sourceRemark"></a-input>
              </a-col>
            </a-row>
          </template>
			</jw-layout-builder>
			<!-- <p class="common-title">
				<span class="title-name">{{$t('txt_problem_affected')}}</span>
				<span class="title-collspan" @click="handleColl('schemaInfo')">
					{{ collspanList.schemaInfo ? $t('txt_pack_up') : $t('txt_an') }}
				</span>
			</p> -->
			<!-- <jw-layout-builder v-if="collspanList.schemaInfo" ref="ref_appBuilder_schema" type="Model" modelName="Issue" :layoutName="showEdit ? 'schemaUpdate' : 'schemaShow'" :formRules="schemaRule" :view="!showEdit" :instanceData="currentRow.issueSchema">
				<template #hasTechnologySlot="{formData}">
					<a-form-model-item :label="$t('txt_problem_process')">
						<a-radio-group v-model.trim="formData.hasTechnology" :disabled="showEdit ? false : true">
							<a-radio :value="true">{{ $t('txt_yes') }}</a-radio>
							<a-radio :value="false">{{ $t('txt_no') }}</a-radio>
						</a-radio-group>
					</a-form-model-item>
					<a-form-model-item :label="$t('txt_description')" v-if="formData.hasTechnology" prop="hasTechnologyRemark">
						<a-textarea v-model.trim="formData.hasTechnologyRemark" placeholder="" :disabled="showEdit ? false : true" :auto-size="{ minRows: 3, maxRows: 5 }" />
					</a-form-model-item>
				</template>
				<template #hasManufactureSlot="{formData}">
					<a-form-model-item :label="$t('txt_problem_manufacturing')">
						<a-radio-group v-model.trim="formData.hasManufacture" :disabled="showEdit ? false : true">
							<a-radio :value="true">{{ $t('txt_yes') }}</a-radio>
							<a-radio :value="false">{{ $t('txt_no') }}</a-radio>
						</a-radio-group>
					</a-form-model-item>
					<a-form-model-item :label="$t('txt_description')" v-if="formData.hasManufacture" prop='hasManufactureRemark'>
						<a-textarea v-model.trim="formData.hasManufactureRemark" placeholder="" :disabled="showEdit ? false : true" :auto-size="{ minRows: 3, maxRows: 5 }" />
					</a-form-model-item>
				</template>
				<template #hasAfterSalesSlot="{formData}">
					<a-form-model-item :label="$t('txt_problem_service')">
						<a-radio-group v-model.trim="formData.hasAfterSales" :disabled="showEdit ? false : true">
							<a-radio :value="true">{{ $t('txt_yes') }}</a-radio>
							<a-radio :value="false">{{ $t('txt_no') }}</a-radio>
						</a-radio-group>
					</a-form-model-item>
					<a-form-model-item :label="$t('txt_description')" v-if="formData.hasAfterSales" prop='hasAfterSalesRemark'>
						<a-textarea v-model.trim="formData.hasAfterSalesRemark" placeholder="" :disabled="showEdit ? false : true" :auto-size="{ minRows: 3, maxRows: 5 }" />
					</a-form-model-item>
				</template>
			</jw-layout-builder> -->
			<p class="common-title">
				<span class="title-name">{{ $t('txt_problem_history') }}</span>
				<span class="title-collspan" @click="handleColl('historyInfo')">
					{{ collspanList.historyInfo ?  $t('txt_pack_up') : $t('txt_an') }}
				</span>
			</p>
			<a-timeline v-if="collspanList.historyInfo">
				<a-timeline-item v-for="(item, index) in currentRow.records" :key="index">
					{{ item.option }}- {{ formatDate(item.createDate) }} - {{ item.operator }}{{ item.option ? ' - ' + item.handler : '' }}
				</a-timeline-item>
			</a-timeline>
		</div>
		<div class="pane">
			<FlowChart ref="flowChart" :processInstanceName="''" :showIcon="false" :processInstanceId="currentRow.processInstanceId || ''" :processOrderOid="''"></FlowChart>
		</div>
		<div v-if="showEdit" class="issue-footer">
			<a-button type="primary" style="margin-right: 12px; margin-left: 40%" @click="handleSubmit">
				{{ $t('btn_save') }}
			</a-button>
			<a-button @click="handleEdit">{{ $t('btn_cancel') }}</a-button>
		</div>
	</div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { jwAvatar, jwIcon, jwModalForm, jwLayoutBuilder } from "jw_frame";
import FlowChart from "../../components/flow-chart";
import { formatDate } from "jw_utils/moment-date";

// 问题详情
const updateProblem = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/pr/issue/updateIssueBasicInfo`,
  method: "post"
});

// 产品容器列表
const fetchContainerList = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.containerService
  }/container/product/searchByUser?name`,
  method: "get"
});

const getSourceList = (params) =>
  ModelFactory.create({
    url: `${Jw.gateway}/${Jw.sysconfigServer}/preferences/setting/query-config-value`,
    method: "get",
  }).execute(params)


export default {
  name: "oproblem-detail",
  inject: ["setBreadcrumb"],
  components: { jwIcon, FlowChart, jwLayoutBuilder },
  props: ["currentRow"],
  data() {
    return {
      schemaValue: {
        hasTechnology: false,
        hasTechnologyRemark: "",
        hasManufacture: false,
        hasManufactureRemark: "",
        hasAfterSales: false,
        hasAfterSalesRemark: ""
      },
      schemaRule: {
        weight: [
          {
            message: this.$t("txt_problem_valid_weight"),
            pattern: /^(0|[1-9]+[0-9]*)(.[0-9]{1,3})?$/,
            max: 13,
            min: 1,
            validator: (rule, value, cb) =>
              this.validatorNumber(value) ? cb(this.$t("txt_problem_valid_weight")) : cb()
          }
        ],
        stock: [
          {
            message: this.$t("txt_problem_valid_correctly"),
            pattern: /^(0|[1-9]+[0-9]*)(.[0-9]{1,3})?$/,
            max: 13,
            min: 1,
            validator: (rule, value, cb) =>
              this.validatorNumber(value) ? cb(this.$t("txt_problem_valid_correctly")) : cb()
          }
        ],
        cost: [
          {
            message: this.$t("txt_problem_valid_price"),
            pattern: /^(0|[1-9]+[0-9]*)(.[0-9]{1,3})?$/,
            max: 13,
            min: 1,
            validator: (rule, value, cb) =>
              this.validatorNumber(value) ? cb(this.$t("txt_problem_valid_price")) : cb()
          }
        ]
      },
      showEdit: this.$route.query.edit || false,
      formatDate,
      tableData: [],
      validateBasic: false,
      validateSchema: false,
      currentRowInfo: {},
      containerList: [],
      collspanList: { baseInfo: true, schemaInfo: true, historyInfo: true },
      source:"",
      sourceRemark:'',
      sourceList:[],
    };
  },
  computed: {},
  watch: {
    currentRow(val) {
      let compare = function(obj1, obj2) {
        var val1 = obj1.createDate;
        var val2 = obj2.createDate;
        if (val1 < val2) {
          return -1;
        } else if (val1 > val2) {
          return 1;
        } else {
          return 0;
        }
      };
      this.initSource()
      this.currentRow.records.sort(compare);
      this.currentRowInfo = { ...val };
      this.schemaValue = val.issueSchema;
    }
  },
  created() {
    this.setBreadcrumb([]);
    this.fetchContainerList();
    this.getSourceList()
  },
  mounted() {},
  methods: {
    sourceChange(){
      this.sourceRemark=""
    },
    getSourceList(){
      getSourceList({name:'problemSource'}).then(res=>{
        this.sourceList=res||[]
        this.initSource()
      })
    },
    initSource(){
      let value = this.currentRow
      let source = ""
      if(!this.sourceList.length){
        return
      }
      if(value.extensionContent){
        source = value.extensionContent.source||""
        this.source=source
      }
      let obj = this.sourceList.find(ele=>ele.name===source)
      if(!obj&&source){
        this.source="其它"
        this.sourceRemark=source
      }
    },
    changeBuild(data) {
      this.$nextTick(()=>{
        let el = document.querySelectorAll(".cn_jwis_type")[0];
        let label = document.querySelectorAll(".cn_jwis_type label")[0]
        if (el) {
        if (data.source === "其它") {
          el.style.display = "block";
        } else {
          el.style.display = "none";
        }
        label.style.opacity=0
      }
      })
    },
    validatorNumber(value) {
      let numberExp = /^(0|[1-9]+[0-9]*)(.[0-9]{1,3})?$/;
      if (
        value.length > 10 ||
        value <= 0 ||
        isNaN(value) ||
        !numberExp.test(value)
      ) {
        return true;
      } else {
        return false;
      }
    },
    hasChange(e, key) {
      this[key] = e.target.value;
      if (!e.target.value) {
        this[key + "Remark"] = "";
      }
    },
    handleColl(key) {
      let value = this.collspanList[`${key}`];
      this.collspanList[`${key}`] = !value;
    },
    fetchContainerList() {
      fetchContainerList
        .execute()
        .then(data => {
          this.containerList = data;
        })
        .catch(err => {
          // this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    handleEdit() {
      this.$emit("handleEdit", false);
      this.$emit("getTableData", false);
    },
    handleSubmit() {
      let { $refs, validateBasic, validateSchema } = this;
      let ref_appBuilder = $refs.ref_appBuilder;
      // let ref_appBuilder_Schema = $refs.ref_appBuilder_schema;
      let value = ref_appBuilder.getValue();
      // let valueSchema = ref_appBuilder_Schema.getValue();
      if(value.extensionContent){
        value.extensionContent.source=this.sourceRemark||this.source
      }else{
        value.extensionContent={
          source:this.sourceRemark||this.source
        }
      }
      if (validateBasic === true && validateSchema === true) {
        this.handleUpdate(value);
      } else {
        ref_appBuilder &&
          ref_appBuilder
            .validate()
            .then(res => {
              this.validatorSchema();
            })
            .catch(err => {
              console.log(err);
            });
      }
    },
    validatorSchema() {
      this.handleUpdate();
      // ref_appBuilder_Schema &&
      // 	ref_appBuilder_Schema
      // 		.validate()
      // 		.then((res) => {
      // 			this.handleUpdate();
      // 		})
      // 		.catch((err) => {
      // 			console.log(err);
      // 		});
      // let { $refs } = this;
      // let ref_appBuilder_Schema = $refs.ref_appBuilder_schema;
      // let valueSchema = ref_appBuilder_Schema.getValue();
      // if (valueSchema.stock && this.validatorNumber(valueSchema.stock)) {
      //   ref_appBuilder_schema &&
      //     ref_appBuilder_schema
      //       .validate()
      //       .then(res => {})
      //       .catch(err => {
      //         console.log(err);
      //         return false;
      //       });
      // } else if (valueSchema.cost && this.validatorNumber(valueSchema.cost)) {
      //   ref_appBuilder_schema &&
      //     ref_appBuilder_schema
      //       .validate()
      //       .then(res => {})
      //       .catch(err => {
      //         console.log(err);
      //         return false;
      //       });
      // } else if (
      //   valueSchema.weight &&
      //   this.validatorNumber(valueSchema.weight)
      // ) {
      //   ref_appBuilder_schema &&
      //     ref_appBuilder_schema
      //       .validate()
      //       .then(res => {})
      //       .catch(err => {
      //         console.log(err);
      //         return false;
      //       });
      // } else {
      //   this.handleUpdate();
      // }
    },
    handleUpdate() {
      let {
        $refs,
        validateBasic,
        validateSchema,
        currentRow,
        containerList
      } = this;
      let ref_appBuilder = $refs.ref_appBuilder;
      // let ref_appBuilder_Schema = $refs.ref_appBuilder_schema;
      let value = ref_appBuilder.getValue();
      let valueSchema = {};
      let schemaValue=this.schemaValue||{}
      let containerInfo = containerList.filter(
        item => item.oid == value.containerOid
      )[0];
      if(value.extensionContent){
        value.extensionContent.source=this.sourceRemark||this.source
      }else{
        value.extensionContent={
          source:this.sourceRemark||this.source
        }
      }
      let params = {
        ...currentRow,
        ...value,
        issueSchema: {
          cost: valueSchema.cost || "",
          weight: valueSchema.weight || "",
          stock: valueSchema.stock || "",
          hasTechnology: schemaValue.hasTechnology,
          hasTechnologyRemark: schemaValue.hasTechnologyRemark,
          hasManufacture: schemaValue.hasManufacture,
          hasManufactureRemark: schemaValue.hasManufactureRemark,
          hasAfterSales: schemaValue.hasAfterSales,
          hasAfterSalesRemark: schemaValue.hasAfterSalesRemark
        },
        issueList: currentRow.issueList,
        containerOid: containerInfo.oid,
        containerName: containerInfo.name
      };
      updateProblem
        .execute(params)
        .then(data => {
          // this.$emit("handleEdit");
          this.showEdit = false;
          this.$emit("getTableData");
        })
        .catch(err => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    }
  }
};
</script>

<style lang="less" scoped>
.issue-footer {
  position: absolute;
  left: -12px;
  bottom: 0;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  background-color: #fff;
  padding: 12px 0;
  border-top: 1px solid rgba(30, 32, 42, 0.15);
}
.page-container {
  position: relative;
  height: calc(88.5vh);
  width: 100%;
  padding: 24px;
  display: flex;
  justify-content: flex-start;
}
.panel-title {
  padding-left: 8px;
  border-left: 3px solid #255ed7;
  font-weight: 500;
  font-size: 16px;
  color: rgba(30, 32, 42, 0.85);
  .collspan {
    margin-left: 14px;
    font-weight: 400;
    font-size: 12px;
    color: #255ed7;
    cursor: pointer;
  }
}
.common-title {
  margin-top: 20px;
  margin-bottom: 20px;
  padding-left: 8px;
  border-left: 3px solid #255ed7;
  .title-name {
    margin-right: 8px;
    font-weight: 500;
    font-size: 16px;
    color: rgba(30, 32, 42, 0.85);
  }
  .title-collspan {
    cursor: pointer;
    font-weight: 400;
    font-size: 12px;
    color: #255ed7;
  }
}
.basic-info {
  position: relative;
  margin-right: 16px;
  flex: 1;
  overflow-y: scroll;
  overflow-x: hidden;
}
.pane {
  width: 420px;
  border-left: 1px solid rgba(30, 32, 42, 0.15);
  text-align: left;
  overflow: hidden;
}
</style>

