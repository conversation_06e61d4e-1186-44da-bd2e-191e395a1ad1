<template>
	<div class="base-panel page-container">
		<header>
			<div class="header-left">
				<a-breadcrumb separator=">">
					<a-breadcrumb-item>
						<a href="javascript:void(0)" @click="routerBack">
							{{ $t('txt_problem_management') }}
						</a>
					</a-breadcrumb-item>
					<a-breadcrumb-item>
						{{ currentRow.name }}
						<span
							v-if="reportEnable == true"
							@click="switchReport"
							class="switch-class"
							:title="currentSwitch ? $t('txt_problem_report_view') : $t('txt_view_detail')"
						>
							<jwIcon
								:type="
									currentSwitch
										? 'jwi-iconwentibaogao'
										: 'jwi-iconinfo-circle-full'
								"
							/>
						</span>
					</a-breadcrumb-item>
				</a-breadcrumb>
			</div>
			<div class="right">
				<detailOpration
					v-if="currentSwitch"
					ref="detailOpration"
					:currentRow="currentRow"
					@switchReport="switchReport"
					@handleEdit="handleEdit"
					@setConfigStatus="setConfigStatus"
					@getTableData="fetchContainerList"
				/>
			</div>
		</header>
		<a-tabs
			:default-active-key="defaultActiveKey"
			@change="callback"
			v-if="currentSwitch"
		>
			<a-tab-pane key="1" :tab="$t('txt_problem_view')">
				<detail-content
					ref="detailContent"
					:showEdit="showEdit"
					:currentRow="currentRow"
					@handleEdit="handleEdit"
					@getTableData="fetchContainerList"
				/>
			</a-tab-pane>
			<a-tab-pane key="2" :tab="$t('txt_problem_affect_obj')" force-render>
				<problem-object :currentRow="currentRow" />
			</a-tab-pane>
			<a-tab-pane key="3" :tab=" $t('txt_problem_relevant') + 'ECR'" force-render>
				<about-ecr :currentRow="currentRow" :configStatus="configStatus" />
			</a-tab-pane>
		</a-tabs>
		<problem-report v-if="!currentSwitch" />
	</div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { jwIcon } from "jw_frame";
import aboutEcr from "./about-ecr";
import problemObject from "./problem-object";
import detailContent from "./detail-content";
import detailOpration from "../components/detail-opration";
import problemReport from "../problem-report";
import { findDetail } from "apis/baseapi";


// 问题管理列表
const fetchContainerList = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.changeServer}/pr/issue/search`,
	method: "post",
});

// 获取该条数据可操作下拉列表
const getDropdownList = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
	method: "post",
});

export default {
	name: "oproblem-detail",
	inject: ["setBreadcrumb"],
	components: {
		aboutEcr,
		problemObject,
		detailContent,
		jwIcon,
		detailOpration,
		problemReport,
	},
	data() {
		return {
			defaultActiveKey: this.$route.query.issuDetailActive || "1",
			reportEnable: false,
			currentSwitch: true,
			showEdit: false,
			problemDetail: {},
			tableData: [],
			currentRow: {
				issueSchema: {},
			},
			operationList: [
				{
					code: "details",
					name: this.$t("txt_view_detail"),
					status: "enable",
					icon: "jwi-iconinfo-circle-full",
				},
				{
					code: "edit",
					name: this.$t('btn_edit'),
					status: "enable",
					icon: "jwi-iconedit",
				},
				{
					code: "setStatus",
					name: this.$t("txt_set_status"),
					status: "enable",
					icon: "jwi-iconstate-set",
				},
				{
					code: "start",
					name: this.$t('txt_start_process'),
					status: "enable",
					icon: "jwi-iconactivation",
				},
				{
					code: "createChange",
					name: this.$t('txt_problem_request'),
					status: "enable",
					icon: "jwi-iconbiangeng",
				},
				{
					code: "report",
					name: this.$t('txt_problem_view_report'),
					status: "enable",
					icon: "jwi-yanshou",
				},
			],
			configStatus: "",
		};
	},
	computed: {},
	created() {
		this.setBreadcrumb([]);
		this.init();
		this.currentSwitch = this.$route.query.isReport == true ? false : true;
	},
	mounted() {},
	methods: {
		init() {
			this.fetchProblemDetail();
			this.fetchContainerList();
		},
		setConfigStatus(configStatus) {
			this.configStatus = configStatus;
		},
		getDropdownList() {
			let _this = this;
			let { currentRow } = this;
			getDropdownList
				.execute({
					viewCode: "ISSUEINSTANCE",
					objectOid: currentRow.oid,
				})
				.then((data) => {
					console.log("当前权限列表", data);
					data.map((item) => {
						if (item.code === "report") {
							_this.reportEnable =
								currentRow.lifecycleStatus == "Closed" &&
								item.status == "enable"
									? true
									: false;
						}
						return item;
					});
				})
				.catch((err) => {
					this.$error(err.msg || this.$t("msg_failed"));
				});
		},
		switchReport() {
			let { currentSwitch } = this;
			this.currentSwitch = !currentSwitch;
			this.init();
		},
		routerBack() {
			this.$router.push({
				path: "/problem-manage",
				query: { tabActive: this.$route.query.tabActive },
			});
		},
		handleEdit(flag) {
			let { showEdit } = this;
			this.showEdit = flag;
			this.$refs.detailContent.showEdit = flag;
		},
		rowOperation(item) {
			console.log(item);
			let { key } = item;
			if (key === "details") {
				this.handleProblemDetail(row);
			} else if (key === "edit") {
				this.handleProblemDetail(row);
			} else if (key === "start") {
				this.onStartProcess(row);
			} else if (key === "report") {
				this.handleProblemReport(row);
			} else if (key === "delete") {
				this.onDelete(row);
			} else if (key === "setStatus") {
				this.searchStatusList(row);
			} else if (key === "createChange") {
				this.handleCreateChange(row);
			}
		},
		fetchContainerList() {
			let oid = this.$route.query.oid;
			fetchContainerList
				.execute()
				.then((data) => {
					console.log(data);
					this.tableData = data.rows;
					this.currentRow = data.rows.filter((item) => item.oid === oid)[0];
					this.getDropdownList();
				})
				.catch((err) => {
					// this.$error(err.msg || this.$t("msg_failed"));
				});
		},
		fetchProblemDetail() {
			let params = {
				oid: this.$route.query.oid,
				type: "Issue",
			};
			findDetail
				.execute(params)
				.then((data) => {
					this.problemDetail = { ...data };
				})
				.catch((err) => {
					// this.$error(err.msg || this.$t("msg_failed"));
				});
		},
		callback(key) {
			this.$router.push({
				name: "problem-detail",
				path: "/problem-detail",
				query: {
					oid: this.$route.query.oid,
					tabActive: this.$route.query.tabActive,
					issuDetailActive: key,
				},
			});
		},
		handleBack() {
			this.$router.push({
				name: "problem-manage",
				path: "/problem-manage",
				query: { tabActive: this.$route.query.tabActive },
			});
		},
	},
};
</script>

<style lang="less" scoped>
.switch-class {
	cursor: pointer;
}
.page-container {
	width: 100%;
	overflow: hidden;
	.product-config {
		display: flex;
		justify-content: flex-start;
		flex-wrap: nowrap;
	}
}
/deep/.ant-tabs-bar {
	margin-bottom: 0;
	border-bottom: 1px solid rgba(30, 32, 42, 0.15);
}
/deep/.ant-tabs-nav-wrap {
	padding-left: 24px;
}
/deep/.ant-tabs-nav .ant-tabs-tab {
	padding: 12px 0;
	font-weight: 400;
	font-size: 14px;
	color: rgba(30, 32, 42, 0.65);
}
/deep/.ant-tabs-nav .ant-tabs-tab-active {
	font-weight: 500;
	font-size: 14px;
	color: rgba(30, 32, 42, 0.85);
}
/deep/.ant-tabs-top .ant-tabs-ink-bar-animated,
.ant-tabs-bottom .ant-tabs-ink-bar-animated {
	color: #255ed7;
}
.detail-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 60px;
	min-height: 60px;
	line-height: 60px;
	padding: 0 24px;
	background: #fff;
	.left {
		.back-icon {
			margin-right: 10px;
			cursor: pointer;
			i {
				font-size: 20px;
			}
		}
		.title {
			font-weight: 500;
			font-size: 20px;
			color: rgba(30, 32, 42, 0.85);
		}
	}
	.right {
		margin-right: 100px;
	}
}
header {
	display: flex;
	justify-content: space-between;
	height: 60px;
	background-color: #fff;
	align-items: center;
	padding: 0 110px 0 24px;
	> .header-left {
		font-size: 20px;
		display: flex;
		a {
			color: #40a9ff;
		}
	}
}
</style>

