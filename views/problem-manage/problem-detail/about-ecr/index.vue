<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-07 14:18:30
 * @LastEditTime: 2022-05-06 17:24:07
 * @LastEditors: <EMAIL>
-->
<template>
	<div class="change-management">
		<jw-table
			ref="table"
			:height="tableHeight ? tableHeight : '800px'"
			:panel="pageCode === 'changeRecord' ? false : true"
			:toolbars="isReport ? [] : toolbars"
			:showPage="isReport ? false : true"
			:columns="columns"
			:data-source.sync="tableData"
			:fetch="fetchTable"
			v-if="instanceData.oid"
			@onOperateClick="onOperateClick"
		>
			<template #number="{ row }">
				<div class="name-wrap">
					<div class="name-con" @click="onOpenECRDetail(row)">
						<jwIcon :type="row.modelIcon"></jwIcon>
						<span class="name-item">{{ row.number }}</span>
					</div>
				</div>
			</template>
		</jw-table>
		<!-- 发起流程 -->
		<start-process-modal
			:visible="processVisible"
			:pageCode="'objectProcess'"
			:detailInfo="currentRecord"
			@close="onCloseProcessModal"
			@getTableData="reFetch"
		></start-process-modal>
		<!-- 修改所有者 -->
		<jw-user-modal-v2 ref="user-modal" :isCheckbox="false" />
	</div>
</template>

<script>
import { jwIcon, jwUserModalV2 } from "jw_frame";
import ModelFactory from "jw_apis/model-factory";
import { deleteECRApi } from "apis/change";
import { setOwner } from "apis/baseapi";
import startProcessModal from "/views/product-content/process-manage/start-process-modal";
// 查询部件，文档，CAD权限
const getOperationFilter = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
	method: "post",
});

// ECR是否可以发起流程校验
const startECRWorkflow = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.changeServer}/change/check/startECRWorkflow`,
	method: "post",
});

const fetchEcrList = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.changeServer}/pr/issue/searchECR`,
	method: "post",
});

// 获取该条数据可操作下拉列表
const getDropdownList = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
	method: "post",
});

export default {
	name: "changeManagement",
	data() {
		return {
			tableData: [],
			searchKey: "",
			instanceData: {},
			processVisible: false,
			currentRecord: {},
			permissionList: [],
			isEcrRole: false,
		};
	},
	props: [
		"pageCode",
		"changeApi",
		"currentRow",
		"isReport",
		"tableHeight",
		"configStatus",
	],
	components: {
		jwIcon,
		jwUserModalV2,
		startProcessModal,
	},
	computed: {
		columns() {
			return [
				{
					field: "number",
					title: this.$t("txt_plan_number"),
					slots: {
						default: "number",
					},
				},
				{
					field: "name",
					title: this.$t("txt_plan_name"),
				},
				{
					field: "changeType.txt",
					title: this.$t("change_type"),
				},
				{
					field: "degreeEmergency.txt",
					title: this.$t("change_emergency"),
				},
				{
					field: "lifecycleStatus",
					title: this.$t("txt_status"),
				},
				{
					field: "owner",
					title: this.$t("txt_owner"),
				},
				{
					field: "createDate",
					title: this.$t("txt_create_time"),
					formatter: "date",
				},
				{
					field: "operation",
					title: this.$t("txt_operation"),
					maxShowBtn: 1,
					permissionCode: "ECRINSTANCE",
				},
			];
		},
		toolbars() {
			return [
				{
					name: this.$t("btn_new_create"),
					key: "add",
					type: "primary",
					click: this.jumbTo,
					isVisible: () => {
						if (
							this.permissionList.includes("ECR.create") &&
							!this.isReport &&
							this.isEcrRole
						) {
							return true;
						} else {
							return false;
						}
					},
				},
				{
					display: "input",
					key: "search",
					value: this.searchKey,
					input: this.onSearchInput,
				},
			];
		},
	},
	watch: {
		currentRow(val) {
			if (val) {
				this.operationFilter(val);
				this.dropdownVisibleChange();
			}
		},
		configStatus(val) {
			if (val) {
				this.dropdownVisibleChange();
			}
		},
	},
	created() {
		this.instanceData = { ...this.$route.query };
		this.delaySearch = _.debounce(this.reFetch, 500);
	},
	methods: {
		// 查询当前的权限
		// 获取 row  part 操作下拉列表
		dropdownVisibleChange() {
			let _this = this;
			let { configStatus, currentRow } = this;
			if (configStatus != "" && currentRow.lifecycleStatus) {
				getDropdownList
					.execute({
						viewCode: "ISSUEINSTANCE",
						objectOid: currentRow.oid,
					})
					.then((data) => {
						data.map((item) => {
							if (item.code === "createChange") {
								if (
									configStatus == currentRow.lifecycleStatus &&
									item.status == "enable"
								) {
									item.status = "enable";
									_this.isEcrRole = true;
								} else {
									item.status = "disable";
									_this.isEcrRole = false;
								}
							}
							return item;
						});
					})
					.catch((err) => {
						this.$error(err.msg || this.$t("msg_failed"));
					});
			}
		},
		onOperateClick(item, row) {
			let key = item.code || item;
			if (key === "details") {
				this.onOpenECRDetail(row);
			} else if (key === "startProcess") {
				startECRWorkflow
					.execute({
						ecrOid: row.oid,
					})
					.then((res) => {
						this.currentRecord = row;
						this.processVisible = true;
					})
					.catch((err) => {
						this.$error(err.msg);
					});
			} else if (key === "updateOwner") {
				this.$refs["user-modal"]
					.show({
						type: "User",
					})
					.then((res) => {
						setOwner
							.execute({
								oid: row.oid,
								type: row.type,
								ownerAccount: res.account,
							})
							.then((res) => {
								this.$success(this.$t("msg_save_success"));
								this.reFetch();
							})
							.catch((err) => {
								this.$error(err.msg);
							});
					});
			} else if (key === "delete") {
				this.onDelete(row);
			}
		},
		jumbTo() {
			let oid = this.$route.query.oid;
			this.$router.push({
				name: "ecr-create",
				path: `/change-management/ecr/create/${oid}`,
				params: {
					oid: oid,
				},
				query:{
					issue:1,
					name:this.$route.query.name
				}
			});
		},
		onDelete(row) {
			this.$confirm({
				title: this.$t("change_confirm_delete"),
				okText: this.$t("btn_ok"),
				cancelText: this.$t("btn_cancel"),
				onOk: () => {
					const params = {
						ecrOid: row.oid,
					};
					return deleteECRApi.execute(params).then(() => {
						this.reFetch();
					});
				},
			});
		},
		onSearchInput(value) {
			this.searchKey = value;
			this.delaySearch();
		},
		reFetch() {
			this.$refs.table.reFetchData();
		},
		fetchTable({ current, pageSize }) {
			let { currentRow } = this;
			let params = {
				// searchKey: "",
				oid: this.$route.query.oid,
				type: "Issue",
				// searchType: "ECR",
				// oid: currentRow.containerOid,
				// type: currentRow.containerType,
			};
			return fetchEcrList
				.execute(params)
				.then((data) => {
					return { data: data.rows || data, total: data.count };
				})
				.catch((err) => {
					this.$error(err.msg || this.$t("msg_failed"));
				});
		},
		onCloseProcessModal() {
			this.processVisible = false;
		},
		processModalShow(row) {
			this.currentRecord = row;
			this.processVisible = true;
		},
		operationFilter(currentRow) {
			let param = {
				viewCode: "CHANGEOPERATION",
				objectType: currentRow.containerType,
				objectOid: currentRow.containerOid,
			};
			getOperationFilter
				.execute(param)
				.then((data) => {
					if (data) {
						data.forEach((item) => {
							if (item.status == "enable") {
								this.permissionList.push(
									item.modelType + "." + item.permissionKey
								);
							}
						});
					}
				})
				.catch((err) => {
					console.log(err);
					this.$error(err.msg || this.$t("msg_failed"));
				});
		},
		onOpenECRDetail(row) {
			let isBlank = false;
			if (this.$route.name === "object-details") {
				sessionStorage.setItem("currentTabName", "changeRecord");
				isBlank = true;
			}
			if (
				this.$route.name == "object-details" ||
				this.$route.name == "product-content"
			) {
				let enterEcrRoute = {
					routeName: this.$route.name,
					query: { ...this.$route.query },
				};
				sessionStorage.setItem("enterEcrInfo", JSON.stringify(enterEcrRoute));
			}
			row.masterType = row.modelDefinition;
			localStorage.setItem("isFromIssue", true);
			localStorage.setItem("issueInfo", this.$route.query.oid);
			Jw.jumpToDetail({
				...row,
				tabActive: "change",
				blank: isBlank,
			});
		},
	},
};
</script>

<style lang="less" scoped>
.change-management {
	height: 100%;
	.name-wrap {
		display: flex;
		justify-content: space-between;
		.name-con {
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			.name-item {
				color: #255ed7;
				cursor: pointer;
			}
		}
	}
}
</style>
