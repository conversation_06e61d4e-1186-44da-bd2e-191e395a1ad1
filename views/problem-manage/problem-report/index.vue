<template>
  <!-- <div class="base-panel page-container"> -->
  <!-- <div class="fiexd-header">
			<header>
				<div class="header-left">
					<a-breadcrumb separator=">">
						<a-breadcrumb-item>
							<a href="javascript:void(0)" @click="routerBack">
								{{ "问题管理" }}
							</a>
						</a-breadcrumb-item>
						<a-breadcrumb-item>{{ currentRow.name }}</a-breadcrumb-item>
					</a-breadcrumb>
				</div>
				<a-button
					icon="exclamation-circle"
					style="border: none"
					@click="handleDetail(currentRow)"
				>
					<a-icon
						type="exclamation-circle"
						:title="$t('查看详情')"
						theme="filled"
					/>
					{{ $t("查看详情") }}
				</a-button>
			</header>
		</div> -->

  <div class="report-container" id="reportContainer">
    <div class="report-title">
      <p class="title">
        {{ currentRow.name }}
      </p>
    </div>
    <div class="report-page" id="reportPage">
      <div class="report-panel">
        <p class="common-title" id="baseInfo">
          <span class="title-name">{{ $t("txt_base_info") }}</span>
          <span class="title-collspan" @click="handleColl('baseInfo')">
            {{ collspanList.baseInfo ? $t("txt_pack_up") : $t("txt_an") }}
          </span>
        </p>
        <jw-layout-builder
          v-if="collspanList.baseInfo"
          ref="ref_appBuilder"
          type="Model"
          :layoutName="'show'"
          :modelName="'Issue'"
          :instanceData="problemDetail"
        >
          <template #sourceSlot="{ formData }">
            <a-row :gutter="15">
              <a-col :span="12">
                <a-select
                  disabled
                  v-model.trim="source"
                  allowClear
                  :placeholder="$t('msg_select')"
                  @change="sourceChange"
                >
                  <a-select-option
                    v-for="item in sourceList"
                    :key="item.name"
                    :value="item.name"
                  >
                    {{ $t(item.name) }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="12">
                <a-input
                  disabled
                  v-if="source === '其它'"
                  v-model="sourceRemark"
                ></a-input>
              </a-col>
            </a-row>
          </template>
        </jw-layout-builder>
        <p class="common-title" id="objectInfo">
          <span class="title-name">{{ $t("txt_problem_affect_obj") }}</span>
          <span class="title-collspan" @click="handleColl('objectInfo')">
            {{ collspanList.objectInfo ? $t("txt_pack_up") : $t("txt_an") }}
          </span>
        </p>
        <div v-if="collspanList.objectInfo">
          <problem-object :currentRow="currentRow" :contentHeight="'300px'" />
        </div>
        <p class="common-title" id="schemaInfo">
          <span class="title-name">{{ $t("txt_proble_affect_area") }}</span>
          <span class="title-collspan" @click="handleColl('schemaInfo')">
            {{ collspanList.schemaInfo ? $t("txt_pack_up") : $t("txt_an") }}
          </span>
        </p>
        <!-- <jw-layout-builder
					v-if="collspanList.schemaInfo && currentRow.issueSchema"
					ref="ref_appBuilder_schema"
					type="Model"
					modelName="Issue"
					:layoutName="'schemaShow'"
					:view="true"
					:instanceData="currentRow.issueSchema"
				>
					<template slot="hasTechnologySlot">
						<a-radio-group
							v-model.trim="currentRow.issueSchema.hasTechnology"
							:disabled="true"
						>
							<a-radio :value="true">{{ $t('txt_yes') }}</a-radio>
							<a-radio :value="false">{{ $t('txt_no') }}</a-radio>
						</a-radio-group>
						<a-form-item
							:label="$t('txt_description')"
							v-if="currentRow.issueSchema.hasTechnology"
							style="margin-top: 12px"
						>
							<a-textarea
								v-model.trim="currentRow.issueSchema.hasTechnologyRemark"
								:disabled="true"
								:auto-size="{ minRows: 3, maxRows: 5 }"
							/>
						</a-form-item>
					</template>
					<template slot="hasManufactureSlot">
						<a-radio-group
							v-model.trim="currentRow.issueSchema.hasManufacture"
							:disabled="true"
						>
							<a-radio :value="true">{{ $t('txt_yes') }}</a-radio>
							<a-radio :value="false">{{ $t('txt_no') }}</a-radio>
						</a-radio-group>
						<a-form-item
							:label="$t('txt_description')"
							v-if="currentRow.issueSchema.hasManufacture"
							style="margin-top: 12px"
						>
							<a-textarea
								v-model.trim="currentRow.issueSchema.hasManufactureRemark"
								:disabled="true"
								:auto-size="{ minRows: 3, maxRows: 5 }"
							/>
						</a-form-item>
					</template>
					<template slot="hasAfterSalesSlot">
						<a-radio-group
							v-model.trim="currentRow.issueSchema.hasAfterSales"
							:disabled="true"
						>
							<a-radio :value="true">{{ $t('txt_yes') }}</a-radio>
							<a-radio :value="false">{{ $t('txt_no') }}</a-radio>
						</a-radio-group>
						<a-form-item
							:label="$t('txt_description')"
							v-if="currentRow.issueSchema.hasAfterSales"
							style="margin-top: 12px"
						>
							<a-textarea
								v-model.trim="currentRow.issueSchema.hasAfterSalesRemark"
								:disabled="true"
								:auto-size="{ minRows: 3, maxRows: 5 }"
							/>
						</a-form-item>
					</template>
				</jw-layout-builder> -->
        <p class="common-title" id="ecrInfo">
          <span class="title-name">{{ $t("txt_problem_relevant") }}ECR</span>
          <span class="title-collspan" @click="handleColl('ecrInfo')">
            {{ collspanList.ecrInfo ? $t("txt_pack_up") : $t("txt_an") }}
          </span>
        </p>
        <div v-if="collspanList.ecrInfo">
          <about-ecr
            :currentRow="currentRow"
            :isReport="true"
            :tableHeight="440"
          />
        </div>
        <p class="common-title" id="historyInfo">
          <span class="title-name">{{ $t("txt_problem_exe_record") }}</span>
          <span class="title-collspan" @click="handleColl('historyInfo')">
            {{ collspanList.historyInfo ? $t("txt_pack_up") : $t("txt_an") }}
          </span>
        </p>
        <a-timeline v-if="collspanList.historyInfo">
          <a-timeline-item
            v-for="(item, index) in currentRow.records"
            :key="index"
          >
            {{ item.handler }}- {{ formatDate(item.createDate) }} -
            {{ item.operator }}
          </a-timeline-item>
        </a-timeline>
        <div>
          <p class="common-title" id="processInfo">
            <span class="title-name">{{ $t("txt_processing_flow") }}</span>
            <span class="title-collspan" @click="handleColl('processInfo')">
              {{ collspanList.processInfo ? $t("txt_pack_up") : $t("txt_an") }}
            </span>
          </p>
          <div style="height: 400px">
            <FlowChart
              v-if="collspanList.processInfo"
              ref="flowChart"
              :processInstanceName="''"
              :showIcon="false"
              :processInstanceId="currentRow.processInstanceId || '72754'"
              :processOrderOid="''"
            ></FlowChart>
          </div>
        </div>
      </div>
    </div>
    <div class="report-timeline">
      <a-anchor
        :affix="true"
        :target-offset="targetOffset"
        :showInkInFixed="true"
        :getContainer="getContainer"
        @click="anchorJump"
      >
        <a-anchor-link
          v-for="item in timelineList"
          :key="item.href"
          :href="`#${item.href}`"
          :title="item.name"
        />
      </a-anchor>
    </div>
  </div>
  <!-- </div> -->
</template>

<script>
import Vue from "vue";
import ModelFactory from "jw_apis/model-factory";
import { jwIcon, jwLayoutBuilder } from "jw_frame";
import { Anchor } from "ant-design-vue";
Vue.use(Anchor);
import { formatDate } from "jw_utils/moment-date";
import FlowChart from "../components/flow-chart";
import aboutEcr from "../problem-detail/about-ecr/index.vue";
import problemObject from "../problem-detail/problem-object/index.vue";
import { findInfluenceInfo } from "apis/change";
import { findDetail } from "apis/baseapi";
// 产品容器列表
const fetchContainerList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/container/product/search`,
  method: "post",
});

// 问题管理列表
const fetchProblemList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/pr/issue/search`,
  method: "post",
});
const getSourceList = (params) =>
  ModelFactory.create({
    url: `${Jw.gateway}/${Jw.sysconfigServer}/preferences/setting/query-config-value`,
    method: "get",
  }).execute(params)
export default {
  name: "problem-report",
  inject: ["setBreadcrumb"],
  components: { jwIcon, jwLayoutBuilder, FlowChart, aboutEcr, problemObject },
  data() {
    return {
      targetOffset: undefined,
      formatDate,
      form: {},
      tableData: [],
      problemDetail: {},
      currentRow: {},
      influenceInfo: [],
      collspanList: {
        baseInfo: true,
        objectInfo: true,
        objectSchemaInfo: true,
        schemaInfo: true,
        ecrInfo: true,
        historyInfo: true,
        processInfo: true,
      },
      timelineList: [
        { name: this.$t("txt_base_info"), href: `baseInfo` },
        { name: this.$t("txt_problem_affect_obj"), href: `objectInfo` },
        // {
        // 	name: "受影响对象",
        // 	href: `objectSchemaInfo`,
        // },
        // { name: this.$t('txt_proble_affect_area'), href: `schemaInfo` },
        { name: this.$t("txt_problem_relevant") + "ECR", href: `ecrInfo` },
        { name: this.$t("txt_problem_exe_record"), href: `historyInfo` },
        { name: this.$t("txt_processing_flow"), href: `processInfo` },
      ],
      currentLink: "",
      source: "",
      sourceRemark: "",
      sourceList: [],
    };
  },
  computed: {
    columns() {
      return [
        {
          field: "name",
          title: this.$t("txt_number"),
          minWidth: "200px",
          slots: {
            default: "number",
          },
        },
        {
          field: "name",
          title: this.$t("txt_name"),
        },
        {
          field: "changeType.txt",
          title: this.$t("txt_status"),
        },
        {
          field: "version",
          title: this.$t("txt_version"),
        },
        {
          field: "type",
          title: this.$t("txt_type"),
        },
      ];
    },
  },
  created() {
    this.setBreadcrumb([]);
    this.fetchProblemDetail();
    this.fetchProblemList();
    this.findInfluenceInfoFun();
    this.getSourceList();
  },
  mounted() {
    this.targetOffset = 20;
  },
  watch:{
	problemDetail:{
		handler(val){
			this.initSource()
		},
		deep:true
	}
  },
  methods: {
    sourceChange() {
      this.sourceRemark = "";
    },
    handleDetail(row) {
      this.$router.push({
        name: `problem-detail`,
        path: `/problem-detail`,
        query: {
          oid: row.oid,
        },
      });
    },
    getSourceList() {
      getSourceList({ name: "problemSource" }).then((res) => {
        this.sourceList = res || [];
        this.initSource();
      });
    },
    initSource() {
		console.log(this.problemDetail)
      let value = this.problemDetail;
      let source = "";
      if (!this.sourceList.length) {
        return;
      }
      if (value.extensionContent) {
        source = value.extensionContent.source || "";
        this.source = source;
      }
      let obj = this.sourceList.find((ele) => ele.name === source);
      if (!obj&&source) {
        this.source = "其它";
        this.sourceRemark = source;
      }
    },
    getCurrentAnchor() {
      return "#baseInfo";
    },
    anchorJump(e, link) {
      e.preventDefault();
    },
    getContainer() {
      document.getElementsByClassName("ant-layout")[1].style.backgroundColor =
        "#fff";
      return document.getElementById("reportPage");
    },
    routerBack() {
      // this.$router.push({
      // 	path: "/problem-manage",
      // 	query: { tabActive: this.$route.query.tabActive },
      // });
      this.$router.back();
    },
    handleColl(key) {
      let value = this.collspanList[`${key}`];
      this.collspanList[`${key}`] = !value;
    },
    findInfluenceInfoFun() {
      let params = {
        oid: this.$route.query.oid,
        type: "Issue",
      };
      findInfluenceInfo
        .execute(params)
        .then((data) => {
          this.influenceInfo = [...data];
        })
        .catch((err) => {
          // this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    fetchProblemList() {
      let oid = this.$route.query.oid;
      fetchProblemList
        .execute()
        .then((data) => {
          console.log(data);
          this.tableData = data.rows;
          console.log(data.rows.filter((item) => item.oid === oid));
          this.currentRow = data.rows.filter((item) => item.oid === oid)[0];
        })
        .catch((err) => {
          // this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    fetchProblemDetail() {
      let params = {
        oid: this.$route.query.oid,
        type: "Issue",
      };
      findDetail
        .execute(params)
        .then((data) => {
          this.problemDetail = { ...data };
        })
        .catch((err) => {
          // this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    handleBack() {
      this.$router.push({
        name: "problem-manage",
        path: "/problem-manage",
      });
    },
    fetchTable({ current, pageSize }) {
      let params = {};
      return fetchContainerList
        .execute(params)
        .then((data) => {
          return { data: data.rows || data, total: data.count };
        })
        .catch((err) => {
          // this.$error(err.msg || this.$t("msg_failed"));
        });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.problem-object {
  height: auto !important;
}
/deep/.ant-timeline-item-content {
  cursor: pointer;
}
/deep/.ant-advanced-search-form .ant-form-item {
  margin-bottom: 10px;
}

.fiexd-header {
  // position: fixed;
  // width: 100%;
  // z-index: 999;
}

.report-title {
  width: 65%;
  margin: 0 auto;
  height: 100px;
  line-height: 100px;
  text-align: center;
  background-color: #fff;
  border-bottom: 1px solid rgba(30, 32, 42, 0.15);
  .title {
    font-weight: 500;
    font-size: 24px;
    color: rgba(30, 32, 42, 0.85);
  }
}

.page-container {
  width: 100%;
  overflow-y: hidden;
}
.detail-title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 60px;
  min-height: 60px;
  line-height: 60px;
  padding: 0 24px;
  background: #fff;
  .back-icon {
    margin-right: 10px;
    cursor: pointer;
    i {
      font-size: 20px;
    }
  }
  .title {
    font-weight: 500;
    font-size: 20px;
    color: rgba(30, 32, 42, 0.85);
  }
}

.report-container {
  width: 100%;
  height: 100%;
  background: #fff;
}
.report-page {
  width: 100%;
  height: calc(~"100% - 100px");
  overflow-y: scroll;
  .report-panel {
    width: 65%;
    margin: 0 auto 0 auto;
    padding-bottom: 40px;
  }
}
.report-timeline {
  position: fixed;
  width: 200px;
  right: 20px;
  top: 110px;
}

.common-title {
  margin-top: 20px;
  margin-bottom: 20px;
  padding-left: 8px;
  border-left: 3px solid #255ed7;
  .title-name {
    margin-right: 8px;
    font-weight: 500;
    font-size: 16px;
    color: rgba(30, 32, 42, 0.85);
  }
  .title-collspan {
    cursor: pointer;
    font-weight: 400;
    font-size: 12px;
    color: #255ed7;
  }
}
header {
  display: flex;
  justify-content: space-between;
  height: 60px;
  background-color: #fff;
  align-items: center;
  padding: 0 110px 0 24px;
  > .header-left {
    font-size: 20px;
    display: flex;
    a {
      color: #40a9ff;
    }
  }
}
.go-detail {
  // display: inline-block;
  margin-right: 6px;
  cursor: pointer;
}
</style>

