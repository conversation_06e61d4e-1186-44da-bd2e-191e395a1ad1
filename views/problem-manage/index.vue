<template>
	<div class="base-panel page-container">
		<a-tabs
			:default-active-key="defaultActiveKey"
			v-model.trim="tabActive"
			@change="callback"
		>
			<!-- <a-tab-pane key="1" :tab="$t('txt_problem_overview')">
				<problem-preview ref="problemPreview" />
			</a-tab-pane> -->
			<a-tab-pane key="1" :tab="$t('txt_problem_list')" force-render>
				<problem-list ref="problemList" />
			</a-tab-pane>
		</a-tabs>
	</div>
</template>

<script>
import problemPreview from "./problem-preview";
import problemList from "./problem-list";
export default {
	name: "overall-Preview",
	inject: ["setBreadcrumb"],
	components: { problemPreview, problemList },
	data() {
		return {
			defaultActiveKey: "1",
			tabActive: "1",
		};
	},
	computed: {},
	created() {
		this.setBreadcrumb([{ name: this.$t('txt_problem_management'), path: "/problem-manage" }]);
		this.tabActive = this.$route.query.tabActive == "list" ? "1" : "1";
	},
	mounted() {
		this.callback(this.$route.query.tabActive == "list" ? "1" : "1");
	},
	methods: {
		callback(key) {
			let { refList } = this;
			this.$router.push({
				name: "problem-manage",
				path: "/problem-manage",
				query: {
					tabActive: "list",
				},
			});
			localStorage.setItem("issueTab", key);
			if (key == "1") {
				// this.$nextTick(() => {
				// 	if (this.$refs[`problemPreview`]) {
				// 		this.$refs[`problemPreview`].init();
				// 	}
				// });
				this.$nextTick(() => {
					if (this.$refs[`problemList`]) {
						this.$refs[`problemList`].init();
					}
				});
			} else {
				this.$nextTick(() => {
					if (this.$refs[`problemList`]) {
						this.$refs[`problemList`].init();
					}
				});
			}
		},
	},
};
</script>

<style lang="less" scoped>
/deep/.ant-tabs .ant-tabs-content {
	height: 20px;
	flex-grow: 1;
}
::v-deep .ant-tabs-nav {
	margin-left: 16px;
}
/deep/ .ant-tabs-nav .ant-tabs-tab-active {
	color: rgba(30, 32, 42, 0.65) !important;
	font-weight: 500;
}
.jw-page {
	/deep/.ant-layout-content {
		overflow-y: hidden;
	}
}
.page-container {
	background: white;
	flex-grow: 1;
	overflow-y: auto;
	.ant-tabs {
		height: 100%;
		display: flex;
		flex-direction: column;
		.ant-tabs-content {
			height: 20px;
			flex-grow: 1;
		}
	}
	.product-config {
		display: flex;
		justify-content: flex-start;
		flex-wrap: nowrap;
	}
}
/deep/.ant-tabs-bar {
	border-bottom: 1px solid rgba(30, 32, 42, 0.15);
}
/deep/.ant-tabs-nav-wrap {
	padding-left: 24px;
}
/deep/.ant-tabs-nav .ant-tabs-tab {
	padding-left: 0;
	font-weight: 400;
	font-size: 14px;
	color: rgba(30, 32, 42, 0.65);
}
/deep/.ant-tabs-nav .ant-tabs-tab-active {
	font-weight: 500;
	font-size: 14px;
	color: rgba(30, 32, 42, 0.85);
}
/deep/.ant-tabs-top .ant-tabs-ink-bar-animated,
.ant-tabs-bottom .ant-tabs-ink-bar-animated {
	width: 56px !important;
	color: #255ed7;
}
</style>
