<template>
  <div class="all-user">
    <div class="table-search-line">
      <a-input-search
        v-model.trim="searchAllUserKey"
        :placeholder="$t('search_text')"
        style="width: 250px"
        @search="loadCurrentTeamUsers"
      />
    </div>
    <jw-table
      height="500"
      row-id="oid"
      ref="all-user"
      :showPage="false"
      :columns="allUserGetHeader"
      :dataSource="currentRowAllUser"
      @onOperateClick="clickBtn"
    >
      <template #name="{ row }">
        <div>
          <jw-avatar :showName="true" :tag="true" :data="row" />
        </div>
      </template>
      <template #role="{ row }">
        <div>
          {{ row.roles.map((item) => item.displayName).toString() }}
        </div>
      </template>
    </jw-table>
  </div>
</template>

<script>
import {
  getUserListByTeam,
  productGetTeamAllUser,
  batchRemoveTeamRoleUser,
  productBatchRemoveUsers,
} from "../apis/index";
import { jwAvatar } from "jw_frame";
import { getCookie } from "jw_utils/cookie";
export default {
  components: {
    jwAvatar,
  },
  data() {
    return {
      searchAllUserKey: "",
      currentRowAllUser: [],
      containerOid: "",
    };
  },
  props: {
    teamRow: {
      type: Object,
    },
    type: {
      type: String,
    },
  },
  created() {
    const { oid } = this.$route.query;
    this.containerOid = oid;
    this.loadCurrentTeamUsers();
  },
  computed: {
    allUserGetHeader() {
      return [
        {
          field: "name",
          title: this.$t("txt_user_name"),
          slots: {
            default: "name",
          },
        },
        {
          field: "account",
          title: this.$t("txt_account"),
        },
        {
          field: "phone",
          title: this.$t("txt_contact"),
        },
        {
          field: "email",
          title: this.$t("txt_email"),
        },
        {
          field: "role",
          title: this.$t("btn_users"),
          slots: {
            default: "role",
          },
        },
        {
          // 操作列定义
          field: "operation", //关键字
          title: this.$t("txt_operation"),
          btns: [
            {
              icon: "jwi-icondelete",
              title: this.$t("btn_delete"),
              key: "delete",
            },
          ],
        },
      ];
    },
  },
  methods: {
    clickBtn(key, row) {
      switch (key) {
        case "delete":
          this.deleteUser(row);
          break;

        default:
          break;
      }
    },
    deleteUser(row) {
      this.$confirm({
        title: this.$t("msg_role_delete"),
        content: this.$t("msg_confirm_deletion"),
        class: "deleteModal",
        okText: this.$t("btn_ok"),
        cancelText: this.$t("btn_cancel"),
        onOk: () => {
          this.batchdeleteFun(row);
        },
      });
    },
    batchdeleteFun(row) {
      let req;
      if (this.validSysTeam()) {
        let param = {
          tenantOid: getCookie("tenantOid"),
          userOid: row.oid,
          roleIds: row.roles.map((item) => item.oid),
        };
        req = batchRemoveTeamRoleUser(param);
      } else {
        let param = {
          containerOid: this.containerOid,
          userOid: row.oid,
          roles: row.roles.map(item => item.oid)
        }
        req = productBatchRemoveUsers(param)
      }
      req
        .then((resp) => {
          this.$success(this.$t("txt_delete_success"));
          this.loadCurrentTeamUsers();
        })
        .catch((e) => {
          this.$error(e.msg || this.$t("msg_failed"));
        });
    },
    //验证是否是组织管理的团队
    validSysTeam() {
      return this.type === "team";
    },
    loadCurrentTeamUsers() {
      let req;
      if (this.validSysTeam()) {
        const param = {
          teamTemplateOid: this.teamRow.oid,
          userName: this.searchAllUserKey,
        };
        req = getUserListByTeam.execute(param);
      } else {
        req = productGetTeamAllUser(this.teamRow.oid, this.searchAllUserKey);
      }
      req
        .then((data) => {
          this.currentRowAllUser = [...data];
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
  },
};
</script>

<style lang="less" scoped>
.all-user {
  padding-left: 20px;
  padding-right: 20px;
}
.table-search-line {
  margin-bottom: 10px;
}
</style>