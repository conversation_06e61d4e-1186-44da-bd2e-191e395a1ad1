<template>
  <div>
    <a-tabs @change="tabCallBack">
      <a-tab-pane key="1" :tab="$t('detailed_info')" v-if="type === 'team'">
        <div class="viewform">
          <div class="savebtn" v-if="drawerStatus === 'edit'">
            <a-button :loading="saveLoading" size="small" @click="handleSave">{{ $t("btn_save") }}
            </a-button>
          </div>

          <a-form :form="form" layout="horizontal">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item :label="$t('txt_name')" :colon="false">
                  <a-input :placeholder="$t('placeholder_name')" :disabled="drawerStatus === 'detail' ? true : false" v-decorator="[
                      'name',
                      {
                        initialValue: teamData.name,
                        rules: [
                          {
                            required: true,
                            message: $t('placeholder_name'),
                          },
                        ],
                      },
                    ]" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="24" v-if="teamData.modelDefinition=='PdmTeamTemplate'">

                <a-form-model-item label="是否私有" prop="privateFlag">

                  <a-radio-group v-decorator="[
                      'privateFlag',
                      {
                        initialValue: teamData.privateFlag,
                        rules: [
                          {
                            required: true,
                            message: $t('请选择'),
                          },
                        ],
                      },
                    ]" :colon="false">
                    <a-radio :value="true">
                      是
                    </a-radio>
                    <a-radio :value="false">
                      否
                    </a-radio>
                  </a-radio-group>

                </a-form-model-item>
              </a-col>
              <a-col :span="24">
                <a-form-item :label="$t('txt_description')" :colon="false">
                  <a-textarea :placeholder="$t('txt_enter_description')" :rows="4" :disabled="drawerStatus === 'detail' ? true : false" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item :label="$t('txt_update_by')" :colon="false">
                  <jw-avatar tag show-name :data="{ name: teamData.updateBy, src: '' }" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :label="$t('txt_update_date')" :colon="false">
                  <a-date-picker disabled v-decorator="[
                      'dateTime',
                      {
                        initialValue: formatDate(teamData.updateDate),
                        value: formatDate(teamData.updateDate),
                        rules: [
                          {
                            required: true,
                            message: 'Please choose the dateTime',
                          },
                        ],
                      },
                    ]" :showTime="{ disabledSeconds: false }" style="width: 100%" :get-popup-container="(trigger) => trigger.parentNode" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-tab-pane>
      <a-tab-pane key="2" :tab="$t('txt_view_role')">
        <team-roleuser :teamRow="teamData" :type="type" ref="team-roleuser" />
      </a-tab-pane>
      <a-tab-pane key="3" :tab="$t('txt_view_user')">
        <team-user ref="team-user" :teamRow="teamData" :type="type" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import { formatDate } from "jw_utils/moment-date";
import { jwAvatar, jwUserModalV2 } from "jw_frame";
import { getCookie } from "jw_utils/cookie";
import { updateTeamModel } from "../apis/index";
import TeamRoleuser from "./team-roleuser.vue";
import TeamUser from "./team-user.vue";
export default {
  components: {
    jwAvatar,
    TeamRoleuser,
    TeamUser,
    jwUserModal: jwUserModalV2
  },
  props: {
    teamData: {
      type: Object
    },
    drawerStatus: {
      type: String
    },
    type: {
      type: String
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      formatDate,
      saveLoading: false
    };
  },
  created() {},
  methods: {
    tabCallBack(val) {
      if (val == "3") {
        this.$nextTick(() => {
          this.$refs["team-user"].loadCurrentTeamUsers();
        });
      } else if (val == "2") {
        this.$nextTick(() => {
          this.$refs["team-roleuser"].reloadUser();
        });
      }
    },
    handleChange(event) {
      let value = event.target.value;
      this.form.setFieldsValue({ name: value });
    },
    // 编辑当前团队
    handleSave() {
      let { teamData } = this;
      let tenantOid = getCookie("tenantOid");
      let name = this.form.getFieldValue("name");
      let param = {
        containerModelType: "Tenant",
        containerOid: tenantOid,
        oid: teamData.oid,
        name: name,
        modelDefinition:teamData.modelDefinition,
        description: this.form.getFieldValue("description"),
        disabled: teamData.disabled,
        privateFlag: this.form.getFieldValue("privateFlag")
      };
      this.saveLoading = true;
      updateTeamModel
        .execute(param)
        .then(data => {
          this.$success(this.$t("msg_save_success"));
        })
        .catch(err => {
          this.$error(err.msg || this.$t("msg_failed"));
        })
        .finally(() => {
          this.saveLoading = false;
        });
    }
  }
};
</script>

<style lang="less" scoped>
.viewform {
  width: 60%;
  margin-left: 20%;
  .savebtn {
    display: flex;
    justify-content: flex-end;
  }
}
</style>