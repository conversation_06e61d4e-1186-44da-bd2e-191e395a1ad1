<template>
  <div style="height: 600px" class="main-content">
    <TeamView v-if="teamData" :teamData="teamData" :type="type" :drawerStatus="drawerStatus" />
  </div>
</template>

<script>
import TeamView from "./components/team-view.vue";
import { productDetailContainer, getFolderTeamApi } from "./apis/index";
export default {
  components: {
    TeamView,
  },
  props: {
    folder: {
      type: Object,
      default: () => { }
    }
  },
  provide(){
    return {
      folder: this.folder
    }
  },
  data() {
    return {
      teamName: "",
      teamData: null,
      //根据类型获取团队详情
      type: "",

      initData: null,
      drawerStatus: "edit",
    };
  },
  created() {
    if (this.folder) {
      const { oid } = this.folder;
      this.getFolderTeam(oid);
    }else{
      const { oid, type } = this.$route.query
      this.getItemDetail(oid, type)
    }
  },
  methods: {
    // 获取当前产品容器团队信息
    getItemDetail(oid, type) {
      productDetailContainer
        .execute({ oid, type })
        .then((data) => {
          this.teamData = data.team;
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    //获取文件夹团队
    getFolderTeam(oid){
      getFolderTeamApi.execute({folderOid: oid}).then(resp => {
        this.teamData = resp
        console.log("文件夹团队", this.teamData)
      })
    }
  },
};
</script>

<style lang="less" scoped>
.main-content {
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.header-left {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  min-height: 60px;
  line-height: 60px;
  padding: 0 24px;
  padding-right: 40px;
  background: #fff;
}
</style>