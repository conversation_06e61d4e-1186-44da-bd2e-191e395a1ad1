import ModelFactory from 'jw_apis/model-factory';

//获取团队列表
const libraryTableModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/search/all`,
  method: "get",
});

// 新增团队
const createTeamModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/create`,
  method: "post",
});

// 更新团队
const updateTeamModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/update`,
  method: "post",
})

// 删除团队
const deleteTeamModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/delete`,
  method: "post",
})

// 查询当前团队下的角色
const fetchTeamRole = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/templaterole/search/all`,
  method: "get",
})

// 绑定角色
const addRoleModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/assign/create/batch/teamTemplate/teamtemplaterole`,
  method: "post",
});

// 绑定用户
const bindUserModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/assign/add/batch/user/teamtemplaterole`,
  method: "post",
});

// 获取当前角色下的用户
const getRoleUserList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/templaterole/users`,
  method: "get",
});

//查询当前team下所有的人员信息
const getUserListByTeam = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/templaterole/search/userWithRole`,
  method: "get",
})

// 团队另存为
const saveAsModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/saveas`,
  method: "post",
});

//批量移除团队人员信息
export const batchRemoveTeamRoleUser = function (val){
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountMicroServer}/team/batch/assign/remove/user/teamtemplaterole`,
    method: "post"
  }).execute(val)
}

//产品容器中团队管理
// 产品容器详情
export const productDetailContainer = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/container/product/findDetail`,
    method: 'get',
})

//获取文件夹团队
export const getFolderTeamApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/pdm-folder/getTeamRole`,
  method: 'get'
})

// 查询当前团队下的角色
export const productGetTeamRole = function (teamOid, searchKey) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/team/fuzzyContent?teamOid=${teamOid}&searchKey=${searchKey}`,
    method: "get",
  }).execute()
}

// 绑定角色
export const productAddRoleModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/team/bindRole`,
  method: "post",
});

// 绑定用户
export const productBindUserModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/team/bindUser`,
  method: "post",
});

// 获取当前角色下的用户
export const productGetRoleUserList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/templaterole/users`,
  method: "get",
});

//获取团队角色下用户信息
export const productGetTeamRoleUser = function(teamRowOid, search){
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/team/searchTeamUser?teamRoleOid=${teamRowOid}&searchKey=${search}`,
    method: "get",
  }).execute({teamTemplateRoleOid: teamRowOid})
}

//获取所有人员信息
export const productGetTeamAllUser = function (teamOid, userName){
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/team/searchUserWithRole`,
    method: "get",
  }).execute({teamOid, userName})
}

//团队人员移除
export const productBatchRemoveUsers = function(param){
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/team/batchUnBindUser`,
    method: "post"
  }).execute(param)
}


export {
  libraryTableModel,
  createTeamModel,
  deleteTeamModel,
  addRoleModel,
  bindUserModel,
  getRoleUserList,
  saveAsModel,
  fetchTeamRole,
  updateTeamModel,
  getUserListByTeam
}
