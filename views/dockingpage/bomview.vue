<template>
  <div class="main-page">
    <Structure v-if="provideData" ref="structure" :objectDetailsData="provideData" @init="init">
    </Structure>
  </div>
</template>

<script>
import Structure from "views/product-container/object-details/structure";
import { findDetail } from "apis/baseapi";
import { setCookie } from "jw_utils/cookie";
export default {
  components: {
    Structure
  },
  data() {
    return {
      loading: false,
      provideData: null,
    }
  },
  provide() {
    return {
      detailsData: () => this.provideData,
    }
  },
  created() {
    this.findDetailFun()
  },
  methods: {
    init() {
      console.log('init')
    },
    findDetailFun() {
      let params = {
        oid: this.$route.query.oid,
        type: this.$route.query.type
      }
      this.loading = true
      findDetail.execute(params).then(resp => {
        this.provideData = resp
        // tenantAlias: getCookie("tenantAlias"),
        setCookie('tenantOid', this.provideData.tenantOid)
        this.$nextTick(() => {
          if (this.$refs.structure) {
            this.$refs.structure.init();
          }
        })

      }).finally(() => {
        this.loading = false
      })
    }
  }

}
</script>

<style lang="less" scoped>
.main-page {
  height: 100%;
  padding-top: 8px;
}
</style>