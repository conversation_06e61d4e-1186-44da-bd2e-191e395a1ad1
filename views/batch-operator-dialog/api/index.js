import ModelFactory from "jw_apis/model-factory";

//加载相关对象类型
export const findAllrelationType = function (type) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.sysconfigServer}/collectionRule/findByAppliedType`,
    method: "get"
  }).execute({ appliedType: `${type}_Related_Object`, mainObjectType: type })
}

// 获取对象列表
export const findRelationList = function (param) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/instance/relatedObject/fuzzy`,
    method: "post"
  }).execute(param)
}

//bom对象收集
export const findPartRelList = function(oid){
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/collectionList`,
    method: 'get'
  }).execute({oid: oid})
}

//修改阶段
export const updateStage = (list, currentStage) => {
  let simpleList = list.map((item) => {
    return {
      oid: item.oid,
      type: item.type
    }
  })
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/updateStage`,
    method: "post",
  }).execute({list: simpleList, currentStage});
}


//cad对象收集
export const findMcadRelList = function(oid){
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.cadService}/mcad/collectionList`,
    method: 'get'
  }).execute({oid: oid})
}

const SERVER_DOMAIN = {
  Part: `${Jw.gateway}/${Jw.partBomMicroServer}/part`,
  Document: `${Jw.gateway}/${Jw.docMicroServer}/document`,
  MCAD: `${Jw.gateway}/${Jw.cadService}/mcad`,
  ECAD: `${Jw.gateway}/${Jw.cadService}/ecad`,
  DocumentTemplateMaster: `${Jw.gateway}/${Jw.docMicroServer}/documentTemplate`,
  Delivery: `${Jw.gateway}/${Jw.productDeliveryServer}/delivery`,
};

// 批量移动
export const batchMoveAction = function (datalist, treeData) {
  const { containerOid, containerType } = treeData
  let param = {
    nodes: datalist.map(item => {
      return {
        modelDefinition: item.modelDefinition,
        oid: item.oid,
        type: item.type,
      }
    }),
    target: {
      catalogOid: treeData.oid,
      catalogType: treeData.type,
      containerOid,
      containerType,
    },
  }

  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.cadService}/batchAllMove/move`,
    method: "post",
  }).execute(param)
}


//批量另存为
export const batchSaveAs = function (type, dataList, treeData, splitcode, beforcode, currentStage) {
  const { containerOid, containerType } = treeData
  return ModelFactory.create({
    url: `${SERVER_DOMAIN[type]}/batchCopy`,
    method: "post",
  }).execute({
    currentStage,
    sourceOids: dataList.map(item => item.oid),
    locationInfo: {
      catalogOid: treeData.oid,
      catalogType: treeData.type,
      containerOid,
      containerType,
    },
    names: dataList.map(item => {
      return {
        sourceOid: item.oid,
        newName: item.newname ? item.newname : ((beforcode ? beforcode : '') + item.name + splitcode)
        
      }
    }),
    //创建新链接关系
    linkRelationship: dataList.filter(item => item.preSourceOid).map(item => {
      return {
        sourceOid: item.oid,
        preSourceOid: item.preSourceOid
      }
    }),
    //需要清理旧的关联关系
    unLinkRelationship: dataList.filter(item => item.referenceOid).map(item => {
      return {
        sourceOid: item.oid,
        preSourceOid: item.referenceOid
      }
    })
  })
}

export const batchScreenSaveAs = function (operationType,type, dataList, treeData, splitcode, beforcode, currentStage) {
  const { containerOid, containerType } = treeData
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.customerServer}/part/screenSave/batchScreen`,
    method: "post",
  }).execute({
    currentStage,
    type:operationType,
    sourceOids: dataList.map(item => item.oid),
    locationInfo: {
      catalogOid: treeData.oid,
      catalogType: treeData.type,
      containerOid,
      containerType,
    },
    names: dataList.map(item => {
      return {
        sourceOid: item.oid,
        newName: item.newname ? item.newname : ((beforcode ? beforcode : '') + item.name + splitcode)

      }
    }),
    //创建新链接关系
    linkRelationship: dataList.filter(item => item.preSourceOid).map(item => {
      return {
        sourceOid: item.oid,
        preSourceOid: item.preSourceOid
      }
    }),
    //需要清理旧的关联关系
    unLinkRelationship: dataList.filter(item => item.referenceOid).map(item => {
      return {
        sourceOid: item.oid,
        preSourceOid: item.referenceOid
      }
    })
  })
}

/**
 * 创建批量流程
 * @param {*} param 
 * @returns 
 */
export const batchCreateThenStart = function (selectList, params, locationInfo) {
  let listParam = selectList.map(item => {
    return {
      ...params,
      bizObjects: [{
        oid: item.oid,
        type: item.type,
        modelDefinition: item.modelDefinition,
        primary: item.selectParendOid ? false : true
      }],
      locationInfo
    }
  })

  // 创建并启动流程
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/batchCreateThenStart`,
    method: "post",
  }).execute(listParam)
}

/**
 * 上下文目录
 */

export const folderTree = (param) => {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/folder/searchTree`,
    method: 'get'
  }).execute(param)
}

// 查询容器下的基线列表
export const getBaselineList = function (containerOid, searchKey) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/model/query`,
    method: 'post',
  }).execute({
    index: 1,
    page: 1,
    size: 99999,
    searchKey,
    containerOid,
  })
}

// 查询 扩展？ 属性
export const findExtendedProperty = function (param) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/classification/find/withProperty`,
    method: "get",
  }).execute(param)
}
//查询密级
export const getLevelList = function (param) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.securityService}/secret/specialSearch`,
    method: "get"
  }).execute(param)
}

// 获取该条数据可操作下拉列表
export const getBaseLinePermission = function (param) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
    method: "post",
  }).execute(param)
}

// 创建基线
export const createBaseline = function (param, containerOid, containerType) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/model/create`,
    method: 'post',
  }).execute({
    ...param,
    catalogOid: containerOid,
    catalogType: containerType,
    containerOid,
    containerType
  })
}

// 添加基线对象
export const batchLink = function (lineOid, selectList) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/batchLink`,
    method: 'post',
  }).execute({
    isReplace: false,
    needExecuteFilter: true,
    oid: lineOid,
    secObjList: selectList.map(v => {
      return {
        modelIcon: v.modelIcon,
        name: v.name,
        number: v.number,
        status: v.lifecycleStatus,
        displayVersion: v.displayVersion,
        wideType: v.type,
        sourceOid: v.oid,
        modelDefinition: v.modelDefinition,
        bizMasterType: v.masterType,
        subject: true,
        children: [],
        lockedTime: v.lockedTime,
        lockOwnerAccount: v.lockOwnerAccount,
        lockOwnerOid: v.lockOwnerOid,
        lockSourceOid: v.lockSourceOid,
      }
    })
  })
}

//查询mcad bom结构
export const findSecBom = function (oid) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.cadService}/mcad/useTree/findUseAllOrLevel`,
    method: 'get',
  }).execute({ rootOid: oid })
}

//查询part bom结构
export const findPartBom = function (oid) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/useTree/find`,
    method: 'get',
  }).execute({
    rootOid: oid
  })
}

//查询配置信息
export const findConf = function (val) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.sysconfigServer}/preferences/setting/query-config-value?name=${val}`,
    method: "get",
  }).execute();
}

// 权限查询
export const checkPermission = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
  method: "post",
});

