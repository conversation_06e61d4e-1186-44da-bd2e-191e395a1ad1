<template>
  <div class="main-page">
    <div class="toolbar" v-if="showtoolbar">
      <a-button type="primary" @click="addobject">{{
        $t("txt_add_object")
      }}</a-button>
    </div>
    <div v-if="type === 'changestage'" class="stage-tool">
      <a-select v-model="currentStage" class="stage-select" :placeholder="$t('change_stage')">
        <a-select-option :value="item" v-for="(item, index) in stageList" :key="index">{{ item }}</a-select-option>
      </a-select>
    </div>
    <!-- 暂时不用复选框 -->
    <!-- :selectedRows.sync="modalSelectedRowsValue" -->
    <jw-table ref="jwtable" :data-source.sync="dataList" :columns="modalColumns" :showPage="false" :height="height"
      :loading="loading" :disableCheck="disableCheckFun">
      <template #thumbSlot="{ row }">
        <keep-alive>
          <div style="height: 100%;">
            <div class="thumb-view" :title="$t('txt_filePreview')" v-if="validThumbType(row)">
              <jwIcon type="#jwi-PDFwendang" />
            </div>
            <cad-thumbnail v-if="validcadthumb(row)" :currentRow="row"></cad-thumbnail>
          </div>
        </keep-alive>
      </template>

      <template #numberSlot="{ row }">
        <span>
          <jwIcon :type="numbericonType(row)" />
          <span style="color: #255ed7">{{ row.number }}</span>
          <span class="mainobject" v-if="!row.selectParendOid">{{
            $t("txt_object_main")
          }}</span>
        </span>
      </template>

      <template #nameSlot="{ row }">
        <div>
          <div v-if="validChangeName(row) && showinput" class="namegroup">
            <a-input v-model.trim="rownewnames[row.oid]" :default-value="beforcode + row.name + splitcode"
              :maxLength="100" v-if="type!=='forming'"></a-input>
            <a-input v-model.trim="rownewnames[row.oid]" :default-value="row.name + splitcode"
                     :maxLength="100" v-if="type==='forming'" :disabled="true"></a-input>
          </div>
          <div v-else>
            {{ row.name }}
          </div>
        </div>
      </template>

      <template #operation="{ row }">
        <div class="btn-group">
          <a-tooltip placement="top" :title="$t('txt_collecting_Objects')">
            <span @click="addrelationrow(row)">
              <i class="jwi-chanpinxingpu" />
            </span>
          </a-tooltip>

          <a-tooltip placement="top" :title="$t('btn_delete')">
            <span @click="removerow(row)">
              <i class="jwi-icondelete" />
            </span>
          </a-tooltip>
        </div>
      </template>
    </jw-table>
  </div>
</template>

<script>
import cadThumbnail from "components/cad-thumbnail.vue";
import { jwIcon } from "jw_frame";
import { findConf } from "../api";
export default {
  props: {
    selectList: {
      type: Array,
      default: () => [],
    },
    modalSelectedRows: {
      type: Array,
      default: () => [],
    },
    relationshow: {
      type: Boolean,
    },
    type: {
      type: String,
    },
    height: {
      type: Number,
      default: 400,
    },
    showtoolbar: {
      type: Boolean,
      default: false,
    },
    splitcode: {
      type: String,
    },
    beforcode: {
      type: String,
    },
    loading: {
      type: Boolean,
      default: false,
    }
  },
  components: {
    cadThumbnail,
    jwIcon,
  },
  data() {
    return {
      dataList: [],
      rownewnames: {},
      showinput: true,
      currentStage: '',
      stageList: [],
    };
  },
  watch: {
    dataList: function (val, oldval) {
      if (val.length > oldval.length) {
        this.selectDefault();
      }
    },
    type: {
      handler: function (val) {
        if (val === 'changestage') {
          this.stagesList()
        } else {
          this.currentStage = ''
        }
      },
      immediate: true
    }
  },
  computed: {
    modalSelectedRowsValue: {
      get() {
        return this.modalSelectedRows;
      },
      set(val) {
        this.$emit("update:modalSelectedRows", val);
      },
    },
    modalColumns() {
      return [
        {
          field: "thumbnailOid",
          title: "",
          params: {
            showHeaderMore: false,
          },
          width: '40px',
          className: "thumbSlotclass",
          slots: {
            default: "thumbSlot",
          },
        },
        {
          field: "number",
          title: this.$t("txt_number_of"),
          sortable: true,
          slots: {
            default: "numberSlot",
          },
        },
        {
          field: "name",
          title: this.$t("txt_name"),
          sortable: true,
          slots: {
            default: "nameSlot",
          },
        },
        {
          field: "lifecycleStatus",
          title: this.$t("txt_plan_lifecycle"),
          formatter: ({ row }) => this.$t(row.lifecycleStatus),
          sortable: true,
        },
        {
          field: "displayVersion",
          title: this.$t("txt_plan_version"),
          sortable: true,
        },
        {
          // 操作列定义
          field: "operation", //关键字
          title: this.$t("txt_operation"),
          slots: {
            default: "operation",
          },
          isVisible: this.type !=='screen' || this.type!=='forming'
        },
      ];
    },
  },
  created() {
    this.dataList = [...this.selectList];
    this.filterotherdata()
  },
  methods: {
    //获取阶段
    stagesList() {
      findConf('part_document_stages').then(resp => {
        this.stageList = resp.map((item) => item.value)
        if (this.stageList.length > 0) {
          this.currentStage = this.stageList[0]
        } else {
          console.error('请在系统配置part_document_stages值')
        }
      })
    },
    validChangeName(row) {
      return (
        (this.type === "saveAs" || this.type === 'changestage' || this.type === 'screen' || this.type==='forming') &&
        (row.masterType === "MCAD" ||
          row.masterType === "Part" ||
          row.masterType === "ECAD")
      );
    },
    //验证禁用的数据
    disableCheckFun({ row }) {
      if (row.lockOwnerAccount) {
        //检出数据不能批量操作
        return true;
      }
      if ((row.masterType !== "Part" && row.masterType !== 'Document' && this.type === "saveAs")) {
        //非部件文档类型不能另存
        return true;
      }
      if (this.type === 'changestage' && row.masterType !== 'Part' && row.masterType !== 'Document') {
        //非文档类型和part类型不能转阶段
        return true
      }
      return false;
    },
    //新增数据默认选中
    selectDefault() {
      this.modalSelectedRowsValue = this.dataList.filter(
        (item) => !this.disableCheckFun({ row: item })
      );
    },
    addobject() {
      this.$emit("openselectdialog");
    },
    inittabledata(val) {
      if (val) {
        this.dataList = [...this.selectList];
        this.filterotherdata()
      } else {
        this.dataList = [];
      }
    },
    //打开收集对象
    addrelationrow(row) {
      this.$emit("addrelationrow", row, this.dataList);
    },
    changerelationselect(val, selectParendOid) {
      let selectData = val.map((item) => {
        return {
          ...item,
          selectParendOid,
        };
      });
      let dataListOids = this.dataList.map((item) => item.oid);
      let needAddList = selectData.filter(
        (item) => !dataListOids.includes(item.oid)
      );
      this.dataList = [...this.dataList, ...needAddList];
      //过滤禁用的选项，另存只能另存part
      this.filterotherdata()
    },
    //过滤不能操作的数据
    filterotherdata() {
      let disabledata = []
      this.dataList = this.dataList.filter((item) => {
        let res = this.disableCheckFun({ row: item })
        if (res) {
          disabledata.push(item)
        }
        return !res
      }
      );
      if (disabledata.length !== 0) {
        this.$warning(this.$t("txt_batch_operation_filter") + "：" + disabledata.map(item => item.number));
      }
    },
    //删除
    removerow(row) {
      this.showinput = false;
      let selectIndex = this.dataList.findIndex((item) => item.oid === row.oid);
      if (selectIndex !== -1) {
        this.dataList.splice(selectIndex, 1);
      }

      //清理已选择项
      let chooseIndex = this.modalSelectedRows.findIndex(
        (item) => item.oid === row.oid
      );
      if (chooseIndex !== -1) {
        this.modalSelectedRows.splice(chooseIndex, 1);
      }
      setTimeout(() => {
        this.showinput = true;
      }, 0);
    },
    numbericonType(row) {
      return row.modelIcon;
    },

    validcadthumb(row) {
      return (
        row.thumbnailOid &&
        row.modelDefinition != "CADDrawing" &&
        row.modelDefinition != "PCB" &&
        row.modelDefinition != "Schematic"
      );
    },

    validThumbType(row) {
      return (
        row.modelDefinition == "CADDrawing" ||
        row.modelDefinition == "PCB" ||
        row.modelDefinition == "Schematic"
      );
    },
  },
};
</script>

<style lang="less" scoped>
.toolbar {
  margin-bottom: 10px;
}

.thumb-view {
  display: flex;
  justify-content: center;
  font-size: 25px;
  cursor: pointer;
  height: 100%;
  align-items: center;
}

.btn-group {
  display: flex;
  justify-content: space-around;
}

.btn-group span {
  cursor: pointer;
}

.mainobject {
  background: #f0f7ff;
  border: 1px solid #a4c9fc;
  border-radius: 4px;
  font-size: 12px;
  color: #255ed7;
  padding: 1px 2px 1px 2px;
}

.namegroup {
  display: flex;
  align-items: center;
}

.stage-tool {
  margin-bottom: 8px;

  .stage-select {
    width: 220px;
  }
}
</style>