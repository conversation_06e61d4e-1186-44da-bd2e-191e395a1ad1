<template>
  <div>
    <a-modal
      :title="title"
      :visible.sync="visibleshow"
      :cancelText="$t('btn_cancel')"
      :okText="$t('btn_confirm')"
      @ok="confrim"
      width="80%"
      @cancel="visibleshow = false"
    >
      <div class="area-split">
        <div class="area-left">
          <a-input-search
            :placeholder="$t('search_text')"
            class="search-input"
            v-model.trim="searchKey"
            @search="searchlist"
          ></a-input-search>

          <a-tabs v-if="!loadingtable">
            <a-tab-pane
              :key="index"
              v-for="(item, index) in tabsList"
              :tab="item.name"
              :forceRender="true"
            >
              <a-spin :spinning="item.loading">
                <!-- <jw-table
                  ref="relation-table"
                  :height="550"
                  :showPage="false"
                  :columns="tableColumn"
                  :data-source="item.listdata"
                  :selectedRows.sync="tablesSelectRows[index]"
                  :checkbox-config="{
                    reserve: true,
                  }"
                >
                  <template #updateDate="{ row }">
                    <span>
                      {{ formatDate(row.updateDate) }}
                    </span>
                  </template>
                </jw-table> -->
                <TableVirtualScroller
                  :items="item.listdata"
                  :columns="tableColumn"
                  :selectedRows.sync="tablesSelectRows[index]"
                >
                  <template #updateDate="{ row }">
                    <div>
                      {{ formatDate(row.updateDate) }}
                    </div>
                  </template>
                </TableVirtualScroller>
              </a-spin>
            </a-tab-pane>

            <a-tab-pane :key="0" v-if="tabsList.length == 0" class="defaulttab">
            </a-tab-pane>
          </a-tabs>
          <div class="spin-area" v-else>
            <a-spin />
          </div>
        </div>
        <div class="area-right">
          <div class="right-select-head">
            <div class="select-count">
              {{ $t('txt_added') + selectList.length + $t('txt_item') }}
            </div>
            <div class="clear-btn" @click="clearSelect">
              {{ $t('txt_empty') }}
            </div>
          </div>
          <div class="right-select-list">
            <div
              class="select-line"
              v-for="(item, index) in selectList"
              :key="index"
            >
              <div class="select-name">
                {{
                  item.number +
                  ',' +
                  (item.name && item.name.length > 8
                    ? item.name.substring(0, 8) + '...'
                    : item.name) +
                  ',' +
                  $t(item.lifecycleStatus) +
                  ',' +
                  item.displayVersion
                }}
              </div>

              <div class="remove-select-btn" @click="removeselect(index)">
                <i class="jwi-iconclose-circle-full"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import {
  findAllrelationType,
  findRelationList,
  findSecBom,
  findPartBom,
  findConf,
  findPartRelList,
  findMcadRelList
} from '../api'
import { formatDate } from 'jw_utils/moment-date'
import { jwTableVirtualScroller } from 'jw_frame'
import { reductionArr } from 'utils/util'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    //被收集对象的数据
    rowData: {
      type: Object,
    },
    //默认选中项
    defaultSelect: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    TableVirtualScroller: jwTableVirtualScroller,
  },
  data() {
    return {
      title: this.$t('txt_collecting_Objects'),

      searchKey: '',
      //关联关系对象类型
      tabsList: [],
      //原始tabs数据
      tabsInitData: [],

      tablesSelectRows: [],

      formatDate,

      tableColumn: [
        {
          title: this.$t('txt_name'),
          field: 'name',
        },
        {
          title: this.$t('txt_type'),
          field: 'modelDefinition',
        },
        {
          title: this.$t('txt_plan_number'),
          field: 'number',
        },
        {
          title: this.$t('txt_plan_lifecycle'),
          field: 'lifecycleStatus',
        },
        {
          title: this.$t('txt_version'),
          field: 'displayVersion',
        },
        {
          title: this.$t('txt_update_date'),
          field: 'updateDate',
          slots: {
            default: 'updateDate',
          },
        },
      ],

      loadingtable: false,
    }
  },
  computed: {
    visibleshow: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
    selectList: {
      get() {
        let res = []
        this.tablesSelectRows.forEach((item) => {
          res = [...res, ...item]
        })
        return res
      },
      set(val) {
        console.error('selectList无法set')
      },
    },
  },
  watch: {
    visible: function (val) {
      if (val) {
        this.loadTabsType()
      } else {
        this.tabsList = []
        this.searchKey = ''
      }
    },
    defaultSelect(val) {
      this.initDefaultValue()
    },
  },
  methods: {
    initDefaultValue() {
      let val = this.defaultSelect
      this.tablesSelectRows = []
      this.tabsList.forEach((item, index) => {
        this.tablesSelectRows[index] = []
        val.forEach((select) => {
          let choose = this.validIncludes(select, item.listdata)
          if (choose) {
            this.tablesSelectRows[index].push(choose)
          }
        })
      })
    },
    validIncludes(row, list) {
      let index = list.findIndex((item) => item.oid === row.oid)
      if (index !== -1) {
        return list[index]
      } else {
        return null
      }
    },
    //加载有哪些关联对象
    loadTabsType() {
      this.loadingtable = true
      findAllrelationType(this.rowData.modelDefinition)
        .then((resp) => {
          this.tabsList = resp.map((item) => {
            this.tablesSelectRows.push([])
            return {
              name: item.relationDisplayName,
              listdata: [],
              loading: false,
              objData: item,
            }
          })
          this.tabsList.forEach((item, index) => {
            this.loadTabList(index, item.objData)
          })
          this.initDefaultValue()
          //查询mcad bom
          if (this.rowData.type === 'MCADIteration') {
            this.findSecBomFun(this.tabsList.length, findSecBom)
          }

          //查询part bom
          if (this.rowData.type === 'PartIteration') {
            this.findSecBomFun(this.tabsList.length, findPartBom)
          }
        })
        .catch((e) => {
          console.error(e)
          this.$error(e.msg || this.$t('msg_failed'))
        })
        .finally(() => {
          this.loadingtable = false
        })
    },
    //关联所有对象
    async loadAllRelData(row) {
      console.log("这里是收集bom的接口")
      //单独接口获取
      if(row.type === 'PartIteration'){
        try {
          let bomdata = await findPartBom(row.oid)
          let now = bomdata[0]
          let bomlist = this.listbomtotable(now).filter(part => part.lifecycleStatus !== bomstatus)
          if (bomlist.length > 0) {
            bomlist = reductionArr(bomlist)
            resData = [...resData, ...bomlist]
            boms = [...boms, ...bomlist]
          }
        }catch (e){
          console.error('loadAllRelData发生错误：' + e);
        }

        // 检查btnType是否为"外发BOM"
        if (row.btnType && row.btnType === '外发BOM') {
          return []
        }

        let resp = await findPartRelList(row.oid)
        return resp
      }

      if(row.type === 'MCADIteration'){
        // let resp = await findMcadRelList(row.oid)
        // return resp
        let resp = await findMcadRelList(row.oid).then((res) => {
          return resp
        }).catch((e) => {
          console.error(e)
          // this.$error(e.msg)
        }).finally(() => {
          this.loadingtable = false
        })
      }

      let onoff = await findConf('auto_collect_state')
      //验证是否开启收集规则
      if (onoff.length > 0 && onoff[0].name !== 'on') {
        return []
      }
      let resData = []

      let boms = [row]

      let bomstatus = ''

      if(row.type === 'MCADIteration' || row.type === 'PartIteration'){
        let bomstatusconf = await findConf('collect_bom_status')
        if(bomstatusconf.length > 0 && bomstatusconf[0].name){
          bomstatus = bomstatusconf[0].name
        }
      }

      //查询mcad bom
      if (row.type === 'MCADIteration') {
        let bomdata = await findSecBom(row.oid)
        let now = bomdata[0]
        let bomlist = this.listbomtotable(now).filter(mcad => mcad.lifecycleStatus !== bomstatus)
        if (bomlist.length > 0) {
          bomlist = reductionArr(bomlist)
          resData = [...resData, ...bomlist]
          boms = [...boms, ...bomlist]
        }
      }

      //查询part bom
      if (row.type === 'PartIteration') {
        let bomdata = await findPartBom(row.oid)
        let now = bomdata[0]
        let bomlist = this.listbomtotable(now).filter(part => part.lifecycleStatus !== bomstatus)
        if (bomlist.length > 0) {
          bomlist = reductionArr(bomlist)
          resData = [...resData, ...bomlist]
          boms = [...boms, ...bomlist]
        }
      }

      //配置的相关信息
      let collectList = await findConf('collect_obj_list')
      console.log('需要收集的对象', collectList)

      let resp = await findAllrelationType(row.modelDefinition)
      let tabsList = resp.map((item) => {
        return {
          name: item.relationDisplayName,
          listdata: [],
          loading: false,
          objData: item,
        }
      })

      for (let i = 0; i < tabsList.length; i++) {
        const item = tabsList[i]
        if (collectList.length > 0) {
          let config = collectList.find((coll) => coll.name === item.name)
          if (config) {
            //收集所有数据及子结构的相关对象
            for (let j = 0; j < boms.length; j++) {
              const bom = boms[j]
              let relresp = await findRelationList({
                mainObjectOid: bom.oid,
                ...item.objData,
              })
              let listmap = [...relresp].map((item) => {
                //相关关联对象，删除旧的关联关系使用
                item.referenceOid = bom.oid
                return item
              })
              //配置了Released,过滤状态为Released的 非配置的状态数据
              if (config.value) {
                listmap = listmap.filter(
                  (colldata) => colldata.lifecycleStatus !== config.value
                )
              }
              if (listmap.length > 0) {
                resData = [...resData, ...listmap]
              }
            }
          }
        }
      }


      return this.deWeightThree(resData)
    },
    //去重
    deWeightThree(arr){
      let map = new Map()
      for(let item of arr){
        if(!map.has(item.oid)){
          map.set(item.oid, item)
        }
      }
      return [...map.values()]
    },
    findSecBomFun(index, req) {
      this.tabsList[index] = {
        name: this.$t('txt_collecting_BOM'),
        listdata: [],
        loading: true,
      }
      this.tablesSelectRows.push([])
      req(this.rowData.oid)
        .then((resp) => {
          let now = resp[0]
          if (now && now.children.length !== 0) {
            let bomlist = this.listbomtotable(now)
            this.tabsList[index].listdata = reductionArr(bomlist)
          }
          this.initDefaultValue()
          this.tabsInitData = JSON.parse(JSON.stringify(this.tabsList))
        })
        .catch((e) => {
          console.error(e)
        })
        .finally(() => {
          this.tabsList[index].loading = false
          this.$forceUpdate()
        })
    },

    listbomtotable(data) {
      let res = []
      let loadchildren = function (val) {
        res.push(val)
        if (val && val.children && val.children.length !== 0) {
          val.children.forEach((item) => {
            item.preSourceOid = val.oid
            loadchildren(item)
          })
        }
      }
      loadchildren(data)

      if (res.length > 0) {
        res.splice(0, 1)
      }
      return res
    },

    //加载关联对象数据
    loadTabList(index, row) {
      this.tabsList[index].loading = true
      findRelationList({ mainObjectOid: this.rowData.oid, ...row })
        .then((resp) => {
          this.tabsList[index].listdata = [...resp].map((item) => {
            //相关关联对象，删除旧的关联关系使用
            item.referenceOid = this.rowData.oid
            return item
          })
        })
        .catch((e) => {
          console.error(e)
          this.$error(e.msg || this.$t('msg_failed'))
        })
        .finally(() => {
          this.tabsList[index].loading = false
        })
    },

    removeselect(index) {
      let row = this.selectList[index]
      this.tablesSelectRows.forEach((item, index) => {
        let rowIndex = item.findIndex((item) => item.oid === row.oid)
        if (rowIndex !== -1) {
          this.clearTableSelect(index, row)
          item.splice(rowIndex, 1)
        }
      })
      this.tablesSelectRows = [...this.tablesSelectRows]
    },
    clearSelect() {
      this.$confirm({
        title: this.$t('txt_empty'),
        content: this.$t('btn_ok') + this.$t('txt_empty') + '?',
        class: 'deleteModal',
        cancelText: this.$t('btn_cancel'),
        okText: this.$t('btn_ok'),
        onOk: () => {
          this.tablesSelectRows = this.tablesSelectRows.map((item, index) => {
            item.forEach((row) => {
              this.clearTableSelect(index, row)
            })
            return []
          })
        },
      })
    },
    //清理table选中
    clearTableSelect(tablIndex, row) {
      if (
        this.$refs['relation-table'] &&
        this.$refs['relation-table'].length > 0
      ) {
        this.$refs['relation-table'][tablIndex].$refs['table'].setCheckboxRow(
          row,
          false
        )
      }
    },
    searchlist(val) {
      if (val) {
        this.tabsList = this.tabsInitData.map((item) => {
          let res = Object.assign({}, item)
          res.loading = false
          res.listdata = item.listdata.filter(
            (da) => da.name.includes(val) || da.number.includes(val)
          )
          return res
        })
      } else {
        this.tabsList = this.tabsInitData.map((item) => {
          item.loading = false
          return item
        })
      }
    },
    //确认选择
    confrim() {
      this.$emit('changeselect', this.selectList, this.rowData.oid)
      if (this.defaultSelect.length !== 0) {
        let secOid = this.selectList.map((item) => item.oid)
        let deltedData = this.defaultSelect.filter(
          (item) => !secOid.includes(item.oid)
        )
        console.log('需要移除的oid', deltedData)
        this.$emit('removeSelect', deltedData)
      }
      this.visibleshow = false
    },
  },
}
</script>

<style lang="less" scoped>
.area-split {
  display: flex;

  .area-left {
    width: 70%;
    border-right: 1px solid rgba(30, 32, 42, 0.15);
    padding-right: 10px;
  }

  .area-right {
    width: 30%;
    padding: 10px 0 10px 10px;
    .right-select-head {
      display: flex;
      justify-content: space-between;

      .select-count {
        font-size: 14px;
        color: #1e202a;
      }
      .clear-btn {
        font-size: 14px;
        color: #255ed7;
        cursor: pointer;
      }
    }

    .right-select-list {
      height: 600px;
      overflow: scroll;
      .select-line {
        border: 1px solid rgba(30, 32, 42, 0.15);
        border-radius: 6px;
        height: 48px;
        width: 100%;

        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 10px;
        padding-right: 10px;
        margin-top: 10px;

        .select-name {
          color: rgba(30, 32, 42, 0.65);
          font-weight: 500;
          font-size: 14px;
        }

        .remove-select-btn {
          cursor: pointer;
        }
      }
    }
  }

  .search-input {
    width: 42%;
  }
}

.spin-area {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 620px;
  width: 100%;
}

.defaulttab {
  height: 530px;
}
</style>
