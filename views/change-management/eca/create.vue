<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-20 15:08:52
 * @LastEditTime: 2022-04-28 17:42:11
 * @LastEditors: <EMAIL>
-->
<template>
  <jw-page>
    <template slot="header">
        <span style="margin-right:8px;color:#1890ff;cursor:pointer;"
            @click="goBackList"
          >
            {{ fromRouteTitle || $t('txt_change_management')}} 
          </span>
          &gt;
          <span style="margin-right:8px;color:#1890ff;cursor:pointer;"
           @click="goBackECR"
          >
            ECR
          </span>
          &gt;
          <span style="margin-right:8px;color:#1890ff;cursor:pointer;"
            @click="goBackECO"
          >
            ECO
          </span>
          &gt;
      <div class="_mpm-page-title">{{$t('txt_create_ECA')}}</div>
    </template>
    <div class="create-container jw-height">
      <step-list :current="currentStep" :list="stepList" />
      <div class="step-content">
        <keep-alive>
          <jwLayoutBuilder class="create-form" ref="layout" layoutName="create" modelName="ECA" :instanceData="baseData" :formRules="formRules" v-if="currentStep===0" />
          <change-program :changeDetail="ecaData" type="ECA" class="change-program" isEdit v-if="currentStep===1" />
        </keep-alive>
      </div>
      <div class="setp-footer">
        <a-button type="primary" @click="onCreate"  :loading="btnLoading" v-if="currentStep===0">{{$t('btn_next_step')}} </a-button>
        <a-button type="primary" @click="goDetail" v-if="currentStep===1"> {{$t('btn_done')}}</a-button>
        <a-button @click="goBack" v-if="currentStep===0">{{$t('btn_cancel')}} </a-button>
      </div>
    </div>
  </jw-page>
</template>

<script>
import commonStore from "jw_stores/common"
import Row_Store from "jw_stores/instance-info";
import {jwLayoutBuilder} from "jw_frame";
import {createEcaApi,findEcaChangeInfoApi} from 'apis/change/eca'
import { findDetail } from "apis/baseapi";
import ChangeProgram from '../components/change-program.vue'
import StepList from '../components/step-list.vue'

export default {
  name:'ecaCreate',
  components:{
    jwLayoutBuilder,
    ChangeProgram,
    StepList
  },
  data() {
    const validateDate=(rule, value, callback)=> {
      if (value) {
        // 选择当天时间之前
        if (
          value.valueOf() < new Date(new Date().toLocaleDateString()).getTime()
        ) {
          return callback(new Error(this.$t('change_time_validte')));
        }
        callback();
      } else {
        callback();
      }
    }
    return {
      isFromIssue:false,
      fromRouteTitle:"",
      changeOid: this.$route.params.oid,
      stepList:[{
        name:this.$t('change_fill_info'),
        key:'base',
      },{
        name:this.$t('change_confirm_program'),
        key:'object',
      }],
      currentStep:0,
      baseData:{},
      ecoData:{},
      ecaData:{},
      changeObjs:[],
      btnLoading:false,
      ecoSendRows:[],
      isLast:false,
      formRules:{plannedCompletionTime:[
        {validator: validateDate, trigger: "change"}
      ]}
    }
  },
    inject:[
        'setBreadcrumb',
    ],
  created () {
    this.setBreadcrumb([]);
    // 取缓存 选中的变更对象 是否最后一次创建
    const {rows,isLast}=commonStore.get('createEcaData')
    if(!rows||!rows.length){
      this.$error(this.$t('change_no_obj'))
      this.$router.go(-1)
    }
    this.ecoSendRows=rows
    this.isLast=isLast
    this.fetchEcoData();
  },
  methods: {
    goBackList () {
        const containerInfo = Row_Store.get('ProductContainer');
        let isFromIssue = localStorage.getItem("isFromIssue");
        let issueInfo = localStorage.getItem("issueInfo");
        if(isFromIssue){
            localStorage.removeItem("isFromIssue");
            localStorage.removeItem("issueInfo");
            this.$router.push({
                name: "problem-detail",
                path: `/problem-detail`,
                query:{
                  oid: issueInfo,
					        tabActive: "list",
                  issuDetailActive:"3"
                }
            });
        } else { 
          Jw.jumpToDetail({
            ...containerInfo,
            tabActive: 'change',
          });
        }
    },
    goBackECR() {
        let row = Row_Store.get('ECR');
        Jw.jumpToDetail(
          row,
          { 
            toUrl: '/ecr-details', 
          }
        );
    },
    goBackECO() {
        let row = Row_Store.get('ECO');
        Jw.jumpToDetail(
          row,
          { 
            toUrl: '/eco-details', 
          }
        );
    },
    fetchEcoData() {
      findDetail.execute({ oid: this.changeOid, type: 'ECO' }).then((res) => {
        this.ecoData=res;

        // 从缓存读取是否是从问题详情的ECR跳转过来的
        let isFromIssue = localStorage.getItem("isFromIssue");
        this.isFromIssue = isFromIssue;
        this.fromRouteTitle = this.$t('txt_problem_management');
      });
    },
    onCreate() {
      const layout=this.$refs.layout
      layout.validate()
        .then(() => {
          this.fetchCreate().then((res)=>{
            this.ecaData=res
            this.currentStep++
            // this.findEcaChangeInfo()
          })
        })
    },
    fetchCreate(){
      let baseData=this.baseData
      if(this.$refs.layout){
        baseData=this.$refs.layout.getValue()
      }
      const {containerOid,containerType,catalogType,catalogOid}=this.ecoData
      const params={
        ...baseData,
        ecoOid:this.changeOid,
        "locationInfo": {
          containerOid,containerType,catalogType,catalogOid
        },
        changeList:this.ecoSendRows,
        lasted:this.isLast
      }
      this.btnLoading=true
      return createEcaApi.execute(params)
        .then(res=>{
          this.btnLoading=false
          return res
        })
        .catch(err=>{
          this.$error(err.msg)
          this.btnLoading=false
          return Promise.reject()
        })
    },
    findEcaChangeInfo(){
      findEcaChangeInfoApi.execute({oid:this.ecaData.oid,list:[]})
        .then(res=>{
          this.changeObjs=res
        })
    },
    onPreClick() {
      this.currentStep--
    },
    goBack() {
      this.$router.go(-1)
    },
    goDetail(){
      Jw.jumpToDetail(
            this.ecaData,
            { 
                toUrl: '/eca-details',
                viewCode: 'ECAINSTANCE',
            }
        );
    },
  },
}
</script>

<style lang="less" scoped>
.create-container{
  display: flex;
  flex-direction: column;
  background-image: linear-gradient(179deg, #F0F7FF 0%, @white 30%);
}
.step-content{
  flex: 1;
  min-height: 1px;
  overflow: auto;
}
.setp-footer{
  padding: 14px 0;
  border-top: 1px solid @border-color-base;
  text-align: center;
}
.create-form{
  width: 944px;
  margin: 0 auto;
}
.change-obj,.change-program{
  /deep/.left-panel,/deep/.right-panel{
    padding-top: 0;
  }
}
</style>