<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-16 14:29:34
 * @LastEditTime: 2022-04-27 09:20:09
 * @LastEditors: <EMAIL>
-->
<template>
    <jw-page>
        <jw-toolbar class="jw-width" slot="header">
            <template slot="before-start">
                <span style="margin-right: 8px; color: #1890ff; cursor: pointer" @click="goBackList">
                    {{ fromRouteTitle || $t("txt_change_management") }}
                </span>
                &gt;
                <span style="margin-right: 8px; color: #1890ff; cursor: pointer" @click="goBackECR">
                    ECR
                </span>
                &gt;
                <span style="margin-right: 8px; color: #1890ff; cursor: pointer" @click="goBackECO">
                    ECO
                </span>
                &gt;
                <jw-icon :type="lockIcon" v-if="lockIcon" />

                <div class="_mpm-page-title">
                    <jw-icon type="#jwi-biangengduixiang" />
                    <span>{{ ecaData.name }} (ECA)</span>
                </div>
                <a-tag color="blue">{{ $t(ecaData.lifecycleStatus) }}
                </a-tag>
            </template>
            <template slot="after-end">
                <PermissionDropdown style="margin-right: 30px" showType="btn" :permissionCode="viewCode" :callback="permissionFilter" :instanceData="ecaData" @click="onMenuClick" />
            </template>
        </jw-toolbar>
        <a-tabs class="jw-tabs" v-model.trim="tabActive" @change="onTabChange">
            <a-tab-pane class="jw-height" key="base" :tab="$t('txt_base_info')">
                <jw-flex-row :rigthWidth="418" :marginRight="0">
                    <jwLayoutBuilder class="base-form" ref="layout" layoutName="show" modelName="ECA" :instanceData="ecaData" slot="middle" />
                    <!-- <flow-status :list="flowList" slot="right" /> -->
                </jw-flex-row>
            </a-tab-pane>
            <a-tab-pane class="jw-height" key="program" :tab="$t('change_program')">
                <change-program ref="changeProgram" type="ECA" :changeObjs="changeObjs" :changeDetail="ecaData" class="change-program" />
            </a-tab-pane>
            <!-- <a-tab-pane class="jw-height" key="history" tab="历史记录"></a-tab-pane> -->
        </a-tabs>
    </jw-page>
</template>

<script>
import { findEcaChangeInfoApi, confirmChangeApi } from "apis/change/eca";
import { findWithCrumbs } from "apis/change";
import { findDetail } from "apis/baseapi";
import Row_Store from "jw_stores/instance-info";
import { jwLayoutBuilder, jwFlexRow, jwToolbar } from "jw_frame";
import ChangeObj from "../components/eco-change-obj.vue";
import ChangeProgram from "../components/change-program.vue";
import FlowStatus from "../components/flow-status.vue";
import PermissionDropdown from "components/permission-dropdown";
export default {
  name: "ecoDetail",
  components: {
    jwLayoutBuilder,
    jwFlexRow,
    jwToolbar,
    ChangeObj,
    ChangeProgram,
    FlowStatus,
    PermissionDropdown
  },
  data() {
    return {
      isFromIssue: false,
      ecInfo: "",
      fromRouteTitle: this.$t("txt_change_management"),
      lastRoute: "",
      changeOid: this.$route.query.oid,
      ecaData: {},
      changeObjs: [],
      tableData: [],
      tabActive: "base",
      flowList: [
        { status: "wait" },
        { status: "done" },
        { status: "error" },
        { status: "done" },
        { status: "done" },
        { status: "done" },
        { status: "done" },
        { status: "done" }
      ],
      viewCode: "ECAINSTANCEOPERATION"
    };
  },
  computed: {
    toolbars() {
      return [
        {
          display: "dropdown",
          position: "after",
          name: this.$t("txt_operation"),
          key: "more",
          type: "primary",
          prefixIcon: "jwi-iconmenu-application",
          menuList: [
            {
              name: this.$t("btn_edit"),
              key: "edit",
              click: this.goEdit,
              isVisible: true
            },
            {
              name: this.$t("btn_confirm"),
              key: "confirm",
              click: this.done,
              isVisible: true
            }
          ]
        }
      ];
    },
    isMeCreate() {
      const { createBy } = this.ecaData;
      if (!createBy) return false;
      const { account } = Jw.getUser();
      return account === createBy;
    },
    lockIcon() {
      return this.isMeCreate ? "#jwi-beiwojianchu" : "#jwi-bierenjianchu";
    }
  },
  inject: ["setBreadcrumb"],
  created() {
    this.setBreadcrumb([]);
    this.fetchBaseDetail();
    this.fetchChangeObjs();
    this.getEcInfo();
    let enterEcrInfo = JSON.parse(sessionStorage.getItem("enterEcrInfo"));
    if (enterEcrInfo.routeName == "object-details") {
      this.fromRouteTitle = this.$t("txt_change_record");
    }
  },
  methods: {
    goBackList() {
      // 从缓存获取跳转进来变更详情的信息
      let enterEcrInfo = JSON.parse(sessionStorage.getItem("enterEcrInfo"));
      let isFromIssue = localStorage.getItem("isFromIssue");
      let issueInfo = localStorage.getItem("issueInfo");
      if (isFromIssue) {
        localStorage.removeItem("isFromIssue");
        localStorage.removeItem("issueInfo");
        this.$router.push({
          name: "problem-detail",
          path: `/problem-detail`,
          query: {
            oid: issueInfo,
            tabActive: "list",
            issuDetailActive: "3"
          }
        });
      } else {
        if (enterEcrInfo && enterEcrInfo.routeName == "object-details") {
          Jw.jumpToDetail(enterEcrInfo.query, {
            toUrl: "/object-details"
          });
          sessionStorage.setItem("currentTabName", "changeRecord");
          sessionStorage.removeItem("enterEcrInfo");
        } else {
          const containerInfo = this.ecaData.containerInfo[0] || {};
          console.log(this.ecaData);
          this.$route.query.tabActive = "change";
          Jw.jumpToDetail({
            ...containerInfo,
            tabActive: "change"
          });
        }
      }
    },
    goBackECR() {
      let row = Row_Store.get("ECR");
      if (row) {
        Jw.jumpToDetail(row, {
          toUrl: "/ecr-details",
          blank: false
        });
      } else {
        Jw.jumpToDetail(ecInfo[2], {
          toUrl: "/ecr-details",
          blank: false
        });
      }
    },
    goBackECO() {
      let ecInfo = this.ecInfo;
      let row = Row_Store.get("ECO");
      if (row) {
        Jw.jumpToDetail(row, {
          toUrl: "/eco-details",
          blank: false
        });
      } else {
        Jw.jumpToDetail(ecInfo[3], {
          toUrl: "/eco-details",
          blank: false
        });
      }
    },
    getEcInfo() {
      findWithCrumbs
        .execute({
          oid: this.$route.query.oid,
          type: this.$route.query.type
        })
        .then(res => {
          console.log("EC详细信息", res);
          this.ecInfo = res;

          // 从缓存读取是否是从问题详情的ECR跳转过来的
          let isFromIssue = localStorage.getItem("isFromIssue");
          if (isFromIssue) {
            this.isFromIssue = isFromIssue;
            this.fromRouteTitle = this.$t("txt_problem_management");
          }
        });
    },
    permissionFilter(btns) {
      let permissionBtns = btns.filter(btn => {
        return btn.code != "details";
      });
      return permissionBtns;
    },
    onMenuClick(item) {
      let key = item.code || item;
      if (key === "edit") {
        this.goEdit();
      } else if (key === "create") {
        this.done();
      }
    },
    fetchBaseDetail() {
      findDetail.execute({ oid: this.changeOid, type: "ECA" }).then(res => {
        this.ecaData = res;
      });
    },
    fetchChangeObjs() {
      findEcaChangeInfoApi
        .execute({ oid: this.changeOid, list: [] })
        .then(res => {
          this.changeObjs = res;
        });
    },
    onTabChange() {},
    goEdit() {
      const tabKeys = ["base", "program"];
      const step = tabKeys.indexOf(this.tabActive);
      this.$router.push(
        `/change-management/eca/edit/${this.changeOid}?step=${step}`
      );
    },
    done() {
      if (this.changeObjs.every(item => item.changeScheme)) {
        this.$confirm({
          title: this.$t("change_confirm"),
          okText: this.$t("btn_ok"),
          cancelText: this.$t("btn_cancel"),
          onOk: () => {
            return confirmChangeApi
              .execute({ oid: this.changeOid })
              .then(() => {
                this.$success(this.$t("msg_success"));
              });
          }
        });
      } else {
        this.$warning(this.$t("change_check_program"));
      }
    }
  }
};
</script>

<style lang="less" scoped>
.base-form {
  width: 75%;
  padding-top: 14px;
  margin: 0 auto;
}
.status-tag {
  height: 32px;
  margin-right: 0;
  line-height: 32px;
}
</style>