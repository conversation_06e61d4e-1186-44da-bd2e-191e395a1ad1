<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-08 15:27:27
 * @LastEditTime: 2022-04-26 17:23:58
 * @LastEditors: <EMAIL>
-->
<template>
  <jw-flex-row class="select-change-obj" :leftWidth="414" :marginRight="0">
    <div class="jw-height left-panel" slot="left" v-if="objList.length">
      <div class="left-panel-header">
        <p class="header-title">{{$t('change_obj')}}</p>
        <a-button type="primary" @click="getStockValue" style="margin-right: 50px" size="small">查询U9数据</a-button>
        <a-radio-group class="type-radio" v-model.trim="currentType" @change="onTypeChange" size="small">
          <a-radio-button :value="type.key" v-for="type in typeList" :key="type.key">
            <a-tooltip placement="top" :title="type.title">
              <jw-icon :type="type.icon" v-if="type.icon" />
              <template v-else>{{type.key}}</template>
            </a-tooltip>
          </a-radio-button>
        </a-radio-group>
      </div>
      <obj-list :current.sync="currentObj" :data="showObjList" @onDelClick="onDelClick" @change="onCurrentChange" :showDel="isEdit" />
      <a-button class="add-btn" @click="showAdd" v-if="isEdit">{{$t('change_add_change')}} </a-button>
    </div>
    <div slot="middle" class="jw-height right-panel">

      <jw-table class="right-table" :loading="tableLoading" :columns="columns" :show-page="false" :dataSource="tableData" v-if="showObjList.length">
        <p class="header-title" slot="header">{{$t('change_influence_obj')}} </p>
      </jw-table>
      <empty class="empty-box" v-else>
        <p class="empty-text">{{$t('change_empty_txt')}} </p>
        <a-button type="primary" size="large" @click="showAdd" v-if="isEdit">{{$t('change_add_change')}} </a-button>
      </empty>
    </div>
    <!-- <add-obj-dialog ref="addDialog" /> -->
    <jw-search-engine-modal :title="$t('change_add_change')" only-search-object :visible.sync="globalSearchVisible" :model-list='[
          {
              name: $t("txt_part"),
              code: "PartIteration",
          },
          {
              name: $t("txt_document"),
              code: "DocumentIteration",
          },
          {
              name: "MCAD",
              code: "MCADIteration",
          },
          {
              name: "ECAD",
              code: "ECADIteration",
          },
      ]' @ok='addObjectOk' />
    <DialogStockValue ref="transfer" :visibleShow="visibleStack" @close="cancelData" :showObjList="showObjList">
    </DialogStockValue>
  </jw-flex-row>
</template>

<script>
import { findInfluenceInfo, checkChangeInfo } from "apis/change";
import { jwFlexRow, jwSearchEngineModal } from "jw_frame";
import ObjList from "./obj-list.vue";
// import AddObjDialog from './add-obj-dialog.vue'
import Empty from "components/empty";
import DialogStockValue from "./dialog-stock-value";
import ModelFactory from "jw_apis/model-factory";
// 获取相关对象
const relationObjsModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.sysconfigServer}/collectionRule/findByAppliedType`,
  method: "get"
});
// 获取对象列表
const relationSearchModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/relatedObject/fuzzy`,
  method: "post"
});
export default {
  name: "selectChangeObj",
  props: {
    changeObjs: Array, // 列表数据
    changeDetail: Object,
    isEdit: Boolean // 可编辑
  },
  components: {
    ObjList,
    jwFlexRow,
    Empty,
    // AddObjDialog,
    jwSearchEngineModal,
    DialogStockValue
  },
  data() {
    return {
      typeList: [
        {
          key: "All",
          title: this.$t("txt_all")
        },
        {
          key: "Part",
          title: this.$t("txt_part"),
          icon: "#jwi-lianjian",
          children: ["PartIteration"]
        },
        {
          key: "Document",
          title: this.$t("txt_document"),
          icon: "#jwi-wendang",
          children: ["DocumentIteration"]
        },
        {
          key: "ECAD",
          title: "ECAD",
          icon: "#jwi-ecad",
          children: ["ECADIteration"]
        },
        {
          key: "MCAD",
          title: "MCAD",
          icon: "#jwi-mcad",
          children: ["MCADIteration"]
        }
      ],
      tableLoading: false,
      objList: [],
      tableData: [],
      currentType: "All",
      currentObj: {},
      visibleStack: false,
      globalSearchVisible: false,
      relatedObjList:[]
    };
  },
  computed: {
    checkRowKeys() {
      return this.objList.map(v => v.oid);
    },
    columns() {
      return [
        {
          field: "number",
          title: this.$t("txt_number")
        },
        {
          field: "name",
          title: this.$t("txt_name"),
          formatter: ({ text, row }) => {
            return row.cname || row.name
          }
        },
        {
          field: "relationName",
          title: this.$t("txt_relation")
        },
        {
          field: "modelDefinition",
          title: this.$t("txt_type")
        },
        {
          field: "displayVersion",
          title: this.$t("txt_version")
        },
        {
          field: "lifecycleStatus",
          title: this.$t("txt_lifecycle")
        }
      ];
    },
    showObjList() {
      const typeObj = this.typeList.find(item => item.key === this.currentType);
      if (!typeObj.children) return this.objList;
      return this.objList.filter(item => typeObj.children.includes(item.type));
    }
  },
  created() {
    this.init(this.changeObjs);
  },
  watch: {
    changeObjs(val) {
      this.init(val);
    },
    currentObj: {
      handler: function() {
        this.getInfuenceData();
      },
      deep: true
    },
    showObjList(val){
      console.log(val,'showObjelist')
    }
  },
  methods: {
    getStockValue() {
      this.visibleStack = true;
    },
    cancelData() {
      this.visibleStack = false;
    },
    init(objs) {
      this.objList = _.cloneDeep(objs);
      if (this.objList.length) {
        this.currentObj = this.objList[0]; // 初始化时默认高亮第一条
      }
    },
    onTypeChange() {
      if (this.showObjList.length) {
        this.currentObj = this.showObjList[0];
      }
    },
    showAdd() {
      this.globalSearchVisible = true;
    },
    addObjectOk(selectedRows) {
      //选择发布状态的数据发起变更流程
      let noRel = selectedRows.find(item => {
        return item.lifecycleStatus != "Released" && item.lifecycleStatus != "released";
      });
      if (noRel) {
        return this.$error(`${noRel.number}数据状态为未发布，不能执行变更操作`);
      }
      let list = [];
      selectedRows.forEach(item => {
        list.push({
          oid: item.oid,
          type: item.type,
          name: item.name,
          number: item.number
        });
      });
      checkChangeInfo
        .execute({
          ecrOid: this.changeDetail ? this.changeDetail.oid : "",
          list: list
        })
        .then(data => {
          let addList = _.differenceBy(selectedRows, this.objList, "oid"); // 比较出新增的数据
          let deleteList = _.differenceBy(this.objList, selectedRows, "oid"); // 比较出删除的数据
          let temp = [...this.objList,...addList]
          console.log(temp,'temp')
          if(temp.length>=2){
            let d=temp.filter(ele=>ele.type==='DocumentIteration')
            if(d.length!==temp.length&&d.length){
              return this.$error('文档变更不允许与其他对象一起进行')
            }
          }
          this.$emit("change", { deleteList: [], addList },(val)=>{
            if(val){
              // this.objList=_.cloneDeep(selectedRows)
              console.log('caaaaaa')
              this.objList = this.objList.concat(addList);
              this.currentType = "All";
              this.currentObj = this.objList[0];
              this.globalSearchVisible = false;
            }
          });
        })
        .catch(err => {
          console.log(err)
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    onDelClick(item) {
      if (item.oid === this.currentObj.oid) {
        // 删除当前高亮
        // if(index===0&&this.showObjList[1]){
        //   this.currentObj=this.showObjList[1]
        // }else{
        this.tableData = [];
        // }
      }
      let objIndex = this.objList.findIndex(obj => obj.oid === item.oid);
      this.objList.splice(objIndex, 1);
      this.$emit("change", { deleteList: [item], addList: [] });
    },
    onCurrentChange() {
      // this.getInfuenceData()
    },
    getInfuenceData() {
      if (this.currentObj) {
        this.tableLoading = true;
        this.getfindInfl()
        // if(this.currentObj.modelDefinition==='Document'){
        //   this.relationObjsModel()
        // }else{
        //   this.getfindInfl()
        // }
      }
    },
    getfindInfl(){
      const param = {
          oid: this.currentObj.oid,
          type: this.currentObj.type
        };
        findInfluenceInfo
          .execute(param)
          .then(data => {
            this.tableLoading = false;
            this.tableData = data;
            console.log(this.tableData,'this.tableData')
          })
          .catch(err => {
            this.tableLoading = false;
            this.$error(err.msg || this.$t("msg_failed"));
          });
    },
    getData() {
      return this.objList;
    },
    getValue() {
      return { changeObjs: this.objList };
    },
    async validate() {
      if (!this.objList.length) {
        this.$error("请选择变更对象");
        throw new Error("请选择变更对象");
      }
    },
    relationObjsModel(){
      let params = {
        appliedType: `${this.currentObj.modelDefinition}_Related_Object`,
        mainObjectType: this.currentObj.modelDefinition
      };
      relationObjsModel
        .execute(params)
        .then(res => {
          this.tableLoading = false;
          this.relatedObjList = res;
          this.loadFetch(this.currentObj.modelDefinition)
        })
        .catch(err => {
          this.tableLoading = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    loadFetch(type) {
      let row = this.relatedObjList.find(item => {
        return item.mainObjectType == type;
      });
      relationSearchModel
        .execute({
          ...row,
          mainObjectOid: this.currentObj.oid
        })
        .then(data => {
          this.tableLoading = false;
          this.tableData = data;
        })
        .catch(() => {
          this.tableLoading = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    }
  }
};
</script>

<style lang="less" scoped>
.left-panel {
  display: flex;
  flex-direction: column;
  padding: 14px 15px 16px 24px;
}
.left-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 8px;
}
.select-change-obj {
  background-color: transparent;
  /deep/.column-middle {
    padding: 0 16px 16px;
  }
}
.type-radio {
  /deep/.ant-radio-button-wrapper {
    height: 28px;
    line-height: 28px;
  }
}
.header-title {
  color: @heading-color;
}
.add-btn {
  width: 285px;
  margin: 0 auto;
}

.right-panel {
  position: relative;
  padding-top: 14px;
}
.right-table.has-tools {
  /deep/.vxe-grid--toolbar-wrapper {
    padding-bottom: 8px;
    line-height: 28px;
  }
}
.empty-text {
  margin: 32px 0;
  color: @text-color-secondary;
}
</style>
