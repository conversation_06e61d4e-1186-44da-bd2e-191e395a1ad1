<template>
  <a-modal v-dialogDrag="true" :width="1280" :visible.sync="isVisible" :title="$t('change_add_change')" :maskClosable="false" @cancel="onClose" dialogClass="add-obj-dialog">
    <jw-flex-row class="dialog-content" :leftWidth="840" :marginRight="0">
      <div slot="left" class="jw-height left-panel">
        
        <jw-table class="left-table" ref="table" :checkbox-config="{checkMethod:canCheck,checkRowKeys:checkRowKeys}" :toolbars="toolbars" :columnSelecter="false" :columns="columns" :fetch="fetchTable" :dataSource.sync="tableData" v-if="isVisible">
          <template #checkTitle="{ checked, indeterminate  }">
            <a-checkbox :indeterminate="indeterminate" :checked="checked" @change="onAllCheck">
            </a-checkbox>
          </template>
          <template #checkContent="{ row, checked, disabled }">
            <a-tooltip v-if="disabled" :title="row.msg" placement="left">
              <jw-icon type="jwi-iconwarning-circle-full" class="disable-icon" />
            </a-tooltip>
            <a-checkbox :checked="checked" @change="onSingleCheck(row,!checked)" v-else>
            </a-checkbox>
          </template>
          <div class="can-change-box" slot="tool-before-end">
            <p>{{$t('change_can')}} </p>
            <a-switch class="can-change-switch" v-model.trim="isCanChange" @change="onSwitchChange">
              <a-icon slot="checkedChildren" type="check" />
              <a-icon slot="unCheckedChildren" type="close" />
            </a-switch>
          </div>
          <a-radio-group slot="tool-after-start" class="type-radio" v-model.trim="currentType" @change="onTypeChange" size="small">
            <a-radio-button :value="type.key" v-for="type in typeList" :key="type.key">
              <a-tooltip placement="top" :title="type.title">
                <jw-icon :type="type.icon" v-if="type.icon"/>
                <template v-else>{{type.key}}</template>
              </a-tooltip>
            </a-radio-button>
          </a-radio-group>
          <template #name="{row}">
            <jw-icon :type="row.modelIcon" />
            <span style="margin-left:5px">{{row.name}}</span>
          </template>
        </jw-table>
      </div>
      <div class="jw-height right-panel" slot="middle">
        <div class="right-panel-header">
          <p class="header-title">{{$t('txt_added')}} {{selectedRows.length}} {{$t('txt_item')}}</p>
          <a-button type="link" @click="onClearClick">{{$t('txt_empty')}} </a-button>
        </div>
        <obj-list :data="selectedRows"  @onDelClick="onDelClick" :showDel="showDel" />
      </div>
    </jw-flex-row>
    <div slot="footer">
      <a-button type="primary" @click="onConfirm">{{$t('btn_ok')}} </a-button>
      <a-button @click="onCancel">{{$t('btn_cancel')}} </a-button>
    </div>
  </a-modal>
</template>

<script>
import { jwFlexRow } from 'jw_frame';
import {searchCanChangeInfoBy} from 'apis/change'
import ObjList from './obj-list.vue'

export default {
  name: "addObjDialog",
  components: {
    ObjList,
    jwFlexRow
  },
  data() {
    return {
      isVisible: false,
      tableData:[],
      selectedRows:[],
      searchKey:'',
      changeDetail:{},
      showDel:true,
      disableDelKeys:[],
      typeList: [{
        key:'All',
        title:this.$t('txt_all'),
      },{
        key:'Part',
        title:this.$t('txt_part'),
        icon:'#jwi-lianjian',
        children:['PartIteration']
      },{
        key:'Document',
        title:this.$t('txt_document'),
        icon:'#jwi-wendang',
        children:['DocumentIteration']
      },{
        key:'CAD',
        title:'CAD',
        icon:'#jwi-cad',
        children:['MCADIteration','ECADIteration']
      }],
      currentType:'All',
      isCanChange:false
    };
  },
  computed:{
    toolbars(){
      return[
        {
          name: this.$t('btn_search'),
          display:'input',
          value:this.searchKey,
          allowClear:true,
          input: this.onSearchInput,
          key: "search"
        },
      ]
    },
    columns(){
      return[
        {
          type: "checkbox",
          field: 'oid',
          width: 40,
          resizable: false,
          fixed: "left",
          showOverflow: 'ellipsis',
          slots:{
            checkbox:'checkContent',
            header:'checkTitle'
          }
        },
        {
          field:'name',
          title:this.$t('txt_name'),
          width:170,
          slots:{
            default:'name'
          }
        },
        {
          field:'number',
          title:this.$t('txt_number')
        },
        // {
        //   field:'relationName',
        //   title:'关系'
        // },
        {
          field:'type',
          title:this.$t('txt_type'),
          width:170
        },
        {
          field:'displayVersion',
          title:this.$t('txt_plan_version'),
          width:90
        },
        {
          field:'lifecycleStatus',
          title:this.$t('txt_lifecycle')
        }
      ]
    },
    checkRowKeys(){
      return this.selectedRows.map(item=>item.oid)
    }
  },
  created () {
    this.delaySearch = _.debounce(this.onSearch, 500)
  },
  methods: {
    show(options) {
      if (options) {
        this.selectedRows=_.cloneDeep(options.selectedRows)
        this.changeDetail=options.changeDetail||{}
        this.showDel=options.showDel||true
        if(typeof this.showDel==='function'){ // 列表中会存在不可删除的对象
          this.disableDelKeys=this.selectedRows.filter(row=>!this.showDel(row)).map(row=>row.oid)
        }
      }
      this.isVisible = true
      return new Promise((resolve, reject) => {
        this.onCancel = () => {
          this.hide();
          reject("cancel");
        };

        this.onClose = () => {
          this.hide();
          reject("close");
        };

        this.onConfirm = () => {
          if(this.selectedRows.length){
            resolve(this.selectedRows)
            this.hide();
          }else{
            this.$warning(this.$t('change_choose_warning'))
          }
        };
      });
    },
    onSearchInput(value){
      this.searchKey=value
      this.delaySearch()
    },
    onSearch() {
      this.$refs.table.reFetchData()
    },
    onTypeChange(){
      this.onSearch()
    },
    onSwitchChange(){
      this.onSearch()
    },
    onAllCheck(){
      const table=this.$refs.table
      const beforeCheckRows=table.getCheckboxRecords() // 获取之前选中的数据
      table.toggleAllCheckboxRow().then(()=>{
        const afterCheckRows=table.getCheckboxRecords() // 获取之后选中的数据
        const addRows=_.differenceBy(afterCheckRows,beforeCheckRows,'oid') // 新选中的数据
        const delRows=_.differenceBy(beforeCheckRows,afterCheckRows,'oid').map(item=>item.oid)  // 取消选中的数据
        _.remove(this.selectedRows,item=>delRows.includes(item.oid))  // 删除取消选中的数据
        this.selectedRows=this.selectedRows.concat(addRows) // 加上新选中的数据
      })
    },
    onSingleCheck(row,checked){
      this.$refs.table.toggleCheckboxRow(row).then(()=>{
        if(checked){
          this.selectedRows.push(row) // 加上新选中的数据
        }else{
          const index=this.selectedRows.findIndex(item=>item.oid===row.oid)
          if(index!==-1){
            this.selectedRows.splice(index,1) // 删除取消选中的数据
          }
        }
      })
    },
    onDelClick(item,index){
      this.selectedRows.splice(index,1)
      const table=this.$refs.table
      const row=table.getRowById(item.oid)
      if(row){
        this.$refs.table.setCheckboxRow(row,false)
      }
    },
    onClearClick(){
      this.selectedRows=this.selectedRows.filter((item)=>{ // 保留不可删除的数据
        return this.disableDelKeys.includes(item.oid)
      })
      this.$refs.table.setAllCheckboxRow(false) // 表格清空选中
    },
    canCheck({row}){
      // 根据change字段和筛选不可删除数据 返回是否出现选中框
      return row.change&&!this.disableDelKeys.includes(row.oid)
    },
    fetchTable({current,pageSize}) {
      const toTypes=this.typeList.find(item=>item.key===this.currentType).children
      let param = {
        fromTypes: [
          "ResourceContainer",
          "ProductContainer"
        ],
        change:this.isCanChange,
        toTypes,
        searchKey: this.searchKey, //关键字（name/number）
        index: current,
        size: pageSize,
        ecrOid: this.changeDetail.oid
      };
      return searchCanChangeInfoBy
        .execute(param)
        .then((data) => {
          setTimeout(() => { // 需激活表格当前展示数据选中的，每次刷新一下选中状态 由 checkRowKeys 来控制
            this.$refs.table.reloadData(this.tableData)
          }, 0);
          return {data:data.rows,total:data.count}
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },

    
    hide() {
      this.onClearClick()
      this.isVisible = false;
      this.searchKey = ''
      this.disableDelKeys = []
    },

    onClose() {
      this.$emit("close");
      this.hide();
    },
    onConfirm:_.noop,
    onCancel:_.noop
  }
};
</script>
<style lang="less">
.add-obj-dialog{
  font-size: 12px;
  .ant-modal-content{
    .ant-modal-body{
      padding-bottom: 0;
    }
    .ant-modal-footer{
      border-top: 1px solid @border-color-base;
    }
  } 
}
</style>
<style lang="less" scoped>

.right-panel{
  display: flex;
  flex-direction: column;
  padding: 0 0 16px 16px;
}
.right-panel-header{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 8px;
}
.dialog-content{
  background: transparent;
  height: 500px;
}
.header-title{
  color: @text-color-secondary;
}
.left-panel{
  position: relative;
  padding-right: 16px;
}
.can-change-box{
  display: flex;
  align-items: center;
}
.can-change-switch {
  margin-left: 8px;
}
.left-table.has-tools{
  
  /deep/.vxe-grid--toolbar-wrapper{
    padding-bottom: 8px;
    line-height: 28px;
  }
}
.disable-icon{
  color: @warning-color;
}
</style>


