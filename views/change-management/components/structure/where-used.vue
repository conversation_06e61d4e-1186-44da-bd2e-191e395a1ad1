<template>
    <div class="child-where-used-wrap">
        <jw-table ref="refTable"
            :panel="false"
            :toolbars="toolbars"
            :columns="columns"
            row-id="id"
            :showPage="false"
            :data-source.sync="tableData"
            :fetch="fetchTable"
            @onToolClick="onToolClick"
            @onToolInput="onToolInput"
            @onOperateClick="onOperateClick"
        >
            <template slot="tool-before-end">
                <a-radio-group v-if="isShowLevel" v-model.trim="level" @change="onChangeLevel">
                    <a-radio-button value="all">all</a-radio-button>
                    <a-radio-button v-for="item in maxLevel" :key="item" :value="item">{{item}}</a-radio-button>
                </a-radio-group>
            </template>
            <template #name="{ row }">
                <jwIcon :type='row.modelIcon'></jwIcon>
                {{ row.name }}
            </template>
        </jw-table>
    </div>
</template>

<script>
import { jwIcon } from 'jw_frame';
import {
  getTableData,
  getMaxlevel,
  generateUUID,
} from '../apis';

export default {
    name: 'structureWhereUsed',
    props: [
        'detailInfo',
    ],
    components: {
        jwIcon,
    },
    data() {
        return {
            searchKey: '',
            level: 'all',
            maxLevel: 0,
            isShowLevel: true,
            tableData: [],
        }
    },
    computed: {
        toolbars() {
            return [
                {
                    name: '搜索',
                    position: 'before',
                    display: 'input',
                    value: this.searchKey,
                    allowClear: true,
                    placeholder: '请输入搜索关键字',
                   
                    key: 'search',
                },
            ]
        },
        columns() {
            return [
                {
                    field: 'number',
                    title: '父级物料号',
                    treeNode: true,
                    minWidth: 170,
                    slots: {
                        default: 'name',
                    },
                },
                {
                    field: 'name',
                    title: '父级物料名称',
                    minWidth: 170,
                },
                {
                    field: 'productName',
                    title: '产品名称',
                    minWidth: 120,
                },
                {
                    field: 'displayVersion',
                    title: '版本',
                    minWidth: 100,
                    cellRender: {
                        name: 'tag',
                    },
                },
                {
                    field: 'lifecycleStatus',
                    title: '生命周期',
                    minWidth: 150,
                    cellRender: {
                        name: 'tag',
                    },
                },
            ];
        },
    },
    mounted() {

    },
    methods: {
        onSearch() {
            this.$refs.refTable.reFetchData();
        },
        async getMaxlevel(){
            await getMaxlevel({
                partOid: this.detailInfo.oid
            }).then(res=>{
                this.maxLevel = res;
                if (this.maxLevel > 0) {
                    this.isShowLevel = true;
                } else {
                    this.isShowLevel = false;
                }
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        fetchTable() {
            return getTableData({
                searchKey: this.searchKey,
                maxLevel: this.level === 'all' ? this.maxLevel : this.level,
                partOid: this.detailInfo.oid,
            }).then((res) => {
                generateUUID(res);
                return {
                    data: res,
                }
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        onChangeLevel(e) {
            this.level = e.target.value;
            this.onSearch();
        },
        onToolClick({key}) {
            if (key==='create') {
                
            }
        },
        onToolInput({key}, value) {
            if (key ==='search') {
                this.searchKey = value;
            }
        },
        onOperateClick(key, row) {
            if (key === 'delete') {
                
            }
        },
    },
}
</script>

<style lang="less" scoped>
.child-where-used-wrap {
    height: calc(~"100% - 32px");
    margin: 16px 0;
    .ant-radio-button-wrapper {
        width: 32px;
        padding: 0;
        text-align: center;
    }
}
</style>
