<template>
    <a-drawer
        :bodyStyle="bodyStyle"
        width="50%"
        :visible="visible"
        @close="onClose"
    >
        <div slot="title" class="title-wrap">
            <div>{{title ? title: '结构详情'}}</div>
        </div>
        <div class="detail-drawer-wrap">
            <a-tabs v-model.trim="activeTab" @change="onChangeTab">
                <a-tab-pane key="basicInfo" tab="详细信息"></a-tab-pane>
                <a-tab-pane key="childStructure" tab="子结构"></a-tab-pane>
                <a-tab-pane v-if="parentData && parentData.oid" key="replaceItem" tab="替代件"></a-tab-pane>
                <a-tab-pane key="whereUsed" tab="被使用"></a-tab-pane>
            </a-tabs>
            <div class="detail-drawer-body-wrap">
                <basic-info
                    v-if="activeTab === 'basicInfo'"
                    ref="basicInfo"
                    :detailInfo="detailInfo"
                ></basic-info>
                <child-structure
                    v-if="activeTab === 'childStructure'"
                    ref="childStructure"
                    :detailInfo="detailInfo"
                ></child-structure>
                <replace-item
                    v-if="activeTab === 'replaceItem'"
                    ref="replaceItem"
                    :detailInfo="detailInfo"
                    :parentData="parentData"
                ></replace-item>
                <where-used
                style="padding-bottom: 20px;"
                    v-if="activeTab === 'whereUsed'"
                    ref="whereUsed"
                ></where-used>
            </div>
        </div>
    </a-drawer>
</template>

<script>
import basicInfo from './basic-info';
import childStructure from './child-structure';
import replaceItem from './replace-item';
import whereUsed from './usage/index';
import { getParent } from 'utils/util.js';

export default {
    name: 'structureChildDetail',
    props: [
        'visible',
        'detailInfo',
    ],
    provide() {
        return {
            detailsData: () => this.detailInfo,
        };
    },
    components: {
        basicInfo,
        childStructure,
        replaceItem,
        whereUsed,
    },
    data() {
        return {
            bodyStyle: { padding: 0, height: 'calc(100% - 55px)' },
            title: '',
            activeTab: 'basicInfo',
            parentData: {},
        }
    },
    watch: {
        visible(val) {
            if (val) {
                this.title = this.detailInfo.name;
                this.$nextTick(() => {
                    this.$refs.basicInfo.getObjDetail();
                })
                let treeData = this.$parent.$refs.refTable.getTableData().fullData;
                this.parentData = getParent(treeData, this.detailInfo.id, 'id');
            }
        }
    },
    mounted() {

    },
    methods: {
        onChangeTab(tab) {
            if (tab === 'basicInfo') {
                this.$nextTick(() => {
                    this.$refs.basicInfo.getObjDetail();
                })
            } else if (tab === 'replaceItem') {
                this.$nextTick(() => {
                    this.$refs.replaceItem.onSearch();
                })
            } else if (tab === 'whereUsed') {
                this.$nextTick(() => {
                    this.$refs.whereUsed.init();
                })
            }
        },
        onClose() {
            this.$emit('close');
            this.activeTab = 'basicInfo';
        },
    },
}
</script>

<style lang="less" scoped>
.title-wrap {
    display: flex;
    justify-content: space-between;
    padding-right: 35px;
    .jwi-iconedit {
        cursor: pointer;
    }
}
.detail-drawer-wrap {
    height: 100%;
    .detail-drawer-body-wrap {
        height: calc(~"100% - 61px");
    }
}
</style>
