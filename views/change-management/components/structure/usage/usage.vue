<template>
  <div class="object-details-usage">
    <header>
      <div class="left">
        <a-input-search
          placeholder="请输入搜索关键字"
          v-model.trim="searchKey"
          @search="onSearch"
        />
        <a-radio-group v-model.trim="level" @change="onChange" button-style="solid">
          <a-radio-button value="all"> all </a-radio-button>
          <a-radio-button :value="item" v-for="item in maxLevel" :key="item">
            {{ item }}
          </a-radio-button>
        </a-radio-group>
      </div>
      <div class="right">
        <!-- <button @click="initTable">init</button> -->
        <i class="jwi-iconproduct-mix" @click="showTable = !showTable"></i>
      </div>
    </header>
    <main>
      <!-- <a-table
      bordered
        :columns="columns"
        :data-source="data"
        :expanded-row-keys.sync="expandedRowKeys"
      /> -->
      <jw-table
        v-show="showTable"
        ref="refTable"
        row-id="uuid"
        :columns="columns"
        :tree-config="{ rowField: 'uuid', expandRowKeys: expandRowKeys }"
        :data-source.sync="data"
        :showPage="false"
      >
      </jw-table>
      <div v-show="!showTable" id="echartUsage"></div>
    </main>
  </div>
</template>

<script>
import { jwTable } from "jw_frame";
import { getTableData, getMaxlevel, getEchartData, generateUUID } from "../apis";
import G6 from "@antv/g6";
export default {
  name: "Usage",
  components: {
    jwTable,
  },
  computed: {
    columns() {
      return [
        {
          title: "父级物料号",
          field: "number",
          width: "240",
          treeNode: true,
          cellRender: {
            name: "link",
            events: {
              click: ({ row }) => {},
            },
          },
        },
        {
          title: "父级物料名称",
          field: "name",
          width: "200",
        },
        {
          title: "产品名称",
          dataIndex: "productName",
          field: "productName",
          cellRender: {
            name: "link",
          },
        },
        {
          title: "视图",
          dataIndex: "viewName",
          field: "viewName",
          width: "120",
          cellRender: {
            name: "tag",
          },
        },
        {
          title: this.$t('txt_version'),
          dataIndex: "version",
          field: "version",
          width: "120",
          cellRender: {
            name: "tag",
          },
        },
        {
          title: "生命周期",
          dataIndex: "catalogType",
          width: "130",
          field: "catalogType",
          cellRender: {
            name: "tag",
            render: ({ row }) => {
              return {
                color: "blue",
              };
            },
          },
        },
      ];
    },
  },
  props: {
    objectDetailsData: {
      default: {},
      type: Object,
    },
  },
  data() {
    return {
      searchKey: undefined,
      level: "all",
      showTable: false,
      maxLevel: 0,
      graph: {},
      data: [],
      echartData: {
        id: "Modeling Methods",
        children: [
          {
            id: "Classification",
            children: [
              { id: "Logistic regression" },
              { id: "Linear discriminant analysis" },
              { id: "Rules" },
              { id: "Decision trees" },
              { id: "Naive Bayes" },
              { id: "K nearest neighbor" },
              { id: "Probabilistic neural network" },
              { id: "Support vector machine" },
            ],
          },
          {
            id: "Consensus",
            children: [
              {
                id: "Models diversity",
                children: [
                  { id: "Different initializations" },
                  { id: "Different parameter choices" },
                  { id: "Different architectures" },
                  { id: "Different modeling methods" },
                  { id: "Different training sets" },
                  { id: "Different feature sets" },
                ],
              },
              {
                id: "Methods",
                children: [
                  { id: "Classifier selection" },
                  { id: "Classifier fusion" },
                ],
              },
              {
                id: "Common",
                children: [
                  { id: "Bagging" },
                  { id: "Boosting" },
                  { id: "AdaBoost" },
                ],
              },
            ],
          },
          {
            id: "Regression",
            children: [
              { id: "Multiple linear regression" },
              { id: "Partial least squares" },
              { id: "Multi-layer feedforward neural network" },
              { id: "General regression neural network" },
              { id: "Support vector regression" },
            ],
          },
        ],
      },
      expandRowKeys: ["1", "12"],
    };
  },
  methods: {
    onSearch() {
      this.initTable();
      this.initG6();
    },
    onChange() {
      this.initTable();
      this.initG6();
    },
    initTable() {
      let params = {
        searchKey: this.searchKey,
        maxLevel: this.level === "all" ? this.maxLevel : this.level,
        partOid: this.objectDetailsData.oid,
      };
      getTableData(params)
        .then((res) => {
          generateUUID(res);
          this.data = res;
        })
        .catch((err) => {});
    },
    initG6() {
      const container = document.getElementById("echartUsage");
      const width = container.scrollWidth;
      const height = container.scrollHeight || 500;
      const graph = (this.graph = new G6.TreeGraph({
        container: "echartUsage",
        width,
        height,
        // fitView: true,
        // linkCenter: true,
        modes: {
          default: [
            {
              type: "collapse-expand",
              onChange: function onChange(item, collapsed) {
                const data = item.get("model");
                data.collapsed = collapsed;
                return true;
              },
            },
            {
              type: "tooltip",
              formatText: function formatText(model) {
                return model.name;
              },
            },
            {
              type: "edge-tooltip",
              formatText: function formatText(model, e) {
                return model.type || false;
              },
            },
            "drag-canvas",
            "drag-node",
            "zoom-canvas",
          ],
        },
        nodeStateStyles: {
          highlight: {
            opacity: 1,
          },
          dark: {
            opacity: 0.2,
            // fill: "red",
          },
        },
        // defaultNode: {
        //   size: 26,
        // },
        edgeStateStyles: {
          highlight: {
            stroke: "#999",
          },
        },
        layout: {
          type: "compactBox",
          direction: "RL",
          //  unitRadius: 280,
          //  rankSep: 1500,
          //  subTreeSep: 500,
          // getId: function getId(d) {
          //   return d.id;
          // },
          // getHeight: () => {
          //   return 26;
          // },
          // getWidth: () => {
          //   return 26;
          // },
          getVGap: () => {
            return 20;
          },
          getHGap: () => {
            return 60;
          },
          radial: true,
        },
      }));

      graph.node(function (node) {
        return {
          size: 60,
          style: {
            fill: "#40a9ff",
            // stroke: "#096dd9",
            icon: {
              type: "image",
              value: `https://avatars.githubusercontent.com/u/105033?v=4`,
              size: [20, 20],
              clip: {
                r: 10,
              },
            },
          },
          label: node.name,
        };
      });
      graph.edge(function (node) {
        let sourceId = node.source._cfg.model.oid;
        let relations = node.target._cfg.model.relations;
        if (relations) {
          let data = relations.filter((p) => p.toOid === sourceId);
          if (data && data.length) {
            return {
              color: "#A3B1BF",
              ...data[0],
              label: data[0].type,
            };
          } else {
            return "";
          }
        } else {
          return "";
        }
      });
      graph.on("node:mouseenter", function (e) {
        var item = e.item;
        graph.setAutoPaint(false);
        graph.getNodes().forEach(function (node) {
          graph.clearItemStates(node);
          graph.setItemState(node, "dark", true);
        });
        graph.setItemState(item, "dark", false);
        graph.setItemState(item, "highlight", true);
        // graph.getEdges().forEach(function (edge) {
        //   if (edge.getSource() === item) {
        //     graph.setItemState(edge.getTarget(), "dark", false);
        //     graph.setItemState(edge.getTarget(), "highlight", true);
        //     graph.setItemState(edge, "highlight", true);
        //     edge.toFront();
        //   } else if (edge.getTarget() === item) {
        //     graph.setItemState(edge.getSource(), "dark", false);
        //     graph.setItemState(edge.getSource(), "highlight", true);
        //     graph.setItemState(edge, "highlight", true);
        //     edge.toFront();
        //   } else {
        //     graph.setItemState(edge, "highlight", false);
        //   }
        // });
        graph.paint();
        graph.setAutoPaint(true);
      });
      function clearAllStats() {
        graph.setAutoPaint(false);
        graph.getNodes().forEach(function (node) {
          graph.clearItemStates(node);
        });
        graph.getEdges().forEach(function (edge) {
          graph.clearItemStates(edge);
        });
        graph.paint();
        graph.setAutoPaint(true);
      }
      graph.on("node:mouseleave", clearAllStats);
      graph.on("canvas:click", clearAllStats);
    },
    initEchart() {
      let params = {
        searchKey: this.searchKey,
        maxLevel: this.level === "all" ? this.maxLevel : this.level,
        partOid: this.objectDetailsData.oid,
      };
      getEchartData(params)
        .then((res) => {
          this.objectDetailsData.children = res;
          generateUUID(this.objectDetailsData);
          this.echartData = this.objectDetailsData;
        })
        .catch((err) => {
          this.$error(err.msg);
        })
        .finally(() => {
          const graph = this.graph;
          graph.data(this.echartData);
          graph.render();
          graph.fitView();
        });
    },
    getMaxlevel() {
      let params = {
        partOid: this.objectDetailsData.oid,
      };
      getMaxlevel(params)
        .then((res) => {
          this.maxLevel = res;
        })
        .catch((err) => {
          this.$error(err.msg);
        })
        .finally(() => {
          this.initTable();
          this.initEchart();
        });
    },
  },
  mounted() {
    this.initG6();
    this.getMaxlevel();
  },
};
</script>

<style lang="less">
.object-details-usage {
  display: flex;
  flex-direction: column;
  height: 100%;
  > header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      .ant-input-search {
        width: 200px;
      }
      .ant-radio-group {
        margin-left: 10px;
      }
    }
  }
  > main {
    margin-top: 15px;
    height: 20px;
    flex-grow: 1;
    #echartUsage {
      height: 100%;
    }
  }
  .g6-tooltip {
    margin-top: 120px;
    // margin-left: 30px;
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    font-size: 12px;
    color: #545454;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 10px 8px;
    box-shadow: rgb(174, 174, 174) 0px 0px 10px;
  }
}
</style>
