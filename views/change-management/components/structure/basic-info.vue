<template>
    <div class="child-basic-info-wrap">
        <Info
            :objectDetailsData="instanceData"
            :childObj="'childObj'"
            @findInUserView="getObjDetail"
        ></Info>
    </div>
</template>

<script>
import Info from "./info.vue";
import ModelFactory from 'jw_apis/model-factory';

//获取对象详情
const fetchObjDetail = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/instance/findByOid`,
    method: 'get',
});

export default {
    name: 'structureBasicInfo',
    props: [
        'detailInfo',
        'isEdit',
    ],
    components: {
        Info,
    },
    data() {
        return {
            instanceData: {},
        }
    },
    mounted() {

    },
    methods: {
        getObjDetail() {
            fetchObjDetail.execute({
                oid: this.detailInfo.oid,
                type: this.detailInfo.type,
            }).then(res => {
                this.instanceData = res;
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
    },
}
</script>

<style lang="less" scoped>
.child-basic-info-wrap {
    height: 100%;
}
</style>
