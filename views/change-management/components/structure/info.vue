<template>
    <div class="basic-info-wrap">
        <div class="info-head" :style="{width:childObj?'100%':'60%'}">
            <a-button v-show="layoutName==='update'"
                @click="onSave">{{$t('btn_save')}}</a-button>
        </div>
        <div class="info-body">
            <div class="info-body-con" :style="{width:childObj?'100%':'60%'}">
                <jw-layout-builder
                    v-if="objectDetailsData.modelDefinition"
                    ref="ref_appBuilder"
                    type="Model"
                    :layoutName="layoutName"
                    :modelName="objectDetailsData.modelDefinition"
                    :instanceData="objectDetailsData"
                >
                </jw-layout-builder>
            </div>
        </div>
    </div>
</template>

<script>
import { jwLayoutBuilder } from 'jw_frame';
import ModelFactory from 'jw_apis/model-factory';

// 更新Part对象详情
const updatePartDetail = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/update`,
    method: 'post',
});

// 更新Doc对象详情
const updateDocDetail = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.docMicroServer}/document/update`,
    method: 'post',
});

export default {
    name: 'objectDetailsInfo',
    props: [
        'objectDetailsData',
        'opeKey',
        'childObj',
    ],
    components: {
        jwLayoutBuilder,
    },
    computed:{
        layoutName() {
            return this.objectDetailsData.lockSourceOid && (this.objectDetailsData.lockOwnerOid===Jw.getUser().oid)?'update' : 'show';
        },
    },
    data() {
        return {
            
        }
    },
    mounted() {

    },
    methods: {
        onSave() {
            let appBuilder = this.$refs.ref_appBuilder;
            appBuilder &&
                appBuilder
                .validate()
                .then(() => {
                    let value = appBuilder.getValue();
                    let params = {...this.objectDetailsData, ...value};
                    let api = updatePartDetail;
                    if (this.objectDetailsData.masterType === 'Part') {
                        api = updatePartDetail;
                    } else if (this.objectDetailsData.masterType === 'Document') {
                        api = updateDocDetail;
                    }
                    api.execute(
                        params
                    ).then((res) => {
                        this.$success('修改成功！');
                        this.$emit('handleMenuClick', '');
                        this.$emit('findInUserView');
                    }).catch((err) => {
                        this.$error(err.msg);
                    });
                })
        },
    },
}
</script>

<style lang="less" scoped>
.basic-info-wrap {
    height: 100%;
    display: flex;
    flex-direction: column;
    .info-head {
        display: flex;
        justify-content: flex-end;
        width: 60%;
        margin: 0 auto 16px;
    }
    .info-body {
        height: 100%;
        overflow: auto;
        .info-body-con {
            height: 100%;
            width: 60%;
            margin: 0 auto;
        }
    }
}
</style>
