<template>
  <a-modal
    :dialogStyle="{ top: '30%' }"
    :title="$t('txt_import_data')"
    :mask="false"
    :visible="visible"
    :confirm-loading="confirmLoading"
    :mask-closable="false"
    :getPopupContainer="() => document.getElementById('importPart')"
    @ok="onSubmit"
    @cancel="onCancel"
  >
    <div class="upload-wrap">
      <a-form-model ref="ref_model_form" :model="modelData">
        <a-upload-dragger
          class="upload-wrap"
          accept=".xls, .xlsx"
          :fileList="files"
          :multiple="true"
          :beforeUpload="beforeUpload"
          :remove="onRemoveFile"
        >
          <div class="appendixs-label">
            <span
              >{{$t('txt_feil_drop')}} <span class="upload-btn">
               {{$t('txt_click_upload')}}  </span
              ></span
            >
            <div> ({{$t('txt_feil_size_20')}})</div>
          </div>
        </a-upload-dragger>
      </a-form-model>
    </div>
    <template slot="footer">
      <div style="float: left">
        <a-button
          style="padding-left: 0"
          key="submit"
          type="link"
          :loading="downLoading"
          @click="onDownloadTemp"
        >
         {{$t('btn_download_temp')}}
        </a-button>
      </div>
      <div style="float: right">
        <a-button key="cancel" @click="onCancel">{{$t('btn_cancel')}}  </a-button>
        <a-button
          key="submit"
          type="primary"
          :loading="confirmLoading"
          @click="onSubmit"
        >
         {{$t('btn_ok')}}
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { getCookie } from "jw_utils/cookie";

export default {
  name: "importModal",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentTree: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      file: "",
      files: [],
      downLoading: false,
      exportLoading: false,
      confirmLoading: false,
      fileData: {},
      fileName: undefined,
      modelData: {
        activeModle: undefined,
        classModle: undefined,
        fileName: undefined,
      }
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.$refs.ref_model_form && this.$refs.ref_model_form.resetFields();
        this.modelData = {
          activeModle: undefined,
          classModle: undefined,
          fileName: undefined,
        };
        this.files = [];
      }
    },
  },
  methods: {
    onDownloadTemp() {
      const accesstoken = getCookie('token');
      this.downLoading = true;
      fetch(`${Jw.gateway}/${Jw.partBomMicroServer}/part-export/export/bom`, {
        method: "get",
        headers: {
          "Content-Type": "application/json;charset=utf8",
          appName: Jw.appName,
          accesstoken,
          tenantAlias: getCookie("tenantAlias"),
          tenantOid: getCookie("tenantOid"),
        },
      })
        .then((response) => {
          return response.blob();
        })
        .then((data) => {
          this.$success(this.$t('txt_export_success'));
          let url = window.URL.createObjectURL(
            new Blob([data], {
              type: "application/vnd.ms-excel",
            })
          );
          let link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", this.$t('txt_bom_import_temp'));
          document.body.appendChild(link);
          link.click();
          this.downLoading = false;
        })
        .catch((err) => {
          console.log(err);
          this.downLoading = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    onCancel() {
      this.fileName = "";
      this.fileData = {};
      this.files = [];
      this.$emit("update:visible", false);
    },
    beforeUpload(file) {
      let isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message({
          message: this.$t('txt_feil_size_20'),
          type: "error",
        });
        return false;
      }
      this.files = [file];
      return false;
    },
    onRemoveFile(file, fileList) {
      let { files } = this;
      this.files = [].concat(files.filter((item) => item.name != file.name));
    },
    onSubmit() {
      let { files, currentTree } = this;
      if (files.length === 0) return this.$error(this.$t('txt_please_file'));
      let formData = new FormData();
      formData.append("file", files[0]);
      formData.append("oid", this.$parent.detailInfo.oid); // 传递当前节点的oid

      this.confirmLoading = true;
      // part导入数据
      ModelFactory.create({
        url: `${Jw.gateway}/${Jw.customerServer}/part/bom/upload/withOid`,
        method: "post",
      })
        .execute(formData)
        .then((res) => {
          this.$success(this.$t('txt_import_success'));
          this.$emit("renderData");
          this.confirmLoading = false;
          this.onCancel();
        })
        .catch((err) => {
          this.confirmLoading = false;
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
  },
};
</script>

<style lang="less" scoped>
.upload-wrap {
  margin-top: 10px;
}
.file-name-input {
  margin-right: 10px;
  color: rgba(0, 0, 0, 0.65);
  /deep/ &.ant-input {
    background: #fff;
  }
}
</style>
