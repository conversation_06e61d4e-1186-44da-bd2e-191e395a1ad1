<template>
    <div class="child-structure-wrap">
        <jw-table ref="refTable"
            :loading="loading"
            :columns="columns"
            row-id="id"
            :tree-config="{rowField: 'id'}"
            :data-source.sync="tableData"
            :fetch="fetchTable"
            :showPage="false"
        >
            <template #isLock="{ row }">
                <a-tooltip>
                    <template slot="title">{{ row.lockOwnerAccount }}</template>
                    <jw-icon
                        v-if="row.lockOwnerOid"
                        :type="
                            row.lockOwnerOid === userOid
                            ? '#jwi-beiwojianchu'
                            : '#jwi-bierenjianchu'
                        "
                    />
                </a-tooltip>
            </template>
            <template #name="{ row }">
                <jwIcon :type='row.modelIcon'></jwIcon>
                {{ row.name }}
            </template>
        </jw-table>
    </div>
</template>

<script>
import { jwIcon } from 'jw_frame';
import { generateUUID } from './apis';
import ModelFactory from 'jw_apis/model-factory';

// 按需获取层级结构树
const fetchUseTree = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/useTree/find`,
    method: 'get',
});

export default {
    name: 'structureChild',
    props: [
        'detailInfo',
    ],
    components: {
        jwIcon,
    },
    data() {
        return {
            userOid: Jw.getUser().oid,
            loading: false,
            tableData: [],
        }
    },
    computed: {
        columns() {
            return [
                {
                    field: 'isLock',
                    title: '',
                    align: 'center',
                    width: 38,
                    showOverflow: false,
                    params: {
                        showHeaderMore: false,
                    },
                    slots: {
                        default: 'isLock',
                    },
                },
                {
                    field: 'name',
                    title: '名称',
                    treeNode: true,
                    minWidth: 300,
                    slots: {
                        default: 'name',
                    },
                },
                {
                    field: 'number',
                    title: '编码',
                    minWidth: 170,
                },
                {
                    field: 'displayVersion',
                    title: '版本',
                    minWidth: 160,
                    cellRender: {
                        name: 'tag',
                    },
                },
                {
                    field: 'use.quantity',
                    title: '数量',
                    minWidth: 140,
                },
                {
                    field: 'use.unit',
                    title: '单位',
                    minWidth: 140,
                },
                {
                    field: 'use.extensionContent.lineNumber',
                    title: '行号',
                    minWidth: 140,
                },
            ];
        },
    },
    mounted() {

    },
    methods: {
        onSearch() {
            this.$refs.refTable.reFetchData();
        },
        fetchTable() {
            this.loading = true;
            return fetchUseTree.execute({
                rootOid: this.detailInfo.oid,
                maxLevel: 99,
            }).then((res) => {
                generateUUID(res);
                this.loading = false;
                return {
                    data: res[0].children,
                }
            }).catch((err) => {
                this.loading = false;
                this.$error(err.msg);
            });
        },
    },
}
</script>

<style lang="less" scoped>
.child-structure-wrap {
    height: calc(~"100% - 16px");
    margin: 0 0 16px;
}
</style>
