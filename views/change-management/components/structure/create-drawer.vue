<template>
    <a-drawer
        :title="title"
        :bodyStyle="bodyStyle"
        width="50%"
        :visible="visible"
        @close="onClose"
    >
        <div class="detail-drawer-wrap">
            <div class="detail-drawer-body-wrap">
                <a-form-model v-if='hasSubModel'
                    ref="ref_model_form"
                    :model="modelData"
                    :label-position="'right'"
                >
                    <a-form-model-item
                        label="类型 :"
                        prop="activeModle"
                        :rules="{ required: true, message: '请选择类型', trigger: 'change' }"
                    >
                        <a-select v-model.trim="modelData.activeModle"
                            clearable
                            placeholder="请选择"
                            @change="onchangeModel"
                        >
                            <a-select-option
                                v-for="item in subModelOptions"
                                :key="item.name" 
                                :value="item.name"
                            >
                                {{item.name}}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>
                </a-form-model>
                <jw-layout-builder
                    v-if="visible&&modelData.activeModle"
                    ref="ref_appBuilder"
                    type="Model"
                    :layoutName="modelInfo.layoutName"
                    :modelName="modelData.activeModle"
                    :instanceData="instanceData"
                >
                    <template v-for="(node, slotName) in $scopedSlots"
                        :slot="slotName"
                        slot-scope="slotData"
                    >
                        <slot :name="slotName" v-bind="slotData"></slot>
                    </template>
                </jw-layout-builder>
            </div>
        </div>
        <div class="detail-drawer-foot-wrap">
            <a-button type="primary" :loading="nextLoading" @click="onSave('next')">完成并创建下一条</a-button>
            <a-button class="btn-cancel" :loading="saveLoading" @click="onSave('save')">完成</a-button>
        </div>
    </a-drawer>
</template>

<script>
import { jwLayoutBuilder } from 'jw_frame';
import ModelFactory from 'jw_apis/model-factory';

// 获取子类型
const fetchSubModel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/query-sub-model`,
    method: 'post',
});

export default {
    name: 'createDrawer',
    props: [
        'modelMenu',
    ],
    components: {
        jwLayoutBuilder,
    },
    data() {
        this._initInstanceData = {};
        return {
            visible: false,
            bodyStyle: { padding: 0 },
            title: '',
            saveLoading: false,
            nextLoading: false,
            instanceData: {},
            modelInfo: {},
            paramsData: {},
            hasSubModel: false,
            subModelOptions: [],
            modelData: {
                activeModle: undefined,
            }
        }
    },
    mounted() {},
    methods: {
        fetchSubModel(options) {
            fetchSubModel.execute({
                viewCode: "ENTITY_FILTER",
                objectType: this.modelInfo.modelName,
                // 处理在详情里 已存在type导致参数错误  如果存在containerType表示当前处于详情页
                //containerType 详情的containerType    ||    type  容器的containerType 
                //contextType:this.paramsData.locationInfo.catalogType,
                //同理
                //id  容器的contextOid    ||    contextOid  详情的contextOid
                //contextType:options.params.locationInfo.catalogType,
              contextType:options.params.locationInfo.catalogType,
              contextOid:options.params.locationInfo.catalogOid
            }).then(res => {
                this.subModelOptions = res;
                if (res.length == 1) {
                    this.modelData.activeModle = res[0].name;
                    this.hasSubModel = false;
                } else {
                    this.modelData.activeModle = undefined;
                    this.hasSubModel = true;
                }
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        onchangeModel(val) {
            this.instanceData = _.cloneDeep(this._initInstanceData);
            this.$nextTick(() => {
                this.modelData.activeModle = val;
            });
        },
        show(options) {
            if (options) {
                this.title = options.title;
                this.instanceData = options.instanceData || {};
                this._initInstanceData = JSON.parse(JSON.stringify(this.instanceData));
                this.modelInfo = options.modelInfo;
                this.paramsData = options.params;
            }
            this.fetchSubModel(options);
            this.visible = true;
            this.saveLoading = false;
            this.nextLoading = false;
            _.delay(() => {
                this.$refs.ref_model_form && this.$refs.ref_model_form.resetFields();
                this.$refs.ref_appBuilder && this.$refs.ref_appBuilder.clearValidate();
            });
        },
        checkModelForm() {
            let modelForm = this.$refs.ref_model_form;
            if (modelForm) {
                modelForm.validate().catch(err => {
                    this.$error('modelForm Error....');
                });
            }
        },
        onSave(flag) {
            this.checkModelForm();
            let appBuilder = this.$refs.ref_appBuilder;
            appBuilder &&
                appBuilder
                .validate()
                .then(() => {
                    this[flag+'Loading'] = true;
                    let params = appBuilder.getValue();
                    params.genericType = 'standard';
                    params.modelDefinition = this.modelData.activeModle;
                    params.locationInfo = this.paramsData.locationInfo;
                    if (this.paramsData.targetOid) {
                        params.targetOid = this.paramsData.targetOid;
                    }
                    ModelFactory.create({
                        url: this.paramsData.url,
                        method: 'post',
                    }).execute(
                        params
                    ).then((res) => {
                        this.$success('创建成功！');
                        this.$emit('fetchTable', {
                            // oid: this.paramsData.locationInfo.catalogOid,
                            // type: this.paramsData.locationInfo.catalogType,
                            // clickOid: this.paramsData.targetOid,
                            // clickType: this.paramsData.targetType,
                        });
                        if (flag === 'save') {
                            this.hide();
                        } else {
                            this.$refs.ref_model_form && this.$refs.ref_model_form.resetFields();
                            this.$refs.ref_appBuilder && this.$refs.ref_appBuilder.clearValidate();
                            this.nextLoading = false;
                        }
                    }).catch((err) => {
                        this[flag+'Loading'] = false;
                        this.$error(err.msg);
                    });
                })
        },
        hide() {
            this.visible = false;
            this.saveLoading = false;
            this.nextLoading = false;
        },
        onClose() {
            this.hide();
        },
    },
}
</script>

<style lang="less" scoped>
.detail-drawer-wrap {
    .detail-drawer-body-wrap {
        height: calc(~"100vh - 126px");
    }
}
</style>
