<template>
    <a-modal
        :visible="visible"
        title="Part移动"
        :mask-closable="false"
        width="60%"
        :bodyStyle="{height:'500px'}"
        @cancel="onCancel"
    >
        <jw-table ref="refTable"
            :toolbars="toolbars"
            :columns="columns"
            row-id="id"
            :tree-config="{rowField: 'id'}"
            :data-source.sync="tableData"
            :fetch="fetchTable"
            :row-config="{isCurrent:true}"
            :showPage="false"
            @onToolInput="onToolInput"
            @current-change="onCurrentChange"
        >
            <template #name="{ row }">
                <jwIcon :type='row.modelIcon'></jwIcon>
                {{ row.name }}
            </template>
        </jw-table>
        <template slot="footer">
            <a-button type="primary" @click="onOk">{{$t('btn_ok')}}</a-button>
            <a-button class="form-btn-cancel" @click="onCancel">{{$t('btn_cancel')}}</a-button>
        </template>
    </a-modal>
</template>

<script>
import { jwIcon } from 'jw_frame';
import { generateUUID } from './apis';
import ModelFactory from 'jw_apis/model-factory';
import { getParent } from 'utils/util.js';

// 获取可移动的目标节点
const fetchAllNodes = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/bom/parentAble`,
    method: 'get',
});

// 移动节点
const moveNode = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/bom/move`,
    method: 'post',
});

export default {
    name: 'structurePartMoveModal',
    props: [
        'visible',
        'detailInfo',
        'objectDetailsData',
    ],
    components: {
        jwIcon,
    },
    data() {
        return {
            searchKey: '',
            tableData: [],
            selectedRow: {},
        };
    },
    computed: {
        toolbars() {
            return [
                {
                    name: '搜索',
                    position: 'before',
                    display: 'input',
                    value: this.searchKey,
                    allowClear: true,
                    placeholder: '请输入搜索关键字',
                    
                    key: 'search',
                },
            ]
        },
        columns() {
            return [
                {
                    field: 'name',
                    title: '名称',
                    treeNode: true,
                    minWidth: 400,
                    slots: {
                        default: 'name',
                    },
                },
                {
                    field: 'number',
                    title: '编码',
                    minWidth: 170,
                },
                {
                    field: 'displayVersion',
                    title: '版本',
                    minWidth: 120,
                    cellRender: {
                        name: 'tag',
                    },
                },
            ];
        },
    },
    mounted() {

    },
    methods: {
        onSearch() {
            this.$refs.refTable.reFetchData();
        },
        fetchTable() {
            return fetchAllNodes.execute({
                rootOid: this.objectDetailsData.oid,
                partOid: this.detailInfo.oid,
            }).then((res) => {
                generateUUID(res);
                return {
                    data: res,
                }
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        onToolInput({key}, value) {
            if (key ==='search') {
                this.searchKey = value;
                this.onSearch();
            }
        },
        onCurrentChange({newValue}) {
            this.selectedRow = newValue;
        },
        onOk() {
            if (!this.selectedRow.oid) {
                this.$warning('请选择要移动的节点！');
                return;
            }
            let treeData = this.$parent.$refs.refTable.getTableData().fullData;
            let parent = getParent(treeData, this.detailInfo.id, 'id');
            moveNode.execute({
                parentOid: parent.oid,
                partOid: this.detailInfo.oid,
                targetOid: this.selectedRow.oid,
            }).then((res) => {
                this.$success('移动成功！');
                this.$emit('getTableData', {
                    clickOid: this.selectedRow.oid,
                    clickType: this.selectedRow.type,
                });
                this.onCancel();
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        onCancel() {
            this.searchKey = '';
            this.selectedRow = {},
            this.$refs.refTable.clearCurrentRow();
            this.$emit('close');
        },
    },
};
</script>

<style lang="less" scoped>

</style>
