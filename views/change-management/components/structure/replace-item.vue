<template>
    <div class="child-replace-item-wrap">
        <div class="table-wrap table-wrap-global">
            <div class="table-title"><div class="left-line"></div>全局替代件</div>
            <div class="table-con">
                <jw-table ref="refTableG"
                    :panel="false"
                    :toolbars="toolbarsG"
                    :tooltip-config="{}"
                    :columns="columnsG"
                    :showPage="false"
                    :data-source.sync="tableDataG"
                    :selectedRows.sync="selectedRowsG"
                    @onToolInput="onToolInput"
                    @onToolClick="onToolClick"
                    @onOperateClick="onOperateClick"
                >
                    <template slot="tool-after-start">
                        <a-button type="link" v-show="selectedRowsG.length > 0"
                            @click="onBatchRemoveG">批量删除</a-button>
                    </template>
                    <template #mutual="{ row }">
                        <a-switch v-model.trim="row.relationship.mutual"
                            @click="onChangeMutual(row)">
                            <a-icon slot="checkedChildren" type="check" />
                            <a-icon slot="unCheckedChildren" type="close" />
                        </a-switch>
                    </template>
                </jw-table>
            </div>
        </div>
        <div class="table-wrap">
            <div class="table-title"><div class="left-line"></div>局部替代件</div>
            <div class="table-con">
                <jw-table ref="refTableL"
                    :panel="false"
                    :toolbars="toolbarsL"
                    :tooltip-config="{}"
                    :columns="columnsL"
                    :showPage="false"
                    :data-source.sync="tableDataL"
                    :selectedRows.sync="selectedRowsL"
                    @onToolInput="onToolInput"
                    @onToolClick="onToolClick"
                    @onOperateClick="onOperateClick"
                >
                    <template slot="tool-after-start">
                        <a-button type="link" v-show="selectedRowsL.length > 0"
                            @click="onBatchRemoveL">批量删除</a-button>
                    </template>
                    <template #mutual="{ row }">
                        <a-switch v-model.trim="row.relationship.mutual" disabled>
                            <a-icon slot="checkedChildren" type="check" />
                            <a-icon slot="unCheckedChildren" type="close" />
                        </a-switch>
                    </template>
                </jw-table>
            </div>
        </div>
        <add-object-modal
            ref="addObjModal"
            title="添加替代件"
            :width="1280"
            :visible.sync="modalVisible"
            :fetch="searchSubstitute"
            @ok="onAddOk"
            @cancel="onCloseModal"
        />
    </div>
</template>

<script>
import addObjectModal from 'components/add-object-modal';
import ModelFactory from 'jw_apis/model-factory';

// 获取全局替代件
const fetchGlobalSubstitute = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/fuzzyGlobalSubstitute`,
    method: 'post',
});

// 获取局部替代件
const fetchLocalSubstitute = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/fuzzyLocalSubstitute`,
    method: 'post',
});

// 获取所有带版本对象
const fetchAllPart = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/fuzzyPage`,
    method: 'post',
});

// 批量添加全局替代件
const addGlobalSubstitutes = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/addGlobalSubstitutes`,
    method: 'post',
});

// 批量添加局部替代件
const addLocalSubstitutes = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/addLocalSubstitutes`,
    method: 'post',
});

// 移除局部替代件
const deleteLocalSubstitute = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/deleteLocalSubstitute`,
    method: 'post',
});

// 移除全局替代件
const deleteGlobalSubstitute = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/deleteGlobalSubstitute`,
    method: 'post',
});

// 批量移除局部替代件
const deleteLocalBatch = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/deleteLocalSubstitutes`,
    method: 'post',
});

// 批量移除全局替代件
const deleteGlobalBatch = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/deleteGlobalSubstitutes`,
    method: 'post',
});

// 更新全局替代件
const updateGlobalSubstitute = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/updateGlobalSubstitute`,
    method: 'post',
});

export default {
    name: 'structureReplaceItem',
    props: [
        'detailInfo',
        'parentData',
    ],
    components: {
        addObjectModal,
    },
    data() {
        return {
            searchKeyG: '',
            searchKeyL: '',
            tableDataG: [],
            tableDataL: [],
            selectedRowsG: [],
            selectedRowsL: [],
            munuKey: 'global',
            modalVisible: false,
        }
    },
    computed: {
        toolbarsG() {
            return [
                {
                    name: '添加',
                    key: 'createG',
                    type: 'primary',
                },
                {
                    name: '搜索',
                    position: 'before',
                    display: 'input',
                    value: this.searchKeyG,
                    allowClear: true,
                    placeholder: '请输入搜索关键字',
                   
                    key: 'searchG',
                },
            ]
        },
        toolbarsL() {
            return [
                {
                    name: '添加',
                    key: 'createL',
                    type: 'primary',
                },
                {
                    name: '搜索',
                    position: 'before',
                    display: 'input',
                    value: this.searchKeyL,
                    allowClear: true,
                    placeholder: '请输入搜索关键字',
                   
                    key: 'searchL',
                },
            ]
        },
        columnsG() {
            return [
                {
                    field: 'name',
                    title: '名称',
                    minWidth: 150,
                    cellRender: {
                        name:'link',
                    },
                },
                {
                    field: 'number',
                    title: '编码',
                    minWidth: 170,
                },
                {
                    field: 'modelDefinition',
                    title: '替代类型',
                    minWidth: 120,
                },
                {
                    field: 'relationship.type',
                    title: '类型',
                    minWidth: 170,
                    cellRender: {
                        name: 'tag',
                    },
                },
                {
                    field: 'relationship.mutual',
                    title: '双向替代',
                    minWidth: 110,
                    slots: {
                        default: 'mutual',
                    },
                },
                {
                    field: 'operation',
                    title: '操作',
                    btns: [
                        {
                            icon: 'jwi-icondelete',
                            title: '删除',
                            key: 'delete',
                        },
                    ],
                }
            ];
        },
        columnsL() {
            return [
                {
                    field: 'name',
                    title: '名称',
                    minWidth: 150,
                    cellRender: {
                        name:'link',
                    },
                },
                {
                    field: 'number',
                    title: '编码',
                    minWidth: 170,
                },
                {
                    field: 'modelDefinition',
                    title: '替代类型',
                    minWidth: 120,
                },
                {
                    field: 'relationship.type',
                    title: '类型',
                    minWidth: 170,
                    cellRender: {
                        name: 'tag',
                    },
                },
                {
                    field: 'operation',
                    title: '操作',
                    btns: [
                        {
                            icon: 'jwi-icondelete',
                            title: '删除',
                            key: 'delete',
                        },
                    ],
                }
            ];
        },
    },
    mounted() {

    },
    methods: {
        onSearch() {
            this.fetchTableG();
            this.fetchTableL();
        },
        fetchTableG() {
            fetchGlobalSubstitute.execute({
                searchKey: this.searchKeyG,
                fromOid: this.detailInfo.masterOid,
                fromType: this.detailInfo.masterType,
            }).then((res) => {
                this.tableDataG = res;
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        fetchTableL() {
            fetchLocalSubstitute.execute({
                oid: this.detailInfo.oid,
                parentNumber: this.parentData ? this.parentData.number : '',
                searchKey: this.searchKeyL,
            }).then((res) => {
                this.tableDataL = res;
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        onToolInput({key}, value) {
            if (key ==='searchG') {
                this.searchKeyG = value;
                this.fetchTableG();
            } else if (key === 'searchL') {
                this.searchKeyL = value;
                this.fetchTableL();
            }
        },
        onToolClick({key}) {
            if (key==='createG') {
                this.munuKey = 'global';
            } else if (key==='createL') {
                this.munuKey = 'local';
            }
            this.modalVisible = true;
        },
        onOperateClick(key, row) {
            if (key === 'delete') {
                let api = deleteGlobalSubstitute;
                let params = {};
                if (row.relationship.type === 'GLOBAL_SUBSTITUTE') {
                    api = deleteGlobalSubstitute;
                    params = {
                        oid: row.oid,
                        fromOid: this.detailInfo.masterOid,
                        fromType: this.detailInfo.masterType,
                        toOid: row.masterOid,
                        toType: row.masterType,
                        mutual: row.relationship.mutual,
                        type: row.relationship.type,
                    }
                } else if (row.relationship.type === 'LOCAL_SUBSTITUTE') {
                    api = deleteLocalSubstitute;
                    params = {
                        oid: row.oid,
                        fromOid: this.detailInfo.oid,
                        fromType: this.detailInfo.type,
                        toOid: row.oid,
                        toType: row.type,
                        parentPartNumber: this.parentData ? this.parentData.number : '',
                        type: row.relationship.type,
                    }
                }
                this.$confirm({
                    title: '确定移除该替代件吗?',
                    onOk: () => {
                        this.onRemoveFn(params, api, row.relationship.type);
                    },
                });
            }
        },
        onRemoveFn(params, api, key) {
            return api.execute(
                params
            ).then((res) => {
                this.$success('移除成功！');
                if (key === 'GLOBAL_SUBSTITUTE') {
                    this.fetchTableG();
                    this.selectedRowsG = [];
                } else if (key === 'LOCAL_SUBSTITUTE') {
                    this.fetchTableL();
                    this.selectedRowsL = [];
                }
            }).catch((err) => {
                if (err.msg) {
                    this.$error(err.msg);
                }
            });
        },
        onBatchRemoveG() {
            this.$confirm({
                title: '确定移除这些替代件吗?',
                onOk: () => {
                    let globalParams = [];
                    this.selectedRowsG.forEach(item => {
                        globalParams.push({
                            oid: item.oid,
                            fromOid: this.detailInfo.masterOid,
                            fromType: this.detailInfo.masterType,
                            toOid: item.masterOid,
                            toType: item.masterType,
                            mutual: item.relationship.mutual,
                            type: item.relationship.type,
                        })
                    })
                    this.onRemoveFn(globalParams, deleteGlobalBatch, 'GLOBAL_SUBSTITUTE');
                },
            });
        },
        onBatchRemoveL() {
            this.$confirm({
                title: '确定移除这些替代件吗?',
                onOk: () => {
                    let localParams = [];
                    this.selectedRowsL.forEach(item => {
                        localParams.push({
                            oid: item.oid,
                            fromOid: this.detailInfo.oid,
                            fromType: this.detailInfo.type,
                            toOid: item.oid,
                            toType: item.type,
                            parentPartNumber: this.parentData ? this.parentData.number : '',
                            type: item.relationship.type,
                        })
                    })
                    this.onRemoveFn(localParams, deleteLocalBatch, 'LOCAL_SUBSTITUTE');
                },
            });
        },
        onChangeMutual(row) {
            updateGlobalSubstitute.execute({
                oid: row.oid,
                fromOid: this.detailInfo.masterOid,
                fromType: this.detailInfo.masterType,
                toOid: row.masterOid,
                toType: row.masterType,
                mutual: row.relationship.mutual,
                type: row.relationship.type,
            }).then((res) => {
                this.$success('更新成功！');
                this.fetchTableG();
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        searchSubstitute(params) {
            return fetchAllPart.execute(params);
        },
        onAddOk(selectedRows) {
            if (selectedRows.length === 0) {
                this.$warning('请至少选择一条数据！');
                return;
            }
            let selected = [];
            let api = addGlobalSubstitutes;
            if (this.munuKey === 'global') {
                selectedRows.forEach(item => {
                    let temp = {
                        fromOid: this.detailInfo.masterOid,
                        fromType: this.detailInfo.masterType,
                        toOid: item.masterOid,
                        toType: item.masterType,
                        mutual: true,
                        type: 'GLOBAL_SUBSTITUTE',
                    }
                    selected.push(temp);
                })
                api = addGlobalSubstitutes;
            } else if (this.munuKey === 'local') {
                selectedRows.forEach(item => {
                    let temp = {
                        fromOid: this.detailInfo.oid,
                        fromType: this.detailInfo.type,
                        toOid: item.oid,
                        toType: item.type,
                        parentPartNumber: this.parentData ? this.parentData.number : '',
                        type: 'LOCAL_SUBSTITUTE',
                    }
                    selected.push(temp);
                })
                api = addLocalSubstitutes;
            }
            api.execute(
                selected
            ).then((res) => {
                this.$success('添加成功！');
                if (this.munuKey === 'global') {
                    this.fetchTableG();
                } else if (this.munuKey === 'local') {
                    this.fetchTableL();
                }
                this.$refs.addObjModal.cancelFn();
                this.onCloseModal();
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        onCloseModal() {
            this.modalVisible = false;
        },
    },
}
</script>

<style lang="less" scoped>
.child-replace-item-wrap {
    display: flex;
    flex-direction: column;
    height: calc(~"100% - 16px");
    margin: 0 0 16px;
    .ant-btn-link {
        color: #f6445a;
    }
    .table-wrap {
        display: flex;
        flex-direction: column;
        overflow: auto;
        height: 100%;
        &.table-wrap-global {
            margin-bottom: 16px;
        }
        /deep/.jw-table.has-tools .vxe-grid--toolbar-wrapper {
            padding-bottom: 10px;
        }
        .table-title {
            display: flex;
            height: 36px;
            line-height: 36px;
            margin-bottom: 10px;
            color: rgba(30, 32, 42, 0.85);
            background: #f3f1f1;
            .left-line {
                width: 3px;
                height: 100%;
                margin-right: 6px;
                background: #1890ff;
            }
        }
        .table-con {
            height: 100%;
        }
    }
}
</style>
