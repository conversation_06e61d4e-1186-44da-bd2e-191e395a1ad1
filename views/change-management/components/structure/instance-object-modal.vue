<template>
    <a-modal
        :visible="visible"
        title="选择对象"
        width="60%"
        :bodyStyle="bodyStyle"
        :mask-closable="false"
        @cancel="onCancel"
    >
        <jw-table ref="refTable"
            :panel="true"
            :toolbars="toolbars"
            :columns="columns"
            :data-source.sync="tableData"
            :fetch="fetchTable"
            :row-config="{isCurrent:true}"
            :pagerConfig="pagerConfig"
            @onToolInput="onToolInput"
            @current-change="onCurrentChange"
        >
        </jw-table>
        <template slot="footer">
            <a-button type="primary" @click="onOk">{{$t('btn_ok')}}</a-button>
            <a-button class="form-btn-cancel" @click="onCancel">{{$t('btn_cancel')}}</a-button>
        </template>
    </a-modal>
</template>

<script>
import { getParent } from 'utils/util.js';
import ModelFactory from 'jw_apis/model-factory';

// 获取所有带版本对象
const fetchAllPart = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/fuzzyPage`,
    method: 'post',
});

// 添加至
const addTo = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/bom/addTo`,
    method: 'post',
});

// 替换
const replaceObj = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/bom/replaceChange`,
    method: 'post',
});

export default {
    name: 'instanceObjectModal',
    props: [
        'visible',
        'detailInfo',
    ],
    data() {
        return {
            bodyStyle: {
                height: '500px',
                padding: 0,
            },
            searchKey: '',
            tableData: [],
            pagerConfig: {
                defaultPageSize: 10,
            },
        };
    },
    computed: {
        toolbars() {
            return [
                {
                    name: '搜索',
                    position: 'before',
                    display: 'input',
                    value: this.searchKey,
                    allowClear: true,
                    placeholder: '请输入搜索关键字',
                    
                    key: 'search',
                },
            ]
        },
        columns() {
            return [
                {
                    field: 'number',
                    title: '编码',
                    minWidth: 130,
                    cellRender: {
                        name:'link',
                    },
                },
                {
                    field: 'name',
                    title: '名称',
                    minWidth: 170,
                },
                {
                    field: 'displayVersion',
                    title: '版本',
                    minWidth: 100,
                    cellRender: {
                        name: 'tag',
                    },
                },
                {
                    field: 'lifecycleStatus',
                    title: '生命周期',
                    minWidth: 140,
                },
            ];
        },
    },
    watch: {
        visible(val) {
            if (val) {
                this.$nextTick(() => {
                    this.onSearch();
                })
            }
        }
    },
    mounted() {
        
    },
    methods: {
        onSearch() {
            this.$refs.refTable.reFetchData();
        },
        fetchTable({ current, pageSize }) {
            return fetchAllPart.execute({
                index: current,
                size: pageSize,
                searchKey: this.searchKey,
                type: this.detailInfo.type,
            }).then((res) => {
                return {
                    data: res.rows,
                    total: res.count,
                }
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        onToolInput({key}, value) {
            if (key ==='search') {
                this.searchKey = value;
                this.onSearch();
            }
        },
        onCurrentChange({newValue}) {
            this.selectedRow = newValue;
        },
        onOk() {
            if (!this.selectedRow.oid) {
                this.$warning('请选择目标节点！');
                return;
            }
            let api, params;
            let clickOid, clickType;
            if (this.detailInfo.opeKey === 'addTo') {
                api = addTo;
                params = {
                    partOid: this.detailInfo.oid,
                    targetOid: this.selectedRow.oid,
                }
                clickOid = this.selectedRow.oid;
                clickType = this.selectedRow.type;
            } else if (this.detailInfo.opeKey === 'replace') {
                let treeData = this.$parent.$refs.refTable.getTableData().fullData;
                let parent = getParent(treeData, this.detailInfo.id, 'id');
                api = replaceObj;
                params = {
                    newPartOid: this.selectedRow.oid,
                    originalPartOid: this.detailInfo.oid,
                    parentOid: parent.oid,
                }
                clickOid = parent.oid;
                clickType = parent.type;
            }
            api.execute(
                params
            ).then((res) => {
                this.$success('操作成功！');
                this.$emit('getTableData', {
                    // clickOid: clickOid,
                    // clickType: clickType,
                });
                this.onCancel();
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        onCancel() {
            this.searchKey = '';
            this.selectedRow = {},
            this.$refs.refTable.clearCurrentRow();
            this.$emit('close');
        },
    },
};
</script>

<style lang="less" scoped>
/depp/.ant-modal-body {
    height: 500px;
}
</style>
