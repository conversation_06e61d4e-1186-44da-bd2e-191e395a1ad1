<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-03-21 16:38:46
 * @LastEditTime: 2022-04-28 10:17:03
 * @LastEditors: <EMAIL>
-->
<template>
  <a-drawer :width="width" :visible="visible" :maskStyle="{background:'transparent'}" @close="onClose" :closable="false" destroyOnClose wrapClassName="layout-drawer">
    <div class="drawer-header">
      <span class="drawer-title">{{$t('txt_work_step')}}</span>
      <span class="drawer-icon-btn" @click="onClose">
        <jw-icon type="jwi-iconclose" class="drawer-icon-btn-content" />
      </span>
    </div>
    <div class="drawer-content">
      <sequence-info ref="sequence" :baseData="instanceData" :tabKeys="showTabKeys" :processData="processData" />
    </div>
  </a-drawer>
</template>

<script>
import SequenceInfo from 'views/plan/lib-sub-detail/detail/sequence-info/index.vue'

export default {
  name: "sequenceDrawer",
  components: {
    SequenceInfo
  },
  props: {
    width: {
      type: [Number, String],
      default: "1280px"
    }
  },
  data() {
    return {
      visible: false,
      instanceData: {},
      processData:{},
      showTabKeys:[]
    };
  },
  methods: {
    show(options) {
      const { instanceData,processData,activeTab,showConsumeTab } = options;
      this.instanceData = _.cloneDeep(instanceData) || {};
      this.processData = _.cloneDeep(processData) || {};
      this.showTabKeys=['Resource','Param','Accessories']
      if(showConsumeTab){ // 展示消耗式分配
        this.showTabKeys.unshift('Part')
      }
      this.visible = true;
      if(activeTab){
        setTimeout(()=>{
          this.$refs.sequence.setTab(activeTab)
        },0)
      }
      return new Promise((resolve, reject) => {

        this.onClose = () => {
          this.hide();
          resolve();
        };
      });
    },
    hide() {
      this.$refs.sequence.resetTab()
      this.visible = false;
      this.isBtnLoading = false;
    },
  
    onClose: _.noop
  }
};
</script>

<style lang="less" scoped>
.layout-drawer {
  /deep/.ant-drawer-body {
    display: flex;
    flex-direction: column;
    padding: 0;
    height: 100%;
  }
}
.drawer-header {
  display: flex;
  align-items: center;
  padding: 18px 24px;
}
.drawer-title {
  flex: 1;
  font-size: 16px;
  color: @heading-color;
}
.drawer-icon-btn {
  width: 24px;
  height: 24px;
  text-align: center;
  margin-left: 4px;
  cursor: pointer;
  &:hover {
    color: @primary-color;
  }
}
.drawer-icon-btn-content {
  font-size: 14px;
  line-height: 24px;
}
.drawer-content {
  flex: 1;
  min-height: 1px;
  padding: 0;
  overflow: auto;
}
.drawer-footer {
  height: 60px;
  line-height: 60px;
  text-align: center;
  border-top: 1px solid @border-color-base;
}
</style>