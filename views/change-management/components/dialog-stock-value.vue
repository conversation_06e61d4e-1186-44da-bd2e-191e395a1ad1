<template>
    <a-modal :visible="visibleShow" title="关联数据" width="70%" @cancel="onCancel" destroy-on-close v-if="visibleShow">
        <a-spin :spinning="tableLoading">
            <div class="table-line">
                <div class="right-panel-header">
                    <p class="header-title">{{$t('txt_stock_msg')}}</p>
                </div>
                <jw-table ref="refTable" row-id="oid" :columns="stockColumns" :dataSource="tableData.stores" :auto-load="true" :showPage="false" :height="300">
                    <template #numberSlot="{ row }">
                        <span style="color: #255ed7; cursor: pointer" @click="routerLink(row)">
                            {{ row.number }}
                        </span>
                    </template>
                </jw-table>
            </div>
            <div class="table-line">
                <div class="right-panel-header">
                    <p class="header-title">{{$t('txt_document_msg')}}</p>
                </div>
                <jw-table ref="refTable" row-id="oid" :columns="transitColumns" :dataSource="tableData.transit" :auto-load="true" :showPage="false" :height="300">
                </jw-table>
            </div>
        </a-spin>
        <div slot="footer">
            <a-button type="primary" @click="onConfirm">{{ $t('btn_ok') }}</a-button>
            <a-button @click="onCancel">{{ $t('btn_cancel') }}</a-button>
        </div>
    </a-modal>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { jwAvatar, jwUserModalV2, jwIcon } from "jw_frame";

const fetchTransferData = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/integration/queryInventoryFromU9`,
  method: "get"
});

export default {
  name: "dialog-stock-value",
  props: ["visibleShow", "showObjList"],
  components: {
    jwAvatar,
    jwUserModalV2,
    jwIcon
  },
  data() {
    return {
      ischeckBox: false,
      tableLoading: false,
      transferRecord: {},
      tableData: [],
      pagerConfig: {
        current: 1,
        pageSize: 20,
        total: 0
      },
      isVisible: true,
      stockColumns: [
        {
          field: "itemCode",
          title: this.$t("txt_materialNumber")
        },
        {
          field: "orgCode",
          title: this.$t("txt_organizationalNumber")
        },
        {
          field: "orgName",
          title: this.$t("txt_organizationalName")
        },
        {
          field: "whCode",
          title: this.$t("txt_storageLocationNumber")
        },
        {
          field: "whName",
          title: this.$t("txt_storageLocationName")
        },
        {
          field: "qty",
          title: this.$t("txt_quantity")
        }
      ],
      transitColumns: [
        {
          field: "itemCode",
          title: this.$t("txt_materialNumber")
        },
        {
          field: "docType",
          title: this.$t("txt_documentTypeName")
        },
        {
          field: "docNo",
          title: this.$t("txt_oddNumbers")
        },
        {
          field: "createdBy",
          title: this.$t("txt_creatorEmailPrefix")
        },
        {
          field: "orgCode",
          title: this.$t("txt_organizationalNumber")
        },
        {
          field: "orgName",
          title: this.$t("txt_organizationalName")
        }
      ]
    };
  },
  watch: {
    visibleShow: function(val) {
      if (val) {
        this.fetchTable();
      }
    }
  },
  created() {
    //this.fetchTable()
    console.log("showObjList", this.showObjList);
  },
  methods: {
    onConfirm() {
      this.onSubmit();
    },
    onCancel() {
      this.hide();
      this.$emit("close");
    },
    onSubmit() {
      this.hide();
      this.$emit("close");
    },
    // onPageChange(page, pageSize) {
    //   this.pagerConfig.current = page;
    //   this.pagerConfig.pageSize = pageSize;
    //   this.fetchTable(this.pagerConfig);
    // },
    // onSizeChange(pageSize, page) {
    //   this.pagerConfig.current = page;
    //   this.pagerConfig.pageSize = pageSize;
    //   this.fetchTable(this.pagerConfig);
    // },
    fetchTableData(params) {
      return fetchTransferData.execute(params);
    },
    fetchTable() {
      this.tableLoading = true;
      let numbersList = this.showObjList.map(item => item.number);
      let numbers = numbersList.join(",");
      console.log("numbersList", numbersList);
      let params = {
        number: numbers
      };
      return this.fetchTableData(params)
        .then(data => {
          this.tableLoading = false;
          this.tableData = data;
          console.log("tableData.stores", this.tableData.stores);
        })
        .catch(err => {
          this.tableLoading = false;
          console.log(err);
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    hide() {
      this.$refs.refTable.clearCurrentRow();
      this.tableLoading = false;
    }
  }
};
</script>

<style lang="less" scoped>
.detail-drawer-wrap {
  .detail-drawer-body-wrap {
    height: calc(~"100vh - 126px");
  }
}

.user-line {
  display: flex;
  align-items: center;
}
.header-title {
  color: #000000e0;
  font-size: 15px;
}
.right-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 5px;
  padding-bottom: 3px;
}
</style>
