<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-18 15:45:48
 * @LastEditTime: 2022-04-19 10:45:17
 * @LastEditors: <EMAIL>
-->
<template>
  <div class="jw-height change-flow-status">
    <p class="flow-header">{{$t('txt_process_state')}}</p>
    <div class="flow-content">
      <ul class="flow-list">
        <li class="flow-item" :class="[step.status]" v-for="(step,index) in list" :key="index">
          <div class="item-header" >
            <jw-icon :type="iconMap[step.status]" class="item-status-icon" />
            <span class="item-title">{{$t('txt_ECR_confrim')}}</span>
          </div>
          <div class="item-card">
            <p class="card-content">{{$t('txt_counditiong_advice')}}</p>
            <div class="card-footer">
              <span class="card-time"></span>
              <jw-avatar class="card-avatar" :data="{name:'Z'}"></jw-avatar>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import {jwAvatar} from 'jw_frame'

const iconMap={
  done:'jwi-iconcheck',
  wait:'jwi-iconellipsis',
  error:'jwi-iconclose'
}

export default {
  name:'changeFlowStatus',
  components:{
    jwAvatar
  },
  props:{
    list:Array,

  },
  data() {
    return {
      iconMap
    }
  },
}
</script>

<style lang="less" scoped>
.change-flow-status{
  display: flex;
  flex-direction: column;
  border-left: 1px solid  @border-color-base;
  
}
.flow-header{
  padding: 15px 20px;
  color: @heading-color;
  border-bottom: 1px solid @border-color-base;
}
.flow-content{
  flex: 1;
  overflow: auto;
}
.flow-list{
  padding: 12px 24px 0 16px;
}
.flow-item{
  position: relative;
  padding-bottom: 24px;
  overflow: hidden;
  &.error{
    .item-status-icon{
      background: @error-color;
    }
    .item-card{
      background: #FFF0F0;
    }
  }
  &.wait{
    .item-status-icon{
      background: @primary-color;
    }
    .item-card{
      background: #F0F7FF;
    }
  }
  &::before{
    content: '';
    position: absolute;
    height: 100%;
    width: 1px;
    top: 0;
    left: 12px;
    background: @border-color-base;
    z-index: 1;
  }
  &:first-child{
    &::before{
      top: 31px;
    }
  }
  &:last-child{
    &::before{
      display: none;
    }
  }
}
.item-header{
  display: flex;
  position: relative;
  align-items: center;
  margin-bottom: 8px;
  z-index: 10;
}
.item-status-icon{
  width: 24px;
  height: 24px;
  margin-right: 8px;
  text-align: center;
  border-radius: 100%;
  color: @white;
  background: @success-color;
}
.item-title{
  font-size: 16px;
  color: @heading-color;
}
.item-card{
  margin-left: 28px;
  padding: 10px 13px 0 16px;
  background: @jw-bg;
  border-radius: 4px;
}
.card-content{
  font-size: 12px;
  line-height: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid @border-color-base;
}
.card-footer{
  display: flex;
  height: 40px;
  align-items: center;
  justify-content: space-between;
}
.card-time{
  color: @text-color-secondary;
}
.card-avatar{
  /deep/.jw-avatar-item {
    width: 24px;
    height: 24px;
    line-height: 24px;
  }
}
</style>