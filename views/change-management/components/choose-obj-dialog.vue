<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-22 17:04:07
 * @LastEditTime: 2022-04-24 16:12:54
 * @LastEditors: <EMAIL>
-->
<template>
  <a-modal v-dialogDrag="true" :width="1080" :visible.sync="isVisible" :title="$t('change_obj_info')" :maskClosable="false" @cancel="onClose" dialogClass="choose-obj-dialog">
    <jw-table class="left-table" height="500px" :auto-load="false" ref="table" :selectedRows.sync="selectedRows" :checkbox-config="{checkMethod:canCheck}" :showPage="false"  :columns="columns" :fetch="fetchTable" :dataSource.sync="tableData">
    </jw-table>
    <div slot="footer">
      <a-button @click="onCancel"> {{$t('btn_cancel')}}</a-button>
      <a-button type="primary" @click="onConfirm">{{$t('btn_ok')}} </a-button>
    </div>
  </a-modal>
</template>

<script>
import {findSendChangeInfoApi} from 'apis/change/eco'

export default {
  name: "chooseObjDialog",
  components: {
  },
  data() {
    return {
      isVisible: false,
      tableData:[],
      selectedRows:[],
      changeDetail:{}
    };
  },
  computed:{
    columns(){
      return[
        {
          field:'name',
          title:this.$t('txt_name')
        },
        {
          field:'number',
          title:this.$t('txt_number')
        },
        {
          field:'modelDefinition',
          title:this.$t('txt_type')
        },
        {
          field:'displayVersion',
          title:this.$t('txt_version')
        },
        {
          field:'lifecycleStatus',
          title:this.$t('txt_lifecycle')
        },
        {
          field:'ecaNumber',
          title:this.$t('change_eca_of')
        }
      ]
    },
  },
  created () {
  },
  methods: {
    show(options) {
      if (options) {
        this.changeDetail=options.changeDetail||{}
      }
      this.isVisible = true;
      this.$nextTick(()=>{
        this.$refs.table.reFetchData()
      })
      return new Promise((resolve, reject) => {
        this.onCancel = () => {
          this.hide();
          reject("cancel");
        };

        this.onClose = () => {
          this.hide();
          reject("close");
        };

        this.onConfirm = () => {
          const {selectedRows}=this
          if(selectedRows.length){
            const isLast=this.$refs.table.isAllCheckboxChecked()
            // 根据表格是全选状态 判断是否最后一次
            resolve({selectedRows,isLast})
            this.hide();
          }else{
            this.$warning(this.$t('change_choose_warning'))
          }
        };
      });
    },
    canCheck({row}){
      return !row.ecaNumber // 已分配eca不可选中
    },
    fetchTable() {
      let param = {
        oid: this.changeDetail.oid
      };
      return findSendChangeInfoApi
        .execute(param)
        .then((data) => {
          return {data}
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },

    
    hide() {
      this.selectedRows=[]
      this.isVisible = false;
    },

    onClose() {
      this.$emit("close");
      this.hide();
    },
    onConfirm:_.noop,
    onCancel:_.noop
  }
};
</script>
<style lang="less" scoped>

</style>


