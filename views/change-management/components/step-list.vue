<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-24 14:13:58
 * @LastEditTime: 2022-04-24 14:23:43
 * @LastEditors: <EMAIL>
-->
<template>
  <div class="step-list">
    <div class="step-item" v-for="(item,index) in list" :class="{active:current===index,click:allowClick}" :key="item.key" @click="onClick(index)">
      <span class="step-no">{{index+1}}</span>
      <p class="step-name">{{item.name}}</p>
      <jw-icon class="step-icon" type="jwi-arrow-right" />
    </div>
  </div>
</template>

<script>
export default {
  name:'stepList',
  model: {
    prop: 'current',
    event: 'change'
  },
  props: {
    current: {
      type: Number,
      default: 0 
    },
    list:Array
  },
  data() {
    return {
      allowClick: false
    }
  },
  created () {
    if(typeof this.$listeners.change ==='function'){
      this.allowClick=true
    }
  },
  methods: {
    onClick(index) {
      this.$emit('change',index)
    }
  },
}
</script>

<style lang="less" scoped>
.step-list{
  display: flex;
  padding:10px;
}
.step-item{
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 60px;
  background: @jw-bg;
  &.active{
    background: #CFE4FF;
    .step-no{
      color: @white;
      background: @primary-color;
    }
    .step-name{
      color: @text-color;
    }
    .step-icon{
      color: fade(@primary-color,35);
    }
  }
  &.click{
    cursor: pointer;
  }
}
.step-no{
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  background: @jw-bg;
  border-radius: 100%;
}
.step-name{
  margin-left: 8px;
  color: @text-color-secondary;
}
.step-icon{
  margin-left: 16px;
}
</style>