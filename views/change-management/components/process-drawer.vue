<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-03-21 16:38:46
 * @LastEditTime: 2022-04-28 10:12:33
 * @LastEditors: <EMAIL>
-->
<template>
  <a-drawer :width="width" :visible="visible" :maskStyle="{background:'transparent'}" @close="onClose" :closable="false" destroyOnClose wrapClassName="layout-drawer">
    <div class="drawer-header">
      <span class="drawer-title">{{$t('txt_editor_rocess')}}</span>
      <span class="drawer-icon-btn" @click="onClose">
        <jw-icon type="jwi-iconclose" class="drawer-icon-btn-content" />
      </span>
    </div>
    <div class="drawer-content">
      <process-info ref="process" change :baseData="instanceData" :tabKeys="showTabKeys" :planData="planData" />
    </div>
  </a-drawer>
</template>

<script>
import ProcessInfo from 'views/plan/lib-sub-detail/detail/process-info/index.vue'

export default {
  name: "processDrawer",
  components: {
    ProcessInfo
  },
  props: {
    width: {
      type: [Number, String],
      default: "1280px"
    }
  },
  data() {
    return {
      visible: false,
      instanceData: {},
      planData:{},
      showTabKeys:[]
    };
  },
  methods: {
    show(options) {
      const { instanceData,planData,activeTab,showConsumeTab } = options;
      this.instanceData = _.cloneDeep(instanceData) || {};
      // if(this.instanceData.children){
      //   this.$XEUtils.filterTree(this.instanceData.children,item=>item.flag!==DEL_FLAG_VALUE) // 从结构树中过滤掉已删除数据
      // }
      this.planData = _.cloneDeep(planData) || {};
      this.showTabKeys=['Resource','Param','Document','Accessories','Sequence',]
      if(showConsumeTab){ // 展示消耗式分配
        this.showTabKeys.unshift('Part')
      }
      this.visible = true;
      if(activeTab){
        setTimeout(()=>{
          this.$refs.process.setTab(activeTab)
        },0)
      }
      return new Promise((resolve, reject) => {

        this.onClose = () => {
          this.hide();
          resolve();
        };
      });
    },
    hide() {
      this.$refs.process.resetTab()
      this.visible = false;
      this.isBtnLoading = false;
    },
  
    onClose: _.noop
  }
};
</script>

<style lang="less" scoped>
.layout-drawer {
  /deep/.ant-drawer-body {
    display: flex;
    flex-direction: column;
    padding: 0;
    height: 100%;
  }
}
.drawer-header {
  display: flex;
  align-items: center;
  padding: 18px 24px;
}
.drawer-title {
  flex: 1;
  font-size: 16px;
  color: @heading-color;
}
.drawer-icon-btn {
  width: 24px;
  height: 24px;
  text-align: center;
  margin-left: 4px;
  cursor: pointer;
  &:hover {
    color: @primary-color;
  }
}
.drawer-icon-btn-content {
  font-size: 14px;
  line-height: 24px;
}
.drawer-content {
  flex: 1;
  min-height: 1px;
  padding: 0;
  overflow: auto;
}
.drawer-footer {
  height: 60px;
  line-height: 60px;
  text-align: center;
  border-top: 1px solid @border-color-base;
}
</style>