<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-03-21 16:38:46
 * @LastEditTime: 2022-04-29 17:53:27
 * @LastEditors: <EMAIL>
-->
<template>
  <a-drawer :width="width" :visible="visible" :maskStyle="{background:'transparent'}" @close="onClose" :closable="false" wrapClassName="layout-drawer">
    <div class="drawer-header">
      <span class="drawer-title">{{$t('txt_editor_proce')}}</span>
      <span class="drawer-icon-btn" @click="onClose">
        <jw-icon type="jwi-iconclose" class="drawer-icon-btn-content" />
      </span>
    </div>
    <div class="drawer-content">
      <div class="jw-height content-left">
        <jw-table
          ref="tree"
          row-id="_oid"
          :columns="columns"
          tree
          panel
          compact
          :loading="loading"
          :tree-config="{rowField:'_oid',filter:true,filterMethod:filterMethod,expandRowKeys:initExpand,reserve:true,lazy:true,loadMethod:loadTreeChild}"
          :dataSource="treeData"
          @current-change="onCurrentNodeChange">
          <template #tree-content="{row}">
            <jw-icon :type="getIconByType(row)" v-if="row.type!=='OperationIteration'" /> 
            <div class="operation-cell" v-if="row.type==='OperationIteration'">
              <span class="_mpm_text">
                <jw-icon :type="getIconByType(row)" />
                {{row.no}} {{row.name}}
              </span>
            </div>
            <template v-else-if="row.type==='Sequence'">
              {{row.serialNumber}} {{row.name}}
            </template>
            <template v-else>{{row.name}}</template>
          </template>
        </jw-table>
      </div>
      <div class="jw-height content-right">
        <component ref="detail" :is="detailComponent" :key="selectNode._oid" :tabKeys="showTabKeys" :planData="planData" :processData="processData" :baseData="selectNode" v-if="selectNode.oid" />
      </div>
    </div>
  </a-drawer>
</template>

<script>
import {getIconByType} from 'utils'
import { getPlanTreeApi,getPlanDetailApi,updatePlanApi} from 'apis/plan'
import { findTreeSubApi } from 'apis/operation'
import PlanInfo from 'views/plan/lib-sub-detail/detail/plan-info'
import ProcessInfo from 'views/plan/lib-sub-detail/detail/process-info'
import SequenceInfo from 'views/plan/lib-sub-detail/detail/sequence-info'

export default {
  name: "planDrawer",
  components: {
    PlanInfo,
    ProcessInfo,
    SequenceInfo,
  },
  props: {
    width: {
      type: [Number, String],
      default: "calc(100% - 240px)"
    },
  },
  provide(){
    return {
      notifyUpdate:this.notifyUpdate
    }
  },
  data() {
    return {
      oid:'',
      visible: false,
      detailComponent:'planInfo',
      planData:{},
      processData:{},
      treeData: [],
      instanceData: {},
      selectNode:{},
      columns:[
          {
            field:'name',
            slots:{
              default:'tree-content'
            },
            treeNode:true,  // 树节点标志，必填
          },
      ],
      initExpand:[],
      loading:false,
      type:'plan'
    };
  },
  computed:{
    showTabKeys(){
      if(this.type==='plan'){
        return ['Resource','Param','Document','Accessories','Sequence','Part']
      }else{
        return ['Resource','Param','Accessories','Sequence']
      }
    }
  },
  methods: {
    show(options) {
      this.visible = true;
      this.$nextTick(async()=>{
        const tree=this.$refs.tree
        const { instanceData,currentData,parentData,type } = options;
        this.oid=instanceData.oid
        this.type=type
        if(type==='process'){
          this.processData=instanceData
          this.initProcessTree().then(()=>{
            const row=tree.getRowById(currentData.oid)
            this.detailComponent=currentData.type==='Sequence'?'SequenceInfo':'processInfo'
            tree.setCurrentRow(row)
            this.selectNode=row
          })
        }else{
          await this.initPlanDetail()
          this.initPlanTree().then(()=>{
            setTimeout(async() => {
              let row=tree.getRowById(currentData.oid)
              if(currentData.type==='Sequence'){
                const parentRow=tree.getRowById(parentData.oid)
                await tree.setTreeExpand(parentRow,true)
                row=tree.getRowById(parentRow._oid+currentData.oid)
                this.detailComponent='SequenceInfo'
              }else{
                this.detailComponent=currentData.type==='ProcessPlanIteration'?'planInfo':'ProcessInfo'
              }
              tree.setCurrentRow(row)
              this.selectNode=row
            }, 100);
          })
        }
      })
      return new Promise((resolve, reject) => {

        this.onClose = () => {
          this.hide();
          resolve();
        };
      });
    },
    hide() {
      this.visible = false;
      this.isBtnLoading = false;
    },
    initPlanDetail(){
      return getPlanDetailApi.execute({type:'ProcessPlanIteration',oid:this.oid})
      .then(res=>{
        this.planData=res;
      })
    },
    initPlanTree(){
      this.loading = true
      return this.getPlanTree().then((data)=>{
        this.treeData=data
        if(data.length){
          data[0]._oid=data[0].oid
          data[0].children.forEach(item=>{
            item.hasChild=true
            item._oid=item.oid
          })
          this.initExpand=[data[0]._oid]
        }
        this.loading = false
      },()=>{
        this.loading = false
      })
    },
    loadTreeChild({row}){
      return this.getPlanTree(row).then(data=>{
        if(data.length===0){
          row.hasChild=false
        }
        if(row.type==='OperationIteration'){ // 暂时处理 oid重复的问题（已发现 物料）
          data.forEach(item=>{
            item._oid=row._oid+item.oid
          })
        }
        return data
      })
    },
    initProcessTree(){
      let params={
        oid:this.oid
      }
      this.loading=true
      return findTreeSubApi.execute(params)
      .then(data=>{
        this.treeData=data
        if(data.length){
          data[0]._oid=data[0].oid
          data[0].children.forEach(item=>{
            item._oid=item.oid
          })
          this.initExpand=[data[0]._oid]
        }
        this.loading=false
      })
      .catch(()=>{
        this.loading=false
      })
    },
    getPlanTree(node={oid:'',type:''}) {
      let params = {
        processPlanOid: this.oid,
        nodeOid: node.oid,
        nodeType: node.type
      }
      return getPlanTreeApi
        .execute(params)
        .then((data) => {
          return data
        })
    },
    filterMethod({option,row}) {
      return row.name.indexOf(option.data)!==-1 // option.data为搜索输入框当前值
    },
    onCurrentNodeChange({row}) {
      if(row.type==='ProcessPlanIteration'){ // 工艺规程
        this.detailComponent='PlanInfo'
        this.selectNode=row
      }else if(row.type==='OperationIteration'){ // 工序
        this.detailComponent='processInfo'
        this.selectNode=row

      }else if(row.type==='Sequence'){ // 工步
        const parentNode=this.$refs.tree.getRowById(row.parentId) // 获取工序信息
        this.detailComponent='SequenceInfo'
        this.selectNode=row
        this.processData=parentNode
      }else{
        const parentNode=this.$refs.tree.getRowById(row.parentId)
        this.detailComponent='processInfo'
        this.selectNode=parentNode
        this.$nextTick(()=>{
          this.$refs.detail.setTab(row.modelDefinition)
        })
      }
    },
    notifyUpdate(){
      return this.initPlanTree()
    },
    getIconByType,
    onClose: _.noop
  }
};
</script>

<style lang="less" scoped>
.layout-drawer {
  /deep/.ant-drawer-body {
    display: flex;
    flex-direction: column;
    padding: 0;
    height: 100%;
  }
}
.drawer-header {
  display: flex;
  align-items: center;
  padding: 18px 24px;
}
.drawer-title {
  flex: 1;
  font-size: 16px;
  color: @heading-color;
}
.drawer-icon-btn {
  width: 24px;
  height: 24px;
  text-align: center;
  margin-left: 4px;
  cursor: pointer;
  &:hover {
    color: @primary-color;
  }
}
.drawer-icon-btn-content {
  font-size: 14px;
  line-height: 24px;
}
.drawer-content {
  display: flex;

  flex: 1;
  min-height: 1px;
  overflow: hidden;
}
.content-left{
  flex-basis: 294px;
  flex-shrink: 0;
  border-right: 1px solid @border-color-base;
  /deep/.jw-table{
    padding:3px 0 16px 0;
    &.has-tools.is-panel{
      .vxe-grid--toolbar-wrapper{
        padding: 0 20px 8px;
      }
      .jw-tool-content{
        width: 100%;
      }
    }
  }
}
.content-right{
  flex: 1;
  min-width: 1px;
}
.drawer-footer {
  height: 60px;
  line-height: 60px;
  text-align: center;
  border-top: 1px solid @border-color-base;
}
</style>