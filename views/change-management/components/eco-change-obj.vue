<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-21 17:31:39
 * @LastEditTime: 2022-04-27 10:00:16
 * @LastEditors: <EMAIL>
-->
<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-08 15:27:27
 * @LastEditTime: 2022-04-20 17:07:41
 * @LastEditors: <EMAIL>
-->
<template>
  <jw-flex-row class="select-change-obj" :leftWidth="414" :marginRight="0">
    <div class="jw-height left-panel" slot="left" v-if="objList.length">
      <div class="left-panel-header">
        <p class="header-title">{{$t('change_obj')}}</p>
        <a-button type="primary" @click="getStockValue" style="margin-right: 50px" size="small">查询U9数据</a-button>
        <a-radio-group class="type-radio" v-model.trim="currentType" @change="onTypeChange" size="small">
          <a-radio-button :value="type.key" v-for="type in typeList" :key="type.key">
            <a-tooltip placement="top" :title="type.title">
              <jw-icon :type="type.icon" v-if="type.icon" />
              <template v-else>{{type.key}}</template>
            </a-tooltip>
          </a-radio-button>
        </a-radio-group>
      </div>
      <obj-list :current.sync="currentObj" :data="showObjList" @onDelClick="onDelClick" @change="onCurrentChange" :showDel="showDel" />
      <a-button class="add-btn" @click="showAdd" v-if="isEdit">{{$t('change_add_change')}}</a-button>
    </div>
    <div slot="middle" class="jw-height right-panel">

      <jw-table class="right-table" :loading="tableLoading" :columns="columns" :show-page="false" :dataSource="tableData" v-if="showObjList.length">
        <p class="header-title" slot="header">{{$t('change_influence_obj')}}</p>
        <template #number="{row}">
          <a-tooltip v-if="!row.change&&isEdit" :title="row.msg" placement="left">
            <jw-icon type="jwi-iconwarning-circle-full" class="disable-icon" />
          </a-tooltip>
          {{row.number}}
        </template>
        <template #influence="{row}">
          <a-radio-group v-model.trim="row.influence" @change="onRadioChange(row)" :disabled="!row.change" v-if="isEdit">
            <a-radio :value="true">{{$t('txt_yes')}}</a-radio>
            <a-radio :value="false">{{$t('txt_no')}}</a-radio>
          </a-radio-group>
          <template v-else>{{row.influence?$t('change_is_influnence'):$t('change_no_influnence')}}</template>
        </template>
      </jw-table>
      <empty class="empty-box" v-else>
        <p class="empty-text">{{$t('change_empty_txt')}}</p>
        <a-button type="primary" size="large" @click="showAdd" v-if="isEdit">{{$t('change_add_change')}}</a-button>
      </empty>
    </div>
    <jw-search-engine-modal :title="$t('change_add_change')" only-search-object :visible.sync="globalSearchVisible" :model-list='[
          {
              name:$t("txt_part") ,
              code: "PartIteration",
          },
          {
              name:$t("txt_doc") ,
              code: "DocumentIteration",
          },
          {
              name: "MCAD",
              code: "MCADIteration",
          },
          {
              name: "ECAD",
              code: "ECADIteration",
          },
      ]' @ok='addObjectOk' />
    <DialogStockValue ref="transfer" :visibleShow="visibleStack" @close="cancelData" :showObjList="showObjList">
    </DialogStockValue>
  </jw-flex-row>
</template>

<script>
import { findEcoInfluenceInfoApi, dealInfluenceInfoApi } from "apis/change/eco";
import { jwFlexRow, jwSearchEngineModal } from "jw_frame";
import ObjList from "./obj-list.vue";
import Empty from "components/empty";
import DialogStockValue from "./dialog-stock-value";
export default {
  name: "ecoChangeObj",
  props: {
    changeObjs: Array,
    changeDetail: Object,
    isEdit: Boolean
  },
  components: {
    ObjList,
    jwFlexRow,
    Empty,
    jwSearchEngineModal,
    DialogStockValue
  },
  data() {
    return {
      typeList: [
        {
          key: "All",
          title: this.$t("txt_all")
        },
        {
          key: "Part",
          title: this.$t("txt_part"),
          icon: "#jwi-lianjian",
          children: ["PartIteration"]
        },
        {
          key: "Document",
          title: this.$t("txt_doc"),
          icon: "#jwi-wendang",
          children: ["DocumentIteration"]
        },
        {
          key: "ECAD",
          title: "ECAD",
          icon: "#jwi-ecad",
          children: ["ECADIteration"]
        },
        {
          key: "MCAD",
          title: "MCAD",
          icon: "#jwi-mcad",
          children: ["MCADIteration"]
        }
      ],
      tableLoading: false,
      changeLoading: false,
      objList: [],
      tableData: [],
      currentType: "All",
      currentObj: {},
      globalSearchVisible: false,
      visibleStack: false
    };
  },
  computed: {
    checkRowKeys() {
      return this.objList.map(v => v.oid);
    },
    columns() {
      return [
        {
          field: "number",
          title: this.$t("txt_number"),
          slots: {
            default: "number"
          }
        },
        {
          field: "name",
          title: this.$t("txt_name"),
          showOverflow: "ellipsis"
        },
        {
          field: "relationName",
          title: this.$t("txt_relation")
        },
        {
          field: "modelDefinition",
          title: this.$t("txt_type")
        },
        {
          field: "displayVersion",
          title: this.$t("txt_version")
        },
        {
          field: "lifecycleStatus",
          title: this.$t("txt_lifecycle")
        },
        {
          field: "influence",
          title: this.$t("change_influnence_whether"),
          showOverflow: "ellipsis",
          slots: {
            default: "influence"
          }
        }
      ];
    },
    showObjList() {
      const typeObj = this.typeList.find(item => item.key === this.currentType);
      if (!typeObj.children) return this.objList;
      return this.objList.filter(item => typeObj.children.includes(item.type));
    }
  },
  created() {
    this.init(this.changeObjs);
  },
  watch: {
    changeObjs(val) {
      this.init(val);
    },
    currentObj: {
      handler: function() {
        this.getInfuenceData();
      },
      deep: true
    }
  },
  methods: {
    getStockValue() {
      this.visibleStack = true;
    },
    cancelData() {
      this.visibleStack = false;
    },
    init(objs) {
      this.objList = _.cloneDeep(objs);
      if (this.objList.length) {
        this.currentType = "All";
        const hasOldCurrent = this.objList.find(
          item => item.oid === this.currentObj.oid
        );
        if (!hasOldCurrent) {
          // 更新列表数据时 不改变当前高亮
          this.currentObj = this.objList[0];
        }
      }
    },
    onTypeChange() {
      if (this.showObjList.length) {
        this.currentObj = this.showObjList[0];
      }
    },
    showAdd() {
      this.globalSearchVisible = true;
    },
    addObjectOk(selectedRows) {
      let addList = _.differenceBy(selectedRows, this.objList, "oid"); // 比较出新增的数据
      let deleteList = _.differenceBy(this.objList, selectedRows, "oid"); // 比较出删除的数据
      this.globalSearchVisible = false;
      if (addList.length > 0) {
        this.$emit("change", { deleteList: [], addList }); // 完全由外部控制列表数据 组件只传递 更新数据
      }
    },
    onRadioChange(row) {
      // 切换受影响按钮
      if (this.changeLoading) return;
      const params = {
        oid: row.oid,
        type: row.type,
        ecoOid: this.changeDetail.oid,
        influence: row.influence
      };
      this.changeLoading = true;
      dealInfluenceInfoApi
        .execute(params)
        .then(() => {
          this.$emit("update"); // 通知外部刷新列表数据
          this.changeLoading = false;
        })
        .catch(err => {
          this.changeLoading = false;
          this.$error(err.msg || err);
        });
    },
    onDelClick(item, index) {
      if (item.oid === this.currentObj.oid) {
        // if(index===0&&this.objList[1]){
        //   this.currentObj=this.objList[1]
        //   this.getInfuenceData()
        // }else{
        this.tableData = [];
        // }
      }
      let objIndex = this.objList.findIndex(obj => obj.oid === item.oid);
      this.objList.splice(objIndex, 1);
      this.$emit("change", { deleteList: [item], addList: [] });
    },
    onCurrentChange() {
      // this.getInfuenceData()
    },
    getInfuenceData() {
      if (this.currentObj) {
        if (this.currentObj.objectType === "2") {
          // 受影响对象变成变更对象后不需要查再次受影响对象 防止套娃
          console.log(
            "受影响对象变成变更对象后不需要查再次受影响对象 防止套娃,设计如此"
          );
          return (this.tableData = []);
        }
        this.tableLoading = true;
        const param = {
          oid: this.currentObj.oid,
          type: this.currentObj.type,
          ecoOid: this.changeDetail.oid
        };
        findEcoInfluenceInfoApi
          .execute(param)
          .then(data => {
            this.tableLoading = false;
            this.tableData = data;
            //判断受影响对象是否在变更对象里
            if (data.length) {
              let numbers = this.changeObjs.map(item => item.number);
              this.tableData.forEach(item => {
                let exist = this.changeObjs.find(
                  c =>
                    c.number == item.number &&
                    c.modelDefinition == item.modelDefinition
                );
                if (exist) {
                  item.influence = true;
                  item.oid = exist.oid;
                }
              });
            }
          })
          .catch(err => {
            this.tableLoading = false;
            this.$error(err.msg || this.$t("msg_failed"));
          });
      }
    },
    getData() {
      return this.objList;
    },
    showDel(item) {
      if (this.isEdit) {
        return item.hasOwnProperty("objectType")
          ? item.objectType === "3"
          : true; // 只有手动新增的对象才可删除
      } else {
        return false;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.left-panel {
  display: flex;
  flex-direction: column;
  padding: 14px 15px 16px 24px;
}
.disable-icon {
  color: @warning-color;
  // margin-right: 5px;
}
.left-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 8px;
}
.select-change-obj {
  background-color: transparent;
  /deep/.column-middle {
    padding: 0 16px 16px;
  }
}
.type-radio {
  /deep/.ant-radio-button-wrapper {
    height: 28px;
    line-height: 28px;
  }
}
.header-title {
  color: @heading-color;
}
.add-btn {
  width: 285px;
  margin: 0 auto;
}

.right-panel {
  position: relative;
  padding-top: 14px;
}
.right-table.has-tools {
  /deep/.vxe-grid--toolbar-wrapper {
    padding-bottom: 8px;
    line-height: 28px;
  }
}
.empty-text {
  margin: 32px 0;
  color: @text-color-secondary;
}
</style>
