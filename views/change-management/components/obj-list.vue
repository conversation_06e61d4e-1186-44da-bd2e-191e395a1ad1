<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-08 16:14:35
 * @LastEditTime: 2022-05-06 17:18:33
 * @LastEditors: <EMAIL>
-->
<template>
  <ul class="obj-list">
    <li class="obj-item" v-for="(item,index) in data" :key="item.oid" :class="{active:current&&current.oid===item.oid}" @click="onCurrentChange(item)">
      <a-tooltip placement="top">
        <template slot="title">
          <span>{{item | showName}}</span>
        </template>
        <p class="obj-name"><jw-icon :type="item.modelIcon" class="obj-icon" />{{item | showName(true)}}</p>
      </a-tooltip>
      <a style="margin-left:20px;" @click="toDetail(item)">详情页</a>
      <jw-icon type="jwi-iconclose-circle-full" class="del-icon" v-if="delVisible(item) && !item.issueObject" @click.native.stop="onDelClick(item,index)" />
    </li>
  </ul>
</template>

<script>

export default {
  name:'objList',
  props:{
    data:Array,
    current:Object,
    showDel:[Boolean,Function] // 删除按钮控制
  },
  filters: {
    showName: function(value,isLimitLength) {
      let {cname, name,number,displayVersion}=value
      // if(isLimitLength&&name.length>8){
      //   name=name.slice(0,8)+'...'
      // }
      return [cname || name,number,displayVersion].join('，')
    },
  },
  data() {
    return {
    }
  },
  methods: {
    onDelClick(item,index) {
      this.$emit('onDelClick',item,index)
    },
    onCurrentChange(item){
      this.$emit('update:current',item)
      if(this.current&&item.oid!==this.current.oid){
        this.$emit('change',item)
      }
    },
    delVisible(item){ // 删除按钮显示逻辑
      if(typeof this.showDel==='function'){
        return this.showDel(item)
      }else{
        return this.showDel
      }
    },
    toDetail(row){
      console.log(row,'row')
      const { href } = this.$router.resolve({
        path: '/detailPage/object-details',
        query: {
          oid: row.oid,
          type: row.type,
          masterType: row.masterType||row.type,
          modelDefinition: row.modelDefinition,
          tabActive: 'product',
        }
      })
      window.open(href, "_blank")
    }
  },
}
</script>

<style lang="less" scoped>
.obj-list{
  flex: 1;
  overflow: auto;
  margin-bottom: 16px;
}
.obj-item{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  padding: 0 16px;
  background: fade(@black,2);
  border: 1px solid @border-color-base;
  border-radius: 6px;
  cursor: pointer;
  & + .obj-item{
    margin-top: 8px;
  }
  &.active{
    border-color:#A4C9FC;
    background: #F0F7FF;
  }
}
.obj-icon{
  margin-right: 5px;
}
.obj-name{
  flex-basis: auto;
}
.del-icon{
  flex-shrink: 0;
  margin-left: 6px;
  font-size: 14px;
  cursor: pointer;
  &:hover{
    color: @primary-color;
  }
}
</style>