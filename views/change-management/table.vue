<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-07 14:18:30
 * @LastEditTime: 2022-05-06 17:24:07
 * @LastEditors: <EMAIL>
-->
<template>
  <div class="change-management">
    <jw-table ref="table" :toolbars="pageCode==='changeRecord'?[]:toolbars" :showPage="pageCode==='changeRecord'?false:true" :columns="columns" :data-source.sync="tableData" :fetch="fetchTable" v-if="instanceData.oid" @onOperateClick="onOperateClick">
      <template #number="{ row }">
        <div class="name-wrap">
          <div class="name-con" @click="onOpenECRDetail(row)">
            <jwIcon :type='row.modelIcon'></jwIcon>
            <span class="name-item">{{ row.number }}</span>
          </div>
        </div>
      </template>
      <template #owner="{ row }">
        <user-info :accounts="[row.owner]" :showname="true"></user-info>
      </template>
    </jw-table>
    <!-- 发起流程 -->
    <start-process-modal :visible="processVisible" :pageCode="'objectProcess'" :detailInfo="currentRecord" @close="onCloseProcessModal" @getTableData="reFetch"></start-process-modal>
    <!-- 修改所有者 -->
    <jw-user-modal-v2 ref="user-modal" :isCheckbox="false" />
  </div>
</template>

<script>
import { jwIcon, jwUserModalV2 } from "jw_frame";
import userInfo from "components/user-info";
import ModelFactory from "jw_apis/model-factory";
import commonStore from "jw_stores/common";
import { deleteECRApi } from "apis/change";
import { setOwner } from "apis/baseapi";
import startProcessModal from "/views/product-content/process-manage/start-process-modal";
// 查询部件，文档，CAD权限
const getOperationFilter = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
  method: "post"
});

// ECR是否可以发起流程校验
const startECRWorkflow = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/change/check/startECRWorkflow`,
  method: "post"
});

export default {
  name: "changeManagement",
  data() {
    return {
      tableData: [],
      searchKey: "",
      instanceData: {},
      processVisible: false,
      currentRecord: {},
      permissionList: []
    };
  },
  props: ["pageCode", "changeApi"],
  components: {
    jwIcon,
    jwUserModalV2,
    startProcessModal,
    userInfo
  },
  computed: {
    columns() {
      return [
        {
          field: "number",
          title: this.$t("txt_plan_number"),
          slots: {
            default: "number"
          }
        },
        {
          field: "name",
          title: this.$t("txt_plan_name")
        },
        {
          field: "changeType.txt",
          title: this.$t("change_type")
        },
        {
          field: "degreeEmergency.txt",
          title: this.$t("change_emergency")
        },
        {
          field: "lifecycleStatus",
          title: this.$t("txt_status")
        },
        {
          field: "owner",
          title: this.$t("txt_owner"),
          slots: {
            default: 'owner'
          }
        },
        {
          field: "createDate",
          title: this.$t("txt_create_time"),
          formatter: "date"
        },
        {
          field: "operation",
          title: this.$t("txt_operation"),
          maxShowBtn: 1,
          permissionCode: "ECRINSTANCE"
        }
      ];
    },
    toolbars() {
      return [
        {
          name: "发起变更申请",
          key: "add",
          type: "primary",
          click: this.jumbTo,
          isVisible: this.permissionList.includes("ECR.create")
        },
        {
          display: "input",
          key: "search",
          value: this.searchKey,
          input: this.onSearchInput
        }
      ];
    }
  },
  created() {
    this.instanceData = { ...this.$route.query };
    this.delaySearch = _.debounce(this.reFetch, 500);
    this.operationFilter();
  },
  methods: {
    onOperateClick(item, row) {
      let key = item.code || item;
      if (key === "details") {
        this.onOpenECRDetail(row);
      } else if (key === "startProcess") {
        startECRWorkflow
          .execute({
            ecrOid: row.oid
          })
          .then(res => {
            this.currentRecord = row;
            this.processVisible = true;
          })
          .catch(err => {
            this.$error(err.msg);
          });
      } else if (key === "updateOwner") {
        this.$refs["user-modal"]
          .show({
            type: "User"
          })
          .then(res => {
            setOwner
              .execute({
                oid: row.oid,
                type: row.type,
                ownerAccount: res.account
              })
              .then(res => {
                this.$success(this.$t("msg_save_success"));
                this.reFetch();
              })
              .catch(err => {
                this.$error(err.msg);
              });
          });
      } else if (key === "delete") {
        this.onDelete(row);
      }
    },
    jumbTo() {
      let { oid, modelDefinition } = this.$route.query;
      this.$router.push({
        path: `/change-management/ecr/create/${oid}`,
        query: {
          oid,
          modelDefinition
        }
      });
    },
    onDelete(row) {
      this.$confirm({
        title: this.$t("change_confirm_delete"),
        okText: this.$t("btn_ok"),
        cancelText: this.$t("btn_cancel"),
        onOk: () => {
          const params = {
            ecrOid: row.oid
          };
          return deleteECRApi.execute(params).then(() => {
            this.reFetch();
          });
        }
      });
    },
    onSearchInput(value) {
      this.searchKey = value;
      this.delaySearch();
    },
    reFetch() {
      this.$refs.table.reFetchData();
    },
    fetchTable({ current, pageSize }) {
      let params = {};
      if (this.pageCode === "changeManage") {
        params = {
          fromOid: this.instanceData.oid,
          fromType: this.instanceData.type,
          searchKey: this.searchKey, //关键字（name/number）
          index: current,
          size: pageSize
        };
      } else if (this.pageCode === "changeRecord") {
        params = {
          searchKey: "",
          oid: this.$route.query.oid,
          type: this.$route.query.type,
          searchType: "ECR"
        };
      }
      return ModelFactory.create({
        url: this.changeApi,
        method: "post"
      })
        .execute(params)
        .then(data => {
          return { data: data.rows || data, total: data.count };
        })
        .catch(err => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    onCloseProcessModal() {
      this.processVisible = false;
    },
    processModalShow(row) {
      this.currentRecord = row;
      this.processVisible = true;
    },
    operationFilter() {
      let query = this.$route.query;
      let param = {
        viewCode: "CHANGEOPERATION",
        objectType: query.type,
        objectOid: query.oid
      };
      getOperationFilter
        .execute(param)
        .then(data => {
          if (data) {
            data.forEach(item => {
              if (item.status == "enable") {
                this.permissionList.push(
                  item.modelType + "." + item.permissionKey
                );
              }
            });
          }
        })
        .catch(err => {
          console.log(err);
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    onOpenECRDetail(row) {
      let isBlank = false;
      if (this.$route.name === "object-details") {
        sessionStorage.setItem("currentTabName", "changeRecord");
        isBlank = true;
      }
      if (
        this.$route.name == "object-details" ||
        this.$route.name == "product-content"
      ) {
        let enterEcrRoute = {
          routeName: this.$route.name,
          query: { ...this.$route.query }
        };
        sessionStorage.setItem("enterEcrInfo", JSON.stringify(enterEcrRoute));
      }
      row.masterType = row.modelDefinition;
      Jw.jumpToDetail({
        ...row,
        tabActive: "change",
        blank: isBlank
      });
    }
  }
};
</script>

<style lang="less" scoped>
.change-management {
  height: 100%;
  padding: 10px 10px 0 10px;
  .name-wrap {
    display: flex;
    justify-content: space-between;
    .name-con {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      .name-item {
        color: #255ed7;
        cursor: pointer;
      }
    }
  }
}
</style>
