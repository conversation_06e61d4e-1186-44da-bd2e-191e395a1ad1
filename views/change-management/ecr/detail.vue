<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-16 14:29:34
 * @LastEditTime: 2022-05-06 18:04:29
 * @LastEditors: <EMAIL>
-->
<template>
    <jw-page @back="goBack">
        <!-- :toolbars="toolbars" -->
        <jw-toolbar class="jw-width" slot="header">
            <template slot="before-start">
                <span style="margin-right: 8px; color: #1890ff; cursor: pointer" @click="goBackList">
                    {{ isFromIssue == true ? this.$t('txt_problem_management') : fromRouteTitle }}
                </span>
                &gt;
                <jw-icon :type="lockIcon" v-if="lockIcon" />

                <div class="_mpm-page-title">
                    <jw-icon type="#jwi-biangengduixiang" />
                    <span>{{ ecrData.name }}(ECR)</span>
                </div>
                <a-tag color="blue">{{ $t(ecrData.lifecycleStatus) }}
                </a-tag>
            </template>
            <template slot="after-end">
                <PermissionDropdown style="margin-right: 30px" showType="btn" :permissionCode="viewCode" :callback="permissionFilter" :instanceData="ecrData" @click="onMenuClick" />
            </template>
        </jw-toolbar>
        <a-tabs class="jw-tabs" v-model.trim="tabActive" @change="onTabChange">
            <a-tab-pane class="jw-height" key="base" :tab="$t('txt_base_info')">
                <jw-flex-row :rigthWidth="418" :marginRight="0">
                    <jwLayoutBuilder class="base-form" ref="layout" layoutName="show" modelName="ECR" :instanceData="ecrData" slot="middle">
                        <template slot="issueSlot">
                            <a-select style="min-width: 180px" disabled placeholder="" mode="multiple" :defaultValue="ecrData.issueList">
                                <a-select-option v-for="item in issueFilterSelect" :key="item.oid">
                                    {{ item.name }}
                                </a-select-option>
                            </a-select>
                        </template>
                    </jwLayoutBuilder>
                    <!-- <flow-status :list="flowList" slot="right" /> -->
                </jw-flex-row>
            </a-tab-pane>
            <a-tab-pane class="jw-height" key="obj" :tab="$t('change_obj')">
                <change-obj :changeObjs="changeObjs" ref="changeObj" />
            </a-tab-pane>
            <a-tab-pane class="jw-height" key="program" :tab="$t('change_program')">
                <change-program ref="changeProgram" type="ECR" :changeObjs="changeObjs" :changeDetail="ecrData" class="change-program" />
            </a-tab-pane>
            <!-- <a-tab-pane class="jw-height" key="relation" tab="ECO">
                <jw-table class="left-table" style="padding:10px;" ref="table" :showPage="false" :columns="columns" :fetch="fetchTable" :dataSource.sync="tableData">
                    <template #number="{ row }">
                        <div class="name-wrap">
                            <div class="name-con" @click="onOpenECODetail(row)">
                                <jwIcon :type="row.modelIcon"></jwIcon>
                                <span class="name-item">{{ row.number }}</span>
                            </div>
                        </div>
                    </template>
                </jw-table>
            </a-tab-pane> -->
            <!-- <a-tab-pane class="jw-height" key="history" tab="历史记录"></a-tab-pane> -->
        </a-tabs>
        <!-- 发起流程 -->
        <start-process-modal :visible="processVisible" :pageCode="'objectProcess'" :detailInfo="ecrData" @close="onCloseProcessModal" @getTableData="fetchBaseDetail"></start-process-modal>
        <!-- 修改所有者 -->
        <jw-user-modal-v2 ref="user-modal" :isCheckbox="false" />
    </jw-page>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { findChangeInfoApi, searchRelevantByECRApi } from "apis/change";
import { findDetail, setOwner } from "apis/baseapi";
import { jwLayoutBuilder, jwFlexRow, jwToolbar, jwUserModalV2 } from "jw_frame";
import ChangeObj from "../components/change-obj.vue";
import ChangeProgram from "../components/change-program.vue";
import FlowStatus from "../components/flow-status.vue";
import PermissionDropdown from "components/permission-dropdown";
import startProcessModal from "/views/product-content/process-manage/start-process-modal";

// 问题管理列表
const fetchIssueList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/pr/issue/search`,
  method: "post"
});

export default {
  name: "ecrDetail",
  components: {
    jwLayoutBuilder,
    jwFlexRow,
    jwToolbar,
    jwUserModalV2,
    ChangeObj,
    ChangeProgram,
    FlowStatus,
    startProcessModal,
    PermissionDropdown
  },
  data() {
    return {
      isFromIssue: false,
      issueFilterSelect: [],
      fromRouteTitle: this.$t("txt_change_management"),
      lastRoute: "",
      changeOid: this.$route.query.oid,
      ecrData: {},
      changeObjs: [],
      tableData: [],
      tabActive: "base",
      flowList: [
        { status: "wait" },
        { status: "done" },
        { status: "error" },
        { status: "done" },
        { status: "done" },
        { status: "done" },
        { status: "done" },
        { status: "done" }
      ],
      viewCode: "ECRINSTANCEOPERATION",
      processVisible: false,
      isFromIssue: false
    };
  },
  computed: {
    toolbars() {
      return this.isMeCreate
        ? [
            {
              display: "dropdown",
              position: "after",
              name: this.$t("txt_operation"),
              key: "more",
              type: "primary",
              prefixIcon: "jwi-iconmenu-application",
              menuList: [
                {
                  name: this.$t("btn_edit"),
                  key: "edit",
                  click: this.goEdit,
                  isVisible: true
                },
                {
                  name: this.$t("txt_edtor_CEO"),
                  key: "eco",
                  click: this.createECO,
                  isVisible: true
                }
              ]
            }
          ]
        : [];
    },
    columns() {
      return [
        {
          field: "number",
          title: this.$t("txt_number"),
          slots: {
            default: "number"
          }
        },
        {
          field: "name",
          title: this.$t("txt_name")
        },
        {
          field: "type",
          title: this.$t("txt_type")
        },
        {
          field: "lifecycleStatus",
          title: this.$t("txt_plan_lifecycle")
        },
        {
          field: "updateBy",
          title: this.$t("txt_updateBy")
        },
        {
          field: "updateDate",
          title: this.$t("txt_update_time"),
          formatter: "date"
        }
      ];
    },
    isMeCreate() {
      const { createBy } = this.ecrData;
      if (!createBy) return false;
      const { account } = Jw.getUser();
      return account === createBy;
    },
    lockIcon() {
      return this.isMeCreate ? "#jwi-beiwojianchu" : "#jwi-bierenjianchu";
    }
  },
  inject: ["setBreadcrumb"],
  created() {
    this.setBreadcrumb([]);
    this.fetchBaseDetail();
    this.fetchChangeObjs();
    this.fetchIssueList();
    let enterEcrInfo = JSON.parse(sessionStorage.getItem("enterEcrInfo"));
    let isFromIssue = localStorage.getItem("isFromIssue");
    if (enterEcrInfo && enterEcrInfo.routeName == "object-details") {
      this.fromRouteTitle = this.$t("txt_change_record");
    }
    if (isFromIssue) {
      // 从缓存读取是否是从问题详情的ECR跳转过来的
      this.isFromIssue = isFromIssue;
      this.fromRouteTitle = this.$t("txt_problem_management");
    }
  },
  methods: {
    fetchIssueList() {
      let params = {};
      fetchIssueList
        .execute(params)
        .then(res => {
          this.issueFilterSelect = res.rows;
        })
        .catch(err => {
          // this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    goBackList() {
      // 从缓存获取跳转进来变更详情的信息
      let enterEcrInfo = JSON.parse(sessionStorage.getItem("enterEcrInfo"));
      let isFromIssue = localStorage.getItem("isFromIssue");
      let issueInfo = localStorage.getItem("issueInfo");
      if (isFromIssue) {
        localStorage.removeItem("isFromIssue");
        localStorage.removeItem("issueInfo");
        this.$router.push({
          name: "problem-detail",
          path: `/problem-detail`,
          query: {
            oid: issueInfo,
            tabActive: "list",
            issuDetailActive: "3"
          }
        });
      } else {
        if (enterEcrInfo && enterEcrInfo.routeName == "object-details") {
          Jw.jumpToDetail(enterEcrInfo.query, {
            toUrl: "/object-details"
          });
          sessionStorage.setItem("currentTabName", "changeRecord");
          sessionStorage.removeItem("enterEcrInfo");
        } else if (this.isFromIssue) {
          this.$router.back();
        } else {
          const containerInfo = this.ecrData.containerInfo[0] || {};
          this.$route.query.tabActive = "change";
          Jw.jumpToDetail({
            ...containerInfo,
            tabActive: "change"
          });
        }
      }
    },
    permissionFilter(btns) {
      let permissionBtns = btns.filter(btn => {
        return btn.code != "details";
      });
      return permissionBtns;
    },
    onMenuClick(item) {
      let key = item.code || item;
      if (key === "edit") {
        this.goEdit();
      } else if (key === "eco") {
        this.createECO();
      } else if (key === "startProcess") {
        this.processVisible = true;
      } else if (key === "updateOwner") {
        this.$refs["user-modal"]
          .show({
            type: "User"
          })
          .then(res => {
            setOwner
              .execute({
                oid: this.ecrData.oid,
                type: this.ecrData.type,
                ownerAccount: res.account
              })
              .then(res => {
                this.$success(this.$t("msg_save_success"));
                this.fetchBaseDetail();
              })
              .catch(err => {
                this.$error(err.msg);
              });
          });
      }
    },
    onCloseProcessModal() {
      this.processVisible = false;
    },
    fetchBaseDetail() {
      findDetail.execute({ oid: this.changeOid, type: "ECR" }).then(res => {
        let issueList = res.issueList
          ? res.issueList.map(item => item.oid)
          : [];
        res.issueList = issueList;
        this.ecrData = res;
      });
    },
    fetchChangeObjs() {
      findChangeInfoApi.execute({ oid: this.changeOid, list: [] }).then(res => {
        this.changeObjs = res;
      });
    },
    fetchTable() {
      let param = {
        oid: this.changeOid
      };
      return searchRelevantByECRApi
        .execute(param)
        .then(data => {
          return { data: data };
        })
        .catch(err => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    onTabChange() {},
    onOpenECODetail(row) {
      if (this.$route.name === "object-details") {
        sessionStorage.setItem("currentTabName", "changeRecord");
      }
      Jw.jumpToDetail(row, {
        toUrl: "/eco-details",
        viewCode: "ECOINSTANCE"
      });
    },
    goEdit() {
      const tabKeys = ["base", "obj", "program"];
      const step = tabKeys.indexOf(this.tabActive);
      this.$router.push(
        `/change-management/ecr/edit/${this.changeOid}?step=${step}`
      );
    },
    createECO() {
      this.$router.push(`/change-management/eco/create/${this.changeOid}`);
    },
    goBack(){
      this.$router.go(-1)
    },
  }
};
</script>

<style lang="less" scoped>
.base-form {
  width: 75%;
  padding-top: 14px;
  margin: 0 auto;
}
.status-tag {
  height: 32px;
  margin-right: 0;
  line-height: 32px;
}
.name-wrap {
  display: flex;
  justify-content: space-between;
  .name-con {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    .name-item {
      color: #255ed7;
      cursor: pointer;
    }
  }
}
</style>