<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-20 15:11:49
 * @LastEditTime: 2022-04-26 13:45:51
 * @LastEditors: <EMAIL>
-->
<template>
  <jw-page>
    <template slot="header">
      <span style="margin-right:8px;color:#1890ff;cursor:pointer;" @click="goBackList">
        {{$t('txt_change_management')}}
      </span>
      &gt;
      <div class="_mpm-page-title">

        <jw-icon type="#jwi-biangengduixiang" />
        <span>{{ecrData.name}}(ECR)</span>
      </div>
    </template>
    <div class="create-container jw-height">
      <step-list v-model.trim="currentStep" :list="stepList" />
      <div class="step-content">
        <jwLayoutBuilder class="create-form" ref="layout" layoutName="update" :instanceData="ecrData" modelName="ECR" v-if="currentStep===0">
          <template slot="issueSlot">
            <a-select style="min-width: 180px" placeholder="" mode="multiple" v-model.trim="issueSelectData" @change="handleChange">
              <a-select-option v-for="item in issueFilterSelect" :key="item.oid">
                {{ item.name }}
              </a-select-option>
            </a-select>
          </template>
        </jwLayoutBuilder>
        <change-obj :changeObjs="changeObjs" :changeDetail="ecrData" class="change-obj" ref="changeObj" @change="onObjsChange" isEdit v-loading="objLoading" v-if="currentStep===1" />
        <change-program type="ECR" :changeObjs="changeObjs"  :changeDetail="ecrData" class="change-program" isEdit v-if="currentStep===2" />
      </div>
      <div class="setp-footer">
        <a-button type="primary" @click="onPreClick" v-if="currentStep===1">{{$t('btn_pre_step')}}</a-button>
        <a-button type="primary" @click="onSaveClick" :loading="btnLoading" v-if="currentStep===0">{{$t('btn_save')}}</a-button>
        <a-button type="primary" @click="onNextClick" v-if="currentStep!==2">{{$t('btn_next_step')}}</a-button>
        <a-button type="primary" :disabled=disableNext @click="goBack" v-if="currentStep===2">{{$t('btn_confirm')}}</a-button>
        <a-button @click="goBack" v-if="currentStep<=2">{{$t('btn_cancel')}}</a-button>
      </div>
    </div>
  </jw-page>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { jwLayoutBuilder } from "jw_frame";
import {
  findChangeInfoApi,
  updateEcrBasicInfoApi,
  updateChangeInfoApi
} from "apis/change";
import { findDetail } from "apis/baseapi";
import ChangeObj from "../components/change-obj.vue";
import ChangeProgram from "../components/change-program.vue";
import StepList from "../components/step-list.vue";
// 问题管理列表
const fetchIssueList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/pr/issue/search`,
  method: "post"
});

export default {
  name: "ecrEdit",
  components: {
    jwLayoutBuilder,
    ChangeObj,
    ChangeProgram,
    StepList
  },
  data() {
    return {
      disableNext:false,
      issueFilterSelect: [],
      issueSelectData: [],
      stepList: [
        {
          name: this.$t("txt_change_info"),
          key: "base",
          com: ""
        },
        {
          name: this.$t("change_select_obj"),
          key: "object",
          com: ""
        },
        {
          name: this.$t("change_fill_program"),
          key: "program",
          com: ""
        }
      ],
      changeOid: this.$route.params.oid,
      currentStep: 0,
      changeObjs: [],
      ecrData: {},
      btnLoading: false,
      objLoading: false
    };
  },
  watch: {
    currentStep(val) {
      switch (val) {
        case 0:
          this.fetchBaseDetail();
          break;
        case 1:
          this.fetchChangeObjs();
          break;
        case 2:
          this.fetchChangeObjs();
          break;
      }
    }
  },
  inject: ["setBreadcrumb"],
  created() {
    this.setBreadcrumb([]);
    let { step } = this.$route.query;
    if (step && Number(step) <= 2) {
      this.currentStep = Number(step);
    }
    if (step && Number(step) == -1) {
      this.currentStep = 0;
    }
    this.fetchBaseDetail();
    this.fetchIssueList();
  },
  methods: {
    handleChange(value) {
      this.issueSelectData = value;
      this.$refs.layout.insData.aboutIssue = value;
    },
    fetchIssueList() {
      let params = {};
      fetchIssueList
        .execute(params)
        .then(res => {
          this.issueFilterSelect = res.rows;
        })
        .catch(err => {
          // this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    fetchBaseDetail() {
      findDetail.execute({ oid: this.changeOid, type: "ECR" }).then(res => {
        let issueList = res.issueList
          ? res.issueList.map(item => item.oid)
          : [];
        res.issueList = issueList;
        this.issueSelectData = issueList;
        this.ecrData = res;
      });
    },
    fetchChangeObjs() {
      findChangeInfoApi.execute({ oid: this.changeOid, list: [] }).then(res => {
        this.changeObjs = res;
      });
    },
    onSaveClick(flag) {
      const layout = this.$refs.layout;
      let { issueSelectData, issueFilterSelect } = this;
      let issueListArray = issueFilterSelect.filter(item =>
        issueSelectData.includes(item.oid)
      );
      let issueList = issueListArray.map(item => {
        return { oid: item.oid, type: item.type };
      });
      console.log(issueList);
      layout.validate().then(() => {
        const data = layout.getValue();
        const params = {
          ...this.ecrData,
          ...data,
          issueList: issueList
        };
        this.btnLoading = true;
        updateEcrBasicInfoApi
          .execute(params)
          .then(() => {
            this.btnLoading = false;
            if (flag !== "next") {
              this.$success(this.$t("msg_save_success"));
            }
          })
          .catch(err => {
            this.btnLoading = false;
            this.$error(err.msg || err);
          });
      });
    },
    onNextClick() {
      switch (this.currentStep) {
        case 0:
          this.onSaveClick("next");
          this.currentStep++;
          break;
        case 1:
          this.currentStep++;
          break;
      }
    },
    goBackList() {
      const containerInfo = this.ecrData.containerInfo[0] || {};
      Jw.jumpToDetail({
        ...containerInfo,
        tabActive: "change"
      });
    },
    goBack() {
      this.$router.go(-1);
    },
    onPreClick() {
      this.currentStep--;
    },
    onObjsChange(data,callback) {
      const params = {
        deleteList: [],
        addList: [],
        ecrOid: this.changeOid,
        ...data
      };
      this.objLoading = true;
      updateChangeInfoApi
        .execute(params)
        .then(() => {
          this.objLoading = false;
          this.fetchChangeObjs();
          callback&&callback(true)
        })
        .catch(err => {
          this.objLoading = false;
          this.$error(err.msg || err);
          callback&&callback(false)
        });
    }
  }
};
</script>

<style lang="less" scoped>
.create-container {
  display: flex;
  flex-direction: column;
  background-image: linear-gradient(179deg, #f0f7ff 0%, @white 30%);
}
.step-content {
  flex: 1;
  min-height: 1px;
  overflow: auto;
}
.setp-footer {
  padding: 14px 0;
  border-top: 1px solid @border-color-base;
  text-align: center;
}
.create-form {
  width: 944px;
  margin: 0 auto;
}
.change-obj,
.change-program {
  /deep/.left-panel,
  /deep/.right-panel {
    padding-top: 0;
  }
}
</style>