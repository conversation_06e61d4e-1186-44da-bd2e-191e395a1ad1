<!--
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2022-04-20 15:08:52
 * @LastEditTime: 2022-05-06 17:25:54
 * @LastEditors: <EMAIL>
-->
<template>
  <jw-page>
    <template slot="header">
      <span style="margin-right: 8px; color: #1890ff; cursor: pointer" @click="goBack">
        变更管理
      </span>
      &gt;
      <div class="_mpm-page-title">{{ $t("btn_new_create") }}</div>
    </template>
    <div class="create-container jw-height">

      <step-list :current="currentStep" :list="stepList" />
      <div class="steps-content">
        <keep-alive>
          <component isEdit :ref="stepList[currentStep].key" :is="stepList[currentStep].content" :tabData='stepList[currentStep]' :baseData="stepsInfo.baseData" :changeObjs="stepsInfo.changeObjs" :changeDetail="stepsInfo.ecrData" @change="onObjsChange"/>
        </keep-alive>
      </div>
      <div class="steps-action">
        <a-button v-if="currentStep!=0" :disabled="btnLoading" style="margin-left: 8px" @click="prev">
          上一步
        </a-button>
        <a-button :loading="btnLoading" :disabled="currentStep ===stepList.length - 1 || disableNext" type="primary" @click="next">
          下一步
        </a-button>
        <template v-if="stepList[currentStep].key=='changeWorkflow'">
          <a-button :loading="btnLoading" type="primary" @click="onStartFolw">
            启动流程
          </a-button>
        </template>

        <a-button @click="goBack">
          取消
        </a-button>

      </div>
    </div>

  </jw-page>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import Row_Store from "jw_stores/instance-info";

import ChangeObj from "../components/change-obj.vue";
import ChangeProgram from "../components/change-program.vue";
import BaseInfo from "./base-info.vue";
import changeWorkflow from "./workflow-template.vue";
import StepList from "../components/step-list.vue";

// 创建变更单
const createChangeApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/change/create`,
  method: "post"
});

// 更新变更单变更对象
const updateChangeInfoApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/change/updateChangeInfo`,
  method: "post"
});

// 查询变更单变更对象
const findChangeInfoApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/change/findChangeInfo`,
  method: "post"
});

// 文件夹目录
const findDefaultCatalogApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/folder/searchTree`,
  method: "get"
});

export default {
  name: "ecrCreate",
  components: {
    ChangeObj,
    ChangeProgram,
    changeWorkflow,
    BaseInfo,
    StepList
  },
  data() {
    let {oid,modelDefinition} = this.$route.query;
    this._containerModelDefinition=modelDefinition
    this._containerOid=oid
    return {
      stepList: [
        {
          name: this.$t("txt_change_info"),
          key: "BaseInfo",
          content: "BaseInfo"
        },
        {
          name: this.$t("change_select_obj"),
          key: "ChangeObj",
          content: "ChangeObj"
        },
        {
          name: "填写变更方案",
          key: "changeProgram",
          content: "changeProgram"
        },
        {
          name: "启动流程",
          key: "changeWorkflow",
          content: "changeWorkflow"
        }
      ],
      currentStep: 0,

      stepsInfo: {
        baseData: {},
        changeObjs: [],
        ecrData: {}
      },
      btnLoading: false,

      // libraryData: {},

      libraryCatalog: {},

      fetching: false,
      disableNext:false
    };
  },
  inject: ["setBreadcrumb"],
  created() {
    this.setBreadcrumb([]);
    // 从缓存中拿库信息
    // this.libraryData = Row_Store.get(this._containerModelDefinition);

    // 从缓存中拿变更对象的信息
    let changeObjsStore= Row_Store.get("changeRows")
    if(changeObjsStore){
      this.stepsInfo.baseData.name=changeObjsStore[0].name+"-变更申请"
    }
    this.getLibraryCatalog();
  },

  methods: {
    async onStartFolw() {
      let key = this.stepList[this.currentStep].key;
      let ref = this.$refs[key];
      // 检查是否通过
      const isReviewValid = await ref.checkIsReviewEcr();
      if (!isReviewValid) {
        return;  // 结束流程，不继续执行
      }
      this.btnLoading = true;
      await ref.onStart().then(()=>{
        this.btnLoading = false;
        this.goBack();
      }).catch(() => {
        this.btnLoading = false;
        throw new Error("启动流程异常");
      });
    },
    async next() {
      await this.checkData();
      //选择变更对象后会自动创建ECR
      if (this.currentStep == 1 && !this.stepsInfo.ecrData.oid) {
        await this.createECR();
      }

      this.currentStep++;

    },

    onObjsChange(data,callback) {
      //更新变更单的变更对象
      if(!this.stepsInfo?.ecrData?.oid){
        callback&&callback(true)
        return
      }
      const params = {
        ecrOid: this.stepsInfo.ecrData.oid,
        ...data
      };
      this.btnLoading = true;
      updateChangeInfoApi
        .execute(params)
        .then(() => {
          this.btnLoading = false;
          this.fetchChangeObjs();
          callback&&callback(true)
        })
        .catch(err => {
          this.btnLoading = false;
          this.$error(err.msg || err);
          callback&&callback(false)
        });
    },

     fetchChangeObjs() {
      findChangeInfoApi.execute({ oid: this.stepsInfo.ecrData.oid, list: [] }).then(res => {
        this.stepsInfo.changeObjs = res;
      }).catch(err=>{
        this.$error(err.msg)
      });
    },

    createECR() {
      const {
        changeObjs,
        baseData,
        issueList,
        issueLocationInfo
      } = this.stepsInfo;

      let locationInfo = {};
      // 如果是从问题管理跳转过来，那么容器信息，需要从当前问题获取容器信息
      if (issueLocationInfo) {
        locationInfo = issueLocationInfo;
      } else {
        let { libraryCatalog } = this;
        locationInfo = {
          containerOid: libraryCatalog.containerOid,
          containerType: libraryCatalog.containerType,
          catalogType: libraryCatalog.type || "Folder",
          catalogOid: libraryCatalog.oid
        };
      }
      if (changeObjs.length) {
        const params = {
          ...baseData,
          issueList,
          locationInfo: locationInfo,
          changeList: changeObjs.map(item => ({
            oid: item.oid,
            type: item.type
          }))
        };
        this.btnLoading = true;
        return createChangeApi
          .execute(params)
          .then(res => {
            this.btnLoading = false;
            this.stepsInfo.ecrData = res;

          })
          .catch(err => {
            this.$error(err.msg);
            this.btnLoading = false;
            throw new Error("创建变更对象异常");
          });
      } else {
        this.$warning(this.$t("change_select_warning"));
        return Promise.reject();
      }
    },

    prev() {
      let key = this.stepList[this.currentStep].key;
      let ref = this.$refs[key];
      if (ref && ref.getValue) {
        let val = ref.getValue();
        Object.assign(this.stepsInfo, val);
      }
      this.currentStep--;
    },

    // async checkData() {
    //   let key = this.stepList[this.currentStep].key;
    //   let ref = this.$refs[key];
    //   if (ref && ref.validate) {
    //     await ref.validate();
    //   }
    //   if (ref && ref.getValue) {
    //     let val = ref.getValue();
    //     Object.assign(this.stepsInfo, val);
    //   }
    // },
    async checkData() {
  let key = this.stepList[this.currentStep].key;
  let ref = this.$refs[key];

  // 添加对 change-program 组件的特殊处理
  if (key === 'changeProgram') {
    const layout = ref.$refs.layout;
    debugger
    if (layout) {
      // 获取当前编辑的对象
      const currentObj = ref.currentObj;

      // 如果是文档类型，则验证 deliveryOid
      if (currentObj?.type === 'DocumentIteration' && ref.isEdit) {
        const attributeChange = currentObj.attributeChange;
        if (attributeChange && (!attributeChange.extensionContent || !attributeChange.extensionContent.deliveryOid)) {
          this.$error('请选择交付清单');
          throw new Error('交付项未选择');
        }
      }
    }
  }

  // 原有的验证逻辑
  if (ref && ref.validate) {
    await ref.validate();
  }
  if (ref && ref.getValue) {
    let val = ref.getValue();
    Object.assign(this.stepsInfo, val);
  }
},
    goBack() {
      // let row=this.libraryData||{}
      let {containerOid,containerType,containerModelDefinition,name}=this.libraryCatalog

      Jw.jumpToDetail({
        oid:containerOid,
        modelDefinition:containerModelDefinition,
        containerOid,
        name,
        type:"Container",
        viewCode: "CONTAINERINSTANCE",
        tabActive: "change"
      });
    },
    // 获取容器信息
    getLibraryCatalog() {

      const param = {
        containerOid: this._containerOid,
        containerModel:this._containerModelDefinition
      };
      findDefaultCatalogApi.execute(param).then(res => {
        this.libraryCatalog = res[0];
      });
    }
  }
};
</script>

<style lang="less" scoped>
.create-container {
  display: flex;
  flex-direction: column;
  background-image: linear-gradient(179deg, #f0f7ff 0%, @white 30%);
  .steps-content {
    flex: 1;
    min-height: 1px;
    overflow: auto;
  }

  .steps-action {
    text-align: center;
    padding: 14px 0;
    border-top: 1px solid @border-color-base;
  }
}
</style>
