<template>
  <jwLayoutBuilder
    class="create-form"
    :instanceData="baseData"
    ref="ref_base_info"
    layoutName="create"
    modelName="ECR"
  >
    <template #issue="scoped">
      <a-select
        style="min-width: 180px"
        :placeholder="$t('txt_selective_association')"
        mode="multiple"
        show-search
        v-model="issueSelectData"
        :disabled="isFromIssue"
        @change="handleChange"
        :filter-option="filterOption"
        option-label-prop="label"
      >
        <a-select-option
          v-for="item in issueFilterSelect"
          :key="item.oid"
          :label="item.name"
        >
          <jwIcon style="margin-right: 8px" type="#jwi-linwu" />
          {{ item.name }},{{ item.number }}
        </a-select-option>
      </a-select>
    </template>
  </jwLayoutBuilder>
</template>
<script>
import { jwLayoutBuilder } from "jw_frame";
import Row_Store from "jw_stores/instance-info";
import ModelFactory from "jw_apis/model-factory";
// 问题管理列表
const fetchIssueList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/pr/issue/search`,
  method: "post",
});

// 创建变更申请单查询
const fetchRoleChange = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.sysconfigServer}/preferences/setting/query-config-value`,
  method: "get",
});

export default {
  components: {
    jwLayoutBuilder,
  },
  props: {
    baseData: Object,
    changeObjs: Array,
  },
  data() {
    this._oid = this.$route.params.oid;
    return {
      issueFilterSelect: [],
      issueSelectData: [],
      isFromIssue: false,
      issue: this.$route.query.issue,
      issueData:{}
    };
  },
  // 判断如果是从问题管理跳转过来的，那么相关问题字段不能编辑
  beforeRouteEnter(to, from, next) {
    const { query } = from;
    Row_Store.set(`${query.masterType}`, query);
    next((vm) => {
      if (from.name == "problem-manage" || from.name == "problem-detail") {
        vm.isFromIssue = true;
        vm.issueSelectData = [`${vm.$route.params.oid}`];
        vm.$refs.layout.insData.issueList = [`${vm.$route.params.oid}`];
      } else {
        vm.isFromIssue = false;
      }
    });
  },
  created() {
    this.fetchIssueList();
    this._changeObjsStore = Row_Store.get("changeRows");

    if (this._changeObjsStore) {
      Row_Store.set("changeRows", "");
    }
    this.$nextTick(() => {
      if (this.issue) {
        this.isFromIssue = true;
        this.issueSelectData = [`${this._oid}`];
        this.$refs.ref_base_info.insData.issueList = [`${this._oid}`];
        this.issueData={name:this.$route.query.name,oid:this._oid}
      } else {
        this.isFromIssue = false;
      }
    });
  },
  methods: {
    getValue() {
      const layout = this.$refs.ref_base_info;

      let initChangeObjs = this.changeObjs.filter((item) => !item.issueObject);
      if (this._changeObjsStore) {
        initChangeObjs.push(...this._changeObjsStore);
        this._changeObjsStore = null;
      }

      // 点击下一步的时候，把当前的问题管理的对象放到第二步的对象里面
      let paramsIssueList = this.issueFilterSelect.filter((item, index) =>
        this.issueSelectData.includes(item.oid)
      );
      let issueList = paramsIssueList.map((item) => {
        return { oid: item.oid, type: item.type };
      });

      let issueChangeObjs = [];

      if (paramsIssueList) {
        let issueObjectList = [];
        paramsIssueList.forEach((item) => {
          let ro = item.issueList
          issueObjectList.push(...ro);
        });
        issueObjectList = _.uniqBy(issueObjectList, "oid");
        issueChangeObjs = issueObjectList.map((item) => {
          item.issueObject = true;
          return item;
        });

        console.log("问题单的变更对象", issueChangeObjs);
      }

      let locationInfo = null;

      if (this.isFromIssue) {
        locationInfo = {
          containerOid: paramsIssueList[0].containerOid,
          containerType: paramsIssueList[0].containerType,
          catalogType: paramsIssueList[0].catalogType || "Folder",
          catalogOid: paramsIssueList[0].catalogOid,
        };
      }

      return {
        baseData: layout.getValue(),
        issueList,
        issueLocationInfo: locationInfo,
        changeObjs: initChangeObjs.concat(issueChangeObjs),
      };
    },
    async validate() {
      let res = await this.$refs.ref_base_info.validate();
      if (!res) {
        throw new Error("校验失败");
      }
    },
    //暂时没有用到
    fetchRoleChange() {
      let params = {
        name: "PR_Apply_Status",
      };
      fetchRoleChange
        .execute(params)
        .then((data) => {
          this.configStatus = data[0].value;
          console.log("问题变更申请单状态配置---------------", data);
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },

    filterOption: (input, option) => {
      return (
        option.componentOptions.children[1].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    handleChange(value) {
      this.issueSelectData = value;
      this.$refs.ref_base_info.insData.issueList = value;
    },
    fetchIssueList() {
      let params = {
        index: 1,
        size: 1000,
        searchKey: "",
        typeEnum: "lifecycleStatus",
        value: "Inwork",
      };
      fetchIssueList
        .execute(params)
        .then((res) => {
          // let isObjs= res.rows.filter(item=>{
          //   return item.issueList.some(o=>o.lifecycleStatus=="Released")
          // })
          this.issueFilterSelect = res.rows || [];
          if (this.isFromIssue) {
            let issueObject = this.issueFilterSelect.find(
              (item) => item.oid == this._oid
            )||{};
            let issueObjectList = issueObject.issueList||[]
            this.changeObjs = issueObjectList.map((item) => {
              item.issueObject = true;
              return item;
            });
          }
        })
        .catch((err) => {
          this.$error(err.msg);
          console.log(err,'ssss')
        });
    },
  },
};
</script>
<style lang="less" scoped>
.create-form {
  width: 944px;
  margin: 0 auto;
}
</style>


