<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-22 14:13:54
 * @LastEditTime: 2022-04-26 13:46:23
 * @LastEditors: <EMAIL>
-->
<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-20 15:11:49
 * @LastEditTime: 2022-04-21 17:33:13
 * @LastEditors: <EMAIL>
-->
<template>
  <jw-page>
    <template slot="header">
      <span style="margin-right:8px;color:#1890ff;cursor:pointer;" @click="goBackList">
        {{$t('txt_change_management')}}
      </span>
      &gt;
      <span style="margin-right:8px;color:#1890ff;cursor:pointer;" @click="goBackECR">
        ECR
      </span>
      &gt;
      <div class="_mpm-page-title">
        <jw-icon type="#jwi-biangengduixiang" />
        <span>{{ecoData.name}}(ECO)</span>
      </div>
    </template>
    <div class="create-container jw-height">
      <step-list v-model.trim="currentStep" :list="stepList" />
      <div class="step-content">
        <jwLayoutBuilder class="create-form" ref="layout" layoutName="update" :instanceData="ecoData" modelName="ECO" v-if="currentStep===0" />
        <change-obj :changeObjs="changeObjs" :changeDetail="ecoData" class="change-obj" ref="changeObj" @change="onObjsChange" @update="fetchChangeObjs" isEdit v-loading="objLoading" v-if="currentStep===1" />
        <change-program :changeObjs="changeObjs" :changeDetail="ecoData" type="ECO" class="change-program" isEdit v-if="currentStep===2" />
      </div>
      <div class="setp-footer">
        <a-button type="primary" @click="onPreClick" v-if="currentStep===1">{{$t('btn_pre_step')}}</a-button>
        <a-button type="primary" @click="onSaveClick" :loading="btnLoading" v-if="currentStep===0">{{$t('btn_save')}}</a-button>
        <a-button type="primary" @click="onNextClick" v-if="currentStep!==2">{{$t('btn_next_step')}}</a-button>
        <a-button type="primary" @click="goBack" v-if="currentStep===2">{{$t('btn_ok')}}</a-button>
        <a-button @click="goBack" v-if="currentStep<=2">{{$t('btn_cancel')}}</a-button>
      </div>
    </div>
  </jw-page>
</template>

<script>
import { jwLayoutBuilder } from "jw_frame";
import Row_Store from "jw_stores/instance-info";
import {
  findEcoChangeInfoApi,
  updateECOBasicInfoApi,
  updateEcoChangeInfoApi
} from "apis/change/eco";
import { findDetail } from "apis/baseapi";
import ChangeObj from "../components/eco-change-obj.vue";
import ChangeProgram from "../components/change-program.vue";
import StepList from "../components/step-list.vue";

export default {
  name: "ecoEdit",
  components: {
    jwLayoutBuilder,
    ChangeObj,
    ChangeProgram,
    StepList
  },
  data() {
    return {
      stepList: [
        {
          name: this.$t("change_fill_info"),
          key: "base",
          com: ""
        },
        {
          name: this.$t("txt_comfirm_object"),
          key: "object",
          com: ""
        },
        {
          name: this.$t("change_fill_program"),
          key: "program",
          com: ""
        }
      ],
      changeOid: this.$route.params.oid,
      currentStep: 0,
      changeObjs: [],
      ecoData: {},
      btnLoading: false,
      objLoading: false
    };
  },
  watch: {
    currentStep(val) {
      switch (val) {
        case 0:
          this.fetchBaseDetail();
          break;
        case 1:
          this.fetchChangeObjs();
          break;
        case 2:
          this.fetchChangeObjs();
          break;
      }
    }
  },
  inject: ["setBreadcrumb"],
  created() {
    this.setBreadcrumb([]);
    let { step } = this.$route.query;
    if (step && Number(step) <= 2) {
      this.currentStep = Number(step);
    }
    if (step && Number(step) == -1) {
      this.currentStep = 0;
    }
    this.fetchBaseDetail();
  },
  methods: {
    fetchBaseDetail() {
      findDetail.execute({ oid: this.changeOid, type: "ECO" }).then(res => {
        this.ecoData = res;
      });
    },
    fetchChangeObjs() {
      findEcoChangeInfoApi
        .execute({ oid: this.changeOid, list: [] })
        .then(res => {
          this.changeObjs = res;
        });
    },
    onSaveClick(flag) {
      const layout = this.$refs.layout;
      layout.validate().then(() => {
        const data = layout.getValue();
        const params = {
          ...this.ecoData,
          ...data
        };
        this.btnLoading = true;
        updateECOBasicInfoApi
          .execute(params)
          .then(() => {
            this.btnLoading = false;
            if (flag !== "next") {
              this.$success(this.$t("msg_save_success"));
            }
          })
          .catch(err => {
            this.btnLoading = false;
            this.$error(err.msg || err);
          });
      });
    },
    onNextClick() {
      switch (this.currentStep) {
        case 0:
          this.onSaveClick("next");
          this.currentStep++;
          break;
        case 1:
          this.currentStep++;
          break;
      }
    },
    onCreate() {
      this.fetchCreate().then(res => {
        this.changeDetail = res;
        this.$confirm({
          title: this.$t("change_continue_create"),
          okText: this.$t("change_add_program"),
          cancelText: this.$t("btn_done"),
          onOk: () => {
            this.onNextClick();
          },
          onCancel: () => {
            this.$router.go(-1);
          }
        });
      });
    },
    goBackList() {
      const containerInfo = this.ecoData.containerInfo[0] || {};
      Jw.jumpToDetail({
        ...containerInfo,
        tabActive: "change"
      });
    },
    goBackECR() {
      let row = Row_Store.get("ECR");
      Jw.jumpToDetail(row, {
        toUrl: "/ecr-details"
      });
    },
    goBack() {
      this.$router.go(-1);
    },
    onPreClick() {
      this.currentStep--;
    },
    onObjsChange(data) {
      const params = {
        deleteList: [],
        addList: [],
        ecoOid: this.changeOid,
        ...data
      };
      this.objLoading = true;
      updateEcoChangeInfoApi
        .execute(params)
        .then(() => {
          this.objLoading = false;
          this.fetchChangeObjs();
        })
        .catch(err => {
          this.objLoading = false;
          this.$error(err.msg || err);
        });
    }
  }
};
</script>

<style lang="less" scoped>
.create-container {
  display: flex;
  flex-direction: column;
  background-image: linear-gradient(179deg, #f0f7ff 0%, @white 30%);
}
.step-content {
  flex: 1;
  min-height: 1px;
  overflow: auto;
}
.setp-footer {
  padding: 14px 0;
  border-top: 1px solid @border-color-base;
  text-align: center;
}
.create-form {
  width: 944px;
  margin: 0 auto;
}
.change-obj,
.change-program {
  /deep/.left-panel,
  /deep/.right-panel {
    padding-top: 0;
  }
}
</style>