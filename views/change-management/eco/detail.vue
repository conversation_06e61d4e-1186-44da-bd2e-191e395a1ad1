<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-16 14:29:34
 * @LastEditTime: 2022-04-27 09:20:01
 * @LastEditors: <EMAIL>
-->
<template>
    <jw-page>
        <jw-toolbar class="jw-width" slot="header">
            <template slot="before-start">
                <span style="margin-right: 8px; color: #1890ff; cursor: pointer" @click="goBackList">
                    {{ fromRouteTitle || $t("txt_change_management") }}
                </span>
                &gt;
                <span style="margin-right: 8px; color: #1890ff; cursor: pointer" @click="goBackECR">
                    ECR
                </span>
                &gt;
                <jw-icon :type="lockIcon" v-if="lockIcon" />

                <div class="_mpm-page-title">
                    <jw-icon type="#jwi-biangengduixiang" />
                    <span>{{ ecoData.name }} (ECO)</span>
                </div>
                <a-tag color="blue">{{ ecoData.lifecycleStatus }}
                </a-tag>
            </template>
            <template slot="after-end">
                <PermissionDropdown style="margin-right: 30px" showType="btn" :permissionCode="viewCode" :callback="permissionFilter" :instanceData="ecoData" @click="onMenuClick" />
            </template>
        </jw-toolbar>
        <a-tabs class="jw-tabs" v-model.trim="tabActive" @change="onTabChange">
            <a-tab-pane class="jw-height" key="base" :tab="$t('txt_base_info')">
                <jw-flex-row :rigthWidth="418" :marginRight="0">
                    <jwLayoutBuilder class="base-form" ref="layout" layoutName="show" modelName="ECO" :instanceData="ecoData" slot="middle" />
                    <!-- <flow-status :list="flowList" slot="right" /> -->
                </jw-flex-row>
            </a-tab-pane>
            <a-tab-pane class="jw-height" key="obj" :tab="$t('change_obj')">
                <change-obj :changeDetail="ecoData" :changeObjs="changeObjs" ref="changeObj" />
            </a-tab-pane>
            <a-tab-pane class="jw-height" key="program" :tab="$t('change_program')">
                <change-program ref="changeProgram" type="ECO" :changeObjs="changeObjs" :changeDetail="ecoData" class="change-program" />
            </a-tab-pane>
            <a-tab-pane class="jw-height" key="relation" tab="ECA">
                <jw-table class="left-table" ref="table" style="padding:10px;" :showPage="false" :columns="columns" :fetch="fetchRelationData" :dataSource.sync="tableData">
                    <template #number="{ row }">
                        <div class="name-wrap">
                            <div class="name-con" @click="onOpenECADetail(row)">
                                <jwIcon :type="row.modelIcon"></jwIcon>
                                <span class="name-item">{{ row.number }}</span>
                            </div>
                        </div>
                    </template>
                </jw-table>
            </a-tab-pane>
            <!-- <a-tab-pane class="jw-height" key="history" tab="历史记录"></a-tab-pane> -->
        </a-tabs>
        <!-- 选择变更对象弹窗 -->
        <choose-obj-dialog ref="chooseObjDialog" />
    </jw-page>
</template>

<script>
import commonStore from "jw_stores/common";
import Row_Store from "jw_stores/instance-info";
import { findEcoChangeInfoApi, searchRelevantByECOApi } from "apis/change/eco";
import { findWithCrumbs } from "apis/change";
import { findDetail } from "apis/baseapi";
import { jwLayoutBuilder, jwToolbar, jwFlexRow } from "jw_frame";
import ChangeObj from "../components/eco-change-obj.vue";
import ChangeProgram from "../components/change-program.vue";
import FlowStatus from "../components/flow-status.vue";
import PermissionDropdown from "components/permission-dropdown";
import ChooseObjDialog from "../components/choose-obj-dialog.vue";
export default {
  name: "ecoDetail",
  components: {
    jwLayoutBuilder,
    jwFlexRow,
    jwToolbar,
    ChangeObj,
    ChangeProgram,
    FlowStatus,
    PermissionDropdown,
    ChooseObjDialog
  },
  data() {
    return {
      isIssue: false,
      ecInfo: "",
      fromRouteTitle: this.$t("txt_change_management"),
      lastRoute: "",
      changeOid: this.$route.query.oid,
      ecoData: {},
      changeObjs: [],
      tableData: [],
      tabActive: "base",
      flowList: [
        { status: "wait" },
        { status: "done" },
        { status: "error" },
        { status: "done" },
        { status: "done" },
        { status: "done" },
        { status: "done" },
        { status: "done" }
      ],
      viewCode: "ECOINSTANCEOPERATION",
      globalSearchVisible: false
    };
  },
  computed: {
    toolbars() {
      return [
        {
          display: "dropdown",
          position: "after",
          name: this.$t("txt_operation"),
          key: "more",
          type: "primary",
          prefixIcon: "jwi-iconmenu-application",
          menuList: [
            {
              name: this.$t("btn_edit"),
              key: "edit",
              click: this.goEdit,
              isVisible: true
            },
            {
              name: this.$t("txt_create_ECA"),
              key: "eca",
              click: this.createECA,
              isVisible: true
            }
          ]
        }
      ];
    },
    columns() {
      return [
        {
          field: "number",
          title: this.$t("txt_number"),
          slots: {
            default: "number"
          }
        },
        {
          field: "name",
          title: this.$t("txt_name")
        },
        {
          field: "type",
          title: this.$t("txt_type")
        },
        {
          field: "lifecycleStatus",
          title: this.$t("txt_plan_lifecycle")
        },
        {
          field: "updateBy",
          title: this.$t("txt_updateBy")
        },
        {
          field: "updateDate",
          title: this.$t("txt_update_time"),
          formatter: "date"
        }
      ];
    },
    isMeCreate() {
      const { createBy } = this.ecoData;
      if (!createBy) return false;
      const { account } = Jw.getUser();
      return account === createBy;
    },
    lockIcon() {
      return this.isMeCreate ? "#jwi-beiwojianchu" : "#jwi-bierenjianchu";
    }
  },
  inject: ["setBreadcrumb"],
  created() {
    this.setBreadcrumb([]);
    this.fetchBaseDetail();
    this.fetchChangeObjs();
    this.getEcInfo();
    let enterEcrInfo = JSON.parse(sessionStorage.getItem("enterEcrInfo"));
    if (enterEcrInfo.routeName == "object-details") {
      this.fromRouteTitle = this.$t("txt_change_record");
    }
  },
  methods: {
    goBackList() {
      // 从缓存获取跳转进来变更详情的信息
      let enterEcrInfo = JSON.parse(sessionStorage.getItem("enterEcrInfo"));
      let isFromIssue = localStorage.getItem("isFromIssue");
      let issueInfo = localStorage.getItem("issueInfo");
      if (isFromIssue) {
        localStorage.removeItem("isFromIssue");
        localStorage.removeItem("issueInfo");
        this.$router.push({
          name: "problem-detail",
          path: `/problem-detail`,
          query: {
            oid: issueInfo,
            tabActive: "list",
            issuDetailActive: "3"
          }
        });
      } else {
        console.log(enterEcrInfo);
        if (enterEcrInfo && enterEcrInfo.routeName == "object-details") {
          Jw.jumpToDetail(enterEcrInfo.query, {
            toUrl: "/object-details"
          });
          sessionStorage.setItem("currentTabName", "changeRecord");
          sessionStorage.removeItem("enterEcrInfo");
        } else {
          const containerInfo = this.ecoData.containerInfo[0] || {};
          this.$route.query.tabActive = "change";
          Jw.jumpToDetail({
            ...containerInfo,
            tabActive: "change"
          });
        }
      }
    },
    goBackECR() {
      let row = Row_Store.get("ECR");
      if (row) {
        Jw.jumpToDetail(row, {
          toUrl: "/ecr-details",
          blank: false
        });
      } else {
        Jw.jumpToDetail(ecInfo[2], {
          toUrl: "/ecr-details",
          blank: false
        });
      }
    },
    getEcInfo() {
      findWithCrumbs
        .execute({
          oid: this.$route.query.oid,
          type: this.$route.query.type
        })
        .then(res => {
          console.log("EC详细信息", res);
          this.ecInfo = res;

          // 从缓存读取是否是从问题详情的ECR跳转过来的
          let isFromIssue = localStorage.getItem("isFromIssue");
          if (isFromIssue) {
            this.isFromIssue = isFromIssue;
            this.fromRouteTitle = this.$t("txt_problem_management");
          }
        });
    },
    permissionFilter(btns) {
      let permissionBtns = btns.filter(btn => {
        return btn.code != "details";
      });
      return permissionBtns;
    },
    onMenuClick(item) {
      let key = item.code || item;
      if (key === "edit") {
        this.goEdit();
      } else if (key === "eca") {
        this.createECA();
      }
    },
    fetchBaseDetail() {
      findDetail.execute({ oid: this.changeOid, type: "ECO" }).then(res => {
        this.ecoData = res;
      });
    },
    fetchChangeObjs() {
      findEcoChangeInfoApi
        .execute({ oid: this.changeOid, list: [] })
        .then(res => {
          this.changeObjs = res;
        });
    },
    fetchRelationData() {
      let param = {
        oid: this.changeOid
      };
      return searchRelevantByECOApi
        .execute(param)
        .then(data => {
          return { data: data };
        })
        .catch(err => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    onTabChange() {},
    onOpenECADetail(row) {
      if (this.$route.name === "object-details") {
        sessionStorage.setItem("currentTabName", "changeRecord");
      }
      Jw.jumpToDetail(row, {
        toUrl: "/eca-details",
        viewCode: "ECAINSTANCE"
      });
    },
    goEdit() {
      const tabKeys = ["base", "obj", "program"];
      const step = tabKeys.indexOf(this.tabActive);
      this.$router.push(
        `/change-management/eco/edit/${this.changeOid}?step=${step}`
      );
    },
    createECA() {
      this.$refs.chooseObjDialog
        .show({
          changeDetail: this.ecoData
        })
        .then(({ selectedRows, isLast }) => {
          // 设置缓存 选中的变更对象 是否最后一次创建
          commonStore.set("createEcaData", {
            rows: selectedRows,
            isLast
          });
          this.$router.push(`/change-management/eca/create/${this.changeOid}`);
        });
    }
  }
};
</script>

<style lang="less" scoped>
.base-form {
  width: 75%;
  padding-top: 14px;
  margin: 0 auto;
}
.status-tag {
  height: 32px;
  margin-right: 0;
  line-height: 32px;
}
.name-wrap {
  display: flex;
  justify-content: space-between;
  .name-con {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    .name-item {
      color: #255ed7;
      cursor: pointer;
    }
  }
}
</style>