<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-04-20 15:08:52
 * @LastEditTime: 2022-04-26 13:46:08
 * @LastEditors: <EMAIL>
-->
<template>
  <jw-page>
    <template slot="header">
        <span style="margin-right:8px;color:#1890ff;cursor:pointer;"
            @click="goBack"
          >
          {{ fromRouteTitle || $t('txt_change_management')}}
          </span>
          &gt;
          <span style="margin-right:8px;color:#1890ff;cursor:pointer;"
            @click="goBack"
          >
            ECR
          </span>
          &gt;
      <div class="_mpm-page-title">{{$t('txt_edtor_CEO')}}</div>
    </template>
    <div class="create-container jw-height">
      <step-list :current="currentStep" :list="stepList" />
      <div class="step-content">
        <keep-alive>
          <jwLayoutBuilder class="create-form" ref="layout" layoutName="create" modelName="ECO" :instanceData="baseData" v-if="currentStep===0" />
          <change-obj :changeObjs="changeObjs" :changeDetail="ecoData"  @change="onObjsChange" class="change-obj" ref="changeObj" isEdit v-loading="objLoading" v-if="currentStep===1" @update="findEcoChangeInfo" />
        </keep-alive>
      </div>
      <div class="setp-footer">
        <a-button type="primary" @click="onPreClick" v-if="currentStep===1">{{$t('btn_pre_step')}} </a-button>
        <a-button type="primary" @click="onCreate"  :loading="btnLoading" v-if="currentStep===0">{{$t('btn_next_step')}}</a-button>
        <a-button type="primary" @click="goDetail" v-if="currentStep===1">{{$t('btn_done')}}</a-button>
        <a-button @click="goBack" v-if="currentStep<=1">{{$t('btn_cancel')}}</a-button>
      </div>
    </div>
  </jw-page>
</template>

<script>
import {jwLayoutBuilder} from "jw_frame";
import Row_Store from "jw_stores/instance-info";
import {findDetail} from 'apis/baseapi'
import {createEcoApi,findEcoChangeInfoApi,updateEcoChangeInfoApi} from 'apis/change/eco'
import ChangeObj from '../components/eco-change-obj.vue'
import StepList from '../components/step-list.vue'

export default {
  name:'changeCreate',
  components:{
    jwLayoutBuilder,
    ChangeObj,
    StepList,
  },
  data() {
    return {
      isFromIssue:false,
      fromRouteTitle:"",
      changeOid: this.$route.params.oid,
      stepList:[{
        name:this.$t('change_fill_info'),
        key:'base',
        com:''
      },{
        name:this.$t('change_confirm_program'),
        key:'object',
        com:''
      }],
      currentStep:0,
      libraryData: {},
      baseData:{},
      ecrData:{},
      ecoData:{},
      changeObjs:[],
      changeDetail:{},
      btnLoading:false,
      objLoading:false
    }
  },
    inject:[
        'setBreadcrumb',
    ],
  created () {
    this.setBreadcrumb([]);
    this.fetchBaseDetail();
    // 从缓存读取是否是从问题详情的ECR跳转过来的
    let isFromIssue = localStorage.getItem("isFromIssue");
    this.isFromIssue = isFromIssue;
    this.fromRouteTitle = this.$t('txt_problem_management');
  },
  methods: {
    fetchBaseDetail() {
      findDetail.execute({ oid: this.changeOid, type: 'ECR' }).then((res) => {
        this.baseData = {name:res.name}; // 默认名称取自来源ecr
        this.ecrData=res
      });
    },
    onCreate() {
      const layout=this.$refs.layout
      layout.validate()
        .then(() => {
          if (!this.ecoData.oid) {
            this.fetchCreate().then((res)=>{
              this.ecoData=res
              this.currentStep++
              this.findEcoChangeInfo()
            })
        } else 
          this.currentStep++
          this.findEcoChangeInfo()
        })
    },
    onObjsChange(data) {
      const params={
        deleteList:[],
        addList:[],
        ecoOid:this.ecoData.oid,
        ...data
      }
      this.objLoading=true
      updateEcoChangeInfoApi.execute(params)
        .then(()=>{
          this.objLoading=false
          this.findEcoChangeInfo()
        })
        .catch((err)=>{
          this.objLoading=false
          this.$error(err.msg||err)
        })
    },
    fetchCreate(){
      const {containerOid,containerType,catalogType,catalogOid}=this.ecrData
      this.baseData = this.$refs.layout.getValue()
      const params={
        ...this.baseData,
        ecrOid:this.changeOid,
        "locationInfo": {
          containerOid,containerType,catalogType,catalogOid
        }
      }
      this.btnLoading=true
      return createEcoApi.execute(params)
        .then(res=>{
          this.btnLoading=false
          return res
        })
        .catch(err=>{
          this.$error(err.msg)
          this.btnLoading=false
          return Promise.reject()
        })
    },
    findEcoChangeInfo(){
      findEcoChangeInfoApi.execute({oid:this.ecoData.oid,list:[]})
        .then(res=>{
          this.changeObjs=res;
        })
    },
    onPreClick() {
      this.currentStep--
    },
    goBackList () {
        const containerInfo = this.ecrData.containerInfo[0] || {};
        let isFromIssue = localStorage.getItem("isFromIssue");
        let issueInfo = localStorage.getItem("issueInfo");
        if(isFromIssue){
            localStorage.removeItem("isFromIssue");
            localStorage.removeItem("issueInfo");
            this.$router.push({
                name: "problem-detail",
                path: `/problem-detail`,
                query:{
                  oid: issueInfo,
                  tabActive: "list",
                  issuDetailActive:"3"
                }
            });
        } else {
          Jw.jumpToDetail({
            ...containerInfo,
            tabActive: 'change',
          });
        }
    },
    goBackECR() {
        let row = Row_Store.get('ECR');
        Jw.jumpToDetail(
          row,
          { 
            toUrl: '/ecr-details', 
          }
        );
    },
    goBack() {
      this.$router.go(-1)
    },
    goDetail(){
        Jw.jumpToDetail(
            this.ecoData,
            { 
                toUrl: '/eco-details',
                viewCode: 'ECOINSTANCE',
            }
        );
    },
  },
}
</script>

<style lang="less" scoped>
.create-container{
  display: flex;
  flex-direction: column;
  background-image: linear-gradient(179deg, #F0F7FF 0%, @white 30%);
}
.step-content{
  flex: 1;
  min-height: 1px;
  overflow: auto;
}
.setp-footer{
  padding: 14px 0;
  border-top: 1px solid @border-color-base;
  text-align: center;
}
.create-form{
  width: 944px;
  margin: 0 auto;
}
.change-obj,.change-program{
  /deep/.left-panel,/deep/.right-panel{
    padding-top: 0;
  }
}
</style>