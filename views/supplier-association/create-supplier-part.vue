<template>
  <a-modal v-if="visible" :visible="visible" :title="$t('txt_create_supplier_part')" width="67%" :mask-closable="false" :footer="null"
           @cancel="onCancel">
    <div>
      <jw-layout-builder ref="ref_appBuilder" type="Model" :layoutName="'create'" modelName="SupplierPart">
      </jw-layout-builder>
    </div>
    <div :style="{
					textAlign: 'center'
				}">
      <a-button type="primary" @click="onSave"> {{ $t('btn_save') }}</a-button>
      <a-button :style="{ marginRight: '8px' }" @click="onCancel">
        {{ $t('btn_cancel') }}
      </a-button>
    </div>
  </a-modal>
</template>

<script>
import {
  jwLayoutBuilder
} from "jw_frame";
import ModelFactory from "jw_apis/model-factory";

const createSupplierPart = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/supplierPart/create`,
  method: "post",
});

export default {
  name: "create-supplier-part",
  components: {
    jwLayoutBuilder,
  },
  props: ["visible", "partOid"],
  methods: {
    onCancel() {
      let {$refs: {ref_appBuilder}} = this;
      ref_appBuilder && ref_appBuilder.reset();
      this.$emit("close");
    },
    onSave() {
      let appBuilder = this.$refs.ref_appBuilder;
      console.log('appBuilder', appBuilder.getValue())
      console.log('this.partOid', this.partOid)
      appBuilder.validate().then(() => {
        let params = appBuilder.getValue();
       // params.partMasterOid = this.partOid
        createSupplierPart
            .execute({
              ...params,
              partMasterOid:this.partOid
            })
            .then((res) => {
              this.$success('添加成功！');
              this.onCancel();
              this.$emit('getTableData');
            })
            .catch((err) => {
              this.$error(err.msg || this.$t("txt_create_supplier_fail"));
            });
      });
    }
  }
}
</script>

<style scoped>

</style>