<template>
  <div class="all-background">
    <jw-table
        ref="ref_table"
        disableCheck="disableCheck"
        :data-source.sync="tableData"
        :columns="getHeader"
        :selectedRows.sync="selectedRows"
        :fetch="fetchTable"
        :toolbars="toolbars"
        :pagerConfig="pagerConfig"
        @onPageChange="onPageChange"
        @onSizeChange="onSizeChange"
        @onToolClick="onToolClick"
        @onToolInput="onToolInput"
        @checkbox-change="onSelectChange"
        @onOperateClick="onOperateClick"
    >
    </jw-table>
    <create-supplier-part
        :visible="visible"
        :partOid="this.objectDetailsData.masterOid"
        @close="onCloseModal"
        @getTableData="getAllTableData">
    </create-supplier-part>
    <a-drawer
        width="55%"
        :visible="visibleDraw"
        :body-style="{ paddingBottom: '80px' }"
        @close="onClose">
      <jw-layout-builder ref="ref_appBuilder" type="Model" :layoutName="showEdit ? 'update' : 'show'"
                         modelName="SupplierPart" :instanceData="currentRow">
      </jw-layout-builder>
      <div>
        <a-button v-if="showEdit" type="primary" style="margin-right: 12px; margin-left: 40%" @click="onEdit">
          {{ $t('btn_save') }}
        </a-button>
        <a-button v-if="showEdit" @click="onClose">{{ $t('btn_cancel') }}</a-button>
      </div>
    </a-drawer>
  </div>
</template>

<script>
import {getCookie} from "jw_utils/cookie";
import ModelFactory from "jw_apis/model-factory";
import createSupplierPart from "./create-supplier-part"
import {
  jwLayoutBuilder
} from "jw_frame";

const supplierPartTableModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/supplierPart/fuzzyPageByFrom`,
  method: "post",
});
const updateSupplierPart = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/supplierPart/update`,
  method: "post"
});


export default {
  props: ["objectDetailsData"],
  name: "index",
  components: {
    createSupplierPart,
    jwLayoutBuilder
  },
  data() {
    return {
      tableData: [],
      pagerConfig: {
        current: 1,
        pageSize: 20,
        total: 0
      }, //分页配置
      selectedRows: [],
      total: 0,
      selectRow: [],
      visible: false,
      visibleDraw: false,
      currentRow: {},
      showEdit: false,
      searchKey: "",
      pagination: {
        page: 1,
        size: 10,
        total: 30,
      },
      tableLoading: false,
    }
  },
  created() {
    console.log('this.objectDetailsData',this.objectDetailsData)
    this.delaySearch = _.debounce(this.onSearch, 500);
  },
  computed: {
    getHeader() {
      return [
        {
          field: "manufacturer",
          // title: this.$t("sys_full_name"),
          title: "厂商",
          sortable: true, // 开启排序
          slots: {
            // 插槽形式
            default: "",
          },
        },
        {
          field: "modelIdentification",
          title: "厂商型号标识",
        },
        {
          field: "manufacturerModel",
          title: "厂商型号",
        },
        // {
        //   field: "createDate",
        //   title: this.$t("sys_create_Date"),
        //   sortable: true, // 开启排序
        //   formatter: 'date',
        // },
        {
          // 操作列定义
          field: "operation", //关键字
          title: this.$t('txt_operation'),
          btns: [
            {
              icon: "jwi-iconedit", // 图标显示
              title: this.$t('btn_edit'),
              key: "edit",
            },
            {
              icon: "jwi-iconinfo-circle", // 图标显示
              title: this.$t('detailed_info'),
              key: "detail",
            },
            {
              icon: "jwi-icondelete", // 图标显示
              title: this.$t('btn_delete'),
              key: "delete",
            }
          ],
        },
      ];
    },
    toolbars() {
      return [
        {
          name: this.$t("btn_new_add"),
          position: "before",
          type: "primary",
          key: "create",
          // prefixIcon: "jwi-plus",
        },
        {
          name: this.$t("btn_delete"),
          position: "before",
          type: "primary",
          key: "delete",
          // prefixIcon: "jwi-plus",
        },
        {
          name: this.$t("btn_search"),
          position: "before",
          display: "input",
          value: this.searchKey,
          allowClear: false,
          placeholder: this.$t("txt_search"),
          suffixIcon: 'jwi-iconsearch',
          key: "search",
        },
      ];
    },
  },
  methods: {
    onCloseModal() {
      this.visible = false;
    },
    getAllTableData() {
      this.fetchTable()
    },
    fetchTable() {
      let { current, pageSize } = this.pagerConfig;
      let param = {
        searchKey: this.searchKey.trim(),//关键字（name/number）
       // containerOid: getCookie("tenantOid"),
        // pageNum: current,
        // pageSize: pageSize,
        fromOid: this.objectDetailsData.masterOid,
        fromType:this.objectDetailsData.masterType,
        relationType:"CONTAIN",//部门的oid
        index: current,
        size: pageSize
      };
      this.tableLoading = true;
      return supplierPartTableModel
          .execute(param)
          .then((data) => {
            // console.log(data);
            this.tableLoading = false;
            this.tableData = data.rows;
            this.pagerConfig.total = data.count;
            this.total = data.count;
            return {data: data.rows, total: data.length};
          })
          .catch((err) => {
            this.tableLoading = false;
            this.$error(err.msg || this.$t("msg_failed"));
          });
    },
    onPageChange(page, pageSize) {
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;
      this.fetchTable();
    },
    onSizeChange(pageSize, page) {
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;
      this.fetchTable();
    },
    onToolClick({key, row}) {
      if (key === "create") {
        this.visible = true;
      } else if (key === "detail") {
        this.visibleDraw = true;
      } else if (key === "edit") {
        this.visibleDraw = true;
      } else if (key === "delete") {
        console.log("this.selectedRows", this.selectedRows);
        this.fetchDelete(this.selectedRows);
      }
    },
    onSearch() {
      this.$refs.ref_table.reFetchData();
    },
    // 工具栏输入回调
    onToolInput({key}, value) {
      if (key === "search") {
        this.searchKey = value;
        this.pagerConfig = {
          current: 1,
          pageSize: 20,
          total: 0
        };
        this.delaySearch();
      }
    },
    onSelectChange(args) {
      // console.log(args);
      this.selectRow = args;
    },
    // 操作列回调
    onOperateClick(key, row) {
      if (key === "detail") {
        this.visibleDraw = true
        this.showEdit = false
        this.currentRow = row
        console.log('1111', 1111)
      } else if (key === "edit") {
        this.visibleDraw = true
        this.showEdit = true
        this.currentRow = row
      } else if (key === "delete") {
        // console.log(row);
        this.onDelete(row);
      }
    },
    onClose() {
      this.visibleDraw = false
      this.showEdit = false
    },
    onDelete(row) {
      this.fetchDelete([row]);
    },
    fetchDelete(row) {
      // console.log("当前删除操作的数据", row);
      if (! row.length)
        return this.$warning(this.$t("txt_please_seleted_data"));
      let {tableData} = this;
      let oids = row.map((item) => item.oid);
      let param = oids
      this.$confirm({
        width: "280px",
        class: "deleteModal",
        closable: true,
        mask: false,
        title: (
            <p style="font-size:16px;font-weight:500;color:rgba(30, 32, 42, 0.85);">
              {this.$t("txt_delete")}
            </p>
        ),
        content: (
            <p style="font-size:14px;font-weight:400;color:rgba(30, 32, 42, 0.45);padding-right: 10px;">
              {tableData.length === 1 ? this.$t("txt_delete_supplier_part") : this.$t("txt_delete_supplier_part")}
            </p>
        ),
        cancelText: this.$t("btn_cancel"),
        okText: this.$t("btn_confirm"),
        onOk: () => {
          ModelFactory.create({
            url: `${Jw.gateway}/${Jw.partBomMicroServer}/supplierPart/batchDeleteByOid`,
            method: "post",
          })
              .execute(param)
              .then((data) => {
                // console.log(data);
                this.$success(this.$t("msg_success"));
                this.selectedRows = [];
                //刷新列表
                this.fetchTable();
              })
              .catch((err) => {
                this.$error(err.msg || this.$t("msg_failed"));
              });
        },
      });
    },
    onEdit() {
      let appBuilder = this.$refs.ref_appBuilder;
      console.log('appBuilder', appBuilder.getValue())
      appBuilder.validate().then(() => {
        let params = appBuilder.getValue();
        // 创建基线
        updateSupplierPart
            .execute({
              ...params,
              partMasterOid: this.$route.params.oid
            })
            .then((res) => {
              this.visibleDraw = false
              this.fetchTable({});
            })
            .catch((err) => {
              this.$error(err.msg || this.$t("txt_create_supplier_fail"));
            });
      });
    }
  }
}
</script>

<style scoped>

</style>