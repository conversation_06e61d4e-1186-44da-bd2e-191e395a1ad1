<template>
  <div style="min-height:100%; background:white;">
    <a-tabs :default-active-key="activeKey" @change="callback">
      <a-tab-pane key="1" tab="内容管理">
        <tree />
      </a-tab-pane>



      <a-tab-pane key="2" tab="产品结构" force-render>
        产品结构
      </a-tab-pane>



      <a-tab-pane key="3" tab="变更管理">
        变更管理
      </a-tab-pane>

      <a-tab-pane key="4" tab="基线管理">
        基线管理
      </a-tab-pane>
    </a-tabs>


    


  </div>
</template>
<script>
import tree from './tree.vue'
export default {
  components:{
    tree,
  },
  data() {
    return {
      activeKey:'1'
    };
  },
  methods: {
    callback(key) {
    },
  },
  created(){
    console.log("this.$route: ",this.$route);
    this.activeKey = this.$route.query.type || '1';
  },
};
</script>