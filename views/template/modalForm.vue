<template>
  <!-- 添加分组 -->
  <a-modal
    v-model.trim="visible2"
    :title="title"
    :cancelText="cancelText"
    :okText="confirmText"
    @ok="handleOk"
    @cancel.stop="handleCancel"
  >
    <template slot="footer">
      <a-button key="submit" type="primary" @click="handleOk">
        {{confirmText}}
      </a-button>

      <a-button key="back" @click="handleCancel">
        {{cancelText}}
      </a-button>
    </template>


    <a-form-model
      ref="ruleForm"
      :model="formDatas2"
      @submit.native.prevent
    >
      <a-form-model-item prop="name" label="显示名称" required :colon="false" >
         <a-input v-model.trim="formDatas2.name" placeholder="请输入" />
      </a-form-model-item>

      <a-form-model-item prop="description" label="描述" :colon="false">
         <a-textarea v-model.trim="formDatas2.description" placeholder="请输入" />
      </a-form-model-item>

      <a-form-model-item prop="disabled" label="启用" :colon="false">
         <a-switch v-model.trim="formDatas2.disabled" placeholder="请输入" />
      </a-form-model-item>
      
      <a-form-model-item prop="file" label="附件"  :colon="false">
         <jwUploadFile v-model.trim="formDatas2.file" :accept="'.json'" />
      </a-form-model-item>

    </a-form-model>
  </a-modal>
</template>

<script>
import uploadSingle from './upload-single'
import {jwUploadFile} from 'jw_frame'

export default {
  components:{
    uploadSingle,
    jwUploadFile,
  },
  data() {
    return {
      visible2: this.visible,
      formDatas2: JSON.parse(JSON.stringify(this.formDatas)),
    };
  },
  props: {
    visible: { type: Boolean, default: false },
    title: { type: String, default: "标题" },
    formDatas: { type: Object, default: () => ({}) },

    cancelText:{  type:String, default:'取消'  },
    confirmText:{  type:String, default:'确认'  },
  },

  watch: {
    visible2(newVal, oldVal) {
      this.$emit("update:visible", newVal);
    },
  },
  methods: {
    showModal() {
      this.visible2 = true;
    },
    handleOk(e) {
      this.onSubmit();
    },
    handleCancel(e) {
      this.resetForm();
      console.log('取消事件---');
      this.visible2 = false;
      this.$emit('cancel');
    },
    

    onSubmit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit("submit", this.formDatas2);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },
  },
};
</script>