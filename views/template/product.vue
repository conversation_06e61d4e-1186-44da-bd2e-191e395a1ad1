<template>
  <div style="padding:0px 20px; height:100%; background:white;">
    <!-- dataSource:{{dataSource}} -->
    <jwFixedContent :top="'60px'" :height="'40px'" :wrapStyle="'padding:0px 20px;'">
      <div style="display:flex; justify-content:space-between; align-items:center;">
        <span>
          <a-button @click="addVisible=true" type="primary" style="margin-right:10px;">新增</a-button>
          <a-input-search allowClear placeholder="请输入关键词" style="width: 200px" @search="search" />
        </span>

        <span>
          <a-button><span class="jwi-iconfilter"></span></a-button>
          <a-button>导入</a-button>
          <a-button>导出</a-button>
        </span>
      </div>
    </jwFixedContent>


    <div style="height:calc(100vh - 100px);">
      <jw-table
        :data-source="dataSource"
        :columns="columns"
        height="content"
        style="height:100%; margin-top:10px;"
        :showPage="false"
        :selectedRows.sync="selectedRows"
      >
        <template #tenantOid="{row:record}">
          <span v-if="record.tenantOid">
            <jw-icon type="daima"/>
            <a>{{record.tenantOid}}</a>
          </span>
        </template>

        <template #disabled="{row:record}">
          <a-switch v-model.trim="record.disabled" @change="handleChange(record)">
            <a-icon slot="checkedChildren" type="check" />
            <a-icon slot="unCheckedChildren" type="close" />
          </a-switch>
        </template>

        <template #file="{row:record}">
          <a v-show="record.file.filePath" :href="record.file.filePath">
            <jwIcon type="#jwi-daima"></jwIcon>
            {{record.file.name || ''}}
          </a>
        </template>

        <template #action="{row:record}">
          <jwDeleteConfirm @confirm="deleteItem(record)">
            <span class="jwi-icondelete" style="padding:0px 10px;"></span>
          </jwDeleteConfirm>
        </template>
      </jw-table>
    </div>

    <modalForm 
       :key="'1'"
        v-if="addVisible"
        :visible.sync="addVisible"
        :title="'新增'"
        :formDatas="addParams"
        @submit="addItem"
    />

  </div>
</template>


<script>
import rules from '../../utils/rules.js'
import {getList,deleteItem,updateItem,addItem} from '../../apis/template/index.js'
import {jwTable,jwModalForm,jwSimpleSelect,jwIcon,jwFixedContent,jwDeleteConfirm} from 'jw_frame'
import modalForm  from './modalForm.vue'

export default {
  components:{
    jwTable,
    jwModalForm,
    jwSimpleSelect,
    jwIcon,
    modalForm,
    jwFixedContent,
    jwDeleteConfirm,
  },
  data() {
    return {
        rules,
        // query:{},      
        
        dataSource: [
        // {  id:1, name: "xxx", address: "xxxxx" },
        ],
        columns: [
            // { title: "唯一标识符", field: "number", key: "number" },
            { title: "显示名称", field: "name", key: "name" },
            { title: this.$t('txt_description'), field: "description", key: "description" },
            { title: "附件", field: "file", key: "file", slots: { default: "file" }, align:'center' },
            { title: "启用", field: "disabled", key: "disabled", slots: { default: "disabled" }, align:'center' },
            { title: "操作", field: "action", key: "action", slots: { default: "action" }, align:'center' },
        ],
        loading:false,
        selectedRows:[],
        
        addVisible:false,  // 新增弹窗控制
        addParams:{
          // number:'',
          name: '',
          description:'',
          disabled:true,
          category: "product",
          file:{},
        },

        updateVisible:false,  // 编辑弹窗控制
        updateParams:{},  // 编辑弹窗的数据
        
        deleteLoading:false,
        // index: 0, // 编辑表单第一条记录
    };
  },
  methods: {
    getList(params={},keyword=''){
        console.log("获取数据的操作",params);
        this.loading = true;

        getList().execute({
            category: "product",
            searchKey: keyword,
            index: 1,
            size: 100000,   // 10
        })
        .then(res=>{
            console.log("getList res: ",res);
            this.dataSource = res.rows.reverse();
            // console.log("this.dataSource: ",this.dataSource);
        }).catch(error=>{
            console.log("error: ",error);
        }).finally(()=>{
            this.loading = false;
        });
    },
    showList(params) {
        this.getList(params)
    },
    // 搜索：过滤
    search(value) {
        // console.log(value);
        console.log('过滤操作',value)
        this.getList({},value);
    },
    deleteItem(params){
        deleteItem(params).execute(params)
        .then(res=>{
            console.log("res: ",res);
            this.getList();
            this.$success('删除成功');
        }).catch(error=>{
            this.$error('删除失败');
        });
    },
    // 点击编辑按钮
    clickupdateBtn(record){
      console.log("record: ",record);
      this.updateParams =record;
      this.updateVisible=true
    },
    updateItem(params){
        console.log("编辑操作",params);

        updateItem().execute(params)
        .then(res=>{
            console.log("res: ",res);
            this.$success('修改成功');
            this.updateVisible = false;
            this.getList();
        }).catch(error=>{
        //   console.log("error: ",error);
          this.$error('修改失败');
        });      
    },
    addItem(params){
        console.log("新增操作",params);
      
        addItem().execute({
          ...params,
        })
        .then(res=>{
            console.log("res: ",res);
            this.$success('新增成功');
            this.addVisible = false;
            this.getList();
        }).catch(error=>{
            //  console.log("error: ",error);
             this.$error(error.msg==='Name exists' ? '已经存在' : '新增失败');
        });
    },


    handleChange(params){
      console.log("params: ",params);
      this.updateItem(params);
    },
  },
  created(){
    // this.query = this.$route.query;
    this.showList();
  },
};
</script>