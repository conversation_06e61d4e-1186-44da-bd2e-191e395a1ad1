<template>
  <div style="position:relative; height:100%; background:white;">
    <a-tabs :default-active-key="activeKey" @change="callback">
      <a-tab-pane key="1" tab="产品容器模板">
        <div  style="height:calc(100% - 64px - 45px - 50px); overflow:auto;">
          <product />
        </div>
      </a-tab-pane>



      <a-tab-pane key="2" tab="资源容器模板">
        <div  style="height:calc(100vh - 64px - 45px - 50px); overflow:auto;">
          <resource />
        </div>
      </a-tab-pane>



      <a-tab-pane key="3" tab="文档模板">
        <div  style="height:calc(100vh - 64px - 45px - 50px); overflow:auto;">
          文档模板...
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script>

import product from './product.vue'
import resource from './resource.vue'
export default {
  components:{
    product,
    resource,
  },
  data() {
    return {
      activeKey:'1'
    };
  },
  methods: {
    callback(key) {
    },
  },
  created(){
    console.log("this.$route: ",this.$route);
    this.activeKey = this.$route.query.type || '1';
  },
};
</script>

<style lang="less" scoped>
/deep/ .ant-tabs-nav .ant-tabs-tab-active {
  color: rgba(30, 32, 42, 0.65)!important;
  font-weight: 500;
}
</style>