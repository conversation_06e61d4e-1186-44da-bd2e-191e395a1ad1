<template>
  <div style="position: relative; height: 100%; background: white">
    <div style="width: 600px; height: 80px">
      <!-- <div v-if="file">
        <div  v-for="(v,k) in file" :key="k">{{k}} : {{v}}<br/><br/></div>
      </div> -->
      上传文件，绑定json数据，只能上传1个
      <uploadFile v-model.trim="file" :accept="'.jpg,.png,.gif,.mp4,.txt,.doc,.docx'" />
      <!-- <div v-if="file2">
        <div  v-for="(v,k) in file2" :key="k">{{k}} : {{v}}<br/><br/></div>
      </div> -->
      上传图片，绑定json数据，能上传多个
      <uploadFile v-model.trim="file2" :accept="'.jpg,.png,.gif,.mp4,.txt,.doc,.docx'" :multiple="true" />




      上传图片，绑定json数据，只能上传1张图片
      <uploadImage v-model.trim="file" :accept="'.jpg,.png,.gif,.mp4,.txt,.doc,.docx'" />
      上传图片，绑定json数据，能上传多个
      <uploadImage v-model.trim="file2" :accept="'.jpg,.png,.gif,.mp4,.txt,.doc,.docx'" :multiple="true" />

      
      
    </div>
  </div>
</template>
<script>
import uploadFile from "../../components/uploadFile/index.vue";
import uploadImage from "../../components/uploadImage/index.vue";
export default {
  components: {
    uploadFile,
    uploadImage,
  },
  data(){
    return {
      // file:'',
      file:{
        oid: 'f21603de-d0bb-4908-9b86-5aff893beb1a',
        type: null,
        detailType: null,
        markForDelete: false,
        createBy: 'admin',
        createDate: 1647402866279,
        updateBy: 'admin',
        updateDate: 1647402866279,
        tenantOid: '',
        fileOriginalName: '',
        fileName: '1647402867796_my.png',
        filePath: 'http://minio.dev.jwis.cn/mybucket/1647402867796_my.png',
        fileSize: 7086,
        fileSuffix: '',
        bucketName: 'mybucket',
        uid: 'f21603de-d0bb-4908-9b86-5aff893beb1a',
        name: 'my.png',
        url: 'http://minio.dev.jwis.cn/mybucket/1647402867796_my.png',
      },


      // file2:'',
      file2:[
        {
          oid: 'f21603de-d0bb-4908-9b86-5aff893beb1a',
          type: null,
          detailType: null,
          markForDelete: false,
          createBy: 'admin',
          createDate: 1647402866279,
          updateBy: 'admin',
          updateDate: 1647402866279,
          tenantOid: '',
          fileOriginalName: '',
          fileName: '1647402867796_my.png',
          filePath: 'http://minio.dev.jwis.cn/mybucket/1647402867796_my.png',
          fileSize: 7086,
          fileSuffix: '',
          bucketName: 'mybucket',
          uid: 'f21603de-d0bb-4908-9b86-5aff893beb1a',
          name: 'my.png',
          url: 'http://minio.dev.jwis.cn/mybucket/1647402867796_my.png',
        }
      ],
    }
  },
};
</script>