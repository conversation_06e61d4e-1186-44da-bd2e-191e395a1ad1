<template>
  <a-upload-dragger
    :action="url"
    :headers="header"
    :multiple="false"
    :file-list="fileList"
    @change="handleChange"
  >
    <p class="ant-upload-text">
      <a-icon type="upload" />上传  <br/>
      点击或者拖拽到这个区域进行上传
    </p>
    <p class="ant-upload-hint">
      只能同时上传1个文件
    </p>
  </a-upload-dragger>
</template>

<script>
export default {
  props:{
    value:{  type:Object, default:()=>({})  },
  },
  data() {
    const getCookie = function(name) {
      let arr,
        reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
      if ((arr = document.cookie.match(reg))) {
        return unescape(arr[2]);
      } else {
        return null;
      }
    };

    return {
      url: `${Jw.gateway}/${Jw.fileServer}/file/upload`,
      header: {
        accesstoken: getCookie('accesstoken'),
        appName: Jw.appName,
        tenantAlias: getCookie("tenantAlias"),
        tenantOid: getCookie("tenantOid"),
      },
      fileList: [
        // {
        //   appId: null
        //   checkSum: "BkBDZ9sMHHECATzLJ7urJuHfwVxyKIXVfkY8ypN1ghs="
        //   createdTime: 1646820067488
        //   creatorId: null
        //   creatorName: "管理员"
        //   fileName: "featureManageExport.xls"
        //   filePath: "group1/M00/06/A9/wKgCEGIoeaeAWxKCAABUAKtTYdg593.xls"
        //   fileSize: 21504
        //   modelType: null
        //   name: null
        //   oid: "daa1903eaec843f9a329cab3c80cb43b"
        //   suffix: "xls"
        //   tenantId: "administrator"
        //   updatedTime: 1646820067488
        //   updatorId: null
        //   updatorName: "管理员"
        //   withDoc: false
        // },
      ],
    };
  },

  methods: {
    beforeAvatarUpload(file) {
      const isLt1G = file.size / 1024 / 1024 / 1024 < 1;
      if(!isLt1G) {
        this.$error("上传文件大小不能超过 1GB")
      }
      return isLt1G
    },
    onPreview(file) {
      window.open(`${Jw.gateway}/${Jw.fileServer}/file/downloadByOid?oid=${file.response.data.oid}`)
    },
    handleChange(info) {
      // console.log("info111: ",info);

      let fileList = [...info.fileList];
      // fileList = fileList.slice(-2);

      fileList = fileList.map(file => {
        if (file.response) {
          file.url = file.response.url;
        }
        return file;
      });

      this.fileList = fileList;
      // console.log("this.fileList[0].response: ",this.fileList[0].response);

      if(  this.fileList.length>0 && this.fileList[0].response && this.fileList[0].response.code===0  ){
        this.$emit('input',this.fileList[0].response.result || {});
        console.log("value: ",this.fileList[0].response.result || {});
      }
    },
  }
};
</script>


