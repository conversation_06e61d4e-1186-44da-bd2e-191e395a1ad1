<template>
  <div class="menuDatas">
    <div>
      <div v-for="(item, index) in dataList" :key="index">
        <div
            @click="getRouerLink(item.path)"
            class="menuTitle"
            style="display: flex; align-items: center; padding: 10px 0"
        >
          <div
              style="
              display: inline-block;
              width: 3.25px;
              height: 20px;
              background: #255ed7;
              border-radius: 2.5px;
              margin-right: 10px;
            "
          ></div>
          <span style="display: inline-block; width: 230px; font-size: 14px"
          >{{ item.title }}
          </span>
          <div
              style="
              width: 100%;
              display: flex;
              align-items: center;
              justify-content: space-between;
            "
          >
            <span style="font-size: 12px; color: rgba(30, 32, 42, 0.45)"
            >{{ item.content }}
            </span>
            <!-- <a-icon type="right" /> -->
          </div>
        </div>
        <div
            v-if="item.type"
            style="
            width: 100%;
            border-top: 1px solid #ccc;
            height: 1px;
            margin: 10px 0;
          "
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import { jwToolbar, jwPage } from "jw_frame"

export default {
  components: {
    jwToolbar,
    jwPage,
  },
  inject: ["setBreadcrumb"],
  data() {
    const datas = [
      {
        title: this.$t("page_encoding_rules"),
        content: this.$t("txt_create_rules"),
        path: "/business-configuration/code?type=1",
      },
      {
        title: this.$t("page_version_control_rules"),
        content: this.$t("msg_create_viersion"),
        path: "/business-configuration/version?type=1",
      },
      {
        title: this.$t("txt_lifecycle"),
        content: this.$t("msg_create_lifecly"),
        path: "/lifecycle?type=1",
      },
      {
        title: this.$t("txt_work"),
        content: this.$t("msg_create_worlk_life"),
        path: "/business-configuration/workflow?type=1",
      },
      {
        title: this.$t("page_model_mannange"),
        content: this.$t("msg_create_model_attr"),
        path: "/module-manage/diagram",
      },
      // {
      // 	title: "权限清单",
      // 	content: "创建和管理系统action",
      // 	path: "/permission-listing?type=1",
      // },
      // {
      // 	title: "权限策略设置",
      // 	content: "创建和管理域和访问控制的策略",
      // 	type: "true",
      // 	path: "/permission-listing?type=2",
      // },
      {
        title: this.$t("page_objiect_rules"),
        content: this.$t("msg_can_configure"),
        path: "/object-collection-rules?type=1",
      },
      // {
      //     title: "变更流程设置",
      //     content: "设置用户在指定默认情况下，变更流程任务的配置",
      //     type: "true",
      // },
      // {
      //     title: "CAD创建编码获取规则",
      //     content: "编码规则备注说明文字",
      //     type: "true",
      // },
      {
        title: this.$t("txt_unit"),
        content: this.$t("msg_create_unit_va"),
        path: "/site-unit-management?type=1",
      },
    ]
    const list = []
    console.log("脚手架 Jw.systemUniqueName", Jw.systemUniqueName)

    // if (Jw.appName === 'pdm')
    const title = this.$t("txt_system_config_title")

    datas.push({
      title,
      content: title,
      path: "/sysconfig-manage",
    })

    if (Jw.systemUniqueName != "xingwang")
      datas.push({
        title: this.$t("msg_mcad_rules"),
        content: this.$t("msg_cad_part"),
        path: "/mcad-attribute-mapping",
      })

    if (Jw.appName == "mpm") {
      datas.push({
        title: this.$t("视图管理"),
        content: this.$t("txt_view_explain"),
        path: "/view-management",
      })
      datas.push({
        title: "表单配置管理",
        content: "创建管理工艺业务模型对象",
        path: "/model-adapter",
      })
      if (!Jw.dmApp) {
        list.push({
          title: this.$t("构型管理"),
          content: this.$t("构型管理，业务模式配置"),
          path: "/configuration-management",
        })
      }
    }
    if (Jw.appName == "ppm") {
      datas.push({
        title: this.$t("视图管理"),
        content: this.$t("txt_view_explain"),
        path: "/view-management",
      })
      datas.push({
        title: "表单配置管理",
        content: "创建管理工艺业务模型对象",
        path: "/model-adapter",
      })
    }
    if (Jw.appName === "pdm") {
      datas.push({
        title: this.$t("txt_cad_monitor"),
        content: this.$t("txt_cad_monitor"),
        path: "/conversion-monitor",
      })
      datas.push({
        title: this.$t("txt_data_migration"),
        content: this.$t("txt_data_migration"),
        path: "/setl",
      })
      datas.push({
        title: this.$t("txt_attribule_value_syn"),
        content: this.$t("txt_attribule_value_syn"),
        path: "/attribute-value-synthesis?type=1",
      })
    }

    return {
      datas,
      //组织
      list: [
        {
          title: this.$t("page_encoding_rules"),
          content: this.$t("txt_create_rules"),
          path: "/business-configuration/code?type=2",
        },
        {
          title: this.$t("page_version_control_rules"),
          content: this.$t("msg_create_viersion"),
          path: "/business-configuration/version?type=2",
        },
        {
          title: this.$t("txt_lifecycle"),
          content: this.$t("msg_create_lifecly"),
          path: "/lifecycle?type=2",
        },
        {
          title: this.$t("txt_work"),
          content: this.$t("msg_create_worlk_life"),
          path: "/business-configuration/workflow?type=2",
        },
        {
          title: this.$t("page_model_mannange"),
          content: this.$t("msg_create_model_attr"),
          path: "/module-manage/diagram",
        },
        // {
        // 	title: "权限清单",
        // 	content: "创建和管理系统action",
        // 	path: "/permission-listing?type=1",
        // },
        // {
        // 	title: "权限策略设置",
        // 	content: "创建和管理域和访问控制的策略",
        // 	type: "true",
        // 	path: "/permission-listing?type=2",
        // },
        {
          title: this.$t("page_objiect_rules"),
          content: this.$t("msg_can_configure"),
          path: "/object-collection-rules?type=2",
        },
        // {
        //     title: "变更流程设置",
        //     content: "设置用户在指定默认情况下，变更流程任务的配置",
        //     type: "true",
        // },
        // {
        //     title: "CAD创建编码获取规则",
        //     content: "编码规则备注说明文字",
        //     type: "true",
        // },
        {
          title: this.$t("txt_unit"),
          content: this.$t("msg_create_unit_va"),
          path: "/site-unit-management?type=2",
        },
        {
          title: this.$t("txt_integrated_task"),
          content: this.$t("txt_integrated_task"),
          path: "/integrated-task?type=2",
        },
        // {
        //     title: "工作流进程管理",
        //     content:
        //         "搜索工作流进程并查看活动和任务级详细信息。重新启动已停止的工作流，终止或删除工作流。",
        // },
        ...list,
      ],
      dataList: [],
    }
  },
  watch: {
    $route: {
      deep: true,
      immediate: true,
      handler(val) {
        this.getrou();
        this.setBreadcrumb([
          {
            name: this.$t("txt_busin_config"),
            path: "/site-business-configuration?type=1",
          },
        ])
      },
    },
  },
  created() {
    // this.setBreadcrumb([
    //   {
    //     name: this.$t("txt_busin_config"),
    //     path: "/site-business-configuration?type=1",
    //   },
    // ])
    let a = this.$route.query.type || 1
    this.dataList = a == 2 ? this.list : this.datas
  },
  methods: {
    getrou() {
      let a = this.$route.query.type || 1
      this.dataList = a == 2 ? this.list : this.datas
    },
    getRouerLink(url) {
      let query = {}
      if (url) {
        //模型管理
        if (url === "/module-manage/diagram") {
          if (this.$route.query.type === "1") {
            query = {
              jurisdiction: "write",
              type:"1"
            }
          } else {
            query = {
              jurisdiction: "read",
              type:"2"
            }
          }
        }
        this.$router.push({ path: url, query })
      }
    },
  },
}
</script>

<style scoped>
.menuDatas {
  background: #fff;
  padding: 10px 20px;
  /* border-radius: 5px;
	box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25); */
  height: 100%;
  z-index: 999;
}
.menuTitle {
  color: #333;
}
.menuTitle:hover {
  background: #acaeaf4b;
  cursor: pointer;
  /* color: #fff; */
}
</style>
