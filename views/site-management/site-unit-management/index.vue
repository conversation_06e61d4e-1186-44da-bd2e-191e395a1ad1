<template>
  <div class="all-background">
    <import-modal
      :visible="visibleExport"
      @close="visibleExport = false"
      @getList="getUnitManagementList"
    ></import-modal>
    <jw-table
      :columns="columns"
      :toolbars="toolbars"
      :data-source.sync="dataSource"
      @onPageChange="onPageChange"
      :pagerConfig="pagerConfig"
      @onSizeChange="onSizeChange"
      @onOperateClick="onOperateClick"
      @onToolClick="onToolClick"
      @checkbox-chang="onSelectChange"
      :selectedRows.sync="selectedRows"
    >
      <template slot="tool-after-end">
        <a-tooltip placement="top">
          <template #title>
            <span id="importPart">{{ $t("btn_import") }}</span>
          </template>
          <a-button @click="visibleExport = true">
            <jw-icon type="jwi-iconImport" />
          </a-button>
        </a-tooltip>
        <a-tooltip placement="top">
          <template #title>
            <span>{{ $t("btn_export") }}</span>
          </template>
          <a-button :loading="exportLoading" @click="onExport">
            <jw-icon type="jwi-iconexport" />
          </a-button>
        </a-tooltip>
      </template>

      <template #containerModelType="{ row }">
        {{
          row.containerModelType == "Site" ? $t("txe_site") : $t("txe_tenant")
        }}
      </template>
      <template #Tabletitle="{ row }">
        <span
          @click="onOperateClick('editor', row, true)"
          style="color: #255ed7; cursor: pointer"
          >{{ row.name }}</span
        >
      </template>
      <template #creatdTime="{ row }">
        <span style="color: #666">{{
          formatDate(row.createDate || row.updateDate)
        }}</span>
      </template>
      <template #TableCreateUser="{ row }">
        <jw-avatar
          tag
          show-name
          color="#333"
          :data="{ name: row.createBy, src: '' }"
        />
      </template>
      <template #TableSystemDefault="{ row }">
        <a-tag v-if="index % 2 == 0">{{ $t("txt_seystem_secr") }}</a-tag>
        <span v-else style="color: #255ed7; cursor: pointer"
          >{{ $t("txt_set_system") }}
        </span>
      </template>
      <template #markForDelete="{ row }">
        <a-switch
          :checked="row.disabled == 0"
          :disabled="disabled"
          @change="swichChange(row, row.disabled)"
        >
          <a-icon slot="checkedChildren" type="check" />
          <a-icon slot="unCheckedChildren" type="close" />
        </a-switch>
      </template>
    </jw-table>
    <jw-modal-form
      :key="'1'"
      ref="ref_modal_form"
      v-if="addVisible"
      :visible.sync="addVisible"
      :title="title"
      :formDatas="JSON.parse(JSON.stringify(addParams))"
      :keys="formModalData"
      @submit="addItem"
    />
    <!-- 抽屉表单 -->
    <a-drawer
      :title="$t('txt_uniti_name')"
      width="55%"
      :visible="visible"
      :body-style="{ paddingBottom: '80px' }"
      @close="onClose"
    >
      <a-form-model :model="addParams" layout="vertical" hide-required-mark>
        <a-row :gutter="16">
          <!-- <a-col :span="16">
						<a-form-model-item
							label="系统默认(系统默认单位设置后无法取消，默认只能开启一个。)"
						>
							<a-switch :disabled="addParams.hasSecurity" v-model:checked="addParams.hasSecurity">
								<a-icon slot="checkedChildren" type="check" />
								<a-icon slot="unCheckedChildren" type="close" />
							</a-switch>
						</a-form-model-item>
					</a-col> -->
          <a-col :span="24">
            <a-form-model-item :label="$t('唯一标识符')">
              <a-input :disabled="true" v-model="addParams.code" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item>
              <span>
                {{ $t("txt_name") }}
              </span>
              <a-input
                type="input"
                v-model.trim="addParams.name"
                :disabled="disabled"
                :maxLength="100"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item :label="$t('txt_enable')">
              <a-switch
                :checked="addParams.disabled == 0"
                :disabled="disabled"
                @change="swichChange(addParams, addParams.disabled)"
              >
                <a-icon slot="checkedChildren" type="check" />
                <a-icon slot="unCheckedChildren" type="close" />
              </a-switch>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item :label="$t('txt_description')">
              <a-input
                type="textarea"
                v-model.trim="addParams.description"
                :disabled="disabled"
                :maxLength="255"
              />
            </a-form-model-item>
          </a-col>

          <a-col :span="12">
            <a-form-model-item :label="$t('createby_role')">
              <jw-avatar
                tag
                show-name
                :data="{ name: addParams.createBy, src: '' }"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item :label="$t('txt_create_date')">
              <a-input :disabled="true" v-model="addParams.createDate" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <div
        :style="{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'center',
          zIndex: 1,
        }"
        v-if="!disabled"
      >
        <a-button type="primary" @click="editorSubmit">
          {{ $t("btn_save") }}
        </a-button>
        <a-button :style="{ marginRight: '8px' }" @click="onClose">
          {{ $t("btn_cancel") }}
        </a-button>
      </div>
    </a-drawer>
    <jwLangModal ref="ref_lang_model" />
  </div>
</template>

<script>
import util from "jw_common/util";
import importModal from "./import-modal.vue";
import rules from "jw_frame/utils/rules";
import { jwTable, jwModalForm, jwAvatar, jwLangModal } from "jw_frame";
import {
  getUnitManagement,
  setUnitsDisable,
  searchRoleIsSystem,
} from "./apis/index";
import { getCookie } from "jw_utils/cookie";
import { formatDate } from "jw_utils/moment-date";
const jurisdiction = Jw.getUser().systemAdmin || Jw.getUser().tenantAdmin;
const isSystemAdmin = Jw.getUser().systemAdmin || false;
export default {
  components: {
    jwTable,
    jwModalForm,
    jwAvatar,
    jwLangModal,
    importModal,
  },
  inject: ["setBreadcrumb"],
  data() {
    return {
      visibleExport: false,
      exportLoading: false,
      langObj: {},
      businessType: 1, //1为站点管理，2为组织管理
      selectedRows: [], //开启多选
      searchKey: "",

      addVisible: false,
      dataSource: [],
      title: this.$t("btn_new_create"),
      showJurisdiction: false,
      columns: [
        {
          title: this.$t("唯一标识符"),
          key: "code",
          field: "code",
        },
        {
          title: this.$t("txt_plan_name"),
          key: "name", // 点击回调判断唯一值
          field: "name",
          slots: {
            // 插槽形式
            default: "Tabletitle",
          },
        },
        {
          title: this.$t("txt_description"),
          key: "description",
          field: "description",
        },
        {
          title: this.$t("txt_enable"),
          key: "disabled",
          field: "disabled",
          slots: {
            // 插槽形式
            default: "markForDelete",
          },
        },
        // {
        // 	title: "创建人",
        // 	key: "createBy",
        // 	field: "createBy",
        // 	slots: {
        // 		// 插槽形式
        // 		default: "TableCreateUser",
        // 	},
        // },
        {
          title: this.$t("txt_create_date"),
          key: "createDate",
          field: "createDate",
          slots: {
            default: "creatdTime",
          },
        },
        {
          title: this.$t("tabel_context"),
          slots: {
            // 插槽形式
            default: "containerModelType",
          },
        },
        {
          field: "operation",
          title: this.$t("txt_operation"),
          btns: [
            {
              title: this.$t("btn_edit"),
              key: "editor",
              icon: "jwi-iconedit",
              isShow: function (row) {
                if (row.pageType == "Tenant") {
                  return (
                    row.showJurisdiction && row.containerModelType == "Tenant"
                  );
                } else if (row.pageType == "Site") {
                  return isSystemAdmin;
                }
              },
            },
            {
              title: this.$t("btn_delete"),
              key: "delete",
              icon: "jwi-icondelete",
              isShow: function (row) {
                if (row.pageType == "Tenant") {
                  return (
                    row.showJurisdiction && row.containerModelType == "Tenant"
                  );
                } else if (row.pageType == "Site") {
                  return isSystemAdmin;
                }
              },
            },
          ],
        },
      ],

      formModalData: [
        {
          prop: "code",
          label: "唯一标识符",
          rules: [
            {
              required: true,
              message: this.$t("txt_input"),
              trigger: "change",
            },
            {
              pattern: /^(?![\.])(?!.*?\.$)[a-zA-Z0-9_\.]{1,50}$/,
              message: "只能以字母 _ 数字开头 结尾,且只能包含 . _ 字母、数字",
              trigger: "change",
            },
          ],
          props: {
            is: "a-input",
            value: "code",
            placeholder: this.$t("请输入"),
            maxLength: 100,
            disabled: false,
          },
        },
        {
          prop: "name",
          label: h => {
            return <span>{this.$t("txt_name")}</span>;
          },
          rules: [
            {
              required: true,
              message: this.$t("txt_input"),
              trigger: "change",
            },
          ],
          props: {
            is: "a-input",
            value: "name",
            placeholder: this.$t("placeholder_name"),
            maxLength: 100,
            disabled: false,
          },
        },
        {
          prop: "description",
          label: this.$t("txt_description"),
          // rules: [rules.required],
          props: {
            is: "a-input",
            value: "description",
            maxLength: 255,
            placeholder: this.$t("txt_enter_description"),
            disabled: false,
          },
        },
      ],
      //请求分页配置
      query: {
        page: 1,
        size: 20,
        searchKey: "",
      },
      //formModalData编辑参数
      addParams: {
        code: "",
        oid: "",
        name: "",
        description: "",
        disabled: "",
      },
      // 接口传入的分页
      pages: {
        currentPage: 1,
        size: 20,
        searchKey: "",
      },
      //组件配置分页
      pagerConfig: {
        current: 1,
        pageSize: 20,
        total: 0,
      },
      visible: false,
    };
  },
  computed: {
    toolbars() {
      return [
        {
          name: this.$t("btn_new_create"),
          position: "before",
          type: "primary",
          key: "create",
          // prefixIcon: "jwi-plus",
        },
        // {
        // 	name: this.$t("btn_search"),
        // 	position: "before",
        // 	display: "input",
        // 	value: this.searchKey,
        // 	allowClear: true,
        // 	placeholder: this.$t("msg_input"),
        //     suffixIcon: 'jwi-iconsearch',
        // 	key: "search",
        // },
        // {
        // 	name: "",
        // 	position: "after",
        // 	key: "compare",
        // 	prefixIcon: "jwi-iconfilter",
        // },
        // {
        // 	name: "批量操作",
        // 	display: "dropdown",
        // 	position: "after",
        // 	key: "batch",
        // 	menuList: [
        // 		{
        // 			name: "删除",
        // 			key: "delete",
        // 		},
        // 	],
        // },
        // {
        // 	name: "导入",
        // 	position: "after",
        // 	type: "",
        // 	key: "import",
        // 	// prefixIcon: "jwi-plus",
        // },
        // {
        // 	name: "导出",
        // 	position: "after",
        // 	type: "",
        // 	key: "export",
        // 	// prefixIcon: "jwi-plus",
        // },
      ];
    },
    disabled() {
      return (
        this.addParams.containerModelType == true ||
        (this.businessType == 2 && this.addParams.containerModelType == "Site")
      );
    },
  },
  watch: {
    $route: {
      immediate: true,
      // deep: true,
      handler() {
        if (this.businessType != this.$route.query.type) {
          this.businessType = this.$route.query.type;
          this.getUnitManagementList();
        }

        // this.getDetail();
        //深度监听，同时也可监听到param参数变化
      },
    },
  },
  created() {
    this.businessType = this.$route.query.type || 0;
    this.getUnitManagementList();
    if (Jw.appName === "mpm") {
      let type = this.$route.query.type;
      this.setBreadcrumb([
        {
          name: this.$t("txt_busin_config"),
          path: `/site-business-configuration?type=${type}`,
          isClick: true,
        },
        { name: this.$t("txt_unit"), path: "/site-unit-management?type=1" },
      ]);
    } else {
      this.setBreadcrumb([
        { name: this.$t("txt_unit"), path: "/site-unit-management?type=1" },
      ]);
    }
  },
  methods: {
    onExport() {
      util.download(
        `${Jw.gateway}/${Jw.sysconfigServer}/units/export?containerOid=${
          this.businessType == 1 ? "" : getCookie("tenantOid")
        }`,
        {},
        "post"
      );
    },
    swichChange(site, disabled) {
      // 0 选中   1 不选中
      const afterDisable = disabled === 0 ? 1 : 0;
      setUnitsDisable({
        unitOid: site.oid,
        // disable: +!+site.disable,
        disabled: afterDisable,
      })
        .execute()
        .then(() => {
          this.$success(this.$t("msg_success"));
          site.disabled = afterDisable;
          this.getUnitManagementList();
        })
        .catch(err => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    onLang(item) {
      this.$refs.ref_lang_model
        .show({
          contextInfo: Object.assign({ targetType: "Units" }, this.langObj.row),
          form: this.langObj.propertyLang,
        })
        .then(lang => {
          this.langObj.propertyLang = { ...lang, ...item };
        });
    },
    //获取单位列表
    getUnitManagementList(type = "search") {
      // isSystemAdmin

      let showJurisdiction = jurisdiction;

      let query = {
        ...this.pages,
        type: "search",
        containerOid: this.businessType == 1 ? "" : getCookie("tenantOid"),
        containerType: this.businessType == 1 ? "Site" : "Tenant",
      };
      getUnitManagement(query)
        .execute()
        .then(res => {
          let list = res.rows;
          list.map(v => {
            (v.showJurisdiction = jurisdiction),
              (v.pageType = this.businessType == 1 ? "Site" : "Tenant");
          });
          this.pagerConfig.total = res.count;
          this.dataSource = list;
        });
    },
    //查询当前角色是否为系统管理员
    searchRoleIs() {
      let showJurisdiction = false;
      searchRoleIsSystem
        .execute({ userOid: Jw.getUser().oid })
        .then(res => {
          this.showJurisdiction = data;
          showJurisdiction = data;
        })
        .catch(err => {
          // this.$error(err.msg)
        })
        .finally(() => {});
      return showJurisdiction;
    },
    // 选择列回调
    onSelectChange(args) {
      console.log(args);
    },
    // 操作列回调
    onOperateClick(key, row, disabled) {
      const that = this;
      this.title = this.$t("btn_edit");
      if (key === "editor") {
        this.langObj.row = {
          oid: row.oid,
          code: row.code,
          targetType: "Units",
        };

        this.addParams = {
          code: row.code,
          oid: row.oid,
          name: row.name,
          disabled: row.disabled,
          hasSecurity: row.hasSecurity,
          createBy: row.createBy || row.updateBy,
          description: row.description,
          containerModelType: disabled === true || row.containerModelType,
          createDate: this.formatDate(row.createDate || row.updateDate),
        };
        this.visible = true;
      } else if (key === "delete") {
        this.$confirm({
          title: this.$t("txt_tips_top"),
          content: this.$t("msg_confirm_delete") + "？",
          okText: this.$t("btn_confirm"),
          cancelText: this.$t("btn_cancel"),
          onOk() {
            that.onDelete(row.oid);
          },
        });
      }
    },
    // 工具栏点击回调
    onToolClick({ key }) {
      console.log(key);
      this.addParams = {};
      const that = this;
      if (key === "create") {
        this.title = this.$t("btn_new_create");
        that.addVisible = true;
      } else if (key === "compare") {
        //
      } else if (key === "delete") {
        // this.getUnitManagementList()
      }
    },
    // 工具栏输入回调
    onToolInput({ key }, value) {
      console.log(value);
      if (key === "search") {
        this.pages.searchKey = value;
        this.searchKey = value;
        this.getUnitManagementList();
      }
    },
    // 删除
    onDelete(oid) {
      getUnitManagement({ type: "delete" })
        .execute([oid])
        .then(res => {
          this.$success(this.$t("txt_delete_success"));
          this.getUnitManagementList();
        })
        .catch(err => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    //创建表单提交事件
    addItem(val) {
      console.log(val);
      val.type = "create";
      val.containerModelType = this.businessType == 2 ? "Tenant" : "Site";
      val.containerOid = getCookie("tenantOid");
      getUnitManagement(val)
        .execute(val)
        .then(res => {
          let localizableInfo = null;

          if (this.langObj.propertyLang) {
            localizableInfo = Object.assign(this.langObj.propertyLang, {
              [this.langObj.propertyLang.prop]:
                res[this.langObj.propertyLang.code],
            });
          }
          let options = {
            contextInfo: {
              oid: res.oid,
              code: res.code,
              targetType: "Units",
            },
            handleType: "create",
            localizableInfo,
          };

          // jwLangModal.util
          // 	.onSave(options)
          // 	.catch((err) => {
          // 		this.$error(this.$t("msg_failed"))
          // 	})
          // 	.finally(() => {
          // 		this.langObj.propertyLang = null
          // 	})
          this.$success(this.$t("msg_save_success"));
          this.getUnitManagementList();
          this.addVisible = false;
        })
        .catch(err => {
          this.$refs.ref_modal_form.confirmLoading = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    //抽屉开关
    onClose() {
      this.visible = false;
    },
    //编辑表单提交
    editorSubmit() {
      console.log(this.addParams);
      this.addParams.containerOid = getCookie("tenantOid");
      this.addParams.containerModelType =
        this.businessType == 2 ? "Tenant" : "Site";
      getUnitManagement({ type: "update" })
        .execute(this.addParams)
        .then(res => {
          let localizableInfo = null;

          if (this.langObj.propertyLang) {
            localizableInfo = Object.assign(this.langObj.propertyLang, {
              [this.langObj.propertyLang.prop]:
                this.addParams[this.langObj.propertyLang.code],
            });
          }

          let options = {
            contextInfo: this.langObj.row,
            handleType: "update",
            localizableInfo,
          };

          // jwLangModal.util
          // 	.onSave(options)
          // 	.catch((err) => {
          // 		this.$error(this.$t("msg_failed"))
          // 	})
          // 	.finally(() => {
          // 		this.langObj.propertyLang = null
          // 	})
          this.getUnitManagementList();
          this.$success(this.$t("msg_save_success"));
          this.visible = false;
        })
        .catch(err => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    // 时间戳转换
    formatDate(time) {
      return formatDate(time, "YYYY-MM-DD HH:mm:ss");
    },
    // pages: {
    // 		currentPage: 1,
    // 		size: 20,
    // 		searchKey: "",
    // 	},
    //分页操作
    onPageChange(page, pageSize) {
      this.pages = {
        currentPage: page,
        size: pageSize,
        searchKey: this.pages.searchKey,
      };
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;
      this.getUnitManagementList();
    },
    onSizeChange(pageSize, page) {
      this.pages = {
        currentPage: page,
        size: pageSize,
        searchKey: this.pages.searchKey,
      };
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;

      this.getUnitManagementList();
    },
  },
};
</script>

<style scoped>
/deep/ .jw-avatar.is-tag .jw-avatar-name {
  color: #333 !important;
}
</style>
