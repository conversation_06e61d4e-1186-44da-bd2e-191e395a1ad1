import ModelFactory from "jw_apis/model-factory"

//获取单位查询列表
const getUnitManagement =type=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.sysconfigServer}/units/${type.type}?containerOid=${type.containerOid}&searchKey&size=${type.size}&currentPage=${type.currentPage} `,
    method: type.type=='search'?'get':'post', 
})

//开关单位
const setUnitsDisable =data=>ModelFactory.create({
    url: `${Jw.gateway}/${Jw.sysconfigServer}/units/disabled?disabled=${data.disabled}&unitOid=${data.unitOid}`,
    method: "post"
})

//获取当前角色是否是管理员
const searchRoleIsSystem = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountMicroServer}/user/systemadmin`,
    // url: `${Jw.gateway}/${Jw.permissionServer}/viewPermission/search?page=1&size=10&searchKey=`,
    method: "get"
}) 
export{
    getUnitManagement,
    setUnitsDisable,
    searchRoleIsSystem
}