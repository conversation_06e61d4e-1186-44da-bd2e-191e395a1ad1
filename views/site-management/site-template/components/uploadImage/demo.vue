<template>
  <div style="position: relative; height: 100%; background: white">
    <div style="width: 600px; height: 80px">
      <!-- <div v-if="file">
        <div  v-for="(v,k) in file" :key="k">{{k}} : {{v}}<br/><br/></div>
      </div> -->
      <!-- <div v-if="file2">
        <div  v-for="(v,k) in file2" :key="k">{{k}} : {{v}}<br/><br/></div>
      </div> -->

      {{$t('txt_upload_json_img')}}
      <uploadImage v-model="file" :accept="'.jpg,.png,.gif,.mp4,.txt,.doc,.docx'" />
       {{$t('txt_upload_all')}}
      <uploadImage v-model="file2" :accept="'.jpg,.png,.gif,.mp4,.txt,.doc,.docx'" :multiple="true" />
      
    </div>
  </div>
</template>
<script>
import uploadImage from "./index.vue";
export default {
  components: {
    uploadImage,
  },
  data(){
    return {
      // file:'',
      file:{
        oid: 'f21603de-d0bb-4908-9b86-5aff893beb1a',
        type: null,
        detailType: null,
        markForDelete: false,
        createBy: 'admin',
        createDate: 1647402866279,
        updateBy: 'admin',
        updateDate: 1647402866279,
        tenantOid: '',
        fileOriginalName: '',
        fileName: '1647402867796_my.png',
        filePath: 'http://minio.dev.jwis.cn/mybucket/1647402867796_my.png',
        fileSize: 7086,
        fileSuffix: '',
        bucketName: 'mybucket',
        uid: 'f21603de-d0bb-4908-9b86-5aff893beb1a',
        name: 'my.png',
        url: 'http://minio.dev.jwis.cn/mybucket/1647402867796_my.png',
      },


      // file2:'',
      file2:[
        {
          oid: 'f21603de-d0bb-4908-9b86-5aff893beb1a',
          type: null,
          detailType: null,
          markForDelete: false,
          createBy: 'admin',
          createDate: 1647402866279,
          updateBy: 'admin',
          updateDate: 1647402866279,
          tenantOid: '',
          fileOriginalName: '',
          fileName: '1647402867796_my.png',
          filePath: 'http://minio.dev.jwis.cn/mybucket/1647402867796_my.png',
          fileSize: 7086,
          fileSuffix: '',
          bucketName: 'mybucket',
          uid: 'f21603de-d0bb-4908-9b86-5aff893beb1a',
          name: 'my.png',
          url: 'http://minio.dev.jwis.cn/mybucket/1647402867796_my.png',
        }
      ],
    }
  },
};
</script>