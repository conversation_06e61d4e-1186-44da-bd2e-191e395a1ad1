<template>
  <a-upload-dragger
    :file-list="fileList"
    :accept="accept"
    :multiple="multiple"
    :listType="listType"
    :disabled="disabled"
    
    :beforeUpload="beforeUploadHandle"
    @change="handleChange"
  >
    <div  style="display:flex; justify-content:center; align-items:center;">
      <a-icon type="upload" style="font-size:28px;" />
      <div style="padding-left:10px;">
        <p class="ant-upload-text" style="text-align:left;">{{$t('txt_fiel_click')}}</p>
        <p class="ant-upload-hint">{{$t('txt_size_feil')}}{{maxSize}}M{{$t('txt_upload_feil_fromat')}}:{{accept.split(",").map(c=>c.replace('.','')).join('、')}}</p>
      </div>
    </div>
  </a-upload-dragger>
</template>

<script>
import {getAction,bindFile} from './apis.js'
import axios from 'axios'
function isJson(obj){
  let isjson = typeof(obj) == "object" && Object.prototype.toString.call(obj).toLowerCase() == "[object object]" && !obj.length;
  return isjson;
}
// {
//   appId: null
//   checkSum: "BkBDZ9sMHHECATzLJ7urJuHfwVxyKIXVfkY8ypN1ghs="
//   createdTime: 1646820067488
//   creatorId: null
//   creatorName: "管理员"
//   fileName: "featureManageExport.xls"
//   filePath: "group1/M00/06/A9/wKgCEGIoeaeAWxKCAABUAKtTYdg593.xls"
//   fileSize: 21504
//   modelType: null
//   name: null
//   oid: "daa1903eaec843f9a329cab3c80cb43b"
//   suffix: "xls"
//   tenantId: "administrator"
//   updatedTime: 1646820067488
//   updatorId: null
//   updatorName: "管理员"
//   withDoc: false

//   uid: '1',
//   name: 'xxx.png',
//   // status: 'done',
//   url: 'http://www.baidu.com/xxx.png',
// },


export default {
  name:'jwUploadFile',
  props:{
    value:{  default:()=>({})  },   // 当个文件数据传json，多个文件数据传数组
    accept:{ type:String, default:'.jpg,.png,.gif,.mp4,.txt,.doc,.docx'},  // 上传文件的格式  
    multiple:{  type:Boolean, default:false},
    maxSize:{ type:Number, default:20 },  // 单位为M
    disabled:{ type:Boolean, default:false },
    listType:{ type:String, default:'text' },   // text, picture 和 picture-card
  },
  data() {
    let fileList = null;
    if( this.multiple === false ){
      fileList = (this.value && isJson(this.value) && Object.keys(this.value).length>0)  ?  [this.value]  :  [];
    }
    else{
      fileList = (this.value && Array.isArray(this.value) && this.value.length>0)  ?  this.value  :  [];
    }
    // console.log("fileList: ",fileList);

    return {
      fileList,
      bindParams:{
        // bucketName:'',  // 获取上传action时，获取到的
        // fileName:"1647311325999_企业微信截图_1631501964858.png",
        // fileSize:5414930,
      },
      sts:'',
    };
  },
  watch:{
    fileList(newVal){
      // console.log("newVal: ",newVal);

      if(  this.multiple  ){
        var value = newVal
      }
      else{
        var value = newVal.length>0  ?  newVal[0]  : {};
      }
      console.log("value: ",value);
      this.$emit('input',value);
    },
  },

  methods: {
    // 检查大小
    checkSize(size,maxSize=this.maxSize) {
      if(!(size / 1024 / 1024 / 1024 < maxSize)) {
        this.$error(this.$t('msg_fiel_size'))
        return false;
      }
      else{
        return true;        
      }
    },
    // 文件发生变化  状态有：uploading done error removed
    handleChange(info) {
      if (info.file.status === 'removed') {
        let index = this.fileList.findIndex((c,index,arr) =>c.oid === info.file.oid);
        // console.log("index: ",index);
        if(  index >= 0  ) this.fileList.splice(index,1);
      }
      else if (info.file.status === 'uploading') {
        // console.log(`${info.file.name} 上传中`,info.file.uid);
      }
      else if (info.file.status === 'error') {
        // console.log(`${info.file.name} 上传错误.`);
      }
      else if (info.file.status === 'done') {
        // console.log(`${info.file.name} 上传成功`);
      }
    },


    // 上传之前的操作
    async beforeUploadHandle(file, fileList){
      if(!this.checkSize(file.size)) return false

      this.sts = new Date().getTime();
      await this.getAction({fileName:this.sts + '_' + file.name,file:file});
    },
    // 获取上传action
    async getAction(params){
      await getAction(params).execute(params)
      .then(res=>{
        this.bindParams = {
          bucketName : res.bucketName,
          fileSize : params.file.size,
          fileName : params.fileName,
        };
        this.uploadFile(params.file,res.url)
      })
      .catch(error=>{
        console.log("error: ",error);
      });
    },
    // 上传文件
    uploadFile(file,url) {
      axios.put(url, file, {
        //原生获取上传进度的事件
        onUploadProgress: function (progressEvent) {
          file.status = 'uploading'
          file.percentage = (progressEvent.loaded / progressEvent.total) * 100
          // console.log('%c [ file.percentage ]-137', 'font-size:13px; background:pink; color:#bf2c9f;', file.percentage)

          if(file.percentage == 100){
              file.status = 'success'
          }
        }
      }).then((res) => {
        this.bindFile(this.bindParams);
      })
      .catch((err) => {
          console.log(err)
      })
    },    
    // 绑定文件
    bindFile(params){
      bindFile().execute(params)
      .then(res=>{
        // 生产组件中需要的展示数据
        res = {
          ...res,
          uid:res.oid,
          name:res.fileName.split('_')[1],
          url:res.filePath,
        };
        // console.log("res: ",res);

        this.multiple  ?  this.fileList.push(res)  :  this.fileList = [res];
      })
      .catch(error=>{
        console.log("error: ",error);
      });
    },
  }
};
</script>

<style>
.ant-upload-list-item-error, .ant-upload-list-item-error .anticon-paper-clip, .ant-upload-list-item-error .ant-upload-list-item-name{
  color: #999999;
}
.ant-upload-list-item-error .ant-upload-list-item-card-actions .anticon {
    color: #999999;
}
</style>