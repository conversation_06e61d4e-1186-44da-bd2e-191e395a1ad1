<template>
<span @click="deletConfirm">
    <slot name="default"></slot>
</span>
</template>


<script>
export default {
    props:{
        options:{  type:Object, default:()=>({})  },
    },
    methods:{
        deletConfirm() {
            let data = {
                title:this.$t('btn_delete'),
                content:this.$t('txt_is_delete'),
                cancelText:this.$t('btn_cancel'),
                okText:this.$t('btn_confirm'), 
                width:'200px',
                class:'deleteModal1',
                ...this.options,
            };
            console.log("data: ",data);

            this.$confirm({
                width: data.width,
                class: data.class,
                closable: true,
                mask: false,
                title: (
                <p style="font-size:16px;font-weight:500;color:rgba(30, 32, 42, 0.85);">{data.title}</p>
                ),
                content: (
                <p style="font-size:14px;font-weight:400;color:rgba(30, 32, 42, 0.45);">{data.content}</p>
                ),
                cancelText: data.cancelText,
                okText: data.okText,
                onOk: () => {
                    this.$emit('confirm');
                },
            });
        },
    },
}
</script>

<style>
.deleteModal1 .ant-modal-body {
  padding: 24px;
  width:400px;
}

.deleteModal1 .ant-modal-content{
  width:400px;
}

.deleteModal1 .ant-modal-body {
  width:400px;
}
</style>