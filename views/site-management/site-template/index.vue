<template>
  <div class="template-product-container">
    <a-tabs v-model="activeKey" @change="onChangeTab">
      <a-tab-pane v-for="(item) in tabs" :tab="item.name" :key="item.category">
        <component ref="ref_template" :is="item.com||'commonCom'" :category="item.category" :baseInfo="item"/>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script>
import commonCom from "./libraryContent.vue";

export default {
  components: {
    commonCom
  },
  inject: ["setBreadcrumb"],
  data() {
    return {
      tabs: [],
      activeKey: ""
    };
  },
  methods: {
    callback(key) {},
    onChangeTab(key) {
      this.activeKey = key;
    }
  },
  created() {
    this.setBreadcrumb([{ name: this.$t("btn_model"), path: "" }]);
  },
  mounted() {
    let tabs = Jw.templateTabs;
    if (tabs.length == 0) {
      return this.$error("请在配置文件中配置templateTabs");
    } else {
      this.tabs = tabs.map(item => {
        const langName = this.$t(item.name);
        if (item.com) {
          let path=`template-tenant/${item.com}-com.vue`
          this.$options.components[item.com] = () =>
            import('views/home/'+path);
        }
        return Object.assign(item, { name: langName || item.name });
      });
      this.onChangeTab(tabs[0].category);
    }
  }
};
</script>
<style lang="less" scoped>
.template-product-container {
  height: 20px;
  background: white;
  flex-grow: 1;
  .ant-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  ::v-deep .ant-tabs-nav {
    margin-left: 16px;
  }
  ::v-deep .ant-tabs-content {
    height: 100%;
  }
}
</style>