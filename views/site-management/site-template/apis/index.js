
import ModelFactory from "jw_apis/model-factory"
// 获取产品模板列表
const getList =data=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/containerTemplate/fuzzySearch`,
    method: "post",
});

// 新增产品模板
const addItem =data=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/containerTemplate/create`,
    method: "post",
});


// 删除产品模板
const deleteItem =data=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/containerTemplate/deleteTemplate/${data.oid}`,
    method: "post",
});

// 编辑产品模板
const updateItem =data=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/containerTemplate/update`,
    method: "post",
});


export {
    getList,
    addItem,
    deleteItem,
    updateItem,
}