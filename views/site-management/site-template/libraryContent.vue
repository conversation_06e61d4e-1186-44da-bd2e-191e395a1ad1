<template>
  <div class="template-product-container-resource">
    <!-- dataSource:{{dataSource}} -->
    <jwFixedContent :top="'60px'" :height="'40px'" :wrapStyle="'padding:0px 20px;'">
      <div style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        ">
        <span>
          <a-button @click="showAdd" type="primary" style="margin-right: 10px">
            {{$t('btn_new_create')}}
          </a-button>
          <a-input-search allowClear :placeholder="$t('txt_input')" style="width: 200px" @input="search($event)" />
        </span>

        <span>
          <!-- <a-button><span class="jwi-iconfilter"></span></a-button>
          <a-button>导入</a-button>
          <a-button>导出</a-button> -->
        </span>
      </div>
    </jwFixedContent>

    <div class="table">
      <jw-table :data-source="dataSource" :columns="columns" :pagerConfig="pagerConfig" @onPageChange="onPageChange" @onSizeChange="onSizeChange" :selectedRows.sync="selectedRows">
        <template #tenantOid="{ row: record }">
          <span v-if="record.tenantOid">
            <jw-icon type="jwi-daima" />
            <a>{{ record.tenantOid }}</a>
          </span>
        </template>

        <template #disabled="{ row: record }">
          <a-switch :checked="!record.disabled" @change="handleChange(record)">
            <a-icon slot="checkedChildren" type="check" />
            <a-icon slot="unCheckedChildren" type="close" />
          </a-switch>
        </template>

        <template #file="{ row: record }">
          <span v-show="record.file && record.file.oid" @click="onDownload(record.file)">
            <jwIcon type="#jwi-daima"></jwIcon>
            {{ (record.file && record.file.name) || "" }}
          </span>
        </template>

        <template #action="{ row: record }">
          <jwDeleteConfirm @confirm="deleteItem(record)">
            <span class="jwi-icondelete" style="padding: 0px 10px"></span>
          </jwDeleteConfirm>
        </template>
      </jw-table>
    </div>

    <modalForm :key="'1'" v-if="addVisible" :visible.sync="addVisible" :title="$t('btn_new_create')" :formDatas="addParams" :baseInfo='baseInfo' :category="category" @submit="addItem" />
  </div>
</template>


<script>
import rules from "./utils/rules";
import { getList, deleteItem, updateItem, addItem } from "./apis/index";
import {
  jwTable,
  jwModalForm,
  jwSimpleSelect,
  jwIcon,
  jwFixedContent,
  jwDeleteConfirm
} from "jw_frame";
import modalForm from "./modalForm.vue";
import deleteConfirm from "./components/deleteConfirm/index.vue";
import { getCookie } from "jw_utils/cookie";

export default {
  components: {
    jwTable,
    jwModalForm,
    jwSimpleSelect,
    jwIcon,
    modalForm,
    jwFixedContent,
    deleteConfirm,
    jwDeleteConfirm
  },
  props: {
    category: {
      type: String,
      default: "product"
    },
    baseInfo: {
      type: Object
    }
  },
  data() {
    return {
      //分页配置
      pagerConfig: {
        current: 1,
        pageSize: 20,
        total: 0
      },
      rules,
      // query:{},
      dataSource: [
        // {  id:1, name: "xxx", address: "xxxxx" },
      ],
      columns: [
        // { title: "唯一标识符", field: "number", key: "number" },
        {
          title:
            this.category == "signTemplate"
              ? this.$t("txt_temp_name")
              : this.$t("tabel_name"),
          field: "name",
          key: "name"
        },
        {
          title: this.$t("txt_describe"),
          field: "description",
          key: "description"
        },
        {
          title: this.$t("txt_attachment"),
          field: "file",
          key: "file",
          slots: { default: "file" },
          align: "center"
        },
        {
          title: this.$t("txt_enable"),
          field: "disabled",
          key: "disabled",
          slots: { default: "disabled" },
          align: "center"
        },
        {
          title: this.$t("txt_operation"),
          field: "action",
          key: "action",
          width: "120px",
          slots: { default: "action" },
          align: "center"
        }
      ],
      loading: false,
      selectedRows: [],

      addVisible: false, // 新增弹窗控制
      addParams: {
        // number:'',
        name: "",
        description: "",
        disabled: true,
        category: "",
        file: {}
      },

      updateVisible: false, // 编辑弹窗控制
      updateParams: {}, // 编辑弹窗的数据

      deleteLoading: false,
      // index: 0, // 编辑表单第一条记录
      currentCategory: ""
    };
  },
  methods: {
    onDownload(row) {
      let url = `${Jw.gateway}/${
        Jw.fileMicroServer
      }/file/downloadByOid?fileOid=${row.oid}`;
      window.open(url, "_blank");
    },
    showAdd() {
      this.addParams.disabled = false;
      this.addVisible = true;
    },
    //分页操作
    onPageChange(page, pageSize) {
      this.pages = {
        currentPage: page,
        size: pageSize,
        searchKey: this.searchKey
      };
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;
      this.getListAll();
    },
    onSizeChange(pageSize, page) {
      this.pages = {
        currentPage: page,
        size: pageSize,
        searchKey: this.searchKey
      };
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;
      this.getListAll();
    },
    getListAll(params = {}, keyword = "") {
      // console.log("获取数据的操作", params);
      this.loading = true;
      let { pagerConfig } = this;
      getList()
        .execute({
          category: this.currentCategory,
          containerType: "Tenant",
          containerOid: getCookie("tenantOid"),
          searchKey: keyword,
          index: pagerConfig.current,
          size: pagerConfig.pageSize
        })
        .then(res => {
          console.log("getList res: ", res);
          this.dataSource = res.rows;
          this.pagerConfig.total = res.count;
        })
        .catch(error => {
          console.log("error: ", error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    showList(params) {
      this.getListAll(params);
    },
    // 搜索：过滤
    search(event) {
      // console.log(value);
      // console.log("过滤操作", value);
      this.pagerConfig = {
        current: 1,
        pageSize: 20,
        total: 0
      };
      this.getListAll({}, event.target.value);
    },
    deleteItem(params) {
      deleteItem(params)
        .execute(params)
        .then(res => {
          console.log("res: ", res);
          this.pagerConfig = {
            current: 1,
            pageSize: 20,
            total: 0
          };
          this.getListAll();
          this.$success(this.$t("txt_delete_success"));
        })
        .catch(error => {
          this.$error(this.$t("msg_failed"));
        });
    },
    // 点击编辑按钮
    clickupdateBtn(record) {
      console.log("record: ", record);
      this.updateParams = record;
      this.updateVisible = true;
    },
    updateItem(params) {
      // console.log("编辑操作", params);

      updateItem()
        .execute(params)
        .then(res => {
          console.log("res: ", res);
          this.$success(this.$t("msg_update_success"));
          this.updateVisible = false;
          this.getListAll();
        })
        .catch(error => {
          //   console.log("error: ",error);
          this.$error(this.$t("msg_failed"));
        });
    },
    addItem(params) {
      params.containerType = "Tenant";
      params.containerOid = getCookie("tenantOid");
      addItem()
        .execute({
          ...params
        })
        .then(res => {
          console.log("res: ", res);
          this.$success(this.$t("msg_success"));
          this.addVisible = false;
          this.getListAll();
        })
        .catch(error => {
          //  console.log("error: ",error);
          this.$error(this.$t("msg_failed"));
        });
    },

    handleChange(params) {
      console.log("params: ", params);
      let flag = params.disabled;
      params.disabled = !flag;
      this.updateItem(params);
    }
  },
  created() {
    this.currentCategory = this.category;
    this.addParams.category = this.category;
    this.showList();
  }
};
</script>


<style lang="less">
.template-product-container-resource {
  padding: 0px 20px;
  height: 100%;
  background: white;
  display: flex;
  flex-direction: column;
  .toolbar > div {
    position: static !important;
    padding: 0 !important;
    &:last-child {
      display: none;
    }
  }
  div.table {
    height: 20px;
    flex-grow: 1;
  }
}
</style>