<template>
	<!-- 添加分组 -->
	<a-modal v-model="visible2" :title="title" :cancelText="cancelText" :okText="confirmText" @ok="handleOk" @cancel.stop="handleCancel">
		<template slot="footer">
			<a-button key="submit" type="primary" @click="handleOk">
				{{ confirmText }}
			</a-button>

			<a-button key="back" @click="handleCancel">
				{{ cancelText }}
			</a-button>
		</template>

		<a-form-model ref="ruleForm" :model="formDatas2" @submit.native.prevent>
			<a-form-model-item prop="name" :label="labelName" required :colon="false">
				<a-input v-model.trim="formDatas2.name" :placeholder="$t('txt_input')" />
			</a-form-model-item>

			<a-form-model-item prop="description" :label="$t('txt_describe')" :colon="false">
				<a-textarea v-model="formDatas2.description" :placeholder="$t('txt_input')" />
			</a-form-model-item>

			<a-form-model-item prop="disabled" :label="$t('txt_enable')" :colon="false">
				<a-switch :placeholder="$t('txt_input')" :checked="!formDatas2.disabled" @change="handleChange" />
			</a-form-model-item>

			<a-form-model-item prop="file" :label="$t('txt_attachment')" :colon="false">
				<jwUploadFile v-model="formDatas2.file" :accept="fileAccept" />
			</a-form-model-item>
		</a-form-model>
	</a-modal>
</template>

<script>
import uploadSingle from "./upload-single";
import { jwUploadFile } from "jw_frame";

export default {
  components: {
    uploadSingle,
    jwUploadFile
  },
  data() {
    return {
      visible2: this.visible,
      formDatas2: JSON.parse(JSON.stringify(this.formDatas)),
      fileAccept: this.baseInfo.fileType || ".json",
      labelName:
        this.category == "signTemplate"
          ? this.$t("txt_temp_name")
          : this.$t("tabel_name")
    };
  },
  props: {
    visible: { type: Boolean, default: false },
    title: { type: String, default: "Title" },
    formDatas: { type: Object, default: () => ({}) },

    cancelText: {
      type: String,
      default: function() {
        return this.$t("btn_cancel");
      }
    },
    confirmText: {
      type: String,
      default: function() {
        return this.$t("btn_save");
      }
    },
    category: {
      type: String,
      default: "product"
	},
	baseInfo: {
      type: Object
    }
  },

  watch: {
    visible2(newVal, oldVal) {
      this.$emit("update:visible", newVal);
    }
  },
  methods: {
    showModal() {
      this.visible2 = true;
    },
    handleOk(e) {
      this.onSubmit();
    },
    handleCancel(e) {
      this.resetForm();
      // console.log("取消事件---");
      this.visible2 = false;
      this.$emit("cancel");
    },

    onSubmit() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.$emit("submit", this.formDatas2);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },
    handleChange(val) {
      // console.log(val);
      this.formDatas2.disabled = !val;
    }
  }
};
</script>
