<template>
  <a-upload-dragger
    :action="url"
    :headers="header"
    :multiple="false"
    :file-list="fileList"
    @change="handleChange"
  >
    <p class="ant-upload-text">
      <a-icon type="upload" /> {{$t('btn_upload')}} <br/>
      {{$t('btn_click_upload')}}
    </p>
    <p class="ant-upload-hint">
      {{$t('btn_one_fiel')}}
    </p>
  </a-upload-dragger>
</template>

<script>
import { getCookie } from "jw_utils/cookie";
export default {
  props:{
    value:{  type:Object, default:()=>({})  },
  },
  data() {
    return {
      url: `${Jw.gateway}/${Jw.fileServer}/file/upload`,
      header: {
        accesstoken: getCookie('token')
      },
      fileList: [
        // {
        //   appId: null
        //   checkSum: "BkBDZ9sMHHECATzLJ7urJuHfwVxyKIXVfkY8ypN1ghs="
        //   createdTime: 1646820067488
        //   creatorId: null
        //   creatorName: "管理员"
        //   fileName: "featureManageExport.xls"
        //   filePath: "group1/M00/06/A9/wKgCEGIoeaeAWxKCAABUAKtTYdg593.xls"
        //   fileSize: 21504
        //   modelType: null
        //   name: null
        //   oid: "daa1903eaec843f9a329cab3c80cb43b"
        //   suffix: "xls"
        //   tenantId: "administrator"
        //   updatedTime: 1646820067488
        //   updatorId: null
        //   updatorName: "管理员"
        //   withDoc: false
        // },
      ],
    };
  },

  methods: {
    beforeAvatarUpload(file) {
      const isLt1G = file.size / 1024 / 1024 / 1024 < 1;
      if(!isLt1G) {
        this.$error(this.$t('msg_fiel_size'))
      }
      return isLt1G
    },
    onPreview(file) {
      window.open(`${Jw.gateway}/file/file/downloadByOid?oid=${file.response.data.oid}`)
    },
    handleChange(info) {
      // console.log("info111: ",info);

      let fileList = [...info.fileList];
      // fileList = fileList.slice(-2);

      fileList = fileList.map(file => {
        if (file.response) {
          file.url = file.response.url;
        }
        return file;
      });

      this.fileList = fileList;
      // console.log("this.fileList[0].response: ",this.fileList[0].response);

      if(  this.fileList.length>0 && this.fileList[0].response && this.fileList[0].response.code===0  ){
        this.$emit('input',this.fileList[0].response.result || {});
        console.log("value: ",this.fileList[0].response.result || {});
      }
    },
  }
};
</script>


