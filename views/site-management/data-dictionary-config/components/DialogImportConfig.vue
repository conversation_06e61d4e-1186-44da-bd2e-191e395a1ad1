<template>
  <jw-base-color-modal
    :title="$t('导入数据字典')"
    :visible.sync="visible"
    :ok-btn-loading="saveLoading"
    :ok-text="totalcount + ' ' + $t('btn_ok') + $t('btn_import')"
    :cancel-text="$t('btn_cancel')"
    :dialogClass="'dialog-class'"
    :mask="false"
    @ok="onSave"
    @cancel="onClose"
  >
    <div class="btn-wrap">
      <a-upload-dragger
        name="file"
        :accept="'.xlsx'"
        action="string"
        :fileList.sync="files"
        :customRequest="onUpload"
        :remove="removelist"
      >
        <div class="appendixs-label">
          <jw-icon type="#jwi-shangzhuanwenjian"></jw-icon>
          <div>
            <span
              >{{ $t("txt_feil_drop") }}
              <span class="upload-btn">{{ $t("txt_click_upload") }}</span>
            </span>
            <div>
              ({{
                $t("txt_feil_size_1000") +
                "," +
                $t("txt_uplaod_type") +
                ".xlsx"
              }})
            </div>
          </div>
        </div>
      </a-upload-dragger>
    </div>
    <template #footer-before-btn>
      <a-button
        type="link"
        style="float: left; padding: 0"
        :loading="downloadLoading"
        @click="downexameplefile"
      >
        {{ $t("btn_download") + $t("txt_doc_temp") }}
      </a-button>
    </template>
  </jw-base-color-modal>
</template>

<script>
import { jwBaseColorModal } from "jw_frame";
import { getCookie } from "jw_utils/cookie";
import { importExcel } from 'apis/dataDictionaryV2'
import { downloadFile } from '../utils'

export default {
  name: "DialogImportConfig",
  components: {
    jwBaseColorModal
  },
  props: ["currentTree"],
  data() {
    return {
      visible: true,
      file: null,
      downloadLoading: false,
      testLoading: false,
      saveLoading: false,
      totalcount: "",
      files: [],
    };
  },
  methods: {
    removelist(){
      this.files = []
      this.file = null
    },
    onUpload(info) {
      this.file = info.file;
      let filetype = this.file.name.substring(this.file.name.lastIndexOf("."));
      if (filetype !== ".xlsx") {
        this.$error(this.$t("nonsupport_filetype") + filetype);
        return;
      }
      this.files = [this.file]
    },
    async downexameplefile() {
      this.downloadLoading = true;
      try {
        await downloadFile({
          url: `${Jw.gateway}/${Jw.customerServer}/dataDictionary/exportExcelTemp`,
          method: 'get',
          fileName: '数据字典配置模版.xlsx'
        })
        this.$success(this.$t("txt_export_success"))
      } catch (err) {
        console.log(err);
        this.$error(err.msg || err)
      } finally {
        this.downloadLoading = false
      }
    },
    onClose() {
      this.$emit("close")
    },
    onSave() {
      if (!this.file) {
        this.$warning(this.$t("txt_import_first"));
        return
      }
      let formData = new FormData();
      formData.append("file", this.file)
      this.saveLoading = true
      importExcel(formData)
        .then((res) => {
          this.$success(this.$t('txt_import_success'))
          this.$emit("init-data")
          this.onClose();
        })
        .catch((err) => {
          this.$error(err.msg);
        }).finally(() => {
          this.saveLoading = false
        })
    },
  },
};
</script>

<style lang="less" scoped>
.appendixs-label {
  display: flex;
  align-items: center;
  margin-left: 16px;
  text-align: left;
}
.jw-icon {
  font-size: 32px;
  margin-right: 8px;
}
</style>
<style lang="less">
.dialog-class {
  top: 160px;
  margin-right: 76px;
}
</style>
