<!-- 创建分组 -->
<template>
  <a-modal
    width="612px"
    :visible="true"
    :title="title"
    :maskClosable="false"
    @cancel="close">
    <!-- :footer="null" -->
    <a-form-model
      v-loading="loadingShow"
      ref="formRef"
      :model="formData"
      size="small"
      :rules="rules"
      class="delivery-form">
      <a-form-model-item
        :label="$t('名称')"
        prop="displayName">
        <a-input
          v-model="formData.displayName"
          clearable
          placeholder="请输入名称" />
      </a-form-model-item>
      <a-form-model-item
        :label="$t('编码')"
        prop="code">
        <a-input
          v-model="formData.code"
          clearable
          :disabled="isUpdate"
          placeholder="请输入编码" />
      </a-form-model-item>
      <a-form-model-item
        v-if="formData.dataType === 'data'"
        :label="$t('是否启用')"
        prop="enable">
        <a-switch
          v-model="formData.enable" />
      </a-form-model-item>
    </a-form-model>
    <div slot="footer">
      <a-button
        type="primary"
        :loading="loadingShow"
        @click="submit">
        {{ $t("btn_confirm") }}
      </a-button>
      <a-button
        :loading="loadingShow"
        @click="close">{{ $t("btn_cancel") }}</a-button>
    </div>
  </a-modal>
</template>

<script>
import { postDataConfigSubmit } from 'apis/dataDictionaryV2'
import { cloneDeep } from 'lodash'
export default {
  name: 'DialogCreateGroup',
  props: {
    type: {
      type: String,
      default: 'create'
    },
    detailData: {
      type: Object,
      default: () => ({})
    },
    dataType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loadingShow: false,
      formData: {}
    }
  },
  computed: {
    isUpdate() {
      return this.type === 'update'
    },
    title() {
      return this.isUpdate ? '编辑分组' : '创建分组'
    },
    rules() {
      return {
        displayName: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入编码', trigger: 'blur' },
          {
            pattern: /^(\w|-)+(\.(\w|-)+)*$/,
            message: '只能包含字母、数字、连接符(-)、下划线(_)、单个点号(.)只能在中间不能在开头和结尾',
            trigger: 'blur'
          }
        ],
      }
    }
  },
  created() {
    this.formData = Object.assign({}, this.detailData)
  },
  methods: {
    close() {
      this.$emit('close')
    },
    submit() {
      this.$refs.formRef.validate(async valid => {
        if (!valid) return
        const params = cloneDeep(this.formData)
        // if (!this.isUpdate) {
        //   params.dataType = this.dataType
        //   params.enable = true
        // }
        this.loadingShow = true
        try {
          await postDataConfigSubmit(params)
          this.$success('创建成功')
          this.$emit('success')
          this.close()
        } catch (err) {
          this.$error(err.msg)
        } finally {
          this.loadingShow = false
        }
      })
    }
  }
}
</script>

<style scoped lang='less'>

</style>
