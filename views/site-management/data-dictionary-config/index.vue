<!-- 字典配置 -->
<template>
  <div v-loading="loadingShow" class="content-config base-panel">
    <header class="header">
      <h3 class="title">数据字典配置</h3>
      <div class="import-export">
        <a-button
          :title="$t('btn_import')"
          class="btn-no-border"
          @click="clickImport">
          <jw-icon type="jwi-iconImport"/>
        </a-button>
        <a-button
          :title="$t('btn_export')"
          class="btn-no-border"
          @click="clickExport">
          <jw-icon type="jwi-iconexport"/>
        </a-button>
      </div>
    </header>
    <div class="content">
      <div class="left">
        <header class="left-head">
          <span class="text">字典分组</span>
          <a-button
            type="link"
            title="创建字典分组"
            @click="showAddGroup">
            <i class="jwi-iconadd add-icon"></i>
          </a-button>
        </header>
        <div class="tree-cont">
          <!-- <jw-table
            ref="tree"
            :columns="columns"
            tree
            compact
            :tree-config="{
              childrenField: 'child'
            }"
            :dataSource.sync="groupList">
            <template #tree-content="{ row }">
              <div class="title-cont">
                <span class="name no-wrap" :title="row.displayName">{{ row.displayName }}</span>
                <span class="right-title">
                  <i
                    v-if="row.dataType === 'group'"
                    class="jwi-iconadd btn-icon"
                    @click="clickAdd(row)"></i>
                  <i
                    class="jwi-iconedit btn-icon"
                    @click="clickUpdate(row)"></i>
                  <i
                    class="jwi-icondelete btn-icon"
                    @click="clickDetail(row)"></i>
                </span>
              </div>
            </template>
          </jw-table> -->
          <a-tree
            :block-node="true"
            :selectedKeys.sync="selectedKeys"
            :tree-data="groupList"
            :expanded-keys.sync="expandedKeys"
            :replace-fields="{
              title: 'displayName',
              children: 'child',
              key: 'oid'
            }"
            class="group-tree"
            @select="clickSelect">
            <template #title="row">
              <div class="title-cont">
                <span class="name no-wrap" :title="row.displayName">{{ row.displayName }}</span>
                <span class="right-title" @click.stop>
                  <i
                    v-if="row.dataType === 'group'"
                    title="添加子级"
                    class="jwi-iconadd btn-icon"
                    @click="clickAdd(row)"></i>
                  <i
                    title="编辑"
                    class="jwi-iconedit btn-icon"
                    @click="clickUpdate(row)"></i>
                  <i
                    title="删除"
                    class="jwi-icondelete btn-icon"
                    @click="clickDelete(row)"></i>
                </span>
              </div>
            </template>
          </a-tree>
        </div>
      </div>
      <div class="right">
        <header class="header-right">
          <div>
            <a-button
              type="primary"
              @click="clickConfigCreate">
              创建
            </a-button>
            <a-input-search
              v-model="searchKey"
              placeholder="请输入关键字"
              class="search"
              @search="searchConfig" />
          </div>
          
        </header>
        <div class="table-cont">
          <!-- disableCheck="disableCheck" -->
          <!-- :selectedRows.sync="selectedRows" -->
          <jw-table
            ref="table"
            :data-source="tableData"
            :columns="tableColumns"
            :pagerConfig="pagerConfig"
            :show-page="false"
            @onPageChange="onPageChange"
            @onSizeChange="onSizeChange">
            <template #enable="{ row }">
              <a-switch
                v-model="row.enable"
                @change="enableChange(row)" />
            </template>
            <template #createDate="{ row }">
              {{ row.createDate | filterDate }}
            </template>
          </jw-table>
        </div>
      </div>
    </div>
    <!-- 创建分组 -->
    <dialog-create-group
      v-if="dialogShow"
      :detail-data="detailData"
      :type="dialogType"
      @success="initData"
      @close="dialogShow = false" />
    <!-- 导入 -->
    <dialog-import-config
      v-if="dialogImportShow"
      @close="dialogImportShow = false"
      @init-data="initData" />
  </div>  
</template>

<script>
import DialogCreateGroup from './components/DialogCreateGroup'
import DialogImportConfig from './components/DialogImportConfig'
import { columns } from './const'
import { getDataConfigList, deleteDataConfig, toggleEnable } from 'apis/dataDictionaryV2'
import { cloneDeep, isArray } from 'lodash'
import { confirmHandler } from './utils'
import moment from 'moment'
import { downloadFile } from './utils'
export default {
  name: 'DataDictionaryConfig',
  inject: [
    'setBreadcrumb',
  ],
  filters: {
    filterDate(val) {
      if (!val) return '-'
      return moment(val).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  components: {
    DialogCreateGroup,
    DialogImportConfig
  },
  data() {
    return {
      loadingShow: false,
      pagerConfig: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      searchKey: undefined,
      groupList: [],
      expandedKeys: [],
      selectedKeys: [],
      dialogShow: false,
      dialogType: '',
      dataType: '',
      detailData: {},
      activeData: {},
      tableData: [],
      dialogImportShow: false
    }
  },
  computed: {
    toolbars() {
      return 
    },
    columns() {
      return [
        {
          field: "name",
          slots: {
            default: "tree-content",
          },
          treeNode: true // 树节点标志，必填
        }
      ]
    },
    tableColumns() {
      return [
        ...columns,
        {
          // 操作列定义
          field: "operation", //关键字
          title: this.$t("txt_operation"),
          btns: [
            {
              title: '编辑',
              icon: 'jwi-iconedit',
              click: this.clickUpdate
            },
            {
              title: '删除',
              icon: 'jwi-icondelete',
              click: this.clickDelete
            }
          ]
        }
      ]
    }
  },
  created() {
    this.setBreadcrumb([])
    this.initData()
  },
  methods: {
    async initData() {
      this.loadingShow = true
      try {
        const res = await getDataConfigList()
        this.handlerGroup(res)
        // this.setOpenSelect(res)
        this.groupList = res
      } catch (err) {
        console.log(err);
        this.$error(err.msg)
      } finally {
        this.loadingShow = false
      }
    },
    handlerGroup(list) {
      list.forEach(item => {
        item.slots  = { title: 'title' }
        item.child = item.children
        item.selectable = false
        item.child && item.child.forEach(child => {
          child.slots = { title: 'title' }
          child.configList = child.children
          delete child.children
          if (!this.expandedKeys.length) {
            this.expandedKeys = [item.oid]
          }
          if (!this.selectedKeys.length) {
            this.selectedKeys = [child.oid]
            this.activeData = child
            this.searchConfig()
          } else {
            if (this.selectedKeys.includes(child.oid)) {
              this.activeData = Object.assign({}, child)
              this.searchConfig()
            }
          }
        })
      })
    },
    // setOpenSelect() {

    // },
    // 选中树节点
    clickSelect(selectedKeys, { node, selectedNodes }) {
      console.log(this.checkedKeys, node, selectedNodes)
      if (!selectedKeys.length) {
        this.selectedKeys = [node.dataRef.oid]
        return
      }
      this.activeData = node.dataRef
      this.searchConfig()
    },
    // 搜索
    searchConfig() {
      const configList = this.activeData.configList
      if (!isArray(configList)) {
        this.tableData = []
        return
      }
      if (!this.searchKey) {
        this.tableData = configList
        return
      }
      this.tableData = configList.filter(item => {
        return item.code.includes(this.searchKey) || item.displayName.includes(this.searchKey)
      })
    },
    showAddGroup() {
      this.detailData = {
        enable: true,
        dataType: 'group'
      }
      this.dialogType = 'create'
      this.dialogShow = true
    },
    onPageChange(page, pageSize) {
      this.pagerConfig.current = page
      this.pagerConfig.pageSize = pageSize
      this.initData();
    },
    onSizeChange(pageSize, page) {
      this.pagerConfig.current = page
      this.pagerConfig.pageSize = pageSize
      this.initData()
    },
    clickUpdate(row) {
      this.detailData = cloneDeep(row)
      this.dialogType = 'update'

      this.dialogShow = true
    },
    clickAdd(row) {
      this.detailData = {
        enable: true,
        dataType: 'dictionary',
        parentCode: row.code
      }
      this.dialogType = 'create'
      this.dialogShow = true
    },
    clickDelete(row) {
      confirmHandler(async () => {
        this.loadingShow = true
        try {
          await deleteDataConfig(row.oid)
          this.$success('删除成功')
          if (row.oid === this.selectedKeys[0]) {
            this.selectedKeys = []
          }
          this.initData()
        } catch (err) {
          this.$error(err.msg || err)
        } finally {
          this.loadingShow = false
        }
      })
    },
    async enableChange(row) {
      this.loadingShow = true
      try {
        await toggleEnable(row.oid, row.enable)
      } catch (err) {
        row.enable = !row.enable
        this.$error(err.msg || err)
      } finally {
        this.loadingShow = false
      }
    },
    clickImport() {
      this.dialogImportShow = true
    },
    async clickExport() {
      this.loadingShow = true
      try {
        await downloadFile({
          url: `${Jw.gateway}/${Jw.customerServer}/dataDictionary/exportExcel`,
          method: 'get',
          fileName: '数据字典配置.xlsx'
        })
      } catch (err) {
        this.$error(err.msg || err)
      } finally {
        this.loadingShow = false
      }
    },
    clickConfigCreate() {
      this.dialogType = 'create'
      this.detailData = {
        dataType: 'data',
        enable: true,
        parentCode: this.activeData.code
      }
      this.dialogShow = true
    }
  }
}
</script>

<style scoped lang='less'>
.no-wrap {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.content-config {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.header {
  padding: 0 20px;
  border-bottom: 1px solid #cccccc;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title {
    line-height: 60px;
    font-size: 20px;
  }
  .import-export {
    padding-right: 40px;
  }
  .btn-no-border {
    border: 0;
    padding: 0 5px;
  }
}
.content {
  flex: 1;
  min-height: 0;
  display: flex;
}
.left {
  width: 288px;
  border-right: 1px solid #cccccc;
  display: flex;
  flex-direction: column;
  .left-head {
    display: flex;
    justify-content: space-between;
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #cccccc;
    padding-left: 20px;
    align-items: center;
    .text {
      font-size: 16px;
      font-weight: 500;
    }
    .add-icon {
      font-size: 18px;
    }
  }
  .tree-cont {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    padding: 10px 0;
  }
  .title-cont {
    display: flex;
    .name {
      flex: 1;
      min-width: 0;
    }
  }
  // .group-tree {
  //   /deep/ .ant-tree-node-content-wrapper {
  //     width: calc(100% - 24px);
  //   }
  // }
}
.right {
  flex: 1;
  min-width: 0;
  padding: 0 20px 20px;
  display: flex;
  flex-direction: column;
}
.header-right {
  display: flex;
  padding: 10px 0;
  justify-content: space-between;
  .search {
    width: 200px;
  }
}
.table-cont {
  flex: 1;
  min-height: 0;
}
</style>
