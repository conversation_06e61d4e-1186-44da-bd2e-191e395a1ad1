
import { getCookie } from "jw_utils/cookie"
import axios from 'axios'
import { Modal } from 'ant-design-vue'

// 确认删除
const confirmHandler = (cb, content = '是否确定删除?') => {
  const config = {
    title: '提示',
    content,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      cb && cb()
    },
    onCancel() {}
  }
  if (typeof content === 'string') {
    Modal.confirm(config)
    return
  }
  if (typeof content === 'object') {
    Object.assign(config, content)
    Modal.confirm(config)
    return
  }
  return cb
}

// 下载
const downloadFile = ({ url, fileName = '导出文件.xlsx', method = 'get' }) => new Promise((resolve, reject) => {
  axios({
    url,
    method,
    responseType: 'blob',
    headers: {
      "Content-Type": "application/json;charset=utf8",
      appName: Jw.appName,
      accesstoken: getCookie("token"),
      tenantAlias: getCookie("tenantAlias"),
      tenantOid: getCookie("tenantOid")
    }
  }).then(res => {
    if (res.data.code === -1) {
      reject(res.data)
      return
    }
    const data = res.data
    console.log(data);
    if (data.type === 'application/json') {
      const reader = new FileReader()
      reader.onload = function () {
        // const msg = JSON.parse(reader.result).msg
        // message.error(msg) // 将错误信息显示出来
        reject(JSON.parse(reader.result))
      }
      reader.readAsText(data)
      return
    }
    const blob = new Blob([res.data])
    let href = window.URL.createObjectURL(blob)
    let link = document.createElement("a");
    link.href = href;
    link.setAttribute("download", fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link)
    window.URL.revokeObjectURL(href) 
    resolve()
  }).catch(reject)
})

export {
  confirmHandler,
  downloadFile
}
