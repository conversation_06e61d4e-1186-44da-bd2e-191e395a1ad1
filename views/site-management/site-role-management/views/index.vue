<template>
  <div class="all-background">
    <jw-table
      :columns="columns"
      :toolbars="toolbars"
      :loading="tableloading"
      :data-source.sync="dataSource"
      :pagerConfig="pagerConfig"
      @onPageChange="onPageChange"
      @onSizeChange="onSizeChange"
      @onOperateClick="onOperateClick"
      @onToolClick="onToolClick"
      @onToolInput="onToolInput"
      @checkbox-chang="onSelectChange"
      :selectedRows.sync="selectedRows"
    >
      <template slot="tool-after-end">
        <a-tooltip placement="top">
          <template #title>
            <span id="importPart">{{ $t("btn_import") }}</span>
          </template>
          <a-button @click="visibleExport = true">
            <jw-icon type="jwi-iconImport" />
          </a-button>
        </a-tooltip>
        <a-tooltip placement="top">
          <template #title>
            <span>{{ $t("btn_export") }}</span>
          </template>
          <a-button :loading="exportLoading" @click="onExport">
            <jw-icon type="jwi-iconexport" />
          </a-button>
        </a-tooltip>
      </template>
      <template #containerModelType="{ row }">
        {{
          row.containerModelType == "Site" ? $t("txe_site") : $t("txe_tenant")
        }}
      </template>
      <template #markForDelete="{ row }">
        <a-switch
          :checked="row.disable == 0"
          :disabled="disabled(row)"
          @change="swichChange(row, row.disable)"
        >
          <a-icon slot="checkedChildren" type="check" />
          <a-icon slot="unCheckedChildren" type="close" />
        </a-switch>
      </template>
    </jw-table>
    <jw-modal-form
      ref="addForm"
      :key="'1'"
      v-if="addVisible"
      :visible.sync="addVisible"
      :title="title"
      layout="inline"
      :formDatas="JSON.parse(JSON.stringify(addParams))"
      :keys="formModalData"
      @submit="addItem"
    />
    <!-- 抽屉详情 -->
    <a-drawer
      width="55%"
      :visible="visible"
      :body-style="{ paddingBottom: '80px' }"
      @close="onClose"
    >
      <div style="color: #333; font-size: 16px">{{ $t("btn_edit") }}</div>
      <a-tabs default-active-key="1">
        <a-tab-pane key="1" :tab="$t('detailed_info')">
          <a-form-model :model="addParams" layout="vertical" hide-required-mark>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-model-item :label="$t('txt_name')">
                  <a-input
                    type="input"
                    v-model.trim="addParams.displayName"
                    :maxLength="100"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item :label="$t('txt_enable')">
                  <a-switch
                    :checked="addParams.disable == 0"
                    @change="swichChange(addParams, addParams.disable)"
                  >
                    <a-icon slot="checkedChildren" type="check" />
                    <a-icon slot="unCheckedChildren" type="close" />
                  </a-switch>
                </a-form-model-item>
              </a-col>

              <a-col :span="24">
                <a-form-model-item :label="$t('txt_describe')">
                  <a-input
                    type="textarea"
                    v-model.trim="addParams.description"
                    :maxLength="255"
                  />
                </a-form-model-item>
              </a-col>

              <a-col :span="12">
                <a-form-model-item :label="$t('txt_update_by')">
                  <jw-avatar
                    tag
                    show-name
                    :data="{ name: addParams.createBy, src: '' }"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item :label="$t('txt_update_date')">
                  <a-input :disabled="true" v-model="addParams.updateDate" />
                </a-form-model-item>
              </a-col>
              <!-- <a-col :span="16">
								<div
									style="
										display: flex;
										align-items: center;
										margin-bottom: 40px;
									"
								>
									<span
										style="
											display: inline-block;
											width: 3.25px;
											height: 20px;
											background: #255ed7;
											border-radius: 2.5px;
											margin-right: 10px;
										"
									></span>
									国际化配置
								</div>
							</a-col>
							<a-col :span="16">
								<a-form-model-item label="英文名称">
									<a-input v-model="addParams.english" />
								</a-form-model-item>
							</a-col>
							<a-col :span="16">
								<a-form-model-item label="俄文名称">
									<a-input v-model="addParams.russia" />
								</a-form-model-item>
							</a-col>
							<a-col :span="16">
								<a-form-model-item label="西班牙文名称">
									<a-input v-model="addParams.spain" />
								</a-form-model-item>
							</a-col> -->
            </a-row>
          </a-form-model>
        </a-tab-pane>
      </a-tabs>

      <div
        :style="{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          zIndex: 1,
          textAlign: 'center',
        }"
      >
        <a-button type="primary" @click="editorSubmit">
          {{ $t("btn_save") }}
        </a-button>
        <a-button :style="{ marginRight: '8px' }" @click="onClose">
          {{ $t("btn_cancel") }}
        </a-button>
      </div>
    </a-drawer>
    <import-modal
      :visible="visibleExport"
      @close="visibleExport = false"
      @getList="getDataList"
    ></import-modal>
  </div>
</template>

<script>
import { message } from "ant-design-vue";
import rules from "jw_frame/utils/rules";

import { jwTable, jwModalForm, jwAvatar, jwToolbar, jwPage } from "jw_frame";
import { formatDate } from "jw_utils/moment-date";
import { getCookie } from "jw_utils/cookie";
import importModal from "./import-modal.vue";
import { baseDelete } from "../../../../utils/baseaction";
import {
  getDataListArr,
  createUsers,
  updateUsers,
  setUserDisable,
  searchRoleIsSystem,
} from "../apis/index";
const jurisdiction = Jw.getUser().systemAdmin || Jw.getUser().tenantAdmin;
const isSystemAdmin = Jw.getUser().systemAdmin || false;
export default {
  components: {
    jwTable,
    jwModalForm,
    jwAvatar,
    jwToolbar,
    jwPage,
    importModal,
  },
  inject: ["setBreadcrumb"],
  data() {
    const codeName = (rule, value, callback) => {
      let { pattern, message } = rules.code;

      if (!pattern.test(value)) {
        callback(new Error(message));
      } else {
        callback();
      }
    };
    return {
      visibleExport: false,
      businessType: 1, //1为站点管理，2为组织管理
      selectedRows: [], //开启多选
      title: this.$t("btn_new_create"),
      tableloading: false,
      searchKey: "",
      addVisible: false,
      isSystemAdmin: Jw.getUser().systemAdmin || false,
      showJurisdiction: false,
      dataSource: [],
      columns: [
        {
          title: this.$t("table_identifier"),
          key: "name", // 点击回调判断唯一值
          field: "name",
        },
        {
          title: this.$t("txt_name"),
          key: "displayName", // 点击回调判断唯一值
          field: "displayName",
        },
        {
          title: this.$t("txt_description"),
          key: "description",
          field: "description",
        },
        {
          title: this.$t("txt_enable"),
          key: "disable",
          field: "disable",
          slots: {
            // 插槽形式
            default: "markForDelete",
          },
        },
        {
          title: this.$t("tabel_context"),
          field: "containerModelType",
          slots: {
            // 插槽形式
            default: "containerModelType",
          },
        },
        {
          field: "operation",
          title: this.$t("txt_operation"),
          btns: [
            {
              title: this.$t("btn_edit"),
              key: "editor",
              icon: "jwi-iconedit",
              isShow: row => !this.disabled(row),
            },
            {
              title: this.$t("txt_delete"),
              key: "delete",
              icon: "jwi-icondelete",
              isShow: row => !this.disabled(row),
            },
          ],
        },
      ],
      //formModalData编辑参数

      formModalData: [
        {
          prop: "name",
          label: this.$t("table_identifier"),
          layout: "horizontal",
          itemStyle: "display:inline-block; width:50%; padding-right:10px;",
          rules: [
            {
              required: true,
              message: this.$t("txt_input"),
              trigger: "blur",
            },
            { validator: codeName, trigger: "blur" },
          ],
          props: {
            is: "a-input",
            value: "name",
            placeholder: this.$t("txt_input"),
            disabled: false,
            maxLength: 64,
          },
        },
        {
          prop: "displayName",
          label: this.$t("txt_name"),

          layout: "horizontal",
          itemStyle: "display:inline-block; width:50%; padding-right:0px;",
          rules: [
            {
              required: true,
              message: this.$t("txt_input"),
              trigger: "blur",
            },
          ],
          props: {
            is: "a-input",
            value: "displayName",
            placeholder: this.$t("txt_input"),
            disabled: false,
            maxLength: 100,
          },
        },
        {
          prop: "description",
          label: this.$t("txt_describe"),

          // rules: [rules.required],
          props: {
            is: "a-input",
            value: "description",
            placeholder: this.$t("txt_input"),
            disabled: false,
            maxLength: 255,
          },
        },
      ],
      //请求分页配置
      query: {
        page: 1,
        size: 20,
        searchKey: "",
      },
      pagerConfig: {
        current: 1,
        pageSize: 20,
        total: 0,
      },
      addParams: {
        oid: "",
        displayName: "", //名称
        disable: "", //启用状态
        description: "", //描述
        user: "", //修改人
        img: "", //修改人头像
        updateDate: "", //修改时间
        russia: "", //俄文
        english: "", //英文
        spain: "", //西班牙
      },
      visible: false,
      importLoading: false,
      exportLoading: false,
      templateLoading: false,
    };
  },
  computed: {
    //工具栏
    toolbars() {
      return [
        {
          name: this.$t("btn_new_create"),
          position: "before",
          type: "primary",
          key: "create",
          // prefixIcon: "jwi-plus",
        },
        {
          name: this.$t("btn_search"),
          position: "before",
          display: "input",
          value: this.searchKey,
          allowClear: true,
          placeholder: this.$t("search_text"),
          // suffixIcon: 'jwi-iconsearch',
          key: "search",
        },
        // {
        // 	name: "",
        // 	position: "after",
        // 	key: "compare",
        // 	prefixIcon: "jwi-iconfilter",
        // },

        // {
        // 	name: "导入",
        // 	position: "after",
        // 	type: "dashed",
        // 	key: "import",
        // 	// prefixIcon: "jwi-plus",
        // },
        // {
        // 	name: "导出",
        // 	position: "after",
        // 	type: "dashed",
        // 	key: "export",
        // 	// prefixIcon: "jwi-plus",
        // },
      ];
    },
    disabled() {
      return row =>
        (this.businessType == 2 && row.containerModelType == "Site") ||
        (this.businessType == 1 &&
          (row.name == "ProductManager" ||
            row.name == "member" ||
            row.name == "owner"));
    },
  },

  watch: {
    $route: {
      immediate: true,
      deep: true,
      handler() {
        this.businessType = this.$route.query.type;
        this.query = {
          page: 1,
          size: 20,
          searchKey: "",
        };
        this.pagerConfig = {
          current: 1,
          pageSize: 20,
          total: 0,
        };
        this.getDataList();
        this.setBreadcrumb([{ name: this.$t("page_user_title") }]);
        // this.getDetail();
        //深度监听，同时也可监听到param参数变化
      },
    },
  },
  created() {
    // this.searchRoleIs()
    this.delaySearch = _.debounce(this.getDataList, 500);
    // this.setBreadcrumb([{name:this.$t('page_user_title')}])
    this.businessType = this.$route.query.type || 0;
  },
  methods: {
    //获取角色基础列表
    getDataList(value, type = "search") {
      this.tableloading = true;
      let searchRoleIsStatus = jurisdiction;
      console.log(Jw.getUser());
      console.log(searchRoleIsStatus);
      let query = {
        containerOid: this.businessType == 2 ? getCookie("tenantOid") : "",
        containerType: this.businessType == 2 ? "Tenant" : "Site",
        keyword: this.query.searchKey,
        // "enable":true,
        pageNum: this.query.page,
        pageSize: this.query.size,
      };

      getDataListArr(type)
        .execute(query)
        .then(res => {
          let list = res.rows;
          list.map(v => {
            v.showJurisdiction = searchRoleIsStatus;
          });
          this.pagerConfig.total = res.count;
          this.dataSource = list;
          this.tableloading = false;
        })
        .catch(err => {
          this.$error(err.msg || this.$t("msg_failed"));
          this.tableloading = false;
        });
    },
    //查询当前角色是否为系统管理员
    // searchRoleIs() {
    // 	let a = false
    // 	searchRoleIsSystem
    // 		.execute({ userOid: Jw.getUser().oid })
    // 		.then((res) => {
    // 			this.showJurisdiction = res
    // 			a = res
    // 			this.getDataList()
    // 		})
    // 		.catch((err) => {
    // 			this.$error(err.msg)
    // 		})
    // 	return a
    // },
    // 选择列回调
    onSelectChange(args) {
      console.log(args);
    },
    // 操作列回调
    onOperateClick(key, row) {
      // this.title = key === "editor" ? "编辑角色" : "新建"
      if (key === "international") {
        this.visible = true;
      } else if (key === "editor") {
        this.addParams = {
          oid: row.oid,
          name: row.name,
          disable: row.disable,
          displayName: row.displayName,
          description: row.description,
          createBy: row.createBy,
          updateDate: row.updateDate ? this.formatDateFn(row.updateDate) : "",
        };
        console.log(this.addParams);
        this.visible = true;
      } else if (key === "delete") {
        baseDelete({ oid: row.oid, type: row.type }, this.getDataList);
      }
    },
    swichChange(site, disable) {
      // 0 选中   1 不选中
      const afterDisable = disable === 0 ? 1 : 0;
      setUserDisable({
        roleOid: site.oid,
        // disable: +!+site.disable,
        disable: afterDisable,
      })
        .execute()
        .then(() => {
          this.$success(this.$t("msg_success"));
          site.disable = afterDisable;

          this.getDataList();
        })
        .catch(err => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    // 工具栏点击回调
    onToolClick({ key }) {
      const that = this;
      this.addParams = {
        // oid:'',
        // name:'',
        // display: '',
        // description: '',
      };
      if (key === "create") {
        that.formModalData[0].props.disabled = false;
        that.addVisible = true;
      } else if (key === "compare") {
      } else if (key === "delete") {
        // this.fetchDelete()
      } else if (key === "import") {
        that.importData();
      } else if (key === "export") {
        that.exportData();
      }
    },
    // 工具栏输入回调
    onToolInput({ key }, value) {
      console.log(value);
      if (key === "search") {
        this.query.searchKey = value;
        this.searchKey = value;
        this.query.page = 1;
        this.pagerConfig.current = 1;
        this.getDataList();
      }
    },
    // 删除
    onDelete(row) {
      // this.fetchDelete(row)
    },
    //编辑表单提交
    editorSubmit(val) {
      if (!this.addParams.displayName) {
        message.error("名称不能为空");
        return;
      }

      this.addParams.containerOid = getCookie("tenantOid");
      let type = this.businessType == 1 ? "Site" : "Tenant";
      this.addParams.containerModelType = type;
      delete this.addParams.updateDate;
      updateUsers("update")
        .execute(this.addParams)
        .then(res => {
          this.getDataList();
          this.$success(this.$t("msg_success"));
          this.visible = false;
        })
        .catch(err => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    //表单提交事件
    addItem(val) {
      console.log(val);
      val.containerOid = getCookie("tenantOid");
      // val.containerModelType = "Tenant"
      let type = this.businessType == 1 ? "Site" : "Tenant";
      val.containerModelType = type;
      // if (this.title === "新建角色") {
      // 	type = "create"
      // } else {
      // 	type = "update"
      // }
      createUsers("create")
        .execute(val)
        .then(res => {
          this.getDataList();
          this.$success(this.$t("msg_success"));
          this.addVisible = false;
        })
        .catch(err => {
          this.$refs.addForm.setLoadinng(false);
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    //抽屉开关
    onClose() {
      this.visible = false;
      this.getDataList();
    },
    //分页操作
    onPageChange(page, pageSize) {
      this.query = {
        page: page,
        size: pageSize,
        searchKey: this.query.searchKey,
      };
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;
      this.getDataList();
    },
    onSizeChange(pageSize, page) {
      this.query = {
        page: page,
        size: pageSize,
        searchKey: this.query.searchKey,
      };
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;

      this.getDataList();
    },
    clickIsSystemAdmin(showJurisdiction, containerModelType) {
      return (
        this.isSystemAdmin ||
        (showJurisdiction && containerModelType == "Tenant")
      );
    },
    // 时间戳转换
    // formatDate(time) {
    // 	let date = new Date(time)
    // 	//  var date = new Date(timestamp * 1000);//时x/间戳为10位需*1000，时间戳为13位的话不需乘1000
    // 	let Y = date.getFullYear() + "-"
    // 	let M =
    // 		(date.getMonth() + 1 < 10
    // 			? "0" + (date.getMonth() + 1)
    // 			: date.getMonth() + 1) + "-"
    // 	let D =
    // 		(date.getDate() + 1 < 10
    // 			? "0" + (date.getDate() + 1)
    // 			: date.getDate() + 1) + " "
    // 	let H = date.getHours() + ":"
    // 	//获得系统分钟;
    // 	let F = date.getMinutes() + ":"
    // 	//获得系统秒数;
    // 	let S = date.getSeconds()
    // 	return Y + M + D + H + F + S
    // },
    formatDateFn(time) {
      return formatDate(time);
    },
    /**
     * 导入
     */
    onImport(params) {
      let that = this;
      const _file = params.file;
      var formData = new FormData();
      formData.append("file", _file);
      let xhr = new XMLHttpRequest();
      // containerType: this.businessType == 2 ? "Tenant" : "Site"
      xhr.open(
        "POST",
        `${Jw.gateway}/${
          Jw.accountMicroServer
        }/role/upload?containerModelType=${
          this.businessType == 2 ? "Tenant" : "Site"
        }&containerOid=${getCookie("tenantOid")}`,
        true
      );
      xhr.setRequestHeader("accesstoken", getCookie("token"));
      xhr.setRequestHeader("appName", Jw.appName);
      xhr.setRequestHeader("tenantAlias", getCookie("tenantAlias"))
      xhr.setRequestHeader("tenantOid", getCookie("tenantOid"))
      xhr.onload = res => {
        const response = JSON.parse(xhr.response);
        if (response.code === 0) {
          this.$success("文件上传成功!");
          that.getDataList();
        } else {
          this.$error(response.msg);
        }
      };
      xhr.send(formData);
    },
    /**
     * 下载模板
     */
    onTemplate() {
      this.templateLoading = true;
      fetch(`${Jw.gateway}/${Jw.accountMicroServer}/role/export/template`, {
        method: "get",
        headers: {
          "Content-Type": "application/json;charset=utf8",
          appName: Jw.appName,
          accesstoken: getCookie('token'),
          tenantOid: getCookie("tenantOid"),
          tenantAlias: getCookie("tenantAlias"),
        },
      })
        .then(response => {
          return response.blob();
        })
        .then(res => {
          let url = window.URL.createObjectURL(
            new Blob([res], {
              type: "application/vnd.ms-excel",
            })
          );
          let link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", "user_temp.xlsx");
          document.body.appendChild(link);
          link.click();
          this.templateLoading = false;
        })
        .catch(err => {
          this.templateLoading = false;
          this.$error(err.msg || "导出错误，请重试!");
        });
    },
    /**
     * 导出
     */
    onExport() {
      this.exportLoading = true;
      fetch(
        `${Jw.gateway}/${Jw.accountMicroServer}/role/export?containerType=${
          this.businessType == 2 ? "Tenant" : "Site"
        }&containerOid=${getCookie("tenantOid")}`,
        {
          method: "post",
          // body: JSON.stringify({
          // containerType: this.businessType == 2 ? "Tenant" : "Site",
          // tenantOid: getCookie("tenantOid"),
          // }),
          headers: {
            "Content-Type": "application/json;charset=utf8",
            appName: Jw.appName,
            accesstoken: getCookie('token'),
            tenantOid: getCookie("tenantOid"),
            tenantAlias: getCookie("tenantAlias"),
          },
        }
      )
        .then(response => {
          return response.blob();
        })
        .then(res => {
          let url = window.URL.createObjectURL(
            new Blob([res], {
              type: "application/vnd.ms-excel",
            })
          );
          let link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", "角色管理.xlsx");
          document.body.appendChild(link);
          link.click();
          this.exportLoading = false;
        })
        .catch(err => {
          this.exportLoading = false;
          this.$error(err.msg || "导出错误，请重试!");
        });
    },
  },
};
</script>

<style scoped>
asd {
  display: flex;
}
</style>
