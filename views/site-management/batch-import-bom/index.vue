<template>
  <div class="base-panel">
    <div class="progress">{{ progress }}</div>
    <div v-loading="loading" class="page-container">
      <a-upload-dragger :accept="accept"
                        :file-list="fileList"
                        :showUploadList="false"
                        class="excel-upload"
                        @change="handleChange">
        <div style="display:flex; justify-content:center; align-items:center;">
          <a-icon type="upload" style="font-size:28px;"/>
          <div style="padding-left:10px;">
            <p class="ant-upload-text" style="text-align:left;">将文件拖拽到此处，或点击上传</p>
            <p class="ant-upload-hint">上传允许格式为：{{ accept.split(",").map(c => c.replace('.', '')).join('、') }}</p>
          </div>
        </div>
      </a-upload-dragger>
      <div class="file-list" v-if="fileList.length > 0">
        {{ fileList[0].name }}
      </div>
<!--      <div class="msg">
        <div v-for="(item, index) in msgList" :key="index" class="msg-line">{{ item }}</div>
      </div>-->
      <div class="buttons">
        <a-button type="primary" @click="submit">导入</a-button>
        <a-button style="margin-left: 10px" @click="clearFile">清除</a-button>
      </div>
    </div>

    <div v-loading="loading2" class="page-container">
      <a-upload-dragger :accept="accept"
                        :file-list="fileList2"
                        :showUploadList="false"
                        class="excel-upload"
                        @change="handleChange2">
        <div style="display:flex; justify-content:center; align-items:center;">
          <a-icon type="upload" style="font-size:28px;"/>
          <div style="padding-left:10px;">
            <p class="ant-upload-text" style="text-align:left;">将文件拖拽到此处，或点击上传</p>
            <p class="ant-upload-hint">上传允许格式为：{{ accept.split(",").map(c => c.replace('.', '')).join('、') }}</p>
          </div>
        </div>
      </a-upload-dragger>
      <div class="file-list" v-if="fileList2.length > 0">
        {{ fileList2[0].name }}
      </div>
      <div class="buttons">
        <a-button type="primary" @click="submit2">导入</a-button>
        <a-button style="margin-left: 10px" @click="clearFile2">清除</a-button>
      </div>
    </div>
  </div>
</template>

<script>
import ModelFactory from 'jw_apis/model-factory';
import {jwSimpleTabs, jwAvatar, jwIcon, jwModalForm} from "jw_frame";

//更新所有者和物料状态
const updateParentPartStatus = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/common/updateParentPartStatus`,
  method: 'post',
});
// 批量导入BOM
const batchImportBOM = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/common/batchImportBOMU9`,
  method: 'post',
});
// 批量导入全局替代关系
const batchImportGLOBAL = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/common/batchImportGLOBAL`,
  method: 'post',
});

export default {
  name: 'jwBatchProductFolder',
  components: {},
  inject: [
    'setBreadcrumb',
  ],
  data() {
    return {
      progress: '',
      accept: '.xls,.xlsx',
      fileList: [],
      fileList2: [],
      loading: false,
      loading2: false,
      msgList: []
    };
  },
  created() {
    this.setBreadcrumb([{name: '批量导入BOM'}]);
    const socket = new WebSocket(`${Jw.gateway.replace('http', 'ws')}/customer/websocket/${Jw.getUser().oid}`)
    socket.onmessage = (obj) => {
      const {data} = obj
      this.progress = JSON.parse(JSON.parse(data)).msg
    }
  },
  methods: {
    handleChange(info) {
      let fileList = [...info.fileList]
      fileList = fileList.slice(-1)
      this.fileList = fileList
    },
    clearFile() {
      this.fileList = []
      this.$message.success('已清除上传文件')
    },
    submit() {
      if (this.fileList.length === 0) {
        this.$error('请上传文件！')
        return
      }
      this.loading = true
      const formData = new FormData()
      formData.append("file", this.fileList[0].originFileObj)
      batchImportBOM.execute(formData).then(res => {
        // this.msgList = res
        console.log(res);
        if (res) {
          this.$success(res)
        }
        this.loading = false
      }).catch(err => {
        this.loading = false
        this.$error(err?.msg || this.$t("msg_failed"))
      })
    },
    handleChange2(info) {
      let fileList = [...info.fileList]
      fileList = fileList.slice(-1)
      this.fileList2 = fileList
    },
    clearFile2() {
      this.fileList2 = []
      this.$message.success('已清除上传文件')
    },
    submit2() {
      if (this.fileList2.length === 0) {
        this.$error('请上传文件！')
        return
      }
      this.loading2 = true
      const formData = new FormData()
      formData.append("file", this.fileList2[0].originFileObj)
      updateParentPartStatus.execute(formData).then(res => {
        console.log(res);
        if (res) {
          this.$success(res)
        }
        this.loading2 = false
      }).catch(err => {
        this.loading2 = false
        this.$error(err?.msg || this.$t("msg_failed"))
      })
    }
  }
};
</script>

<style lang="less" scoped>
.base-panel {
  height: calc(100% - 60px);
  display: flex;
  flex-direction: column;
  padding: 20px 0;

  .progress {
    padding: 0 50px;
    text-align: center;
    color: red;
  }

  .page-container {
    margin-bottom: 10px;
    padding: 0 50px;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

    .excel-upload {
      display: inline-block;
      height: 80px;
      width: 100%;
    }

    .file-list {
      margin: 10px 0;
      padding: 0 10px;
      color: #666;
    }

    .buttons {
      margin: 10px 0;
      text-align: center;
    }
  }
}
</style>
