<template>
  <div class="base-panel page-container">
    <div class="org-tree">
      <a-input-search :placeholder="$t('txt_search')" style="margin-bottom: 10px" @search="onTreeSearch" v-model.trim="searchTreeKey" />
      <div class="loadingdiv" v-if="treeLoading">
        <a-spin :spinning="treeLoading" />
      </div>
      <a-tree v-else ref="folderTree" :tree-data="treeData" :expandedKeys.sync="expandedKeys" :selectedKeys.sync="selectedTree" :replaceFields="replaceFields" @select="onTreeSelect">
        <template slot="title" slot-scope="{ dataRef }">
          <div>
            <jw-icon :type="getiClass(dataRef.current.type)"></jw-icon>
            <span v-if="
                (dataRef.current.displayName || dataRef.current.name ).length <=
                12
              " v-text="dataRef.current.displayName || dataRef.current.name"></span>

            <a-tooltip v-else>
              <template slot="title">
                {{ dataRef.current.displayName || dataRef.current.name }}
              </template>
              <span v-text="
                  (
                    dataRef.current.displayName || dataRef.current.name
                  ).substring(0, 12) + '...'
                "></span>
            </a-tooltip>
          </div>
        </template>
      </a-tree>
    </div>

    <div class="user-table">
      <div>
        <a-button type="primary" @click="syncusers" :loading="syncloading">{{ $t("txt_user_sync") }}
        </a-button>
        <a-input-search :placeholder="$t('txt_search')" style="width: 200px; margin-bottom: 10px" @search="onSearch" v-model.trim="condition.searchKey" />

        <a-dropdown>
          <a-menu slot="overlay" @click="handleMenuClick">
            <a-menu-item key="1">{{ $t("txt_enable") }}</a-menu-item>
            <a-menu-item key="2">{{ $t("txt_disable") }}</a-menu-item>
          </a-menu>
          <a-button style="margin-left: 8px">{{ $t("txt_batch_operation") }}
            <a-icon type="down" />
          </a-button>
        </a-dropdown>
      </div>

      <a-spin :spinning="tableLoading">
        <div class="table-line">
          <jw-table ref="tree" row-id="oid" :columns="columns" :dataSource="tableData" :pagerConfig="pagerConfig" :fetch="fetchTable" :auto-load="false" :tree-config="{ expandAll: true }" @onPageChange="onPageChange" @onSizeChange="onSizeChange" :selectedRows.sync="selectedRows">
            <template #name="{ row }">
              <div class="namegroup">
                <jw-avatar :data="row" type="User" />
                <div class="username" v-text="row.name"></div>
              </div>
            </template>

            <template #addFlagslot="{ row }">
              <div class="iconclass">
                <a-switch :checked="row.addFlag" :loading='row.loading' @change="
                    row.addFlag === false
                      ? addper(row.oid, row)
                      : removeper(row.oid, row)
                  " />
              </div>
            </template>
          </jw-table>
        </div>
      </a-spin>
    </div>
    <DialogAddTransfer ref="transfer" :visible="this.visible" @close="cancelData" @submit="submitData" :userAccount="userAccount" :tableData="transferData">
    </DialogAddTransfer>
  </div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import DialogAddTransfer from "./dialog-add-transfer";
// 获取左侧树列表数据
const fetchOrganize = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/tenant/searchOrgStructure`,
  method: "get"
});

//获取左侧人员信息
const fetchMemberList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/user/fuzzyDetailPage/byOrg`,
  method: "post"
});

//批量添加人员信息
const batchaddmember = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/user/add/batch`,
  method: "post"
});

//批量移除人员信息
const batchremovemember = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/user/remove/batch`,
  method: "post"
});

//同步iam人员信息
const syncUserApi = () =>
  ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountMicroServer}/tenant/org/synchronize`,
    method: "get"
  });

//添加授权
const addPreApi = userOid => {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountMicroServer}/user/add`,
    method: "get"
  }).execute({ userOid });
};

//取消授权
const removePreApi = userOid => {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.accountMicroServer}/user/remove`,
    method: "get"
  }).execute({ userOid });
};
import { jwAvatar } from "jw_frame";

export default {
  name: "jwMember",
  components: {
    jwAvatar,
    DialogAddTransfer
  },
  inject: ["setBreadcrumb"],
  data() {
    return {
      routerUrl: "/member",
      visible: false,
      condition: {
        index: 1,
        orgOid: "",
        orgType: "",
        searchKey: "",
        size: 20
      },
      pagerConfig: {
        total: 0
      },
      tableData: [],

      tableLoading: false,
      syncloading: false,

      searchTreeKey: "",
      treeLoading: true,
      treeData: [],
      expandedKeys: [],
      selectedTree: [],

      selectedRows: [],
      userAccount: "",
      transferData: [],
      replaceFields: { children: "children", title: "name", key: "oid" },
      columns: [
        {
          field: "name",
          title: this.$t("txt_username"),
          slots: {
            default: "name"
          }
        },
        // {
        //   filed: "gender",
        //   title: this.$t("txt_user_gender"),
        //   width: 80,
        //   formatter: ({ row }) =>
        //     row.gender === 0
        //       ? this.$t("txt_user_man")
        //       : this.$t("txt_user_woman")
        // },
        {
          field: "account",
          title: this.$t("txt_account")
        },
        {
          field: "number",
          title: this.$t("txt_userNumber")
        },
        {
          field: "orgs",
          title: this.$t("txt_user_dep"),
          formatter: ({ row }) =>
            [...new Set(row.orgs.map(item => item.name))].join(",")
        },
        {
          field: "positions",
          title: this.$t("txt_user_post"),
          formatter: ({ row }) =>
            [...new Set(row.positions.map(item => item.displayName))].join(",")
        },

        {
          field: "phone",
          title: this.$t("txt_contact"),
          width: 120,
        },
        {
          field: "email",
          title: this.$t("txt_email")
        },
       
        {
          field: "",
          width: 100,
          title: this.$t("txt_user_add_permission"),
          slots: {
            // 复杂内容使用jsx语法
            default: "addFlagslot"
          }
        },
         {
          field: "transferRecord",
          title: this.$t("txt_transferRecord")
        },
      ],
      switchLoading: false
    };
  },
  mounted() {
    this.setBreadcrumb([{ name: this.$t("btn_role_management") }]);
    this.fetchTree();
  },
  methods: {
    handleMenuClick({ key }) {
      if (this.selectedRows.length == 0) {
        this.$warning("请先选择数据");
        return;
      }

      let req;
      let param;
      if (key == 1) {
        req = batchaddmember;
        param = this.selectedRows.map(item => item.oid);
      } else if (key == 2) {
        req = batchremovemember;
        param = this.selectedRows.map(item => item.oid);
      }
      req
        .execute(param)
        .then(resp => {
          this.tableData = [];
          this.fetchTable();
          this.$success("操作成功");
        })
        .catch(e => {
          console.log(e);
          this.$error(e.msg);
        });
    },
    cancelData() {
      this.currentRow.loading=false
      this.visible = false;
    },
    async submitData() {
      await this.removePreFetch(this.currentRow.oid, this.currentRow);
      this.visible = false;
      this.fetchTable();
    },
    onTreeSelect(val, { selectedNodes }) {
      if (selectedNodes.length > 0) {
        const { type, oid } = selectedNodes[0].data.props;
        this.condition.orgOid = oid;
        this.condition.orgType = type;

        this.fetchTable();
      }
    },

    //树搜索
    onTreeSearch() {
      this.fetchTree();
    },
    //添加授权
    addper(userOid, cur) {
      this.$set(cur, "loading", true);
      addPreApi(userOid)
        .then(resp => {
          this.$success(this.$t("txt_user_add_permission_sucess"));
          cur.addFlag = true;
          this.$set(cur, "loading", false);
        })
        .catch(e => {
          this.$set(cur, "loading", false);
          this.$error(e.msg);
        });
    },
    //移除授权
    removeper(userOid, cur) {
      this.currentRow=cur
      this.$set(cur, "loading", true);

      if (cur.addFlag) {
        this.userAccount = cur.account;
        const typeList = [
          "PartIteration",
          "MCADIteration",
          "ECADIteration",
          "Issue",
          "DocumentIteration",
          "ECR",
          "Baseline",
          "ProcessOrder"
        ];
        let params = {
          account: cur.account,
          typeList: typeList
        };
        this.$refs.transfer
          .fetchTableData(params)
          .then(data => {
            if (data.length > 0) {
              this.transferData = data;
              this.visible = true;
            } else {
              this.removePreFetch(userOid, cur);
            }
          })
          .catch(err => {
            this.$error(err.msg || this.$t("msg_failed"));
          });
      }
    },

    removePreFetch(userOid, cur) {
      return removePreApi(userOid)
        .then(resp => {
          this.$success(this.$t("txt_user_remove_permission_sucess"));
          cur.addFlag = false;
          this.$set(cur, "loading", false);
        })
        .catch(e => {
          this.$set(cur, "loading", false);
          this.$error(e.msg);
        });
    },
    getiClass(type) {
      switch (type) {
        case "Company":
          return "#jwi-5jieweilogo";
        case "Position":
        case "Department":
          return "#jwi-wenjianga-youneiyong";
        case "User":
          return "#jwi-yonghu";
        default:
          break;
      }
    },
    syncusers() {
      this.syncloading = true;
      syncUserApi()
        .execute()
        .then(resp => {
          this.fetchTree();
          this.$success(this.$t("txt_user_sync_success"));
        })
        .catch(e => {
          console.error(e);
          this.$error(this.$t("txt_user_sync_error"));
        })
        .finally(() => {
          this.syncloading = false;
        });
    },
    onSearch() {
      this.fetchTable();
    },
    fetchTree() {
      this.treeLoading = true;
      fetchOrganize
        .execute({ searchKey: this.searchTreeKey })
        .then(resp => {
          this.treeData = resp ? [resp] : [];
          this.executeListdata(this.treeData);
          if (this.treeData.length > 0) {
            let firstTree = this.treeData[0];
            this.expandedKeys = [firstTree.oid];
            this.selectedTree = [firstTree.oid];

            this.condition.orgOid = firstTree.oid;
            this.condition.orgType = firstTree.type;
            //防止第一次组件请求
            this.fetchTable();
          }
        })
        .catch(e => {
          console.error(e);
        })
        .finally(() => {
          this.treeLoading = false;
        });
    },
    fetchTable({ current = 1, pageSize = 20 } = {}) {
      this.tableLoading = true;
      this.condition.index = current;
      this.condition.size = pageSize;
      return fetchMemberList
        .execute(this.condition)
        .then(res => {
          console.log("人员信息", res);
          const { count, rows } = res;
          this.tableData = rows;
          this.pagerConfig.total = count;
          return { data: rows };
        })
        .catch(err => {
          console.error(err);
          this.tableLoading = false;
          this.$error(err.msg);
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    onPageChange(page, pageSize) {
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;
      this.fetchTable(this.pagerConfig);
    },
    onSizeChange(pageSize, page) {
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;
      this.fetchTable(this.pagerConfig);
    },
    executeListdata(list) {
      list.forEach(item => {
        for (const param in item.current) {
          item[param] = item.current[param];
        }
        if (item.children) {
          this.executeListdata(item.children);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.page-container {
  padding: 15px;
  display: flex;
  flex-direction: row;
  border-top: 1px solid #ddd;
  padding-top: 0;
}

.table-line {
  height: calc(~"100vh - 145px");
}

.iconclass {
  display: flex;
  justify-content: center;
  cursor: pointer;
}

.org-tree {
  flex: 0 0 280px;
  width: 280px;
  padding-right: 10px;
  border-right: 1px solid #ddd;
  padding-top: 15px;
  height: calc(~"100vh - 80px");
  overflow: auto;
}

.user-table {
  width: calc(~"100vw - 500px");
  padding-left: 10px;
  padding-top: 15px;
}

.loadingdiv {
  display: flex;
  justify-content: center;
  width: 100%;
  height: 40%;
  align-items: center;
}

.namegroup {
  display: flex;
  align-items: center;

  .username {
    margin-left: 5px;
  }
}
</style>
