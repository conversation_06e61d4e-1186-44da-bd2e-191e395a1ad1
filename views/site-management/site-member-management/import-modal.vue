<template>
  <a-modal
    :dialogStyle="{ top: '30%' }"
    :title="$t('txt_import_data')"
    :mask="false"
    :visible="visible"
    :confirm-loading="confirmLoading"
    :mask-closable="false"
    :getPopupContainer="() => document.getElementById('importPart')"
    @ok="onSubmit"
    @cancel="onCancel"
  >
    <div class="upload-wrap">
      <a-form-model ref="ref_model_form" :model="modelData">
        <a-upload-dragger
          class="upload-wrap"
          accept=".xls, .xlsx"
          :fileList="files"
          :multiple="true"
          :beforeUpload="beforeUpload"
          :remove="onRemoveFile"
        >
          <div class="appendixs-label">
            <span
              >{{$t('txt_feil_drop')}}<span class="upload-btn">
              {{$t('txt_click_upload')}}  </span
              ></span
            >
            <div>({{$t('txt_feil_size_20')}})</div>
          </div>
        </a-upload-dragger>
      </a-form-model>
    </div>
    <template slot="footer">
      <div style="float: left">
        <a-button
          style="padding-left: 0"
          key="submit"
          type="link"
          :loading="templateLoading"
          @click="onDownloadTemp"
        >
         {{$t('btn_download_temp')}} 
        </a-button>
      </div>
      <div style="float: right">
        <a-button key="cancel" @click="onCancel">{{$t('btn_cancel')}}  </a-button>
        <a-button
          key="submit"
          type="primary"
          :loading="confirmLoading"
          @click="onSubmit"
        >
         {{$t('btn_ok')}} 
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { getCookie } from "jw_utils/cookie";
// part导入数据
const uploadPartList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part-export/upload`,
  method: "post",
});

export default {
  name: "importModal",
  props: ["visible", "currentTree", "showDown"],
  data() {
    return {
      file: "",
      files: [],
      templateLoading: false,
      exportLoading: false,
      confirmLoading: false,
      fileData: {},
      fileName: undefined,
      modelCode: "",
      modelData: {
        activeModle: undefined,
        classModle: undefined,
        fileName: undefined,
      },
      hasSubModel: false,
      subModelOptions: [],
      treeData: [],
      replaceFields: {
        children: "children",
        title: "displayName",
        key: "oid",
        value: "oid",
      },
    };
  },
  watch: {
    visible(val) {
      // console.log("val", val);
      if (val) {
        this.$refs.ref_model_form && this.$refs.ref_model_form.resetFields();
        this.modelData = {
          activeModle: undefined,
          classModle: undefined,
          fileName: undefined,
        };
        this.files = [];
      }
    },
  },
  methods: {
    onDownloadTemp() {
      this.templateLoading = true;
      fetch(`${Jw.gateway}/${Jw.accountMicroServer}/user/export/template`, {
        method: "get",
        headers: {
          "Content-Type": "application/json;charset=utf8",
          appName: Jw.appName,
          accesstoken: getCookie('token'),
          tenantOid: getCookie("tenantOid"),
          tenantAlias: getCookie("tenantAlias"),
        },
      })
        .then((response) => {
          return response.blob();
        })
        .then((res) => {
          let url = window.URL.createObjectURL(
            new Blob([res], {
              type: "application/vnd.ms-excel",
            })
          );
          let link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", this.$t('txt_role_file_temp'));
          document.body.appendChild(link);
          link.click();
          this.templateLoading = false;
        })
        .catch((err) => {
          this.templateLoading = false;
          this.$error(err.msg || this.$t('msg_import_erro'));
        });
    },
    onCancel() {
      this.fileName = "";
      this.fileData = {};
      this.files = [];
      this.$emit("close");
    },
    beforeUpload(file) {
      let isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message({
          message:his.$t('txt_feil_size_20') ,
          type: "error",
        });
        return false;
      }
      this.files = [file];
      return false;
    },
    onRemoveFile(file, fileList) {
      console.log(file);
      console.log(fileList);
      let { files } = this;
      this.files = [].concat(files.filter((item) => item.name != file.name));
    },
    onSubmit() {
      let that = this;
      const _file = this.files[0];
      var formData = new FormData();
      formData.append("file", _file);
      this.confirmLoading = true;
      let xhr = new XMLHttpRequest();
      xhr.open(
        "POST",
        `${Jw.gateway}/${
          Jw.accountMicroServer
        }/user/upload?tenantOid=${getCookie("tenantOid")}`,
        true
      );
      xhr.setRequestHeader("accesstoken", getCookie("token"));
      xhr.setRequestHeader("appName", Jw.appName);
      xhr.setRequestHeader("tenantOid", getCookie("tenantOid"));
      xhr.setRequestHeader("tenantAlias", getCookie("tenantAlias"));
      xhr.onload = (res) => {
        const response = JSON.parse(xhr.response);
        this.confirmLoading = false;
        if (response.code === 0) {
          this.$success(this.$t('msg_upload_success'));
          this.$emit("getList");
          this.onCancel();
        } else {
          this.$error(response.msg);
        }
      };
      xhr.send(formData);
    },
  },
};
</script>

<style lang="less" scoped>
.upload-wrap {
  margin-top: 10px;
}
.file-name-input {
  margin-right: 10px;
  color: rgba(0, 0, 0, 0.65);
  /deep/ &.ant-input {
    background: #fff;
  }
}
/deep/ .ant-select-tree {
  height: 200px;
  overflow: scroll;
}
</style>
