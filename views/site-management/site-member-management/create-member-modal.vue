<template>
    <a-modal
        :visible="visible"
        :title="$t('btn_new_create')"
        :mask-closable="false"
        :footer="null"
        @cancel="onCancel"
    >
        <a-form-model
            ref="addForm"
            class="form-container"
            layout="vertical"
            :model="form"
            :rules="rules">
            <a-row :gutter="10">
                <a-col :span="12">
                    <a-form-model-item :label="$t('txt_account')" prop="account">
                        <a-input
                            v-model.trim="form.account"
                            allow-clear
                            :placeholder="$t('msg_input')" />
                    </a-form-model-item>
                </a-col>
                <a-col :span="12">
                    <a-form-model-item :label="$t('txt_user_name')" prop="name">
                        <a-input
                            v-model.trim="form.name"
                            allow-clear
                            :placeholder="$t('msg_input')" />
                    </a-form-model-item>
                </a-col>
            </a-row>
            <a-form-model-item  :label="$t('txt_contact')"  prop="phone">
                <a-input
                    v-model.trim="form.phone"
                    allow-clear
                    :placeholder="$t('msg_input')" />
            </a-form-model-item>
            <a-form-model-item :label="$t('txt_email')" prop="email">
                <a-input
                    v-model.trim="form.email"
                    allow-clear
                    :placeholder="$t('msg_input')" />
            </a-form-model-item>
            <a-form-model-item :label="$t('txt_passworld')" prop="password">
                <a-input
                    v-model="form.password"
                    disabled
                    allow-clear
                    :placeholder="$t('msg_input')" />
            </a-form-model-item>
            <a-form-model-item :label="$t('签名')" prop="signature">
                <jwUploadFile v-model="form.file" :accept="fileAccept" />
            </a-form-model-item>
            <a-form-model-item class="form-item-btns">
                <a-button type="primary" @click="onCreate">{{$t('txt_add_take_effect')}}</a-button>
                <a-button class="form-btn-cancel" @click="onCancel">{{$t('btn_cancel')}}</a-button>
            </a-form-model-item>
        </a-form-model>
    </a-modal>
</template>

<script>
import { getCookie } from 'jw_utils/cookie';
import ModelFactory from 'jw_apis/model-factory';
import { jwUploadFile } from "jw_frame"

// 添加人员
const createMember = ModelFactory.create({
    url: Jw.appName === 'npi' ? 
        `${Jw.gateway}/${Jw.accountMicroServer}/v2/user/create` :
        `${Jw.gateway}/${Jw.accountMicroServer}/user/create`,
    method: 'post',
});

export default {
    name: 'addMemberModal',
    props: [
        'visible',
    ],
    components: {
		jwUploadFile,
	},
    data() {
		return {
            fileAccept: ".png,.jpg",
            form: {
                name: '',
                account: '',
                phone: '',
                email: '',
                disabled: 0,
                password: 'Jwi_plm@2022',
                tenantOid: getCookie('tenantOid') ,
                signature: null,
			},
			rules: {
                name: [
					{ required: true, message: this.$t('msg_input'), trigger: 'change' },
                    { max: 50, message: this.$t('txt_prompt'), trigger: 'change' },
				],
                account: [
                    { required: true, message: this.$t('txt_prompt'), trigger: 'change' },
                ],
                phone: [
                    {
                        pattern: /^1[3-9]\d{9}$/,
                        message: this.$t('txt_phone_number'),
                        trigger: 'change',
                    },
                ],
                email: [
                    // { required: true, message: this.$t('txt_prompt'), trigger: 'change' },
                    {
                        pattern: /^[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?$/,
                        message:  this.$t('txt_email_address'),
                        trigger: 'change',
                    },
                ],
			},
		}
    },
    mounted() {

	},
	methods: {
        onCreate() {
            this.$refs.addForm.validate((valid) => {
				if (valid) {
                    createMember.execute(
                        this.form
                    ).then((res) => {
                        this.$success(this.$t('msg_save_success'));
                        this.onCancel();
                        this.$emit('getTableData');
                    }).catch((err) => {
                        if (err.msg) {
                            this.$error(err.msg);
                        }
                    });
				} else {
					return false;
				}
			});
        },
        onCancel() {
            this.$refs.addForm.resetFields();
            this.$refs.addForm.clearValidate();
            this.$emit('close');
        },
  	},
}
</script>

<style lang="less" scoped>

</style>
