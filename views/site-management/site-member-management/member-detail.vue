<template>
	<a-drawer
		:title="$t('btn_edit')"
		:bodyStyle="bodyStyle"
		width="50%"
		:visible="visible"
		@close="onClose"
	>
		<div class="detail-drawer-wrap">
			<a-tabs>
				<a-tab-pane key="basicInfo" :tab="$t('detailed_info')"></a-tab-pane>
				<a-button slot="tabBarExtraContent" v-show="isEdit" @click="onEdit">{{
					$t("btn_edit")
				}}</a-button>
			</a-tabs>
			<div class="detail-drawer-body-wrap">
				<a-form-model
					ref="updateForm"
					class="form-container"
					layout="vertical"
					:model="form"
					:rules="rules"
				>
					<a-form-model-item :label="$t('txt_account')" prop="account">
						<a-input
							v-model.trim="form.account"
							disabled
							allow-clear
							:placeholder="$t('txt_input')"
						/>
					</a-form-model-item>
					<a-form-model-item :label="$t('btn_user_name')" prop="name">
						<a-input
							v-model.trim="form.name"
							:disabled="isEdit"
							allow-clear
							:placeholder="$t('txt_input')"
						/>
					</a-form-model-item>
					<a-form-model-item :label="$t('txt_contact')" prop="phone">
						<a-input
							v-model.trim="form.phone"
							:disabled="isEdit"
							allow-clear
							:placeholder="$t('txt_input')"
						/>
					</a-form-model-item>
					<a-form-model-item :label="$t('txt_email')" prop="email">
						<a-input
							v-model.trim="form.email"
							:disabled="isEdit"
							allow-clear
							:placeholder="$t('txt_input')"
						/>
					</a-form-model-item>
					<a-form-model-item :label="$t('txt_passworld')" prop="password">
						<a-input
							:value="'Jwi_plm@2022'"
							disabled
							allow-clear
							:placeholder="$t('txt_input')"
						/>
					</a-form-model-item>
					<a-form-model-item :label="$t('签名')" prop="signature">
						<img v-if="isEdit && signatureImg" class="signimg" :src="signatureImg" />
						<jwUploadFile
							v-if="!isEdit && visible"
							v-model="form.signature"
							:layoutName="'update'"
							:accept="fileAccept"
						/>
					</a-form-model-item>
				</a-form-model>
			</div>
		</div>
		<div class="detail-drawer-foot-wrap" v-show="!isEdit">
			<a-button type="primary" @click="onSave">{{ $t("btn_save") }}</a-button>
			<a-button class="btn-cancel" @click="onClose">{{
				$t("btn_cancel")
			}}</a-button>
		</div>
	</a-drawer>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { jwUploadFile } from "jw_frame";
import { getCookie } from "jw_utils/cookie";

// 更新版本规则
const updateMember = ModelFactory.create({
	url:
		Jw.appName === "npi"
			? `${Jw.gateway}/${Jw.accountMicroServer}/v2/user/update`
			: `${Jw.gateway}/${Jw.accountMicroServer}/user/update`,
	method: "post",
});

// 根据文件名称获取文件下载路径
const getFileUrl = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.fileMicroServer}/file/searchByOid`,
	method: "get",
});

export default {
	name: "memberDetail",
	props: ["visible", "detailInfo", "toEdit"],
	components: {
		jwUploadFile,
	},
	data() {
		return {
			signatureImg: "",
			fileAccept: ".png,.jpg",
			bodyStyle: { padding: 0 },
			form: {},
			rules: {
				name: [
					{ required: true, message: this.$t("msg_input"), trigger: "change" },
					{ max: 50, message: this.$t("txt_prompt"), trigger: "change" },
				],
				account: [
					{ required: true, message: this.$t("msg_input"), trigger: "change" },
				],
				phone: [
					{
						pattern: /^1[3-9]\d{9}$/,
						message: this.$t("txt_phone_number"),
						trigger: "change",
					},
				],
				email: [
					// { required: true, message: this.$t('msg_input'), trigger: 'change' },
					{
						pattern:
							/^[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?$/,
						message: this.$t("txt_email_address"),
						trigger: "change",
					},
				],
			},
			isEdit: true,
		};
	},
	watch: {
		detailInfo(val) {
			if (val) {
				this.getFileUrl(val.signature);
				this.fetchSignImg(val.signature);
			}
		},
		visible(val) {
			if (val) {
				if (this.toEdit) this.isEdit = false;
				this.$nextTick(() => {
					this.form = {
						...this.detailInfo,
					};
				});
			}
		},
	},
	mounted() {},
	methods: {
		getFileUrl(signature) {
			if (signature && signature.oid) {
				{
					let params = {
						fileOid: signature.oid,
					};
					getFileUrl
						.execute(params)
						.then((res) => {
							res.status = "done";
							res.name = res.fileOriginalName;
							res.uid = res.oid;
							res.url = res.filePath;
							this.form.signature = { ...res };
							this.detailInfo.signature = { ...res };
							this.$emit("showDetail");
						})
						.catch((err) => {
							if (err.msg) {
								this.$error(err.msg);
							}
						});
				}
			} else {
				this.$emit("showDetail");
			}
		},
		fetchSignImg(signature) {
			if (signature && signature.oid) {
				// 使用图片缩略图oid获取图片地址
				let fileOid = signature.oid;
				fetch(
					`${Jw.gateway}/${Jw.fileMicroServer}/file/downloadByOid?fileOid=${fileOid}`,
					{
						method: "get",
						headers: {
							"Content-Type": "application/json;charset=utf8",
							appName: Jw.appName,
							accesstoken: getCookie('token'),
							tenantAlias: getCookie("tenantAlias"),
							tenantOid: getCookie("tenantOid"),
						},
					}
				)
					.then((response) => {
						return response.blob();
					})
					.then((data) => {
						let url = window.URL.createObjectURL(
							new Blob([data], {
								type: "application/vnd.ms-excel",
							})
						);
						this.signatureImg = url;
					})
					.catch((err) => {
						this.$error(err.msg || this.$t("msg_failed"));
					});
			}else{
				this.signatureImg = null
			}
		},
		onClose() {
			this.isEdit = true;
			this.$refs.updateForm.clearValidate();
			this.form = {};
			this.$emit("close");
		},
		onEdit() {
			this.isEdit = false;
		},
		// onCancel() {
		// 	this.isEdit = true;
		// 	this.$refs.updateForm.clearValidate();
		// 	this.form = { ...this.detailInfo };
		// },
		onSave() {
			let _this = this;
			this.$refs.updateForm.validate((valid) => {
				if (valid) {
					updateMember
						.execute(this.form)
						.then((res) => {
							this.form.signature = { ...res.signature };
							this.$emit("getTableData");
							this.$success(this.$t("msg_update_success"));
							this.isEdit = true;
							this.$refs.updateForm.clearValidate();
							this.form = { ...this.detailInfo };
							if (res.signature && res.signature.oid != null) {
								this.fetchSignImg(res.signature);
								let params = {
									fileOid: res.signature.oid,
								};
								getFileUrl
									.execute(params)
									.then((result) => {
										result.status = "done";
										result.name = result.fileOriginalName;
										result.uid = result.oid;
										result.url = result.filePath;
										_this.form.signature = { ...result };
										_this.detailInfo.signature = { ...result.signature };
										_this.isEdit = true;
									})
									.catch((err) => {
										if (err.msg) {
											this.$error(err.msg);
										}
									});
							} else {
								this.signatureImg = "";
							}
						})
						.catch((err) => {
							this.isEdit = false;
							if (err.msg) {
								this.$error(err.msg);
							}
						});
				} else {
					return false;
				}
			});
		},
	},
};
</script>

<style lang="less" scoped>
	.signimg{
		width: 236px;
	}
</style>
