<template>
  <a-modal :visible="visible" title="关联数据" width="60%" @cancel="onCancel" v-if="visible">
    <a-spin :spinning="tableLoading">
      <div class="table-line">
        <jw-table ref="refTable" row-id="oid" :columns="columns" :dataSource="tableData" :auto-load="true" :showPage="false" :height="500">
          <template #numberSlot="{ row }">
            <span style="color: #255ed7; cursor: pointer" @click="routerLink(row)">
              {{ row.number }}
            </span>
          </template>
        </jw-table>
      </div>
    </a-spin>
    <div style="margin-top:8px;">
      <div class="text-name">
        <span style="color: #f81d22">*</span>转派人
      </div>
      <div class="user-line">
        <a-button class="plus-handle-btn" shape="circle" icon="plus" @click="choseUser()" />
        <jw-avatar v-if="transferRecord!==null && transferRecord.account" tag show-name :data="transferRecord" />
      </div>
    </div>
    <jw-user-modal-v2 ref="user-modal" :visible="isVisible" :isCheckbox="false" />
    <div slot="footer">
      <a-button :loading="bthLoading" type="primary" @click="onConfirm">{{ $t('btn_ok') }}</a-button>
      <a-button @click="onCancel">{{ $t('btn_cancel') }}</a-button>
    </div>
  </a-modal>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { jwAvatar, jwUserModalV2, jwIcon } from "jw_frame";

const fetchTransferData = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/findOwnerData`,
  method: "post"
});

const updateOwnerData = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/updateOwnerData`,
  method: "post"
});

export default {
  name: "dialog-add-transfer",
  props: ["visible", "userAccount", "tableData"],
  components: {
    jwAvatar,
    jwUserModalV2,
    jwIcon
  },
  data() {
    return {
      ischeckBox: false,
      tableLoading: false,
      transferRecord: {},
      pagerConfig: {
        current: 1,
        pageSize: 20,
        total: 0
      },
      isVisible: true,
      columns: [
        {
          field: "number",
          title: "编码",
          // slots: {
          //   default: "numberSlot"
          // },
          cellRender: {
            name: "link",
            showLock: true,
            icon: ({ row }) => row.modelIcon,
            events: {
              click: ({ row }) => {
                this.routerLink(row);
              }
            }
          }
        },
        {
          field: "name",
          title: "名称"
        },
        {
          field: "lifecycleStatus",
          title: "状态",
          formatter: ({ text, row }) => row.lifecycleStatus ? row.lifecycleStatus : row.processState,
        },
        {
          field: "modelDefinition",
          title: "类型"
        },
        {
          field: "displayVersion",
          title: this.$t("txt_version"),
          cellRender: {
            name: "tag"
          },
          width: 100,
        },
        {
          field: "owner",
          title: this.$t("txt_owner")
        },
        {
          field: "updateDate",
          title: this.$t("txt_update_time"),
          width: 160,
          cellRender: {
            name: "date"
          }
        }
      ],
      bthLoading: false
    };
  },
  methods: {
    onConfirm() {
      console.log(this.tableData);
      console.log(this.transferRecord);
      
      if (!this.transferRecord?.account) {
        return this.$error("转派人必选");
      }
      if(this.transferRecord.account==this.userAccount){
        return this.$error("转派人 与被转派人不能相同");
      }
      this.bthLoading = true;
      updateOwnerData
        .execute({
          transferData: this.tableData,
          transferAccount: this.transferRecord.account,
          account: this.userAccount
        })
        .then(res => {
          this.onSubmit();
        })
        .catch(err => {
          this.bthLoading = false;
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
    onCancel() {
      this.hide();
      this.$emit("close");
    },
    onSubmit() {
      this.hide();
      this.$emit("submit");
    },
    choseUser() {
      this.$refs["user-modal"]
        .show({
          type: "User"
        })
        .then(data => {
          this.transferRecord = data;
          console.log("transferRecord", this.transferRecord);
        });
    },
    // onPageChange(page, pageSize) {
    //   this.pagerConfig.current = page;
    //   this.pagerConfig.pageSize = pageSize;
    //   this.fetchTable(this.pagerConfig);
    // },
    // onSizeChange(pageSize, page) {
    //   this.pagerConfig.current = page;
    //   this.pagerConfig.pageSize = pageSize;
    //   this.fetchTable(this.pagerConfig);
    // },
    fetchTableData(params) {
      return fetchTransferData.execute(params);
    },
    fetchTable() {
      this.tableLoading = true;
      let { pagerConfig: { current, pageSize } } = this;
      console.log("current", current);
      console.log("pageSize", pageSize);
      const typeList = [
        "PartIteration",
        "MCADIteration",
        "ECADIteration",
        "Issue",
        "DocumentIteration",
        "ECR",
        "Baseline",
        "ProcessOrder"
      ];
      let params = {
        account: this.userAccount,
        typeList: typeList
      };
      return this.fetchTableData(params)
        .then(data => {
          this.tableLoading = false;
          this.tableData = data.rows;
          this.pagerConfig.total = data.count;
          return { data: data.rows || data, total: data.count };
        })
        .catch(err => {
          this.tableLoading = false;
          console.log(err);
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    hide() {
      this.$refs.refTable.clearCurrentRow();
      this.transferRecord = null;
      this.tableLoading = false;
    },
    routerLink(row) {
      if (row.OID) {
        row.oid = row.OID;
      }
      if (row.type === "ProcessOrder") {
        let issueUrl = this.$router.resolve({
          name: `detailed-task`,
          path: `/detailPage/detailed-task`,
          query: {
            backName: "我的流程",
            processOrderOid: row.oid,
            processInstanceId: row.processInstanceId
          }
        });
        window.open(issueUrl.href, "_bank");
      } else {
        Jw.jumpToDetail(row, { blank: true });
      }
    }
  }
};
</script>

<style lang="less" scoped>
.detail-drawer-wrap {
  .detail-drawer-body-wrap {
    height: calc(~"100vh - 126px");
  }
}

.user-line {
  display: flex;
  align-items: center;
}
</style>