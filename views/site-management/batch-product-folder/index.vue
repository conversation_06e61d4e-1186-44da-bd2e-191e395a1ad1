<template>
  <div class="base-panel">
    <div class="progress">{{ progress }}</div>
    <div v-loading="loading" class="page-container">
      <a-form-model :model="form" ref="form" :rules="rules">
        <a-form-model-item label="型号" prop="containerOid">
          <a-select v-model="form.containerOid" show-search :filter-option="filterOption" @change="change">
            <a-select-option
                v-for="(ele, index) in containerList"
                :value="ele.oid"
                :key="index"
            >
              {{ ele.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
      <a-upload-dragger :accept="accept"
                        :file-list="fileList"
                        :showUploadList="false"
                        class="excel-upload"
                        @change="handleChange">
        <div style="display:flex; justify-content:center; align-items:center;">
          <a-icon type="upload" style="font-size:28px;"/>
          <div style="padding-left:10px;">
            <p class="ant-upload-text" style="text-align:left;">将文件拖拽到此处，或点击上传</p>
            <p class="ant-upload-hint">格式为：{{ accept.split(",").map(c => c.replace('.', '')).join('、') }}</p>
          </div>
        </div>
      </a-upload-dragger>
      <div class="file-list" v-if="fileList.length > 0">
        {{ fileList[0].name }}
      </div>
      <div class="msg">
        <div v-for="item in msgList" class="msg-line">{{ item }}</div>
      </div>
      <div class="buttons">
        <a-button type="primary" @click="submit(true)">新导入</a-button>
        <a-button type="primary" @click="submit(false)">历史导入</a-button>
      </div>
    </div>
  </div>
</template>

<script>
import ModelFactory from 'jw_apis/model-factory';
import {fetchContainerList} from "../../../apis/product-container";

// 批量创建文件夹
const batchCreateFolder = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/batchFolder/batchCreateFolder`,
  method: 'post',
});
export default {
  name: 'jwBatchProductFolder',
  components: {},
  inject: [
    'setBreadcrumb',
  ],
  data() {
    return {
      progress: '',
      accept: '.xls,.xlsx',
      fileList: [],
      form: {
        containerOid: localStorage.getItem("selectProduct")
      },
      loading: false,
      containerList: [],
      rules: {
        containerOid: [{required: true, message: "请选择型号"}]
      },
      msgList: []
    };
  },
  created() {
    this.setBreadcrumb([{name: '批量创建产品文件夹'}]);
    this.getConList();
    const socket = new WebSocket(`${Jw.gateway.replace('http', 'ws')}/customer/websocket/${Jw.getUser().oid}`)
    socket.onmessage = (obj) => {
      const {data} = obj
      this.progress = JSON.parse(JSON.parse(data)).msg
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    getConList() {
      let param = {
        index: 1,
        size: 10000,
      };
      this.loading = true
      fetchContainerList
          .execute(param)
          .then((res) => {
            this.containerList = res.rows || [];
            this.loading = false
          })
          .catch((err) => {
            this.loading = false
            this.$error(err?.msg || this.$t("msg_failed"));
          });
    },
    change(value) {
      localStorage.setItem("selectProduct", value)
    },
    filterOption(input, option) {
      return (
          option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      );
    },
    handleChange(info) {
      let fileList = [...info.fileList]
      fileList = fileList.slice(-1)
      this.fileList = fileList
    },
    submit(isNew) {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          if (this.fileList.length === 0) {
            this.$error('请上传文件！')
          } else {
            this.loading = true
            const formData = new FormData()
            formData.append("file", this.fileList[0].originFileObj)
            formData.append("containerOid", this.form.containerOid)
            formData.append("isNew", isNew)
            this.msgList = await batchCreateFolder.execute(formData)
            if (this.msgList.length === 0) {
              this.$success('导入成功！')
              this.progress = ''
            }
            this.loading = false
          }
        }
      })
    }
  }
};
</script>

<style lang="less" scoped>
.base-panel {
  height: calc(100% - 60px);

  .progress {
    padding: 0 50px;
    text-align: center;
    color: red;
  }

  .page-container {
    height: calc(100% - 15px);
    padding: 0 50px;
    display: flex;
    flex-direction: column;

    .excel-upload {
      display: inline-block;
      height: 80px;
      width: 100%;
    }

    .msg {
      height: calc(100% - 190px);
      overflow: auto;

      .msg-line {
        font-size: 15px;
        font-weight: bold;
        padding: 5px 0;
      }
    }

    .buttons {
      margin-bottom: 5px;
      text-align: center;
    }
  }
}

</style>
