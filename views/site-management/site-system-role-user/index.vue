<template>
  <div class="all-background">
    <div class="org-tree">
      <div class="search-group">
        <a-input-search
          :placeholder="$t('txt_search')"
          style="margin-bottom: 10px"
          @search="onTreeSearch"
          v-model.trim="searchTreeKey"
        />
      </div>
      <div class="loadingdiv" v-if="treeLoading">
        <a-spin :spinning="treeLoading" />
      </div>
      <a-tree
        v-else
        ref="folderTree"
        :tree-data="treeData"
        :expandedKeys.sync="expandedKeys"
        :selectedKeys.sync="selectedTree"
        :replaceFields="replaceFields"
        @select="onTreeSelect"
      >
        <template slot="title" slot-scope="{ dataRef }">
          <div>
            <jw-icon type="#jwi-jiaose"></jw-icon>
            <span
              v-if="dataRef.displayName.length <= 12"
              v-text="dataRef.displayName"
            ></span>

            <a-tooltip v-else>
              <template slot="title">
                {{ dataRef.displayName }}
              </template>
              <span
                v-text="dataRef.displayName.substring(0, 12) + '...'"
              ></span>
            </a-tooltip>
          </div>
        </template>
      </a-tree>
    </div>

    <div class="user-table">
      <jw-table
        ref="ref_table"
        disableCheck="disableCheck"
        :data-source.sync="tableData"
        :columns="getHeader"
        :selectedRows.sync="selectedRows"
        :fetch="fetchTable"
        :toolbars="toolbars"
        :showPage="false"
        :autoLoad="false"
        @onToolClick="onToolClick"
        @onToolInput="onToolInput"
        @checkbox-change="onSelectChange"
        @onOperateClick="onOperateClick"
      >
        <template #Tabletitle="{ row }">
          <jw-avatar tag show-name :data="row" />
        </template>
        <template slot="tool-after-start">
          <a-button
            v-show="selectedRows.length > 0"
            style="background: #ffffff; color: #f81d22; text-shadow: none"
            type="danger"
            @click="onToolClick({ key: 'delete' })"
          >
            {{ $t("btn_batch_delete") }}
          </a-button>
        </template>
      </jw-table>
    </div>
    <jwUserModalV2 ref="user-modal" />
  </div>
</template>

<script>
import { jwAvatar, jwUserModalV2 } from "jw_frame";
import { getCookie } from "jw_utils/cookie";
import ModelFactory from "jw_apis/model-factory";

//获取角色列表
const loadRolelist = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/systemrole/fuzzy/tenantEnable`,
  method: "get",
});

//获取列表
const libraryTableModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.sysconfigServer}/admin/setting/searchByType`,
  method: "get",
});
// 添加人员
const addAdminModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.sysconfigServer}/admin/setting/add`,
  method: "post",
});

export default {
  components: {
    jwAvatar,
    jwUserModalV2,
  },
  inject: ["setBreadcrumb", "addBreadcrumb"],
  data() {
    return {
      // 选人人员

      deleteModal: false,
      searchKey: "",
      tableData: [],
      selectedRows: [],

      pagination: {
        page: 1,
        size: 10,
        total: 30,
      },
      tableLoading: false,
      total: 0,
      selectRow: [],

      searchTreeKey: "",
      treeLoading: false,
      treeData: [],
      expandedKeys: [],
      selectedTree: [],
      replaceFields: { children: "children", title: "displayName", key: "name" },

    };
  },
  computed: {
    getHeader() {
      return [
        {
          field: "name",
          title: this.$t("sys_full_name"),
          sortable: true, // 开启排序
          slots: {
            // 插槽形式
            default: "Tabletitle",
          },
        },
        {
          field: "account",
          title: this.$t("txt_account"),
        },
        // {
        //   field: "createDate",
        //   title: this.$t("sys_create_Date"),
        //   sortable: true, // 开启排序
        //   formatter: 'date',
        // },
        {
          // 操作列定义
          field: "operation", //关键字
          title: this.$t("txt_operation"),
          btns: [
            {
              icon: "jwi-icondelete", // 图标显示
              title: this.$t("btn_delete"),
              key: "delete",
            },
          ],
        },
      ];
    },
    toolbars() {
      return [
        {
          name: this.$t("add_personnel"),
          position: "before",
          type: "primary",
          key: "create",
          // prefixIcon: "jwi-plus",
        },
        {
          name: this.$t("btn_search"),
          position: "before",
          display: "input",
          value: this.searchKey,
          allowClear: false,
          placeholder: this.$t("txt_search"),
          suffixIcon: "jwi-iconsearch",
          key: "search",
        },
      ];
    },
  },
  created() {
    // 输入回调去抖动
    this.delaySearch = _.debounce(this.onSearch, 500);
    this.loadRoleData();
    this.initBreadcrumb();
  },

  methods: {
    //加在角色信息
    loadRoleData() {
      this.treeLoading = true;
      loadRolelist
        .execute({ searchKey: this.searchTreeKey })
        .then((resp) => {
          this.treeData = resp;
          if (this.treeData.length > 0) {
            this.selectedTree = [this.treeData[0].name];
            this.fetchTable()
          }
        })
        .catch((e) => {
          console.error(e);
        })
        .finally(() => {
          this.treeLoading = false;
        });
    },
    onTreeSelect(val) {
      console.log(val);
      this.fetchTable()
    },
    onTreeSearch() {
      this.loadRoleData();
    },
    // 选择列回调
    onSelectChange(args) {
      // console.log(args);
      this.selectRow = args;
    },
    // 操作列回调
    onOperateClick(key, row) {
      if (key === "lanuch") {
        ///
      } else if (key === "delete") {
        // console.log(row);
        this.onDelete(row);
      }
    },
    // 工具栏点击回调
    onToolClick({ key, row }) {
      if (key === "create") {
        this.$refs["user-modal"].show({ type: "User" }).then((data) => {
          this.modalCallback(data);
        });
      } else if (key === "compare") {
        //
      } else if (key === "delete") {
        // console.log("this.selectedRows", this.selectedRows);
        this.fetchDelete(this.selectedRows);
      }
    },
    // 工具栏输入回调
    onToolInput({ key }, value) {
      if (key === "search") {
        this.searchKey = value;
        // console.log(value);
        this.delaySearch();
      }
    },
    // 删除
    onDelete(row) {
      this.fetchDelete([row]);
    },
    // 数据请求函数
    fetchTable() {
      if(this.selectedTree.length === 0){
        return
      }
      let param = {
        searchKey: this.searchKey, //关键字（name/number）
        containerType: "Tenant",
        containerOid: getCookie("tenantOid"),
        systemRoleName: this.selectedTree[0],
      };
      this.tableLoading = true;
      return libraryTableModel
        .execute(param)
        .then((data) => {
          // console.log(data);
          this.tableLoading = false;
          this.tableData = data.result;
          return { data: data.result, total: data.length };
        })
        .catch((err) => {
          this.tableLoading = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    initBreadcrumb() {
      let breadcrumbData = [
        { name: this.$t("sys_role_users"), path: "/site-system-admin" },
      ];
      this.setBreadcrumb(breadcrumbData);
    },
    // 表单录入弹窗-成功回调
    modalCallback(dataSource) {
      // 处理选中的人员
      let userOids = dataSource.map((dataItem) => dataItem.oid);
      let param = {
        systemRoleName: this.selectedTree[0],
        userOids,
      };

      addAdminModel
        .execute(param)
        .then((data) => {
          let { code } = data;
          if (code == "-1") {
            this.$error(data.msg);
          } else {
            this.$success(this.$t("msg_success"));

            //刷新列表
            this.fetchTable({ current: 1, pageSize: 10 });
          }
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    // 删除列操作
    fetchDelete(row) {
      let { tableData } = this;
      let userOids = row.map((item) => item.oid);
      let param = {
        systemRoleName: this.selectedTree[0],
        userOids,
      };
      this.$confirm({
        width: "280px",
        class: "deleteModal",
        closable: true,
        mask: false,
        title: (
          <p style="font-size:16px;font-weight:500;color:rgba(30, 32, 42, 0.85);">
            {this.$t("txt_delete")}
          </p>
        ),
        content: (
          <p style="font-size:14px;font-weight:400;color:rgba(30, 32, 42, 0.45);padding-right: 10px;">
           {this.$t("txt_delete_content")}
          </p>
        ),
        cancelText: this.$t("btn_cancel"),
        okText: this.$t("btn_confirm"),
        onOk: () => {
          ModelFactory.create({
            url: `${Jw.gateway}/${Jw.sysconfigServer}/admin/setting/delete`,
            method: "post",
          })
            .execute(param)
            .then((data) => {
              // console.log(data);
              this.$success(this.$t("msg_success"));
              this.selectedRows = [];
              //刷新列表
              this.fetchTable({ current: 1, pageSize: 10 });
            })
            .catch((err) => {
              this.$error(err.msg || this.$t("msg_failed"));
            });
        },
      });
    },
    // 输入回调刷新表格数据
    onSearch() {
      this.$refs.ref_table.reFetchData();
    },
  },
};
</script>

<style lang="less" scoped>
.search-group {
  display: flex;
}
.org-tree {
  width: 280px;
  padding-right: 10px;
  border-right: 1px solid #ddd;
  padding-top: 15px;
  height: calc(~"100vh - 80px");
  overflow: auto;
}
.loadingdiv {
  display: flex;
  justify-content: center;
  width: 100%;
  height: 40%;
  align-items: center;
}

.user-table {
  width: calc(~"100vw - 500px");
  padding-left: 10px;
  padding-top: 15px;
}
.all-background {
  width: 100%;
  padding: 15px;
  display: flex;
  border-top: 1px solid #ddd;
  padding-top: 0;
}
.openBtn {
  float: right;
  margin: 10px 20px 0 0;
}

.delteIcon {
  cursor: pointer;
}
</style>
<style>
.deleteModal .ant-modal-body {
  padding: 24px;
}

.deleteModal .ant-modal {
  /* top: 112px;
  left: 42%; */
}

.deleteModal .ant-modal-close-x {
  line-height: 69px;
}

.deleteModal
  .ant-modal-confirm-body
  > .anticon
  + .ant-modal-confirm-title
  + .ant-modal-confirm-content {
  margin-left: 0;
}

.deleteModal .ant-modal-confirm-btns .ant-btn {
  width: 75px;
  float: right;
}

.deleteModal .ant-modal-confirm-btns .ant-btn.ant-btn-primary {
  margin-right: 8px;
  /* background-color: rgba(37, 94, 215, 1);
  border-color: rgba(37, 94, 215, 1); */
  /* background-color: #1890ff; */
}
</style>
