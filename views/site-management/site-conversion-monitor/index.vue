<template>
    <div class="base-panel page-container">
        <jw-table ref="refTable"
            :panel="true"
            :toolbars="toolbars"
            :columns="columns"
            :data-source.sync="tableData"
            :fetch="fetchTable"
        >
            <template slot="tool-before-end">
                <a-form-model
                    style="width:70%;"
                    layout="vertical"
                    :model="form">
                    <a-row :gutter="10">
                        <a-col :span="5">
                            <a-select
                                v-model="form.tenantOid"
                                :placeholder="$t('msg_select')+$t('txe_tenant')"
                                allowClear
                                showSearch
                                filterOption
                                option-filter-prop="children"
                            >
                                <a-select-option
                                    v-for="val in tenantList"
                                    :title="val.name"
                                    :key="val.oid"
                                    :value="val.oid"
                                >
                                    {{ val.name }}
                                </a-select-option>
                            </a-select>
                        </a-col>
                        <a-col :span="5">
                            <a-select
                                v-model="form.status"
                                :placeholder="$t('msg_select')+$t('txt_status')"
                                allowClear
                            >
                                <a-select-option
                                    v-for="val in statusList"
                                    :title="val.value"
                                    :key="val.key"
                                    :value="val.value"
                                >
                                    {{ val.value }}
                                </a-select-option>
                            </a-select>
                        </a-col>
                        <a-col :span="5">
                            <a-input
                                v-model.trim="form.keyWord"
                                allowClear
                                :placeholder="$t('txt_enter_keyword')" />
                        </a-col>
                        <a-col :span="6">
                            <a-range-picker
                                v-model="searchDate"
                                allowClear
                                :placeholder="[$t('msg_starttime'), $t('msg_endtime')]"
                                @change="onDateChange"
                                />
                        </a-col>
                        <a-col :span="2">
                            <a-button type="primary" @click="onSearch">{{ $t("btn_search") }}</a-button>
                        </a-col>
                    </a-row>
                </a-form-model>
            </template>
            <template #updateDate="{ row }">
                <span>{{row.updateDate - row.createDate}} ms</span>
            </template>
            <template #operation="{ row }">
                <i class="jwi-kaigong"
                    v-if="row.status === 'Error'"
                    :title="$t('txt_agin_text')"
                    @click="onReStart(row)"
                >
                </i>
                <a-icon type="loading"
                    v-if="row.status !== 'Error' && row.status !== 'Completed'"
                    :title="$t('txt_being_performed')"
                />
            </template>
        </jw-table>
    </div>
</template>

<script>
import ModelFactory from 'jw_apis/model-factory';
import moment from "moment";

// 获取CAD转换记录
const fetchConversionList = ModelFactory.create({
    url: `${Jw.cadConverterMicroServer.indexOf('http') !== -1 ? '' : Jw.gateway + '/' }${Jw.cadConverterMicroServer}/cad/converter/findRecord`,
    method: 'post',
});

// 失败状态重试
const doStep =  ModelFactory.create({
    url: `${Jw.cadConverterMicroServer.indexOf('http') !== -1 ? '' : Jw.gateway + '/' }${Jw.cadConverterMicroServer}/cad/converter/convert`,
    method: 'post',
});

export default {
    name: 'jwCADConversionMonitor',
    components: {

    },
    inject:[
        'setBreadcrumb',
    ],
    data() {
        return {
            form: {
                tenantOid: undefined,
                status: undefined,
                keyWord: '',
                startDate: '',
                endDate: '',
            },
            searchDate: [],
            tenantList: Jw.getUser().tenants,
            statusList: [
                {key: 'Executing', value: 'Executing'},
                {key: 'Completed', value: 'Completed'},
                {key: 'Error', value: 'Error'},
            ],
            tableData: [],
            tableLoading: false,
        };
    },
    created() {
        this.setBreadcrumb([{name:this.$t('txt_cad_monitor')}]);
    },
    computed: {
        toolbars() {
            return [
                {
                    name: this.$t('btn_new_create'),
                    key: 'create',
                    type: 'primary',
                    isVisible: false,
                },
            ]
        },
        columns() {
            return [
                {
                    field: 'cadName',
                    title: this.$t('CAD名称'),
                    minWidth: 210,
                },
                {
                    field: 'fileName',
                    title: this.$t('stp文件名称'),
                    minWidth: 210,
                },
                {
                    field: 'status',
                    title: this.$t('txt_status') ,
                    minWidth: 150,
                    cellRender: {
                        name: 'tag',
                        render: ({ row }) => {
                            if (row.status === 'Completed') {
                                return {
                                    text: row.status,
                                    color: 'blue',
                                }
                            } else if (row.status === 'Error') {
                                return {
                                    text: row.status,
                                    color: 'red',
                                }
                            } else {
                                return {
                                    text: this.$t('txt_being_performed'),
                                }
                            }
                        }
                    },
                },
                {
                    field: 'msg',
                    title: this.$t('错误信息'),
                    minWidth: 240,
                    visible: false,
                },
                {
                    field: 'createDate',
                    title: this.$t('msg_starttime') ,
                    minWidth: 180,
                    formatter: 'date',
                },
                {
                    field: 'updateDate',
                    title: this.$t('txt_duration'),
                    minWidth: 180,
                    formatter: 'date',
                    slots: {
                        default: 'updateDate',
                    },
                },
                {
                    field: 'operation',
                    title: this.$t('txt_operation'),
                    slots: {
                        default: 'operation',
                    },
                }
            ];
        },
    },
    mounted() {

	},
    methods: {
        onSearch() {
            this.$refs.refTable.reFetchData();
        },
        fetchTable({ current, pageSize }) {
            let params = {
                size: pageSize,
                index: current,
                param: {
                    tenantOid: this.form.tenantOid,
                    status: this.form.status,
                    keyWord: this.form.keyWord,
                    startDate: this.form.startDate,
                    endDate: this.form.endDate,
                }
            };
            this.tableLoading = true;
            return fetchConversionList.execute(
                params
            ).then((res) => {
                return {
                    data: res.rows,
                    total: res.count,
                };
            }).catch((err) => {
                this.tableLoading = false;
                this.$error(err.msg);
            });
        },
        onDateChange(date, dateString) {
            if (date.length) {
                const startDate = moment(dateString[0] + ' 00:00:00', 'YYYY-MM-DD hh:mm:ss'),
                    endDate = moment(dateString[1] + ' 23:59:59', 'YYYY-MM-DD hh:mm:ss');
                this.form.startDate = startDate.format('x');
				this.form.endDate = endDate.format('x');
            } else {
                this.form.startDate ='';
				this.form.endDate = '';
            }
        },
        onReStart(row) {
            this.$confirm({
                title: this.$t('确定重试吗？'),
                okText: this.$t('btn_ok'),
                cancelText: this.$t('btn_cancel'),
                onOk: () => {
                    return doStep.execute([
                        {
                            oid: row.oid,
                            fileOid: row.fileOid,
                            cadOid: row.cadOid,
                            fileName: row.fileName,
                            cadName: row.cadName,
                            fileType: row.fileType
                        }
                    ]).then((res) => {
                        this.$warning(this.$t('后台正在重新执行，请稍后查看执行结果'));
                        this.onSearch();
                    }).catch((err) => {
                        if (err.msg) {
                            this.$error(err.msg);
                        }
                    });
                },
            });
        },
    },
};
</script>

<style lang="less" scoped>

</style>
