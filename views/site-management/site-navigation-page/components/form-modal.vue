<template>
	<a-modal
		v-model="vis"
		:title="title"
		:ok-text="okText"
		:cancel-text="cancelText"
		:mask-closable="false"
		:confirmLoading="confirmLoadingStatus"
		:width="width"
		@cancel="cancelClick"
		@ok="confirm"
		class="jw-common-form-modal"
	>
		<a-form-model
			:layout="layout"
			:model="model"
			:rules="rules"
			ref="form-modal"
		>
			<a-form-model-item
				class="form-model-item"
				v-for="(item, index) in dataSource"
				:key="index"
				:label="`${item.useHelp ? '' : item.label}`"
				:prop="item.prop"
				:class="{ block: item.block }"
				:id="`form-item-${item.prop}`"
			>
				<span v-if="item.useHelp" class="seacret"
					><span style="color: red; font-size: 14px">* </span
					>{{ item.label }}</span
				>
				<!-- input 输入框、 textarea 文本域 -->
				<!-- 通过 v-model 同步数据后，无法监听 表单域元素 上的 change 事件 -->
				<a-input
					v-if="item.type == 'input' || item.type == 'textarea'"
					v-model.trim="model[item.prop]"
					:maxLength="item.maxLength || -1"
					:placeholder="item.placeholder || '请输入'"
					:type="item.type"
					@change="inputValues"
					:disabled="item.disabled"
				/>
				<!-- 特殊类型：IP 地址段 -->
				<a-row v-if="item.type == 'ip-range'" :gutter="16" class="ip-range">
					<a-col :span="12">
						<div class="inner-box">
							<span v-for="j in 3" :key="j" class="item-box">
								<a-input
									:value="ipRange[j - 1]"
									:maxLength="3"
									tyep="number"
									:max="255"
									:min="0"
									@change="(evt) => validateInteger(evt.target.value, j - 1)"
								/><span :key="j" class="split-pointer">.</span>
							</span>
							<span class="item-box">
								<a-input
									:value="startIPLastPos"
									:maxLength="3"
									tyep="number"
									:max="255"
									:min="0"
									@change="
										(evt) => validateInteger(evt.target.value, 'startIPLastPos')
									"
								/>
							</span>
						</div>
					</a-col>
					<!-- 结束ip段 -->
					<a-col :span="12">
						<div class="inner-box">
							<span v-for="j in 3" :key="j" class="item-box">
								<a-input
									:value="endIpRange[j - 1]"
									:maxLength="3"
									tyep="number"
									:max="255"
									:min="0"
									@change="
										(evt) => validateInteger(evt.target.value, j - 1, 'endIp')
									"
								/><span :key="j" class="split-pointer">.</span>
							</span>
							<span class="item-box">
								<a-input
									:value="endIPLastPos"
									:maxLength="3"
									tyep="number"
									:max="255"
									:min="0"
									@change="
										(evt) => validateInteger(evt.target.value, 'endIPLastPos')
									"
								/>
							</span>
						</div>
					</a-col>
				</a-row>
				<!-- tree-select -->
				<!-- 无法使用 tree-data-simple-mode 属性？？ -->
				<a-tree-select
					v-else-if="item.type == 'tree'"
					v-model="model[item.prop]"
					:placeholder="item.placeholde || '请选择'"
					:disabled="item.disabled"
					:tree-data="item.treeData"
					@change="treeSelect"
					:load-data="item.loadData"
				/>
				<!-- select 下拉 -->
				<a-select
					v-else-if="item.type == 'select'"
					v-model="model[item.prop]"
					placeholder="请选择"
					:disabled="item.disabled"
					:show-search="!!item.showSearch"
					:filter-option="item.fetch ? false : filterOption"
					:mode="item.multiple ? 'multiple' : 'default'"
					@change="selectListValu"
					@search="(value) => (item.fetch ? fetch(value, item) : null)"
					@getPopupContainer="
						() => document.getElementById(`form-item-${item.prop}`)
					"
					@dropdownVisibleChange="
						(open) => dropdownVisibleChange(open, item.defaultOptions)
					"
				>
					<a-spin v-if="fetching" slot="notFoundContent" size="small" />
					<a-select-option
						v-for="(val, index) in item.fetch ? fetchedData : item.options"
						:key="index"
						:value="val.value"
					>
						{{ val.label }}
					</a-select-option>
				</a-select>
				<!-- checkbox多选 -->
				<a-checkbox-group
					v-else-if="item.type == 'checkbox'"
					v-model="model[item.prop]"
				>
							<a-checkbox style="width:48%;margin-left: 0px;padding:5px 10px;background: rgba(30,32,42,0.04);margin:0 8px 8px 0"
								v-for="(val, item) in item.options"
								:key="item"
								:value="val.value"
								:disabled="val.disabled || false"
							>
								{{ val.label}}
							</a-checkbox>
				</a-checkbox-group>
				<!-- radio 单选 -->
				<a-radio-group
					v-else-if="item.type == 'radio'"
					v-model="model[item.prop]"
					:disabled="item.disabled"
				>
					<a-radio
						v-for="(val, index) in item.radios"
						:key="index"
						:value="val.value"
					>
						{{ val.label }}
					</a-radio>
				</a-radio-group>
				<!-- transfer 穿梭框 -->
				<a-transfer
					v-else-if="item.type == 'transfer'"
					:data-source="item.dataSource"
					:titles="item.titles"
					show-search
					:filter-option="
						(inputValue, option) => option.title.indexOf(inputValue) > -1
					"
					:target-keys="item.targetKeys"
					:render="(val) => val.title"
					@change="
						(nextTargetKeys, direction, moveKeys) =>
							transferChange(nextTargetKeys, direction, moveKeys, index)
					"
					@search="(direction, value) => null"
				/>
				<span slot="help" v-if="item.useHelp && help" style="color: red">{{
					help
				}}</span>
				<!-- cascader 联级选择 -->
				<a-cascader
					v-else-if="item.type == 'cascader'"
					:options="item.cascaVal"
					v-model="model[item.prop]"
					:load-data="loadData"
					change-on-select
					:defaultValue="item.value || (() => [])"
					:placeholder="item.placeholder || '请选择'"
					@change="onChangeCascader"
				/>
			</a-form-model-item>
		</a-form-model>
	</a-modal>
</template>

<script>

// 数组、字母2-20位
let validPassword = (rule, value, callback) => {
	let reg = /^[0-9a-zA-Z]{2,20}$/
	if (!reg.test(value)) {
		callback(new Error("请输入2-20位字母、数字组合"))
	} else {
		callback()
	}
}
// let requerTarLength = (rule, value, callback) => {
// 	if (value.length>50) {
// 		callback(new Error("请输入50位以内的字符"))
// 	} else {
// 		callback()
// 	}
// }
// 数组、字母、中文1-20位
let validRequre = (rule, value, callback) => {
	let reg = /^[\u4E00-\u9FA5A-Za-z0-9]{1,20}$/
	if (!reg.test(value)) {
		callback(new Error("请输入1-20位字母、数字或中文!"))
	} else {
		callback()
	}
}
// 中文1-20位
let validChinese = (rule, value, callback) => {
	let reg = /^[\u4E00-\u9FA5]{1,20}$/
	if (!reg.test(value)) {
		callback(new Error("请输入1-20位中文"))
	} else {
		callback()
	}
}
//ip校验
let validIpCode = (rule, value, callback) => {
	let reg =
		/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
	if (value && !reg.test(value)) {
		callback(new Error("请输入正确的IP地址"))
	} else {
		callback()
	}
}
// 英文1-20位
let validEnglish = (rule, value, callback) => {
	let reg = /^[A-Za-z]{1,20}$/
	if (!reg.test(value)) {
		callback(new Error("请输入1-20位字母"))
	} else {
		callback()
	}
}
//50以内
let ValidLength = (rule, value, callback) => {
	console.log(value)
	if (value && value.length > 50) {
		callback(new Error("请输入不超过50个字符"))
	} else {
		callback()
	}
}
//40位以内的字符
let NumbeValidEzLength = (rule, value, callback) => {
	console.log(value)
	if (value && value.length > 40) {
		callback(new Error("请输入不超过40个字符"))
	} else {
		callback()
	}
}
let NumbeLength = (rule, value, callback) => {
	console.log(value)
	if (value && value.length > 20) {
		callback(new Error("请输入不超过20个字符"))
	} else {
		callback()
	}
}
// 数字0-99
let validNmuber = (rule, value, callback) => {
	let reg = /^[0-9]{1,2}$/
	if (!reg.test(value)) {
		callback(new Error("请输入1-99的数字"))
	} else {
		callback()
	}
}
export default {
	name: "form-modal",

	props: {
		title: {
			type: String,
		},
		width: {
			type: Number,
			default: 608,
		},
		confirmLoadingStatus: {
			type: Boolean,
		},
		okText: {
			type: String,
			default:  function(){return this.$t('btn_ok')}  
		},
		cancelText: {
			type: String,
			default:  function(){return this.$t('btn_cancel')}  
		},
		visible: {
			type: Boolean,
		},
		// 生成表单域的配置项
		/**
		 * [
		 *  {
		 *      ******************** 必须包含项 ****************************
		 *      label: '名称',
		 *      type: 'input' || 'textarea' || 'select' || 'checkbox'（暂无该类型） || 'radio' || 'transfer' || 'cascader' || 'tree-select',
		 *      prop: '', // 数据字段，根据该字段生成 数据对象 和 校验规则
		 *
		 *      // select 下拉，当 type == 'select'，需传该下拉项
		 *      options: [
		 *          { label: '', value: xxx }
		 *      ],
		 *      defaultOptions: Array, // 远程搜索时 下拉列表中的默认选项
		 *      multiple: true, // 是否支持多选，默认为单选
		 *
		 *      // radio 选项，当 type == 'radio'，需传该可选项
		 *      radios: [
		 *          { label: '', value: xxx }
		 *      ],
		 *
		 *      // transfer 选项，当 type == 'transfer'，需传该可选项
		 *      dataSource: [
		 *          { key: '权限id', title: xxx, description: '', disabled: Boolean }
		 *      ],
		 *      titles: ['xx', 'xx'],
		 *      targetKeys: [], // 右侧已选择的项 key
		 *
		 *      // tree-select 树形下拉选择
		 *      dataSource: Array,
		 *      loadData: Function 点击树节点展开按钮，动态加载子节点（可选）
		 *      replaceFields: Object, 替换 treeNode 中 title,key,children 字段为 treeData 中对应的字段
		 *      ***********************************************************
		 *
		 *      fetch: ModelFactory, // 输入框文本变化时，远程搜索接口
		 *      useHelp： Boolean, 是否使用自定义校验并添加校验反馈提示文本 help，默认为 false，使用 rule 校验。
		 *      placeholder: String, // 提示文本
		 *      block: Boolean, // 是否独占一行，默认为 false
		 *      disabled: Boolean, // 是否禁用，默认为 false
		 *      value: any, // 默认值，当 type == 'transfer' 时，该字段为 targetKeys
		 *      required: Boolean, // 是否必填，默认为 true
		 *      change: Function, // 表单项-值 变化时的回调
		 *      rule: [{ min: 1, max: 20 }], // 校验规则，当 type == 'transfer' 时，传入该字段暂时无效
		 *		confirmLoadingStatus:Boolean //给按钮添加loading状态防止重复提交，开启为true，在接口回调后设置为false即可关闭
		 *  }
		 * ]
		 */
		dataSource: {
			type: Array,
		},
		// 表单布局
		layout: {
			type: String,
			default: "vertical",
		},
	},

	components: {},

	data() {
		return {
			vis: false,
			model: {}, // 数据对象

			help: "", // cascader 类型，校验提示文本

			fetching: false, // 是否正在搜索
			fetch: _.debounce(this._fetch, 500), // 防抖
			fetchedData: [],

			validateInteger: _.debounce(this._validateInteger, 200),
			ipRange: {
				0: "",
				1: "",
				2: "",
			},
			endIpRange: {
				0: "",
				1: "",
				2: "",
			},
			startIPLastPos: "",
			endIPLastPos: "",
		}
	},
	created() {
		// 我被调用了
		// console.log('我被调用了',window.localStorage.getItem('deep'))
	},
	watch: {
		dataSource: {
			// immediate: true,
			// deep:true,
			handler(val) {
				// 根据每个表单域的 prop 字段，生成对应的数据对象
				const obj = {}
				val.forEach((v) => {
					obj[v.prop] = v.type == "transfer" ? [...v.targetKeys] : v.value

					// 属性值变化时，执行回调
					if (v.change instanceof Function)
						this.$watch(`model.${v.prop}`, function (newVal, oldVal) {
							v.change(newVal, oldVal)
						})

					// 设置 远程搜索列表 默认选项
					if (v.fetch && v.defaultOptions) this.fetchedData = v.defaultOptions

					// 设置 ip 起始段 默认值
					if (v.type == "ip-range") {
						console.log(v)
						const ips = (v.value || "").split("-"),
							pos = ips[0].split(".")
						const der = ips[1] ? ips[1].split(".") : pos
						// console.log(pos,endps)
						for (const key in this.ipRange) {
							if (Object.hasOwnProperty.call(this.ipRange, key)) {
								this.$set(this.ipRange, key, pos[key])
							}
						}
						for (const key in this.endIpRange) {
							if (Object.hasOwnProperty.call(this.endIpRange, key)) {
								this.$set(this.endIpRange, key, der[key])
							}
						}

						this.startIPLastPos = pos[pos.length - 1]

						// 结束ip 存在
						if (ips[1]) {
							const der = ips[1].split(".")

							this.endIPLastPos = der[der.length - 1]
							console.log(der)
							// this.endIpRange = pos
						} else {
							this.endIPLastPos = der[der.length - 1]
						}
					}
				})

				this.model = obj

				// console.log('formItem.dataSource change:', val, 'model：', this.model);
			},
		},
		visible(val) {
			//    val?this._fetch():''
			if (this.vis != val) this.vis = val
		},
		vis(val) {
			// 关闭时重置表单为初始值并且移除校验结果

			if (!val) {
				this.$emit("update:visible", false)
				this.$refs["form-modal"].resetFields()
				this.help = ""
			}
		},
	},

	computed: {
		// 根据每个表单域的 prop 字段，生成对应的 校验规则
		rules() {
			const obj = {}
			this.dataSource.forEach((v) => {
				let ruler = []

				// 使用传入规则
				if (v.rule instanceof Array) ruler = v.rule
				else {
					// if(v.codeType=='ipFig'){
					// 	console.log(v)
					// }

					if (v.codeType == "ipCode") {
						// if(){

						// }
						ruler = [
							{
								required: v.required != false,
								message: "请输入",
								trigger: "blur",
							},
							{ validator: validIpCode, trigger: "blur" },
						]
					}
					if (v.codeType == "code") {
						ruler = [
							{
								required: v.required != false,
								message: "请输入",
								trigger: "blur",
							},
							{ validator: validPassword, trigger: "blur" },
						]
					}
					if (v.codeType == "validChinese") {
						ruler = [
							{
								required: v.required != false,
								message: "请输入",
								trigger: "blur",
							},
							{ validator: validChinese, trigger: "blur" },
						]
					}
					if (v.codeType == "NumbeLength") {
						ruler = [
							{
								required: v.required != false,
								message: "请输入",
								trigger: "blur",
							},
							{ validator: NumbeLength, trigger: "blur" },
						]
					}
					if (v.codeType == "validEnglish") {
						ruler = [
							{
								required: v.required != false,
								message: "请输入",
								trigger: "blur",
							},
							{ validator: validEnglish, trigger: "blur" },
						]
					}
					if (v.codeType == "ValidLength") {
						console.log(v.value)
						ruler = [
							{
								required: v.required != false,
								message: "请输入",
								trigger: "blur",
							},
							{ validator: ValidLength, trigger: "blur" },
						]
					}
					if (v.codeType == "NumbeValidEzLength") {
						console.log(v.value)
						ruler = [
							{
								required: v.required != false,
								message: "请输入",
								trigger: "blur",
							},
							{ validator: NumbeValidEzLength, trigger: "blur" },
						]
					}
					// if(v.codeType=='textareaIp'){
					// 	ruler = [

					// 		{ required: v.required != false, message: "请输入50个以内的字符", trigger: "blur" },
					// 		{ validator: requerTarLength, trigger: "blur" },
					// 	]
					// }
					if (v.codeType == "enNumberEz") {
						ruler = [
							{
								required: v.required != false,
								message: "请输入",
								trigger: "blur",
							},
							{ validator: validRequre, trigger: "blur" },
						]
					}
					if (v.codeType == "validNmuber") {
						ruler = [
							{
								required: v.required != false,
								message: "请输入",
								trigger: "blur",
							},
							{ validator: validNmuber, trigger: "blur" },
						]
					}
					// transfer 类型时，使用此处校验规则（form-model 不支持 transfer 组件的 v-model 校验？）
					if (v.type == "transfer") {
						/*
                        ruler = [{
                            required: v.required != false,
                            validator: (rule, value, callback) => {
                                console.log('transfer validator:', rule, value, this.dataSource)
                                if (!value.length)
                                    return callback('请至少选择一条数据');

                                callback();
                            }   
                        }];
                        // */
					} else {
						ruler.push({
							required: v.required != false,
							message:
								v.type == "input"
									? `请输入`
									: v.type == "textarea"
									? "请输入"
									: "请选择",
						})

						if (v.type == "input")
							ruler.push({
								whitespace: true,
							})
					}
				}

				obj[v.prop] = ruler
			})

			// console.log('%c 根据 dataSource，求取 rules：', 'color:green', obj);
			return obj
		},
	},

	methods: {
		//下拉框选中的值回调
		selectListValu(e) {
			this.$emit("selectListValue", e)
		},
		// input输入框数据
		inputValues(e) {
			// console.log(e.target.value)
			this.$emit("inputValues", e.target.value)
		},
		//树选择
		treeSelect(value, label, extra){
			this.$emit("treeSelect",value, label, extra)
		},
		// 远程搜索
		_fetch(value, { fetch, defaultOptions }) {
			const modelFactory = fetch
			// console.log('search :', value, "");

			this.fetching = true

			modelFactory
				.execute({ page: 1, size: 500, searchKey: (value || "").trim() })
				.then((res) => {
					// if (defaultOptions)
					//     this.fetchedData = defaultOptions;

					this.fetchedData = this.fetchedData.concat(
						res.rows.map((t) => ({ label: t.name, value: t.oid }))
					)

					this.fetching = false
				})
				.catch((err) => (this.fetching = false))
		},

		// select 本地过滤
		filterOption(input, option) {
			return (
				option.componentOptions.children[0].text
					.toLowerCase()
					.indexOf(input.toLowerCase()) >= 0
			)
		},

		// select 下拉菜单展开收起
		dropdownVisibleChange(open, defaultOptions) {
			// console.log("dropdownVisibleChange :", open, defaultOptions)
			// if (open && defaultOptions)
			//     this.fetchedData = defaultOptions;
		},

		//远程动态搜索cascader
		loadData(selectedOptions) {
			const targetOption = selectedOptions[selectedOptions.length - 1]
			targetOption.loading = true

			// load options lazily
			setTimeout(() => {
				targetOption.loading = false
				targetOption.children = [
					{
						label: `${targetOption.label} Dynamic 1`,
						value: "dynamic1",
					},
					{
						label: `${targetOption.label} Dynamic 2`,
						value: "dynamic2",
					},
				]
				// this.options = [...this.options];
			}, 1000)
		},
		//onChangeCascader
		onChangeCascader(value, selectedOptions) {
			// console.log(value, selectedOptions)
			this.$emit("onChangeCascader", value, selectedOptions)
		},

		// 穿梭框左右项变化
		transferChange(nextTargetKeys, direction, moveKeys, index) {
			// 必须要设置 targetKeys，才会渲染右侧已选项
			this.dataSource[index].targetKeys = nextTargetKeys
			if (nextTargetKeys.length) this.help = ""
			this.model.permissionGroup = nextTargetKeys
			// console.log('右侧已选择的项：', nextTargetKeys, 'direction：', direction, 'moveKeys: ', moveKeys, '\ndataSource：', this.model, index)
		},

		_validateInteger(value, attr, type) {
			let val = value > 255 ? 255 : value < 0 ? 0 : value

			if ((value || "").trim() == "" || /^[0-9]{1,3}$/.test(value)) {
				if (attr == "startIPLastPos" || attr == "endIPLastPos") {
					this[attr] = val
				} else if (type == "endIp") {
					this.endIpRange[attr] = val
				} else {
					this.endIpRange[attr] = val
					this.ipRange[attr] = val
				}

				for (const key in this.ipRange) {
					if (Object.hasOwnProperty.call(this.ipRange, key)) {
						const element = this.ipRange[key]
						if (!element) return
					}
				}

				if (this.startIPLastPos || this.endIPLastPos) this.help = ""
			}
		},

		// 确认
		confirm() {
			this.$refs["form-modal"].validate((valid) => {
				const cas = this.dataSource.find((v) => v.type == "transfer")

				// 穿梭框右侧是否有选中项 校验
				// if (cas) {
				// 	if (!cas.targetKeys.length) return (this.help = "请至少选择一条数据")
				// 	else this.help = ""
				// }

				const startIPLastPos = (this.startIPLastPos || "").trim(),
					endIPLastPos = (this.endIPLastPos || "").trim()
				// ip 校验
				if ("ip" in this.model) {
					for (const key in this.ipRange) {
						if (Object.hasOwnProperty.call(this.ipRange, key)) {
							const element = this.ipRange[key]
							if (!element) return (this.help = "请输入完整IP地址")
						}
					}
					if (!startIPLastPos && !endIPLastPos)
						return (this.help = "请输入完整IP地址")
					else this.help = ""
				}

				if (valid) {
					if ("ip" in this.model) {
						let str = ""
						for (const key in this.ipRange) {
							if (Object.hasOwnProperty.call(this.ipRange, key)) {
								const element = this.ipRange[key]
								str += element + "."
							}
						}

						if (
							!startIPLastPos ||
							!endIPLastPos
							// ||startIPLastPos == endIPLastPos
						)
							str += startIPLastPos || endIPLastPos
						else str += startIPLastPos + "-" + str + endIPLastPos
						let strIp = str.split("-")
						let a = 0,
							b = 0
						let s = this.endIpRange
						let t = []
						let start = strIp[0].split(".")
						let end = (strIp[1] || "").split(".")
						if (start) {
							for (let i in start) {
								if (start[i] < 10) {
									start[i] = `00${start[i]}`
								} else if (start[i] < 100 && start[i] >= 10) {
									start[i] = `0${start[i]}`
								}
								a += start[i]
							}
						}

						for (let m in s) {
							t.push(s[m])
						}
						let endI = end[3] ? t.concat(end[3]) : t
						let opp = end[3] ? t.concat(end[3]) : t
						if (endI) {
							for (let k in endI) {
								console.log(`${endI[k]}`.length)
								if (`${endI[k]}`.length == 1) {
									endI[k] = `00${endI[k]}`
								} else if (`${endI[k]}`.length == 2) {
									endI[k] = `0${endI[k]}`
								}
								b += endI[k]
							}
						}

						let tr = str.split("-")
						str = tr[0] + "-" + opp.toString().replaceAll(",", ".")
						console.log(Number(a))
						console.log(Number(b))
						if (b && Number(a) > Number(b)) {
							return (this.help = "请输入正确的IP地址！")
						} else {
							this.help = ""
							this.model.ip = str
						}
					}
					//给按钮添加loading状态防止重复提交，在接口回调后设置为false即可
					// this.confirmLoadingStatus = true
					this.$emit("confirm", this.model)
				}
			})
		},
		//关闭回掉
		cancelClick() {
			this.$emit("cancelBack")
		},
	},
}
</script>
<style scoped>
/deep/.ant-checkbox-group{
	width: 100%!important;
}
</style>
<style lang="less">
.seacret {
	display: inline-block;
	font-size: 14px;
	margin-bottom: 10px;
	color: rgba(0, 0, 0, 0.85);
}

.jw-common-form-modal {
	.ant-modal-body {
		.ant-form {
			.ant-form-item {
				width: calc(50% - 4px);
				display: inline-block;

				&:nth-of-type(odd) {
					margin-right: 8px;
				}

				&.block {
					width: 100%;
					display: block;
				}

				.ip-range {
					.ant-col {
						.inner-box {
							border: 1px solid #d9d9d9;
							border-radius: 4px;
							// display: flex;
							overflow: hidden;
							height: 32px;

							.item-box {
								display: inline-block;
								overflow: hidden;
								width: 25%;
								height: 32px;
								position: relative;

								&:nth-of-type(2),
								&:nth-of-type(3),
								&:nth-of-type(4) {
									margin-left: -2px;
								}

								.ant-input {
									border: none;
									outline: none;
									text-align: center;
									border-radius: 0;

									&:focus {
										border: none !important;
									}
								}

								.split-pointer {
									position: absolute;
									right: 2px;
									bottom: 2px;
									width: 4px;
									display: inline-block;
								}
							}
						}
					}
				}
			}
		}
	}
}
</style>
