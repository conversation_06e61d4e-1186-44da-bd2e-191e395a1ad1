import ModelFactory from "jw_apis/model-factory"
// 获取antion列表
const getAttributeGroupList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/searchActionList`,
    method: "get"
});
// antion保存
const saveAttributeGroupJson = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/save`,
    method: "post"
});
export {
    getAttributeGroupList,
    saveAttributeGroupJson
}