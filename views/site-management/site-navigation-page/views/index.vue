<template>
  <div class="h-full all-background">
    <a-row :gutter="[16, 10]" style="margin: 0;height: 100%;">
      <!-- Sider -->
      <a-col :span="6" style="">
        <div style="
						height: 100%;
						background: var(--light);
						box-shadow: 0 2px 8px 0 rgb(30 32 42 / 25%);
					">
          <div style="
							padding: 10px 0 10px 20px;
							display: flex;
							justify-content: space-between;
							align-items: center;
							border-bottom: 1px solid #ccc;
						">
            actionList
            <a-button @click="addList" type="link">
              {{$t('btn_new_create')}}
              <!-- <a-icon type="plus" /> -->
            </a-button>
          </div>
          <div>
            <a-tree class="draggable-tree" :default-selected-keys="defaultCheckedKeys" :tree-data="treeData" @select="onDragEnter" @drop="onDrop" />
            <!-- <div class="action" v-for="(item,index) in data" :key="index">{{item.title}}</div> -->
          </div>
        </div>
      </a-col>
      <!-- Content -->
      <a-col :span="18" style='height: 100%;'>
        <div class="main-info" style='height: 100%;'>
          <div style="
							padding: 10px 0 10px 20px;
							display: flex;
							justify-content: space-between;
							align-items: center;
							border-bottom: 1px solid #ccc;
						">
            {{ title }}
          </div>
          <a-input style="height: 70%; margin: 20px; width: 96%" v-model.trim="details" :placeholder="$t('txt_input')" type="textarea"></a-input>
          <a-button type="" @click="saveJson" style="margin: 20px">{{$t('btn_save')}}</a-button>
        </div>
      </a-col>
    </a-row>
    <!-- 表单录入弹窗 -->
    <form-modal :title="$t('btn_new_create')" :okText="$t('btn_save')" :data-source="formModalData" :visible.sync="modalVisible" @confirm="modalCallback" />
  </div>
</template>

<script>
import formModal from "../components/form-modal.vue";
// import treeSearchSider from "../components/treeSearchSider.vue";

import { getAttributeGroupList, saveAttributeGroupJson } from "../apis";

export default {
  components: {
    formModal,
    // treeSearchSider
  },
  data() {
    return {
      defaultCheckedKeys: [],
      details: null,
      treeData: [],
      modalVisible: false,
      oid: "",
      title: "",
      query: [],
      formModalData: [
        {
          label: this.$t('txt_plan_name'),
          type: "input",
          value: "",
          block: false,
          prop: "name"
        },
        {
          label: this.$t('table_identifier'),
          type: "input",
          value: "",
          block: false,
          prop: "viewCode"
        },
        {
          label: this.$t('txt_description'),
          type: "input",
          value: "",
          block: true,
          required: false,
          prop: "description"
        },
        {
          label: "JSON list",
          type: "textarea",
          required: false,
          value: "",
          block: true,
          prop: "actionList"
        }
      ]
    };
  },

  computed: {},

  created() {
    this.getActionList();
  },

  methods: {
    //获取action 列表
    getActionList() {
      getAttributeGroupList.execute({ currentPage: 1, size: 500 }).then(res => {
        this.treeData = res.rows.map(v => ({
          title: v.name,
          key: v.oid,
          viewCode: v.viewCode,
          description: v.description,
          filter: v.filter,
          actionList: v.actionList
        }));
        if (this.query.length <= 0) {
          let detail = res.rows[0];
          this.title = res.rows[0].name;
          this.oid = res.rows[0].oid;
          this.defaultCheckedKeys = [res.rows[0].oid];
          console.log(this.defaultCheckedKeys);
          this.details = JSON.stringify(detail, null, "\t") || ""; //转成字符串并格式化
        }
      });
    },
    // 选中的树节点发生变化
    nodeSelect(selectedKeys, event) {
      if (selectedKeys && selectedKeys[0]) {
        // let detail = res.rows[0]
        // this.oid = res.rows[0].oid
        // this.details = JSON.stringify(detail, null, "\t") || "" //转成字符串并格式化
      }
    },
    //保存json
    saveJson() {
      let arr = this.details;

      // console.log(arr,obj)
      if (typeof arr == "string") {
        try {
          let obj = JSON.parse(arr);
          if (typeof obj == "object" && obj) {
            let query = {
              oid: obj.oid || "",
              name: obj.name,
              viewCode: obj.viewCode,
              description: obj.description,
              filter: obj.filter,
              actionList: obj.actionList
            };

            saveAttributeGroupJson
              .execute(query)
              .then(res => {
                this.$success(this.$t('msg_save_success'));
                this.getActionList();
              })
              .catch(err => {
                this.$error(err.msg || this.$t('msg_failed'));
              });
          } else {
            this.$error("json格式有误，请检查格式");
          }
        } catch (e) {
          this.$error("json格式有误，请检查格式");
        }
      }
    },
    //打开添加弹框
    addList() {
      this.modalVisible = true;
    },
    //保存新增
    modalCallback(model) {
      saveAttributeGroupJson
        .execute(model)
        .then(res => {
          this.$success(this.$t('msg_save_success'));
          this.getActionList();
          this.modalVisible = false;
        })
        .catch(err => {
          this.$error(err.msg || this.$t('msg_failed'));
        });
    },
    onDragEnter(info, e) {
      this.oid = e.node._props.dataRef.key;
      this.title = e.node._props.dataRef.title;
      let query = {
        oid: e.node._props.dataRef.key,
        name: e.node._props.dataRef.title,
        viewCode: e.node._props.dataRef.viewCode,
        description: e.node._props.dataRef.description,
        filter: e.node._props.dataRef.filter,
        actionList: e.node._props.dataRef.actionList
      };
      this.query = query;
      console.log(query, e.node._props.dataRef);
      this.details = JSON.stringify(query, null, "\t"); //转成字符串并格式化
    },
    onDrop(info) {
      console.log(info);
    }
  }
};
</script>

<style lang="less" scoped>
.all-background {
  height: 100%;
  background: #fff;
  // box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
  border-radius: 4px;
  padding: 20px;
  ::v-deep.ant-row {
    height: 100%;
    .ant-col {
      height: 100%;
    }
    .main-info{
      height: 100%;
    }
  }
}
.search-info {
  height: 52px;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  border-bottom: 1px solid rgba(30, 32, 42, 0.15);
  align-items: center;

  .search-btn {
    display: flex;
  }
}

.table-col {
  display: inline-block;
  text-indent: 16px;
  &:nth-of-type(1) {
    width: 150px;
  }
  &:nth-of-type(2) {
    width: 180px;
  }
  &:nth-of-type(3) {
    width: 210px;
  }
  &:nth-of-type(4) {
    width: 160px;
  }
  &:nth-of-type(5) {
    width: 190px;
  }
  &:nth-of-type(6) {
    width: 116px;
  }
  &:nth-of-type(7) {
    width: 186px;
  }
}
.table-header {
  height: 54px;
  line-height: 54px;
  margin: 16px 20px;
  background: rgba(30, 32, 42, 0.03);
  font-size: 14px;
  color: rgba(30, 32, 42, 0.85);

  > div {
    .table-col;
  }
}
.table-body {
  height: calc(100% - 52px - 54px - 32px);
  overflow-y: auto;
  margin: 0 20px;
  padding-bottom: 20px;

  .ant-collapse > .ant-collapse-item {
    > .ant-collapse-header {
      height: 44px;
      padding: 0;
      line-height: 44px;
      background: #f0f7ff;
      padding-left: 40px;
      font-size: 14px;
      color: rgba(30, 32, 42, 0.85);
    }
    .ant-collapse-content-box {
      padding: 0;
      overflow-x: auto;

      .table-row {
        background: #fff;
        border-bottom: 1px solid rgba(30, 32, 42, 0.06);
        width: 120%;
        height: 54px;
        line-height: 54px;
        font-size: 14px;
        color: rgba(30, 32, 42, 0.65);
      }
    }
  }
}
.action {
  padding: 5px 20px;
  font-size: 16px;
  cursor: pointer;
}
</style>
