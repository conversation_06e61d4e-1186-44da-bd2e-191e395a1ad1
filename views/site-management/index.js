import { init as initSiteBusinessConfiguration } from "./site-business-configuration";
import { init as initSiteRoleManagement } from "./site-role-management"
import { init as initSiteTeamManagement } from "./site-team-management";
import { init as initSiteTemplate } from "./site-template";
import { init as initSiteUnitManagement } from "./site-unit-management";
import { init as initSiteNavigationPage } from "./site-navigation-page";
import { init as initSiteClassifiyAdmin } from "./site-classifiy-admin";
import { init as initSiteMemberManagement } from "./site-member-management";
import { init as initSiteSetCreator } from "./site-set-creator";
import { init as initSiteSystemAdmin } from "./site-system-admin";
import { init as initSiteSystemRoleUser } from "./site-system-role-user";
import { init as initSiteSystemRole } from "./site-system-role";
import { init as initSiteConversionMonitor } from "./site-conversion-monitor";
import { init as initIntegratedTask } from "./site-integrated-task"

export let init = function () {
    initSiteBusinessConfiguration()
    initSiteRoleManagement()
    initSiteTeamManagement()
    initSiteTemplate()
    initSiteUnitManagement()
    initSiteNavigationPage()
    initSiteClassifiyAdmin()
    initSiteMemberManagement()
    initSiteSetCreator()
    initSiteSystemAdmin()
    initSiteSystemRoleUser()
    initSiteSystemRole()
    initSiteConversionMonitor()
    initIntegratedTask()
}