<template>
  <div>
    <a-row>
      <a-col :span="8" class="tree-list">
        <div class="search-line">
          <a-button type="primary" @click="addRoles">{{
            $t("add_role")
          }}</a-button>
          <a-input-search
            class="search-input"
            v-model.trim="searchRoleKey"
            :placeholder="$t('search_text')"
            @search="onSearchRole"
          />
        </div>
        <a-menu
          class="role-list"
          v-model="selectRoleOid"
          @select="changeUserTable"
        >
          <a-menu-item :key="item.oid" v-for="item in currentRowRole">
            <div class="role-menu-item">
              <jw-icon type="jwi-iconbusiness" />
              {{ item.displayName }}
              <span @click="removerole(item.oid)" class="removeRoleBtn">
                <jw-icon style="float: right" type="jwi-icondelete" />
              </span>
            </div>
          </a-menu-item>
        </a-menu>
      </a-col>
      <a-col :span="16" class="user-table">
        <div class="split-line"></div>
        <div class="role-user-table">
          <div class="table-search-line">
            <a-button type="primary" @click="openUserAdd">
              {{ $t("btn_add_user") }}
            </a-button>

            <a-input-search
              v-model.trim="searchUserKey"
              :placeholder="$t('search_text')"
              style="width: 250px; margin-left: 10px"
              @search="onSearchUser"
            />
          </div>
          <jw-table
            height="600"
            row-id="oid"
            ref="user"
            :showPage="false"
            :columns="userGetHeader"
            :dataSource="currentRowUser"
            @onOperateClick="roleOperateClick"
          >
            <div name="empty" slot="empty"></div>
            <template #name="{ row }">
              <div>
                <jw-avatar :showName="true" :tag="true" :data="row" />
              </div>
            </template>
          </jw-table>
        </div>
      </a-col>
    </a-row>

    <jw-user-modal
      ref="role-modal"
      :isCheckbox="true"
      :visible="roleModalVisible"
      :teamTemplateOid="teamRow.oid"
      :defaultOidValue="defaultRoleOidValue"
      @handleSubmit="roleModalCallback"
    />

    <!-- 表单录入弹窗 -->
    <jw-user-modal
      ref="user-modal"
      :isCheckbox="true"
      :visible="isRoleVisible"
      :defaultOidValue="currentCheckUser"
      @handleSubmit="handleUser"
    />
  </div>
</template>

<script>
import {
  getRoleUserList,
  fetchTeamRole,
  addRoleModel,
  bindUserModel,
  productGetTeamRole,
  productAddRoleModel,
  productBindUserModel,
  productGetTeamRoleUser,
} from "../../apis/index";
import { jwAvatar } from "jw_frame";
import jwUserModalV2 from "components/user-modal-v2";
import { getCookie } from "jw_utils/cookie";
import ModelFactory from "jw_apis/model-factory";
export default {
  components: {
    jwAvatar,
    jwUserModal: jwUserModalV2,
  },
  data() {
    return {
      currentRowUser: [],
      //选中的角色
      selectRoleOid: [],
      searchRoleKey: "",

      searchUserKey: "",

      roleModalVisible: false,

      currentRowRole: [],

      isRoleVisible: false,
      currentCheckUser: [],
      confirmLoadingStatus: false,

      containerOid: "",
    };
  },
  props: {
    teamRow: {
      type: Object,
    },
    type: {
      type: String,
    },
  },
  created() {
    this.getCurrentRowRole(this.teamRow);

    if (!this.validSysTeam()) {
      const { team } = this.$route.query;
      const { oid } = JSON.parse(team);
      this.containerOid = oid;
    }
  },
  watch: {
    currentRowRole: function (val) {
      this.validSelectValue();
    },
  },
  computed: {
    userGetHeader() {
      return [
        {
          field: "name",
          title: this.$t("txt_user_name"),
          slots: {
            default: "name",
          },
        },
        {
          field: "account",
          title: this.$t("txt_account"),
        },
        {
          field: "phone",
          title: this.$t("txt_contact"),
        },
        {
          field: "email",
          title: this.$t("txt_email"),
        },
        {
          // 操作列定义
          field: "operation", //关键字
          title: this.$t("txt_operation"),
          btns: [
            {
              icon: "jwi-icondelete",
              title: this.$t("btn_delete"),
              key: "delete",
            },
          ],
        },
      ];
    },

    defaultRoleOidValue: {
      get() {
        return this.currentRowRole.map((item) => item.sourceOid);
      },
    },
  },
  methods: {
    //验证是否是组织管理的团队
    validSysTeam() {
      return this.type === "team";
    },
    // 角色添加绑定用户
    handleUser(dataSource, roleOid) {
      let tenantOid = getCookie("tenantOid");
      this.confirmLoadingStatus = true;

      let req;
      if (this.validSysTeam()) {
        let param = dataSource.map((item) => {
          return {
            teamTemplateRoleOid: roleOid,
            userOid: item.oid,
            tenantOid: tenantOid,
          };
        });
        req = bindUserModel.execute(param);
      } else {
        let param = {
          teamRoleOid: roleOid,
          containerOid: this.containerOid,
          teamUserList: dataSource.map((item) => {
            return {
              description: item.description,
              oid: item.oid,
              tenantOid: item.tenantOid,
              type: item.type,
              name: item.name,
              account: item.account,
            };
          }),
        };
        req = productBindUserModel.execute(param);
      }
      return req
        .then((data) => {
          this.$success(this.$t("msg_save_success"));
          this.isRoleVisible = false;
          this.reloadUser();
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"));
        })
        .finally(() => {
          this.confirmLoadingStatus = false;
        });
    },
    //打开当前用户
    openUserAdd() {
      if (this.selectRoleOid.length === 0) {
        this.$warning(this.$t("txt_befor_role"));
        return;
      }
      let defaultCheckUserOid = this.currentRowUser.map((item) => item.oid);
      this.executeOpenUser(this.selectRoleOid[0], defaultCheckUserOid);
    },

    //打开添加用户页面
    executeOpenUser(roleOid, checkUserOid) {
      this.currentCheckUser = checkUserOid;
      this.$refs["user-modal"].show({ type: "User" }).then((data) => {
        this.handleUser(data, roleOid);
      });
      this.isRoleVisible = true;
    },
    // 查询当前团队绑定的角色列表
    getCurrentRowRole(row, search) {
      let searchKey = search ? search : "";
      let param = { teamTemplateOid: row.oid, search: searchKey };
      param.containerOid = getCookie("tenantOid");
      param.containerType = "Tenant";

      console.log("团队信息", row);
      let req = this.validSysTeam()
        ? fetchTeamRole.execute(param)
        : productGetTeamRole(row.oid, searchKey);
      req
        .then((data) => {
          //刷新列表
          this.currentRowRole = [...data];
          this.validSelectValue();
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    // 绑定角色
    roleModalCallback(dataSource) {
      let { teamRow } = this;
      this.confirmLoadingStatus = true;
      let req;
      if (this.validSysTeam()) {
        let param = dataSource.map((item) => {
          return {
            teamTemplateOid: teamRow.oid,
            sourceOid: item.oid,
            name: item.name,
            displayName: item.displayName,
            description: item.description,
          };
        });
        req = addRoleModel.execute(param);
      } else {
        let param = {
          teamOid: teamRow.oid,
          teamRoleList: dataSource,
          containerOid: this.containerOid,
        };
        req = productAddRoleModel.execute(param);
      }
      return req
        .then((data) => {
          this.$success(this.$t("msg_save_success"));
          this.roleModalVisible = false;
          this.getCurrentRowRole(teamRow);
          //刷新列表
        })
        .catch((err) => {
          this.confirmLoadingStatus = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    //验证选中的值是否是列表中的角色
    validSelectValue() {
      if (this.currentRowRole.length === 0) {
        this.selectRoleOid = [];
        return;
      }
      if (this.selectRoleOid && this.selectRoleOid.length !== 0) {
        let index = this.currentRowRole.findIndex(
          (item) => item.oid === this.selectRoleOid[0]
        );
        if (index === -1) {
          this.selectRoleOid = [this.currentRowRole[0].oid];
        }
      } else {
        this.selectRoleOid = [this.currentRowRole[0].oid];
      }

      this.loadCurrentUsers(this.selectRoleOid[0], this.searchUserKey);
    },
    addRoles() {
      // 角色处理
      this.$refs["role-modal"].show().then((data) => {
        this.roleModalCallback(data);
      });
    },

    onSearchUser(val) {
      this.reloadUser();
    },
    onSearchRole(val) {
      this.getCurrentRowRole(this.teamRow, this.searchRoleKey);
    },
    //切换左侧角色信息
    changeUserTable(val) {
      this.loadCurrentUsers(val.key, this.searchUserKey);
    },
    reloadUser() {
      if (this.selectRoleOid.length > 0) {
        this.loadCurrentUsers(this.selectRoleOid[0], this.searchUserKey);
      } else {
        this.currentRowUser = [];
      }
    },
    //删除角色
    removerole(keyid) {
      if (this.validSysTeam()) {
        this.onRoleOrUserDelete(keyid, true);
        this.currentRowUser = [];
      } else {
        this.onProductRoleDelete(keyid, true);
      }
    },
    roleOperateClick(key, row) {
      if (this.validSysTeam()) {
        this.onRoleOrUserDelete(row.oid, false);
      } else {
        this.onProductRoleDelete(row.oid, false);
      }
    },
    //加载当前角色用户
    loadCurrentUsers(oid, search) {
      const param = {
        teamTemplateRoleOid: oid,
        search: search ? search : "",
      };
      let req = this.validSysTeam()
        ? getRoleUserList.execute(param)
        : productGetTeamRoleUser(oid, search);
      req
        .then((data) => {
          this.currentRowUser = [...data];
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    // 删除当前团队绑定角色
    onRoleOrUserDelete(oid, isRole) {
      let { teamRow } = this;
      let tenantOid = getCookie("tenantOid");
      let param = {};
      let unBindUrl = "";
      if (isRole) {
        unBindUrl = `${Jw.gateway}/${Jw.accountMicroServer}/team/assign/remove/teamtemplate/teamtemplaterole?teamTemplateOid=${teamRow.oid}&teamTemplateRoleOid=${oid}&tenantOid=${tenantOid}`;
      } else {
        unBindUrl = `${Jw.gateway}/${Jw.accountMicroServer}/team/assign/remove/user/teamtemplaterole?teamTemplateRoleOid=${this.selectRoleOid[0]}&userOid=${oid}&tenantOid=${tenantOid}`;
      }
      this.$confirm({
        class: "deleteModal",
        mask: false,
        title: isRole ? this.$t("btn_delete") : this.$t("msg_role_delete"),
        content: isRole
          ? this.$t("msg_delete_confirm_data")
          : this.$t("msg_confirm_deletion"),
        cancelText: this.$t("btn_cancel"),
        okText: this.$t("btn_ok"),
        onOk: () => {
          ModelFactory.create({
            url: unBindUrl,
            method: "post",
          })
            .execute(param)
            .then((data) => {
              this.$success(this.$t("txt_delete_success"));
              //刷新列表
              if (isRole) {
                this.getCurrentRowRole(this.teamRow, this.searchRoleKey);
              } else {
                this.reloadUser();
              }
            })
            .catch((err) => {
              this.$error(err.msg || this.$t("msg_failed"));
            });
        },
      });
    },

    // 容器产品删除删除当前团队绑定角色
    onProductRoleDelete(roleUserOid, isRole) {
      const { oid } = this.teamRow;
      let unBindUrl = "";
      let param = {};
      if (isRole) {
        unBindUrl = `${Jw.gateway}/${Jw.containerService}/team/unBindRole`;
        param.teamOid = oid;
        param.teamRoleOids = [roleUserOid];
        param.containerOid = this.containerOid;
      } else {
        unBindUrl = `${Jw.gateway}/${Jw.containerService}/team/unBindUser`;
        param.teamRoleOid = this.selectRoleOid[0];
        param.teamUserOidList = [roleUserOid];
        param.containerOid = this.containerOid;
      }
      this.$confirm({
        class: "deleteModal",
        mask: false,
        title: isRole ? this.$t("txt_user_delete") : this.$t("msg_role_delete"),
        content: isRole
          ? this.$t("msg_delete_confirm_data")
          : this.$t("txt_sure_continue"),
        cancelText: this.$t("btn_cancel"),
        okText: this.$t("btn_ok"),
        onOk: () => {
          ModelFactory.create({
            url: unBindUrl,
            method: "post",
          })
            .execute(param)
            .then((data) => {
              this.$success(this.$t("txt_delete_success"));
              //刷新列表
              //刷新列表
              if (isRole) {
                this.getCurrentRowRole(this.teamRow, this.searchRoleKey);
              } else {
                this.reloadUser();
              }
            })
            .catch((err) => {
              this.$error(err.msg || this.$t("msg_failed"));
            })
            .finally(() => {});
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.tree-list {
  padding: 5px 20px 0px 20px;
}
.search-line {
  display: flex !important;
  margin-bottom: 20px;
}

.search-input {
  margin-left: 10px;
}

.ant-menu-inline,
.ant-menu-vertical,
.ant-menu-vertical-left {
  border-right: 0px;
}

.split-line {
  height: calc(100vh - 120px);
  border-right: 1px solid rgba(30, 32, 42, 0.15);
  width: 1px;
  float: left;
  margin-top: -16px;
}

.user-table {
}

.role-user-table {
  padding-left: 20px;
  padding-right: 20px;
}

.table-search-line {
  display: flex;
  margin-top: 5px;
  margin-bottom: 10px;
}

.role-menu-item:hover > .removeRoleBtn {
  visibility: visible;
}

.removeRoleBtn {
  visibility: hidden;
}
.role-list{
  height: calc(~"100vh - 200px");
  overflow: scroll;
}
</style>