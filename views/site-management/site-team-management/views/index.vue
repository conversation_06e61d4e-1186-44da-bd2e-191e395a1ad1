<template>
  <div class="all-background">
    <jw-table
      ref="ref_table"
      disableCheck="disableCheck"
      :data-source.sync="tableData"
      :columns="getHeader"
      :selectedRows.sync="selectedRows"
      :fetch="fetchTable"
      :toolbars="toolbars"
      @onToolClick="onToolClick"
      @onToolInput="onToolInput"
      @checkbox-change="onSelectChange"
      @onOperateClick="onOperateClick"
      :pagerConfig="pagerConfig"
      @onPageChange="onPageChange"
      @onSizeChange="onSizeChange"
    >
      <template #containerModelType="{ row }">
        {{
          row.containerModelType == "Site" ? $t("txe_site") : $t("txe_tenant")
        }}
      </template>
      <template slot="tool-after-start">
        <a-button
          v-show="selectedRows.length > 0"
          style="background: #ffffff; color: #f81d22; text-shadow: none"
          type="danger"
          @click="onToolClick({ key: 'delete' })"
        >
          {{ $t("btn_batch_delete") }}
        </a-button>
      </template>
    </jw-table>
    <!-- 新建团队 -->
    <jw-modal-form
      ref="createTeamModal"
      :key="'1'"
      v-if="modalVisible"
      :visible.sync="modalVisible"
      :title="$t('btn_create_team')"
      :formDatas="addParams"
      :keys="[
        {
          prop: 'name',
          label: $t('btn_team_name'),
          rules: [rules.required],
          props: {
            is: 'a-input',
            value: 'name',
            placeholder: $t('placeholder_name'),
          },
        },
      ]"
      @submit="createTeam"
      @cancel="cancelTeam"
    />
    <jw-modal-form
      :key="'1'"
      v-if="saveAsmodalVisible"
      :visible.sync="saveAsmodalVisible"
      :title="$t('btn_copy_the_team')"
      :formDatas="saveAsParams"
      :confirmText="confirmText"
      @submit="submitSaveAs"
      :keys="[
        {
          prop: 'name',
          label: $t('btn_team_name'),
          rules: [rules.required],
          props: {
            is: 'a-input',
            value: 'name',
            placeholder: $t('placeholder_name'),
          },
        },
      ]"
    />

  </div>
</template>

<script>
//接口
import ModelFactory from "jw_apis/model-factory";
import { jwModalForm, jwAvatar, jwIcon } from "jw_frame";
import { formatDate } from "jw_utils/moment-date";
import { getCookie } from "jw_utils/cookie";
import rules from "jw_frame/utils/rules.js";
// 新增团队
const createTeamModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/create`,
  method: "post",
});


export default {
  components: {
    jwModalForm,
    jwAvatar,
    jwIcon,
    // jwPage,
  },
  inject: ["setBreadcrumb", "addBreadcrumb"],
  data() {
    return {
      saveLoading: false,
      //分页配置
      pagerConfig: {
        current: 1,
        pageSize: 20,
        total: 0,
      },
      activeTab: "1",
      businessType: 1, //1为站点管理，2为组织管理
      drawerStatus: "",
      confirmText: this.$t("btn_save"),
      saveAsmodalVisible: false,
      rules,
      // 角色列表信息
      ischeckBox: true,
      isRoleVisible: false,
      roleTableData: [],
      form: this.$form.createForm(this),
      visible: false,
      deleteModal: false,
      modelName: "Baseline",
      searchKey: "",
      tableData: [],
      selectedRows: [],
      modalVisible: false,
      addVisible: "",
      confirmLoadingStatus: false,
      // 另存团队
      saveAsParams: {
        name: "",
      },
      // 新增团队表单信息
      addParams: {
        name: "",
        enName: "",
        russiaName: "",
      },
      formModalData: [
        {
          label: this.$t("txt_name"),
          type: "input",
          prop: "name",
          block: true,
          required: false,
          useHelp: true,
        },
        {
          label: this.$t("btn_english_name"),
          type: "input",
          codeType: "textareaIp",
          required: false,
          maxLength: 50,
          prop: "comment",
          block: true,
        },
        {
          label: this.$t("btn_e_name"),
          type: "input",
          codeType: "textareaIp",
          required: false,
          maxLength: 50,
          prop: "comment",
          block: true,
        },
      ],
      roleModalData: [
        {
          label: this.$t("btn_users"),
          type: "select",
          prop: "user",
          value: [],
          multiple: "multiple",
          showSearch: true, //是否开启搜索功能
          options: [],
        },
      ],
      roleModalVisible: false,
      tableLoading: false,
      total: 0,
      currentRowOid: "", // 当前编辑项的 id
      currentRow: {},
      // 查询当前团队下的角色
      currentRowData: {},
      currentRowRole: [],
      //当前默认选中user
      currentCheckUser: [],
    };
  },
  computed: {
    defaultRoleOidValue: {
      get() {
        return this.currentRowRole.map((item) => item.sourceOid);
      },
    },
    getHeader() {
      return [
        {
          field: "name",
          title: this.$t("btn_team_name"),
          sortable: true, // 开启排序
        },
        {
          field: "description",
          title: this.$t("txt_description"),
          sortable: true, // 开启排序
        },

        {
          field: "updateBy",
          title: this.$t("修改人"),
          width: "160",
          sortable: true // 开启排序
        },
        {
          field: "updateDate",
          title: "修改时间",
          width: "160",
          formatter: "date"
        },
        {
          // 操作列定义
          field: "operation", //关键字
          title: this.$t("txt_operation"),
          btns: [
            {
              icon: "jwi-iconedit",
              title: this.$t("btn_edit"),
              key: "edit",
            },
            {
              icon: "jwi-iconsave-as",
              title: this.$t("btn_copy_the_team"),
              key: "saveAs",
            },
            {
              icon: "jwi-icondelete",
              title: this.$t("btn_delete"),
              key: "delete",
            },
          ],
        },
      ];
    },
    toolbars() {
      return [
        {
          name: this.$t("btn_new_create"),
          position: "before",
          type: "primary",
          key: "create",
          // prefixIcon: "jwi-plus",
        },
        {
          name: this.$t("btn_search"),
          position: "before",
          display: "input",
          value: this.searchKey,
          allowClear: false,
          placeholder: this.$t("search_text"),
          suffixIcon: "jwi-iconsearch",
          key: "search",
        },
      ];
    },
  },
  created() {
    // 输入回调去抖动
    this.businessType = this.$route.query.type || 0;
    this.delaySearch = _.debounce(this.onSearch, 500);
    this.initBreadcrumb();
    // window.localStorage.setItem('deep',true)
  },

  methods: {
    // 切换tab
    tabCallBack(event) {
      this.activeTab = event;
    },
    // 时间格式化转换
    formatDateFn(date) {
      return formatDate(date);
    },
    // 处理name编辑
    handleChange(event) {
      let value = event.target.value;
      this.form.setFieldsValue({ name: value });
    },

    // 团队启用
    switchClick(row) {
      let param = {
        oid: row.oid,
        disabled: row.disabled,
        containerOid: getCookie("tenantOid"),
        containerModelType: "Tenant",
      };
      ModelFactory.create({
        url: `${Jw.gateway}/${Jw.containerCatalogService}/team/setDisabled`,
        method: "post",
      })
        .execute(param)
        .then((data) => {
          this.$success(this.$t("msg_save_success"));
          //刷新列表
          this.fetchTable({ current: 1, pageSize: 10 });
        })
        .catch((err) => {
          //刷新列表
          this.fetchTable({ current: 1, pageSize: 10 });
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },

    // 创建团队
    createTeam(params) {
      let tenantOid = getCookie("tenantOid");
      let param = {
        containerOid: getCookie("tenantOid"),
        containerModelType: "Tenant",
        name: params.name,
        description: "",
        disabled: true,
      };
      return createTeamModel
        .execute(param)
        .then((data) => {
          this.modalVisible = false;
          this.$success(this.$t("msg_save_success"));
          //刷新列表
          this.fetchTable({ current: 1, pageSize: 10 });
        })
        .catch((err) => {
          this.tableLoading = false;
          this.$refs.createTeamModal.confirmLoading = false
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    // 关闭团队新增弹窗
    cancelTeam() {
      this.modalVisible = false;
    },

    // 选择列回调
    onSelectChange(args) {
      this.selectedRows = [...args];
    },

    // 操作列回调
    onOperateClick(key, row) {
      this.currentRow = { ...row };
      this.form.setFieldsValue({ name: row.name });
      this.form.setFieldsValue({ description: row.description });
      this.form.setFieldsValue({ updateBy: row.updateBy });
      this.form.setFieldsValue({ dateTime: this.formatDateFn(row.updateDate) });
      if (key === "edit") {
        this.$router.push({
          path: "site-team-management-view",
          query: {
            drawerStatus: key,
            type: "team",
            team: JSON.stringify(this.currentRow),
          },
        });
      } else if (key === "detail") {
        this.showDrawer();
        this.activeTab = "1";
        this.drawerStatus = "detail";
        this.getCurrentRowRole(row);
      } else if (key === "addRole") {
        this.showDrawer();
        this.activeTab = "2";
        this.drawerStatus = "detail";
        this.getCurrentRowRole(row);
      } else if (key === "saveAs") {
        this.saveAsmodalVisible = true;
      } else if (key === "delete") {
        this.onDelete(row);
      }
    },
    // 工具栏点击回调
    onToolClick({ key }) {
      if (key === "create") {
        //
        this.addVisible = "create";
        this.modalVisible = true;
      } else if (key === "compare") {
        //
      } else if (key === "delete") {
        let { selectedRows } = this;
        this.fetchDelete(selectedRows);
      }
    },
    // 工具栏输入回调
    onToolInput({ key }, value) {
      if (key === "search") {
        this.searchKey = value;
        this.delaySearch();
      }
    },
    // 删除团队
    onDelete(row) {
      this.fetchDelete([row]);
    },
    // 数据请求函数
    fetchTable() {
      let { searchKey } = this;
      let current = this.pagerConfig.current;
      let pageSize = this.pagerConfig.pageSize;
      let tenantOid = getCookie("tenantOid");
      let param = {
        containerOid: getCookie("tenantOid"),
        containerType: "Tenant",
        keyword: searchKey, // 模糊搜索条件
        pageNum: current,
        pageSize: pageSize,
      };
      this.tableLoading = true;
      return ModelFactory.create({
        // url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/search/keyword`,
        url: `${Jw.gateway}/${Jw.accountMicroServer}/team/search/cp/keyword/page`,
        method: "post",
      })
        .execute(param)
        .then((data) => {
          this.tableLoading = false;
          this.tableData = data.rows;
          this.pagerConfig.total = data.count;
          return { data: data.rows, total: data.count };
        })
        .catch((err) => {
          this.tableLoading = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    //分页操作
    onPageChange(page, pageSize) {
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;
      // this.fetchTable({ current: page, pageSize: pageSize });
    },
    onSizeChange(pageSize, page) {
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;
      // this.fetchTable({ current: page, pageSize: pageSize });
    },
    initBreadcrumb() {
      let breadcrumbData = [
        {
          name: this.$t("page_team_management"),
          path: "/site-team-management",
        },
      ];
      this.setBreadcrumb(breadcrumbData);
    },
    // 删除团队
    fetchDelete(row) {
      let paramData = row.map((item) => item.oid);
      let param = {
        teamTemplateOid: paramData[0],
      };
      this.$confirm({
        width: "280px",
        class: "deleteModal",
        closable: true,
        mask: false,
        title: (
          <p style="font-size:16px;font-weight:500;color:rgba(30, 32, 42, 0.85);">
            {this.$t("team_delete")}
          </p>
        ),
        content: (
          <p style="font-size:14px;font-weight:400;color:rgba(30, 32, 42, 0.45);">
            {this.$t("msg_confirm_delete")}
          </p>
        ),
        cancelText: this.$t("btn_cancel"),
        okText: this.$t("btn_confirm"),
        onOk: () => {
          ModelFactory.create({
            url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/delete?teamTemplateOid=${paramData[0]}`,
            method: "post",
          })
            .execute(param)
            .then((data) => {
              this.$success(this.$t("txt_delete_success"));
              //刷新列表
              this.pagerConfig = {
                current: 1,
                pageSize: 20,
                total: 0,
              };
              this.fetchTable({ current: 1, pageSize: 10 });
              this.selectedRows = [];
            })
            .catch((err) => {
              this.$error(err.msg || this.$t("msg_failed"));
            });
        },
      });
    },
    // 输入回调刷新表格数据
    onSearch() {
      this.$refs.ref_table.reFetchData();
    },
    // 团队另存为
    submitSaveAs(data) {
      let { currentRow } = this;
      let tenantOid = getCookie("tenantOid");
      let param = {
        oldTeamTemplateOid: currentRow.oid,
        teamTemplateName: data.name,
        containerOid: getCookie("tenantOid"),
        containerModelType: "Tenant",
      };
      return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/saveas?oldTeamTemplateOid=${currentRow.oid}&teamTemplateName=${data.name}`,
        method: "post",
      })
        .execute(param)
        .then((data) => {
          this.saveAsmodalVisible = false;
          this.$success(this.$t("msg_save_success"));
          //刷新列表
          this.pagerConfig = {
            current: 1,
            pageSize: 20,
            total: 0,
          };
          this.fetchTable({ current: 1, pageSize: 10 });
        })
        .catch((err) => {
          this.tableLoading = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
  },
};
</script>

<style lang="less" scoped>
.bindGroupArea {
  display: flex;
  justify-content: end;
  .bindroleBtn {
    margin-bottom: 10px;
  }
}

.openBtn {
  float: right;
  margin: 10px 20px 0 0;
}
</style>
<style>
.deleteModal .ant-modal-body {
  padding: 24px;
}

.deleteModal .ant-modal {
  /* top: 112px;
  left: 42%; */
}

.deleteModal .ant-modal-close-x {
  line-height: 69px;
}

.deleteModal
  .ant-modal-confirm-body
  > .anticon
  + .ant-modal-confirm-title
  + .ant-modal-confirm-content {
  margin-left: 0;
}

.deleteModal .ant-modal-confirm-btns .ant-btn {
  width: 75px;
  float: right;
}

.deleteModal .ant-modal-confirm-btns .ant-btn.ant-btn-primary {
  margin-right: 8px;
  background-color: rgba(37, 94, 215, 1);
  /* background-color: #1890ff; */
  border-color: rgba(37, 94, 215, 1);
}

/* 抽屉相关样式重置 */
</style>
