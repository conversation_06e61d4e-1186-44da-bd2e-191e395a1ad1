<template>
  <div style="height: 100%" class="main-content">
    <header>
      <div class="header-left">
        <a-breadcrumb separator=">">
          <a-breadcrumb-item>
            <a href="javascript:void(0)" @click="routerBack">
              {{
                $route.query.type === "team"
                  ? $t("page_team_management")
                  : teamData
                  ? teamData.name
                  : ""
              }}
            </a>
          </a-breadcrumb-item>
          <a-breadcrumb-item>{{
            teamData && type == "team" ? teamData.name : $t("txt_team_view")
          }}</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </header>
    <TeamView
      v-if="teamData"
      :teamData="teamData"
      :type="type"
      :drawerStatus="drawerStatus"
    />
  </div>
</template>

<script>
import TeamView from "./components/team-view.vue";
import { productDetailContainer } from "../apis/index";
export default {
  components: {
    TeamView,
  },
  data() {
    return {
      teamName: "",
      teamData: null,
      //根据类型获取团队详情
      type: "",

      initData: null,
      drawerStatus: "",
    };
  },
  inject: ["setBreadcrumb", "addBreadcrumb"],
  created() {
    const { type, team, drawerStatus } = this.$route.query;
    this.type = type;
    this.drawerStatus = drawerStatus;
    this.initData = JSON.parse(team);

    this.initTeamData();
    this.initBreadcrumb();
  },
  methods: {
    initTeamData() {
      if (this.type === "team") {
        this.teamData = this.initData;
      } else if (this.type === "productTeam") {
        //根据产品容器获取团队信息
        this.getItemDetail(this.initData.oid);
      }
    },
    initBreadcrumb() {
      let breadcrumbData = [];
      this.setBreadcrumb(breadcrumbData);
    },
    routerBack() {
      this.$router.go(-1);
    },

    // 获取当前产品容器团队信息
    getItemDetail(oid) {
      productDetailContainer
        .execute({ oid })
        .then((data) => {
          this.teamData = data.team;
          this.teamData.name = this.initData.name;
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
  },
};
</script>

<style lang="less" scoped>
.main-content {
  display: flex;
  flex-direction: column;
  background-color: #fff;
}
.header-left {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  min-height: 60px;
  line-height: 60px;
  padding: 0 24px;
  padding-right: 40px;
  background: #fff;
}
</style>