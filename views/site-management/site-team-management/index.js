import { getExternRouters} from 'jw_common/hook';

const routers = [
    {
        // 站点团队管理
        path: '/site-team-management',
        component: () => import( /*webpackChunkName: "/team-management"*/ './views/index.vue'),
    },
    {
        // 站点团队管理
        path: '/site-team-management-view',
        component: () => import( /*webpackChunkName: "/team-management"*/ './views/view.vue'),
    },
];

export let init = function() {
    let parent= getExternRouters()[0];
    parent.children.push(...routers);
}

export default routers