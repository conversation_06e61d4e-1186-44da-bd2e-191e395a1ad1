<template>
  <a-modal
    v-model="visibleshow"
    :title="form.oid ? $t('btn_edit') : $t('btn_create')"
    @ok="submit"
    @cancel="cancel"
  >
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      :confirmLoading="loading"
    >
      <a-form-model-item :label="$t('table_identifier')" prop="name">
        <a-input v-model.trim="form.name" :readonly="disabledNameList.includes(form.name)" :maxLength="64"/>
      </a-form-model-item>
      <a-form-model-item :label="$t('tabel_name')" prop="displayName">
        <a-input v-model.trim="form.displayName" :maxLength="100"/>
      </a-form-model-item>
      <a-form-model-item :label="$t('txt_remark')" prop="description" >
        <a-input v-model.trim="form.description" type="textarea" :maxLength="255"/>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
import rules from "jw_frame/utils/rules";
import { getCookie } from 'jw_utils/cookie'
import ModelFactory from 'jw_apis/model-factory'

//创建角色
const createRole = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/systemrole/create`,
  method: 'post',
})

//编辑角色
const eidRole = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/systemrole/update`,
  method: 'post'
})

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    viewdata: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      form: {},
      rules: {
        name: [
          { required: true, message: this.$t('msg_input'), trigger: 'blur' },
          {
            ...rules.code,
           
          }
        ],
        displayName: [
          { required: true, message: this.$t('msg_input'), trigger: 'blur' },
        ],
      },
      loading: false,

      disabledNameList: [
        "ChecklistAdministrators",
        "AuditAdministrator",
        "ResourceAdministrators",
        "DocTemplateAdministrators",
        "RiskAdministrators",
        "SecurityAdministrator",
        "ProjectAdministrators",
        "TenantAdministrators"
      ],
    }
  },
  computed: {
    visibleshow: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
  },
  watch: {
    visibleshow: function (val) {
      if (!val) {
        this.cancel()
      } else {
        this.form = Object.assign({}, this.viewdata)
      }
    },
  },
  methods: {
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = false
          this.form.containerOid = getCookie('tenantOid')
          this.form.containerModelType = 'Tenant'
          let api 
          if(this.form.oid){
            api = eidRole
          }else{
            api = createRole
          }
          api.execute(this.form)
            .then((resp) => {
              this.$success(this.$t(this.form.oid ? 'msg_update_success' : 'txt_create_success'))
              this.visibleshow = false
              this.$emit('reload')
            })
            .catch((e) => {
              console.error(e)
              this.$error(e.msg)
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    },
    cancel() {
      if (this.$refs['form']) {
        this.form = {}
        this.$refs['form'].resetFields()
      }
    },
  },
}
</script>

<style lang="less" scoped>
</style>