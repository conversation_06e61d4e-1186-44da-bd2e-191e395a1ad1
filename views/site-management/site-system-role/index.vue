<template>
  <div class="all-background">
    <jw-table
      ref="ref_table"
      :data-source.sync="tableData"
      :columns="columns"
      :fetch="fetchTable"
      :showPage="false"
      :autoLoad="false"
      :loading="loading"
      @onOperateClick="onOperateClick"
    >
      <template #toolbar>
        <div>
          <a-button
            type="primary"
            @click="viewdata = {};addvisible = true"
            >{{ $t('btn_create') }}</a-button
          >
        </div>
      </template>
    </jw-table>
    <add-modal
      :visible.sync="addvisible"
      :title="$t('btn_create')"
      :viewdata="viewdata"
      @reload="fetchTable"
    ></add-modal>
  </div>
</template>

<script>
import { roleList } from './apis'
import addModal from './add-modal.vue'
import { baseDelete } from "../../../utils/baseaction";
export default {
  components: {
    addModal,
  },
  inject: ['setBreadcrumb'],
  data() {
    return {
      loading: false,
      addvisible: false,

      tableData: [],

      viewdata: {},

      columns: [
        {
          title: this.$t('table_identifier'),
          key: 'name', // 点击回调判断唯一值
          field: 'name',
        },
        {
          title: this.$t('txt_name'),
          key: 'displayName', // 点击回调判断唯一值
          field: 'displayName',
        },
        {
          title: this.$t('txt_description'),
          key: 'description',
          field: 'description',
        },
        {
          field: 'operation',
          title: this.$t('txt_operation'),
          btns: [
            {
              title: this.$t('btn_edit'),
              key: 'editor',
              icon: 'jwi-iconedit',
            },
            {
              title: this.$t('txt_delete'),
              key: 'delete',
              icon: 'jwi-icondelete',
            },
          ],
        },
      ],
    }
  },
  created() {
    this.fetchTable()
    this.initBreadcrumb()
  },
  methods: {
    initBreadcrumb() {
      let breadcrumbData = [{ name: this.$t('page_system_user_title') }]
      this.setBreadcrumb(breadcrumbData)
    },
    onOperateClick(val, row){
      console.log(val, row)
      switch (val) {
        case 'editor': {
          this.addvisible = true
          this.viewdata = Object.assign({}, row)
          break
        }
        case 'delete': {
          baseDelete({oid: row.oid, type: row.type}, this.fetchTable)
          break
        }
      
        default:
          break;
      }
    },

    fetchTable() {
      this.loading = true
      roleList()
        .then((data) => {
          this.tableData = data
        })
        .catch((e) => {
          console.error(e)
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style lang="less" scoped>
</style>