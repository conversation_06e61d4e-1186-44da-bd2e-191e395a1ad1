<template>
  <div class="base-panel page-container">
    <jw-table ref="refTable"
              :panel="true"
              :toolbars="toolbars"
              :columns="columns"
              :data-source.sync="tableData"
              :fetch="fetchTable"
    >
      <template slot="tool-before-end">
        <a-form-model
            style="width:100%;"
            layout="vertical"
            :model="form">
          <a-row :gutter="10">
            <a-col :span="3">
              <a-select
                  v-model="form.status"
                  :placeholder="$t('msg_select')+$t('txt_status')"
                  allowClear
                  style="width: 100%"
              >
                <a-select-option
                    v-for="val in statusList"
                    :title="val.value"
                    :key="val.key"
                    :value="val.value"
                >
                  {{ val.key }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :span="3">
              <a-input
                  v-model.trim="form.keyWord"
                  allowClear
                  :placeholder="$t('txt_enter_keyword')" />
            </a-col>
            <a-col :span="4">
              <a-range-picker
                  v-model="searchDate"
                  allowClear
                  :placeholder="[$t('msg_starttime'), $t('msg_endtime')]"
                  @change="onDateChange"
              />
            </a-col>
            <a-col :span="2">
              <a-button type="primary" @click="onSearch">{{ $t("btn_search") }}</a-button>
            </a-col>
            <a-col :span="3">
              <a-input
                  v-model.trim="form.number"
                  allowClear
                  placeholder="编码输入框" />
            </a-col>
            <a-col :span="3">
              <a-select
                  v-model="form.businessType"
                  placeholder="请选择业务"
                  allowClear
                  style="width: 100%"
              >
                <a-select-option
                    v-for="val in businessTypeList"
                    :title="val.value"
                    :key="val.key"
                    :value="val.value"
                >
                  {{ val.key }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :span="3">
              <a-select
                  v-model="form.isFirst"
                  placeholder="请选择发布方式"
                  allowClear
                  style="width: 100%"
              >
                <a-select-option
                    v-for="val in publishList"
                    :title="val.value"
                    :key="val.key"
                    :value="val.value"
                >
                  {{ val.key }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :span="2">
              <a-button type="primary" @click="publish">按编码发布</a-button>
            </a-col>
          </a-row>
        </a-form-model>
      </template>
      <template #updateDate="{ row }">
        <span>{{row.updateDate - row.createDate}} ms</span>
      </template>
      <template #isSuccess="{ row }">
        {{ row.isSuccess === true  ? '成功' : '失败' }}
      </template>
      <template #operation="{ row }">
        <i class="jwi-kaigong"
           v-if="row.isSuccess === false"
           title="重新发布"
           @click="onReStart(row)"
        >
        </i>
      </template>
    </jw-table>
  </div>
</template>

<script>
import ModelFactory from 'jw_apis/model-factory';
import moment from "moment";

// 获取集成概况
const fetchIntegrationList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/integration/monitor/findRecord`,
  method: 'post',
});

const doStep =  ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/integration/monitor/reSend`,
  method: 'post',
});

const sendByNumber =  ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/integration/monitor/sendByNumber`,
  method: 'post',
});

export default {
  name: 'jwCADConversionMonitor',
  components: {},
  inject: [
    'setBreadcrumb',
  ],
  data() {
    return {
      form: {
        status: undefined,
        keyWord: '',
        startDate: '',
        endDate: '',
        number:'',
        businessType:undefined,
        isFirst:undefined,
      },
      searchDate: [],
      tenantList: Jw.getUser().tenants,
      statusList: [
        {key: '成功', value: true},
        {key: '失败', value: false},
      ],
      publishList:[
        {key: '首次发布', value: 1},
        {key: '更新发布', value: 2},
      ],
      businessTypeList:[
        {key: '部件Item发布', value: 'item'},
        {key: '部件BOM发布', value: 'bom'},
        {key: '文档发布', value: 'doc'},
        {key: '结构CAD图档发布', value: 'mcad'},
        {key: '电子CAD图档发布', value: 'ecad'},
        {key: '技术变更单发布', value: 'techChange'},
      ],
      tableData: [],
      tableLoading: false,
    };
  },
  created() {
    // this.setBreadcrumb([{name:this.$t('txt_cad_monitor')}]);
    this.setBreadcrumb([{name: this.$t('txt_integrated_task')}]);
  },
  computed: {
    toolbars() {
      return [
        {
          name: this.$t('btn_new_create'),
          key: 'create',
          type: 'primary',
          isVisible: false,
        },
      ]
    },
    columns() {
      return [
        {
          field: 'bizNumber',
          title: this.$t('txt_number'),
          minWidth: 210,
        },
        {
          field: 'bizName',
          title: this.$t('txt_plan_name'),
          minWidth: 210,
        },
        {
          field: 'bizType',
          title: this.$t('txt_type'),
          minWidth: 150,
          visible: false,
        },
        {
          field: 'bizVersion',
          title: this.$t('txt_plan_version'),
          minWidth: 180,
        },
        {
          field: 'businessType',
          title: this.$t('txt_business_type'),
          minWidth: 180,
        },
        {
          field: 'createDate',
          title: this.$t('msg_starttime') ,
          minWidth: 180,
          formatter: 'date',
        },
        {
          field: 'consumer',
          title: this.$t('txt_consumer') ,
          minWidth: 180,
        },
        {
          field: 'isSuccess',
          title: this.$t('txt_consumerResult') ,
          minWidth: 180,
          slots:{
            default: "isSuccess",
          },
        },
        {
          field: 'msg',
          title: this.$t('txt_error_message'),
          minWidth: 180,
          visible: false,
        },
        /* {
           field: 'integrationResult',
           title: this.$t('txt_integration_result'),
           minWidth: 180,
           formatter: ({row}) => {
             return [
               row.integrationResult.consumer.consumer + ":" + row.integrationResult.consumer.isSuccess
             ]
           }
         },*/
        {
          field: 'operation',
          title: this.$t('txt_operation'),
          slots: {
            default: 'operation',
          },
        }
      ];
    },
  },
  mounted() {

  },
  methods: {
    onSearch() {
      this.$refs.refTable.reFetchData();
    },
    publish(){
      sendByNumber
          .execute({
            number: this.form.number,
            type:this.form.businessType,
            isFirst:this.form.isFirst
          })
          .then((res) => {
            this.$success("操作成功");
          })
          .catch((err) => {
            this.$error(err.msg)
          })
    },
    fetchTable({current, pageSize}) {
      let params = {
        size: pageSize,
        index: current,
        param: {
          status: this.form.status,
          keyWord: this.form.keyWord,
          startDate: this.form.startDate,
          endDate: this.form.endDate,
        }
      };
      this.tableLoading = true;
      return fetchIntegrationList.execute(
          params
      ).then((res) => {
        return {
          data: res.rows,
          total: res.count,
        };
      }).catch((err) => {
        this.tableLoading = false;
        this.$error(err.msg);
      });
    },
    onReStart(row) {
      let params =row
      this.$confirm({
        title: this.$t('确定重发吗？'),
        okText: this.$t('btn_ok'),
        cancelText: this.$t('btn_cancel'),
        onOk: () => {
          return doStep.execute(params).then((res) => {
            this.$warning(this.$t('后台正在重新执行，请稍后查看执行结果'));
            this.onSearch();
          }).catch((err) => {
            if (err.msg) {
              this.$error(err.msg);
            }
          });
        },
      });
    },
    onDateChange(date, dateString) {
      if (date.length) {
        const startDate = moment(dateString[0] + ' 00:00:00', 'YYYY-MM-DD hh:mm:ss'),
            endDate = moment(dateString[1] + ' 23:59:59', 'YYYY-MM-DD hh:mm:ss');
        this.form.startDate = startDate.format('x');
        this.form.endDate = endDate.format('x');
      } else {
        this.form.startDate ='';
        this.form.endDate = '';
      }
    },
  },
};
</script>

<style lang="less" scoped>

</style>
