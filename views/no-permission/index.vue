<template>
	<div>
		<div class="dmaku-error-main">
			<div class="w-1200 clear">
				<div class="error-l fl">
					<div class="clear"></div>
					<!-- <p class="foot-tex" style="margin-top: 20px">
						Copyright ©2018 www.jsdaima.com
					</p> -->
				</div>
				<div class="error-r fr">
					<img src="../../assets/image/images/jsdaima-error-1.png" />
				</div>
				<div class="l-tex">
					<p>页面不存在或暂无权限访问，请联系管理员</p>
					<span class="index" @click="backIndex"> 返回 </span>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
export default {
	data() {
		return {}
	},
	methods: {
		backIndex() {
			this.$router.push('/dashboard')
		},
	},
}
</script>

<style scoped>

.index {
	text-decoration: none;
	-webkit-tap-highlight-color: transparent;
	-webkit-transition: 0.5s;
	-moz-transition: 0.5s;
	transition: 0.5s;
	cursor: pointer;
}
.dmaku-error-main {
	position: absolute;
	width: 100%;
	height: 333px;
	left: 0;
	top: 50%;
	margin-top: -200px;
	/* background: #5dce6c; */
	/* background: url('../../assets/image/images/jsdaima-error-2.jpg') no-repeat center 96px; */
	background-size: 100% 175px;
}
.w-1200 {
	width: 1200px;
	margin: 0 auto;
	position: relative;
}
.dmaku-error-main .error-l {
	margin-top: 140px;
}
.fl {
	float: left;
}
.clear:after {
	content: "";
	clear: both;
	display: block;
}

.fr {
	display: flex;
	align-items: center;
	justify-content: center;
}
img {
	display: block;
    width: 300px;
    height: 255px;
}
.dmaku-error-main .error-l h1 {
	float: left;
}
.dmaku-error-main .error-l h1 a {
	display: block;
	width: 175px;
	height: 46px;
	background: url("../../assets/image/images/logo.png") no-repeat;
}
.l-tex {
	text-align: center;
	width: 100%;
    margin-top: 30px;
	border-left: 1px solid rgba(255, 255, 255, 0.15);
}
.l-tex p {
	color: #000;
	font-size: 18px;
	line-height: 24px;
}
.l-tex .index {
	display: block;
	/* width: 120px; */
	line-height: 34px;
	text-align: center;
	font-size: 16px;
	color: #255ED7;
	margin-top: 10px;
	cursor: pointer;
}
.dmaku-error-main .error-l .foot-tex {
	font-size: 12px;
	color: #7d7d7d;
	padding-top: 70px;
}
</style>
