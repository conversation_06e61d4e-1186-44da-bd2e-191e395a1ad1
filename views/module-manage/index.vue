<template>
  <div>
    <button @click="aa">aa</button>
    <div id="container"></div>
  </div>

</template>

<script>
import G6 from "@antv/g6";
export default {
  mounted() {
    this.init();
  },
  methods: {
    aa() {
      let graph = this.graph;
      const edges = graph.save().edges;
      G6.Util.processParallelEdges(edges);
      graph.getEdges().forEach((edge, i) => {
        graph.updateItem(edge, {
          curveOffset: edges[i].curveOffset,
          curvePosition: edges[i].curvePosition,
        });
      });
      console.log(edges);
    },
    init() {
      const data = {
        nodes: [
          {
            id: "0",
            sortAttr: 0,
            sortAttr2: "a",
          },
          {
            id: "1",
            sortAttr: 0,
            sortAttr2: "a",
          },
          {
            id: "2",
            sortAttr: 0,
            sortAttr2: "a",
          },
          {
            id: "3",
            sortAttr: 0,
            sortAttr2: "a",
          },
          {
            id: "4",
            sortAttr: 2,
            sortAttr2: "c",
          },
          {
            id: "5",
            sortAttr: 0,
            sortAttr2: "a",
          },
          {
            id: "6",
            sortAttr: 1,
            sortAttr2: "b",
          },
          {
            id: "7",
            sortAttr: 1,
            sortAttr2: "b",
          },
          {
            id: "8",
            sortAttr: 2,
            sortAttr2: "c",
          },
          {
            id: "9",
            sortAttr: 3,
            sortAttr2: "d",
          },
        ],
        edges: [
          {
            source: "0",
            target: "1",
            color:'red'
          },
          {
            source: "0",
            target: "2",
          },
          {
            source: "0",
            target: "3",
          },
          {
            source: "0",
            target: "4",
          },
          {
            source: "0",
            target: "5",
          },
          {
            source: "0",
            target: "7",
          },
          {
            source: "0",
            target: "8",
          },
          {
            source: "0",
            target: "9",
          },
          {
            source: "0",
            target: "10",
          },
          {
            source: "0",
            target: "11",
          },
          {
            source: "0",
            target: "13",
          },
          {
            source: "0",
            target: "14",
          },
          {
            source: "0",
            target: "15",
          },
          {
            source: "0",
            target: "16",
          },
          {
            source: "2",
            target: "3",
          },
          {
            source: "4",
            target: "5",
          },
          {
            source: "4",
            target: "6",
          },
          {
            source: "5",
            target: "6",
          },
          {
            source: "7",
            target: "13",
          },
          {
            source: "8",
            target: "14",
          },
          {
            source: "9",
            target: "10",
          },
          {
            source: "10",
            target: "22",
          },
          {
            source: "10",
            target: "14",
          },
          {
            source: "10",
            target: "12",
          },
          {
            source: "10",
            target: "24",
          },
          {
            source: "10",
            target: "21",
          },
          {
            source: "10",
            target: "20",
          },
          {
            source: "11",
            target: "24",
          },
          {
            source: "11",
            target: "22",
          },
          {
            source: "11",
            target: "14",
          },
          {
            source: "12",
            target: "13",
          },
          {
            source: "16",
            target: "17",
          },
          {
            source: "16",
            target: "18",
          },
          {
            source: "16",
            target: "21",
          },
          {
            source: "16",
            target: "22",
          },
          {
            source: "17",
            target: "18",
          },
          {
            source: "17",
            target: "20",
          },
          {
            source: "18",
            target: "19",
          },
          {
            source: "19",
            target: "20",
          },
          {
            source: "19",
            target: "33",
          },
          {
            source: "19",
            target: "22",
          },
          {
            source: "19",
            target: "23",
          },
          {
            source: "20",
            target: "21",
          },
          {
            source: "21",
            target: "22",
          },
          {
            source: "22",
            target: "24",
          },
          {
            source: "22",
            target: "25",
          },
          {
            source: "22",
            target: "26",
          },
          {
            source: "22",
            target: "23",
          },
          {
            source: "22",
            target: "28",
          },
          {
            source: "22",
            target: "30",
          },
          {
            source: "22",
            target: "31",
          },
          {
            source: "22",
            target: "32",
          },
          {
            source: "22",
            target: "33",
          },
          {
            source: "23",
            target: "28",
          },
          {
            source: "23",
            target: "27",
          },
          {
            source: "23",
            target: "29",
          },
          {
            source: "23",
            target: "30",
          },
          {
            source: "23",
            target: "31",
          },
          {
            source: "23",
            target: "33",
          },
          {
            source: "32",
            target: "33",
          },
        ],
      };
      const width = container.scrollWidth;
      const height = (container.scrollHeight || 500) - 20;
      const graph = (this.graph = new G6.Graph({
        container: "container",
        width,
        height,
        linkCenter: true,
        modes: {
          default: ["create-edge", "drag-canvas", "drag-node"],
        },
        layout: {
          type: "radial",
          unitRadius: 170,
          maxIteration: 1000,
          linkDistance: 600,
          preventOverlap: true,
          nodeSize: 30,
        },
        defaultEdge: {
          type: "quadratic",
          style: {
            stroke: "#F6BD16",
            lineWidth: 2,
          },
        },
      }));

      graph.data(data);
      graph.render();

      graph.on("aftercreateedge", (e) => {
        const edges = graph.save().edges;
        G6.Util.processParallelEdges(edges);
        graph.getEdges().forEach((edge, i) => {
          graph.updateItem(edge, {
            curveOffset: edges[i].curveOffset,
            curvePosition: edges[i].curvePosition,
          });
        });
      });
      this.aa()
    },
  },
};
</script>

<style>
</style>