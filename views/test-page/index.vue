<template>
	<div class="all-background audit-log" style="padding-top: 0">
		<a-tabs default-active-key="1" @change="callback">
			<a-tab-pane
				style="min-height: 500px"
				v-for="pane in list"
				:key="pane.key"
				:tab="pane.name"
			>
				<div :class="pages.isLogin?'topCurs':'topCur'">
					<span class="layout-tree"  v-show="!pages.isLogin">
						<span>
							<span> {{ $t("txt_operation_type") }}： </span>
							<a-select
								v-model.trim="action"
								:placeholder="$t('msg_select')"
								allow-clear
								show-search
								@change="containerSearch"
								style="width:150px"
								:dropdown-style="{
									width: '300px',
									maxHeight: '480px',
									overflowY: 'auto',
								}"
								option-filter-prop="children"
								:filter-option="
									(value, option) =>
										option.componentOptions.children[0].children[1].text
											.toLowerCase()
											.indexOf((value || '').trim().toLowerCase()) >= 0
								"
							>
								<a-select-option v-for="item in actionList" :key="item.id">
									<span :title="item.name">
										{{ item.name }}
									</span>
								</a-select-option>
							</a-select>
						</span>
						<span class="layout-tree">
							<span> {{ $t("tabel_context") }}： </span>
							<a-select
								v-model.trim="containerOid"  
								:placeholder="$t('txt_select_context')"
								allow-clear
								show-search
								style="width:200px"
								@change="containerSearch"
								:dropdown-style="{
									width: '300px',
									maxHeight: '480px',
									overflowY: 'auto',
								}"
								option-filter-prop="children"
								:filter-option="
									(value, option) =>
										option.componentOptions.children[0].children[1].text
											.toLowerCase()
											.indexOf((value || '').trim().toLowerCase()) >= 0
								"
							>
								<a-select-option v-for="item in containerList" :key="item.oid">
									<span :title="item.name">
										<jw-icon style="margin-right: 8px" :type="item.modelIcon" />
										{{ item.name }}
									</span>
								</a-select-option>
							</a-select>
							<a-input-search
								:placeholder="$t('search_text')"
								style="width: 220px"
								@search="onSearch"
							/>
						</span>
					</span>
					<span >
						<a-radio-group
							v-model.trim="value"
							@change="seletTime"
							style="margin-bottom: 16px"
						>
							<a-radio-button value="all">{{ $t("all_text") }} </a-radio-button>
							<a-radio-button value="toDay"
								>{{ $t("today_text") }}
							</a-radio-button>
							<a-radio-button value="yesterday"
								>{{ $t("yesterday_text") }}
							</a-radio-button>
							<a-radio-button value="currWeekDays"
								>{{ $t("week_text") }}
							</a-radio-button>
							<a-radio-button value="lastWeekDays">
								{{ $t("Last_week_text") }}
							</a-radio-button>
						</a-radio-group>
					</span>
				</div>
				<a-spin :spinning="spinning" tip="Loading...">
					<!-- 滚动分页开启该行 -->
					<!-- <div
						class="className"
						:style="'height:' + screenHeight + 'px;' + 'overflow-y: auto;'"
						@scroll="scrollEvent($event)"
					> -->
					<div class="className">
						<div class="topColumn" ref="element">
							<a-collapse
								default-active-key="1"
								v-if="listData && listData.length > 0"
								:bordered="false"
							>
								<template #expandIcon="props">
									<a-icon
										type="caret-right"
										:rotate="props.isActive ? 90 : 0"
									/>
								</template>
								<a-collapse-panel
									v-for="(item, index) in listData"
									:key="index"
									:header="Object.keys(item)[0]"
									:style="customStyle"
								>
									<a-timeline v-for="(val, timer) in item" :key="timer">
										<a-timeline-item
											:style="pages.isLogin ? 'height:45px' : 'height:66px'"
											v-for="(js, ints) in val"
											:key="ints"
										>
											<span v-show="pages.isLogin" style="font-size: 12px"
												>{{ js.userName }}{{ js.actionDisplayName }}（IP{{
													js.ipAddress
												}}）{{ js.description }}<br />
												{{ formatDate(js.createDate) }}
											</span>

											<span v-show="!pages.isLogin" style="font-size: 12px"
												>{{ js.userName }}于 {{ formatDate(js.createDate) }}
												{{ js.actionDisplayName }}了 {{ js.bizType }}({{
													js.bizOid
												}})
												<!-- <br />
                        操作账号：{{ js.userAccount }},租户信息:{{
													js.tenantOid 
												}} -->
												<br />
												{{ $t("txt_operation_is") }}：{{ js.description }}
											</span>
										</a-timeline-item>
									</a-timeline>
								</a-collapse-panel>
							</a-collapse>
						</div>

						<div id="box" style="width: 100%; height: 1px"></div>
					</div>
				</a-spin>
			</a-tab-pane>
		</a-tabs>
		<div style="margin-top:20px;text-align:right">
			<a-pagination
				:current="pages.index"
				:pageSize="pages.size"
				show-size-changer
				:total="total"
				@change="pageSizeChange"
				:pageSizeOptions="pages.pageSizeOptions"
				@showSizeChange="onShowSizeChange"
			/>
		</div>
	</div>
</template>

<script>
import { getLoglist } from "./apis"
//获取今日/昨日/本周/上周/本月/上月 时间
import moment from "moment"
import ModelFactory from "jw_apis/model-factory"
// 获取上下文
const allContainer = ModelFactory.create({
	url: `${Jw.gateway}/${Jw.containerService}/container/allContainer/search`,
	method: "get",
})
export default {
	data() {
		return {
			screenHeight: document.documentElement.clientHeight - 200,
			listData: [],
			value: "toDay",
			customStyle:
				"background: #fff;border-radius: 0px;margin-bottom: 2px;border: 0;overflow: hidden",
			list: [
				{
					name: this.$t("tabel_login_log"),
					key: "1",
				},
				{
					name: this.$t("tabel_opration_log"),
					key: "2",
				},
			],

			action: "",
			containerOid: "",
			actionList: [
				{
					name: this.$t("txt_add_c"),
					id: "ADD",
				},
				{
					name: this.$t("txt_delete"),
					id: "DELETE",
				},
				{
					name: this.$t("txt_update"),
					id: "UPDATE",
				},
				{
					name: this.$t("txt_mobile"),
					id: "MOVE",
				},
				{
					name: this.$t("txt_copy"),
					id: "COPY",
				},
				{
					name: this.$t("txt_revision"),
					id: "REVISE",
				},
				{
					name: this.$t("btn_download"),
					id: "DOWNLOAD",
				},
				{
					name: this.$t("txt_rename"),
					id: "UPLOAD",
				},
				{
					name: this.$t("txt_save_as"),
					id: "SAVE_AS",
				},
				{
					name: this.$t("txt_update_status"),
					id: "MODIYF_STATUS",
				},
				{
					name: this.$t("txt_check_in"),
					id: "CHECKIN",
				},
				{
					name: this.$t("txt_check_out"),
					id: "CHENOUT",
				},
				{
					name: this.$t("txt_undo_out"),
					id: "UNCHECKOUT",
				},
				{
					name: this.$t("txt_add_to_baseline"),
					id: "ADD_TO_BASELINE",
				},
			],
			pages: {
				index: 1,
				filterTime: null,
				isLogin: true,
				searchKey: "",
				size: 20,
				pageSizeOptions: ["10", "20", "50", "100"],
			},
			containerList: [],
			total: 0,
			current: {},
			spinning: false,
			devicePageSize: 10, //每页显示
			devicePageNum: 1, //当前页
			devicePageTotal: 0, //总条数
			devicePageTotalPages: 0, //总页数
			deviceListIsFinish: false, //是否加载完成
			deviceListIsLoad: true, //是否加载更多
			deviceListnodata: false, //是否有数据
			deviceTip: "",
		}
	},
	watch: {
		screenHeight(val) {
			// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
			if (!this.timer) {
				// 一旦监听到的screenWidth值改变，就将其重新赋给data里的screenWidth
				this.screenHeight = val
				this.timer = true
				let that = this
				setTimeout(function() {
					// 打印screenWidth变化的值
					console.log(that.screenHeight)
					that.timer = false
				}, 200)
			}
		},
	},
	mounted() {},
	created() {
		this.seletTime()
		this.pages.index = 1
		console.log(this.getToday())
		this.getContent()
		// console.log(this.getYesterday())
		// console.log(this.getCurrWeekDays())
		// console.log(this.getLastWeekDays())
		// console.log()
	},
	methods: {
		//获取上下文
		getContent() {
			allContainer
				.execute({
					containerType: "Container",
				})
				.then((data) => {
					this.containerList = data
				})
				.catch((err) => {
					this.$error(err.msg || this.$t("txt_context_faile"))
				})
		},

		//滚动分页事件 滚动分页开启该行即可
		scrollEvent(e) {
			if (e instanceof Event) {
				let el = e.target
				let scrollTop = el.scrollTop
				// 获取可视区的高度
				let clientHeight = el.clientHeight
				// 获取滚动条的总高度
				let scrollHeight = el.scrollHeight
				let height = this.screenHeight
				//到底了
				// console.log(this.deviceListIsLoad, this.deviceListIsFinish);
				console.log(scrollTop + height, clientHeight, scrollHeight)
				//是否继续加载且已完成加载
				if (
					scrollTop + height >= scrollHeight &&
					this.deviceListIsLoad &&
					this.pages.index >= 1
				) {
					// 把距离顶部的距离加上可视区域的高度 等于或者大于滚动条的总高度就是到达底部
					//下一页
					this.pages.index = this.pages.index + 1
					// console.log("到底了");
					setTimeout(() => {
						this.getListLog(el)
						//防止重复触发请求
						this.deviceListIsLoad = false
					}, 200)
				} else if (
					scrollTop + height <= this.screenHeight &&
					this.deviceListIsLoad &&
					this.pages.index > 1
				) {
					// 上一页
					// console.log("到顶了");
					this.pages.index = this.pages.index - 1
					setTimeout(() => {
						this.getListLog(el)
						//防止重复触发请求
						this.deviceListIsLoad = false
						// this.$loading('加载中')
					}, 200)
				} else {
					// this.$success('没有更多了')
				}
			}
		},
		callback(key) {
			this.pages.isLogin = key == 1 ? true : false
			this.pages.index = 1
			this.pages.searchKey = ""
			this.containerOid = ''
			this.action=''
			this.getListLog()
			// console.log(key)
		},
		containerSearch(){
			this.getListLog()
		},
		//获取列表
		getListLog(el) {
			// 		console.log(this.action,
			//   this.containerOid)
			let query = {
				...this.pages,
				containerOid: this.containerOid,
				action: this.action,
			}
			console.log(query)
			this.listData = []
			this.spinning = true
			const that = this
			getLoglist.execute(query).then((res) => {
				this.listData = res.rows
				this.total = res.count
				// 计算总页数 滚动分页开启该行即可

				// let totle = Math.ceil(res.count / 30)
				// if (this.pages.index >= totle) {
				// 	this.deviceListIsLoad = false
				// } else {
				// 	this.deviceListIsLoad = true
				// }

				// if (el) {
				// 	setTimeout(() => {
				// 		el.scrollTop = 12
				// 	}, 200)
				// }

				this.spinning = false
				// console.log(Object.keys(res.rows[0]))
			})
		},
		//搜索
		onSearch(val) {
			console.log(val)
			this.pages.searchKey = val
			this.getListLog()
		},
		//根据时间筛选日志
		seletTime(value) {
			this.pages.index = 1
			console.log(this.value)
			let a = ""
			this.pages.filterTime = {}
			if (this.value == "all") {
				this.pages.filterTime = null
			} else if (this.value == "toDay") {
				a = this.getToday()
				this.pages.filterTime.startTime = a.starttime
				this.pages.filterTime.endTime = a.endtime
			} else if (this.value == "yesterday") {
				a = this.getYesterday()
				this.pages.filterTime.startTime = a.starttime
				this.pages.filterTime.endTime = a.endtime
			} else if (this.value == "currWeekDays") {
				a = this.getCurrWeekDays()
				this.pages.filterTime.startTime = a.starttime
				this.pages.filterTime.endTime = a.endtime
			} else if (this.value == "lastWeekDays") {
				a = this.getLastWeekDays()
				this.pages.filterTime.startTime = a.starttime
				this.pages.filterTime.endTime = a.endtime
			}

			this.getListLog()
		},
		//分页
		onShowSizeChange(current, size) {
			console.log(current, size)
			this.pages.index = current
			this.pages.size = size
			this.getListLog()
		},
		pageSizeChange(page, pageSize) {
			console.log(page, pageSize)
			this.pages.index = page
			this.pages.size = pageSize
			this.getListLog()
		},
		// 时间戳转换
		formatDate(value) {
			let date = new Date(value)
			let y = date.getFullYear()
			let MM = date.getMonth() + 1
			MM = MM < 10 ? "0" + MM : MM
			let d = date.getDate()
			d = d < 10 ? "0" + d : d
			let h = date.getHours()
			h = h < 10 ? "0" + h : h
			let m = date.getMinutes()
			m = m < 10 ? "0" + m : m
			let s = date.getSeconds()
			s = s < 10 ? "0" + s : s
			return y + "-" + MM + "-" + d + " " + h + ":" + m + ":" + s
		},
		// 获取今日的开始结束时间
		getToday() {
			let obj = {
				starttime: "",
				endtime: "",
			}
			obj.starttime = moment(
				moment()
					.startOf("day")
					.valueOf()
			)._i
			obj.endtime = moment(moment().valueOf())._i
			return obj
		},
		// 获取昨日的开始结束时间
		getYesterday() {
			let obj = {
				starttime: "",
				endtime: "",
			}
			obj.starttime = moment(
				moment()
					.add(-1, "days")
					.startOf("day")
					.valueOf()
			)._i
			// format("YYYY-MM-DD HH:mm:ss")
			obj.endtime = moment(
				moment()
					.add(-1, "days")
					.endOf("day")
					.valueOf()
			)._i
			return obj
		},
		// 获取当前周的开始结束时间
		getCurrWeekDays() {
			let obj = {
				starttime: "",
				endtime: "",
			}
			obj.starttime = moment()
				.startOf("week")
				.add(0, "day")
				.format("YYYY-MM-DD HH:mm:ss")
			obj.endtime = moment()
				.endOf("week")
				.add(0, "day")
				.format("YYYY-MM-DD HH:mm:ss")
			obj.starttime = this.transdate(obj.starttime)
			obj.endtime = this.transdate(obj.endtime)
			console.log(obj)
			return obj
		},
		// 获取上一周的开始结束时间
		getLastWeekDays() {
			let obj = {
				starttime: "",
				endtime: "",
			}
			obj.starttime = moment(
				moment()
					.week(moment().week() - 1)
					.startOf("week")
					.add(0, "days")
					.valueOf()
			)._i
			obj.endtime = moment(
				moment()
					.week(moment().week() - 1)
					.endOf("week")
					.add(0, "days")
					.valueOf()
			)._i
			return obj
		},
		// 获取当前月的开始结束时间
		getCurrMonthDays() {
			let obj = {
				starttime: "",
				endtime: "",
			}
			obj.starttime = moment(
				moment()
					.month(moment().month())
					.startOf("month")
					.valueOf()
			).format("YYYY-MM-DD HH:mm:ss")
			obj.endtime = moment(
				moment()
					.month(moment().month())
					.endOf("month")
					.valueOf()
			).format("YYYY-MM-DD HH:mm:ss")
			return obj
		},
		transdate(time) {
			let date = new Date()
			date.setFullYear(time.substring(0, 4))
			date.setMonth(time.substring(5, 7) - 1)
			date.setDate(time.substring(8, 10))
			date.setHours(time.substring(11, 13))
			date.setMinutes(time.substring(14, 16))
			date.setSeconds(time.substring(17, 19))
			return Date.parse(date)
		},
		// 获取上一个月的开始结束时间
		getLastMonthDays() {
			let obj = {
				starttime: "",
				endtime: "",
			}
			obj.starttime = moment(
				moment()
					.month(moment().month() - 1)
					.startOf("month")
					.valueOf()
			).format("YYYY-MM-DD HH:mm:ss")
			obj.endtime = moment(
				moment()
					.month(moment().month() - 1)
					.endOf("month")
					.valueOf()
			).format("YYYY-MM-DD HH:mm:ss")
			return obj
		},
	},
}
</script>

<style scoped>
/deep/.ant-timeline-item {
	padding-bottom: 5px;
}
/deep/.ant-timeline-item-content {
	border-bottom: 1px solid #fff;
	background: #f7f7f7;
	/* height: 66px; */
	padding: 2px 5px 8px 5px;
}
.topCur {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.topCurs{
	display: flex;
	align-items: center;
	justify-content: right;
}
</style>
<style lang="less">
.all-background.audit-log {
	display: flex;
	flex-direction: column;
	.ant-tabs {
		height: 20px;
		flex-grow: 1;
		display: flex;
		flex-direction: column;
		.ant-tabs-content {
			height: 20px;
			flex-grow: 1;
			.ant-collapse-content-box {
				padding-bottom: 0;
			}
			.ant-tabs-tabpane {
				height: 100%;
				display: flex;
				flex-direction: column;
				.ant-spin-nested-loading {
					height: 20px;
					flex-grow: 1;
					overflow: auto;
				}
			}
		}
	}
}
.layout-tree {
	width: 65%;
	display: flex;
	align-items: center;
}
</style>
