<template>
  <jw-page>
    <div slot="header-left">
      <div style="display: flex; align-items: center">
        <span style="color: #000; font-size: 20px">{{
          $t("txt_product_rq")
        }}</span>
      </div>
    </div>
    <div class="all-background product-container produce-container">
      <div class="product-filter">
        <div>
          <a-button
            type="primary"
            v-if="currentCreateRole"
            @click="showChoose(true)"
          >
            {{ $t("btn_new_create") }}
          </a-button>
          <a-input-search
            :placeholder="$t('search_text')"
            style="width: 200px; margin-right: 8px; margin-left: 8px"
            v-model.trim="searchKey"
            @change="onSearch"
          />
          <span
            >{{ $t("txt_total") }}{{ pagerConfig.total }}
            {{ $t("txt_pieces") }}</span
          >
        </div>
        <div>

          <a-button @click="handleSwitchType">
            <span
              :class="
                switchType ? 'jwi-iconlist' : 'jwi-iconlogo-windows-filled'
              "
            >
            </span>
          </a-button>
        </div>
      </div>
      <div class="product-list">
        <jw-table
          v-if="!switchType"
          ref="ref_table"
          disableCheck="disableCheck"
          :data-source.sync="tableData"
          :columns="getHeader"
          :selectedRows.sync="selectedRows"
          :fetch="fetchTable"
          @onToolClick="onToolClick"
          @onToolInput="onToolInput"
          @checkbox-change="onSelectChange"
          @onOperateClick="onOperateClick"
          :pagerConfig="pagerConfig"
          @onPageChange="onPageChange"
          @onSizeChange="onSizeChange"
        >
          <template #privateSlot="{ row }">
            {{ row.privateFlag ? $t("txt_yes") : $t("txt_no") }}
          </template>
          <template #productContent="{ row }">
            <a style="color: #255ed7" @click="handleContent(row)">
              <jw-icon style="margin-right: 8px" type="#jwi-chanpin" />
              {{ row.name }}
            </a>
          </template>
          <template #catalogNameSlot="{ row }">
            {{ row.productCatalogOid ? loadSpectrumName(row.productCatalogName) :"" }}
          </template>
          <template #creatdTime="{ row }">
            <span style="color: #255ed7">{{
              formatDateFn(row.createDate)
            }}</span>
          </template>
          <template #productManagerSlot="{ row }">
            <userInfo :accounts="row.productManagers.map(item => item.account)"/>
          </template>
          <template #operation="{ row }">
            <a-dropdown
              placement="bottomRight"
              overlayClassName="operation-dropdown-overlay"
              :trigger="['click']"
              :overlayStyle="{width: '110px'}"
              @visibleChange="(visible) => dropdownVisibleChange(visible, row)"
            >
              <span class="jwi-iconellipsis" style="cursor: pointer"></span>
              <a-menu
                slot="overlay"
                @click="({ key }) => onOperateClick(key, row)"
              >
                <a-spin
                  v-if="row.loading_status == 'loading'"
                  style="margin: 20px 65px"
                />
                <a-menu-item
                  v-else-if="row.loading_status == 'failed'"
                  style="margin: 20px auto"
                >
                  {{ $t("txt_get_failure") }}
                </a-menu-item>
                <template v-else v-for="(item, index) in row.operationList">
                  <a-menu-divider
                    v-if="item.permissionKey == 'delete'"
                    :key="index"
                  />
                  <a-menu-item
                    v-if="item.name != '模型管理'"
                    :key="item.code"
                    :disabled="item.status === 'disable'"
                  >
                    <span :class="item.icon" style="margin-right: 8px"></span>
                    {{ $t(item.internationalizationKey) }}
                  </a-menu-item>
                </template>
              </a-menu>
            </a-dropdown>
          </template>
        </jw-table>
        <a-spin :spinning="cardSpinning">
          <div class="card-list" v-if="switchType">
            <a-row :gutter="16">
              <a-col
                class="gutter-row card-item"
                style="padding-left: 0;padding-right:0;"
                :span="6"
                v-for="(item, key) in tableData"
                :key="key"
              >
                <div class="card-content">
                  <ul class="item-info">
                    <li class="item-title">
                      <div class="item-title-name" @click="handleContent(item)">
                        <jw-icon type="#jwi-chanpin" />
                        <a-tooltip>
                          <template slot="title"> {{ item.name }} </template>
                          <span class="item-name">
                            {{ item.name }}
                          </span>
                        </a-tooltip>
                      </div>
                      <div class="item-more">
                        <a-dropdown
                          placement="bottomRight"
                          overlayClassName="operation-dropdown-overlay"
                          :trigger="['click']"
                          :overlayStyle="{width: '110px'}"
                          @visibleChange="
                            (visible) => dropdownVisibleChange(visible, item)
                          "
                        >
                          <span
                            class="jwi-iconellipsis"
                            style="cursor: pointer"
                          ></span>
                          <a-menu
                            slot="overlay"
                            @click="({ key }) => onOperateClick(key, item)"
                          >
                            <a-spin
                              v-if="item.loading_status == 'loading'"
                              style="margin: 20px 65px"
                            />
                            <a-menu-item
                              v-else-if="item.loading_status == 'failed'"
                              style="margin: 20px auto"
                            >
                              {{ $t("txt_get_failure") }}
                            </a-menu-item>
                            <template
                              v-else
                              v-for="(item, index) in item.operationList"
                            >
                              <a-menu-divider
                                v-if="item.permissionKey == 'delete'"
                                :key="index"
                              />
                              <a-menu-item
                                v-if="item.name != '模型管理'"
                                :key="item.code"
                                :disabled="item.status === 'disable'"
                              >
                                <span
                                  :class="item.icon"
                                  style="margin-right: 8px"
                                ></span
                                >{{ $t(item.internationalizationKey) }}
                              </a-menu-item>
                            </template>
                          </a-menu>
                        </a-dropdown>
                      </div>
                    </li>
                    <li class="item-description" @click="handleContent(item)">
                      <a-tooltip :title="item.description">
                        {{ item.description && item.description.slice(0, 30) }}
                      </a-tooltip>
                    </li>
                    <li
                      class="item-productCatalogName"
                      @click="handleContent(item)"
                    >
                      {{ item.productCatalogOid ? loadSpectrumName(item.productCatalogName) :"" }}
                    </li>
                    <li class="item-avatar" @click="handleContent(item)">
                      <span class="avatar">
                        <userInfo :accounts="item.productManagers.map(item => item.account)"/>
                      </span>
                      <span class="private">
                        <a-tag
                          style="margin: 0"
                          :color="item.privateFlag ? 'blue' : ''"
                        >
                          {{
                            item.privateFlag
                              ? $t("txt_private")
                              : $t("txt_public")
                          }}
                        </a-tag>
                      </span>
                    </li>
                  </ul>
                </div>
              </a-col>
            </a-row>
            <a-pagination
              class="card-list-pagination"
              show-size-changer
              :show-total="
                (total) => $t('txt_total') + ` ${total} ` + $t('txt_pieces')
              "
              :total="pagerConfig.total"
              :page-size="pagerConfig.pageSize"
              :default-current="pagerConfig.current"
              :pageSizeOptions="pageSizeOptions"
              @change="onPageChange"
              @showSizeChange="onSizeChange"
            />
          </div>
        </a-spin>
      </div>
      <!-- <div class="no-data-wrap" v-else>
        <div class="no-data-con">
          <img src="../../assets/image/<EMAIL>" alt="" />
          <div>
            暂无产品容器，<span
              style="color: rgba(30, 32, 42, 0.65)"
              v-if="currentCreateRole"
              >点击 <span @click="showChoose(true)">新建</span> 添加</span
            >
          </div>
        </div>
      </div> -->
      <choose-template
        ref="choose-template"
        :visible="templateVisible"
        @showChoose="showChoose"
        @chooseAfter="chooseAfter"
      />
      <create-modal ref="create-modal" @confirm="formModalConfirm" />
      <!-- 详情页抽屉管理 -->
      <detail-drawer
        ref="detail-drawer"
        :currentRow="productRow"
        @fetchTable="onSearch"
      />
      <jw-modal-form
        :key="'1'"
        v-if="saveAsmodalVisible"
        :visible.sync="saveAsmodalVisible"
        :title="$t('txt_save_template')"
        :formDatas="saveAsParams"
        :confirmText="confirmText"
        @submit="submitSaveAs"
        :keys="[
          {
            prop: 'templateName',
            label: $t('txt_temp_name'),
            rules: [rules.required],
            props: {
              is: 'a-input',
              value: 'templateName',
              placeholder: $t('placeholder_name'),
            },
          },
          // {
          //   prop: 'isAddPer',
          //   label: '是否同步人员',
          //   props: {
          //     is: 'a-switch',
          //     value: 'isAddPer',
          //     placeholder: '是否同步人员',
          //   },
          // },
          {
            prop: 'description',
            label: $t('txt_description'),
            props: {
              is: 'a-textarea',
              value: 'description',
              placeholder: $t('txt_description'),
            },
          },
        ]"
      />
    </div>
  </jw-page>
</template>

<script>
//接口
import ModelFactory from "jw_apis/model-factory";
import {
  jwModalForm,
  jwSimpleTabs,
  jwAvatar,
  jwIcon,
  // jwToolbar,
  jwPage,
} from "jw_frame";
import { formatDate } from "jw_utils/moment-date";
import rules from "../../utils/rules.js";
import chooseTemplate from "./chooseTemplate.vue";
import detailDrawer from "./detailDrawer.vue";
import createModal from "./createModal.vue";
import userInfo from "components/user-info";
import {
  fetchContainerList,
  createContainer,
  fetchSpectrumTree,
  deleteContainer,
} from "apis/product-container";

// 获取查询当前角色是否有新建权限
const createRole = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
  method: "post",
});

// 获取该条数据可操作下拉列表
const getDropdownList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
  method: "post",
});

export default {
  components: {
    jwSimpleTabs,
    jwAvatar,
    jwIcon,
    chooseTemplate,
    detailDrawer,
    jwModalForm,
    createModal,
    // jwToolbar,
    jwPage,
    userInfo
  },
  inject: ["setBreadcrumb", "addBreadcrumb"],
  data() {
    return {
      spectrumList:[], // 型谱列表
      cardSpinning: true,
      currentCreateRole: false,
      // 另存为模板处理
      saveAsmodalVisible: false,
      saveAsParams: {
        name: "",
      },
      rules,
      confirmText: this.$t("btn_ok"),
      productRow: {},
      // 数据处理
      flatData: [],
      // 表单弹窗
      containerTemplateOid: "",
      formModalVisible: false,
      pageSizeOptions: ["10", "20", "50", "100"],
      formModalData: [
        {
          label: this.$t("txt_name"),
          type: "input",
          prop: "name",
          block: true,
        },
        {
          label: this.$t("txt_spectrum"),
          type: "tree",
          prop: "spectrum",
          // value: undefined, // 默认值
          treeData: [],
          // loadData: this.fetchSpectrumChild,
        },
        {
          label: this.$t("txt_team_temp"),
          type: "select",
          prop: "team",
          value: [],
          options: [],
          required: false,
        },
        {
          label: this.$t("txt_is_public"),
          type: "radio",
          prop: "privateFlag",
          value: true,
          radios: [
            { label: this.$t("txt_yes"), value: true },
            { label: this.$t("txt_no"), value: false },
          ],
          required: false,
        },
      ],
      templateVisible: false, // 模板选择模态框
      switchType: false,
      deleteModal: false,
      searchKey: "",
      tableData: [],
      selectedRows: [],
      confirmLoadingStatus: false,
      pagerConfig: {
        current: 1,
        pageSize: 20,
        total: 0,
      }, //分页配置
      tableLoading: false,
      selectRow: [],
    };
  },
  computed: {
    getHeader() {
      return [
        {
          field: "name",
          title:
            Jw.appName === "pdm"
              ? this.$t("txt_product_container_name")
              : this.$t("txt_product_name"),
          sortable: true, // 开启排序
          slots: {
            default: "productContent",
          },
        },
        // {
        //   field: "description",
        //   title: this.$t("txt_description"),
        //   sortable: true, // 开启排序
        // },
        // {
        //   field: "productCatalogName",
        //   title: this.$t("txt_subordinate_spectrum"),
        //   sortable: true, // 开启排序
        //   slots: {
        //     default: "catalogNameSlot",
        //   },
        // },
        // {
        //   field: "privateFlag",
        //   title: this.$t("txt_is_public"),
        //   width: "120",
        //   sortable: true, // 开启排序
        //   slots: {
        //     default: "privateSlot",
        //   },
        // },
        {
          field: "updateBy",
          title: this.$t("txt_product_manager"),
          width: "180",
          sortable: true, // 开启排序
          slots: {
            default: "productManagerSlot",
          },
        },
        {
          field: "createdTime",
          title: this.$t("txt_create_date"),
          width: 200,
          sortable: true, // 开启排序
          slots: {
            default: "creatdTime",
          },
        },
        {
          // 操作列定义
          field: "operation", //关键字
          slots: {
            default: "operation",
          },
          maxShowBtn: 1,
          btns: [
            {
              icon: "jwi-iconwarning-circle-full",
              title: this.$t("txt_detail"),
              key: "detail",
            },
            // {
            //   icon: "jwi-iconstar-full",
            //   title: "收藏",
            //   key: "star",
            // },iconuser-add
            {
              icon: "jwi-iconsave-as",
              title: this.$t("txt_save_template"),
              key: "saveAs",
            },
            {
              icon: "jwi-iconuser-add",
              title: this.$t("page_team_management"),
              key: "team",
            },
            // {
            //   icon: "jwi-icondelete",
            //   title: "删除",
            //   key: "delete",
            // },
          ],
        },
      ];
    },
    toolbars() {
      return [
        {
          name: this.$t("btn_new_create"),
          position: "before",
          type: "primary",
          key: "create",
        },
        {
          name: this.$t("btn_search"),
          position: "before",
          display: "input",
          value: this.searchKey,
          allowClear: false,
          placeholder: this.$t("search_text"),
          prefixIcon: "jwi-search",
          key: "search",
        },
        // {
        //   name: "批量操作",
        //   display: "dropdown",
        //   position: "after",
        //   key: "batch",
        //   menuList: [
        //     {
        //       name: "删除",
        //       key: "delete",
        //     },
        //   ],
        // },
        {
          name: "",
          position: "after",
          key: "switch",
          prefixIcon:
            this.switchType === true
              ? "jwi-iconlogo-windows-filled"
              : "jwi-iconlist",
        },
        {
          name: this.$t("btn_import"),
          position: "after",
          type: "",
          key: "import",
        },
        {
          name: this.$t("btn_export"),
          position: "after",
          type: "",
          key: "export",
        },
      ];
    },
  },

  created() {
    this.searchKey = localStorage.getItem('container-search') ? localStorage.getItem('container-search') : ''
    // 输入回调去抖动
    this.delaySearch = _.debounce(this.onSearch, 500);
    this.fetchTable({ current: 1, pageSize: 10 });
    this.checkCreateRole();
    this.initBreadcrumb();
    this.fetchfetchSpectrumList();
    // window.localStorage.setItem('deep',true)
  },
  watch: {
    searchKey: function(val){
      if(val){
        localStorage.setItem('container-search', val)
      }else{
        localStorage.removeItem('container-search')
      }

    },
    switchType(){
      this.selectedRows=[]
    },
    tableData(){
      this.selectedRows=[]
    }
  },
  methods: {
    // 时间格式化转换
    formatDateFn(date) {
      return formatDate(date,'YYYY-MM-DD');
    },
    // 获取 row  part 操作下拉列表
    dropdownVisibleChange(visible, row) {
      if (visible && !row.operationList) {
        this.$set(row, "loading_status", "loading");
        getDropdownList
          .execute({
            viewCode: "CONTAINERINSTANCE",
            // contextType: row.type,
            // objectType: row.type,
            objectOid: row.oid,
          })
          .then((data) => {
            this.$set(row, "operationList", data);
            this.$set(row, "loading_status", "done");
          })
          .catch((err) => {
            this.$error(err.msg || this.$t("msg_failed"));
            this.$set(row, "loading_status", "failed");
          });
      }
    },
    // 查询新建权限
    checkCreateRole() {
      let param = {
        viewCode: "PRODUCTOPERATION",
      };
      createRole
        .execute(param)
        .then((data) => {
          // console.log("权限查询返回结果", data);
          this.currentCreateRole = data[0].status === "disable" ? false : true;
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    // 另存为模板
    submitSaveAs(data) {
      console.log("data", data);
      let { productRow } = this;
      let param = {
        containerOid: productRow.oid,
        templateName: data.templateName,
        description: data.description,
        containerType: productRow.type,
        containerOid: productRow.oid,
        isAddPer:data.isAddPer?true:false
      };
      return ModelFactory.create({
        url: `${Jw.gateway}/customer/customerContainer/saveAsTemplate`,
        method: "post",
      })
        .execute(param)
        .then((data) => {
          console.log(data);
          this.saveAsmodalVisible = false;
          this.$success(this.$t("msg_success"));
          //刷新列表
          this.fetchTable({ current: 1, pageSize: 10 });
          this.$refs["choose-template"].getContainerTemplate();
        })
        .catch((err) => {
          this.tableLoading = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    // 跳转到产品容器内容页面
    handleContent(row) {
      let tabActive = row.modelDefinition === 'ResourceContainer' ? 'dz-product' : 'product'
      Jw.jumpToDetail(row, { tabActive: tabActive});
    },
    // 递归添加value值
    dataHandle(data) {
      let _this = this;
      data.value = data.oid;
      data.title = data.name;
      data.key = data.oid;
      if (data.children && data.children.length > 0) {
        for (let j = 0; j < data.children.length; j++) {
          _this.dataHandle(data.children[j]);
        }
      } else {
        data.value = data.oid;
        data.title = data.name;
        data.key = data.oid;
      }
      return data;
    },
    // 通过型谱名称查询该型谱下的节点
    loadSpectrumName(spectrumName){
      console.log("spectrumName",spectrumName);
      if(spectrumName == "" || spectrumName == null){
        return spectrumName;
      }
      let { spectrumList } = this;
      console.log(spectrumName);
      console.log(`spectrumList`,spectrumList);
      let arrPath = []//保存路径
      let count = 0
      function deepFinds(node,target) {
        arrPath.push(node.name)
        if(node.name == target){
          return count = count+2
        }else {
          if(node.children){
            for (let i=0;i<node.children.length;i++){
              let flag=deepFinds(node.children[i],target)
              if(!flag){
                arrPath.pop()
              }else{
                break
              }
            }
          }
        }
        return count > 0
      }
      deepFinds(spectrumList, spectrumName);
      let rootLen = spectrumList && spectrumList.name && spectrumList.name.length;
      let currentStr = arrPath.join(" / ").substr(rootLen + 2, arrPath.join("/").length);
      return currentStr;
    },
    // 获取型谱下拉树数据
    fetchfetchSpectrumList() {
      let _this = this;
      let param = {
        masterOid: "111",
        masterType: "ProductSpectrumRootNode",
      };
      fetchSpectrumTree
        .execute({ current: 1, pageSize: 500, ...param })
        .then((result) => {
          console.log(this);
          let deepResult = _this.dataHandle(result);
          // console.log("递归后的数据", deepResult);
          // console.log("初始化产品型谱列表", result);
          // console.log("当前数据类型", this.formModalData);
          this.spectrumList = result;
          this.formModalData[1].treeData = [deepResult];
        })
        .catch((err) => {
          console.log(err);
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    // 点击树形菜单获取子列表
    fetchSpectrumChild(treeNode) {
      let param = {
        masterOid: "111",
        masterType: "ProductSpectrumRootNode",
      };
      return new Promise((resolve, reject) => {
        const { key, value } = treeNode.dataRef;
        fetchSpectrumTree
          .execute({ oid: key, size: 500, ...param })
          .then((result) => {
            this.formModalData[1].treeData.find((v) => {
              if (v.key == key) {
                this.$set(
                  v,
                  "children",
                  v.children.concat(
                    result.rows.map((t) => ({
                      title: t.name,
                      value: t.attributeId,
                      key: t.oid,
                      selectable: true,
                      pid: v.oid, // 父节点id
                      isLeaf: true,
                    }))
                  )
                );
                return true;
              }
              return false;
            });
            resolve();
          })
          .catch((err) => reject(err));
      });
    },
    formModalConfirm(model) {
      let _this = this;
      console.log("当前新建表单输入信息", this.formModalData[1].treeData[0]);
      let { containerTemplateOid } = this;
      // 组织数据扁平化 根据oid查询当前数据
      let param = {
        ...model,
        productManagerAccount: true,
        containerTemplateOid: containerTemplateOid,
      };
      // console.log("新增产品容器参数信息", param);
      this.$refs["create-modal"].submitLoading = true;
      createContainer
        .execute(param)
        .then((data) => {
          // console.log("新增提交返回信息", data);
          this.$success(this.$t("msg_save_success"));
          this.formModalVisible = false;
          this.fetchTable({ current: 1, pageSize: 10 });
          this.$refs["create-modal"].submitLoading = false;
          this.$refs["create-modal"].handleCancel();
        })
        .catch((err) => {
          _this.$refs["create-modal"].submitLoading = false;
          _this.tableLoading = false;
          _this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    // 打开模板选择模态框
    showChoose(flag) {
      console.log(flag);
      this.templateVisible = flag;
      this.$refs["choose-template"].getContainerTemplate();
    },
    // 选择模板后续处理，打开新建产品容器模态框
    chooseAfter(dataSource) {
      console.log(dataSource);
      let { oid } = dataSource;
      this.containerTemplateOid = oid;
      this.templateVisible = false;
      this.formModalVisible = true;
      this.$refs["create-modal"].handleShow();
    },
    handleSwitchType() {
      this.switchType = !this.switchType;
    },
    // 选择列回调
    onSelectChange(args) {
      // console.log(args);
      this.selectRow = args;
    },
    // 操作列回调
    onOperateClick(key, row) {
      console.log(key, row);
      this.productRow = row;
      if (key === "details") {
        this.$refs["detail-drawer"].activeTab = "1";
        this.$refs["detail-drawer"].isOnlyEdit = false;
        this.$refs["detail-drawer"].showDrawer(false);
      } else if (key === "edit") {
        this.$refs["detail-drawer"].activeTab = "1";
        this.$refs["detail-drawer"].isOnlyEdit = true;
        this.$refs["detail-drawer"].showDrawer(true);
      } else if (key === "saveAsTemplate") {
        this.saveAsmodalVisible = true;
      } else if (key === "teamMaintain") {
        this.$router.push({path: "site-team-management-view", query: {drawerStatus: 'detail', type: "productTeam", team: JSON.stringify(row)}})
      } else if (key === "effectivityDefinition"){
        //构型有效性设置页面
        this.$router.push({path: 'effectivity-definition', query: {containerOid: row.oid, containerName: row.name}})
      } else if (key === "delete") {
        // console.log(row);
        this.onDelete(row);
      }
    },
    // 工具栏点击回调
    onToolClick({ key, row }) {
      if (key === "create") {
      } else if (key === "switch") {
        // 切换列表
        this.switchType = !this.switchType;
      } else if (key === "delete") {
        // console.log("this.selectedRows", this.selectedRows);
        this.fetchDelete(this.selectedRows);
      }
    },
    // 工具栏输入回调
    onToolInput({ key }, value) {
      // console.log(value);
      if (key === "search") {
        this.searchKey = value;
        this.delaySearch();
      }
    },
    // 删除
    onDelete(row) {
      this.fetchDelete([row]);
    },
    // 数据请求函数
    fetchTable() {
      let { searchKey, pagerConfig } = this;
      let { current, pageSize } = pagerConfig;
      let param = {
        index: current,
        size: pageSize,
        searchKey: searchKey.trim(),
      };
      this.tableLoading = true;
      if (this.switchType === true) {
        this.cardSpinning = true;
      }
      return fetchContainerList
        .execute(param)
        .then((data) => {
          console.log(data);
          this.tableLoading = false;
          this.tableData = data.rows;
          this.cardSpinning = false;
          this.pagerConfig.total = data.count;
          // 处理头像src
          data.rows.map((item) => {
            if (item.productManagers && item.productManagers.length > 0) {
              item.productManagers.map((item) => (item.src = item.avatar));
            }
          });
          return { data: data.rows };
        })
        .catch((err) => {
          this.tableLoading = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    //分页操作
    onPageChange(page, pageSize) {
      let { switchType } = this;
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;
      // console.log(page, pageSize);
      if (switchType) {
        this.fetchTable({ current: page, pageSize: pageSize });
      }
    },
    onSizeChange(pageSize, page) {
      let { switchType } = this;
      if (switchType) {
        this.pagerConfig.current = pageSize;
        this.pagerConfig.pageSize = page;
        this.fetchTable();
      } else {
        this.pagerConfig.current = page;
        this.pagerConfig.pageSize = pageSize;
      }
    },
    initBreadcrumb() {
      let breadcrumbData = [];
      this.setBreadcrumb(breadcrumbData);
      window.localStorage.setItem("index_", 2);
    },
    // 删除列操作
    fetchDelete(row) {
      // console.log("当前删除操作的数据", row);
      let param = row.map((item) => item.oid);
      this.$confirm({
        width: "280px",
        class: "deleteModal",
        closable: true,
        mask: false,
        title: (
          <p style="font-size:16px;font-weight:500;color:rgba(30, 32, 42, 0.85);">
            {this.$t("btn_delete")}
          </p>
        ),
        content: (
          <p style="font-size:14px;font-weight:400;color:rgba(30, 32, 42, 0.45);">
            {this.$t("txt_is_delete_rq")}
          </p>
        ),
        cancelText: this.$t("btn_cancel"),
        okText: this.$t("btn_confirm"),
        onOk: () => {
          deleteContainer
            .execute(param)
            .then((data) => {
              // console.log(data);
              this.$success(this.$t("txt_delete_success"));
              //刷新列表
              this.fetchTable({ current: 1, pageSize: 10 });
            })
            .catch((err) => {
              this.$error(err.msg || this.$t("msg_failed"));
            });
        },
      });
    },
    // 输入回调刷新表格数据
    onSearch() {
      (this.pagerConfig = {
        current: 1,
        pageSize: 20,
        total: 0,
      }), //分页配置
        this.fetchTable({ current: 1, pageSize: 10 });
    }
  },
};
</script>

<style lang="less">
.product-container.produce-container {
  padding: 0;
  box-shadow: none;
  display: flex;
  flex-direction: column;

  .product-list {
    padding: 0 5px 0px 12px;
    flex-grow: 1;
    height: 20px;
    .card-list-pagination {
      padding: 10px 8px 10px 0;
      text-align: right;
    }

    .ant-spin-nested-loading {
      height: 100%;
      > .ant-spin-container {
        height: 100%;
        > .card-list {
          height: 100%;
          display: flex;
          flex-direction: column;
          .ant-row {
            flex-grow: 1;
            height: 20px;
            overflow: auto;
            margin: 0 !important;
            // 卡片样式
            .card-item {
              border: 8px solid #fff;
              &:nth-of-type(4n + 0) {
                margin-right: 0;
                padding-right: 0!important;
              }
              .card-content {
                height: 140px;
                padding: 16px;
                background: rgba(30, 32, 42, 0.04);
                border: 1px solid rgba(30, 32, 42, 0.04);
                border-radius: 4px 4px 4px 4px;
                &:hover {
                  background: #f0f7ff;
                  border: 1px solid #a4c9fc;
                  cursor: pointer;
                }
                .item-info {
                  margin: 0;
                  .item-title {
                    position: relative;
                    display: inline-block;
                    width: 100%;
                    color: rgba(30, 32, 42, 0.85);
                    .item-title-name {
                      width: 86%;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      overflow: hidden;
                      word-break: break-all;
                      font-weight: 500;
                      font-size: 14px;
                      color: rgba(30, 32, 42, 0.85);
                      cursor: pointer;
                    }
                    .item-name {
                      margin-left: 8px;
                      font-weight: 500;
                    }
                    .item-more {
                      position: absolute;
                      right: 5px;
                      top: 0px;
                      z-index: 10;
                    }
                  }
                  .item-description {
                    height: 26px;
                    line-height: 26px;
                    font-size: 12px;
                    color: rgba(30, 32, 42, 0.45);
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  }
                  .item-productCatalogName {
                    height: 28px;
                    line-height: 27px;
                    font-size: 12px;
                    font-weight: 500;
                    color: #1e202a;
                  }
                  .item-avatar {
                    margin-top: 4px;
                    height: 40px;
                    .avatar {
                      float: left;
                    }
                    .private {
                      line-height: 40px;
                      float: right;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    .jw-table {
      padding: 10px 15px 0 10px;
    }
  }
  .product-filter {
    padding: 0 18px;
    margin-bottom: 4px;
    display: flex;
    justify-content: space-between;
  }
  .no-data-wrap {
    height: 100%;
    .no-data-con {
      height: calc(~"100vh - 130px");
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      img {
        margin-bottom: 25px;
      }
      span {
        color: #255ed7;
        cursor: pointer;
      }
      i {
        cursor: pointer;
      }
    }
  }
}
/deep/.form-model-item {
  &:nth-of-type(2) {
    margin-right: 30px;
  }
}
// tab页签样式
.wrap-class {
  /deep/.item-class {
    margin: 0 20px 0 0;
    padding: 4px 0;
    color: rgba(30, 32, 42, 0.65);
  }
  /deep/.item-class-active {
    font-size: 14px;
    font-weight: 500;
    color: rgba(30, 32, 42, 0.85);
  }
}
</style>
<style>
.deleteModal .ant-modal-body {
  padding: 24px;
}
.deleteModal .ant-modal {
  /* top: 112px;
  left: 42%; */
}
.deleteModal .ant-modal-close-x {
  line-height: 69px;
}
.deleteModal
  .ant-modal-confirm-body
  > .anticon
  + .ant-modal-confirm-title
  + .ant-modal-confirm-content {
  margin-left: 0;
}
.deleteModal .ant-modal-confirm-btns .ant-btn {
  width: 75px;
  float: right;
}
.deleteModal .ant-modal-confirm-btns .ant-btn.ant-btn-primary {
  margin-right: 8px;
  /* background-color: rgba(37, 94, 215, 1);
  border-color: rgba(37, 94, 215, 1); */
  /* background-color: #1890ff; */
}
.operation-dropdown-overlay {
  /* width: 150px; */
}
</style>
