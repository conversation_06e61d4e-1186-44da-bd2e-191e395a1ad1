<template>
  <div>
    <a-modal
      :title="$t('txt_create_container')"
      :width="712"
      v-model.trim="createVisible"
      @ok="handleOk"
    >
      <template slot="footer">
        <a-button
          key="submit"
          type="primary"
          :loading="submitLoading"
          @click="onSubmit"
        >
        {{$t('btn_done')}}
        </a-button>
        <a-button key="back" @click="handleCancel">{{$t('btn_cancel')}}  </a-button>
      </template>
      <jw-layout-builder
        ref="ref_appBuilder"
        type="Model"
        :layoutName="'create'"
        :modelName="'ProductContainer'"
      >
        <template slot="catalogSlot">
          <a-tree-select
            :placeholder="$t('txt_select_spectrum')"
            v-model.trim="spectrum"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            :tree-data="treeData"
            @change="treeSelect"
          >
            <template slot="folderIconTitle" slot-scope="record">
              <div>
                <span style="display: inline-block">
                  <jw-icon :type="record.modelIcon" />
                </span>
                <span>
                  {{ record.name }}
                </span>
              </div>
            </template>
          </a-tree-select>
        </template>
      </jw-layout-builder>
    </a-modal>
  </div>
</template>
<script>
import { jwLayoutBuilder } from "jw_frame";
import { fetchSpectrumTree } from "apis/product-container";
export default {
  components: {
    jwLayoutBuilder,
  },
  props: {},
  data() {
    return {
      submitLoading: false,
      createVisible: false,
      productCatalogOid: "",
      spectrum: "",
      // 树形下拉选择开始
      treeData: [],
    };
  },
  created() {},
  methods: {
    // 递归添加value值
    dataHandle(data, onlyDirectChildren = false) {
      const scopedSlots = { title: "folderIconTitle" };
      let arr = [];
      const loop = (tree) => {
        tree.map((item, index) => {
          // item.title = item.name;
          item.key = item.oid;
          item.value = item.oid;
          item.scopedSlots = scopedSlots;
          if (item.type === "Container") {
            delete tree[index];
          }
          // 只获取第一层
          if (onlyDirectChildren) {
            const temp = {
              ...item,
            };
            delete temp.children;
            arr.push(temp);
            return;
          }
          if (item.children && item.children.length > 0) {
            item.children.map((item) => (item.childType = "child"));
            loop(item.children);
          }
        });
      };
      loop(data);

      return onlyDirectChildren ? arr : data;
    },
    // 获取型谱下拉树数据
    fetchfetchSpectrumList() {
      let _this = this;
      let param = {
        masterOid: "111",
        masterType: "ProductSpectrumRootNode",
      };
      fetchSpectrumTree
        .execute({ current: 1, pageSize: 500, ...param })
        .then((result) => {
          console.log(result);
          if(result.children){
            let deepResult = _this.dataHandle(result.children);
            // console.log("递归后的数据", [deepResult]);
            // console.log("初始化产品型谱列表", result);
            // console.log("当前数据类型", this.formModalData);
            this.treeData = deepResult;
          }
        })
        .catch((err) => {
          console.log(err);
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    //树选择
    treeSelect(value, label, extra) {
      this.productCatalogOid = value;
      console.log("treeSelect", value, label, extra);
      let appBuilder = this.$refs.ref_appBuilder;
      appBuilder.insData.productCatalogOid = value;
      console.log(appBuilder);
      // appBuilder.clearValidate();
    },
    onSubmit() {
      let _this = this;
      let appBuilder = this.$refs.ref_appBuilder;
      appBuilder &&
        appBuilder.validate().then(() => {
          let appValue = appBuilder.getValue();
          let productManagerAccounts =
            appValue.productManagerAccounts &&
            appValue.productManagerAccounts.map((item) => item.account);
          let params = {
            ...appValue,
            name: appValue.name,
            privateFlag: appValue.privateFlag,
            productCatalogOid: appValue.productCatalogOid,
            productManagerAccounts,
            teamTemplateOid: appValue.teamTemplateOid || "",
          };
          console.log(params);
          _this.$emit("confirm", params);
        });
    },
    handleOk(e) {
      this.loading = true;
      setTimeout(() => {
        this.createVisible = false;
        this.loading = false;
      }, 3000);
    },
    handleShow() {
      this.fetchfetchSpectrumList();
      this.spectrum = "";
      let promise = new Promise((reject, resolve) => {
        this.createVisible = true;
        reject();
      });
      promise.then(() => {
        let appBuilder = this.$refs.ref_appBuilder;
        console.log(appBuilder);
        appBuilder.instanceData = {
          name: "",
          privateFlag: false,
          productCatalogOid: "",
          teamTemplateOid: "",
        };
      });
    },
    handleCancel(e) {
      this.createVisible = false;
    },
  },
};
</script>
<style lang="less" scoped>
/deep/.ant-modal-header {
  border-bottom: none;
}
.add-user {
  float: left;
  margin-right: 8px;
  height: 100%;
  cursor: pointer;
  .add-btn {
    display: inline-block;
    width: 32px;
    height: 32px;
    background: #f0f7ff;
    border: 1px solid #a4c9fc;
    border-radius: 50%;
    text-align: center;
    line-height: 29px;
  }
}
</style>