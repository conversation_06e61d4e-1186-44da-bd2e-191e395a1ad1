<template>
	<div>
		<a-drawer
			:headerStyle="{ borderBottom: 0 }"
			:bodyStyle="{ paddingTop: 0 }"
			:width="860"
			:visible="drawerVisible"
			:body-style="{ paddingBottom: '80px' }"
			@close="onClose"
		>
			<!-- <a-tabs
        default-active-key="1"
        :activeKey="activeTab"
        @change="tabCallBack"
      >
        <a-tab-pane
          key="1"
          :tab="$t('detailed_info')"
        > -->
			<jw-layout-builder
				ref="ref_appBuilder"
				type="Model"
				:layoutName="isShowEdit === false ? 'show' : 'update'"
				:modelName="'ProductContainer'"
				:instanceData="currentDetailRow"
			>
				<template slot="templateName">
					<a-input v-model.trim="currentDetailRow.containerTemplateName" disabled />
				</template>
				<template slot="catalogName">
					<a-tree-select
						allowClear
						:placeholder="$t('txt_select_spectrum')"
						v-model.trim="productCatalogOid"
						:dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
						:tree-data="treeData"
						@change="treeSelect"
					>
						<template slot="folderIconTitle" slot-scope="record">
							<div>
								<span style="display: inline-block">
									<jw-icon :type="record.modelIcon" />
								</span>
								<span>
									{{ record.name }}
								</span>
							</div>
						</template>
					</a-tree-select>
				</template>
				<template slot="teamName">
					<a-input v-model.trim="currentDetailRow.teamTemplateName" disabled />
				</template>
			</jw-layout-builder>
			<!-- </a-tab-pane>
        <a-tab-pane
          key="2"
          :tab="$t('txt_manager_team')"
        >
          <jw-table
            height="700"
            row-id="oid"
            ref="tree"
            :tree-config="{
              lazy: false,
              expandRowKeys: expandRowKeys,
              loadMethod: loadChildrenMethod,
            }"
            :loading="roleLoading"
            :showPage="false"
            :columns="roleGetHeader"
            :dataSource="currentRowRole"
            :toolbars="toolbars"
            @onToolClick="onToolClick"
            @onToolInput="onToolInput"
            @onOperateClick="roleOperateClick"
          >
            <div
              name="empty"
              slot="empty"
            ></div>
            <template #tree-content="{ row }">
              <span v-if="row.hasChild">
                <jw-icon type="#jwi-wenjianga-youneiyong" />
                {{ row.displayName || row.name }}
              </span>
              <span v-if="!row.hasChild">
                <jw-avatar
                  :showName="true"
                  :tag="true"
                  :data="row"
                />
              </span>
            </template>
          </jw-table>
        </a-tab-pane>
      </a-tabs> -->
			<template slot="title">
				<div class="product-title">
					<span class="title-info">
						{{ currentRow.name }}
						<a-tag :color="currentRow.privateFlag ? 'blue' : ''">
							{{
								currentRow.privateFlag ? $t("txt_private") : $t("txt_public")
							}}
						</a-tag>
					</span>
					<span
						class="title-opration"
						v-if="!isShowEdit && isEditRole"
						@click="handleShowEdit"
					>
						<jw-icon type="jwi-iconedit" />
					</span>
				</div>
			</template>
			<div
				class="edit-footer"
				v-if="isEditRole && isShowEdit && activeTab === '1'"
			>
				<a-button type="primary" :loading="saveLoading" @click="handleSave">
					{{ $t("btn_save") }}
				</a-button>
				<a-button :style="{ marginRight: '8px' }" @click="onClose">
					{{ $t("btn_cancel") }}
				</a-button>
			</div>
		</a-drawer>
		<!-- 表单录入弹窗 -->
		<jw-user-modal
			ref="user-modal"
			:isCheckbox="ischeckBox"
			:visible="isRoleVisible"
			@handleSubmit="handleUser"
			@closeUserModal="handleUserClose"
		/>
		<jw-user-modal
			type="Role"
			ref="role-modal"
			:isCheckbox="ischeckBox"
			:visible="roleModalVisible"
			:teamTemplateOid="currentRow.oid"
			@handleSubmit="roleModalCallback"
			@closeUserModal="handleRoleClose"
		/>
		<jw-user-modal
			ref="choose-managger"
			:isCheckbox="ischeckBox"
			:visible="isRoleVisible"
			@handleSubmit="handleUser"
		/>
	</div>
</template>
<script>
import { jwAvatar, jwUserModalV2, jwIcon } from "jw_frame";
import { jwLayoutBuilder } from "jw_frame";
import { getCookie } from "jw_utils/cookie";
import { formatDate } from "jw_utils/moment-date";
import rules from "../../utils/rules.js";
import _ from "lodash";
//接口
import ModelFactory from "jw_apis/model-factory";
import {
	libraryTableModel,
	createTeamModel,
	addRoleModel,
	bindUserModel,
	getTeamRole,
	detailContainer,
	updateContainer,
	fetchSpectrumTree,
} from "apis/product-container";

export default {
	name: "detailDrawer",
	props: {
		currentRow: Object,
	},
	components: {
		jwUserModal: jwUserModalV2,
		jwAvatar,
		jwIcon,
		jwLayoutBuilder,
	},
	inject: ["setBreadcrumb", "addBreadcrumb"],
	data() {
		return {
			productCatalogOid: "",
			productCatalogName: "",
			saveLoading: false,
			// 处理查看和编辑权限
			isEditRole: false,
			isShowEdit: false,
			isOnlyEdit: false, // 编辑状态下不显示右上角编辑图标
			activeTab: "1",
			drawerVisible: false,
			currentDetailRow: {},
			drawerStatus: "",
			saveAsmodalVisible: false,
			rules,
			// 角色列表信息
			ischeckBox: true,
			isRoleVisible: false,
			roleTableData: [],
			form: this.$form.createForm(this),
			deleteModal: false,
			searchKey: "",
			tableData: [],
			expandRowKeys: [],
			selectedRows: [],
			modalVisible: false,
			addVisible: "",
			confirmLoadingStatus: false,
			roleModalData: [
				{
					label: this.$t("btn_users"),
					type: "select",
					prop: "user",
					value: [],
					multiple: "multiple",
					showSearch: true, //是否开启搜索功能
					options: [],
				},
			],
			roleLoading: false,
			roleModalVisible: false,
			pagination: {
				page: 1,
				size: 10,
				total: 30,
			},
			tableLoading: false,
			total: 0,
			currentRowOid: "", // 当前编辑项的 id
			// currentRow: {},
			// 查询当前团队下的角色
			currentRowData: {},
			currentRowRole: [],
			// 当前产品经理处理
			productManagers: [],
			// 树形下拉选择开始
			treeData: [],
		};
	},
	computed: {
		roleGetHeader() {
			let _this = this;
			return [
				{
					field: "displayName",
					title: this.$t("txt_name"),
					treeNode: true,
					slots: {
						default: "tree-content",
					},
				},
				{
					field: "description",
					title: this.$t("txt_description"),
				},
				{
					field: "operation",
					btns: [
						{
							icon: "jwi-iconuser-add",
							title: this.$t("btn_add_user"),
							key: "addUser",
							isShow: function (row) {
								// 按钮显示逻辑
								return (
									row.name !== "member" && _this.isEditRole && row.hasChild
								);
							},
						},
						{
							icon: "jwi-icondelete",
							title: this.$t("btn_delete"),
							key: "delete",
							isShow:
								// 按钮显示逻辑
								(row) =>
									row.name !== "member" &&
									row.name !== "ProductManager" &&
									_this.isEditRole,
						},
					],
				},
			];
		},
		toolbars() {
			return [
				{
					name: this.$t("add_role"),
					position: "before",
					type: "primary",
					key: "create",
					isVisible: this.isEditRole,
				},
				{
					name: this.$t("btn_search"),
					position: "before",
					display: "input",
					value: this.searchKey,
					allowClear: true,
					placeholder: this.$t("msg_input"),
					suffixIcon: "jwi-iconsearch",
					key: "search",
				},
			];
		},
	},
	created() {
		// window.localStorage.setItem('deep',true)
		this.fetchfetchSpectrumList();
	},
	watch: {
		drawerVisible(value) {
			console.log("currentRow", this.currentRow);
			this.currentRow.operationList &&
				this.currentRow.operationList.map((item, index) => {
					if (item.permissionKey === "edit" && item.status === "enable") {
						this.isEditRole = true;
					}
				});
			if (this.drawerVisible === true) {
				this.getProductDetail();
				// this.getCurrentRowRole();
			}
		},
	},
	methods: {
		// 递归添加value值
		dataHandle(data, onlyDirectChildren = false) {
			const scopedSlots = { title: "folderIconTitle" };
			let arr = [];
			const loop = (tree) => {
				tree.map((item, index) => {
					// item.title = item.name;
					item.key = item.oid;
					item.value = item.oid;
					item.scopedSlots = scopedSlots;
					if (item.type === "Container") {
						delete tree[index];
					}
					// 只获取第一层
					if (onlyDirectChildren) {
						const temp = {
							...item,
						};
						delete temp.children;
						arr.push(temp);
						return;
					}
					if (item.children && item.children.length > 0) {
						item.children.map((item) => (item.childType = "child"));
						loop(item.children);
					}
				});
			};
			loop(data);

			return onlyDirectChildren ? arr : data;
		},
		//树选择
		treeSelect(value, label, extra) {
			console.log("treeSelect", value, label, extra);
			let appBuilder = this.$refs.ref_appBuilder;
			if (value) {
				let currentTreeData = extra.triggerNode.dataRef;
				this.productCatalogOid = value;
				this.productCatalogName = currentTreeData.name;
			} else {
				this.productCatalogOid = "";
				this.productCatalogName = "";
			}
			console.log(appBuilder);
		},
		// 获取型谱下拉树数据
		fetchfetchSpectrumList() {
			let _this = this;
			let param = {
				masterOid: "111",
				masterType: "ProductSpectrumRootNode",
			};
			fetchSpectrumTree
				.execute({ current: 1, pageSize: 500, ...param })
				.then((result) => {
					console.log(result);
					if (result.children) {
						let deepResult = _this.dataHandle(result.children);
						// console.log("递归后的数据", [deepResult]);
						// console.log("初始化产品型谱列表", result);
						// console.log("当前数据类型", this.formModalData);
						this.treeData = deepResult;
					}
				})
				.catch((err) => {
					console.log(err);
					this.$error(err.msg || this.$t("msg_failed"));
				});
		},
		// 打开选择产品经理
		handleChoose() {
			let { productManagers } = this;
			this.$refs["user-modal"].show().then((data) => {
				console.log("productManagers", data);
				this.productManagers = [...data];
			});
		},
		// 是否是编辑状态
		handleShowEdit() {
			let { isShowEdit } = this;
			this.isShowEdit = !isShowEdit;
		},
		// 切换tab
		tabCallBack(event) {
			console.log(event);
			this.activeTab = event;
		},
		// 获取当前容器详情
		getProductDetail() {
			let { currentRow } = this;
			let param = {
				oid: currentRow.oid,
			};
			detailContainer
				.execute(param)
				.then((data) => {
					console.log("当前产品详细信息", data);
					data.productManagers.map((item) => (item.src = item.avatar));
					this.currentDetailRow = { ...data };
					this.productCatalogOid = data.productCatalogOid;
					this.productManagers = [...data.productManagers];
					this.getCurrentRowRole(data.team.oid);
				})
				.catch((err) => {
					this.confirmLoadingStatus = false;
					this.$error(err.msg || this.$t("msg_failed"));
				});
		},
		// 角色添加绑定用户
		handleUser(dataSource) {
			let { currentRow, currentRowData, currentDetailRow } = this;
			let { team } = currentRow;
			let tenantOid = getCookie("tenantOid");
			console.log("当前选择的用户", dataSource);
			console.log("currentRow", currentRow);
			console.log("currentRowData", currentRowData);
			let teamUserList = [];
			dataSource.map((item) => {
				teamUserList.push({
					tenantOid: tenantOid,
					oid: item.oid,
					name: item.name,
					type: item.type,
					description: item.description || "",
				});
			});
			this.confirmLoadingStatus = true;
			let param = {
				teamRoleOid: currentRowData.oid,
				teamUserList: teamUserList,
				containerOid: currentRow.oid,
			};
			console.log("当前提交参数", teamUserList);
			return bindUserModel
				.execute(param)
				.then((data) => {
					// console.log(data);
					this.$success(this.$t("msg_save_success"));
					this.isRoleVisible = false;
					this.getCurrentRowRole(currentDetailRow.team.oid);
					//刷新列表
					this.fetchTable({ current: 1, pageSize: 10 });
					this.$emit("fetchTable", { current: 1, pageSize: 10 });
				})
				.catch((err) => {
					this.confirmLoadingStatus = false;
					this.$error(err.msg || this.$t("msg_failed"));
				})
				.finally(() => {
					this.expandRowKeys = [currentRowData.oid];
				});
		},
		// 关闭用户选择
		handleUserClose() {
			this.isRoleVisible = false;
		},
		loadChildrenMethod({ row }) {
			console.log(row);
			let { currentRow, currentRowData } = this;
			let param = { teamTemplateRoleOid: row.oid };
			console.log("teamTemplateRoleOid", row.oid);
			// 异步加载子节点
			return new Promise((resolve, reject) => {
				setTimeout(() => {
					ModelFactory.create({
						url: `${Jw.gateway}/${Jw.containerService}/team/searchTeamUser?teamRoleOid=${row.oid}`,
						method: "get",
					})
						.execute(param)
						.then((data) => {
							data.map((item) => (item.teamTemplateRoleOid = row.oid));
							console.log("当前角色下的用户", data);
							resolve(data);
						})
						.catch((err) => {
							this.confirmLoadingStatus = false;
							this.$error(err.msg || this.$t("msg_failed"));
						});
				}, 500);
			});
		},
		// 查询当前团队绑定的角色列表
		getCurrentRowRole(teamOid) {
			this.roleLoading = true;
			getTeamRole({ oid: teamOid, searchKey: this.searchKey })
				.then((data) => {
					// console.log("当前返回角色信息", data);
					data.map((item) => {
						item.hasChild = true;
						item.children = [...item.users];
						return item;
					});
					this.expandRowKeys = data.map((item) => item.oid);
					//刷新列表
					this.currentRowRole = [...data];
				})
				.catch((err) => {
					this.$error(err.msg || this.$t("msg_failed"));
				})
				.finally(() => {
					this.roleLoading = false;
				});
		},
		// 绑定角色
		roleModalCallback(dataSource) {
			let { currentRow, currentDetailRow } = this;
			let { team } = currentDetailRow;
			console.log("dataSource", dataSource);
			console.log("currentRow", currentRow);
			console.log("team", team);
			dataSource.map((item) => (item.name = item.uniqueKey));
			let param = {
				teamOid: team.oid,
				teamRoleList: dataSource,
			};
			console.log("param", param);
			this.confirmLoadingStatus = true;
			return addRoleModel
				.execute(param)
				.then((data) => {
					// console.log(data);
					this.$success(this.$t("msg_save_success"));
					this.roleModalVisible = false;
					this.getCurrentRowRole(team.oid);
					//刷新列表
					this.fetchTable({ current: 1, pageSize: 10 });
					//刷新列表
					this.$emit("fetchTable", { current: 1, pageSize: 10 });
				})
				.catch((err) => {
					this.confirmLoadingStatus = false;
					this.$error(err.msg || this.$t("msg_failed"));
				});
		},
		// 关闭角色绑定选择
		handleRoleClose() {
			this.roleModalVisible = false;
		},
		// 创建团队
		createTeam(params) {
			let tenantOid = getCookie("tenantOid");
			let param = {
				containerModelType: "TeamContainer",
				containerOid: tenantOid,
				name: params.name,
				description: "",
				disabled: true,
			};
			return createTeamModel
				.execute(param)
				.then((data) => {
					console.log(data);
					this.modalVisible = false;
					this.$success(this.$t("msg_save_success"));
					//刷新列表
					this.fetchTable({ current: 1, pageSize: 10 });
				})
				.catch((err) => {
					this.tableLoading = false;
					this.$error(err.msg || this.$t("msg_failed"));
				});
		},
		// 关闭团队新增弹窗
		cancelTeam() {
			this.modalVisible = false;
		},
		// 展示当前团队详细信息
		showDrawer(isEdit) {
			if (isEdit) {
				this.isShowEdit = true;
			} else {
				this.isShowEdit = false;
			}
			this.drawerVisible = true;
		},
		// 关闭当前团队信息
		onClose() {
			this.isShowEdit = false;
			this.drawerVisible = false;
			this.form.resetFields();
		},
		// 编辑当前团队
		handleSave() {
			console.log(this.currentRow);
			let appBuilder = this.$refs.ref_appBuilder;
			appBuilder &&
				appBuilder.validate().then(() => {
					let appValue = appBuilder.getValue();
					let productManagers = appValue.productManagers;
					let { currentDetailRow, productCatalogOid, productCatalogName } =
						this;
					let productManagersOids = productManagers.map((item) => item.account);
					let param = {
						...appValue,
						productCatalogOid: productCatalogOid || "",
						productCatalogName: productCatalogName || "",
						teamTemplateOid: currentDetailRow.teamTemplateOid,
						containerTemplateOid: currentDetailRow.containerTemplateOid,
						productManagerAccounts: productManagersOids,
					};
					console.log("param", param);
					this.saveLoading = true;
					updateContainer
						.execute(param)
						.then((data) => {
							// console.log(data);
							this.drawerVisible = false;
							this.saveLoading = false;
							this.$success(this.$t("msg_save_success"));
							//刷新列表
							this.$emit("fetchTable", { current: 1, pageSize: 10 });
						})
						.catch((err) => {
							this.saveLoading = false;
							this.$error(err.msg || this.$t("msg_failed"));
						});
				});
		},
		// 选择列回调
		onSelectChange(args) {
			console.log(args);
			this.selectedRows = [...args];
		},
		// 操作角色列回调
		roleOperateClick(key, row) {
			console.log(row);
			let { currentRow } = this;
			this.currentRowData = row;
			if (key === "addUser") {
				let selectRow = _.cloneDeep(row.children);
				this.$refs["user-modal"].show({ type: "User" }).then((data) => {
					this.handleUser(data);
				});
				this.isRoleVisible = true;
			} else if (key === "delete") {
				this.onRoleDelete(row);
			}
		},
		// 删除当前团队绑定角色
		onRoleDelete(row) {
			let { currentRow, currentRowData, currentDetailRow } = this;
			let { team } = currentRow;
			let tenantOid = getCookie("tenantOid");
			console.log("当前删除角色currentRow", currentRow);
			console.log("currentRowData", currentRowData);
			console.log("row", row);
			let unBindUrl = "";
			let param = {};
			if (row.hasChild) {
				unBindUrl = `${Jw.gateway}/${Jw.containerService}/team/unBindRole`;
				param.teamOid = currentDetailRow.team.oid;
				param.teamRoleOids = [row.oid];
				delete param.containerOid;
			} else {
				unBindUrl = `${Jw.gateway}/${Jw.containerService}/team/unBindUser`;
				param.teamRoleOid = currentRowData.parentId;
				param.teamUserOidList = [row.oid];
				param.containerOid = currentRow.oid;
			}
			this.$confirm({
				width: "280px",
				class: "deleteModal",
				closable: true,
				mask: false,
				title: (
					<p style="font-size:16px;font-weight:500;color:rgba(30, 32, 42, 0.85);">
						{row.hasChild
							? this.$t("txt_user_delete")
							: this.$t("msg_role_delete")}
					</p>
				),
				content: (
					<p style="font-size:14px;font-weight:400;color:rgba(30, 32, 42, 0.45);">
						{row.hasChild
							? this.$t("msg_delete_confirm_data")
							: this.$t("txt_sure_continue")}
					</p>
				),
				cancelText: this.$t("btn_cancel"),
				okText: this.$t("btn_ok"),
				onOk: () => {
					ModelFactory.create({
						url: unBindUrl,
						method: "post",
					})
						.execute(param)
						.then((data) => {
							// console.log(data);
							this.$success(this.$t("txt_delete_success"));
							//刷新列表
							this.getCurrentRowRole(currentDetailRow.team.oid);
							this.$emit("fetchTable", { current: 1, pageSize: 10 });
						})
						.catch((err) => {
							this.$error(err.msg || this.$t("msg_failed"));
						})
						.finally(() => {
							if (row.hasChild) this.expandRowKeys = [];
							else this.expandRowKeys = [currentRowData.parentId];
						});
				},
			});
		},
		// 操作列回调
		onOperateClick(key, row) {
			this.currentRow = { ...row };
			let teamOid = currentDetailRow.team.oid;
			if (key === "edit") {
				this.showDrawer();
				this.drawerStatus = "edit";
				this.getCurrentRowRole(teamOid);
			} else if (key === "detail") {
				this.showDrawer();
				this.drawerStatus = "detail";
				this.getCurrentRowRole(teamOid);
			} else if (key === "saveAs") {
				this.saveAsmodalVisible = true;
			} else if (key === "more") {
			} else if (key === "delete") {
				this.onDelete(row);
			}
		},
		// // 工具栏点击回调
		// onToolClick({ key }) {
		//   if (key === "create") {
		//     //
		//     this.addVisible = "create";
		//     this.modalVisible = true;
		//   } else if (key === "compare") {
		//     //
		//   } else if (key === "delete") {
		//     let { selectedRows } = this;
		//     this.fetchDelete(selectedRows);
		//   }
		// },
		// 工具栏点击回调
		onToolClick({ key }) {
			if (key === "create") {
				this.handleRole();
			}
		},
		// 工具栏输入回调
		onToolInput({ key }, value) {
			let teamOid = this.currentDetailRow.team.oid;
			if (key === "search") {
				this.searchKey = value;
				this.getCurrentRowRole(teamOid);
			}
		},
		// 删除团队
		onDelete(row) {
			this.fetchDelete([row]);
		},
		// 数据请求函数
		fetchTable({ current, pageSize }) {
			let { searchKey } = this;
			let tenantOid = getCookie("tenantOid");
			let param = {
				containerModelType: "TeamContainer",
				containerOid: tenantOid,
				searchKey, // 模糊搜索条件
				index: 1,
				size: 10,
			};
			this.tableLoading = true;
			return libraryTableModel
				.execute(param)
				.then((data) => {
					console.log(data);
					this.tableLoading = false;
					this.tableData = data;
					return { data: data, total: data.length };
				})
				.catch((err) => {
					this.tableLoading = false;
					this.$error(err.msg || this.$t("msg_failed"));
				});
		},
		// 删除团队
		fetchDelete(row) {
			console.log("当前删除的数据：", row);
			let paramData = row.map((item) => item.oid);
			let param = {
				teamTemplateOid: paramData[0],
			};
			console.log("param", param);
			this.$confirm({
				width: "280px",
				class: "deleteModal",
				closable: true,
				mask: false,
				title: (
					<p style="font-size:16px;font-weight:500;color:rgba(30, 32, 42, 0.85);">
						{this.$t("btn_batch_delete")}
					</p>
				),
				content: (
					<p style="font-size:14px;font-weight:400;color:rgba(30, 32, 42, 0.45);">
						{this.$t("msg_system_del")}
					</p>
				),
				cancelText: this.$t("btn_cancel"),
				okText: this.$t("btn_ok"),
				onOk: () => {
					ModelFactory.create({
						url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/delete?teamTemplateOid=${paramData[0]}`,
						method: "post",
					})
						.execute(param)
						.then((data) => {
							// console.log(data);
							this.$success(this.$t("txt_delete_success"));
							//刷新列表
							this.fetchTable({ current: 1, pageSize: 10 });
						})
						.catch((err) => {
							this.$error(err.msg || this.$t("msg_failed"));
						});
				},
			});
		},
		// 角色处理
		handleRole() {
			let { currentRowRole } = this;
			console.log("当前角色", currentRowRole);
			let selectRow = _.cloneDeep(currentRowRole);
			this.$refs["role-modal"].show().then((data) => {
				this.roleModalCallback(data);
			});
		},
		// 时间格式化转换
		formatDateFn(date) {
			return formatDate(date);
		},
	},
};
</script>
<style lang="less" scoped>
.edit-footer {
	position: absolute;
	right: 0;
	bottom: 0;
	width: 100%;
	border-top: 1px solid #e9e9e9;
	padding: 10px 16px;
	background: #fff;
	text-align: center;
	z-index: 1;
}
.product-title {
	.title-info {
		.ant-tag {
			margin-left: 12px;
		}
	}
	.title-opration {
		margin-right: 30px;
		float: right;
		cursor: pointer;
	}
}
.add-user {
	float: left;
	margin-right: 8px;
	height: 100%;
	cursor: pointer;
}
</style>