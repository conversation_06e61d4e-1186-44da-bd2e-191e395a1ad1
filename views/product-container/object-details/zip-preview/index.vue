<template>
  <div class="filepreview">
    <!-- 工程文件 -->
    <div class="projectfile" :style="{ width: showtwotable ? '50%' : '100%' }">
      <jwTable :data-source.sync="proFile" :columns="columns" :loading="loading" :showPage="false">
        <template #header>
          <div class="preview-title">{{ $t('txt_project_file') }}<span v-if="proZip.length">（</span>
            <span v-for="(zip, index) in proZip" :key="'pro' + index"><a :href="zip.url" :download="zip.name">{{ zip.name
            }}</a><span v-if="index !== proZip.length - 1">,</span></span>
            <span v-if="proZip.length">）</span>
          </div>
        </template>
        <template #download="{ row }">
          <a :href="row.url" :download="row.name">下载</a>
        </template>
      </jwTable>
    </div>
    <!-- 生产文件 -->
    <div class="prodfile" v-if="showtwotable">
      <jwTable :showPage="false" :data-source="creFile" :loading="loading" :columns="columns">
        <template #header>
          <div class="preview-title"> <span v-if="creZip.length">（</span>
            <span v-for="(zip, index) in creZip" :key="'cre' + index"><a :href="zip.url" :download="zip.name">{{ zip.name
            }}</a><span v-if="index !== creZip.length - 1">,</span></span>
            <span v-if="creZip.length">）</span>
          </div>
        </template>
        <template #download="{ row }">
          <a :href="row.url" :download="row.name">{{ $t('btn_download') }}</a>
        </template>
      </jwTable>
    </div>
  </div>
</template>

<script>
import JSZip from 'jszip'
import { downloadFile } from "/utils/util";
import { jwTable } from "jw_frame";
import { findConf } from "/apis/config/index";
export default {
  components: {
    jwTable
  },
  props: {
    objectDetailsData: {
      type: Object,
      default: () => { }
    }
  },
  mounted() {
    const { primaryFile, secondaryFile } = this.objectDetailsData
    this.primaryFile = primaryFile
    this.secondaryFile = secondaryFile

    this.loadConfigOn()
  },
  data() {
    return {
      showtwotable: false,
      loading: false,
      primaryFile: [],
      secondaryFile: [],
      proZip: [],
      //项目文件
      proFile: [],
      //生产文件
      creZip: [],
      creFile: [],
      columns: [
        {
          field: 'name',
          title: this.$t('txt_file_name'),
          sortable: true,
          formatter: ({ row }) => decodeURIComponent(row.name),
        },
        {
          field: 'path',
          title: this.$t('txt_file_path'),
          sortable: true
        },
        {
          field: 'size',
          title: this.$t('txt_file_size'),
          sortable: true
        },
        {
          field: 'zipName',
          title: this.$t('txt_zip_file'),
          sortable: true
        },
        {
          field: "url",
          title: this.$t('txt_download_opt'),
          slots: {
            default: 'download'
          },
        }
      ]
    }
  },
  methods: {
    //加载配置
    loadConfigOn() {
      this.loading = true
      findConf('ad-zip-preview').then((resp) => {
        const { value } = resp[0]
        this.showtwotable = value !== 'on'
        this.loadFile()
      }).catch((e) => {
        this.loading = false
        console.error(e)
        this.$error(e.msg)
      })
    },
    loadFile() {
      if(!_.isEmpty(this.primaryFile)){
        this.primaryFile.forEach((row) => {
          this.previewFile(row, this.proFile, this.proZip)
        })
      }
      if(!_.isEmpty(this.secondaryFile)){
        this.secondaryFile.forEach((row) => {
          this.previewFile(row, this.showtwotable ? this.creFile : this.proFile, this.showtwotable ? this.creZip : this.proZip)
        })
      }
    },
    previewFile(row, arr, zipArr) {
        downloadFile(row.oid).then((resp) => {
          let zipName = decodeURIComponent(row.name)
          zipArr.push({
            name: zipName,
            url: URL.createObjectURL(resp)
          })
          const decoder = new TextDecoder("gbk");
          const zip = new JSZip()
          zip.loadAsync(resp,{
             decodeFileName: (bytes) => {
                return decoder.decode(bytes);
            }}).then(zipFile => {
            Object.keys(zipFile.files).forEach(fileName => {
              const newFile = zipFile.files[fileName]
              if (!fileName.endsWith("/")) {
                newFile.async('blob').then(content => {
                  const fileUrl = URL.createObjectURL(content)
                  let size = (newFile._data.uncompressedSize / (1024 * 1024)).toFixed(2)
                  let path = fileName.substring(0, fileName.lastIndexOf("/") + 1)
                  const downFile = {
                    url: fileUrl,
                    size: isNaN(size) ? 0 : size,
                    name: fileName.substring(fileName.lastIndexOf("/") + 1),
                    path: path.length ? path : "/",
                    zipName: zipName
                  }
                  arr.push(downFile)
                  this.loading = false
                })
              }
            })
            this.loading = false
          })
          
        }).catch(e => {
          console.error(e)
          this.loading = false
        })
      }
  }
}
</script>

<style lang="less" scoped>
.filepreview {
  height: 100%;
  display: flex;

  .projectfile {
    height: 100%;
  }

  .prodfile {
    height: 100%;
    width: 50%;
  }

  .preview-title {
    font-size: 18px;
  }
}
</style>