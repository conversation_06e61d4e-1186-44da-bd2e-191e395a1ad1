<template>
    <a-modal
        :visible="visible"
        :title="$t('txt_part_mobile')"
        :mask-closable="false"
        width="60%"
        :bodyStyle="{height:'500px'}"
        @cancel="onCancel"
    >
        <jw-table ref="refTable"
            v-if="visible"
            :toolbars="toolbars"
            :columns="columns"
            row-id="id"
            :tree-config="{rowField: 'id', expandRowKeys}"
            :data-source.sync="tableData"
            :fetch="fetchTable"
            :row-config="{isCurrent:true}"
            :showPage="false"
            @onToolInput="onToolInput"
            @current-change="onCurrentChange"
        >
            <template #name="{ row }">
                <jwIcon :type='row.modelIcon'></jwIcon>
                {{ row.name }}
            </template>
        </jw-table>
        <template slot="footer">
            <a-button type="primary" :loading="confirmLoading" @click="onOk">{{$t('btn_ok')}} </a-button>
            <a-button class="form-btn-cancel" @click="onCancel">{{$t('btn_cancel')}} </a-button>
        </template>
    </a-modal>
</template>

<script>
import { jwIcon } from 'jw_frame';
import { generateUUID } from '../apis';
import ModelFactory from 'jw_apis/model-factory';
import { getParent } from 'utils/util.js';

// 获取可移动的目标节点
const fetchAllNodes = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/bom/parentAble`,
    method: 'get',
});

// 移动节点
const moveNode = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/bom/move`,
    method: 'post',
});

export default {
    name: 'structurePartMoveModal',
    props: [
        'visible',
        'detailInfo',
    ],
    components: {
        jwIcon,
    },
    data() {
        return {
            searchKey: '',
            tableData: [],
            selectedRow: {},
            expandRowKeys: [],
            confirmLoading: false,
        };
    },
    computed: {
        toolbars() {
            return [
                {
                    name:this.$t('btn_search') ,
                    position: 'before',
                    display: 'input',
                    value: this.searchKey,
                    allowClear: true,
                    placeholder: this.$t('search_text'),
                    suffixIcon: 'jwi-iconsearch',
                    key: 'search',
                },
            ]
        },
        columns() {
            return [
                {
                    field: 'name',
                    title: this.$t('txt_name'),
                    treeNode: true,
                    minWidth: 400,
                    slots: {
                        default: 'name',
                    },
                },
                {
                    field: 'number',
                    title: this.$t('txt_number_of'),
                    minWidth: 170,
                },
                {
                    field: 'displayVersion',
                    title:this.$t('txt_version') ,
                    minWidth: 120,
                    cellRender: {
                        name: 'tag',
                    },
                },
            ];
        },
    },
    mounted() {

    },
    methods: {
        onSearch() {
            this.expandRowKeys = [];
            this.$refs.refTable.reFetchData();
        },
        fetchTable() {
            return fetchAllNodes.execute({
                rootOid: this.detailInfo.rootOid,
                partOid: this.detailInfo.oid,
                searchKey: this.searchKey,
            }).then((res = []) => {
                if (res[0] === null) {// 无数据的时候，后台返回的结果是 [null]
                    res = []
                }
                if (res.length > 0) {
                    generateUUID(res);
                    res.forEach(item => {
                        this.expandRowKeys.push(item.id);
                    })
                }
                return {
                    data: res,
                }
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        onToolInput({key}, value) {
            if (key ==='search') {
                this.searchKey = value;
                this.onSearch();
            }
        },
        onCurrentChange({newValue}) {
            this.selectedRow = newValue;
        },
        onOk() {
            if (!this.selectedRow.oid) {
                this.$warning(this.$t('txt_mobile_select'));
                return;
            }
            this.confirmLoading = true;
           
            // let treeData = this.$parent.$refs.refTable.getTableData().fullData;
            // let parent = getParent(treeData, this.detailInfo.id, 'id');

            let tableRef = this.$parent.$refs.tableTreeScroller || this.$parent
            let parent = tableRef.getParentRow(this.detailInfo.uuid)
            moveNode.execute({
                parentOid: parent.oid,
                partOid: this.detailInfo.oid,
                targetOid: this.selectedRow.oid,
            }).then((res) => {
                this.$success(this.$t('txt_mobile_success'));
                this.confirmLoading = false;
                this.$emit('getTableData', {
                    clickOid: this.selectedRow.oid,
                    checkParentId: parent.oid,
                    clickType: this.selectedRow.type,
                });
                this.onCancel();
            }).catch((err) => {
                this.confirmLoading = false;
                this.$error(err.msg);
            });
        },
        onCancel() {
            this.searchKey = '';
            this.selectedRow = {},
            this.expandRowKeys = [];
            // this.$refs.refTable.clearCurrentRow();
            this.$emit('close');
        },
    },
};
</script>

<style lang="less" scoped>

</style>
