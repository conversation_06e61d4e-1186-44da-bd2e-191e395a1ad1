<template>
  <div class="child-basic-info-wrap">
    <Info
      :objectDetailsData="detailInfo"
      :childObj="'childObj'"
      @findInUserView="$parent.getObjDetail"
    ></Info>
  </div>
</template>

<script>
import Info from '../info.vue'
export default {
  name: 'structureBasicInfo',
  props: ['detailInfo', 'isEdit'],
  components: {
    Info,
  },
  data() {
    return {
      instanceData: {},
    }
  },
  mounted() {},
  methods: {},
}
</script>

<style lang="less" scoped>
.child-basic-info-wrap {
}
</style>
