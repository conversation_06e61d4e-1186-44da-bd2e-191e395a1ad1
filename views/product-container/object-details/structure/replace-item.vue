<template>
    <div class="child-replace-item-wrap">
        <div class="table-wrap table-wrap-global">
            <div class="table-title"><div class="left-line"></div>{{$t('txt_all_substitution')}} </div>
            <div class="table-con">
                <jw-table ref="refTableG"
                    :panel="false"
                    :toolbars="toolbarsG"
                    :tooltip-config="{}"
                    :columns="columnsG"
                    :showPage="false"
                    :data-source.sync="tableDataG"
                    :selectedRows.sync="selectedRowsG"
                    @onToolInput="onToolInput"
                    @onToolClick="onToolClick"
                    @onOperateClick="onOperateClick"
                >
                    <template slot="tool-after-start">
                        <a-button type="link" v-show="selectedRowsG.length > 0"
                            @click="onBatchRemoveG">{{$t('btn_batch_delete')}}</a-button>
                    </template>
                    <template #number="{ row }">
                        <div class="name-wrap">
                            <div class="name-con" @click="onOpenDetailPage(row)">
                                <jwIcon :type='row.modelIcon'></jwIcon>
                                <span class="name-item">{{ row.number }}</span>
                            </div>
                        </div>
                    </template>
                    <template #mutual="{ row }">
                        <a-switch v-model.trim="row.relationship.mutual"
                            @click="onChangeMutual(row)">
                            <a-icon slot="checkedChildren" type="check" />
                            <a-icon slot="unCheckedChildren" type="close" />
                        </a-switch>
                    </template>
                </jw-table>
            </div>
        </div>
        <div class="table-wrap">
            <div class="table-title"><div class="left-line"></div>{{$t('txt_local_substitute')}}</div>
            <div class="table-con">
                <jw-table ref="refTableL"
                    :panel="false"
                    :toolbars="toolbarsL"
                    :tooltip-config="{}"
                    :columns="columnsL"
                    :showPage="false"
                    :data-source.sync="tableDataL"
                    :selectedRows.sync="selectedRowsL"
                    @onToolInput="onToolInput"
                    @onToolClick="onToolClick"
                    @onOperateClick="onOperateClick"
                >
                    <template slot="tool-after-start">
                        <a-button type="link" v-show="selectedRowsL.length > 0"
                            @click="onBatchRemoveL">{{$t('btn_batch_delete')}}</a-button>
                    </template>
                    <template #number="{ row }">
                        <div class="name-wrap">
                            <div class="name-con" @click="onOpenDetailPage(row)">
                                <jwIcon :type='row.modelIcon'></jwIcon>
                                <span class="name-item">{{ row.number }}</span>
                            </div>
                        </div>
                    </template>
                    <template #mutual="{ row }">
                        <a-switch v-model.trim="row.relationship.mutual" disabled>
                            <a-icon slot="checkedChildren" type="check" />
                            <a-icon slot="unCheckedChildren" type="close" />
                        </a-switch>
                    </template>
                </jw-table>
            </div>
        </div>
        <jw-search-engine-modal
            :title="$t('txt_add_replace_parts')"
            :onlySearchObject="true"
            :visible.sync="modalVisible"
            :model-list='[
                {
                    name: $t("txt_part"),
                    code: "PartIteration"
                }
            ]'
            @ok='onAddOk'
		/>
    </div>
</template>

<script>
import { jwSearchEngineModal } from 'jw_frame';
import ModelFactory from 'jw_apis/model-factory';

// 获取全局替代件
const fetchGlobalSubstitute = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/fuzzyGlobalSubstitute`,
    method: 'post',
});

// 获取局部替代件
const fetchLocalSubstitute = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/fuzzyLocalSubstitute`,
    method: 'post',
});

// 获取所有带版本对象
const fetchAllPart = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/fuzzyPage`,
    method: 'post',
});

// 批量添加全局替代件
const addGlobalSubstitutes = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/addGlobalSubstitutes`,
    method: 'post',
});

// 批量添加局部替代件
const addLocalSubstitutes = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/addLocalSubstitutes`,
    method: 'post',
});

// 移除局部替代件
const deleteLocalSubstitute = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/deleteLocalSubstitute`,
    method: 'post',
});

// 移除全局替代件
const deleteGlobalSubstitute = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/deleteGlobalSubstitute`,
    method: 'post',
});

// 批量移除局部替代件
const deleteLocalBatch = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/deleteLocalSubstitutes`,
    method: 'post',
});

// 批量移除全局替代件
const deleteGlobalBatch = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/deleteGlobalSubstitutes`,
    method: 'post',
});

// 更新全局替代件
const updateGlobalSubstitute = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/updateGlobalSubstitute`,
    method: 'post',
});

export default {
    name: 'structureReplaceItem',
    props: [
        'detailInfo',
        'parentData',
    ],
    components: {
        jwSearchEngineModal,
    },
    data() {
        return {
            searchKeyG: '',
            searchKeyL: '',
            tableDataG: [],
            tableDataL: [],
            selectedRowsG: [],
            selectedRowsL: [],
            munuKey: 'global',
            modalVisible: false,
        }
    },
    computed: {
        toolbarsG() {
            return [
                {
                    name: this.$t('txt_add_c'),
                    key: 'createG',
                    type: 'primary',
                },
                {
                    name: this.$t('btn_search'),
                    position: 'before',
                    display: 'input',
                    value: this.searchKeyG,
                    allowClear: true,
                    placeholder: this.$t('search_text'),
                    suffixIcon: 'jwi-iconsearch',
                    key: 'searchG',
                },
            ]
        },
        toolbarsL() {
            return [
                {
                    name: this.$t('txt_add_c'),
                    key: 'createL',
                    type: 'primary',
                },
                {
                    name: this.$t('btn_search'),
                    position: 'before',
                    display: 'input',
                    value: this.searchKeyL,
                    allowClear: true,
                    placeholder: this.$t('search_text'),
                    suffixIcon: 'jwi-iconsearch',
                    key: 'searchL',
                },
            ]
        },
        columnsG() {
            return [
                {
                    field: 'number',
                    title: this.$t('txt_number_of'),
                    minWidth: 170,
                    slots: {
                        default: 'number',
                    },
                },
                {
                    field: 'name',
                    title: this.$t('txt_name'),
                    minWidth: 150,
                },
                {
                    field: 'modelDefinition',
                    title: this.$t('txt_substitution_type'),
                    minWidth: 120,
                },
                {
                    field: 'relationship.type',
                    title: this.$t('txt_type'),
                    minWidth: 170,
                    cellRender: {
                        name: 'tag',
                    },
                },
                {
                    field: 'relationship.mutual',
                    title: this.$t('txt_to_way'),
                    minWidth: 110,
                    slots: {
                        default: 'mutual',
                    },
                },
                {
                    field: 'operation',
                    title: this.$t('txt_operation'),
                    btns: [
                        {
                            icon: 'jwi-icondelete',
                            title: this.$t('txt_delete'),
                            key: 'delete',
                        },
                    ],
                }
            ];
        },
        columnsL() {
            return [
                {
                    field: 'number',
                    title: this.$t('txt_number_of'),
                    minWidth: 170,
                    slots: {
                        default: 'number',
                    },
                },
                {
                    field: 'name',
                    title: this.$t('txt_name'),
                    minWidth: 150,
                },
                {
                    field: 'modelDefinition',
                    title: this.$t('txt_substitution_type'),
                    minWidth: 120,
                },
                {
                    field: 'relationship.type',
                    title: this.$t('txt_type'),
                    minWidth: 170,
                    cellRender: {
                        name: 'tag',
                    },
                },
                {
                    field: 'operation',
                    title: this.$t('txt_operation'),
                    btns: [
                        {
                            icon: 'jwi-icondelete',
                            title: this.$t('txt_delete'),
                            key: 'delete',
                        },
                    ],
                }
            ];
        },
    },
    mounted() {

    },
    methods: {
        onSearch() {
            this.fetchTableG();
            this.fetchTableL();
        },
        fetchTableG() {
            fetchGlobalSubstitute.execute({
                searchKey: this.searchKeyG,
                fromOid: this.detailInfo.masterOid,
                fromType: this.detailInfo.masterType,
            }).then((res) => {
                this.tableDataG = res;
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        fetchTableL() {
            fetchLocalSubstitute.execute({
                oid: this.detailInfo.oid,
                parentOid: this.parentData ? this.parentData.oid : '',
                searchKey: this.searchKeyL,
            }).then((res) => {
                this.tableDataL = res;
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        onToolInput({key}, value) {
            if (key ==='searchG') {
                this.searchKeyG = value;
                this.fetchTableG();
            } else if (key === 'searchL') {
                this.searchKeyL = value;
                this.fetchTableL();
            }
        },
        onToolClick({key}) {
            if (key==='createG') {
                this.munuKey = 'global';
            } else if (key==='createL') {
                this.munuKey = 'local';
            }
            this.modalVisible = true;
        },
        onOperateClick(key, row) {
            if (key === 'delete') {
                let api = deleteGlobalSubstitute;
                let params = {};
                if (row.relationship.type === 'GLOBAL_SUBSTITUTE') {
                    api = deleteGlobalSubstitute;
                    params = {
                        oid: row.oid,
                        fromOid: this.detailInfo.masterOid,
                        fromType: this.detailInfo.masterType,
                        toOid: row.masterOid,
                        toType: row.masterType,
                        mutual: row.relationship.mutual,
                        type: row.relationship.type,
                    }
                } else if (row.relationship.type === 'LOCAL_SUBSTITUTE') {
                    api = deleteLocalSubstitute;
                    params = {
                        oid: row.oid,
                        fromOid: this.detailInfo.oid,
                        fromType: this.detailInfo.type,
                        toOid: row.oid,
                        toType: row.type,
                        parentPartOid: this.parentData ? this.parentData.oid : '',
                        type: row.relationship.type,
                    }
                }
                this.$confirm({
                    title: this.$t('txt_remove_substitution'),
                    okText:this.$t('btn_ok'),
                    cancelText:this.$t('btn_cancel'),
                    onOk: () => {
                        this.onRemoveFn(params, api, row.relationship.type);
                    },
                });
            }
        },
        onRemoveFn(params, api, key) {
            return api.execute(
                params
            ).then((res) => {
                this.$success(this.$t('txt_remove_success'));
                if (key === 'GLOBAL_SUBSTITUTE') {
                    this.fetchTableG();
                    this.selectedRowsG = [];
                } else if (key === 'LOCAL_SUBSTITUTE') {
                    this.fetchTableL();
                    this.selectedRowsL = [];
                }
                this.$emit('getTableData');
            }).catch((err) => {
                if (err.msg) {
                    this.$error(err.msg);
                }
            });
        },
        onBatchRemoveG() {
            this.$confirm({
                title: this.$t('txt_remove_substitution'),
                okText:this.$t('btn_ok'),
                cancelText:this.$t('btn_cancel'),
                onOk: () => {
                    let globalParams = [];
                    this.selectedRowsG.forEach(item => {
                        globalParams.push({
                            oid: item.oid,
                            fromOid: this.detailInfo.masterOid,
                            fromType: this.detailInfo.masterType,
                            toOid: item.masterOid,
                            toType: item.masterType,
                            mutual: item.relationship.mutual,
                            type: item.relationship.type,
                        })
                    })
                    this.onRemoveFn(globalParams, deleteGlobalBatch, 'GLOBAL_SUBSTITUTE');
                },
            });
        },
        onBatchRemoveL() {
            this.$confirm({
                title: this.$t('txt_remove_substitution'),
                okText:this.$t('btn_ok'),
                cancelText:this.$t('btn_cancel'),
                onOk: () => {
                    let localParams = [];
                    this.selectedRowsL.forEach(item => {
                        localParams.push({
                            oid: item.oid,
                            fromOid: this.detailInfo.oid,
                            fromType: this.detailInfo.type,
                            toOid: item.oid,
                            toType: item.type,
                            parentPartOid: this.parentData ? this.parentData.oid : '',
                            type: item.relationship.type,
                        })
                    })
                    this.onRemoveFn(localParams, deleteLocalBatch, 'LOCAL_SUBSTITUTE');
                },
            });
        },
        onChangeMutual(row) {
            updateGlobalSubstitute.execute({
                oid: row.oid,
                fromOid: this.detailInfo.masterOid,
                fromType: this.detailInfo.masterType,
                toOid: row.masterOid,
                toType: row.masterType,
                mutual: row.relationship.mutual,
                type: row.relationship.type,
            }).then((res) => {
                this.$success(this.$t('msg_save_success'));
                this.fetchTableG();
                this.$emit('getTableData');
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        searchSubstitute(params) {
            return fetchAllPart.execute(params);
        },
        onOpenDetailPage(row) {
            Jw.jumpToDetail(row,{blank:true});
        },
        onAddOk(selectedRows) {
            if (selectedRows.length === 0) {
                this.$warning(this.$t('txt_please_seleted_data'));
                return;
            }
            let selected = [];
            let api = addGlobalSubstitutes;
            if (this.munuKey === 'global') {
                selectedRows.forEach(item => {
                    let temp = {
                        fromOid: this.detailInfo.masterOid,
                        fromType: this.detailInfo.masterType,
                        toOid: item.masterOid,
                        toType: item.masterType,
                        mutual: true,
                        type: 'GLOBAL_SUBSTITUTE',
                    }
                    selected.push(temp);
                })
                api = addGlobalSubstitutes;
            } else if (this.munuKey === 'local') {
                selectedRows.forEach(item => {
                    let temp = {
                        fromOid: this.detailInfo.oid,
                        fromType: this.detailInfo.type,
                        toOid: item.oid,
                        toType: item.type,
                        parentPartOid: this.parentData ? this.parentData.oid : '',
                        type: 'LOCAL_SUBSTITUTE',
                    }
                    selected.push(temp);
                })
                api = addLocalSubstitutes;
            }
            api.execute(
                selected
            ).then((res) => {
                this.$success(this.$t('txt_add_success'));
                if (this.munuKey === 'global') {
                    this.fetchTableG();
                } else if (this.munuKey === 'local') {
                    this.fetchTableL();
                }
                this.modalVisible = false;
                this.$emit('getTableData');
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
    },
}
</script>

<style lang="less" scoped>
.child-replace-item-wrap {
    display: flex;
    flex-direction: column;
    .ant-btn-link {
        color: #f6445a;
    }
    .table-wrap {
        display: flex;
        flex-direction: column;
        &.table-wrap-global {
            margin-bottom: 16px;
        }
        /deep/.jw-table.has-tools .vxe-grid--toolbar-wrapper {
            padding-bottom: 10px;
        }
        .table-title {
            display: flex;
            height: 36px;
            line-height: 36px;
            margin-bottom: 10px;
            color: rgba(30, 32, 42, 0.85);
            background: #f3f1f1;
            .left-line {
                width: 3px;
                height: 100%;
                margin-right: 6px;
                background: #1890ff;
            }
        }
        .table-con {
            height: calc(50vh - 180px)
        }
        .name-wrap {
            display: flex;
            justify-content: space-between;
            .name-con {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                .name-item {
                    color: #255ed7;
                    cursor: pointer;
                }
            }
        }
    }
}
</style>
