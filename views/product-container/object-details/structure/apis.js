import ModelFactory from 'jw_apis/model-factory';

// 按需获取层级结构树
const fetchUseTree = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/useTree/find`,
    method: 'get',
});

// 模糊查询结构树
const fetchUseTreeFuzzy = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/useTree/fuzzy`,
    method: 'get',
});

// 获取use显示列
 const fetchUseCols = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/model/edgeDef/detail`,
    method: 'get',
});

// 获取该条数据的关系操作
 const getRelationList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
    method: "post",
});

// 删除use关系
 const deleteUse = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/deleteUse`,
    method: 'post',
});

// 获取单位列表
 const fetchUnitList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.sysconfigServer}/units/search`,
    method: 'get',
});

// 获取所有带版本对象
 const fetchAllPart = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/fuzzyPage`,
    method: 'post',
});

// 批量添加子对象
 const batchAddUse = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/batchAddUse`,
    method: 'post',
});

// 添加至
 const addTo = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/bom/addTo`,
    method: 'post',
});

// 替换
 const replaceObj = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/bom/replace`,
    method: 'post',
});

// 批量更新use关系
 const batchUpdateUse = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/batchUpdateUse`,
    method: 'post',
});

const updateUse = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/updateUse`,
    method: 'post',
});

// 获取最新版本Part信息
 const findLatestInfo = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/findLatest`,
    method: 'get',
});

// 检出
 const partCheckout = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/checkOut`,
    method: 'post',
});

//下载bomexcel
const downBomExcel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/downLoadExcel`,
    method: 'get',
    responseType: 'blob'
})

export {
    fetchUseTree,
    fetchUseTreeFuzzy,
    fetchUseCols,
    getRelationList,
    deleteUse,
    fetchUnitList,
    fetchAllPart,
    batchAddUse,
    addTo,
    replaceObj,
    batchUpdateUse,
    updateUse,
    findLatestInfo,
    partCheckout,
    downBomExcel
}