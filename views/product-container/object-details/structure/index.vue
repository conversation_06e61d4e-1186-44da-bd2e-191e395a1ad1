<template>
  <div class="content-wrap">
    <div class="header-btn-group">
      <template v-for="item in btnList">
        <a-button class="tool-btn" :key="item.code" :title="item.title" :disabled="disableList[item.code]"
                  @click="onRelationOpe(item.code)">
          <i :class="item.icon"></i>
        </a-button>
        <div class="btn-divider" :key="item.title" v-show="item.code === 'replace' ||
          item.code === 'undoCheckOut' ||
          item.code === 'remove' ||
          item.code === 'export'
          "></div>
      </template>
      <a-input-search :placeholder="$t('search_text')" style="width: 200px" @search="onSearch" v-model.trim="searchKey" />

      <div class="right-toolbar">
        <div>
          <a-radio-group v-if="isShowLevel" v-model.trim="level" :disabled="radioDisabled" @change="onChangeLevel">
            <a-radio-button key="All" :value="null">All</a-radio-button>
            <a-radio-button v-for="item in 4" :key="item" :value="item">{{
                item
              }}</a-radio-button>
          </a-radio-group>
        </div>
      </div>
    </div>
    <div class="structure-wrap" id="structure-wrap">
      <div class="left-area">
        <jwTableTreeScroller ref="tableTreeScroller" :expandLevel="level" :columns="columns" :loading="loading"
                             :menu-config="menuConfig" @menu-click="onContextMenuClick" @current-change="({ row }) => onOpenChildDrawer(row)"
                             :height="tableHeight" :lineHeight="40">
          <template #thumbnailOid="{ row }">
            <cad-thumbnail v-if="row.thumbnailOid" :currentRow="row" :miniStyle="{ height: '22px' }"></cad-thumbnail>
          </template>
          <template #identity="{ row }">
            <div class="table-number-name-wrap">
              <div class="name-con">
                <a-tooltip>
                  <template slot="title">{{ row.lockOwnerAccount }}</template>
                  <jw-icon v-if="row.lockOwnerOid" :type="row.lockOwnerOid === userOid
                      ? '#jwi-beiwojianchu'
                      : '#jwi-bierenjianchu'
                    " />
                </a-tooltip>
                <jwIcon :type="row.modelIcon"></jwIcon>
                <span class="name-item">{{ row.identity }}</span>
                <a-tooltip :title="$t('txt_view_replacement_parts')">
                  <span @click="opensubstitued(row)">
                    <jw-icon v-if="row.substitutedFlag" type="#jwi-tidaijian" />
                  </span>
                </a-tooltip>
              </div>
              <div v-if="$refs['childDetail'].activeTab === 'visualization'" class="view-btn">
                <a-tooltip :title="$t('txt_hidden')" v-if="row.show3d">
                  <span @click.stop="toggle3dx(row)">
                    <jw-icon type="jwi-invisible"></jw-icon>
                  </span>
                </a-tooltip>
                <a-tooltip :title="$t('txt_hidden')" v-else>
                  <span @click.stop="toggle3dx(row)">
                    <jw-icon type="jwi-visible"></jw-icon>
                  </span>
                </a-tooltip>
              </div>
            </div>
          </template>
          <template #modelDefinition="{ row }">
            {{ $t(row.modelDefinition) }}
          </template>
          <template #lifecycleStatus="{ row }">
            {{ $t(row.lifecycleStatus) }}
          </template>
        </jwTableTreeScroller>
      </div>
      <div class="bomresize" title="收缩侧边栏"></div>
      <operation-dropdown v-show="false" ref="dropdown" :current-record="currentRow" :codeFlag="'relation'"
                          @renderData="renderData" @onRelationOpe="onRelationOpe" @visibleRelationList="visibleRelationList">
        <template #relationList>
          <a-menu-divider v-if="relationList.length > 0" />
          <a-menu-item v-for="item in relationList" :key="item.code" :disabled="item.status === 'disable'"
                       @click="onRelationOpe(item.code, currentRow)">
            <span :class="item.icon" style="margin-right: 8px"></span>{{ $t(item.internationalizationKey) }}
          </a-menu-item>
        </template>
      </operation-dropdown>
      <div class="right-area">
        <child-detail ref="childDetail" :detailInfo="detailInfo" :rootTreeoid="rootTreeoid" @getTableData="onSearch"></child-detail>
      </div>

      <part-move-modal :visible="moveVisible" :detailInfo="detailInfo" @getTableData="onRefresh"
                       @close="onCloseMoveModal"></part-move-modal>
      <jw-search-engine-modal :title="$t('txt_adding_business_object')" :onlySearchObject="true"
                              :visible.sync="objVisible" :pageCode="pageCode" :model-list="[
          {
            name: $t('txt_part'),
            code: 'PartIteration',
          },
        ]" @ok="onAddOk" />
      <create-drawer ref="cerateDrawer" :objectDetailsData="objectDetailsData" @fetchTable="onRefresh"></create-drawer>
      <import-modal :visible="visibleExport" :currentRow="tableData" :currentTree="objectDetailsData"
                    @close="visibleExport = false" @renderData="renderData"></import-modal>
    </div>
  </div>
</template>

<script>
import { jwIcon, jwSearchEngineModal, jwTableTreeScroller } from 'jw_frame'
import operationDropdown from 'components/operation-dropdown'
import partMoveModal from './part-move-modal'
import instanceObjectModal from './instance-object-modal'
import childDetail from './child-detail'
import createDrawer from '../../../product-content/content-manage/create-drawer'
import { getCookie } from 'jw_utils/cookie'
import modelStore from 'jw_stores/common'
import importModal from './import-modal.vue'
import cadThumbnail from 'components/cad-thumbnail.vue'
import {
  fetchUseTree,
  fetchUseTreeFuzzy,
  getRelationList,
  deleteUse,
  fetchAllPart,
  batchAddUse,
  addTo,
  replaceObj,
  findLatestInfo,
  partCheckout,
  downBomExcel,
} from './apis'

export default {
  name: 'structurePage',
  props: ['objectDetailsData'],
  components: {
    jwIcon,
    operationDropdown,
    partMoveModal,
    instanceObjectModal,
    jwSearchEngineModal,
    childDetail,
    createDrawer,
    importModal,
    cadThumbnail,
    jwTableTreeScroller,
  },
  data() {
    return {
      //根节点id
      rootTreeoid: '',
      // 导入导出
      visibleExport: false,
      userOid: Jw.getUser().oid,
      searchKey: '',
      loading: false,
      radioDisabled: false,
      tableData: [],
      level: 2,
      isShowLevel: true,
      relationList: [],
      moveVisible: false,
      objVisible: false,
      childVisible: false,
      detailInfo: {},
      currentRow: {},
      tableHeight: 500,
      btnList: [
        {
          title: this.$t('txt_add_part'),
          icon: 'jwi-iconsubItem-add',
          code: 'createPart',
        },
        {
          title: this.$t('txt_add_existing_part'),
          icon: 'jwi-iconSelectobject',
          code: 'createHasPart',
        },
        {
          title: this.$t('txt_replacement'),
          icon: 'jwi-iconreplace',
          code: 'replace',
        },
        {
          title: this.$t('txt_check_in'),
          icon: 'jwi-iconcheck-in',
          code: 'checkin',
        },
        {
          title: this.$t('txt_check_out'),
          icon: 'jwi-iconcheck-out',
          code: 'checkout',
        },
        {
          title: this.$t('txt_undo_out'),
          icon: 'jwi-iconcheck-out-revoke',
          code: 'undoCheckOut',
        },
        {
          title: this.$t('txt_mobile'),
          icon: 'jwi-iconmove',
          code: 'partMove',
        },
        {
          title: this.$t('txt_add_to_bom'),
          icon: 'jwi-iconCopy-add',
          code: 'copyMove',
        },
        {
          title: this.$t('txt_remove'),
          icon: 'jwi-icondelete',
          code: 'remove',
        },
        {
          title: this.$t('btn_import'),
          icon: 'jwi-iconImport',
          code: 'import',
        },
        {
          title: this.$t('btn_export'),
          icon: 'jwi-iconexport',
          code: 'export',
        },
      ],
      disableList: {
        createPart: true,
        createHasPart: true,
        replace: true,
        checkin: true,
        checkout: true,
        undoCheckOut: true,
        partMove: true,
        copyMove: true,
        moveDown: true,
        moveUp: true,
        remove: true,
        import: false,
        export: false,
      },
      pageCode: '',
      menuConfig: {
        body: {
          options: [
            [
              {
                code: 'partDetails',
                name: this.$t('txt_detail'),
                disabled: true,
                visible: true,
              },
              {
                code: 'checkout',
                name: this.$t('txt_check_out'),
                disabled: true,
                visible: true,
              },
              {
                code: 'checkin',
                name: this.$t('txt_check_in'),
                disabled: true,
                visible: true,
              },
              {
                code: 'undoCheckOut',
                name: this.$t('txt_undo_out'),
                disabled: true,
                visible: true,
              },
              {
                code: 'partEdit',
                name: this.$t('btn_edit'),
                disabled: true,
                visible: true,
              },
              {
                code: 'createPart',
                name: this.$t('txt_add_part'),
                disabled: true,
                visible: true,
              },
              {
                code: 'createHasPart',
                name: this.$t('txt_add_existing_part'),
                disabled: true,
                visible: true,
              },
            ],
            [
              {
                code: 'remove',
                name: this.$t('txt_remove'),
                disabled: true,
                visible: true,
              },
              {
                code: 'replace',
                name: this.$t('txt_replacement_bom'),
                disabled: true,
                visible: true,
              },
              {
                code: 'copyMove',
                name: this.$t('txt_add_to_bom'),
                disabled: true,
                visible: true,
              },
              {
                code: 'partMove',
                name: this.$t('txt_mobile'),
                disabled: true,
                visible: true,
              },
            ],
          ],
        },
        trigger: 'cell',
        className: 'cell-menu-wrap-list',
      },
      isCheckBoxOpe: false,
      columns: [
        {
          field: 'thumbnailOid',
          title: '',
        },
        {
          field: "identity",
          title: this.$t("标识"),
          minWidth: 443,
          treeNode: true,
        },
        // {
        //   field: 'number',
        //   title: this.$t('txt_number'),
        //   treeNode: true,
        //   minWidth: 250,
        // },
        // {
        //   field: 'name',
        //   title: this.$t('txt_name'),
        //   minWidth: 179,
        // },
        // {
        //   field: 'displayVersion',
        //   title: this.$t('txt_version'),
        //   minWidth: 90,
        // },
        /*{
          field: 'modelDefinition',
          title: this.$t('txt_type'),
          minWidth: 120,
        },*/


        {
          field: "use.quantity",
          title: this.$t("数量"),
          width: 50,
        },
        {
          field: "use.extensionContent.position",
          title: this.$t("位号"),
          width: 50,
        },
        {
          field: "use.unit",
          title: this.$t("单位"),
          width: 50,
        },

        {
          field: "use.extensionContent.lineNumber",
          title: this.$t("行号"),
          width: 50,
        },
        {
          field: 'lifecycleStatus',
          title: this.$t('txt_plan_lifecycle'),
          minWidth: 90,
        },
        {
          field: 'owner',
          title: this.$t('txt_owner'),
          minWidth: 120,
        },
        {
          field: 'description',
          title: this.$t('txt_description'),
          minWidth: 120,
        },
      ],
    }
  },
  mounted() {
    this.dragControllerDiv()
  },
  methods: {
    //拖拽调宽度
    dragControllerDiv() {
      var resize = document.getElementsByClassName('bomresize');
      var left = document.getElementsByClassName('left-area');
      var mid = document.getElementsByClassName('right-area');
      var box = document.getElementsByClassName('structure-wrap');
      for (let i = 0; i < resize.length; i++) {
        // 鼠标按下事件
        resize[i].onmousedown = function (e) {
          //颜色改变提醒
          resize[i].style.background = '';
          var startX = e.clientX;
          resize[i].left = resize[i].offsetLeft;
          // 鼠标拖动事件
          document.onmousemove = function (e) {
            var endX = e.clientX;
            var moveLen = resize[i].left + (endX - startX); // （endx-startx）=移动的距离。resize[i].left+移动的距离=左边区域最后的宽度
            var maxT = box[i].clientWidth - resize[i].offsetWidth; // 容器宽度 - 左边区域的宽度 = 右边区域的宽度

            if (moveLen < 32) moveLen = 32; // 左边区域的最小宽度为32px
            if (moveLen > maxT - 150) moveLen = maxT - 150; //右边区域最小宽度为150px

            resize[i].style.left = moveLen; // 设置左侧区域的宽度

            for (let j = 0; j < left.length; j++) {
              left[j].style.width = moveLen + 'px';
              mid[j].style.width = (box[i].clientWidth - moveLen - 10) + 'px';
            }
          };
          // 鼠标松开事件
          document.onmouseup = function (evt) {
            //颜色恢复
            resize[i].style.background = '';
            document.onmousemove = null;
            document.onmouseup = null;
            resize[i].releaseCapture && resize[i].releaseCapture(); //当你不在需要继续获得鼠标消息就要应该调用ReleaseCapture()释放掉
          };
          resize[i].setCapture && resize[i].setCapture(); //该函数在属于当前线程的指定窗口里设置鼠标捕获
          return false;
        };
      }
    },
    init() {
      this.onSearch()
      this.resetDisableList()
    },
    onSearch() {
      this.getTableData()
    },
    getTableData() {
      this.tableHeight = document.querySelector('#structure-wrap')?.clientHeight - 30 || 500
      this.loading = true
      let api = this.searchKey ? fetchUseTreeFuzzy : fetchUseTree
      let oldreluuid = this.detailInfo.reluuid
      api
          .execute({
            searchKey: this.searchKey,
            rootOid: this.objectDetailsData.oid,
            maxLevel: null,
          })
          .then((res) => {
            try {
              // 确保res是数组
              const data = Array.isArray(res) ? res : [];

              // 处理数据，添加或更新identity
              const processData = (nodes) => {
                return nodes.map(node => {
                  // 创建新对象，避免直接修改原对象
                  const processedNode = { ...node };

                  // 确保所有可能作为表单输入的字段都是基本类型
                  processedNode.identity = `${node.number || ''}, ${node.name || ''}, ${node.displayVersion || ''}`;

                  // 处理子节点
                  if (node.children && Array.isArray(node.children)) {
                    processedNode.children = processData(node.children);
                  }

                  return processedNode;
                });
              };

              const processedData = processData(data);

              if (processedData.length > 0) {
                this.rootTreeoid = processedData[0].oid
                this.isShowLevel = true
              } else {
                this.rootTreeoid = null
                this.isShowLevel = false
              }

              if (this.$refs?.tableTreeScroller) {
                this.$refs.tableTreeScroller.initTableData(processedData)
              }

              this.loading = false

              if (processedData.length > 0) {
                this.$nextTick(() => {
                  try {
                    const detailInfo = this.$refs?.tableTreeScroller?.nowlist?.find(
                        (item) => item.reluuid === oldreluuid
                    )

                    let operationCode = modelStore.get('operationCode')
                    if (operationCode) {
                      // 确保传递的是处理过的数据
                      const createModalData = detailInfo || { ...this.objectDetailsData };
                      if (createModalData.use) {
                        createModalData.use = String(createModalData.use.quantity || '');
                      }
                      this.onOpenCreateModal(createModalData)
                    }

                    // 确保传递的是处理过的数据
                    const drawerData = detailInfo || processedData[0];
                    this.onOpenChildDrawer(drawerData)

                    this.tableHeight = document.querySelector('#structure-wrap')?.clientHeight || 500

                    if (this.$refs?.tableTreeScroller) {
                      this.$refs.tableTreeScroller.setCurrentRow(drawerData)
                    }
                  } catch (innerError) {
                    console.error('Error in nextTick callback:', innerError)
                  }
                })
              }
            } catch (error) {
              console.error('Error processing table data:', error)
              this.loading = false
            }
          })
          .catch((err) => {
            this.loading = false
            this.$error(err.msg || '获取数据失败')
          })
    },
    visibleRelationList(visible, row) {
      //替代件不能任何操作
      if (visible) {
        let parent = this.$refs?.tableTreeScroller?.getParentRow(row.uuid)
        getRelationList
            .execute({
              viewCode: 'PARTBOMRELATEION',
              objectOid: row.oid,
              parentOid: parent ? parent.oid : row.oid,
            })
            .then((res) => {
              this.relationList = res
              const temp = [row.operationList, res]
              this.menuConfig.body.options.forEach((arr, index) => {
                arr.forEach((val) => {
                  const item = temp[index].find((t) => t.code == val.code)
                  if (item) {
                    val.prefixIcon = item.icon
                    val.disabled = item.status !== 'enable'
                    val.visible = true
                    if (this.isCheckBoxOpe) {
                      this.disableList[item.code] =
                          item.status === 'enable' ? false : true
                    }
                  } else {
                    // 万一删除某个操作的时候这里也要对应删除
                  }
                })
              })
            })
            .catch((err) => {
              this.relationList = []
              this.$error(err.msg)
            })
      } else {
        this.resetDisableList()
        //替代件右侧菜单只显示详情
        this.menuConfig.body.options.forEach((item, index) => {
          item.forEach((item2, index2) => {
            if (index === 0 && index2 === 0) {
              item2.disabled = false
            } else {
              item2.disabled = true
            }
          })
        })
      }
    },
    onCheckboxChange(row) {
      this.resetDisableList()
      this.currentRow = row
      this.isCheckBoxOpe = true
      this.$nextTick(() => {
        this.$refs.dropdown.dropdownVisibleChange(true)
      })
    },
    onChangeLevel(e) {
      this.level = e.target.value
    },
    onRelationOpe(code) {
      switch (code) {
        case 'partDetails':
          this.detailInfo = { ...this.currentRow }
          if (this.currentRow.oid === this.objectDetailsData.oid) {
            this.$emit('init', null, 'info')
          } else {
            this.onOpenDetailPage(this.currentRow)
          }
          break
        case 'partEdit':
          this.detailInfo = { ...this.currentRow }
          this.onCheckoutThenToDetail(this.currentRow)
          break
        case 'createPart':
          this.detailInfo = { ...this.currentRow }
          this.onOpenCreateModal(this.currentRow)
          modelStore.set('operationCode', code)
          break
        case 'createHasPart':
          this.pageCode = ''
          this.onOpenObjModal(this.currentRow)
          break
        case 'replace':
          this.pageCode = 'structurePage'
          this.currentRow.opeKey = code
          this.onOpenObjModal(this.currentRow)
          break
        case 'checkin':
          this.$refs.dropdown.rowOperation('checkin')
          break
        case 'checkout':
          this.$refs.dropdown.rowOperation('checkout')
          break
        case 'undoCheckOut':
          this.$refs.dropdown.rowOperation('undoCheckOut')
          break
        case 'partMove':
          this.currentRow.rootOid = this.objectDetailsData.oid
          this.onOpenMoveModal(this.currentRow)
          break
        case 'copyMove':
          this.pageCode = 'structurePage'
          this.currentRow.opeKey = code
          this.onOpenObjModal(this.currentRow)
          break
        case 'remove':
          this.onDeleteUse(this.currentRow)
          break
        case 'export':
          //导出所有
          this.openExportSelect(this.objectDetailsData.oid)
          break
        case 'import':
          this.onImport(this.currentRow)
          break
        default:
          break
      }
    },
    resetDisableList() {
      this.disableList = {
        createPart: true,
        createHasPart: true,
        replace: true,
        checkin: true,
        checkout: true,
        undoCheckOut: true,
        partMove: true,
        copyMove: true,
        moveDown: true,
        moveUp: true,
        remove: true,
        import: false,
        export: false,
      }
      this.currentRow = {}
      this.relationList = []
    },
    onCellMenuClick({ row, column }) {
      this.resetDisableList()
      this.currentRow = { ...row }
      this.isCheckBoxOpe = false
      this.$nextTick(() => {
        this.$refs.dropdown.dropdownVisibleChange(true)
      })
    },
    onContextMenuClick({ menu, row, column }) {
      this.onRelationOpe(menu.code)
    },
    renderData(data, row) {
      if (row.oid === this.objectDetailsData.oid) {
        this.$emit('init', data, 'structure')
      } else {
        this.getTableData()
      }
      this.resetDisableList()
    },
    onImport(currentRow) {
      this.visibleExport = true
    },
    onCloseDownModal() {
      this.visibleExport = false
    },
    //选择导出的内容
    openExportSelect(currentRow){
      let type = '2'
      this.$confirm({
        title: this.$t('excel_data_type'),
        confirmLoading: true,
        content:
            (<a-radio-group defaultValue='2' onChange={val => type = val.target.value}>
              <a-radio value="2" >
                { this.$t('part_view') }
              </a-radio>
              <a-radio value="0">
                Bom
              </a-radio>
              <a-radio value="1">
                { this.$t('txt_effectivity') }
              </a-radio>
            </a-radio-group>),
        onOk: () => {
          this.onExport(currentRow, type)
        },
      })
    },
    onExport(currentRow, type) {
      let { objectDetailsData } = this
      let topPartOid = this.$route.query.oid
      const accesstoken = getCookie('token')
      fetch(
          `${Jw.gateway}/${Jw.partBomMicroServer
          }/part-export/export/bom-data?topPartOid=${currentRow.oid || topPartOid
          }&type=${type}`,
          {
            method: 'get',
            headers: {
              'Content-Type': 'application/json;charset=utf8',
              appName: Jw.appName,
              accesstoken,
              tenantAlias: getCookie('tenantAlias'),
              tenantOid: getCookie('tenantOid'),
            },
          }
      )
          .then((response) => {
            return response.blob()
          })
          .then((res) => {
            let url = window.URL.createObjectURL(
                new Blob([res], {
                  type: 'application/vnd.ms-excel',
                })
            )
            let link = document.createElement('a')
            link.href = url
            link.setAttribute(
                'download',
                `${objectDetailsData.name},${objectDetailsData.number},${objectDetailsData.version}(${type == '2' ? this.$t('part_view') : type === '0' ? 'Bom' : type === '1' ? this.$t('txt_effectivity') : '未知'}).xlsx`
            )
            document.body.appendChild(link)
            link.click()
            this.$success(this.$t('txt_export_success'))
          })
          .catch((err) => {
            this.$error(err.msg)
          })
    },
    onDeleteUse(row) {
      this.$confirm({
        title: this.$t('txt_comfirm_remove'),
        okText: this.$t('btn_ok'),
        cancelText: this.$t('btn_cancel'),
        onOk: () => {
          return deleteUse
              .execute(row.use)
              .then((res) => {
                this.$success(this.$t('txt_remove_success'))
                let parent = this.$refs?.tableTreeScroller?.getParentRow(row.uuid)
                this.onRefresh({
                  clickOid: parent.oid,
                  clickType: parent.type,
                })
              })
              .catch((err) => {
                if (err.msg) {
                  this.$error(err.msg)
                }
              })
        },
      })
    },
    onOpenMoveModal(row) {
      this.moveVisible = true
      this.detailInfo = { ...row }
    },
    onCloseMoveModal() {
      this.moveVisible = false
    },
    onOpenObjModal(row) {
      this.objVisible = true
      this.detailInfo = { ...row }
    },
    searchModelTable(params) {
      return fetchAllPart.execute(params)
    },
    onAddOk(selectedRows) {
      if (!this.pageCode) {
        if (selectedRows.length === 0) {
          this.$warning(this.$t('txt_please_seleted_data'))
          return
        }
        let selected = []
        selectedRows.forEach((item) => {
          let temp = {
            fromOid: this.detailInfo.oid,
            fromType: this.detailInfo.type,
            toOid: item.oid,
            toType: item.type,
            quantity: 1,
          }
          selected.push(temp)
        })
        batchAddUse
            .execute(selected)
            .then((res) => {
              this.$success(this.$t('msg_save_success'))
              this.onRefresh({
                clickOid: this.currentRow.oid,
                clickType: this.currentRow.type,
              })
              this.onCloseObjModal()
              this.resetDisableList()
            })
            .catch((err) => {
              this.$error(err.msg)
            })
      } else {
        if (selectedRows.length === 0) {
          this.$warning(this.$t('txt_seletct_target'))
          return
        }
        let api, params
        let clickOid, clickType
        if (this.currentRow.opeKey === 'copyMove') {
          api = addTo
          params = {
            partOid: this.currentRow.oid,
            targetOid: selectedRows[0].oid,
          }
          clickOid = selectedRows[0].oid
          clickType = selectedRows[0].type
        } else if (this.currentRow.opeKey === 'replace') {
          let parent = this.$refs?.tableTreeScroller?.getParentRow(
              this.currentRow.uuid
          )
          api = replaceObj
          params = {
            newPartOid: selectedRows[0].oid,
            originalPartOid: this.currentRow.oid,
            parentOid: parent.oid,
          }
          clickOid = parent.oid
          clickType = parent.type
        }
        api
            .execute(params)
            .then((res) => {
              this.$success(this.$t('msg_success'))
              this.onRefresh({
                clickOid: clickOid,
                clickType: clickType,
              })
              this.onCloseObjModal()
              this.resetDisableList()
            })
            .catch((err) => {
              this.$error(err.msg)
            })
      }
    },
    onCloseObjModal() {
      this.objVisible = false
    },
    onOpenCreateModal(row) {
      this.$refs.cerateDrawer.show({
        title: this.$t('txt_reate_part'),
        modelInfo: {
          layoutName: 'create',
          modelName: 'Part',
        },
        instanceData: { modelName: 'Part' },
        params: {
          targetOid: row.oid,
          targetType: row.type,
          url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/createThenUse`,
          locationInfo: {
            catalogOid: row.catalogOid,
            catalogType: row.catalogType,
            containerOid: row.containerOid,
            containerType: row.containerType,
            containerModelDefinition: row.containerModelDefinition,
          },
        },
      })
    },
    onRefresh(data) {
      //检查是否需要切换顶级节点，顶级节点检出时，需切换最新版本
      if (data.clickOid === this.objectDetailsData.oid || data.checkParentId === this.objectDetailsData.oid) {
        findLatestInfo
            .execute({
              oid: this.objectDetailsData.oid,
              type: this.objectDetailsData.type,
            })
            .then((res) => {
              this.$emit('init', res, 'structure')
            })
            .catch((err) => {
              this.$error(err.msg)
            })
      } else {
        this.getTableData()
      }
      this.resetDisableList()
    },
    opensubstitued() {
      setTimeout(() => {
        this.$refs['childDetail'].onChangeTab('replaceItem')
      }, 0);
    },
    //切换显示隐藏3d
    toggle3dx(row) {
      //切换选中值
      this.onOpenChildDrawer(row)
      //切换状态
      this.$set(row, 'show3d', row.show3d === undefined ? true : !row.show3d)
      let childrenList = this.$refs['tableTreeScroller'].getchildrenlist(row)
      childrenList.forEach(chilrow => {
        this.$set(chilrow, 'show3d', row.show3d)
      })
      if (!row.show3d) {
        let parent = this.$refs['tableTreeScroller'].getParentRow(row.uuid)
        if (parent) {
          this.$set(parent, 'show3d', false)
        }
      }
      setTimeout(() => {
        this.$refs['childDetail'].toggle3dx()
      }, 0)
    },
    onOpenChildDrawer(row) {
      this.detailInfo = { ...row }
      this.childVisible = true
      this.onCheckboxChange(row)
    },
    onCloseChildDrawer() {
      this.childVisible = false
      this.resetDisableList()
    },
    onCheckoutThenToDetail(row) {
      if (!row.lockOwnerOid) {
        partCheckout
            .execute({
              modelDefinition: row.modelDefinition,
              oid: row.oid,
              type: row.type,
            })
            .then((res) => {
              this.renderData(res, row)
              if (row.oid !== this.objectDetailsData.oid) {
                this.onOpenDetailPage(res)
              }
            })
            .catch((err) => {
              this.$error(err.msg)
            })
      } else {
        if (row.oid !== this.objectDetailsData.oid) {
          this.onOpenDetailPage(row)
        }
      }
    },
    onOpenDetailPage(row) {
      Jw.jumpToDetail(row, { blank: true })
    },
  },
}
</script>

<style lang="less" scoped>
.bomresize {
  cursor: col-resize;
  width: 3px;
  height: 100%;
}

.content-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  .header-btn-group {
    flex: none;
  }

  .structure-wrap {
    height: 0;
    flex: 1;
  }
}

.header-btn-group {
  display: flex;
  margin-bottom: 10px;
  position: relative;

  .tool-btn {
    padding: 0 7px;
    margin-left: 8px;
  }

  .btn-divider {
    width: 1px;
    height: 33px;
    margin: 0 8px 0 16px;
    border: 1px solid rgba(30, 32, 42, 0.15);
  }

  .right-toolbar {
    position: absolute;
    right: 0;
  }
}

.left-area {
  width: 50%;
}

.right-area {
  width: 50%;
  margin-left: 12px;
  overflow: scroll;
  border: 1px solid #dcdde3;
  padding: 10px;
}

.structure-wrap {
  display: flex;

  .ant-radio-button-wrapper {
    width: 32px;
    padding: 0;
    text-align: center;
  }

  .table-number-name-wrap {
    display: flex;
    justify-content: space-between;
    cursor: pointer;

    .name-con {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      .name-item {
        color: #255ed7;
        cursor: pointer;
      }
    }

    .view-btn {
      position: absolute;
      right: 0;
    }
  }
}

/deep/.vxe-table--render-default .vxe-body--column.col--ellipsis,
/deep/.vxe-table--render-default .vxe-header--column.col--ellipsis {
  height: 40px;
}

</style>

<style lang="less">
.cell-menu-wrap-list .vxe-context-menu--link .vxe-context-menu--link-prefix {
  top: 0;
}

.ant-modal-confirm-content{
  margin-left: 0px !important;
}
</style>
