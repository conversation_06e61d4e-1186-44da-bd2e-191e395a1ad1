<template>
    <a-modal
        :visible="visible"
        :title="$t('txt_adding_business_object')"
        width="60%"
        :bodyStyle="bodyStyle"
        :mask-closable="false"
        @cancel="onCancel"
        @ok="onOk"
    >
        <div class="add-obj-wrap flex">
            <div class="left-wrap">
                <div class="flex justify-between align-center">
                    <a-input-search
                        class="search-input"
                        v-model.trim="searchKey"
                        :placeholder="$t('search_text')"
                        @search="onSearch"
                    />
                </div>
                <a-tabs v-model.trim="activeTab" @change="onTabClick">
                    <a-tab-pane key="Part" tab="Part"></a-tab-pane>
                </a-tabs>
                <jw-table ref="refTable"
                    :panel="true"
                    :height="500"
                    :tooltip-config="{}"
                    :columns="columns"
                    :data-source.sync="tableData"
                    :fetch="fetchTable"
                    :selectedRows.sync="selectedRows"
                    :pagerConfig="pagerConfig"
                    @checkbox-change="onCheckboxChange"
                >
                </jw-table>
            </div>
            <div class="right-wrap">
                <div class="right-head-wrap flex justify-between align-center">
                   <div>{{$t('txt_added')}} {{selectedData.length}} {{$t('txt_item')}}</div>
                    <a-button type="link" @click="onDeleteAll">{{$t('btn_clear')}}</a-button>
                </div>
                <div class="right-body-wrap">
                    <div class="obj-item flex justify-between align-center"
                        v-for="(item, index) of selectedData"
                        :key="item.oid">
                        <div :title="`${item.number},${item.name},${item.displayVersion}`">
                            {{item.number}},{{item.name}},{{item.displayVersion}}
                        </div>
                        <i class="jwi-iconclose-circle-full"
                            @click="onDeleteSingle(item, index)"></i>
                    </div>
                </div>
            </div>
        </div>
    </a-modal>
</template>

<script>
import ModelFactory from 'jw_apis/model-factory';

// 获取所有带版本对象
const fetchAllPart = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/fuzzyPage`,
    method: 'post',
});

// 批量添加子对象
const batchAddUse = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/batchAddUse`,
    method: 'post',
});

export default {
    name: 'structureAddSubObjModal',
    props: [
        'visible',
        'detailInfo',
    ],
    data() {
		return {
            bodyStyle: { padding: 0 },
            activeTab: 'Part',
            searchKey: '',
            pagerConfig: {
                defaultPageSize: 10,
            },
            tableData: [],
            selectedRows: [],
            selectedData: [],
		}
    },
    computed: {
        columns() {
            return [
                {
                    field: 'number',
                    title:this.$t('txt_number_of') ,
                    minWidth: 170,
                    cellRender: {
                        name:'link',
                    },
                },
                {
                    field: 'name',
                    title: this.$t('txt_name'),
                    minWidth: 170,
                },
                {
                    field: 'displayVersion',
                    title:this.$t('txt_version') ,
                    minWidth: 100,
                    cellRender: {
                        name: 'tag',
                    },
                },
                {
                    field: 'lifecycleStatus',
                    title: this.$t('txt_lifecycle'),
                    minWidth: 140,
                },
            ];
        },
    },
    mounted() {
        
	},
    watch: {
        visible(val) {
            if (val) {
                this.$nextTick(() => {
                    this.onSearch();
                })
            }
        }
    },
	methods: {
        onTabClick(key) {
            this.activeTab = key;
        },
        onSearch() {
            this.$refs.refTable.reFetchData();
        },
        fetchTable({ current, pageSize }) {
            return fetchAllPart.execute({
                index: current,
                size: pageSize,
                searchKey: this.searchKey,
                type: this.detailInfo.type,
            }).then((res) => {
                return {
                    data: res.rows,
                    total: res.count,
                }
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        onCheckboxChange({checked, row}) {
            if (checked) {
                this.selectedData.push(row);
            } else {

            }
        },
        onDeleteSingle(item, index) {
            this.selectedData.splice(index, 1);
        },
        onDeleteAll() {
            this.selectedData = [];
            this.selectedRows = [];
        },
        onOk() {
            if (this.selectedData.length === 0) {
                this.$warning(this.$t('txt_please_seleted_data'));
                return;
            }
            let selected = [];
            this.selectedData.forEach(item => {
                let temp = {
                    fromOid: this.detailInfo.oid,
                    fromType: this.detailInfo.type,
                    toOid: item.oid,
                    toType: item.type,
                    quantity: 1,
                }
                selected.push(temp);
            })
            batchAddUse.execute(
                selected
            ).then((res) => {
                this.$success(this.$t('msg_save_success'));
                this.$emit('getTableData');
                this.onCancel();
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        cancelFn() {
            this.selectedRows = [];
            this.selectedData = [];
            this.searchKey = '';
            this.activeTab = 'Part';
        },
        onCancel() {
            this.cancelFn();
            this.$emit('close');
        },
  	},
}
</script>

<style lang="less" scoped>
.add-obj-wrap {
    .left-wrap {
        width: 60%;
        border-right: 1px solid rgba(30, 32, 42, 0.06);
        /deep/.ant-tabs {
            padding: 0 0 0 20px;
        }
        /deep/.ant-tabs-bar {
            margin: 0;
        }
        /deep/.ant-tabs-nav .ant-tabs-tab {
            padding: 10px 0px;
        }
        .search-input {
            width: 326px;
            margin: 16px 0 8px 20px;
        }
    }
    .right-wrap {
        width: 40%;
        padding: 0 16px 16px;
        .right-head-wrap {
            height: 40px;
        }
        .right-body-wrap {
            height: 530px;
            margin-top: 8px;
            overflow: auto;
            .obj-item {
                height: 48px;
                padding: 0 16px;
                margin-bottom: 8px;
                background: rgba(30, 32, 42, 0.02);
                border: 1px solid rgba(30, 32, 42, 0.15);
                border-radius: 6px;
                div {
                    width: calc(~"100% - 25px");
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
    }
}
</style>
