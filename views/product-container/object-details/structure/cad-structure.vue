<template>
  <a-spin :spinning="spinning" class="structure-wrap">
    <div class="flexBox" v-if="treeData.length > 0">
      <div class="treeBox" :style="'width: ' + widths + 'px;'">
        <a-tree v-if="$route.query.modelDefinition == 'CADAssembly'" :expanded-keys.sync="expandedKeys"
          :tree-data="treeData" :load-data="onLoadData" :replace-fields="replaceFields" @select="selectTree">
          <template slot="title" slot-scope="record">
            <div class="disFlex">
              <svg class="icon" aria-hidden="true">
                <use :xlink:href="record.modelIcon"></use>
              </svg>
              <p>{{ record.cname || record.name }},{{ record.number }},{{ record.displayVersion }}（{{ record.owner }}）</p>
            </div>
          </template>
        </a-tree>
      </div>
      <div v-if="$route.query.modelDefinition == 'CADAssembly'" @mousedown="mouseDown" class="line">
        <div class="bar">
          <i class="jwi-iconminus" />
          <i class="jwi-iconminus" />
          <i class="jwi-iconminus" />
          <i class="jwi-iconminus" />
          <i class="jwi-iconminus" />
          <i class="jwi-iconminus" />
          <i class="jwi-iconminus" />
        </div>
      </div>
      <div class="contentBox">
        <jw-layout-builder ref="ref_appBuilder" type="Model" :layoutName="'show'"
          :modelName="instanceData.modelDefinition" :instanceData="instanceData">
          <template #createBy>
            <user-info :accounts="[instanceData.createBy]" :showname="true"></user-info>
          </template>
          <template #updateBy>
            <user-info :accounts="[instanceData.updateBy]" :showname="true"></user-info>
          </template>
          <template #owner>
            <user-info :accounts="[instanceData.owner]" :showname="true"></user-info>
          </template>
        </jw-layout-builder>
      </div>
    </div>
    <div v-else>
      <p style="text-align: center;">{{ $t('msg_nodata') }} </p>
    </div>
  </a-spin>
</template>

<script>
import { jwIcon, jwLayoutBuilder } from "jw_frame";
import partMoveModal from "./part-move-modal";
import instanceObjectModal from "./instance-object-modal";
import addObjectModal from "./add-object-modal";
import childDetail from "./child-detail";
import createDrawer from "../../../product-content/content-manage/create-drawer";
import { generateUUID } from "../apis";
import ModelFactory from "jw_apis/model-factory";
import { findDetail } from "apis/baseapi";
import userInfo from "components/user-info";

// 获取分类列表父节点
const fetchUseTree = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/useTree/find`,
  method: "get",
});

// 删除use关系
const deleteUse = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/deleteUse`,
  method: "post",
});

//查询结构
const findSecBom = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.cadService}/mcad/useTree/findSecBom`,
  method: "get",
});


const findCADFileApi = oid => ModelFactory.create({
  url: `${Jw.gateway}/${Jw.cadService}/mcad/findCADFile?Oid=${oid}`,
  method: "post",
});

//查询详情
const fetchObjDetail = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/findByOid`,
  method: "get",
});
export default {
  name: "structurePage",
  props: ["objectDetailsData"],
  components: {
    jwIcon,
    partMoveModal,
    instanceObjectModal,
    addObjectModal,
    childDetail,
    createDrawer,
    jwLayoutBuilder,
    userInfo
  },
  data() {
    return {
      expandedKeys: [],
      searchKey: "",
      level: 3,
      isShowLevel: true,
      tableData: [],
      selectedRows: [],
      moveVisible: false,
      replaceFields: { children: "children", title: "name", key: "oid" },
      insObjVisible: false,
      objVisible: false,
      childVisible: false,
      detailInfo: {},
      treeData: [],
      instanceData: {},
      widths: 400,
      lastX: "",
      spinning: false,
      selectOid: '',
    };
  },
  computed: {
    toolbars() {
      return [
        {
          name: this.$t("btn_search"),
          position: "before",
          display: "input",
          value: this.searchKey,
          allowClear: true,
          placeholder: this.$t("search_text"),
          suffixIcon: "jwi-iconsearch",
          key: "search",
        },
      ];
    },
    columns() {
      return [
        {
          field: "",
          title: "",
          width: 40,
          showOverflow: false,
          params: {
            showHeaderMore: false,
          },
        },
        {
          field: "name",
          title: this.$t("txt_name"),
          treeNode: true,
          minWidth: 500,
          slots: {
            default: "name",
          },
          cellRender: {
            events: {
              click: ({ row }) => {
                this.moveVisible = true;
                this.detailInfo = { ...row };
              },
            },
          },
        },
        {
          field: "number",
          title: this.$t("txt_number_of"),
          minWidth: 170,
        },
        {
          field: "displayVersion",
          title: this.$t("txt_version"),
          minWidth: 120,
          cellRender: {
            name: "tag",
          },
        },
        {
          field: "use.quantity",
          title: this.$t("txt_num"),
          minWidth: 140,
          slots: {
            default: "quantity",
          },
        },
        {
          field: "use.unit",
          title: this.$t("txt_unit"),
          minWidth: 120,
        },
        {
          field: "use.lineNumber",
          title: this.$t("txt_line_number"),
          minWidth: 140,
          slots: {
            default: "lineNumber",
          },
        },
      ];
    },
  },
  created() {
    document.addEventListener("mouseup", this.mouseupes);
  },
  mounted() {
    this.fetch();
  },
  methods: {
    getCADFile(instanceData) {
      console.log('instanceData', instanceData)
      let primarySecFile = []
      let secondaryFile = []
      findCADFileApi(instanceData.oid).execute().then(res => {
        if (res) {
          res.forEach(item => {
            let file = {
              name: item.fileName,
              oid: item.url
            }
            if (item.primary) {
              primarySecFile.push(file)
            } else {
              secondaryFile.push(file)
            }
          })

        }
      }).catch(err => {
        this.$error(err.msg)
      }).finally(() => {
        this.instanceData = instanceData
        this.instanceData.primarySecFile = primarySecFile
        this.instanceData.secondaryFile = secondaryFile
      })
    },
    mouseDown(event) {
      this.lastX = event.screenX;
      document.addEventListener("mousemove", this.mouseMove);
      console.log(this.lastX, "...", event.screenX, "...", this.widths);
    },
    mouseMove(event) {
      this.widths -= this.lastX - event.screenX;
      this.lastX = event.screenX;
      console.log(this.lastX, "...", event.screenX, "...", this.widths);
    },
    mouseupes() {
      this.lastX = "";
      document.removeEventListener("mousemove", this.mouseMove);
    },
    //选中树的某一个节点
    selectTree(a, b) {
      console.log("a3333", a)
      if (a.length == 0) {
        return;
      }

      let params = {
        oid: a[0],
        type: b.node.dataRef.type,
      };
      this.selectOid = a[0],
        findDetail
          .execute({ ...params })
          .then((res) => {
            this.getCADFile(res)
            // this.instanceData = res;
          })
          .catch((err) => {
            this.$error(err.msg);
          });
    },
    fetch() {
      findSecBom
        .execute({
          rootOid: this.objectDetailsData.oid,
        })
        .then((res) => {
          console.log(res);
          if (res) {
            this.expandedKeys = [res.oid];
            this.treeData = [res];
          } else {
            this.treeData = [];
          }
        })
        .catch((err) => {
          this.$error(err.msg);
        });
      findDetail
        .execute({
          oid: this.objectDetailsData.oid,
          type: this.objectDetailsData.type,
        })
        .then((res) => {
          this.getCADFile(res)
          // this.instanceData = res;
        })
        .catch((err) => {
          this.$error(err.msg);
        });
    },
    onLoadData(treeNode) {
      console.log(treeNode);
      return new Promise((resolve) => {
        if (treeNode.dataRef.children.length > 0) {
          resolve();
          return;
        }
        findSecBom
          .execute({
            rootOid: treeNode.dataRef.oid,
          })
          .then((res) => {
            treeNode.dataRef.children = res ? res.children : [];
            this.treeData = [...this.treeData];
            resolve();
          });
      });
    },
    onSearch() {
      this.$refs.refTable.reFetchData();
    },
    fetchTable() {
      return fetchUseTree
        .execute({
          searchKey: this.searchKey,
          rootOid: this.objectDetailsData.oid,
          maxLevel: this.level,
        })
        .then((res) => {
          if (res.length > 0) {
            this.isShowLevel = true;
          } else {
            this.isShowLevel = false;
          }
          generateUUID(res);
          this.setScopedSlotse(res);
          return {
            data: res,
          };
        })
        .catch((err) => {
          this.$error(err.msg);
        });
    },
    setScopedSlotse(tree) {
      const loop = (data) => {
        data.forEach((val) => {
          val.isOpeBtn = false;
          val.subObjVisible = false;
          val.moreVisible = false;
          if (val.children instanceof Array) {
            loop(val.children);
          }
        });
      };
      loop(tree);
    },
    onToolInput({ key }, value) {
      if (key === "search") {
        this.searchKey = value;
        this.onSearch();
      }
    },
    onChangeLevel(e) {
      this.level = e.target.value;
      this.onSearch();
    },
    cellMouseenter({ row, column }) {
      if (column.property === "name") {
        row.isOpeBtn = true;
      }
    },
    cellMouseleave({ row, column }) {
      if (column.property === "name") {
        row.isOpeBtn = false;
        if (row.subObjVisible || row.moreVisible) {
          row.isOpeBtn = true;
        } else {
          row.isOpeBtn = false;
        }
      }
    },
    onImport() { },
    onExport() { },
    onDeleteUse(row) {
      this.$confirm({
        title: this.$t("txt_sureDelete"),
        okText: this.$t("btn_ok"),
        cancelText: this.$t("btn_cancel"),
        onOk: () => {
          return deleteUse
            .execute(row.use)
            .then((res) => {
              this.$success(this.$t("txt_delete_success"));
              this.onSearch();
            })
            .catch((err) => {
              if (err.msg) {
                this.$error(err.msg);
              }
            });
        },
      });
    },
    onOpenMoveModal(row) {
      this.moveVisible = true;
      this.detailInfo = { ...row };
    },
    onCloseMoveModal() {
      this.moveVisible = false;
    },
    onOpenAddToModal(row, key) {
      this.insObjVisible = true;
      row.opeKey = key;
      this.detailInfo = { ...row };
    },
    onCloseAddToModal() {
      this.insObjVisible = false;
    },
    onOpenObjModal(row) {
      this.objVisible = true;
      this.detailInfo = { ...row };
    },
    onCloseObjModal() {
      this.objVisible = false;
    },
    onOpenChildDrawer(row) {
      this.childVisible = true;
      this.detailInfo = { ...row };
    },
    onCloseChildDrawer() {
      this.childVisible = false;
    },
  },
};
</script>

<style lang="less" scoped>
.structure-wrap {
  /deep/ .ant-spin-container {
    height: 100%;
  }

  height: 100%;

  .disFlex {
    display: flex;
    align-items: center;
  }

  .line {
    width: 6px;
    height: 100%;
    background: #f8f8f9;
    cursor: col-resize;
    display: flex;
    align-items: center;
    justify-content: center;

    .bar {
      transform: scale(0.3, 0.3);
    }
  }

  .treeBox {
    height: 100%;
    // padding: 0px 20px;
    overflow: auto;
    // border-right: 1px solid #d9d9d9;
  }

  .icon {
    width: 16px;
    min-width: 16px;
    height: 16px;
    min-height: 16px;
    margin-right: 5px;
  }

  .flexBox {
    height: 100%;
    display: flex;
  }

  .contentBox {
    flex: 1;
    overflow: auto;
    padding-right: 5px;
    padding-left: 10px;
  }

  .ant-radio-button-wrapper {
    width: 32px;
    padding: 0;
    text-align: center;
  }

  .tool-btn {
    padding: 0 7px;
  }

  .name-wrap {
    display: flex;
    justify-content: space-between;

    .name-con {
      max-width: 50%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      .name-item {
        color: #255ed7;
        cursor: pointer;
      }
    }

    .ope-btn-item {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      width: 24px;
      height: 24px;
      margin-right: 4px;
      background: #fff;
      border-radius: 4px;
      cursor: pointer;
    }

    .ope-btn {
      cursor: pointer;
    }
  }
}</style>
