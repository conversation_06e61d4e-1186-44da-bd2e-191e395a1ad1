<template>
    <jw-table
      ref="refTable"
      :loading="loading"
      :columns="columns"
      row-id="id"
      :tree-config="{ rowField: 'id' }"
      :data-source.sync="tableData"
      :fetch="fetchTable"
      :showPage="false"
    >
      <template #isLock="{ row }">
        <a-tooltip>
          <template slot="title">{{ row.lockOwnerAccount }}</template>
          <jw-icon
            v-if="row.lockOwnerOid"
            :type="
              row.lockOwnerOid === userOid
                ? '#jwi-beiwojianchu'
                : '#jwi-bierenjianchu'
            "
          />
        </a-tooltip>
      </template>
      <template #number="{ row }">
        <jwIcon :type="row.modelIcon"></jwIcon>
        <router-link
          target= '_blank'
          :to="{
            path: '/detailPage',
            query: { oid: row.oid, type: row.type, masterType: row.masterType, modelDefinition: row.modelDefinition, tabActive: 'product' }
          }"
        >
          {{ row.number }}
        </router-link>
      </template>
      <template #quantity="{ row }">
        <div v-if="!row.isTop && !row.isquantity" class="text-wrap">
          <div>
            {{ showpropvalue(row, 'quantity') }}
          </div>
          <i
            class="jwi-iconedit"
            v-if="validshowedit(row, 'quantity')"
            @click="onClickIcon(row, 'quantity')"
          ></i>
        </div>
        <a-input-number
          v-if="!row.isTop && row.isquantity"
          :value="valuebindprop(row, 'quantity')"
          :min="0"
          style="width: 100%"
          @change="(value) => onChangeUses(row, 'quantity', value)"
          @blur="onCloseEdit(row, 'quantity')"
        />
      </template>
      <template #unit="{ row }">
        <div v-if="!row.isTop && !row.isunit" class="text-wrap">
          <div>
            {{ showpropvalue(row, 'unit') }}
          </div>
          <i
            class="jwi-iconedit"
            v-if="validshowedit(row, 'unit')"
            @click="onClickIcon(row, 'unit')"
          ></i>
        </div>
        <a-select
          v-if="!row.isTop && row.isunit"
          :value="valuebindprop(row, 'unit')"
          showSearch
          allowClear
          filterOption
          style="width: 100%"
          option-filter-prop="children"
          :placeholder="$t('msg_select')"
          @change="(value) => onChangeUses(row, 'unit', value)"
          @blur="onCloseEdit(row, 'unit')"
        >
          <a-select-option
            v-for="val in unitOpts"
            :title="val.name"
            :key="val.oid"
            :value="val.name"
          >
            {{ val.name }}
          </a-select-option>
        </a-select>
      </template>
      <template #lineNumber="{ row }">
        <div v-if="!row.isTop && !row.islineNumber" class="text-wrap">
          <div>
            {{ showpropvalue(row, 'lineNumber') }}
          </div>
          <i
            class="jwi-iconedit"
            v-if="validshowedit(row, 'lineNumber')"
            @click="onClickIcon(row, 'lineNumber')"
          ></i>
        </div>
        <a-input-number
          v-if="!row.isTop && row.islineNumber"
          :value="valuebindprop(row, 'lineNumber')"
          :precision="0"
          :min="1"
          style="width: 100%"
          @change="(value) => onChangeUses(row, 'lineNumber', value)"
          @blur="onCloseEdit(row, 'lineNumber')"
        />
      </template>
      <!-- 属性列自定义插槽 -->
      <template #[code]="{ row }" v-for="code in columnSlots">
        <div :key="code">
          <div v-if="!row.isTop && !row['is' + code]" class="text-wrap">
            <div>
              {{ showpropvalue(row, code) }}
            </div>
            <i
              class="jwi-iconedit"
              v-if="validshowedit(row, code)"
              @click="onClickIcon(row, code)"
            ></i>
          </div>
          <a-input
            v-if="!row.isTop && row['is' + code]"
            :value="valuebindprop(row, code)"
            allowClear
            style="width: 100%"
            @change="(value) => onChangeUses(row, code, value.target.value)"
            @blur="onCloseEdit(row, code)"
          />
        </div>
      </template>
    </jw-table>
</template>

<script>
import { jwIcon } from 'jw_frame'
import { generateUUID } from '../apis'
import { fetchUseCols, fetchUseTree, updateUse, fetchUnitList } from './apis'
import { getParent } from 'utils/util.js'
import { getCookie } from 'jw_utils/cookie'
export default {
  name: 'structureChild',
  props: ['detailInfo'],
  components: {
    jwIcon,
  },
  data() {
    return {
      userOid: Jw.getUser().oid,
      loading: false,
      tableData: [],
      properties: [],
      columns: [],
      unitOpts: [],
      topCatalogOid: null,
      topCatalogType: null,
      level: 1,
    }
  },
  created() {
    this.resetColumns()
  },
  computed: {
    // 属性列自定义插槽
    columnSlots() {
      let slots = []
      this.columns.forEach((col) => {
        if (
          this.properties.some((item) => item.code == col.code) &&
          ![
            this.$t('txt_num'),
            this.$t('txt_line_number'),
            this.$t('txt_unit'),
          ].includes(col.title)
        ) {
          slots = slots.concat(
            Object.values(col.slots).filter((slot) => typeof slot === 'string')
          )
        }
      })
      return slots
    },
  },
  mounted() {
    this.getUnitList()
  },
  methods: {
    valuebindprop(row, prop) {
      return prop in row.use
        ? row.use[prop]
        : prop in row.use.extensionContent
        ? row.use.extensionContent[prop]
        : ''
    },
    showpropvalue(row, prop) {
      let value =
        prop in row.use
          ? row.use[prop]
          : prop in row.use.extensionContent
          ? row.use.extensionContent[prop]
          : ''
      return value
    },
    validshowedit(row, prop) {
      return (
        !row.isTop &&
        !row['is' + prop] &&
        row.fatherLockOwnerOid === this.userOid
      )
    },
    getUnitList() {
      fetchUnitList
        .execute({
          currentPage: 1,
          size: 1000,
          containerOid: getCookie('tenantOid'),
        })
        .then((res) => {
          this.unitOpts = res.rows
        })
        .catch((err) => {
          this.$error(err.msg)
        })
    },
    setInitStatus(tree, rowLevel) {
      const loop = (data, level) => {
        data.forEach((val) => {
          val.isEdit = false
          val.unitOpts = this.unitOpts
          val.treeLevel = level
          let parent = getParent(tree, val.id, 'id')
          val.parentOid = parent ? parent.oid : val.oid
          val.fatherLockOwnerOid = parent ? parent.lockOwnerOid : ''
          if (!parent) {
            val.isTop = true
            this.topCatalogOid = val.catalogOid
            this.topCatalogType = val.catalogType
          }
          if (!val.use) {
            val.use = {
              extensionContent: {},
            }
          }
          this.properties.forEach((item) => {
            val.use.extensionContent = val.use.extensionContent
              ? val.use.extensionContent
              : {}
            val['is' + item.code] = false
          })
          val.cloneUseData = _.cloneDeep(val.use)
          if (val.children instanceof Array) {
            loop(val.children, level + 1)
          }
          if (level >= this.level) {
            val.hasChild = true
          }
        })
      }
      loop(tree, rowLevel)
    },
    onClickIcon(row, key) {
      row['is' + key] = true
      this.$forceUpdate()
    },
    onCloseEdit(row, key) {
      delete row['is' + key]
      this.$forceUpdate()
    },
    getUseCols() {
      this.resetColumns()
      fetchUseCols
        .execute({
          fromCode: "Part",
          toCode: 'Part',
          relCode: 'USE',
        })
        .then((res) => {
          if (res && res.properties && res.properties.length > 0) {
            res.properties.forEach((item) => {
              this.columns.push({
                code: item.code,
                title: item.description || item.name,
                minWidth: 140,
                slots: {
                  default: item.code,
                },
              })
            })
            this.properties = res.properties
          } else {
            this.properties = []
          }
          this.$refs.refTable.reFetchData()
          this.isShowCol = true
        })
        .catch((err) => {
          this.$error(err.msg)
        })
    },

    onSearch() {
      this.getUseCols()
    },
    fetchTable() {
      this.loading = true
      return fetchUseTree
        .execute({
          rootOid: this.detailInfo.oid,
          maxLevel: 1,
        })
        .then((res) => {
          generateUUID(res)
          this.setInitStatus(res, 0)
          this.loading = false
          return {
            data: res[0].children,
          }
        })
        .catch((err) => {
          this.loading = false
          this.$error(err.msg)
        })
    },
    resetColumns() {
      this.columns = [
        {
          field: 'isLock',
          title: '',
          align: 'center',
          width: 38,
          showOverflow: false,
          params: {
            showHeaderMore: false,
          },
          slots: {
            default: 'isLock',
          },
        },
        {
          field: 'number',
          title: this.$t('txt_number_of'),
          treeNode: true,
          minWidth: 300,
          slots: {
            default: 'number',
          },
        },
        {
          field: 'name',
          title: this.$t('txt_name'),
          minWidth: 170,
        },
        {
          field: 'displayVersion',
          title: this.$t('txt_version'),
          minWidth: 160,
          cellRender: {
            name: 'tag',
          },
        },
        {
          field: 'lifecycleStatus',
          title: this.$t('txt_plan_lifecycle'),
          minWidth: 140,
        },
        // {
        //   field: 'use.quantity',
        //   title: this.$t('txt_num'),
        //   minWidth: 140,
        //   slots: {
        //     default: 'quantity',
        //   },
        // },
        // {
        //   field: 'use.unit',
        //   title: this.$t('txt_unit'),
        //   minWidth: 140,
        //   slots: {
        //     default: 'unit',
        //   },
        // },
      ]
    },
    onChangeUses(row, key, value) {
      if (key in row.use) {
        this.$set(row, 'use', { ...row.use, [key]: value })
      } else {
        this.$set(row, 'use', {
          ...row.use,
          extensionContent: { ...row.use.extensionContent, [key]: value },
        })
      }
      console.log('修改值', row)
      this.updateLine(row)
    },
    updateLine(row) {
      updateUse
        .execute(row.use)
        .then((res) => {
          console.log('更新成功')
        })
        .catch((err) => {
          this.$error(err.msg)
        })
    },
  },
}
</script>

<style lang="less" scoped>
.default-form {
}
.text-wrap {
  display: flex;
  justify-content: space-between;
}

</style>
