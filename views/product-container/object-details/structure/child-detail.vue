<template>
  <div class="childrendetail">
    <div class="tabgroup">
      <div v-for="(item, index) in tabs" :key="index">
        <div v-if="validshow(item.key)" class="tab-title" @click="onChangeTab(item.key)"
          :class="{ checktab: item.key === activeTab }">
          {{ item.name }}
        </div>
      </div>
    </div>

    <div class="detailcontent-area">
      <basic-info v-if="activeTab === 'basicInfo'" ref="basicInfo" :detailInfo="fulldetail"></basic-info>
      <child-structure v-if="activeTab === 'childstructure'" :detailInfo="detailInfo"
        ref="childstructure"></child-structure>
      <replace-item v-if="activeTab === 'replaceItem'" ref="replaceItem" :detailInfo="fulldetail" :parentData="parentData"
        @getTableData="onSearch"></replace-item>
      <where-used v-if="activeTab === 'whereUsed'" ref="whereUsed"></where-used>
      <effectivity v-if="activeTab === 'effectivity'" :recorddata="fulldetail" ref="effectivity" />
      <model ref="model" v-if="activeTab === 'visualization' && show3dxoid" :value="show3dxoid" class="model-3d"></model>
    </div>
  </div>
</template>

<script>
import basicInfo from './basic-info'
import childStructure from './child-structure'
import replaceItem from './replace-item'
import whereUsed from '../usage/index'
import effectivity from '../effectivity'
import ModelFactory from 'jw_apis/model-factory'
import model from 'jw_engine/example/views/cad'
import { useDrawerStore } from "jw_engine/stores/drawer";
import { useEngineStore } from "jw_engine/stores/engine";
import { findDetail } from "apis/baseapi";

// 通过part获取CAD的Oid
const fetchCadOid = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/findBeDescribed`,
  method: 'post',
})

export default {
  name: 'structureChildDetail',
  props: ['visible', 'detailInfo', 'rootTreeoid'],
  provide() {
    return {
      detailsData: () => this.detailInfo,
    }
  },
  components: {
    basicInfo,
    childStructure,
    replaceItem,
    whereUsed,
    effectivity,
    model,
  },
  data() {
    return {
      bodyStyle: { padding: 0, width: '50vw', height: 'calc(100% - 55px)' },
      maskStyle: { display: 'none' },
      wrapStyle: { width: 'auto' },
      title: '',
      activeTab: 'basicInfo',
      parentData: {},
      fulldetail: {},
      show3dxoid: null,
      tabs: [
        {
          name: this.$t('txt_property'),
          key: 'basicInfo',
        },
        {
          name: this.$t('txt_visualization'),
          key: 'visualization',
        },
        {
          name: this.$t('txt_more'),
          key: 'more',
        },
        {
          name: this.$t('txt_substructure'),
          key: 'childstructure',
        },
        {
          name: this.$t('txt_replacement_parts'),
          key: 'replaceItem',
        },
        {
          name: this.$t('txt_by_using'),
          key: 'whereUsed',
        },
        {
          name: this.$t('txt_effectivity_tab_title'),
          key: 'effectivity',
        },
      ],
      showmore: true,

      interval: null,
      lightdata: [],
      lightref: null,
      lightindex: 0,
    }
  },
  watch: {
    detailInfo: {
      handler(val) {
        if (val.oid) {
          this.title = this.detailInfo.name
          this.getObjDetail()
          this.parentData = this.$parent?.$refs?.tableTreeScroller?.getParentRow(this.detailInfo.uuid)
        }
      },
      immediate: true
    },
    visible(visible) {
      if (visible) {
        this.wrapStyle.width = '50vw'
      } else {
        this.wrapStyle.width = '0'
      }
    },
  },
  mounted() {
    useEngineStore().workbenchFactory["ASSEMBLY"] =
      require("../../../../components/engineConfig/assembly").workbench;
    useDrawerStore()?.close();
  },
  destroyed() {
    clearInterval(this.interval)
  },
  methods: {
    initinterval() {
      if (this.interval) {
        clearInterval(this.interval)
      }
      this.checkrowlight()
      this.interval = setInterval(() => {
        console.log("定时任务执行")
        this.checkrowlight()
      }, 500);
      if (this.lightdata.length <= 1) {
        clearInterval(this.interval)
      }
    },
    checkrowlight() {
      if (this.lightdata[this.lightindex]) {
        this.lightref?.handleRowClick({ row: this.lightdata[this.lightindex] })
        if (this.lightdata[this.lightindex + 1]) {
          this.lightindex += 1
        } else {
          this.lightindex = 0
        }
      }
    },
    validshow(key) {
      let flat = true
      if (this.showmore) {
        //展示所有
        if (key === 'more') {
          flat = false
        }

        switch (key) {
          case 'replaceItem':
            flat = this.parentData && this.parentData.oid
            break
          case 'effectivity':
            flat = this.fulldetail.genericType === 'variable'
            break
        }
      } else {
        //隐藏展示
        if (
          !(key === 'basicInfo' || key === 'visualization' || key === 'more')
        ) {
          flat = false
        }
      }
      return flat
    },
    //显示隐藏3d图
    toggle3dx() {
      let treeRef = this.findChildrenTree(this.$refs.model)
      if (treeRef) {
        treeRef.setTargetTree();
        let allTree = treeRef?.tree
        this.initTree(allTree ?? [], null)
        let lines = []
        this.getByuuid(allTree ?? [], this.detailInfo.reluuid, lines)
        if (lines) {
          lines.forEach(line => {
            treeRef?.togglePartDisplay(line, line.show3d)
          })
        }
      }
    },
    //获取字符串hash值
    getStringHash(val) {
      var hash = 0, i, chr
      if (val === undefined || val === null || val.length === 0) return hash
      for (i = 0; i < val.length; i++) {
        chr = val.charCodeAt(i)
        hash = (hash << 5) - hash + chr
        hash |= 0 // Convert to 32bit integer
      }
      return hash
    },
    // TreeData
    initTree(treeData, parentNumber) {
      treeData.forEach(item => {
        item.reluuid = (parentNumber ? parentNumber + '-' : '') + this.getStringHash(item.referenceName)
        if (item.children && item.children.length > 0) {
          this.initTree(item.children, item.reluuid)
        }
      })
    },
    handleShowModal() {
      let { detailInfo } = this
      if (detailInfo.masterType === 'Part') {
        if (this.show3dxoid) {
          this.checkNowNode(detailInfo)
        } else {
          let param = {
            fromType: 'MCADIteration',
            toOid: this.rootTreeoid,
            toType: 'PartIteration',
          }
          fetchCadOid
            .execute(param)
            .then((data) => {
              if (data.length > 0) {
                this.$nextTick(() => {
                  this.show3dxoid = data[0].oid
                  this.show3dxName = data[0].name
                })
              }
            })
            .catch((err) => {
              console.error(err)
            })
        }
      } else {
        this.show3dxoid = detailInfo.oid
      }
    },
    getByuuid(tree, oid, alllist) {
      for (const node of tree) {
        if (node.reluuid === oid) {
          alllist.push(node)
        }
        if (node.children) {
          this.getByuuid(node.children, oid, alllist)
        }
      }
    },

    findChildrenTree(model){
      for (let i = 0; i < model.$children.length; i++) {
        const element = model.$children[i];
        if(element?.$refs?.structureTree){
          return element
        }
        let res = this.findChildrenTree(element)
        if(res){
          return res
        }
      }
      
    },

    //初始化选中当前节点
    checkNowNode(detailInfo) {
      let treeRef = this.findChildrenTree(this.$refs.model)
      if (treeRef) {
        let allTree = treeRef?.tree
        this.initTree(allTree ?? [], null)
        let lines = []
        this.getByuuid(allTree ?? [], detailInfo.reluuid, lines)
        if (lines) {
          this.lightdata = lines
          this.lightindex = 0
          this.lightref = treeRef
        } else {
          this.lightdata = []
          this.lightindex = 0
          this.lightref = null
        }
        this.initinterval()
      }
    },

    getObjDetail() {
      return findDetail
        .execute({
          oid: this.detailInfo.oid,
          type: this.detailInfo.type,
        })
        .then((res) => {
          this.fulldetail = { ...res }
          this.onChangeTab(this.activeTab)
        })
        .catch((err) => {
          console.error(err)
          this.$error(err.msg)
        })
    },
    onChangeTab(tab) {
      if (tab !== 'more') {
        this.activeTab = tab
      }
      switch (tab) {
        case 'more':
          this.showmore = true
          break
        case 'basicInfo':
          break
        case 'childstructure':
          this.$nextTick(() => {
            this.$refs?.childstructure?.onSearch()
          })
          break
        case 'replaceItem':
          if (this.parentData && this.parentData.oid) {
            this.$nextTick(() => {
              this.$refs.replaceItem.onSearch()
            })
          } else {
            this.activeTab = 'visualization'
          }
          break
        case 'whereUsed':
          this.$nextTick(() => {
            this.$refs.whereUsed.init()
          })
          break
        case 'visualization':
          this.handleShowModal()
          break
        case 'effectivity':
          if (this.fulldetail.genericType !== 'variable') {
            this.activeTab = 'visualization'
          } else {
            this.$nextTick(() => {
              this.$refs.effectivity.init()
            })
          }
          break

        default:
          break
      }
    },
    onClose() {
      this.$emit('close')
      this.activeTab = 'basicInfo'
    },
    onSearch() {
      this.$emit('getTableData')
    },
  },
}
</script>

<style lang="less" scoped>
.childrendetail {
  height: 100%;
  display: flex;
  flex-direction: column;

  .tabgroup {
    flex: none;
  }

  .detailcontent-area {
    height: 0;
    flex: 1;
    overflow-y: scroll;
  }
}

.tabgroup {
  display: flex;
  margin-bottom: 10px;

  .tab-title {
    padding: 5px 10px;
    cursor: pointer;
    background: #f0f6ff;
    border-radius: 4px;
  }

  .checktab {
    background: #264fd4;
    color: #fff;
  }
}

.title-wrap {
  display: flex;
  justify-content: space-between;
  padding-right: 35px;

  .jwi-iconedit {
    cursor: pointer;
  }
}

.model-3d {
  height: 830px;
}
</style>
