<template>
  <div class="object-details-usage">
    <header>
      <div class="left">
        <a-input-search :placeholder="$t('search_text')"
                        v-model.trim="searchKey"
                        @search="onSearch" />
        <a-radio-group v-model.trim="level"
                       @change="onChange"
                       button-style="solid">
          <a-radio-button value="all"> All </a-radio-button>
          <a-radio-button :value="item"
                          v-for="item in maxLevel"
                          :key="item">
            {{ item }}
          </a-radio-button>
        </a-radio-group>
      </div>
      <div class="right">
        <!-- <button @click="initTable">init</button> -->
        <i class="jwi-iconproduct-mix"
           @click="swicthTable"></i>
      </div>
    </header>
    <main :id="echartUsageMain">
      <div v-show="showTable"
           class="table-usage">
        <div>
          <div class="table-header">{{$t('txt_usage_condition')}} </div>
          <div>
            <jw-table ref="refTable"
                      row-id="id"
                      height="100%"
                      v-loading="tableLoading"
                      :columns="columns"
                      :tree-config="{ rowField: 'id', expandAll: true }"
                      :data-source.sync="data"
                      :showPage="false">
              <template #numberSlot="{ row }">
                <span style="color: #255ed7;cursor: pointer;"
                      @click="onOpenDetailPage(row)">
                  <jw-icon v-if="row.modelIcon && row.modelIcon.includes('#')"
                           :type="row.modelIcon" />
                  <i v-else
                     :class="row.modelIcon"></i>
                  {{ row.number }}
                </span>
              </template>
              <template #updateDate="{ row }">
                <span style="color: #255ed7">
                  {{ formatDateFn(row.updateDate) }}
                </span>
              </template>
            </jw-table>
          </div>
        </div>
        <div v-show="objectDetailsData.type !== 'MCADIteration'">
          <div class="table-header">{{$t('txt_product_condition')}} </div>
          <div>
            <jw-table ref="refTable"
                      row-id="id"
                      height="100%"
                      v-loading="footerLoading"
                      :columns="columns"
                      :tree-config="{ rowField: 'oid', expandAll: true }"
                      :data-source.sync="footTableData"
                      :showPage="false">
              <template #numberSlot="{ row }">
                <span style="color: #255ed7;cursor: pointer;"
                      @click="onOpenDetailPage(row)">
                  <jw-icon v-if="row.modelIcon && row.modelIcon.includes('#')"
                           :type="row.modelIcon" />
                  <i v-else
                     :class="row.modelIcon"></i>
                  {{ row.number }}
                </span>
              </template>
              <template #updateDate="{ row }">
                <span style="color: #255ed7">
                  {{ formatDateFn(row.updateDate) }}
                </span>
              </template>
            </jw-table>
          </div>
        </div>
      </div>
      <div v-show="!showTable"
           class="echartUsage"
           :id="echartUsage"></div>
    </main>
  </div>
</template>

<script>
import { jwTable } from "jw_frame";
import { formatDate } from "jw_utils/moment-date";
import {
  getTableData,
  getMaxlevel,
  getEchartData,
  generateUUID,
  getProdateData,
  getCadTableData,
  getCadEchartData,
  getFuzzyTableData,
  getFuzzyCadTableData,
  getFuzzyEchartData,
  getFuzzyCadEchartData,
} from "../apis";
import G6 from "@antv/g6";

import { jwIcon } from "jw_frame";
import { v4 as uuidv4 } from "uuid";
export default {
  name: "Usage",
  components: {
    jwTable,
  },
  inject: ["detailsData"],
  components: {
    jwIcon,
  },
  computed: {
    columns() {
      return [
        {
          field: "number",
          title: this.$t('txt_number_of'),
          sortable: true,
          treeNode: true,
          slots: {
            default: "numberSlot",
          },
        },
        {
          title: this.$t('txt_name'),
          field: "name",
          minWidth: "130",
          // cellRender: {
          //   name: "link",
          //   events: {
          //     click: ({ row }) => {},
          //   },
          // },
        },
        {
          title:this.$t('txt_type') ,
          field: "modelDefinition",
          minWidth: "120",
        },
        // {
        //   title: "产品名称",
        //   dataIndex: "productName",
        //   field: "productName",
        //   cellRender: {
        //     name: "link",
        //   },
        // },
        // {
        //   title: "视图",
        //   dataIndex: "viewName",
        //   field: "viewName",
        //   width: "120",
        //   cellRender: {
        //     name: "tag",
        //   },
        // },
        {
          title:this.$t('txt_plan_lifecycle') ,
          dataIndex: "lifecycleStatus",
          width: "160",
          field: "lifecycleStatus",
          cellRender: {
            name: "tag",
            render: ({ row }) => {
              return {
                color: "blue",
              };
            },
          },
        },
        {
          title:this.$t('txt_plan_version') ,
          dataIndex: "displayVersion",
          field: "displayVersion",
          width: "120",
          cellRender: {
            name: "tag",
          },
        },
        {
          field: "updateDate",
          title: this.$t('txt_update_date'),
          width: "150",
          sortable: true, // 开启排序
          slots: {
            default: "updateDate",
          },
        },
      ];
    },
    objectDetailsData() {
      return this.detailsData();
    },
  },
  data() {
    return {
      echartUsageMain: uuidv4(),
      echartUsage: uuidv4(),
      searchKey: undefined,
      level: "all",
      showTable: true,
      maxLevel: 4,
      graph: null,
      data: [],
      footTableData: [],
      echartData: {},
      expandRowKeys: [],
      tableLoading: false,
      footerLoading: false,
    };
  },
  methods: {
    onSearch(val) {
      this.tableLoading = true;
      if (val) {
        if (this.objectDetailsData.type === "MCADIteration") {
          let params = {
            searchKey: this.searchKey,
            maxLevel: this.level === "all" ? this.maxLevel : this.level,
            mcadOid: this.objectDetailsData.oid,
          };
          getFuzzyTableData(params)
            .then((res) => {
              generateUUID(res);
              this.data = res;
            })
            .catch((err) => {})
            .finally(() => {
              this.tableLoading = false;
            });
        } else if (this.objectDetailsData.type === "ECADIteration") {
        } else if (this.objectDetailsData.type === "PartIteration") {
          let params = {
            searchKey: this.searchKey,
            maxLevel: this.level === "all" ? this.maxLevel : this.level,
            partOid: this.objectDetailsData.oid,
          };
          getFuzzyEchartData(params)
            .then((res) => {
              generateUUID(res);
              this.data = res;
            })
            .catch((err) => {})
            .finally(() => {
              this.tableLoading = false;
            });
        } else {
          let params = {
            searchKey: this.searchKey,
            maxLevel: this.level === "all" ? this.maxLevel : this.level,
            partOid: this.objectDetailsData.oid,
          };
          getFuzzyCadTableData(params)
            .then((res) => {
              generateUUID(res);
              this.data = res;
            })
            .catch((err) => {})
            .finally(() => {
              this.tableLoading = false;
            });
        }
        this.initTableFoot();
        const graph = this.graph;
        graph.clear();
        if (this.objectDetailsData.type === "MCADIteration") {
          let params = {
            searchKey: this.searchKey,
            maxLevel: this.level === "all" ? this.maxLevel : this.level,
            mcadOid: this.objectDetailsData.oid,
          };
          getFuzzyCadEchartData(params)
            .then((res) => {
              if (res && res.length) {
                this.objectDetailsData.children = res;
                generateUUID(this.objectDetailsData);
                this.echartData = this.objectDetailsData;
              } else {
                this.objectDetailsData.children = [];
                this.echartData = this.objectDetailsData;
              }
            })
            .catch((err) => {
              this.$error(err.msg || err.message);
            })
            .finally(() => {
              if (this.echartData) {
                graph.data(this.echartData);
                graph.render();
                graph.fitView();
                // graph.zoom(0.2);
              }
            });
        } else if (this.objectDetailsData.type === "ECADIteration") {
        } else {
          let params = {
            searchKey: this.searchKey,
            maxLevel: this.level === "all" ? this.maxLevel : this.level,
            partOid: this.objectDetailsData.oid,
          };
          getFuzzyEchartData(params)
            .then((res) => {
              if (res && res.length) {
                this.objectDetailsData.children = res;
                generateUUID(this.objectDetailsData);
                this.echartData = this.objectDetailsData;
              } else {
                this.objectDetailsData.children = [];
                this.echartData = this.objectDetailsData;
              }
            })
            .catch((err) => {
              this.$error(err.msg || err.message);
            })
            .finally(() => {
              if (this.echartData) {
                graph.data(this.echartData);
                graph.render();
                graph.fitView();
                if (
                  !this.echartData.children ||
                  !this.echartData.children.length
                ) {
                  const width = document.getElementById(
                    this.echartUsage
                  ).scrollWidth;
                  const height =
                    document.getElementById(this.echartUsage).scrollHeight ||
                    500;
                  graph.zoom(0.2, { x: width / 2, y: height / 2 });
                }
              }
            });
        }
      } else {
        this.initTable();
        if (!this.objectDetailsData.type === "MCADIteration") {
          this.initTableFoot();
        }
        this.initEchart();
      }
    },
    onChange() {
      this.initTable();
      if (!this.objectDetailsData.type === "MCADIteration") {
        this.initTableFoot();
      }
      this.initEchart();
    },
    formatDateFn(date) {
      return formatDate(date);
    },
    initTable() {
      if (this.objectDetailsData.type === "MCADIteration") {
        let params = {
          searchKey: this.searchKey,
          maxLevel: this.level === "all" ? this.maxLevel : this.level,
          mcadOid: this.objectDetailsData.oid,
        };
        getCadTableData(params)
          .then((res) => {
            generateUUID(res);
            this.data = res;
          })
          .catch((err) => {})
          .finally(() => {
            this.tableLoading = false;
          });
      } else if (this.objectDetailsData.type === "ECADIteration") {
      }
      // else if (this.objectDetailsData.type === "PartIteration") {
      //   let params = {
      //     searchKey: this.searchKey,
      //     maxLevel: this.level === "all" ? this.maxLevel : this.level,
      //     partOid: this.objectDetailsData.oid,
      //   };
      //   getFuzzyEchartData(params)
      //     .then((res) => {
      //       generateUUID(res);
      //       this.data = res;
      //     })
      //     .catch((err) => {})
      //     .finally(() => {
      //       this.tableLoading = false;
      //     });
      // }
      else {
        let params = {
          searchKey: this.searchKey,
          maxLevel: this.level === "all" ? this.maxLevel : this.level,
          partOid: this.objectDetailsData.oid,
        };
        getTableData(params)
          .then((res) => {
            generateUUID(res);
            this.data = res;
          })
          .catch((err) => {})
          .finally(() => {
            this.tableLoading = false;
          });
      }
    },
    initTableFoot() {
      this.footerLoading = true;
      let params = {
        partOid: this.objectDetailsData.oid,
        searchKey: "",
      };
      getProdateData(params)
        .then((res) => {
          this.footTableData = res;
        })
        .catch((err) => {})
        .finally(() => {
          this.footerLoading = false;
        });
    },
    initG6() {
      const width = document.getElementById(this.echartUsageMain)?.scrollWidth;
      const height =
        document.getElementById(this.echartUsageMain)?.scrollHeight || 500;
      const graph = (this.graph = new G6.TreeGraph({
        container: this.echartUsage,
        renderer: "svg",
        width,
        height,
        preventOverlap: true,
        linkDistance: 100,
        nodeSize: 100,
        // fitView: true,
        // linkCenter: true,
        modes: {
          default: [
            {
              type: "collapse-expand",
              onChange: function onChange(item, collapsed) {
                const data = item.get("model");
                data.collapsed = collapsed;
                return true;
              },
            },
            {
              type: "tooltip",
              formatText: function formatText(model) {
                return (
                  model.number + ", " + model.name + ", " + model.displayVersion
                );
              },
            },
            {
              type: "edge-tooltip",
              formatText: function formatText(model, e) {
                return model.type || "";
              },
            },
            "drag-canvas",
            "drag-node",
            "zoom-canvas",
            // Toolbar
          ],
        },
        nodeStateStyles: {
          highlight: {
            opacity: 1,
            stroke: "#096dd9",
          },
          dark: {
            opacity: 0.2,
            stroke: "#ddd",
            // fill: "red",
          },
        },
        edgeStateStyles: {
          highlight: {
            opacity: 0.4,
            stroke: "#1E202A",
            lineWidth: 8,
            lineAppendWidth: 300,
            // shadowColor: "red",
            // shadowOffsetX: 5,
            // shadowOffsetY: 5,
          },
          dark: {
            stroke: "#1E202A",
            lineWidth: 4,
            lineAppendWidth: 300,
            shadowColor: "green",
            // fill: "red",
          },
        },
        defaultEdge: {
          style: {
            opacity: 0.2,
            stroke: "#1E202A",
            lineWidth: 4,
            lineAppendWidth: 300,
          },
        },
        defaultNode: {
          size: 26,
          // type: "dom-node-1",
          style: {
            fill: "#A4C9FC",
            stroke: "#ddd",
          },
        },
        layout: {
          type: "compactBox",
          direction: "RL",
          //  unitRadius: 280,
          //  rankSep: 1500,
          //  subTreeSep: 500,
          // getId: function getId(d) {
          //   return d.id;
          // },
          // getHeight: () => {
          //   return 26;
          // },
          // getWidth: () => {
          //   return 26;
          // },
          getVGap: () => {
            return 20;
          },
          getHGap: () => {
            return 100;
          },
          radial: true,
        },
      }));

      graph.node(function (node) {
        return {
          size: 60,
          style: {},
          // shape: "diamond",
          type: "rectNode",
          label: node.name,
        };
      });
      graph.edge(function (node) {
        let use = node.target._cfg.model.use;
        return use;
      });
      graph.on("node:mouseenter", function (e) {
        var item = e.item;
        graph.setAutoPaint(false);
        graph.setItemState(item, "dark", false);
        graph.getNodes().forEach(function (node) {
          graph.clearItemStates(node);
          graph.setItemState(node, "dark", true);
        });
        graph.setItemState(item, "highlight", true);
        graph.paint();
        graph.setAutoPaint(true);
      });
      graph.on("edge:mouseenter", function (e) {
        var item = e.item;
        graph.setAutoPaint(false);
        graph.setItemState(item, "dark", false);
        graph.setItemState(item, "highlight", true);
        graph.paint();
        graph.setAutoPaint(true);
      });
      function clearAllStats() {
        graph.setAutoPaint(false);
        graph.getNodes().forEach(function (node) {
          graph.clearItemStates(node);
        });
        graph.getEdges().forEach(function (edge) {
          graph.clearItemStates(edge);
        });
        graph.paint();
        graph.setAutoPaint(true);
      }
      graph.on("node:mouseleave", clearAllStats);
      graph.on("edge:mouseleave", clearAllStats);
      graph.on("canvas:click", clearAllStats);
    },
    initEchart() {
      const graph = this.graph;
      graph.clear();
      if (this.objectDetailsData.type === "MCADIteration") {
        let params = {
          // searchKey: this.searchKey,
          maxLevel: this.level === "all" ? this.maxLevel : this.level,
          mcadOid: this.objectDetailsData.oid,
        };
        getCadEchartData(params)
          .then((res) => {
            if (res && res.length) {
              this.objectDetailsData.children = res;
              generateUUID(this.objectDetailsData);
              this.echartData = this.objectDetailsData;
            } else {
              this.objectDetailsData.children = [];
              this.echartData = this.objectDetailsData;
            }
          })
          .catch((err) => {
            console.error(err)
            // this.$error(err.msg || err.message);
          })
          .finally(() => {
            if (this.echartData) {
              graph.data(this.echartData);
              graph.render();
              graph.fitView();
              // graph.zoom(0.2);
            }
          });
      } else if (this.objectDetailsData.type === "ECADIteration") {
      } else {
        let params = {
          // searchKey: this.searchKey,
          maxLevel: this.level === "all" ? this.maxLevel : this.level,
          partOid: this.objectDetailsData.oid,
        };
        getEchartData(params)
          .then((res) => {
            if (res && res.length) {
              this.objectDetailsData.children = res;
              generateUUID(this.objectDetailsData);
              this.echartData = this.objectDetailsData;
            } else {
              this.objectDetailsData.children = [];
              this.echartData = this.objectDetailsData;
            }
          })
          .catch((err) => {
            console.error(err)
            // this.$error(err.msg || err.message);
          })
          .finally(() => {
            if (this.echartData) {
              graph.data(this.echartData);
              graph.render();
              graph.fitView();
              if (
                !this.echartData.children ||
                !this.echartData.children.length
              ) {
                const width = document.getElementById(
                  this.echartUsage
                ).scrollWidth;
                const height =
                  document.getElementById(this.echartUsage).scrollHeight || 500;
                graph.zoom(0.2, { x: width / 2, y: height / 2 });
              }
            }
          });
      }
    },
    init() {
      let _this = this;
      if (this.showTable) {
        this.initTable();
        this.initTableFoot();
      } else {
        if (this.graph) {
          this.graph.clear();
          this.initEchart();
        } else {
          let initG6Node = setTimeout(() => {
            if (_this.graph) {
              this.graph.clear();
              clearTimeout(initG6Node);
              initG6Node = null;
              _this.initEchart();
            }
          }, 1000);
        }
      }
    },
    swicthTable() {
      this.showTable = !this.showTable;
      this.init();
    },
    onOpenDetailPage(row) {
      Jw.jumpToDetail(row,{blank:true});
    },
  },
  created() {
    G6.registerNode("rectNode", {
      draw: (cfg, group) => {
        let icon = "";
        if (cfg.modelIcon && cfg.modelIcon.includes("#")) {
          icon = `<i
                  class="jw-icon"
                  ><svg aria-hidden="true" class="jw-icon-svg">
                    <use href="${cfg.modelIcon}"></use></svg
                ></i>`;
        } else {
          icon = `<i class="${cfg.modelIcon}"></i>`;
        }
        //最外面的那层
        const shape = group.addShape("circle", {
          attrs: {
            x: 0,
            y: 0,
            r: 60,
            fill: "#A4C9FC",
            stroke: "#ddd",
          },
        });
        group.addShape("dom", {
          attrs: {
            y: -50,
            x: -45,
            width: 90,
            height: 100,
            fill: "#000",
            html: `<div>` + icon + `<span>${cfg.name}</span>` + `</div>`,
          },
          capture: false,
        });
        return shape;
      },
      setState(name, value, item) {
        const group = item.getContainer();
        const shape = group.get("children")[0]; // 顺序根据 draw 时确定
        if (name === "highlight") {
          if (value) {
            shape.attr("opacity", 1);
            shape.attr("stroke", "#096dd9");
          } else {
            shape.attr("opacity", 1);
            shape.attr("fill", "#A4C9FC");
            shape.attr("stroke", "#ddd");
          }
        } else if (name === "dark") {
          if (value) {
            shape.attr("opacity", 0.2);
            shape.attr("stroke", "#ddd");
          } else {
            shape.attr("opacity", 1);
            shape.attr("fill", "#A4C9FC");
            shape.attr("stroke", "#ddd");
          }
        } else {
          shape.attr("fill", "#A4C9FC");
          shape.attr("stroke", "#ddd");
        }
      },
    });
  },
  mounted() {
    setTimeout(() => {
      this.initG6();
    }, 1500);
  },
};
</script>

<style lang="less">
.object-details-usage {
  display: flex;
  flex-direction: column;
  height: 100%;
  > header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      .ant-input-search {
        width: 200px;
      }
      .ant-radio-group {
        margin-left: 10px;
      }
    }
  }
  > main {
    // margin-top: 15px;
    height: 20px;
    flex-grow: 1;
    > .table-usage {
      height: 100%;
      // overflow: auto;
      display: flex;
      flex-direction: column;
      > div {
        flex-grow: 1;
        height: 20px;
        display: flex;
        flex-direction: column;
        > .table-header {
          height: 40px;
          display: flex;
          align-items: center;
          margin: 10px 0;
          background-color: #f3f1f1;
          color: #000;
          font-size: 14px;
          &:before {
            display: inline-block;
            width: 3px;
            margin-right: 4px;
            height: 40px;
            background-color: #7189ff;
            content: " ";
          }
        }
        //   height: 20px;
        //   flex-grow: 1;
        //   display: flex;
        //   flex-direction: column;
        //   .jw-table {
        //     height: 20px;
        //     flex-grow: 1;
        //   }
        .table-header + div {
          flex-grow: 1;
          height: 20px;
          .jw-table {
            height: 100%;
          }
        }
        .vxe-table--body-wrapper {
          // height: inherit !important;
        }
      }
    }
    .echartUsage {
      height: 100%;
    }
  }
  .g6-tooltip {
    pointer-events: none;
    margin-top: 120px;
    // margin-left: 30px;
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    font-size: 12px;
    color: #545454;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 10px 8px;
    box-shadow: rgb(174, 174, 174) 0px 0px 10px;
  }
  foreignObject {
    pointer-events: none;
    div {
      display: flex;
      flex-direction: column;
      align-items: center;
      > i {
        font-size: 50px;
        height: 75px;
        width: 50px;
        svg {
          width: 50px;
          height: 50px;
        }
      }
      > span {
        margin-top: -15px;
        overflow: hidden;
        width: 100%;
        white-space: nowrap;
        text-overflow: ellipsis;
        text-align: center;
      }
    }
  }
}
</style>
