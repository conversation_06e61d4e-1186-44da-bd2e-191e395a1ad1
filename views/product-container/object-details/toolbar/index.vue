<!--
* @Author:<EMAIL>
* @Date: 2022/04/28 17:02:02
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/04/28 17:02:02
* @Description: 
-->
<template>
  <div class="object-details-toolbar">
    <div class="left">
      <span class="route" @click="clearTabName">
        <router-link
          v-if="
            $route.query.formPage && $route.query.formPage === 'process-task'
          "
          :title="$route.query.treeName"
          :to="{
            path: '/process-task',
            query: {
              taskId: $route.query.taskId,
              processInstanceId: $route.query.processInstanceId,
            },
          }"
        >
          {{ $route.query.treeName }}
        </router-link>
        <router-link
          v-if="$route.query.masterType === 'DocumentTemplateMaster'"
          :to="{
            path: '/classificationManagement',
          }"
        >
          {{ $t("sys_classified_management") }}
        </router-link>
          <!-- :to="{
            
          }" -->
        <span
          v-else
          :title="breadcrumbLink || $route.query.treeName"
          class="text"
          @click="clickGoFolder"
        >
          {{ breadcrumbLink || $route.query.treeName }}
        </span>
        &gt;
      </span>
      <span v-if="objectDetailsData.lockOwnerOid">
        <jw-icon v-if="lock" type="#jwi-beiwojianchu"></jw-icon>
        <jw-icon v-else type="#jwi-bierenjianchu"></jw-icon>
      </span>
      <!-- <span> <jw-icon type="#jwi-liuchengzhong"></jw-icon></span> -->
      <div
        :class="`${
          getDeactivate(objectDetailsData.lifecycleStatus) ? 'title2' : 'title'
        }`"
        :title="objectDetailsData.name"
      >
        <jw-icon
          v-if="
            objectDetailsData.modelIcon &&
            objectDetailsData.modelIcon.includes('#')
          "
          :type="objectDetailsData.modelIcon"
        />
        <i v-else :class="objectDetailsData.modelIcon"></i>
        <div
          :title="
            (objectDetailsData.cname || objectDetailsData.name) +
            ', ' +
            objectDetailsData.number +
            ', ' +
            objectDetailsData.displayVersion
          "
        >
          <span>{{objectDetailsData.cname || objectDetailsData.name }}</span
          >, <span>{{ objectDetailsData.number }}</span
          >,
          <span>{{ objectDetailsData.displayVersion }}</span>
        </div>
      </div>
    </div>
    <div class="right">
      <operation-dropdown
        :current-record="objectDetailsData"
        :trigger="['hover']"
        @renderData="renderData"
        @complete="complete"
        @beforeCheckInFn="beforeCheckInFn"
        :codeFlag="'details'"
        :containerOid="objectDetailsData.containerOid || breadcrumbObj.id"
        :containerModel="
          objectDetailsData.containerModel || breadcrumbObj.containerModel
        "
        :containerType="
          objectDetailsData.containerType || breadcrumbObj.containerType
        "
        @batchOperator="batchOperator"
      >
        <a-button type="primary">
          <i class="jwi-iconmenu-application" style="margin-right: 10px"></i>
          &nbsp;&nbsp;&nbsp;&nbsp;{{ $t("txt_operation") }}
          <a-icon type="down" />
        </a-button>
      </operation-dropdown>
      <a-button
        :title="$t('txt_switch_new_version')"
        v-if="showNewButton"
        @click="selectNewVersion"
        >{{ $t("txt_switch_new") }}
      </a-button>
      <a-select
        style="width: 200px"
        @change="handleChange"
        :placeholder="$t('txt_select_object')"
        allowClear
        v-model.trim="objectDetailsData.oid"
      >
        <a-select-option
          v-for="item in routeObject"
          :key="item.oid"
          :value="item.oid"
        >
          {{ item.displayVersion + " | " + $t(item.lifecycleStatus) }}
        </a-select-option>
      </a-select>
      <!-- <a-dropdown>
        <a-menu slot="overlay" @click="handleMenuClick1">
          <a-menu-item v-for="item in routeObject" :key="item.oid">
            {{ item.displayVersion + " | " + item.name }}
          </a-menu-item>
        </a-menu>
        <a-button>
          <span>A.1</span> | inwork
          <a-icon type="down" />
        </a-button>
      </a-dropdown> -->
      <!-- <a-input-search
        placeholder="查找产品、对象、文档、任务等"
        style="width: 350px"
        @search="onSearch"
      >
        <a-select
          slot="addonBefore"
          placeholder="字段类型"
          style="width: 100px"
        >
          <a-select-option value="int"> int </a-select-option>
          <a-select-option value="string"> string </a-select-option>
        </a-select>
      </a-input-search> 
      <span>
        <i class="jwi-iconchat-full"></i>
      </span>
      <span>
        <i class="jwi-iconhelp-circle-full"></i>
      </span>-->
    </div>

    <batch-operator
      v-show="false"
      ref="batch-operator"
      :selectList.sync="selectedRows"
      :containerOid="objectDetailsData.containerOid"
      :containerModelDefinition="objectDetailsData.containerModelDefinition"
      :containerType="objectDetailsData.containerType"
    />
  </div>
</template>

<script>
import { jwIcon } from "jw_frame"
import { getHistoryList, getDropdownList } from "../apis"
import operationDropdown from "components/operation-dropdown"
import BatchOperator from "../../../batch-operator-dialog/batch-operator.vue"
import commonStore from "jw_stores/common";

export default {
  name: "Toolbar",
  props: {
    latestVersion: String,
  },
  data() {
    return {
      routeObject: [],
      operationList: [],
      disabledInit: false,

      selectedRows: [],
    }
  },
  inject: ["detailsData", "beforeCheckInFn"],
  computed: {
    breadcrumbData() {
      console.log("computed objectDetailsData:", this.objectDetailsData)
      const containerInfo = (this.objectDetailsData.containerInfo || []).slice()
      // 第二个也是 容器
      containerInfo.splice(1, 1)

      const breadcrumb = containerInfo.map(
        ({
          name,
          modelDefinition,
          catalogOid,
          catalogType,
          containerOid,
          containerType,
          type,
          oid,
        }) => ({
          oid,
          name,
          modelDefinition,
          catalogOid,
          catalogType,
          containerOid: containerOid || oid, // 第一个为 容器，containerOid 不存在，取其自身 oid
          containerType: containerType || type,
        })
      )

      return breadcrumb
    },
    breadcrumbLink() {
      return this.breadcrumbData.map((v) => v.name).join("/")
    },
    breadcrumbObj() {
      const { containerOid, containerType, modelDefinition, name } =
        this.breadcrumbData[0] || {}
        const { oid } = this.breadcrumbData[this.breadcrumbData.length - 1] || {}
      return {
        oid: containerOid,
        masterType: modelDefinition,
        type: containerType,
        modelDefinition: modelDefinition,
        containerModel: modelDefinition,
        containerName: name,
        containerType,
        treeName: this.breadcrumbLink,
        treeOid: oid
      }
    },
    objectDetailsData() {
      return this.detailsData()
    },
    //过滤掉详情
    operationListFilter() {
      if (this.operationList && this.operationList.length) {
        return this.operationList.filter((p) => p.code !== "details")
      }
    },
    lock() {
      return this.objectDetailsData.lockOwnerOid === Jw.getUser().oid
    },
    showNewButton() {
      return (
        this.routeObject.length &&
        this.objectDetailsData.oid !== this.routeObject.at(0).oid
      )
    },
    getDeactivate(lifecycleStatus) {
      return (lifecycleStatus) => {
        return Jw.deactivate ? Jw.deactivate.includes(lifecycleStatus) : false
      }
    },
  },
  components: {
    jwIcon,
    operationDropdown,
    BatchOperator,
  },
  methods: {
    batchOperator(record, type) {
      this.selectedRows = [record]
      this.$nextTick(() => {
        this.$refs["batch-operator"].validSelect(type)
      })
    },
    clearTabName() {
      sessionStorage.removeItem("currentTabName")
    },
    handleChange(value) {
      let from = this.routeObject.find((p) => p.oid === value)
      sessionStorage.setItem("currentTabName", "info")
      this.$parent.init(from)
    },
    init() {
      // this.initOps();
      this.initHtl()
    },
    initOps() {
      let that = this
      let {
        oid,
        type,
        lockOwnerOid,
        masterType,
        levelForSecrecy,
        lifecycleStatus,
        version,
        contextOid,
        iteratedVersion,
      } = this.objectDetailsData

      getDropdownList({
        type: type,
        viewCode: "PRODUCTINSTANCE",
        contextType: "ProductContainer",
        objectType: masterType,
        objectOid: oid,
        contextOid: contextOid,
        state: lifecycleStatus,
        lockOwnerOid: lockOwnerOid,
        version: `${version}.${iteratedVersion}`,
        levelForSecrecy: levelForSecrecy,
      })
        .then((data) => {
          that.operationList = data
        })
        .catch((err) => {
          this.$error(err.msg || err.emassage)
        })
    },
    initHtl() {
      const { modelDefinition, oid, type } = this.objectDetailsData
      getHistoryList({ modelDefinition, oid, type })
        .then((res) => {
          this.routeObject = res
          this.$emit("update:latestVersion", res[0].displayVersion)
        })
        .catch((err) => {
          this.$error(err.msg || err.message)
        })
        .finally(() => {})
    },
    renderData(data) {
      !this.disabledInit && this.$parent.init(data)
    },
    complete(key) {
      if (key === "delete") {
        this.disabledInit = true
        if (
          this.$route.query.formPage &&
          this.$route.query.formPage === "process-task"
        ) {
          this.$router.push({
            path: "/process-task",
            query: {
              taskId: this.$route.query.taskId,
              processInstanceId: this.$route.query.processInstanceId,
            },
          })
        } else {
          //分类文档模板类型时关闭窗口
          if (this.$route.query.masterType === "DocumentTemplateMaster") {
            window.open("about:blank", "_top").close()
          } else {
            this.$router.push({
              path: "/product-content",
              query: this.breadcrumbObj,
            })
          }
        }
      } else if (key === "edit") {
        //  this.disabledInit = true;
      } else {
        this.disabledInit = false
      }
    },
    selectNewVersion() {
      let from = this.routeObject.at(0)
      this.$parent.init(from)
      sessionStorage.setItem("currentTabName", "info")
    },
    // 跳转到文件夹
    clickGoFolder() {
      const oids = this.breadcrumbData.map(item => item.oid).splice(1)
      commonStore.set('bread_folder_oids', oids)
      this.$router.push({
        path: '/product-content',
        query: this.breadcrumbObj
      })
    }
  },
  created() {
    // this.init();
  },
}
</script>

<style lang="less">
.object-details-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 24px;
  > div > span {
    margin-right: 5px;
    > i {
      font-size: 18px;
    }
  }
  .left {
    display: flex;
    align-items: center;
    .title {
      // max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
      background-color: #ddd;
      padding: 5px 13px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      > i {
        margin-right: 10px;
      }
      > div {
        display: flex;
        align-items: center;
        > span {
          max-width: 250px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .title2 {
      // max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
      background: #fff1f0;
      border-color: #ffa39e;
      color: #f5222d;
      padding: 5px 13px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      > i {
        margin-right: 10px;
      }
      > div {
        display: flex;
        align-items: center;
        > span {
          max-width: 150px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .route {
      color: #a69f9f;
      > a {
        max-width: 300px;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
        overflow: hidden;
      }
      .text {
        color: #1890ff;
        cursor: pointer;
      }
    }
    > i {
      &:hover {
        color: #40a9ff;
        cursor: pointer;
      }
    }
  }
  .right {
    display: flex;
    align-items: center;
    margin-right: 100px;
    > div,
    > button {
      margin-left: 15px;
    }
    .ant-btn {
      width: inherit !important;
    }
  }
}
.ant-dropdown.operation > ul {
  max-height: 200px;
  overflow: auto;
}
.invalid-materials {
  color: #f5222d;
}
</style>
