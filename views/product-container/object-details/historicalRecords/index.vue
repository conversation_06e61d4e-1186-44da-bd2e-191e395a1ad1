<!--
* @Author:<EMAIL>
* @Date: 2022/04/28 17:02:02
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/04/28 17:02:02
* @Description: 
-->
<template>
  <div class="historical-records">
    <div class="left">
      <a-button v-if="objectIsPart" type="primary" @click="getCompareData"
        >{{ $t("txt_version_compare") }}
      </a-button>
      <!-- <a-input placeholder="请输入搜索关键字" /> -->
    </div>
    <div class="center">
      <a-timeline>
        <a-timeline-item
          v-for="item in lineData"
          :key="item.oid"
          :color="objectDetailsData.oid === item.oid ? 'blue' : 'gray'"
          :id="item.oid"
          :class="{ curter: objectDetailsData.oid === item.oid }"
          @click="clickHeldler(item)"
        >
          <div class="content">
            <div class="top">
              <div class="ant-popover-arrow"></div>
              <div class="describe">
                <div>
                  <a-tag>{{ item.displayVersion }}</a-tag>
                  <a-tag>{{ $t(item.lifecycleStatus) }}</a-tag>
                </div>
                <p>{{ item.name }}, {{ item.number }}</p>
              </div>
              <div class="info">
                <span
                  >{{ new Date(item.updateDate).toLocaleDateString() }}
                  {{ new Date(item.updateDate).toLocaleTimeString() }}</span
                >
                <div>
                  <user-info :accounts="[item.updateBy]"> </user-info>
                </div>
              </div>
            </div>
            <div class="effectivity-list" v-if="effectivimap[item.oid] && effectivimap[item.oid].length">
              <div v-for="(item, index) in effectivimap[item.oid]" :key="index">
                <span>
                  {{ item.name }}：
                </span>
                <a-tooltip>
                  <template slot="title">
                    {{ item.displayValue }}
                  </template>
                  <span class="effectivity-value"
                    >{{ item.displayValue }}
                  </span>
                </a-tooltip>
              </div>
            </div>
            <div>{{ $t("txt_describe") }}：{{ item.lockNote }}</div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </div>
    <div class="right">
      <a-timeline>
        <a-timeline-item
          v-for="item in lineData"
          :key="item.oid"
          :color="objectDetailsData.oid === item.oid ? 'blue' : 'gray'"
        >
          <a
            href="javascript:void(0)"
            @click="scrollIntoView(item.oid)"
            :style="{
              color:
                objectDetailsData.oid == item.oid ? '#1890ff' : '#00000040',
            }"
          >
            {{ item.displayVersion }}</a
          >
        </a-timeline-item>
      </a-timeline>
    </div>
    <!-- 对比-选择基线弹窗 -->
    <form-modal
      :width="512"
      :title="$t('version_contrast')"
      confirm-btn-position="left"
      :data-source="formModalData"
      :visible.sync="formModalVisible"
      :body-style="{
        height: '388px',
      }"
      @confirm="formModalConfirm"
      @cancelBack="formModalVisible = false"
    >
      <!-- 对比--当前对象 -->
      <template #currentObject="{ item = {} }">
        <div
          :title="`${item.number}，${item.name}，${item.modelDefinition}`"
          :style="{
            height: '40px',
            'line-height': '40px',
            background: 'rgba(30,32,42,0.04)',
            'border-radius': '4px',
            color: 'rgba(30,32,42,0.65)',
            'white-space': 'nowrap',
            'text-overflow': 'ellipsis',
            overflow: 'hidden',
            'padding-left': '10px',
          }"
        >
          {{ item.displayVersion }}
          <a-divider type="vertical" />
          <span style="color: rgba(30, 32, 42, 0.25)">{{
            $t(item.lifecycleStatus)
          }}</span>
        </div>
        <a-divider />
      </template>
    </form-modal>
  </div>
</template>

<script>
import formModal from "components/form-modal.vue";
import addObjectModal from "components/add-object-modal";
import userInfo from "components/user-info";
import ModelFactory from "jw_apis/model-factory";
import { findBatchPartOids } from "apis/efffectivity";

// 搜索对象
const searchModelTable = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/fuzzyPage`,
  method: "post",
});

export default {
  name: "HistoricalRecords",
  inject: ["detailsData"],
  components: {
    formModal,
    addObjectModal,
    userInfo
  },
  created() {},
  data() {
    return {
      oid: undefined,
      objectIsPart: this.$route.query.masterType == "Part",

      // 对比弹窗
      formModalVisible: false,
      formModalData: [
        {
          label: '',
          prop: "currentObject",
          block: true,
          required: false,
          slotData: {},
          scopedSlots: {
            default: "currentObject",
          },
        },
        {
          label: "",
          type: "select",
          prop: "targetOid",
          placeholder: this.$t("txt_current_version_please"),
          showSearch: true,
          block: true,
          required: false,
          scopedSlots: {
            label: "version_lifecycle",
          },
          options: [],
        },
      ],

      effectivimap: {},
    };
  },
  computed: {
    lineData() {
      let routeObject =
        this.$parent.$parent.$parent.$parent.$parent.$refs.toolbar.routeObject;
      return routeObject;
    },
    objectDetailsData() {
      return this.detailsData();
    },
  },
  watch: {
    objectDetailsData: {
      immediate: true,
      handler(val) {
        this.$set(this.formModalData, 0, {
          ...this.formModalData[0],
          slotData: val,
        });
        this.$set(this.formModalData, 1, {
          ...this.formModalData[1],
          options: [],
        });
      },
    },
    lineData: {
      immediate: true,
      handler(val) {
        this.loadfindBatchPartOids();
      },
    },
  },
  methods: {
    loadfindBatchPartOids() {
      let oids = this.lineData.map((item) => item.oid);
      findBatchPartOids(oids)
        .then((resp) => {
          this.effectivimap = resp;
        })
        .catch((e) => {
          console.error(e);
          this.$error(e.msg);
        })
        .finally(() => {});
    },
    getCompareData() {
      this.formModalVisible = true;
      let data = this.lineData
        .filter((v) => v.oid != this.objectDetailsData.oid)
        .map((v) => ({
          value: v.oid,
          displayVersion: v.displayVersion,
          lifecycleStatus: v.lifecycleStatus,
          scopedSlots: { label: "version_lifecycle" },
        }));
      this.$set(this.formModalData, 1, {
        ...this.formModalData[1],
        options: data,
      });
    },
    // 弹窗-确认对比
    formModalConfirm(model) {
      const source = this.formModalData[0].slotData;
      const target = this.lineData.find((v) => v.oid == model.targetOid) || {};
      this.$router.push({
        name: "baseline-contrast",
        query: {
          ...this.$route.query,
          containerOid: this.$route.query.contextOid,
          objectType: "object",
          sourceType: source.type,
          sourceOid: source.oid,
          sourceModelType: source.modelDefinition,
          targetOid: target.oid,
          targetType: target.type,
          targetModelType: target.modelDefinition,
        },
      });
    },
    selectHandle(value, option) {
      // this.oid = value;
    },
    clickHeldler(item) {
      this.$emit("init", item, "info");
    },
    scrollIntoView(id) {
      document.getElementById("#" + id).scrollIntoView(true);
    },
  },
  created() {},
};
</script>

<style lang="less">
.historical-records {
  display: flex;
  height: 100%;
  .left {
    width: 25%;
    display: flex;
    input {
      margin-left: 15px;
    }
  }
  .right {
    width: 15%;
    margin-top: 60px;
    padding-left: 30px;
    overflow: auto;
    .ant-timeline-item {
      padding-bottom: 8px;
      .ant-timeline-item-content {
        min-height: inherit;
      }
    }
  }
  .center {
    height: calc(~"100% - 60px");
    padding: 15px 20px 0;
    overflow: auto;
    margin: 60px 0 0;
    width: 20px;
    flex-grow: 1;
    .ant-timeline-item {
      &:hover,
      &.curter {
        .content {
          cursor: pointer;
          background-color: #f0f7ff;
          // box-shadow: 0 2px 8px #acbafc;
          border: 1px solid #aac4ff;
          .ant-popover-arrow {
            border-bottom: 1px solid #aac4ff;
            border-left: 1px solid #aac4ff;
            background-color: #f0f7ff;
            // box-shadow: 0 2px 8px #acbafc;
          }
        }
      }
    }
    .content {
      background-clip: padding-box;
      border-radius: 4px;
      box-shadow: 0 2px 8px #d0d0d0;
      border: 1px solid #d0d0d0;
      padding: 16px;
      .top {
        display: flex;
        justify-content: space-between;
      }
      .bottom {
        margin-top: 4px;
      }
      .describe {
        > p {
          margin-top: 8px;
          color: #1e202ad9;
        }
      }
      .info {
        display: flex;
        align-items: center;
        > span {
          color: #8f8f8f;
        }
        > div {
          margin-left: 20px;
        }
      }
      .ant-popover-arrow {
        transform: translateY(-50%) rotate(45deg);
        top: 50%;
        left: -5px;
        display: block;
        width: 8.48528137px;
        height: 8.48528137px;
        background: transparent;
        border-style: solid;
        border-width: 4.24264069px;
        background: #fff;
        // position: absolute;
        border-color: transparent transparent #fff #fff;
        box-shadow: -3px 3px 7px #e9e9eb;
      }
    }
  }
  .center {
    //UI组件
    .ant-timeline-item-content {
      margin-left: 40px;
    }
    .ant-timeline-item-tail {
      height: 100%;
      top: 0;
    }
    .ant-timeline-item:first-child {
      .ant-timeline-item-tail {
        top: calc(50% - 10px);
      }
    }
    .ant-timeline-item-head {
      top: calc(~"50% - 20px");
    }
    .ant-timeline-item-last {
      padding: 0;
      > .ant-timeline-item-tail {
        display: block;
        height: calc(~"50% - 20px");
      }
    }
    .time {
      width: 160px;
      text-align: left;
      margin-left: 145px;
      color: #000;
      font-size: 14px;
      border: 1px solid #ddd;
      padding: 6px 12px;
      border-radius: 4px;
    }
  }
}
</style>

<style scoped lang="less">
.effectivity-value {
  white-space: nowrap;
  overflow: hidden;
  width: 500px;
  position: absolute;
  text-overflow: ellipsis;
}
.effectivity-value::-webkit-scrollbar {
  display: none;
}
</style>
