<template>
  <a-drawer :title="title" :bodyStyle="bodyStyle" width="50%" :visible="visible" @close="onClose" v-if="reloaddrawer">
    <div class="detail-drawer-wrap">
      <div class="detail-drawer-body-wrap">
        <a-form-model ref="ref_model_form" :model="modelData" :label-position="'right'">
          <a-form-model-item v-if='hasSubModel' :label="$t('txt_type')" prop="activeModle" :rules="{ required: true, message: $t('msg_select'), trigger: 'change' }">
            <a-select v-model.trim="modelData.activeModle" allowClear :placeholder="$t('msg_select')" @change="onchangeModel">
              <a-select-option v-for="item in subModelOptions" :key="item.name" :value="item.name">
                {{item.name}}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('txt_position_flex')" prop="activeCatalog" :rules="{ required: true, message: $t('txt_select_position'), trigger: 'change' }">
            <a-tree-select v-model.trim="modelData.activeCatalog" :tree-data="catalogTreeData" show-search :placeholder="$t('msg_select')" allowClear @change="onSelectCatalog" :filter-tree-node="filterTreeNode" :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }">
              <template slot="folderIconTitle" slot-scope="{ name, childType }">
                <div>
                  <jw-icon style="margin-right: 8px" :type="childType === 'child'
                                            ? '#jwi-wenjianga-youneiyong'
                                            : '#jwi-chanpin'
                                        " />
                  <span>{{ name }}</span>
                </div>
              </template>
            </a-tree-select>
          </a-form-model-item>
        </a-form-model>
        <jw-layout-builder v-if="visible && modelData.activeModle" ref="ref_appBuilder" type="Model" :layoutName="modelInfo.layoutName" :modelName="modelData.activeModle" :instanceData="instanceData" @initModel='onInitModel'>
          <template v-for="(node, slotName) in $scopedSlots" :slot="slotName" slot-scope="slotData">
            <slot :name="slotName" v-bind="slotData"></slot>
          </template>
        </jw-layout-builder>
      </div>
    </div>
    <div class="detail-drawer-foot-wrap">
      <a-button type="primary" :loading="saveLoading" @click="onSave(true)">{{$t('btn_over_next')}}</a-button>
      <a-button class="btn-cancel" :loading="saveLoading" @click="onSave(false)">{{$t('btn_done')}}</a-button>
    </div>
  </a-drawer>
</template>

<script>
import { jwLayoutBuilder } from "jw_frame";
import { findItem } from "utils/util";

import ModelFactory from "jw_apis/model-factory";

// 获取子类型
const fetchSubModelApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/query-sub-model`,
  method: "post"
});

// 文件夹目录
const fetchfolderTree = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/folder/searchTree`,
  method: "get"
});

// 获取模型绑定的分类列表
const fetchClassifyList = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.docMicroServer
  }/documentTemplate/find/documentTemplateByRootOid`,
  method: "get"
});

// 获取文档模板列表
const fetchDocumentTemplate = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.docMicroServer
  }/documentTemplate/find/documentTemplateByClsOid`,
  method: "get"
});

// 获取下载和预览按钮权限
const fetchBtnFilter = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.sysconfigServer
  }/preferences/setting/query-config-value`,
  method: "get"
});

export default {
  name: "createDrawer",
  props: {},
  components: {
    jwLayoutBuilder
  },
  data() {
    this._initInstanceData = {};
    return {
      visible: false,
      bodyStyle: { padding: 0 },
      title: "",
      saveLoading: false,
      instanceData: {},
      modelInfo: {},
      paramsData: {},
      hasSubModel: false,
      subModelOptions: [],
      catalogTreeData: [],
      modelData: {
        activeModle: undefined,
        activeCatalog: undefined
      },
      currentContainerType: "",

      reloaddrawer: true
    };
  },
  mounted() {},
  methods: {
    fetchSubModel(options) {
      fetchSubModelApi
        .execute({
          viewCode: "ENTITY_FILTER",
          objectType: this.modelInfo.modelName,

          // contextType: this.$route.query.type,
          // contextOid: this.$route.query.oid
          contextType:options.params.locationInfo.catalogType,
          contextOid:options.params.locationInfo.catalogOid
        })
        .then(res => {
          if (res.length == 1) {
            this.hasSubModel = false;
          } else {
            this.hasSubModel = true;
          }
          if (this.paramsData.activeModle) {
            this.modelData.activeModle = this.paramsData.activeModle;
          } else {
            this.modelData.activeModle = res[0].name;
          }

          this.subModelOptions = res;
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },
    getFolderTree() {
      let containerModel = this.paramsData.locationInfo
        .containerModelDefinition;

      fetchfolderTree
        .execute({
          containerModel: containerModel
        })
        .then(res => {
          this.catalogTreeData = this.recursive(res);
          this.modelData.activeCatalog = this.paramsData.locationInfo.catalogOid;
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },
    recursive(data) {
      const loop = tree => {
        tree.map(item => {
          item.key = item.oid;
          item.value = item.oid;
          item.scopedSlots = { title: "folderIconTitle" };
          if (item.children && item.children.length > 0) {
            item.children.map(item => (item.childType = "child"));
            loop(item.children);
          }
        });
      };
      loop(data);
      return data;
    },
    onchangeModel(val) {
      this.instanceData = _.cloneDeep(this._initInstanceData);
      this.$nextTick(() => {
        this.modelData.activeModle = val;
      });
    },
    onSelectCatalog(val) {
      this.$nextTick(() => {
        this.modelData.activeCatalog = val;
        let item = findItem(this.catalogTreeData, this.modelData.activeCatalog);

        this.paramsData.locationInfo.catalogOid = val;
        this.paramsData.locationInfo.catalogOid = item.type;
        this.paramsData.locationInfo.containerOid = item.containerOid;
        this.paramsData.locationInfo.containerType = item.containerType;
        this.paramsData.locationInfo.containerModelDefinition =
          item.containerModelDefinition;
      });
    },
    async show(options) {
      if (options) {
        this.title = options.title;
        this.instanceData = options.instanceData || {
          modelName: options.modelInfo.modelName
        };
        this._initInstanceData = JSON.parse(JSON.stringify(this.instanceData));
        this.modelInfo = options.modelInfo;
        this.paramsData = options.params;
      }

      this.fetchSubModel(options);
      this.getFolderTree();

      this.visible = true;
      this.saveLoading = false;
      _.delay(() => {
        this.$refs.ref_model_form && this.$refs.ref_model_form.resetFields();
        this.$refs.ref_appBuilder && this.$refs.ref_appBuilder.clearValidate();
      });
    },

    onInitModel({ layout }) {},

    checkModelForm() {
      let modelForm = this.$refs.ref_model_form;
      if (modelForm) {
        modelForm.validate().catch(err => {
          this.$error("modelForm Error....");
        });
      }
    },

    onSave(show, toeffectivity) {
      this.checkModelForm();
      let appBuilder = this.$refs.ref_appBuilder;
      appBuilder &&
        appBuilder.validate().then(() => {
          this.saveLoading = true;
          let params = appBuilder.getValue();
          params.modelDefinition = this.modelData.activeModle;
          params.locationInfo = this.paramsData.locationInfo;

          if(this.paramsData.parent){
            params.oid=this.paramsData.parent.oid
          }

          ModelFactory.create({
            url: this.paramsData.url,
            method: "post"
          })
            .execute(params)
            .then(res => {
              this.$success(this.$t("txt_create_success"));
              let item = findItem(
                this.catalogTreeData,
                this.modelData.activeCatalog
              );

              this.$emit("fetchTable", {
                oid: item.oid,
                type: item.type,
                clickOid: this.paramsData.targetOid,
                clickType: this.paramsData.targetType
              });

              this.saveLoading = false;

              try {
                this.visible = true;
                this.$refs.ref_model_form &&
                  this.$refs.ref_model_form.resetFields();
                this.$refs.ref_appBuilder &&
                  this.$refs.ref_appBuilder.clearValidate();
                this.getFolderTree();
              } catch (error) {
                console.error(error);
              }

              if (!show) {
                this.hide();
              } else {
                this.reloaddrawer = false;
                this.visible = false;
                this.onchangeModel("");
                this.$nextTick(() => {
                  this.reloaddrawer = true;
                  this.visible = true;
                  this.onchangeModel(this.modelData.activeModle);
                });
              }
            })
            .catch(err => {
              this.saveLoading = false;
              console.error(err);
              this.$error(err.msg);
            });
        });
    },
    hide() {
      this.visible = false;
      this.saveLoading = false;
    },
    onClose() {
      this.hide();
    },
    filterTreeNode: (value, treeNode, field = "name") => {
      const title = treeNode.componentOptions.propsData.dataRef[
        field
      ].toLowerCase();
      return title.indexOf((value || "").trim().toLowerCase()) > -1;
    }
  }
};
</script>

<style lang="less" scoped>
.detail-drawer-wrap {
  .detail-drawer-body-wrap {
    height: calc(~"100vh - 126px");
  }
}
</style>
