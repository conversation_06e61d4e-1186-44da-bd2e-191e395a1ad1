<template>
  <div class='design-document-wrap'>
    <jw-table ref="ref_table" disableCheck="disableCheck" :data-source.sync="tableData" :columns="getHeader"
              :selectedRows.sync="selectedRows" :toolbars="toolbars" :pagerConfig="pagerConfig"
              @onPageChange="onPageChange" @onSizeChange="onSizeChange" @onToolClick="onToolClick"
              @onToolInput="onToolInput" @checkbox-change="onSelectChange" @onOperateClick="onOperateClick">

      <template #thumbSlot="{ row }">
        <!-- 2D图类型 -->
        <div class="thumb-view" :title="$t('txt_filePreview')" @click="onPreview(row)"
             v-if="valid2d(row.modelDefinition)">
          <jwIcon type="#jwi-PDFwendang"/>
        </div>
        <!-- 3D图类型 -->
        <cad-thumbnail v-else-if="valid3d(row.modelDefinition)" :currentRow="row"></cad-thumbnail>
      </template>
      <template #numberSlot="{ row }">
        <router-link target='_blank' :to="{
                  path: '/detailPage',
                  query: {oid: row.oid, type: row.type, masterType: row.masterType, modelDefinition: row.modelDefinition, tabActive: 'product'}
                }">
          <jwIcon :type="row.modelIcon"/>
          {{ row.number }}
        </router-link>
      </template>

      <template #isLock="{ row }">
        <a-tooltip>
          <template slot="title">
            {{ row.lockOwnerAccount }} {{ $t("txt_check_out") }}
          </template>
          <jw-icon v-if="row.lockOwnerOid" :type="
                    row.lockOwnerOid === jwUser.oid
                      ? '#jwi-beiwojianchu'
                      : '#jwi-bierenjianchu'
                  "/>
        </a-tooltip>
      </template>
      <template #loadStatus="{ row }">
        <jw-icon v-if="!row.loadStatus" type="#jwi-liuchengzhong"/>
      </template>
      <template #versionSlot="{ row }">
        <span>
          {{ row.displayVersion }}
        </span>
      </template>
      <template #operation="{ row }">
        <operation-dropdown :current-record="row" @complete="fetchTable" @fetchTable="fetchTable"
                            :currentTree="objectDetailsData" :treeName="treeNameAll" :containerOid="$route.query.oid"
                            :containerModel="$route.query.modelDefinition" :containerType="$route.query.type"
                            @batchOperator="batchOperator">
        </operation-dropdown>
      </template>
    </jw-table>
    <create-drawer ref="cerateDrawer" @fetchTable="fetchTable"></create-drawer>
    <jw-search-engine-modal :title="$t('关联')" only-search-object :visible.sync="globalSearchVisible"
                            :ok-btn-loading="okBtnLoading" :model-list='modelList' @ok='addObjectOk'/>
  </div>

</template>
<script>
import {jwSearchEngineModal} from "jw_frame";
import operationDropdown from "components/operation-dropdown";
import createDrawer from "./create-drawer";
import cadThumbnail from "components/cad-thumbnail";

import ModelFactory from "jw_apis/model-factory";
import {deleted} from "../../../../apis/efffectivitydefinition";

const findByPageApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.cadService}/ecad/findByPage`,
  method: "post"
});

const batchCreateApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.cadService}/ecad/batchCreate`,
  method: "post"
});

const batchDeleteModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/relatedObject/batchDelete`,
  method: "post"
});


export default {
  components: {
    operationDropdown,
    createDrawer,
    jwSearchEngineModal,
    cadThumbnail
  },
  props: ["objectDetailsData"],
  data() {
    return {
      jwUser: Jw.getUser(),
      permissionList: [],
      tableData: [],
      selectedRows: [],
      searchKey: "",
      treeNameAll: "",
      pagerConfig: {
        current: 1,
        pageSize: 20,
        total: 0
      }, //分页配置
      globalSearchVisible: false, // 添加对象--搜索
      okBtnLoading: false,
      modelList: [
        {
          name: "ECAD",
          code: "ECADIteration"
        }
      ]
    };
  },
  computed: {
    getHeader() {
      return [
        {
          field: "isLock",
          title: "",
          params: {
            showHeaderMore: false
          },
          align: "center",
          width: 38,
          slots: {
            default: "isLock"
          }
        },
        {
          field: "thumbnailOid",
          title: "",
          params: {
            showHeaderMore: false
          },
          width: "40px",
          className: "thumbSlotclass",
          slots: {
            default: "thumbSlot"
          }
        },
        {
          field: "number",
          title: this.$t("txt_number_of"),
          sortable: true,
          slots: {
            default: "numberSlot"
          }
        },
        {
          field: "name",
          title: this.$t("txt_name"),
          sortable: true
          // slots: {
          //   default: "nameSlot"
          // }
        },
        {
          field: "modelDefinition",
          title: this.$t("txt_type"),
          sortable: true
        },

        {
          field: "lifecycleStatus",
          title: this.$t("txt_plan_lifecycle"),
          sortable: true
        },
        {
          field: "displayVersion",
          title: this.$t("txt_plan_version"),
          sortable: true,
          slots: {
            default: "versionSlot"
          }
        },
        {
          field: "updateDate",
          title: this.$t("txt_update_date"),
          sortable: true, // 开启排序
          cellRender: {
            name: "date"
          }
        },
        {
          field: "owner",
          title: this.$t("txt_owner"),
          sortable: true // 开启排序
        },
        {
          // 操作列定义
          field: "operation", //关键字
          title: this.$t("txt_operation"),
          slots: {
            default: "operation"
          }
        }
      ];
    },
    toolbars() {
      let customToolBar = [
        {
          name: "新建",
          position: "before",
          type: "default",
          key: "ECAD",
          prefixIcon: "#jwi-wendang"
          // isVisible: !!this.objectDetailsData.lockSourceOid
        },
        {
          name: this.$t("关联"),
          position: "before",
          type: "default",
          key: "existECAD",
          prefixIcon: "#jwi-wendang"
          // isVisible: !!this.objectDetailsData.lockSourceOid
        },
        {
          name: this.$t("移除"),
          position: "before",
          type: "default",
          key: "removeECAD",
          prefixIcon: "#jwi-wendang"
          // isVisible: !!this.objectDetailsData.lockSourceOid
        },
        {
          position: "before",
          display: "input",
          value: this.searchKey,
          key: "search"
        }
      ];
      return customToolBar;
    }
  },
  created() {
    this.delaySearch = _.debounce(this.fetchTable, 500);
    this.fetchTable();
  },
  methods: {
    //2d文档显示
    valid2d(modelDefinition) {
      return (
          modelDefinition == "CADDrawing" ||
          modelDefinition == "PCB" ||
          modelDefinition == "Schematic" ||
          modelDefinition == "CADLayout" ||
          modelDefinition == "CAD2DSketch" ||
          modelDefinition == "CADCEDrawing"
      );
    },
    //3d图显示
    valid3d(modelDefinition) {
      return modelDefinition != "Document";
    },
    batchOperator(record, type) {
      this.selectedRows = [record];
      this.$nextTick(() => {
        this.$refs["batch-operator"].validSelect(type);
      });
    },
    onPreview(row) {
      console.log(row.thumbnailOid);
      previewApi
          .execute({
            fileOid: row.thumbnailOid
          })
          .then(url => {
            commonStore.set("query", url);
            window.open("#/preview", "_blank");
          })
          .catch(err => {
            if (err.code === -1) {
              this.$error(this.$t("没有可预览的文件"));
            }
          })
          .finally(() => {
          });
    },
    // 添加已有ECAD
    addObjectOk(selectedRows) {
      if (!selectedRows.length)
        return this.$warning(this.$t("txt_please_seleted_data"));
      let {
        // containerOid,
        // containerType,
        // containerModelDefinition,
        // catalogType,
        // catalogOid,
        masterType,
        masterOid
      } = this.objectDetailsData;
      // let ecadList = selectedRows.map(item => {
      //   item.locationInfo = {
      //     containerOid,
      //     containerType,
      //     containerModelDefinition,
      //     catalogType,
      //     catalogOid
      //   };
      //   return item;
      // });
      const params = {
        masterType,
        masterOid,
        ecadList: selectedRows
      };

      this.okBtnLoading = true;

      batchCreateApi
          .execute(params)
          .then(data => {
            this.tableLoading = false;
            this.$success(this.$t("操作成功"));

            this.globalSearchVisible = false;
            this.okBtnLoading = false;
            this.fetchTable();
          })
          // })
          .catch(err => {
            this.tableLoading = false;
            this.okBtnLoading = false;
            this.$error(err.msg || this.$t("msg_failed"));
          });
    },
    // 工具栏点击回调
    onToolClick(item) {
      let {
        containerOid,
        containerType,
        catalogOid,
        catalogType,
        containerModelDefinition
      } = this.objectDetailsData;
      if (item.key === "existECAD") {
        this.globalSearchVisible = true;
      } else if (item.key === "delete") {
        this.fetchDelete(this.selectedRows);
      } else if (item.key === 'removeECAD') {
        this.removeECAD(this.selectedRows);
      } else if (item.key === "ECAD") {
        this.$refs.cerateDrawer.show({
          title: this.$t("btn_new_create") + item.name,
          modelInfo: {
            layoutName: "create",
            modelName: "ECAD"
          },
          params: {
            url: `${Jw.gateway}/${Jw.cadService}/ecad/create`,
            locationInfo: {
              catalogOid,
              catalogType,
              containerOid,
              containerType,
              containerModelDefinition
            },
            parent: {
              oid: this.objectDetailsData.masterOid
            }
          }
        });
      }
    },
    removeECAD( rows) {
      console.log('rows1111', rows)
      if (! rows.length)
        return this.$warning(this.$t("txt_please_seleted_data"));
      let slaveObjectOids = rows.map(row => {
        return row.oid;
      });
      let params = {
        relationConstraint:'MM',
        relationshipName: "REFERENCE",
        mainObjectOid: this.objectDetailsData.oid,
        slaveObjectOids: slaveObjectOids,
        forward: true,
        mainObjectType:this.objectDetailsData.type,
        slaveObjectType:rows[0].type
      };
      this.$confirm({
        title: this.$t('txt_delete'),
        content: this.$t('msg_confirm_delete'),
        okText: this.$t('btn_ok'),
        cancelText: this.$t('btn_cancel'),
        onOk: () => {
          // 确认删除吗
          batchDeleteModel.execute(params).then(res => {
            this.tableLoading = false;
            this.$success(this.$t("操作成功"));
            this.globalSearchVisible = false;
            this.fetchTable();
          }).catch(err => {
            this.tableLoading = false;
            this.$error(err.msg || this.$t("msg_failed"));
          });
        }
      })
    },
    // 工具栏输入回调
    onToolInput({key}, value) {
      if (key === "search") {
        this.searchKey = value;
        this.pagerConfig = {
          current: 1,
          pageSize: 20,
          total: 0
        };
        this.delaySearch();
      }
    },
    // 选择列回调
    onSelectChange(args) {
      this.selectRow = args;
    },
    // 操作列回调
    onOperateClick(key, row) {
      if (key === "lanuch") {
        ///
      } else if (key === "delete") {
        this.onDelete(row);
      }
    },
    // 数据请求函数
    fetchTable() {
      this.tableLoading = true;
      let {current, pageSize} = this.pagerConfig;
      let param = {
        searchKey: this.searchKey.trim(),
        masterType: this.objectDetailsData.masterType,
        masterOid: this.objectDetailsData.masterOid,
        index: current,
        size: pageSize
      };
      findByPageApi
          .execute(param)
          .then(res => {
            this.tableLoading = false;
            this.tableData = res.rows;
            this.pagerConfig.total = res.count;
            this.total = res.count;
            return {data: res.rows, total: res.count};
          })
          .catch(err => {
            this.tableLoading = false;
            this.$error(err.msg || this.$t("msg_failed"));
          });
    },
    //分页操作
    onPageChange(page, pageSize) {
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;
      this.fetchTable();
    },
    onSizeChange(pageSize, page) {
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;

      this.fetchTable();
    }
  }
};
</script>
<style lang="less" scoped>
.design-document-wrap {
  height: 100%;
}
</style>


