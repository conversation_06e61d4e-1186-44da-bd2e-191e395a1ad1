/*
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-03-25 10:01:30
 * @LastEditTime: 2022-03-25 10:01:30
 * @LastEditors: <EMAIL>
 */

// import Vue from 'vue'
import ModelFactory from "jw_apis/model-factory"

// import uuidv1 from "uuid/v1";
import { v4 as uuidv4 } from 'uuid'


/**
 * 使用情况表格数据
 * @param {*} params 
 * @returns 
 */
const getTableData = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/whereUsedTree`,
        method: "post",
    }).execute(params)
}
/**
 * 使用情况谱图
 * @param {*} params 
 * @returns 
 */
const getEchartData = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/whereUsedGraphTree`,
        method: "post",
    }).execute(params)
}
/**
 * 使用情况层级
 * @param {*} params 
 * @returns 
 */
const getMaxlevel = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/whereUsedTree/maxLevel`,
        method: "get",
    }).execute(params)
}

/**
 * 生成UUid
 * @param {*} data 
 */
const uuidv1 = function () {
    return Number(Math.random().toString().substr(2) + Date.now()).toString(36)
}
/**
 * 为数据添加唯一值
 * @param {*} data 
 */
const generateUUID = function (data) {
    if (data.length) {
        data.forEach((element, index) => {
            element.uuid = uuidv4() + index;
            element.id = uuidv4() + index;
            if (element.children && element.children.length) {
                generateUUID(element.children)
            }
        });
    }
    else {
        data.uuid = uuidv4();
        data.id = uuidv4();
        if (data.children && data.children.length) {
            generateUUID(data.children)
        }
    }
}
/**
 * 获取关系图谱数据
 * @param {*} params 
 * @returns 
 */
const getatlasData = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.foundationServer}/instance/relatedObject/withRel/batchFuzzy`,
        method: "post",
    }).execute(params)
}

/**
 * 获取相关对象数据
 * @param {*} params 
 * @returns 
 */
const relatedObjectsData = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.foundationServer}/model/edgeDef/findModelRelated?modelCode=${params}`,
        method: "post",
    }).execute(params)
}

/**
 * 获取历史记录
 * @param {*} params 
 * @returns 
 */
const getHistoryList = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/findHistory`,
        method: "get",
    }).execute(params);
}


const findInUserView = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.foundationServer}/instance/findByOid2`,
        method: "get",
    }).execute(params);
}

/**
 * 获取header操作下拉数据
 * @param {*} params 
 * @returns 
 */
const getDropdownList = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
        method: "post",
    }).execute(params);
}
/**
 * 模糊查询所属产品
 * @param {*} params 
 * @returns 
 */
const getProdateData = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/whereUsed/product`,
        method: "get",
    }).execute(params);
}

const getCadTableData = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.cadService}/mcad/whereUsedTree`,
        method: "post",
    }).execute(params);
}

const getShowData = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.sysconfigServer}/collectionRule/findByAppliedType`,
        method: "get",
    }).execute(params);
}

const getCadEchartData = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.cadService}/mcad/whereUsedGraphTree`,
        method: "post",
    }).execute(params);
}

/**
 * 被使用情况模糊搜索
 * @param {*} params 
 * @returns 
 */
 const getFuzzyTableData = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/whereUsedTree/fuzzy`,
        method: "post",
    }).execute(params);
}

const getFuzzyCadTableData = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.cadService}/mcad/whereUsedTree/fuzzy`,
        method: "post",
    }).execute(params);
}

const getFuzzyCadEchartData = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.cadService}/mcad/whereUsedGraphTree/fuzzy`,
        method: "post",
    }).execute(params);
}


const getFuzzyEchartData = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/whereUsedGraphTree/fuzzy`,
        method: "post",
    }).execute(params);
}

 const fetchGroupListData = ModelFactory.create({
     url: `${Jw.gateway}/${Jw.sysconfigServer}/preferences/setting/query-config`,
     method: "get",
 })


export {
    getTableData,
    getEchartData,
    getMaxlevel,
    generateUUID,
    getatlasData,
    relatedObjectsData,
    getHistoryList,
    findInUserView,
    getDropdownList,
    getProdateData,
    getCadTableData,
    getShowData,
    getCadEchartData,    
    getFuzzyTableData,
    getFuzzyCadTableData,
    getFuzzyEchartData,
    getFuzzyCadEchartData,
    fetchGroupListData
}