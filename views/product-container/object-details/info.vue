<template>
    <a-spin :spinning="spinning" class="basic-info-wrap">
        <div class="info-head" :style="{ width: childObj ? '100%' : '60%' }">
            <a-button v-show="layoutName === 'update'" :loading="saveLoading" @click="onSave">{{ $t('btn_save')
            }}</a-button>
        </div>
        <div class="info-body">
            <div class="info-body-con" :style="{ width: childObj ? '100%' : '60%' }">
<!--                <jw-layout-builder v-if="objectDetailsData.modelDefinition && showlayout" ref="ref_appBuilder" type="Model"-->
              <jw-layout-builder v-if="objectDetailsData.modelDefinition" ref="ref_appBuilder" type="Model"
                    :layoutName="layoutName" :modelName="objectDetailsData.modelDefinition"
                    :instanceData="objectDetailsData" @initModel='onInitModel'>
                    <template #createBy>
                        <user-info :accounts="[objectDetailsData.createBy]" :showname="true"></user-info>
                    </template>
                    <template #updateBy>
                        <user-info :accounts="[objectDetailsData.updateBy]" :showname="true"></user-info>
                    </template>
                    <template #owner>
                        <user-info :accounts="[objectDetailsData.owner]" :showname="true"></user-info>
                    </template>
                  <template #processInstanceId>
                      <a v-if="objectDetailsData.extensionContent.processInstanceId" :href="getDingUrl(objectDetailsData.extensionContent.processInstanceId)">流程详情</a>
                  </template>

                <template #deliveryOid="{ formData }">
                  <a-tree-select  v-model="formData.extensionContent.deliveryOid" :treeData="deliverTree" treeDefaultExpandAll show-search :disabled="layoutName === 'show'"
                                 :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" allowClear :filterTreeNode="filterTreeNode">
                    <div slot="title" slot-scope="{ name, modelDefinition, modelIcon }">
                      <div
                          :class="['name-con', modelDefinition === 'Structure' || modelDefinition === 'Category' ? 'name-con-small' : 'name-con-big']">
                        <jwIcon :type="modelIcon"></jwIcon>
                        <div class="name-item">
                          {{ name }}
                        </div>

                      </div>
                    </div>
                  </a-tree-select>
                </template>
                </jw-layout-builder>
            </div>
        </div>
    </a-spin>
</template>

<script>
import { jwLayoutBuilder } from 'jw_frame';
import ModelFactory from 'jw_apis/model-factory';
import { findConf } from "/apis/config";
import userInfo from "components/user-info";

// 更新Part对象详情
const updatePartDetail = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/update`,
    method: 'post',
});

// 更新Doc对象详情
const updateDocDetail = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.docMicroServer}/document/update`,
    method: 'post',
});

// 更新ECAD对象详情
const updateEcadDetail = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.cadService}/ecad/update`,
    method: 'post',
});

// 更新文档模板对象详情
const updateDocumentTemplateDetail = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.docMicroServer}/documentTemplate/update`,
    method: 'post',
});

//判断是否有下载权限
const downloadPermission = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.customerServer}/common/doc/checkDownloadAccess`,
    method: 'get'
})

const fetchDeliveryFuzzy = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/delivery/querySelectDeliveryTree`,
  method: 'get',
});


export default {
    name: 'objectDetailsInfo',
    props: [
        'objectDetailsData',
        'opeKey',
        'childObj',
    ],
    components: {
        jwLayoutBuilder,
        userInfo
    },
    computed: {
        layoutName() {
            return this.objectDetailsData.lockSourceOid && (this.objectDetailsData.lockOwnerOid === Jw.getUser().oid) ? 'update' : 'show';
        },
    },
    data() {
        return {
            spinning: false,
            saveLoading: false,
            showlayout: false,
            file: [],
            disabledcls: false,
            deliverTree:[],
        }
    },
    created() {
        if (this.objectDetailsData.extensionContent && this.objectDetailsData.extensionContent.thumbnailFile) {
            this.objectDetailsData.extensionContent.thumbnailFile = this.objectDetailsData.extensionContent.thumbnailFile.map(item => {
                return {
                    oid: item.oid,
                    name: item.name,
                }
            })
            return this.objectDetailsData
        }
        this.loadingConfig()
    },
    mounted() {

    },
    methods: {
        loadingConfig() {
            findConf('edit_part_document_cls').then(resp => {
                const { value } = resp[0]
                if(value === 'off'){
                    this.disabledcls = true
                }else{
                    this.disabledcls = false
                }
            }).finally(() => {
                this.showlayout = true
            })
        },
        getDingUrl(instanceId) {
          let url = `https://aflow.dingtalk.com/dingtalk/mobile/homepage.htm?corpid=${Jw.dingCorPid}&dd_share=false&showmenu=false&dd_progress=false&back=native&procInstId=${instanceId}&taskId=&swfrom=isv&dinghash=approval&dtaction=os&dd_from=corp#approval`
          return `dingtalk://dingtalkclient/page/link?url=${encodeURIComponent(url)}&pc_slide=true`
        },
        onInitModel({ layout, colums }) {
            if (this.disabledcls) {
                if (['Part', 'Document'].includes(this.objectDetailsData.masterType)) {
                    let rows = layout?.content?.layout;
                    rows.forEach(row => {
                        row.forEach(item => {
                            if (item.fieldName == "classificationInfo") {
                                item.disabled = true;
                                this.$set(item, 'disabled', true)
                            }
                        });
                    });
                }
            }
            setTimeout(() => {
              if (this.layoutName !== 'update') {
                let btnList = document.getElementsByClassName('jwi-icondownload')
                //若为文档对象，判断不为owner时不允许下载以及预览
                if ('Document' === this.objectDetailsData.masterType) {
                  let account = Jw.getUser().account
                  let isOwner = this.objectDetailsData.owner === account
                  let visBtnList = document.getElementsByClassName('jwi-visible')
                  let allBtn = [...btnList, ...visBtnList];
                  allBtn.forEach(btn => {
                    btn.style.display = ''
                    if (this.isPrimaryBtn(btn)) {
                      if (!isOwner && "sys_admin" !== account && !Jw.getUser().tenantAdmin) {
                        btn.style.display = 'none'
                      }
                    }else {
                      if (this.isSecondaryFileDownBtn(btn) && !isOwner && account !== "sys_admin" && !Jw.getUser().tenantAdmin) {
                        // 先隐藏按钮，校验通过后再显示
                        btn.style.display = 'none';

                        // 校验下载权限
                        downloadPermission.execute({
                          oid: this.objectDetailsData.oid,
                          type: this.objectDetailsData.type
                        }).then(resp => {
                          // 如果权限校验通过，显示按钮
                          if (resp) {
                            btn.style.display = '';
                          }
                        }).catch(err => {
                          console.error("下载权限校验失败:", err);
                        });
                      }
                    }
                  })
                } else {
                  for (let i = 0; i < btnList.length; i++) {
                    const element = btnList[i];
                    element.style.display = 'none'
                  }
                  downloadPermission.execute({
                    oid: this.objectDetailsData.oid,
                    type: this.objectDetailsData.type
                  }).then(resp => {
                    if (resp) {
                      for (let i = 0; i < btnList.length; i++) {
                        const element = btnList[i];
                        element.style.display = ''
                      }
                    }
                  })
                }
              }
              if(['Document', 'ECAD', 'MCAD','PART'].includes(this.objectDetailsData.masterType) &&
                  (this.layoutName === 'show' || this.layoutName === 'update')) {
                this.loadDeliver();
              }
            }, 0);
        },
        isPrimaryBtn(btn) {
          if ("secondaryFile" === btn.getAttribute("prop")) {
            return false;
          } else if ("primaryFile" === btn.getAttribute("prop")) {
            return true;
          } else {
            return true;
          }
        },
      isSecondaryFileDownBtn(btn) {
        return "secondaryFile" === btn.getAttribute("prop") && "下载" === btn.getAttribute("title");
      },
        onSave() {
            let appBuilder = this.$refs.ref_appBuilder;
            return new Promise((resolve, reject) => appBuilder &&
                appBuilder
                    .validate()
                    .then(() => {
                        this.saveLoading = true;
                        let value = appBuilder.getValue();
                        let params = { ...this.objectDetailsData, ...value };
                        let api = updatePartDetail;
                        if (this.objectDetailsData.masterType === 'Part') {
                            api = updatePartDetail;
                        } else if (this.objectDetailsData.masterType === 'Document') {
                            api = updateDocDetail;
                        } else if (this.objectDetailsData.masterType === 'ECAD') {
                            api = updateEcadDetail
                        } else if (this.objectDetailsData.masterType === 'DocumentTemplateMaster') {
                            api = updateDocumentTemplateDetail
                        }
                        api.execute(
                            params
                        ).then((res) => {
                            this.$success(this.$t('msg_update_success'));
                            this.saveLoading = false;
                            this.$emit('findInUserView');
                            resolve();
                        }).catch((err) => {
                            this.saveLoading = false;
                            this.$error(err.msg);
                            reject(err);
                        });
                    })
            );
        },
      loadDeliver() {
        fetchDeliveryFuzzy.execute({
          containerOid: this.objectDetailsData.containerOid,
        }).then(resp => {
          this.deepData(resp)
          this.deliverTree = resp
        })
      },
      filterTreeNode(inputVal, treeNode){
        return treeNode.data.props.name.indexOf(inputVal) !== -1
      },
      deepData(data, onlyDirectChildren = false) {
        const scopedSlots = { title: "title" };
        let arr = [];
        const loop = (tree) => {
          tree.map((item, index) => {
            item.key = item.oid;
            item.value = item.oid;
            item.scopedSlots = scopedSlots;
            delete item.root
            if (item.children && item.children.length > 0) {
              loop(item.children);
            }
          });
        };
        loop(data);
        return onlyDirectChildren ? arr : data;
      }
    },
}
</script>

<style lang="less" scoped>
.basic-info-wrap {
    /deep/ .ant-spin-container {
        height: 100%;
    }

    height: 100%;
    display: flex;
    flex-direction: column;

    .info-head {
        display: flex;
        justify-content: flex-end;
        width: 60%;
        margin: 0 auto 16px;
    }

    .info-body {
        height: calc(~"100% - 42px");
        overflow: auto;
        padding-right: 5px;

        .info-body-con {
            height: 100%;
            width: 60%;
            margin: 0 auto;
        }
    }
}

.detail-drawer-wrap {
  .detail-drawer-body-wrap {
    height: calc(~"100vh - 126px");
  }
}

.name-con {
  display: flex;
  align-items: center;

  .name-item {
    margin: 0 8px;
    cursor: pointer;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
