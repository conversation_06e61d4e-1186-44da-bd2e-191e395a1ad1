<template>
    <div class="change-records-page">
        <div class="ec-item-wrap">
            <div class="ec-title">ECR{{$t('txt_record_en')}}</div>
            <div class="table-wrap">
                <change-table
                    :pageCode="'changeRecord'"
                    :changeApi="changeApi"
                ></change-table>
            </div>
        </div>
        <div class="ec-item-wrap">
            <div class="ec-title">ECO{{$t('txt_record_en')}}</div>
            <div class="table-wrap">
                <jw-table
                    :panel="false"
                    :showPage="false"
                    :columns="columns"
                    :fetch="fetchECOTable"
                    :dataSource.sync="ECOTableData"
                ></jw-table>
            </div>
        </div>
        <div class="ec-item-wrap">
            <div class="ec-title">ECA{{$t('txt_record_en')}}</div>
                <div class="table-wrap">
                <jw-table
                    :panel="false"
                    :showPage="false"
                    :columns="columns"
                    :fetch="fetchECATable"
                    :dataSource.sync="ECATableData"
                ></jw-table>
            </div>
        </div>
    </div>
</template>

<script>
import changeTable from '/views/change-management/table';
import ModelFactory from 'jw_apis/model-factory';

export default {
    name: 'changeRecordsPage',
    components: {
        changeTable,
    },
    data() {
        return {
            ECOTableData: [],
            ECATableData: [],
            changeApi: `${Jw.gateway}/${Jw.changeServer}/change/fuzzyChangeOrderByChangeObject`,
        }
    },
    computed: {
        columns(){
            return [
                {
                    field: 'name',
                    title: this.$t('txt_name'),
                    cellRender: {
                        name: 'link',
                        events: {
                            click: ({row}) => {
                                if(this.$route.name == "object-details"){
                                    let enterEcrRoute = {
                                        routeName:this.$route.name,
                                        query:{...this.$route.query}
                                    };
                                    sessionStorage.setItem("currentTabName","changeRecord");
                                    sessionStorage.setItem("enterEcrInfo",JSON.stringify(enterEcrRoute));
                                }
                                Jw.jumpToDetail({...row,tabActive:"change",blank:true});
                            }
                        }
                    }
                },
                {
                    field:'number',
                    title: this.$t('txt_number'),
                },
                {
                    field: 'type',
                    title: this.$t('txt_type'),
                },
                {
                    field: 'lifecycleStatus',
                    title: this.$t('txt_lifecycle'),
                }
            ]
        },
    },
    beforeRouteLeave(to, from, next) {
        to.query.fromRouteName = "变更记录";
        next();
    },
    methods: {
        fetchECOTable() {
            return ModelFactory.create({
                url: this.changeApi,
                method: 'post',
            }).execute({
                searchKey: '',
                oid: this.$route.query.oid,
                type: this.$route.query.type,
                searchType: 'ECO',
            }).then((res) => {
                return {
                    data: res,
                };
            }).catch((err) => {
                this.$error(err.msg || this.$t("msg_failed"));
            });
        },
        fetchECATable() {
            return ModelFactory.create({
                url: this.changeApi,
                method: 'post',
            }).execute({
                searchKey: '',
                oid: this.$route.query.oid,
                type: this.$route.query.type,
                searchType: 'ECA',
            }).then((res) => {
                return {
                    data: res,
                };
            }).catch((err) => {
                this.$error(err.msg || this.$t("msg_failed"));
            });
        },
    },
}
</script>

<style lang="less" scoped>
.change-records-page {
    display: flex;
    flex-direction: column;
    height: 100%;
    .ec-item-wrap {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        height: 88px;
        margin-bottom: 10px;
        &:last-child {
            margin-bottom: 0;
        }
        .ec-title {
            display: flex;
            align-items: center;
            width: 100%;
            height: 32px;
            margin-bottom: 5px;
            background: #f3f1f1;
            color: rgba(30, 32, 42, 0.85);
            &:before {
                display: inline-block;
                width: 3px;
                height: 32px;
                margin-right: 4px;
                background-color: #255ED7;
                content: "";
            }
        }
        .table-wrap {
            flex-grow: 1;
            height: 88px;
        }
    }
}
</style>
