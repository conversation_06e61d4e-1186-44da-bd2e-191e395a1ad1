<template>
  <div class="object-details-atlas"
       id="objectDetailsAtlas">
    <header>
      <div class="left">
        <a-input-search :placeholder="$t('search_text')"
                        v-show="false"
                        @search="onSearch" />
      </div>
      <div class="right">
        <a-popover placement="bottomRight">
          <template slot="content">
            <a-checkbox-group v-model.trim="showType"
                              @change="getatlasData">
              <a-row v-for="item in plainOptions"
                     :key="item.oid">
                <a-col :span="24">
                  <a-checkbox :value="item.oid">
                    {{ item.relationDisplayName }}
                  </a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </template>
          <a-button>
            <i class="jwi-iconSetting"
               style="margin-right: 8px"></i>{{$t('txt_according_content')}} 
          </a-button>
        </a-popover>
      </div>
    </header>
    <main>
      <div id="graphcontainer"
           class="graphcontainer"></div>
      <div class="svg-set-box"></div>
    </main>
  </div>
</template>

<script>
import $ from "jquery";
import * as d3 from "d3";
import "./lodash";
import systemLanguage from '@jw/scaffold/src/jw.language';
import { getatlasData, generateUUID, getShowData } from "../apis";
export default {
  inject: ["detailsData"],
  computed: {
    objectDetailsData() {
      return this.detailsData();
    },
  },
  data() {
    return {
      value: "a",
      showType: [],
      graph: undefined,
      data: [],
      childrenData: [],
      plainOptions: [],

      //dasdasda
      svg: null,
      timer: null,
      editor: null,
      simulation: null,
      linkGroup: null,
      linktextGroup: null,
      nodeGroup: null,
      nodetextGroup: null,
      nodesymbolGroup: null,
      nodebuttonGroup: null,
      nodebuttonAction: "",
      txx: {},
      tyy: {},
      colorList: [
        "#ff8373",
        "#f9c62c",
        "#a5ca34",
        "#6fce7a",
        "#70d3bd",
        "#ea91b0",
      ],
      color5: "#ff4500",
      predefineColors: [
        "#ff4500",
        "#ff8c00",
        "#90ee90",
        "#00ced1",
        "#1e90ff",
        "#c71585",
      ],
      defaultcr: 30,
      selectnodeid: 0,
      selectnodename: "",
      selectsourcenodeid: 0,
      selecttargetnodeid: 0,
      graph: {
        nodes: [],
        links: [],
      },
      graphEntity: {
        uuid: "",
        name: "",
        color: "ff4500",
        r: 30,
        x: 1000,
        y: 500,
      },

      dialogFormVisible: false,
      headers: {},
    };
  },
  methods: {
    init() {
      getShowData({
        appliedType: this.objectDetailsData.modelDefinition + "_Relation_Graph",
        mainObjectType: this.objectDetailsData.modelDefinition,
      })
        .then((res) => {
          this.plainOptions = res;
          this.showType = res.map((p) => p.oid);
        })
        .catch((err) => {})
        .finally(() => {
          this.getatlasData({ nodeId: false, model: false });
        });
    },
    getatlasData({ nodeId, model }) {
      let params = {
        oid: model ? model.oid : this.objectDetailsData.oid,
        relatedInfos: [],
      };
      this.plainOptions.forEach((p) => {
        if (this.showType.includes(p.oid)) {
          params.relatedInfos.push({
            mainObjectOid: model ? model.oid : this.objectDetailsData.oid,
            forward: p.forward,
            mainObjectType: p.mainObjectType,
            relationConstraint: p.relationConstraint,
            relationshipName: p.relationshipName,
            searchKey: p.searchKey,
            slaveObjectType: p.slaveObjectType,
            mainObjectClassName: p.mainObjectClassName,
            slaveObjectClassName: p.slaveObjectClassName
          });
        }
      });
      getatlasData(params.relatedInfos)
        .then((res) => {
          if (nodeId) {
            generateUUID(res);
            this.childrenData = res || [];
          } else {
            this.objectDetailsData.children = res;
            generateUUID(this.objectDetailsData);
            this.data = [this.objectDetailsData];
          }
        })
        .catch((err) => {
          this.$error(err.msg || err.message);
          this.data = undefined;
        })
        .finally(() => {
          if (nodeId) {
            let datas = this.formatData(this.childrenData, nodeId);
            console.log("xinzneg", datas);
            if (datas.nodes) {
              this.graph.nodes = this.graph.nodes.concat(datas.nodes);
            }
            if (datas.links) {
              this.graph.links = this.graph.links.concat(datas.links);
            }
          } else {
            let datas = this.formatData(this.data);
            this.graph.nodes = datas.nodes;
            this.graph.links = datas.links;
            // $('.linetext text')[0].style('display','none')
          }
          this.$nextTick(() => {
            this.updategraph();
          });
        });
    },
    onSearch() {},
    getNodeDetail() {},
    btnaddsingle() {
      d3.select(".graphcontainer").style("cursor", "crosshair"); //进入新增模式，鼠标变成＋
    },
    btndeletelink() {
      this.isdeletelink = true;
      d3.select(".link").attr("class", "link linkdelete"); // 修改鼠标样式为"+"
    },
    getmorenode(d) {
      this.getatlasData({ nodeId: d.id, model: d });
    },
    initgraph() {
      var graphcontainer = d3.select(".graphcontainer");
      let height = $(".graphcontainer").height();
      let width = $(".graphcontainer").width();
      // graphcontainer.remove("svg");
      this.svg = graphcontainer.append("svg");
      this.svg.attr("width", width);
      this.svg.attr("height", height);
      this.simulation = d3
        .forceSimulation()
        .force(
          "link",
          d3
            .forceLink()
            .distance(function (d) {
              return 100;
            })
            .id(function (d) {
              return d.uuid;
            })
        )
        .force("charge", d3.forceManyBody().strength(-400))
        .force("collide", d3.forceCollide().strength(-400))
        .force("center", d3.forceCenter(width / 2, height / 2));
      // .force("collision", d3.forceCollide(1));
      this.linkGroup = this.svg.append("g").attr("class", "line");
      this.linktextGroup = this.svg.append("g").attr("class", "linetext");
      this.nodeGroup = this.svg.append("g").attr("class", "node");
      this.nodetextGroup = this.svg.append("g").attr("class", "nodetext");
      this.nodesymbolGroup = this.svg.append("g").attr("class", "nodesymbol");
      this.nodebuttonGroup = this.svg.append("g").attr("class", "nodebutton");
      this.addmaker();
      this.addnodebutton();
      this.svg.on(
        "click",
        function () {
          d3.selectAll("use").classed("circle_opreate", true);
        },
        "false"
      );
    },
    updategraph() {
      var _this = this;
      var lks = this.graph.links;
      var nodes = this.graph.nodes;
      var links = [];
      lks.forEach(function (m) {
        var sourceNode = nodes.filter(function (n) {
          return n.uuid === m.sourceid;
        })[0];
        if (typeof sourceNode == "undefined") return;
        var targetNode = nodes.filter(function (n) {
          return n.uuid === m.targetid;
        })[0];
        if (typeof targetNode == "undefined") return;
        links.push({ source: sourceNode.uuid, target: targetNode.uuid, lk: m });
      });
      if (links.length > 0) {
        _l.each(links, function (link) {
          var same = _l.where(links, {
            source: link.source,
            target: link.target,
          });
          var sameAlt = _l.where(links, {
            source: link.target,
            target: link.source,
          });
          var sameAll = same.concat(sameAlt);
          _l.each(sameAll, function (s, i) {
            s.sameIndex = i + 1;
            s.sameTotal = sameAll.length;
            s.sameTotalHalf = s.sameTotal / 2;
            s.sameUneven = s.sameTotal % 2 !== 0;
            s.sameMiddleLink =
              s.sameUneven === true &&
              Math.ceil(s.sameTotalHalf) === s.sameIndex;
            s.sameLowerHalf = s.sameIndex <= s.sameTotalHalf;
            s.sameArcDirection = i;
            //s.sameArcDirection = s.sameLowerHalf ? 0 : 1;
            s.sameIndexCorrected = s.sameLowerHalf
              ? s.sameIndex
              : s.sameIndex - Math.ceil(s.sameTotalHalf);
          });
        });
        var maxSame = _l.chain(links)
          .sortBy(function (x) {
            return x.sameTotal;
          })
          .last()
          .value().sameTotal;

        _l.each(links, function (link) {
          link.maxSameHalf = Math.round(maxSame / 2);
        });
      }
      // 更新连线 links
      var link = _this.linkGroup
        .selectAll(".line >path")
        .data(links, function (d) {
          return d.uuid;
        });
      link.exit().remove();
      var linkEnter = _this.drawlink(link);
      link = linkEnter.merge(link);
      // 更新连线文字
      var linktext = _this.linktextGroup
        .selectAll("text")
        .data(links, function (d) {
          return d.uuid;
        });
      linktext.exit().remove();
      var linktextEnter = _this.drawlinktext(linktext);
      linktext = linktextEnter.merge(linktext).text(function (d) {
        return d.lk.name;
      });

      // 更新节点按钮组
      d3.selectAll(".nodebutton  >g").remove();
      var nodebutton = _this.nodebuttonGroup
        .selectAll(".nodebutton")
        .data(nodes, function (d) {
          return d;
        });
      nodebutton.exit().remove();
      var nodebuttonEnter = _this.drawnodebutton(nodebutton);
      nodebutton = nodebuttonEnter.merge(nodebutton);
      // 更新节点
      var node = _this.nodeGroup.selectAll("circle").data(nodes, function (d) {
        return d;
      });
      node.exit().remove();
      var nodeEnter = _this.drawnode(node);
      node = nodeEnter.merge(node).text(function (d) {
        return d.name;
      });
      node
        .append("title") // 为每个节点设置title
        .text(function (d) {
          return d.name;
        });
      // 更新节点文字
      var nodetext = _this.nodetextGroup
        .selectAll("text")
        .data(nodes, function (d) {
          return d.uuid;
        });
      nodetext.exit().remove();
      var nodetextEnter = _this.drawnodetext(nodetext);
      nodetext = nodetextEnter.merge(nodetext).text(function (d) {
        var length = d.name.length;
        if (length > 9) {
          var s = d.name.slice(0, 4) + "...";
          return s;
        }
        return d.name;
      });
      // 更新节点标识
      var nodesymbol = _this.nodesymbolGroup
        .selectAll("path")
        .data(nodes, function (d) {
          return d.uuid;
        });
      nodesymbol.exit().remove();
      var nodesymbolEnter = _this.drawnodesymbol(nodesymbol);
      nodesymbol = nodesymbolEnter.merge(nodesymbol);
      nodesymbol.attr("fill", "#e15500");
      nodesymbol.attr("display", function (d) {
        if (typeof d.hasfile != "undefined" && d.hasfile > 0) {
          return "block";
        }
        return "none";
      });
      _this.simulation.nodes(nodes).on("tick", ticked);
      _this.simulation.force("link").links(links);
      _this.simulation.alphaTarget(0).restart();
      _this.simulation.alphaDecay(0.001);
      function linkArc(d) {
        var dx = d.target.x - d.source.x,
          dy = d.target.y - d.source.y,
          dr = Math.sqrt(dx * dx + dy * dy),
          unevenCorrection = d.sameUneven ? 0 : 0.5;
        var curvature = 2,
          arc =
            (1.0 / curvature) *
            ((dr * d.maxSameHalf) / (d.sameIndexCorrected - unevenCorrection));
        if (d.sameMiddleLink) {
          arc = 0;
        }
        var dd =
          "M" +
          d.source.x +
          "," +
          d.source.y +
          "A" +
          arc +
          "," +
          arc +
          " 0 0," +
          d.sameArcDirection +
          " " +
          d.target.x +
          "," +
          d.target.y;
        return dd;
      }

      function ticked() {
        // 更新连线坐标
        /*link.attr("x1", function (d) {
						return d.source.x;
					   })
						.attr("y1", function (d) {
							return d.source.y;
						})
						.attr("x2", function (d) {
							return d.target.x;
						})
						.attr("y2", function (d) {
							return d.target.y;
						});*/
        link.attr("d", linkArc);
        // 刷新连接线上的文字位置
        // linktext
        //   .attr("x", function (d) {
        //     console.log(d)
        //     return (d.source.x + d.target.x) / 2;
        //   })
        //   .attr("y", function (d) {
        //     return (d.source.y + d.target.y) / 2;
        //   });
        // 更新节点坐标
        node
          .attr("cx", function (d) {
            return d.x;
          })
          .attr("cy", function (d) {
            return d.y;
          });
        // 更新节点操作按钮组坐标
        nodebutton
          .attr("cx", function (d) {
            return d.x;
          })
          .attr("cy", function (d) {
            return d.y;
          });
        nodebutton.attr("transform", function (d) {
          return "translate(" + d.x + "," + d.y + ") scale(1)";
        });

        // 更新文字坐标
        nodetext
          .attr("x", function (d) {
            return d.x;
          })
          .attr("y", function (d) {
            return d.y;
          });
        // 更新回形针坐标
        nodesymbol.attr("transform", function (d) {
          return (
            "translate(" + (d.x + 8) + "," + (d.y - 30) + ") scale(0.015,0.015)"
          );
        });
      }
      // 鼠标滚轮缩放
      //_this.svg.call(d3.zoom().transform, d3.zoomIdentity);//缩放至初始倍数
      _this.svg.call(
        d3.zoom().on("zoom", function () {
          d3.selectAll(".node").attr("transform", d3.event.transform);
          d3.selectAll(".nodetext").attr("transform", d3.event.transform);
          d3.selectAll(".line").attr("transform", d3.event.transform);
          d3.selectAll(".linetext").attr("transform", d3.event.transform);
          d3.selectAll(".nodesymbol").attr("transform", d3.event.transform);
          d3.selectAll(".nodebutton").attr("transform", d3.event.transform);
          //_this.svg.selectAll("g").attr("transform", d3.event.transform);
        })
      );
      _this.svg.on("dblclick.zoom", null); // 静止双击缩放
      //按钮组事件
      _this.svg.selectAll(".buttongroup").on("click", function (d, i) {
        if (_this.nodebuttonAction) {
          switch (_this.nodebuttonAction) {
            case "info":
              break;
            case "more":
              _this.getmorenode(d);
              break;
          }
        }
      });
      //按钮组事件绑定
      _this.svg.selectAll(".action_0").on("click", function (d) {
        _this.nodebuttonAction = "more";
      });
      _this.svg.selectAll(".action_1").on("click", function (d) {
        _this.nodebuttonAction = "info";
      });
    },
    resetsubmit() {
      this.isaddnode = false;
      this.isedit = false;
      this.resetentity();
      this.fieldDataList = [];
      this.dataconfigactive = "";
      this.isbatchcreate = false;
      this.selectnodeid = 0;
    },
    addmaker() {
      var arrowMarker = this.svg
        .append("marker")
        .attr("id", "arrow")
        .attr("markerUnits", "strokeWidth")
        .attr("markerWidth", "20") //
        .attr("markerHeight", "20")
        .attr("viewBox", "0 -5 10 10")
        .attr("refX", "22") // 13
        .attr("refY", "0")
        .attr("orient", "auto");
      var arrow_path = "M0,-5L10,0L0,5"; // 定义箭头形状
      arrowMarker.append("path").attr("d", arrow_path).attr("fill", "#fce6d4");
    },
    resetentity() {
      this.graphEntity = {
        uuid: 0,
        color: "ff4500",
        name: "",
        r: 30,
        x: "",
        y: "",
      };
    },
    addnodebutton() {
      var _this = this;
      var nodebutton = this.svg
        .append("defs")
        .append("g")
        .attr("id", "out_circle");
      var database = [1, 1];
      var pie = d3.pie();
      var piedata = pie(database);
      var buttonEnter = nodebutton
        .selectAll(".buttongroup")
        .data(piedata)
        .enter()
        .append("g")
        .attr("class", function (d, i) {
          return "action_" + i;
        });
      var arc = d3.arc().innerRadius(30).outerRadius(60);
      buttonEnter
        .append("path")
        .attr("d", function (d) {
          return arc(d);
        })
        .attr("fill", "#D2D5DA")
        .style("opacity", 0.6)
        .attr("stroke", "#f0f0f4")
        .attr("stroke-width", 2);
      buttonEnter
        .append("text")
        .attr("transform", function (d, i) {
          return "translate(" + arc.centroid(d) + ")";
        })
        .attr("text-anchor", "middle")
        .text(function (d, i) {
          var zi = new Array();
          zi[0] = systemLanguage['txt_an'];
          zi[1] = systemLanguage['txt_detail'];
          // zi[2] = "追加";
          // zi[3] = "连线";
          // zi[4] = "删除";
          return zi[i];
        })
        .attr("font-size", 10);
    },
    dragstarted(d) {
      if (!d3.event.active) this.simulation.alphaTarget(0.3).restart();
      d.fx = d.x;
      d.fy = d.y;
      d.fixed = true;
    },
    dragged(d) {
      d.fx = d3.event.x;
      d.fy = d3.event.y;
    },
    dragended(d) {
      if (!d3.event.active) this.simulation.alphaTarget(0);
    },
    drawnode(node) {
      var _this = this;
      var nodeEnter = node.enter().append("circle");
      nodeEnter.attr("r", function (d) {
        if (typeof d.r != "undefined" && d.r != "") {
          return d.r;
        }
        return 20;
      });
      nodeEnter.attr("fill", function (d) {
        if (typeof d.color != "undefined" && d.color != "") {
          return d.color;
        }
        return "#57C7E3";
      });
      nodeEnter.style("opacity", 1);
      nodeEnter.style("stroke", function (d) {
        if (typeof d.color != "undefined" && d.color != "") {
          return d.color;
        }
        return "#23b3d7";
      });
      nodeEnter.style("stroke-opacity", 0.6);
      nodeEnter
        .append("title") // 为每个节点设置title
        .text(function (d) {
          return d.name;
        });
      nodeEnter.on("mouseover", function (d, i) {
        _this.timer = setTimeout(function () {
          d3.select("#richContainer")
            .style("position", "absolute")
            .style("left", d.x + "px")
            .style("top", d.y + "px")
            .style("display", "block");
          _this.editorcontent = "";
          _this.showImageList = [];
          _this.getNodeDetail(d.uuid);
        }, 3000);
      });
      nodeEnter.on("mouseout", function (d, i) {
        clearTimeout(_this.timer);
      });
      nodeEnter.on("dblclick", function (d) {});
      nodeEnter.on("mouseenter", function (d) {
        var aa = d3.select(this)._groups[0][0];
        if (aa.classList.contains("selected")) return;
        d3.select(this).style("stroke-width", "6");
      });
      nodeEnter.on("mouseleave", function (d) {
        var aa = d3.select(this)._groups[0][0];
        if (aa.classList.contains("selected")) return;
        d3.select(this).style("stroke-width", "2");
      });
      nodeEnter.on("click", function (d, i) {
        var out_buttongroup_id = ".out_buttongroup_" + i;
        _this.svg.selectAll("use").classed("circle_opreate", true);
        _this.svg
          .selectAll(out_buttongroup_id)
          .classed("circle_opreate", false);
        _this.graphEntity = d;
        _this.selectnodeid = d.uuid;
        _this.selectnodename = d.name;

        // 更新工具栏节点信息
        _this.getcurrentnodeinfo(d);

        // 添加连线状态
        if (_this.isaddlink) {
          _this.selecttargetnodeid = d.uuid;
          if (
            _this.selectsourcenodeid == _this.selecttargetnodeid ||
            _this.selectsourcenodeid == 0 ||
            _this.selecttargetnodeid == 0
          )
            return;
          _this.createlink(
            _this.selectsourcenodeid,
            _this.selecttargetnodeid,
            "RE"
          );
          _this.selectsourcenodeid = 0;
          _this.selecttargetnodeid = 0;
          d.fixed = false;
          d3.event.stopPropagation();
        }
      });
      nodeEnter.call(
        d3
          .drag()
          .on("start", _this.dragstarted)
          .on("drag", _this.dragged)
          .on("end", _this.dragended)
      );
      return nodeEnter;
    },
    getcurrentnodeinfo() {},
    drawnodetext(nodetext) {
      var _this = this;
      var nodetextenter = nodetext
        .enter()
        .append("text")
        .style("fill", "#fff")
        .attr("dy", 4)
        .attr("font-family", "微软雅黑")
        .attr("font-size", "10px")
        .attr("text-anchor", "middle");

      nodetextenter.on("mouseover", function (d, i) {
        _this.timer = setTimeout(function () {
          d3.select("#richContainer")
            .style("position", "absolute")
            .style("left", d.x + "px")
            .style("top", d.y + "px")
            .style("display", "block");
          _this.editorcontent = "";
          _this.showImageList = [];
          _this.getNodeDetail(d.uuid);
        }, 3000);
      });

      nodetextenter.on("dblclick", function (d) {});
      nodetextenter.on("click", function (d) {
        $("#link_menubar").hide(); // 隐藏空白处右键菜单
        _this.graphEntity = d;
        _this.selectnodeid = d.uuid;
        // 更新工具栏节点信息
        _this.getcurrentnodeinfo(d);
        // 添加连线状态
        if (_this.isaddlink) {
          _this.selecttargetnodeid = d.uuid;
          _this.selectsourcenodeid = 0;
          _this.selecttargetnodeid = 0;
          d.fixed = false;
          d3.event.stopPropagation();
        }
      });

      return nodetextenter;
    },
    drawnodesymbol(nodesymbol) {
      var _this = this;
      var symnol_path =
        "M566.92736 550.580907c30.907733-34.655573 25.862827-82.445653 25.862827-104.239787 0-108.086613-87.620267-195.805867-195.577173-195.805867-49.015467 0-93.310293 18.752853-127.68256 48.564907l-0.518827-0.484693-4.980053 4.97664c-1.744213 1.64864-3.91168 2.942293-5.59104 4.72064l0.515413 0.484693-134.69696 133.727573L216.439467 534.8352l0 0 137.478827-136.31488c11.605333-10.410667 26.514773-17.298773 43.165013-17.298773 36.051627 0 65.184427 29.197653 65.184427 65.24928 0 14.032213-5.33504 26.125653-12.73856 36.829867l-131.754667 132.594347 0.515413 0.518827c-10.31168 11.578027-17.07008 26.381653-17.07008 43.066027 0 36.082347 29.16352 65.245867 65.184427 65.245867 16.684373 0 31.460693-6.724267 43.035307-17.07008l0.515413 0.512M1010.336427 343.49056c0-180.25472-145.882453-326.331733-325.911893-326.331733-80.704853 0-153.77408 30.22848-210.418347 79.0528l0.484693 0.64512c-12.352853 11.834027-20.241067 28.388693-20.241067 46.916267 0 36.051627 29.16352 65.245867 65.211733 65.245867 15.909547 0 29.876907-6.36928 41.192107-15.844693l0.38912 0.259413c33.624747-28.030293 76.301653-45.58848 123.511467-45.58848 107.99104 0 195.549867 87.6544 195.549867 195.744427 0 59.815253-27.357867 112.71168-69.51936 148.503893l0 0-319.25248 317.928107 0 0c-35.826347 42.2912-88.654507 69.710507-148.340053 69.710507-107.956907 0-195.549867-87.68512-195.549867-195.805867 0-59.753813 27.385173-112.646827 69.515947-148.43904l-92.18048-92.310187c-65.69984 59.559253-107.700907 144.913067-107.700907 240.749227 0 180.28544 145.885867 326.301013 325.915307 326.301013 95.218347 0 180.02944-41.642667 239.581867-106.827093l0.13312 0.129707 321.061547-319.962453-0.126293-0.13312C968.69376 523.615573 1010.336427 438.71232 1010.336427 343.49056L1010.336427 343.49056 1010.336427 343.49056zM1010.336427 343.49056"; // 定义回形针形状
      var nodesymbolEnter = nodesymbol
        .enter()
        .append("path")
        .attr("d", symnol_path);
      nodesymbolEnter.call(
        d3
          .drag()
          .on("start", _this.dragstarted)
          .on("drag", _this.dragged)
          .on("end", _this.dragended)
      );
      return nodesymbolEnter;
    },
    drawnodebutton(nodebutton) {
      var _this = this;
      var nodebuttonEnter = nodebutton
        .enter()
        .append("g")
        .append("use") //  为每个节点组添加一个 use 子元素
        .attr("r", function (d) {
          return d.r;
        })
        .attr("xlink:href", "#out_circle") //  指定 use 引用的内容
        .attr("class", function (d, i) {
          return "buttongroup out_buttongroup_" + i;
        })
        .classed("circle_opreate", true);

      return nodebuttonEnter;
    },
    drawlink(link) {
      var _this = this;
      var linkEnter = link
        .enter()
        .append("path")
        .attr("stroke-width", 1)
        .attr("stroke", "#A5ABB6")
        .attr("fill", "none")
        .attr("id", function (d) {
          return (
            "invis_" + d.lk.sourceid + "-" + d.lk.name + "-" + d.lk.targetid
          );
        });
      // .attr("marker-end", "url(#arrow)"); // 箭头
      linkEnter.on("dblclick", function (d) {
        _this.selectnodeid = d.lk.uuid;
      });
      linkEnter.on("contextmenu", function (d) {
        var cc = $(this).offset();
        app.selectnodeid = d.lk.uuid;
        app.selectlinkname = d.lk.name;
        d3.select("#link_menubar")
          .style("position", "absolute")
          .style("left", cc.left + "px")
          .style("top", cc.top + "px")
          .style("display", "block");
        d3.event.preventDefault(); // 禁止系统默认右键
        d3.event.stopPropagation(); // 禁止空白处右键
      });
      linkEnter.on("mouseenter", function (d) {
        d3.select(this)
          .style("stroke-width", "6")
          .attr("stroke", "rgb(106, 198, 255)");
        // .attr("marker-end", "url(#arrow2)");
      });
      linkEnter.on("mouseleave", function (d) {
        d3.select(this).style("stroke-width", "1").attr("stroke", "#A5ABB6");
        // .attr("marker-end", "url(#arrow)");
      });
      return linkEnter;
    },
    drawlinktext(link) {
      var linktextEnter = link
        .enter()
        .append("text")
        .style("fill", "#A5ABB6")
        .append("textPath")
        .attr("font-size", "9px")
        .attr("startOffset", "50%")
        .attr("text-anchor", "middle")
        .attr("xlink:href", function (d) {
          return (
            "#invis_" + d.lk.sourceid + "-" + d.lk.name + "-" + d.lk.targetid
          );
        })
        .text(function (d) {
          if (d.lk.name != "") {
            return d.lk.name;
          }
        });

      linktextEnter.on("mouseover", function (d) {
        app.selectnodeid = d.lk.uuid;
        app.selectlinkname = d.lk.name;
        var cc = $(this).offset();
        d3.select("#link_menubar")
          .style("position", "absolute")
          .style("left", cc.left + "px")
          .style("top", cc.top + "px")
          .style("display", "block");
      });

      return linktextEnter;
    },
    formatData(data, parentid) {
      let datab = {
        nodes: [],
        links: [],
      };
      function alipayobjects(data, parentid) {
        if (data && data.length) {
          data.forEach((element) => {
            datab.nodes.push({
              id: element.id,
              uuid: element.id,
              oid: element.oid,
              name: element.name,
              type: "node",
              filter: [],
            });
            if (parentid) {
              datab.links.push({
                sourceid: parentid,
                targetid: element.id,
                oid: element.oid,
                uuid: "link" + element.id,
                name: element?.relation?.type,
                filter: [],
              });
            }

            if (element.children) {
              alipayobjects(element.children, element.id);
            }
          });
        }
      }
      alipayobjects(data, parentid);
      return datab;
    },
  },
  created() {},
  mounted() {
    setTimeout(() => {
      var token = $("meta[name='_csrf']").attr("content");
      var header = $("meta[name='_csrf_header']").attr("content");
      var str = '{ "' + header + '": "' + token + '"}';
      this.headers = eval("(" + str + ")");
      this.initgraph();
    }, 1500);
  },
};
</script>

<style lang="less">
.object-details-atlas {
  display: flex;
  flex-direction: column;
  height: 100%;
  > header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      .ant-input-search {
        width: 200px;
      }
      .ant-radio-group {
        margin-left: 10px;
      }
    }
  }
  > main {
    margin-top: 15px;
    height: 20px;
    flex-grow: 1;
    #graphcontainer {
      height: 100%;
    }
  }
  .g6-tooltip {
    pointer-events: none;
    margin-top: 120px;
    // margin-left: 30px;
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    font-size: 12px;
    color: #545454;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 10px 8px;
    box-shadow: rgb(174, 174, 174) 0px 0px 10px;
  }
  foreignObject {
    pointer-events: none;
    div {
      display: flex;
      flex-direction: column;
      align-items: center;
      > i {
        font-size: 20px;
        height: 12px;
        width: 12px;
        svg {
          width: 12px;
          height: 120px;
        }
      }
      > span {
        margin-top: -15px;
        overflow: hidden;
        width: 100%;
        white-space: nowrap;
        text-overflow: ellipsis;
        text-align: center;
      }
    }
  }

  .pl-20 {
    padding-left: 20px;
  }

  text {
    cursor: pointer;
    max-width: 25px;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
  }
  .nodetext {
    pointer-events: none;
  }
  circle {
    cursor: pointer;
  }

  #graphcontainerdiv {
    background: #fff;
  }

  .circle_opreate {
    display: none;
  }
}
</style>
