<template>
  <div class="object-details-atlas"
       id="objectDetailsAtlas">
    <header>
      <div class="left">
        <a-input-search placeholder="请输入搜索关键字"
                        v-show="false"
                        @search="onSearch" />
      </div>
      <div class="right">
        <a-popover placement="bottomRight">
          <template slot="content">
            <a-checkbox-group v-model.trim="showType"
                              @change="getatlasData">
              <a-row v-for="item in plainOptions"
                     :key="item.oid">
                <a-col :span="24">
                  <a-checkbox :value="item.oid">
                    {{ item.relationDisplayName }}
                  </a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </template>
          <a-button>
            <i class="jwi-iconSetting"
               style="margin-right: 8px"></i> 显示内容
          </a-button>
        </a-popover>
      </div>
    </header>
    <main>
      <div id="echartAtlas"></div>
    </main>
  </div>
</template>

<script>
import { getatlasData, generateUUID, getShowData } from "../apis";
import G6 from "@antv/g6";
export default {
  inject: ["detailsData"],
  computed: {
    objectDetailsData() {
      return this.detailsData();
    },
  },
  data() {
    return {
      value: "a",
      showType: [],
      graph: undefined,
      data: {},
      plainOptions: [],
    };
  },
  methods: {
    onSearch(e) {
      const graph = this.graph;
      // const nodes = graph.findAll("node", (node) => {
      //   return node.get("model").name.includes(e);
      // });
      graph.setAutoPaint(false);
      graph.getNodes().forEach((node) => {
        graph.clearItemStates(node);
        if (node.get("model").name.includes(e)) {
          graph.setItemState(node.get("model").id, "dark", false);
          graph.setItemState(node.get("model").id, "highlight", true);
        } else {
          graph.setItemState(node.get("model").id, "dark", true);
        }
      });
      // nodes.forEach((node) => {
      //   graph.setItemState(node, "dark", false);
      //   graph.setItemState(node, "highlight", true);
      // });
    },
    onChange() {},
    initG6() {
      const width = document.getElementById("echartAtlas").scrollWidth - 30;
      const height =
        document.getElementById("echartAtlas").scrollHeight - 30 || 500;
      const graph = (this.graph = new G6.TreeGraph({
        container: "echartAtlas",
        renderer: "svg",
        width,
        height,
        modes: {
          default: [
            {
              type: "collapse-expand",
              onChange: function onChange(item, collapsed) {
                const data = item.get("model");
                data.collapsed = collapsed;
                return true;
              },
            },
            // {
            //   type: "tooltip",
            //   formatText: function formatText(model) {
            //     return (
            //       model.number + ", " + model.name + ", " + model.displayVersion
            //     );
            //   },
            // },
            {
              type: "edge-tooltip",
              formatText: function formatText(model, e) {
                return model.type || "";
              },
            },
            "drag-canvas",
            "zoom-canvas",
            "drag-nodes",
          ],
        },
        nodeStateStyles: {
          highlight: {
            opacity: 1,
            stroke: "#096dd9",
          },
          dark: {
            opacity: 0.2,
            stroke: "#ddd",
            // fill: "red",
          },
        },
        edgeStateStyles: {
          highlight: {
            opacity: 0.4,
            stroke: "#1E202A",
            lineWidth: 8,
            lineAppendWidth: 300,
          },
          dark: {
            stroke: "#1E202A",
            lineWidth: 4,
            lineAppendWidth: 300,
            shadowColor: "green",
            // fill: "red",
          },
        },
        defaultEdge: {
          style: {
            opacity: 0.2,
            stroke: "#1E202A",
            lineWidth: 4,
            lineAppendWidth: 300,
          },
          labelCfg: {
            autoRotate: true,
            style: {
              stroke: "#fff",
              size:15,
              lineWidth: 5,
            },
          },
        },
        defaultNode: {
          size: 26,
          // type: "dom-node-1",
          style: {
            fill: "#A4C9FC",
            stroke: "#ddd",
          },
        },
        layout: {
          type: "compactBox",
          direction: "RL",
          //  unitRadius: 280,
          //  rankSep: 1500,
          //  subTreeSep: 500,
          nodeSize: 120,
          preventOverlap: true,
          strictRadial: true,
          // linkDistance: 100,
          nodeSpacing: 150,
          getId: function getId(d) {
            return d.id;
          },
          getHeight: () => {
            return 26;
          },
          getWidth: () => {
            return 26;
          },
          getVGap: () => {
            return 20;
          },
          getHGap: () => {
            return 100;
          },
          radial: true,
        },
      }));

      graph.node(function (node) {
        return {
          size: 60,
          style: {},
          type: "rectNode",
          label: node.name,
        };
      });
      graph.edge(function (node) {
        node.label = node.target._cfg.model.relation.type;
        let relations = node.target._cfg.model.relations;
        if (relations) {
          let data = relations.filter((p) => p.toOid === sourceId);
          if (data && data.length) {
            return {
              ...data[0],
              label: data[0].type,
            };
          } else {
            return "";
          }
        } else {
          return "";
        }
      });

      graph.on("node:mouseenter", function (e) {
        var item = e.item;
        graph.setAutoPaint(false);
        graph.getNodes().forEach(function (node) {
          graph.clearItemStates(node);
          graph.setItemState(node, "dark", true);
        });
        graph.setItemState(item, "dark", false);
        graph.setItemState(item, "highlight", true);
        // graph.getEdges().forEach(function (edge) {
        //   if (edge.getSource() === item) {
        //     graph.setItemState(edge.getTarget(), "dark", false);
        //     graph.setItemState(edge.getTarget(), "highlight", true);
        //     graph.setItemState(edge, "highlight", true);
        //     edge.toFront();
        //   } else if (edge.getTarget() === item) {
        //     graph.setItemState(edge.getSource(), "dark", false);
        //     graph.setItemState(edge.getSource(), "highlight", true);
        //     graph.setItemState(edge, "highlight", true);
        //     edge.toFront();
        //   } else {
        //     graph.setItemState(edge, "highlight", false);
        //   }
        // });
        graph.paint();
        graph.setAutoPaint(true);
      });
      graph.on("node:mouseleave", clearAllStats);
      graph.on("canvas:mouseleave", clearAllStats);
      graph.on("node:click", (evt) => {
        var item = evt.item;
        var nodeId = item.get("id");
        var model = item.getModel();
        var children = model.children;
        if (!children || children.length === 0) {
          this.getatlasData({ nodeId, model });
        }
      });
      function clearAllStats() {
        graph.setAutoPaint(false);
        graph.getNodes().forEach(function (node) {
          graph.clearItemStates(node);
        });
        graph.getEdges().forEach(function (edge) {
          graph.clearItemStates(edge);
        });
        graph.paint();
        graph.setAutoPaint(true);
      }
    },
    getatlasData({ nodeId, model }) {
      let params = {
        // oid: "f25b67a5-ad18-42cc-9ffb-3a476f51b4b5", //this.objectDetailsData.oid,
        oid: model ? model.oid : this.objectDetailsData.oid,
        relatedInfos: [],
      };
      this.plainOptions.forEach((p) => {
        if (this.showType.includes(p.oid)) {
          params.relatedInfos.push({
            mainObjectOid: model ? model.oid : this.objectDetailsData.oid,
            forward: p.forward,
            mainObjectType: p.mainObjectType,
            relationConstraint: p.relationConstraint,
            relationshipName: p.relationshipName,
            searchKey: p.searchKey,
            slaveObjectType: p.slaveObjectType,
            mainObjectClassName: p.mainObjectClassName,
            slaveObjectClassName: p.slaveObjectClassName
          });
        }
      });
      getatlasData(params.relatedInfos)
        .then((res) => {
          if (nodeId) {
            generateUUID(res);
            var parentData = this.graph.findDataById(nodeId);
            if (!parentData.children) {
              parentData.children = [];
            }
            parentData.children = res;
            // 如果childData是一个数组，则直接赋值给parentData.children
            // 如果是一个对象，则使用parentData.children.push(obj)
          } else {
            this.objectDetailsData.children = res;
            generateUUID(this.objectDetailsData);
            if (res && res.length) {
              this.data = this.objectDetailsData;
            } else {
              this.data = this.objectDetailsData;
            }
          }
        })
        .catch((err) => {
          this.$error(err.msg || err.message);
          this.data = undefined;
        })
        .finally(() => {
          const graph = this.graph;
          if (nodeId) {
            graph.changeData();
          } else {
            if (this.data) {
              graph.data(this.data);
              graph.render();
              graph.fitView();
              if (!this.data.children || !this.data.children.length) {
                const width =
                  document.getElementById("echartAtlas").scrollWidth - 15;
                const height =
                  document.getElementById("echartAtlas").scrollHeight || 500;
                graph.zoom(0.2, { x: width / 2, y: height / 2 });
              }
            } else {
              graph.data();
            }
          }
          const autoPaint = graph.get("autoPaint");
          graph.paint();
          graph.setAutoPaint(autoPaint);
        });
    },
    init() {
      let _this = this;
      if (this.graph) {
        this.graph.clear();
        this.getShowData();
      } else {
        let initG6Node = setTimeout(() => {
          if (_this.graph) {
            this.graph.clear();
            clearTimeout(initG6Node);
            initG6Node = null;
            _this.getShowData();
          }
        }, 1000);
      }
    },
    getShowData() {
      getShowData({
        appliedType: this.objectDetailsData.modelDefinition + "_Relation_Graph",
        mainObjectType: this.objectDetailsData.modelDefinition,
      })
        .then((res) => {
          this.plainOptions = res;
          this.showType = res.map((p) => p.oid);
        })
        .catch((err) => {})
        .finally(() => {
          this.getatlasData({ nodeId: false, model: false });
        });
    },
  },
  created() {
    G6.registerNode("rectNode", {
      draw: (cfg, group) => {
        let icon = "";
        if (cfg.modelIcon && cfg.modelIcon.includes("#")) {
          icon = `<i
                  class="jw-icon"
                  ><svg aria-hidden="true" class="jw-icon-svg">
                    <use href="${cfg.modelIcon}"></use></svg
                ></i>`;
        } else {
          icon = `<i class="${cfg.modelIcon}"></i>`;
        }
        //最外面的那层
        const shape = group.addShape("circle", {
          attrs: {
            x: 0,
            y: 0,
            r: 60,
            fill: "#A4C9FC",
            stroke: "#ddd",
          },
        });
        group.addShape("dom", {
          attrs: {
            y: -50,
            x: -45,
            width: 90,
            height: 100,
            fill: "#000",
            html: `<div>` + icon + `<span>${cfg.name}</span>` + `</div>`,
          },
          capture: false,
        });
        return shape;
      },
      setState(name, value, item) {
        const group = item.getContainer();
        const shape = group.get("children")[0]; // 顺序根据 draw 时确定
        if (name === "highlight") {
          if (value) {
            shape.attr("opacity", 1);
            shape.attr("stroke", "#096dd9");
          } else {
            shape.attr("opacity", 1);
            shape.attr("fill", "#A4C9FC");
            shape.attr("stroke", "#ddd");
          }
        } else if (name === "dark") {
          if (value) {
            shape.attr("opacity", 0.2);
            shape.attr("stroke", "#ddd");
          } else {
            shape.attr("opacity", 1);
            shape.attr("fill", "#A4C9FC");
            shape.attr("stroke", "#ddd");
          }
        } else {
          shape.attr("fill", "#A4C9FC");
          shape.attr("stroke", "#ddd");
        }
      },
    });
  },
  mounted() {
    setTimeout(() => {
      this.initG6();
    }, 1500);
  },
};
</script>

<style lang="less">
.object-details-atlas {
  display: flex;
  flex-direction: column;
  height: 100%;
  > header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      .ant-input-search {
        width: 200px;
      }
      .ant-radio-group {
        margin-left: 10px;
      }
    }
  }
  > main {
    margin-top: 15px;
    height: 20px;
    flex-grow: 1;
    #echartAtlas {
      height: 100%;
    }
  }
  .g6-tooltip {
    pointer-events: none;
    margin-top: 120px;
    // margin-left: 30px;
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    font-size: 12px;
    color: #545454;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 10px 8px;
    box-shadow: rgb(174, 174, 174) 0px 0px 10px;
  }
  foreignObject {
    pointer-events: none;
    div {
      display: flex;
      flex-direction: column;
      align-items: center;
      > i {
        font-size: 50px;
        height: 75px;
        width: 50px;
        svg {
          width: 50px;
          height: 50px;
        }
      }
      > span {
        margin-top: -15px;
        overflow: hidden;
        width: 100%;
        white-space: nowrap;
        text-overflow: ellipsis;
        text-align: center;
      }
    }
  }
}
</style>
