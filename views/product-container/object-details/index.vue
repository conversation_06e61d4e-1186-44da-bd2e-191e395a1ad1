<template>
  <div class="object-details">
    <Toolbar ref="toolbar" :latestVersion.sync="latestVersion">
    </Toolbar>
    <div class="main-tab">
      <a-tabs :default-active-key="tabName" v-model.trim="tabName" @change="tabChange" class="object-details-tab"
        id="objectDetailsGraph">
        <a-tab-pane key="info" :tab="$t('detailed_info')" forceRender v-if="objectDetailsData.masterType === 'Part' ||
          objectDetailsData.masterType === 'Document' ||
          objectDetailsData.masterType === 'ECAD' ||
          objectDetailsData.masterType === 'DocumentTemplateMaster'
          ">
          <Info v-if="showinfopage" :objectDetailsData="objectDetailsData" ref="info" @findInUserView="init">
          </Info>
        </a-tab-pane>
        <a-tab-pane key="info" :tab="$t('detailed_info')" forceRender v-if="objectDetailsData.masterType === 'MCAD'">
          <Cadstructure :objectDetailsData="objectDetailsData" ref="info">
          </Cadstructure>
        </a-tab-pane>

        <a-tab-pane key="filePreview" :tab="$t('txt_file_preview')" v-if="objectDetailsData.modelDefinition === 'MentorProject'">
          <zipPreview :objectDetailsData="objectDetailsData"></zipPreview>
        </a-tab-pane>

        <!-- 有效性管理 -->
        <a-tab-pane key="effectivity" v-if="objectDetailsData.genericType === 'variable'"
          :tab="$t('txt_effectivity_tab_title')">
          <effectivity :recorddata="objectDetailsData" />
        </a-tab-pane>
        <a-tab-pane key="structure" :tab="$t('txt_structure')" forceRender
          v-if="objectDetailsData.masterType === 'Part' && showStructure">
          <Structure ref="structure" :objectDetailsData="objectDetailsData" @init="init">
          </Structure>
        </a-tab-pane>
        <a-tab-pane key="atlas" :tab="$t('txt_relationship_graph')" forceRender
          v-if="objectDetailsData.masterType === 'Part'">
          <Atlas ref="atlas">
          </Atlas>
        </a-tab-pane>
        <a-tab-pane key="relevant" :tab="$t('txt_related_objects')" forceRender>
          <RelatedObjects ref="relevant" :objectDetailsData="objectDetailsData" :latestVersion="latestVersion">
          </RelatedObjects>
        </a-tab-pane>

        <a-tab-pane key="designDocument" :tab="$t('设计文件')" v-if="!showStructure">
          <DesignDocument ref="DesignDocument" :objectDetailsData="objectDetailsData">
          </DesignDocument>
        </a-tab-pane>

        <a-tab-pane key="supplier" :tab="$t('txt_supplier_association')" v-if="showSupplier">
          <SupplierAssociation :objectDetailsData="objectDetailsData"></SupplierAssociation>
        </a-tab-pane>

        <a-tab-pane key="usage" :tab="$t('txt_where_use')" forceRender v-if="objectDetailsData.masterType !== 'Document' &&
          objectDetailsData.masterType !== 'ECAD' &&
          objectDetailsData.masterType !== 'DocumentTemplateMaster'">
          <Usage ref="usage" forceRender>
          </Usage>
        </a-tab-pane>
        <a-tab-pane key="processRecord" :tab="$t('txt_process_records')">
          <workflow-page :pageCode="'processRecord'" :objectDetailsData="objectDetailsData"
            :listApi="listApi"></workflow-page>
        </a-tab-pane>
        <a-tab-pane key="changeRecord" :tab="$t('txt_change_record')"
          v-if="objectDetailsData.masterType !== 'DocumentTemplateMaster'">
          <change-records-page></change-records-page>
        </a-tab-pane>
        <a-tab-pane key="record" :tab="$t('txt_history')">
          <HistoricalRecords ref="historicalRecords" @init="init">
          </HistoricalRecords>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script>
import Usage from "./usage/index.vue";
import Info from "./info.vue";
import DesignDocument from "./design-document";
import RelatedObjects from "components/relatedObjects";
import HistoricalRecords from "./historicalRecords";
import Structure from "./structure";
import Cadstructure from "./structure/cad-structure.vue";
import Atlas from "./atlas/index.vue";
import Toolbar from "./toolbar/index.vue";
import workflowPage from "/views/product-content/process-manage/workflow-page";
import SupplierAssociation from "/views/supplier-association"
import changeRecordsPage from "./changeRecords/index";
import { fetchGroupListData } from "./apis";
import { findDetail } from "apis/baseapi";
import effectivity from "./effectivity";
import zipPreview from "./zip-preview";

export default {
  name: "ObjectDetails",
  data() {
    return {
      latestVersion: "",
      tabName: "info",
      provideData: this.$route.query,
      form: undefined,
      jurisdiction: false,
      listApi: `${Jw.gateway}/${Jw.workflowMicroServer
        }/process/order/fuzzyPage/byBiz`,
      showinfopage: true,
      searchData: '部件页签显示',
      dataList: [],
      showStructure: true,
      showSupplier: true,
    };
  },
  components: {
    Info,
    Structure,
    Usage,
    Atlas,
    Toolbar,
    RelatedObjects,
    HistoricalRecords,
    // jwHeader,
    Cadstructure,
    workflowPage,
    changeRecordsPage,
    effectivity,
    SupplierAssociation,
    DesignDocument,
    zipPreview
  },
  computed: {
    objectDetailsData() {
      this.showinfopage = false;
      setTimeout(() => {
        this.showinfopage = true;
      }, 300);
      return this.provideData;
    }
  },
  inject: ["setBreadcrumb", "addBreadcrumb"],
  provide() {
    return {
      detailsData: () => this.provideData,
      beforeCheckInFn: () => {
        if (this.tabName == "info") {
          return this.$refs[this.tabName].onSave();
        } else {
          return new Promise((resolve, reject) => {
            resolve();
          });
        }
      }
    };
  },
  beforeRouteEnter(to, from, next) {
    console.log(from);
    if (
      from.path != "/detailPage/detailed-task" &&
      from.name != "eco-details" &&
      from.name != "eca-details" &&
      from.name != "ecr-details"
    ) {
      sessionStorage.removeItem("currentTabName");
      next();
    } else {
      next();
    }
  },
  methods: {
    initBreadcrumb() {
      let breadcrumbData = [];
      this.setBreadcrumb(breadcrumbData);
    },
    tabChange(key) {
      switch (key) {
        case "info":
          // this.$refs.info && this.$refs.info.fetch && this.$refs.info.fetch();
          break;
        case "structure":
          if (this.$refs.structure) {
            this.$refs.structure.init();
          }
          break;
        //关系图谱
        case "atlas":
          this.$refs.atlas.init();
          break;
        case "relevant":
          this.$refs.relevant.initFetch();
          break;
        case "usage":
          this.$refs.usage.init();
          break;
        case "supplier":
          // 代码块
          break;
        case "record":
          // 代码块
          break;
        case "DesignDocument":
          this.$refs.DesignDocument.fetchTable();
          break
        default:
        // 默认代码块
      }
      sessionStorage.setItem("currentTabName", key);
    },
    getViewData() {
      let param = {
        keyword: this.searchData,
        searchKey: this.searchData
      }
      fetchGroupListData.execute(param)
        .then((resp) => {
          console.log('resp', resp)
          this.dataList = resp
          if (this.dataList.length > 0 && this.dataList[0].itemDTOList.length > 0) {
            this.dataList[0].itemDTOList.forEach(item => {
              if (item.code === 'structure') {
                const splitItem = item.value.split("|");
                console.log('splitItem', splitItem)
                if (splitItem.length > 0) {
                  splitItem.forEach(splitData => {
                    const split = splitData.split(";;;");
                    const keyStructure = split[0]
                    console.log('9999', this.objectDetailsData[keyStructure])
                    this.showStructure = this.objectDetailsData[keyStructure] !== split[1];
                  })
                }
              } else if (item.code === 'supplierInformation') {
                const splitItem = item.value.split("|");
                if (splitItem.length > 0) {
                  splitItem.forEach(splitData => {
                    const split = splitData.split(";;;");
                    const keySupplier = split[0]
                    console.log('this.objectDetailsData', this.objectDetailsData)
                    if (this.objectDetailsData[keySupplier] !== split[1]) {
                      this.showSupplier = false
                    }
                  })
                }
              }
            })
          }
        })
        .catch((e) => {
          console.error(e)
          this.$error(e.msg)
        })
        .finally(() => {
          this.spining = false
        })
    },
    init(form, tab) {
      let that = this;
      let routeTabName = this.$route.query.currentTabName;
      let currentTabName = sessionStorage.getItem("currentTabName");
      let setTabName = routeTabName || currentTabName || "info";
      this.tabName = setTabName;
      if (this.$refs.info) {
        this.$refs.info.spinning = true;
      }
      if (form) {
        // Jw.jumpToDetail({
        //   oid: form.oid,
        //   type: form.type,
        //   masterType: form.masterType,
        // });
        this.$router.push({
          name: "object-details",
          query: {
            ...this.$route.query,
            oid: form.oid,
            type: form.type,
            masterType: form.masterType
          }
        });
        this.form = {
          oid: form.oid,
          type: form.type
        };
      } else {
        if (!this.form) {
          this.form = {
            oid: this.$route.query.oid,
            type: this.$route.query.type
          };
        }
      }

      findDetail
        .execute(this.form)
        .then(res => {
          that.provideData = res;
        })
        .catch(err => {
          this.$error("查询详情接口失败");
        })
        .finally(() => {
          //初始化操作数据
          this.$refs.toolbar && this.$refs.toolbar.init();
          this.tabChange(setTabName);
          if (this.$refs.info) {
            this.$refs.info.spinning = false;
          }
        });
    }
  },
  created() {
    this.initBreadcrumb();
    this.init();
    this.getViewData();
  },
  mounted() {
  }
};
</script>

<style lang="less">
.object-details {
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .main-tab {
    width: 100%;
    height: 20px;
    flex-grow: 1;
    // padding: 15px;
    background-color: #f2f2f2;
  }

  .object-details-tab {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;

    .ant-tabs-nav .ant-tabs-tab {
      margin-right: 0;
    }

    .ant-tabs-bar {
      margin-bottom: 0;
    }

    .ant-tabs-content {
      height: 20px;
      flex-grow: 1;

      .ant-tabs-tabpane {
        padding: 15px;
        height: 101%;
        overflow: scroll;
      }
    }
  }
}
</style>
