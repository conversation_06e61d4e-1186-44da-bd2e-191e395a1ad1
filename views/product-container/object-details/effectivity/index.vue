<template>
  <div class="effect-main-page">
    <div class="page-left">
      <!-- 编辑partbom关联有效性 -->
      <div class="page-left-head">
        <a-button type="primary" v-if="valideditfun" @click="addeffect">{{
          $t("txt_add_c")
        }}</a-button>

        <a-input-search
          :placeholder="$t('txt_search')"
          class="searchinput"
          v-model.trim="searchKey"
          @change="searchlist"
        ></a-input-search>
      </div>
      <div class="page-left-content">
        <div
          class="effectivitycard"
          v-for="(item, index) in effectivitylist"
          :key="index"
        >
          <div class="effect-title">
            <div
              class="effect-name"
              :class="{ addparam: !oldeffectivity[item.name] && haveoldEffect }"
            >
              {{ item.name }}
            </div>
            <!-- 检出状态才可以修改 -->
            <div class="effect-btn" v-if="valideditfun">
              <i class="jwi-iconedit" @click="editaction(item)"></i>
              <i class="jwi-icondelete" @click="deletedaction(item)"></i>
            </div>
          </div>
          <div class="effect-content">
            <div
              class="effect-content-display"
              :class="{ addparam: !oldeffectivity[item.name] && haveoldEffect }"
              v-text="item.displayValue"
            ></div>
            <!-- 历史值 -->
            <div
              class="changevaluesty"
              v-if="
                oldeffectivity[item.name] &&
                oldeffectivity[item.name] != item.displayValue && haveoldEffect
              "
            >
              {{ oldeffectivity[item.name] }}
            </div>
          </div>
        </div>
        <!-- 被删除的有效性 -->
        <div v-if="olddeleteddata.length > 0 && haveoldEffect">
          <div
            class="effectivitycard"
            v-for="(item, index) in olddeleteddata"
            :key="index"
          >
            <div class="effect-title">
              <div class="effect-name deletedeffectivity">{{ item.name }}</div>
            </div>
            <div class="effect-content">
              <div
                class="effect-content-display deletedeffectivity"
                v-text="item.displayValue"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="page-right">
      <time-line-effectivity
        :recordoid="recorddata.oid"
        ref="time-line-effect"
      />
    </div> -->

    <form-modal-dialog
      :recorddata="recorddata"
      :visible.sync="editshow"
      :viewdata.sync="editdata"
      :effectivitylist="firstlistdata"
      @reload="reloadlistdata"
    />
  </div>
</template>

<script>
import { findByPartBomOid, deleted } from "apis/efffectivity";
import formModalDialog from "./common/form-modal-dialog.vue";
import TimeLineEffectivity from "./common/time-line-effectivity.vue";
import { checkPermission } from "apis/part/index";
export default {
  components: { formModalDialog, TimeLineEffectivity },
  props: {
    recorddata: {
      type: Object,
      default: () => {},
    },
    editflag: {
      type: Boolean,
      default: false,
    },
    oldrecorddata: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      searchKey: "",
      editdata: {},
      editshow: false,
      effectivitylist: [],

      firstlistdata: [],
      // 变更时展示旧的有效性
      oldeffectivity: {},
      oldeffectivitylist: [],
      haveoldEffect: false,

      editpermission: false,
    };
  },
  created() {
    this.loadeffectivitylist();
    this.loadoldeffectivity();
    this.loadeditpremission();
  },
  computed: {
    valideditfun() {
      let flag = this.editpermission || this.editflag;
      return flag;
    },
    olddeleteddata() {
      let list = this.oldeffectivitylist.filter(
        (item) => !this.effectivitylist.some((old) => old.name === item.name)
      );
      return list;
    },
  },
  methods: {
    init () {
      this.loadeffectivitylist();
      this.loadoldeffectivity();
      this.loadeditpremission();
    },
    // 查询有效性编辑权限
    loadeditpremission() {
      checkPermission
        .execute({
          objectOid: this.recorddata.oid,
          viewCode: "EFFECTIVITYEDIT",
        })
        .then((resp) => {
          let param = resp.find(
            (item) => item.modelType === this.recorddata.type
          );
          this.editpermission = param.status === "enable";
        })
        .catch((e) => {
          console.error(e);
        })
        .finally(() => {});
    },
    // 加载旧变更对象
    loadoldeffectivity() {
      if (this.oldrecorddata && this.oldrecorddata.oid) {
        this.haveoldEffect = true && !this.valideditfun;
      }
    },
    // 验证是否可以编辑
    reloadlistdata() {
      if (this.$refs["time-line-effect"]) {
        this.$refs["time-line-effect"].loadeffectivity();
      }
      this.loadeffectivitylist();
    },
    searchlist() {
      let search = this.searchKey;
      if (search) {
        this.effectivitylist = this.firstlistdata.filter((item) =>
          item.name.includes(search)
        );
      } else {
        this.effectivitylist = this.firstlistdata;
      }
    },
    //编辑
    editaction(item) {
      this.editdata = Object.assign({}, item);
      this.editshow = true;
    },
    // 删除
    deletedaction(item) {
      this.$confirm({
        title: this.$t("btn_delete"),
        content: this.$t("msg_confirm_deletion"),
        okText: this.$t("btn_ok"),
        cancelText: this.$t("btn_cancel"),
        onOk: () => {
          deleted(item.oid)
            .then((resp) => {
              this.$success(this.$t("txt_delete_success"));
              this.loadeffectivitylist();
              this.reloadlistdata();
            })
            .catch((e) => {
              console.error(e);
              this.$error(e.msg);
            })
            .finally(() => {});
        },
      });
    },

    // 加载有效性值
    loadeffectivitylist() {
      this.searchKey = "";
      findByPartBomOid(this.recorddata.oid)
        .then((res) => {
          let resp = res.filter((item) => !item.markForDelete);
          this.firstlistdata = JSON.parse(JSON.stringify(resp));
          this.effectivitylist = resp;

          this.oldeffectivitylist = res.filter((item) => item.markForDelete);
          this.oldeffectivitylist.forEach((item) => {
            this.oldeffectivity[item.name] = item.displayValue;
          });
          this.$forceUpdate();
        })
        .catch((e) => {
          console.error(e);
        })
        .finally(() => {});
    },

    //添加有效性
    addeffect() {
      this.editdata = {};
      this.editshow = true;
    },
  },
};
</script>

<style lang="less" scoped>
.effect-main-page {
  display: flex;
  justify-content: space-between;
  .page-left {
    width: 100%;
    .page-left-head {
      .searchinput {
        width: 260px;
        margin-left: 5px;
      }
    }

    .page-left-content {
      margin-top: 20px;
      display: flex;
      flex-wrap: wrap;
    }
  }

  .page-right {
  }
}

.effectivitycard {
  width: 396px;
  height: 106px;
  border: 1px solid rgba(30, 32, 42, 0.15);
  border-radius: 4px;
  padding: 10px 15px 10px 15px;
  margin-right: 20px;
  margin-bottom: 20px;
  .effect-title {
    display: flex;
    justify-content: space-between;

    .effect-btn {
      width: 45px;
      display: flex;
      justify-content: space-between;

      i {
        cursor: pointer;
      }
    }

    .effect-name {
      font-weight: 500;
    }
  }

  .effect-content {
    margin-top: 10px;
    width: 376px;
    height: calc(~"100% - 30px");
    overflow: scroll;
    word-wrap: break-word;
  }

  .effect-content-display {
  }

  .changevaluesty {
    color: #ffe4bd;
    text-decoration: line-through;
  }

  .addparam {
    color: #2be62b;
  }
}

.deletedeffectivity {
  color: #ffc2c3;
  text-decoration: line-through;
}
</style>