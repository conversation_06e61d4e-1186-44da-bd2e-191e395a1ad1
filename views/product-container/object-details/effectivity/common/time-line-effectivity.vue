<template>
  <div class="time-line-page" v-if="lineData">
    <div class="time-line-title">
      {{ $t("txt_effectivity_change_history") }}
    </div>
    <!-- 时间线 -->
    <a-timeline>
      <a-timeline-item
        v-for="(item, index) in lineData"
        :key="index"
        :color="item.oid !== recordoid ? 'gray' : 'blue'"
      >
        <div
          class="effectivity-his-card"
          :class="{ bluecard: item.oid === recordoid }"
        >
          <div v-if="effectivimap[item.oid]">
            <div
              class="effectivity-line"
              v-for="(effect, index) in effectivimap[item.oid]"
              :key="'effec' + index"
            >
              <div class="effectivi-name">
                {{ effect.name + "：" }}
              </div>
              <a-tooltip>
                <template slot="title">
                  {{ effect.displayValue }}
                </template>
                <div class="effectivity-value-time">
                  {{ effect.displayValue }}
                </div>
              </a-tooltip>
            </div>
          </div>
          <div v-else class="effectivity-line">
            <div class="effectivi-name">{{ $t('btn_nones') }}</div>
          </div>
          <div class="effectivity-second">
            <div class="effectivity-time">
              {{ moment(new Date(item.updateDate)).format("YYYY-MM-DD HH:mm") }}
            </div>

            <div class="versionstatus">
              <div class="verson-number">
                <span>
                  {{ item.displayVersion }}
                </span>
              </div>
              <div class="version-status">
                <span>{{ $t(item.lifecycleStatus) }}</span>
              </div>

              <div class="updateuserinfo">
                {{ item.updateBy.substring(item.updateBy.length - 2) }}
              </div>
            </div>
          </div>
        </div>
      </a-timeline-item>
    </a-timeline>
  </div>
</template>

<script>
import moment from "moment";
import { findBatchPartOids } from "apis/efffectivity";
export default {
  props: {
    recordoid: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      moment,
      effectivimap: {},
    };
  },
  computed: {
    lineData() {
      try {
        let routeObject =
          this.$parent.$parent.$parent.$parent.$parent.$parent.$refs.toolbar
            .routeObject;
        return routeObject;
      } catch (error) {
        return null;
      }
    },
  },
  watch: {
    lineData: {
      handler(){
        this.loadeffectivity()
      },
      immediate: true,
    }
  },
  methods: {
    loadeffectivity() {
      if (this.lineData && this.lineData.length > 0) {
        let oids = this.lineData.map((item) => item.oid);
        findBatchPartOids(oids)
          .then((resp) => {
            this.effectivimap = resp;
          })
          .catch((e) => {
            console.error(e);
            this.$error(e.msg);
          })
          .finally(() => {});
      }
    },
  },
};
</script>

<style lang="less" scoped>
.effectivity-his-card {
  background: rgba(30, 32, 42, 0.02);
  border: 1px solid rgba(30, 32, 42, 0.15);
  border-radius: 4px;
  padding: 10px 16px 10px 16px;

  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.effectivity-line {
  display: flex;
}

.effectivi-name {
  font-weight: 400;
  font-size: 13px;
  color: rgba(30, 32, 42, 0.85);
  white-space: nowrap;
}

.effectivity-value-time {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.effectivity-value-time::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}

.effectivity-second {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.effectivity-time {
  font-weight: 400;
  color: rgba(30, 32, 42, 0.45);
}

.versionstatus {
  display: flex;
}

.verson-number {
  height: 22px;
  background: rgba(30, 32, 42, 0.04);
  border-radius: 4px;
  margin-right: 5px;
  display: flex;
  align-items: center;
  padding: 0 5px 0 5px;
}

.version-status {
  height: 22px;
  background: #f0f7ff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  padding: 0 5px 0 5px;

  font-weight: 400;
  font-size: 13px;
  color: #255ed7;
  margin-right: 5px;
}

.updateuserinfo {
  width: 24px;
  height: 24px;
  background-color: #75a4f0;
  border-radius: 50%;
  line-height: 24px;
  text-align: center;
  color: #fff;
}

.time-line-title {
  color: #292a2c;
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 20px;
}
.time-line-page {
  border-left: 1px solid rgba(30, 32, 42, 0.15);
  margin-top: -15px;
  padding: 15px;
  height: 85vh;
  overflow: scroll;
  width: 390px;
}

.bluecard {
  border-color: #255ed7;
}
</style>