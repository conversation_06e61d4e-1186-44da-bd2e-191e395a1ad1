<template>
  <a-modal
    v-model.trim="editshow"
    :title="editdata.oid ? $t('btn_edit') : $t('txt_add_c')"
    :confirmLoading="confirmLoading"
    :okText="$t('btn_confirm')"
    :cancelText="$t('btn_cancel')"
    @ok="confirmfun"
  >
    <a-form-model ref="formmodel" layout="vertical" :model="editdata">
      <a-form-model-item
        :label="$t('txt_effectivity_definition_type')"
        :rules="[
          { required: true, message: $t('msg_select'), trigger: 'change' },
        ]"
        prop="effectivityDefinitionOid"
      >
        <a-select
          v-model.trim="editdata.effectivityDefinitionOid"
          @select="selecttype"
        >
          <a-select-option
            :disabled="validdisabled(item)"
            v-for="item in effectivitydefintionlist"
            :key="item.oid"
            :obj="item"
            >{{ item.name }}</a-select-option
          >
        </a-select>
      </a-form-model-item>

      <a-form-model-item
        :label="$t('txt_effectivity_displayValue')"
        prop="displayValue"
        :rules="[
          {
            required: validAllvalue(),
            message: $t('txt_input'),
            trigger: 'change',
          },
          { validator: validdisplayvalue, trigger: 'change' },
        ]"
      >
        <!-- 数字范围 -->
        <a-textarea
          v-if="editdata.effectType === 'numberRange'"
          v-model.trim="editdata.displayValue"
          :placeholder="$t('txt_effectivity_type_numberRange')"
          :auto-size="{ minRows: 6, maxRows: 6 }"
          @change="splitvalue"
          @blur="clearblankvalue"
        />
        <!-- 文本范围 -->
        <a-textarea
          v-if="editdata.effectType === 'text'"
          v-model.trim="editdata.displayValue"
          :auto-size="{ minRows: 6, maxRows: 6 }"
          @change="splitvalue"
          @blur="clearblankvalue"
        />

        <!-- 时间区间 -->
        <div v-if="editdata.effectType === 'timeInterval'">
          <div
            class="data-range-line"
            v-for="(datelist, index) in dateranges"
            :key="'range' + index"
          >
            <a-range-picker
              @change="rangechange"
              v-model.trim="datelist.date"
              :placeholder="[$t('msg_starttime'), $t('msg_endtime')]"
              valueFormat="YYYY-MM-DD"
              :disabledDate="(currentDate) => disabledDate(currentDate, index)"
              class="range-date"
            />
            <i
              class="jwi-iconadd-circle"
              v-if="index === 0"
              @click="dateranges.push({ date: [] })"
            />
            <i
              class="jwi-icondelete"
              v-if="index !== 0"
              @click="dateranges.splice(index, 1)"
            />
          </div>
        </div>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
import { findByContainerOid } from "apis/efffectivitydefinition";
import { create, update } from "apis/efffectivity";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    viewdata: {
      type: Object,
      default: () => {},
    },
    effectivitylist: {
      type: Array,
      default: () => [],
    },
    recorddata: {
      type: Object,
      default: () => [],
    },
  },
  data() {
    return {
      effectivitydefintionlist: [],
      confirmLoading: false,
      dateranges: [{ date: [] }],
    };
  },
  created() {
    this.loadeffectivitydefintion();
  },
  watch: {
    // 日期范围
    dateranges: function (val) {
      this.rangechange();
    },
    editdata: function (val) {
      this.initdatarange();
    },
  },
  computed: {
    editshow: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    editdata: {
      get() {
        return this.viewdata;
      },
      set(val) {
        this.$emit("update:viewdata", val);
      },
    },
  },
  methods: {
    disabledDate(data, index) {
      let newArray = this.dateranges.filter((item, i) => i !== index);
      return newArray.some(
        (item) => {
          console.log(data, item.date[0], data.isSame(item.date[0]))
          return data.isBetween(item.date[0], item.date[1]) ||
          data.format("YYYY-MM-DD") == item.date[0] ||
          data.format("YYYY-MM-DD") == item.date[1]
        }
          
      );
    },
    initdatarange() {
      if (
        this.editdata.effectType === "timeInterval" &&
        this.editdata.displayValue
      ) {
        this.dateranges = this.editdata.displayValue.split(",").map((item) => {
          return { date: item.split("~") };
        });
      }
    },
    // 日期改变时转换为form表达的数据格式
    rangechange() {
      let displayValue = this.dateranges
        .map((item) => {
          const { date } = item;
          if (date.length === 2) {
            return `${date[0]}~${date[1]}`;
          } else {
            return "";
          }
        })
        .filter((item) => item)
        .join(",");
      this.editdata.displayValue = displayValue;
    },
    validdisplayvalue(rule, value, callback) {
      switch (this.editdata.effectType) {
        case "numberRange": {
          this.validnumber(rule, value, callback);
          break;
        }
        case "text": {
          this.validtxt(rule, value, callback);
          break;
        }
        case "timeInterval": {
          callback();
          break;
        }
        default:
          callback();
          break;
      }
    },
    //验证数字，范围前数字大于后数字
    validnumber(rule, value, callback) {
      if (value) {
        if (!/^\d+$/.test(value.replace(/,|~/g, ""))) {
          callback(new Error(this.$t("txt_effectivity_type_number_valid")));
          return;
        }

        let numbersplit = value.split(",");
        for (const ns of numbersplit) {
          let nvalue = ns.split("~").filter((item) => item);
          for (const iterator of nvalue) {
            // 最大数值不能超过9位
            if (iterator.length > 9) {
              callback(new Error(this.$t("txt_effectivity_number_length")));
              return;
            }
          }
        }

        // 验证输入值范围规范
        let splicevlaue = value
          .split(",")
          .filter((item) => item && item.includes("~"));
        for (const sv of splicevlaue) {
          let rangevalue = sv.split("~").filter((item) => item);
          if (
            rangevalue.length !== 2 ||
            parseInt(rangevalue[0]) >= parseInt(rangevalue[1])
          ) {
            callback(
              new Error(this.$t("txt_effectivity_type_number_range_valid"))
            );
            return;
          }
        }
      }
      callback();
    },
    // 验证是文本类型
    validtxt(rule, value, callback) {
      callback();
    },
    //对输入值格式化
    splitvalue() {
      let value = this.editdata.displayValue;
      if (value) {
        let now = value.replace(/\s|，/g, ",").replace(/-/g, "~");
        this.$set(this.editdata, "displayValue", now);
      }
    },
    // 清除空白数据
    clearblankvalue() {
      let value = this.editdata.displayValue;
      if (value) {
        this.$set(
          this.editdata,
          "displayValue",
          value
            .split(",")
            .filter((item) => item)
            .join(",")
        );
      }
    },
    // 验证是否已被使用
    validdisabled(item) {
      return this.effectivitylist
        .map((item) => item.effectivityDefinitionOid)
        .includes(item.oid);
    },
    //验证是否已有具体值，无具体值时为必填
    validAllvalue() {
      let required = true;
      this.effectivitylist.forEach((item) => {
        if (required && item.displayValue) {
          required = false;
        }
      });
      return required;
    },
    // 加载当前容器有效性规则
    loadeffectivitydefintion() {
      const { containerOid } = this.recorddata;
      findByContainerOid(containerOid)
        .then((resp) => {
          this.effectivitydefintionlist = resp;
        })
        .catch((e) => {
          console.error(e);
        })
        .finally(() => {});
    },
    selecttype(value, node) {
      const { effectType, oid, name } = node.data.attrs.obj;
      this.editdata.name = name;
      this.editdata.effectivityDefinitionOid = oid;
      this.editdata.effectType = effectType;
      this.$set(this.editdata, "displayValue", "");
      this.dateranges = [{ date: [] }];
    },
    confirmfun() {
      this.$refs["formmodel"].validate((valid) => {
        if (valid) {
          this.confirmLoading = true;
          this.editdata.partbomOid = this.recorddata.oid;
          let req = this.editdata.oid ? update : create;
          req(this.editdata)
            .then((resp) => {
              this.$success(
                this.editdata.oid
                  ? this.$t("msg_update_success")
                  : this.$t("txt_add_success")
              );
              this.$emit("reload");
              this.editshow = false;
            })
            .catch((e) => {
              console.error(e);
              this.$error(e.msg);
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.data-range-line {
  width: 100%;
  margin-top: 10px;

  i {
    cursor: pointer;
  }

  .range-date {
    width: 430px;
    margin-right: 10px;
  }
}
</style>