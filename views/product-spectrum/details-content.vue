<template>
  <a-form-model ref="ruleForm" :model.sync="formDatas2" @submit.native.prevent>
    <a-form-model-item
      v-for="(v, k) in keys"
      :key="k"
      :prop="v.prop"
      :label="v.label"
      :rules="v.rules"
      :layout="layout"
      :labelCol="labelCol"
      :wrapperCol="wrapperCol"
      :style="v.itemStyle"
    >
      <jw-complex-input
        v-bind="v.props"
        :is="v.props.is"
        :options="v.props.options || []"
        v-model.trim="formDatas2[v.props.value]"
      />
    </a-form-model-item>
    <a-form-model-item
      v-if="isShowBottom"
      style="direction: rtl; margin-top: 20px"
    >
      <span>
        <a-button style="margin-left: 10px" type="primary" @click="onSubmit">{{
          confirmText
        }}</a-button>
        <a-button @click="resetForm">{{ cancelText }}</a-button>
      </span>
    </a-form-model-item>
  </a-form-model>
</template>

<script>
import systemLanguage from '@jw/scaffold/src/jw.language';
export default {
  data() {
    return {
      formDatas2: JSON.parse(JSON.stringify(this.formDatas)),
    };
  },
  props: {
    formDatas: { type: Object, default: () => ({}) },
    keys: { type: Array, default: () => [] },
    isShowBottom: { type: Boolean, default: true }, // 是否显示底部的取消、确认按钮

    layout: { type: String, default: "vertical" },
    labelCol: { type: Object, default: () => ({ span: 0 }) },
    wrapperCol: { type: Object, default: () => ({ span: 24 }) },
    cancelText: { type: String, default:systemLanguage['btn_cancel'] },
    confirmText: { type: String, default: systemLanguage['btn_confirm'] },
  },
  methods: {
    handleCancel(e) {
      this.resetForm();
      console.log("取消事件---");
      this.$emit("cancel");
    },

    onSubmit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit("submit", this.formDatas2);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },
  },
};
</script>