<template>
	<div style="height: 100%" id="addBtn">
		<a-tree
			v-if="isShow"
			:tree-data="treeData"
			:selected-keys.sync="selectedKeys"
			:replace-fields="replaceFields"
			block-node
			show-icon
			:defaultExpandAll="false"
			style="
				width: 300px;
				height: 100%;
				padding: 16px 20px;
				overflow: auto;
				border-right: 1px solid #d9d9d9;
			"
		>
			<template slot="title" slot-scope="record">
				<div @click="selecttem(record)" class="space-between">
					<span style="display: inline-block">
						<jw-icon :type="record.modelIcon" />
					</span>
					<span>
						{{ record.title }}
					</span>
					<span
						class="show-opration"
						v-if="currentRole && record.type !== 'Container'"
					>
						<span
							v-if="!record.beRoot"
							style="float: right"
							@click="deleteItem(record)"
						>
							<jw-icon
								class="ceil-class"
								type="jwi-icondelete"
								:title="$t('btn_delete')"
							/>
						</span>
						<span style="float: right" @click.stop="clickAddBtn(record)">
							<jw-icon type="jwi-iconsubItem-add" :title="$t('txt_add_c')" />
						</span>
					</span>
				</div>
			</template>
		</a-tree>

		<a-modal
			v-model.trim="visible"
			:title="$t('txt_create_subclass')"
			width="248px"
			:mask="false"
			:getPopupContainer="() => document.getElementById('addBtn')"
			@ok="handleOk"
		>
			<a-form-model
				:layout="layoutType"
				ref="ruleForm"
				:model="form"
				:rules="rules"
			>
				<a-row :gutter="16">
					<a-col :span="24">
						<a-form-model-item ref="name" :label="$t('txt_name')" prop="name">
							<a-input
								v-model.trim="form.name"
								:maxLength="100"
								@blur="
									() => {
										$refs.name.onFieldBlur();
									}
								"
							/>
						</a-form-model-item>
					</a-col>
				</a-row>
				<!-- <a-row :gutter="16">
          <a-col :span="24">
            <a-form-model-item label="继承父分类内容" prop="isPrivate">
              <a-radio-group v-model.trim="form.isPrivate">
                <a-radio :value="true"> 是 </a-radio>
                <a-radio :value="false"> 否 </a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row> -->
			</a-form-model>
			<template slot="footer">
				<a-button
					key="submit"
					type="primary"
					:loading="submitLoading"
					@click="onSubmit"
				>
					{{ $t("btn_done") }}
				</a-button>
				<a-button key="back" @click="handleCancel"
					>{{ $t("btn_cancel") }}
				</a-button>
			</template>
		</a-modal>

		<!-- 新建产品型谱 -->
		<jw-modal-form
			:key="'1'"
			v-if="addVisible"
			:visible.sync="addVisible"
			:title="$t('txt_create_subclass')"
			:formDatas="JSON.parse(JSON.stringify(addParams))"
			:keys="[
				{
					prop: 'name',
					label: $t('txt_name'),
					rules: [rules.required],
					props: {
						is: 'a-input',
						value: 'name',
						placeholder: $t('placeholder_name'),
						maxLength: 100
					},
				},
				{
					prop: 'description',
					label: $t('txt_description'),
					rules: [rules.required],
					props: {
						is: 'a-input',
						value: 'description',
						placeholder: $t('txt_description'),
						maxLength: 255
					},
				},
			]"
			@submit="addItem"
		/>

		<!-- 编辑分组 -->
		<jw-modal-form
			:key="'2'"
			v-if="updateVisible"
			:visible.sync="updateVisible"
			:title="$t('txttxt_editor_fz_name')"
			:formDatas="updateParams"
			:keys="[
				{
					prop: 'name',
					label: $t('txt_name'),
					rules: [rules.required],
					props: {
						is: 'a-input',
						value: 'name',
						placeholder: $t('placeholder_name'),
					},
				},
			]"
			@submit="updateItem"
		/>
	</div>
</template>


<script>
import { jwModalForm, jwIcon } from "jw_frame";
import ModelFactory from "jw_apis/model-factory";
import { getCookie } from "jw_utils/cookie";
import rules from "../../utils/rules.js";
import {
	getList,
	addItem,
	deleteItem,
	updateItem,
	getDetails,
} from "../../apis/productSpectrum/index.js";

export default {
	components: {
		jwModalForm,
		jwIcon,
	},
	props: {
		// 权限判断
		currentRole: Boolean,
	},
	data() {
		return {
			submitLoading: false,
			rules,
			visible: false,
			isShow: true, // 控制重新渲染树结构
			rootInfo: [],
			treeData: [],
			replaceFields: { children: "children", title: "name", key: "oid" },
			selectedKeys: [],
			addVisible: false, // 控制分组的新建弹窗
			addParams: {},
			updateVisible: false,
			updateParams: {}, // 编辑弹窗的数据
			// 新建弹窗数据
			layoutType: "vertical",
			form: {
				name: "",
				spectrum: "",
				team: "",
				isPrivate: false,
			},
			rules: {
				name: [
					{
						required: true,
						message: this.$t("placeholder_name"),
						trigger: "blur",
					},
				],
				spectrum: [
					{
						required: true,
						message: this.$t("txt_select_spectrum"),
						trigger: "change",
					},
				],
			},
		};
	},
	created() {
		this.getList();
	},
	methods: {
		// 提交信息
		onSubmit() {
			let _this = this;
			// 获取根节点信息
			let { treeData, addParams, rootInfo } = _this;
			let rootData = { ...rootInfo };
			console.log(rootData);
      console.log(addParams);
			let { name } = this.form;
			let tenantOid = getCookie("tenantOid");
			this.$refs.ruleForm.validate((valid, res) => {
				if (valid) {
					let param = {
						name: name,
						description: "",
						locationInfo: {
							containerType: "Tenant",
							containerOid: tenantOid,
							catalogType: addParams.catalogType || "Tenant",
							catalogOid: addParams.catalogOid || tenantOid,
						},
					};
					this.submitLoading = true;
					addItem()
						.execute(param)
						.then((res) => {
							this.$success(this.$t("msg_save_success"));
							this.visible = false;
							this.submitLoading = false;
							this.getList();
						})
						.catch((error) => {
							this.submitLoading = false;
							this.$error(error.msg || this.$t("msg_failed"));
							console.log("error: ", error);
						});
				} else {
					console.log("error submit!!");
					return false;
				}
			});
		},
		handleShow() {
			this.visible = true;
		},
		handleCancel(e) {
			this.visible = false;
		},
		handleOk(e) {
			console.log(e);
			this.visible = false;
		},
		isJson(obj) {
			var isjson =
				typeof obj == "object" &&
				Object.prototype.toString.call(obj).toLowerCase() ==
					"[object object]" &&
				!obj.length;
			return isjson;
		},
		// 递归函数
		recursion(data) {
			let _this = this;
			data.map((item, index) => {
				item.title = item.name;
				item.key = item.oid;
				item.value = item.oid;
				if (item.children && item.children.length > 0) {
					item.children.map((item) => (item.childType = "child"));
					_this.recursion(item.children);
				}
			});
			return data;
		},
		// 获取结构树数据
		getList() {
			let tenantOid = getCookie("tenantOid");
			getList()
				.execute({
					searchKey: "",
				})
				.then((res) => {
					console.log("getList res: ", res);
					// 只有1个顶层时，使用this.treeData[0].children-----------------------------------
					if (res.children) {
						this.treeData = res.children;
						this.treeData = this.recursion(res.children);
					} else {
						this.treeData = [];
					}
					this.rootInfo = res;
					this.rootData = res;
					// console.log("this.treeData：",this.treeData);

					// 初始化，默认选中第一条记录
					this.getDetails({
						oid: tenantOid,
						type: "Tenant",
					});
					// 重新渲染
					this.isShow = false;
					let timer = setTimeout(() => {
						this.isShow = true;
						clearTimeout(timer);
					}, 10);
				})
				.catch((error) => {
					this.$error(error.msg || this.$t("msg_failed"));
					console.log("error: ", error);
				});
		},

		clickAddBtn({ dataRef }) {
			console.log("dataRef: ", dataRef);
			this.form.name = "";
			this.addParams = {
				catalogType: dataRef.type,
				catalogOid: dataRef.oid,
			};
			// this.addVisible = true;
			this.visible = true;
		},
		addItem(params) {
			console.log("params: ", params);
			let tenantOid = getCookie("tenantOid");
			addItem()
				.execute({
					name: params.name,
					description: params.description,
					locationInfo: {
						containerType: "Tenant",
						containerOid: tenantOid,
						catalogType: params.catalogType,
						catalogOid: params.catalogOid,
					},
				})
				.then((res) => {
					this.$success(this.$t("msg_save_success"));
					this.addVisible = false;
					this.getList();
				})
				.catch((error) => {
					this.$error(error.msg || this.$t("msg_failed"));
					console.log("error: ", error);
				});
		},

		deleteItem({ dataRef }) {
			let _this = this;
			console.log("dataRef: ", dataRef);
			this.$confirm({
				title: this.$t("txt_tips_top"),
				content: this.$t("txt_confirm_delete"),
				cancelText: this.$t("btn_cancel"),
				okText: this.$t("btn_ok"),
				class: "delete-modal",
				width: "330px",
				mask: false,
				onOk() {
					ModelFactory.create({
						url: `${Jw.gateway}/${Jw.containerService}/productCatalog/delete/${dataRef.oid}`,
						method: "post",
					})
						.execute()
						.then((data) => {
							console.log("deleteItem res: ", data);
							// 重新获取数据
							_this.getList();
							_this.$success(_this.$t("txt_delete_success"));
						})
						.catch((error) => {
							_this.$error(error.msg || this.$t("msg_failed"));
							console.log("error: ", error);
						});
				},
				onCancel() {
					console.log("Cancel");
				},
			});
		},
		// 点击编辑按钮，获取需要编辑的数据
		clickUpdateBtn({ dataRef }) {
			console.log("dataRef: ", dataRef);
			this.updateParams = {
				oid: dataRef.oid,
				name: dataRef.name,
			};
			this.updateVisible = true;
		},
		updateItem(params) {
			// console.log('params: ',params);
			updateItem()
				.execute({
					...params,
					// modelType:params.type,
				})
				.then((res) => {
					this.$success(this.$t("msg_update_success"));
					this.updateVisible = false;
					this.getList();
				})
				.catch((error) => {
					this.$error(error.msg || this.$t("msg_failed"));
					console.log("error: ", error);
				});
		},
		// 选择树节点，更新定义表
		selecttem({ dataRef }) {
			// console.log("dataRef: ", dataRef, "选择树节点");
			let params = { oid: dataRef.oid, type: dataRef.type };
			this.getDetails(params);
		},
		// 获取某1条数据对应的其他信息（如：详情等）
		getDetails(params) {
			getDetails()
				.execute(params)
				.then((res) => {
					// console.log("selecttem res: ",res);
					this.$emit("select", res);
				})
				.catch((error) => {
					this.$error(error.msg || this.$t("msg_failed"));
					console.log("error: ", error);
				});
		},
	},
};
</script>


<style lang="less" scoped>
.space-between {
	.show-opration {
		display: none;
	}
	&:hover {
		.show-opration {
			display: initial;
		}
	}
}
.ceil-class {
	padding-left: 10px;
}

/deep/.ant-modal {
	left: -300px;
	top: 127px;
}
.delete-modal {
	/deep/.ant-modal {
		left: -300px;
		top: 127px;
	}
}
</style>

<style>
.delete-modal .ant-modal-body {
	padding: 24px;
}

.delete-modal .ant-modal {
	left: -255px;
	top: 127px;
}

.delete-modal .ant-modal-close-x {
	line-height: 69px;
}

.delete-modal
	.ant-modal-confirm-body
	> .anticon
	+ .ant-modal-confirm-title
	+ .ant-modal-confirm-content {
	margin-left: 0;
}

.delete-modal .ant-modal-confirm-btns .ant-btn {
	width: 75px;
	float: right;
}

.delete-modal .ant-modal-confirm-btns .ant-btn.ant-btn-primary {
	margin-right: 8px;
	/* background-color: rgba(37, 94, 215, 1);
  border-color: rgba(37, 94, 215, 1); */
	/* background-color: #1890ff; */
}
</style>