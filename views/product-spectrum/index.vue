<template>
  <jw-page>
    <div slot="header-left">
      <div style="display: flex; align-items: center">
        <span style="color: #000; font-size: 20px">{{$t('txt_product_spectrum')}}</span>
      </div>
    </div>
    <div style="background: white; height: 100%">
      <div class="page-title" style="height: 61px">
        <div class="page-title-info">
          <span>{{$t('txt_product_spectrum')}}</span>
          <span class="page-title-info-add" v-if="currentRole" :title="$t('txt_add_c')" @click="AddChild">
            <jw-icon type="jwi-iconsubItem-add" />
          </span>
        </div>
        <div style="width: calc(100% - 301px);">
          <a-tabs class="right-tab" v-model.trim="activeTab" @change="onChangeTab">
            <a-tab-pane key="basicInfo" :tab="$t('txt_base_info')"></a-tab-pane>
            <a-tab-pane v-if="isShowDelivery" key="delivery" :tab="$t('txt_delivery_list')"></a-tab-pane>
            <div slot="tabBarExtraContent"
              v-if="activeTab==='basicInfo'"
              @click="changeEdit(!isEdit)"
            >
                <jw-icon
                  v-if="form.type!=='Container' && currentRole==true"
                  :type="isEdit ? 'jwi-iconclose' : 'jwi-iconedit'"
                  :title="isEdit ? $t('btn_cancel') : $t('btn_edit')"
                />
            </div>
          </a-tabs>
        </div>
      </div>
      
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: calc(100% - 61px);
        "
      >
        <!-- 产品型谱树结构 -->
        <tree ref="tree" :currentRole="currentRole" @select="handleSelect" />
        <!-- 详情页 -->
        <div class="detail-content" v-if="activeTab==='basicInfo'">
          <!-- 不显示租户信息 -->
          <div style="margin: 20px auto 0px auto" v-if="this.form.type !== 'Tenant'">
            <a-form-model
              :layout="layoutType"
              ref="ruleForm"
              :model="form"
              :rules="rules"
            >
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-model-item ref="name" :label=" $t('txt_name')" prop="name">
                    <a-input
                      :disabled="!isEdit"
                      v-model.trim="form.name"
                      @blur="
                        () => {
                          $refs.name.onFieldBlur();
                        }
                      "
                    />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-model-item :label=" $t('txt_description')" prop="description">
                    <a-input
                      style="height: 150px"
                      type="textarea"
                      :disabled="!isEdit"
                      v-model.trim="form.description"
                    />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-model-item :label=" $t('txt_dead_user')" prop="isPrivate">
                    <jw-avatar showName tag :data="{ name: form.createBy }" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item
                    :label=" $t('txt_create_date')"
                    prop="createDate"
                    :colon="false"
                  >
                    <a-input disabled :value="formatDateFn(form.createDate)" />
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-form-model>
          </div>
          <a-row class="opration-footer" v-if="isEdit">
            <a-button type="primary" :loading="submitLoading" @click="onSubmit"
              >{{ $t('btn_save')}}</a-button
            >
            <a-button style="margin-left: 10px" @click="changeEdit(false)">
              {{ $t('btn_cancel')}}
            </a-button>
          </a-row>
        </div>
        <!-- 交付清单 -->
        <delivery-list class="delivery-content"
          ref="deliveryList"
          v-if="activeTab === 'delivery'"
          :spectrumInfo="form"
          />
      </div>
    </div>
  </jw-page>
</template>

<script>
import { jwSimpleForm, jwIcon, jwAvatar } from "jw_frame";
import ModelFactory from "jw_apis/model-factory";
import { jwToolbar, jwPage } from "jw_frame";
import { formatDate } from "jw_utils/moment-date";
import tree from "./tree.vue";
import detailsContent from "./details-content.vue";
import deliveryList from "views/product-content/delivery-list/index.vue";
// 更新接口
const updateProductCatalog = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/productCatalog/update`,
  method: "post",
});

// 获取查询当前角色是否有新增和删除权限
const createRole = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
  method: "post",
});

export default {
  components: {
    tree,
    detailsContent,
    jwSimpleForm,
    jwAvatar,
    jwIcon,
    // jwToolbar,
    jwPage,
    deliveryList,
  },
  inject: ["setBreadcrumb"],
  data() {
    return {
      activeTab: 'basicInfo',
      isShowDelivery: false,
      rootData: {},
      submitLoading: false,
      // 权限判断
      currentRole: false,
      // 表单数据处理
      layoutType: "vertical",
      form: {
        name: "",
        spectrum: "",
        team: "",
        isPrivate: false,
      },
      rules: {
        name: [
          {
            required: true,
            message: this.$t('placeholder_name'),
            trigger: "blur",
          },
        ],
        spectrum: [
          {
            required: true,
            message: this.$t('txt_select_spectrum'),
            trigger: "change",
          },
        ],
      },
      isEdit: false,
      details: {}, // 选中时的详情页内容
    };
  },
  created() {
    this.checkCreateRole();
    let breadcrumbData = [];
    this.setBreadcrumb(breadcrumbData);
    window.localStorage.setItem("index_", 2)
  },
  methods: {
    // 顶层节点新增子节点
    AddChild() {
      let { rootInfo } = this.$refs.tree;
      this.$refs.tree.form.name = "";
      this.$refs.tree.addParams = {
        catalogType: rootInfo.type,
        catalogOid: rootInfo.oid,
      };
      // this.addVisible = true;
      this.$refs.tree.visible = true;
    },
    // 查询删除和新增权限
    checkCreateRole() {
      let param = {
        viewCode: "PRODUCTCATALOGCREATE",
        objectType: "ProductContainer",
        contextType: "ProductContainer",
      };
      createRole
        .execute(param)
        .then((data) => {
          this.currentRole = data[0].status === "disable" ? false : true;
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    // 时间格式化转换
    formatDateFn(date) {
      return formatDate(date);
    },
    // 树形选中回调
    handleSelect(params) {
      this.details = params;
      this.form = { ...params };
      if (this.form.type!=='Tenant') {
        this.isShowDelivery = true;
      }
      if (this.activeTab === 'delivery') {
        this.$nextTick(() => {
          this.$refs.deliveryList.init();
        })
      }
    },
    onSubmit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 提交编辑信息
          let { form, details } = this;
          let params = { oid: details.oid, type: details.type };
          let param = {
            description: form.description,
            name: form.name,
            oid: details.oid,
          };
          this.submitLoading = true;
          updateProductCatalog
            .execute(param)
            .then((res) => {
              this.$success(this.$t('msg_save_success'));
              this.visible = false;
              this.$refs["tree"].getDetails(params);
              this.submitLoading = false;
              this.isEdit = false;
            })
            .catch((error) => {
              console.error("error: ", error);
              this.$refs["tree"].getDetails(params);
              this.isEdit = false;
              this.submitLoading = false;
            });
        } else {
          console.error("error submit!!");
          return false;
        }
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },
    changeEdit(flag) {
      let { details } = this;
      if (flag === false) {
        this.form = { ...details };
      }
      this.isEdit = flag;
    },
    onChangeTab(tab) {

    },
  },
};
</script>

<style lang="less" scoped>
.page-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 60px;
  font-size: 14px;
  color: #1e202a;
  font-weight: bold;
}
.page-title .page-title-info {
  width: 300px;
  padding: 0px 20px;
  border-right: 1px solid #d9d9d9;
  border-bottom: 1px solid #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.page-title .page-title-info .page-title-info-add {
  cursor: pointer;
}
.detail-content {
  position: relative;
  width: calc(~"100% - 301px");
  height: 100%;
  padding: 10px 20px;
}
.opration-footer {
  position: absolute;
  height: 60px;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: 1px solid rgba(30, 32, 42, 0.15);
  background-color: #ffffff;
}
.delivery-content{
  width: calc(~"100% - 301px");
  height: 100%;
}
.right-tab {
  height: 100%;
  /deep/.ant-tabs-bar {
    padding: 0 20px;
    margin-bottom: 0;
  }
  /deep/.ant-tabs-nav .ant-tabs-tab {
    padding: 20px 0;
  }
  /deep/.ant-tabs-extra-content {
    line-height: 60px;
  }
}
</style>
