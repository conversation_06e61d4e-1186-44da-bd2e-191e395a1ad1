<template>
  <div class="view-page">
    <a-spin :spinning="loading" style="height: 100%">
      <jw-table
        class="tablecls"
        :dataSource="tabledata"
        :columns="columns"
        :showPage="false"
      >
        <template #toolbar>
          <div class="toolbar-line">
            <a-button type="primary" @click="addeffectivity">{{
              $t("btn_create")
            }}</a-button>
          </div>
        </template>
        <template #operation="{ row }">
          <div class="btn-lists">
            <i class="jwi-iconedit" @click="editdatafun(row)"></i>

            <i class="jwi-icondelete" @click="deleteddata(row)"></i>
          </div>
        </template>
      </jw-table>
    </a-spin>

    <a-modal
      :title="editdata.oid ? $t('btn_edit') : $t('btn_new_create')"
      @ok="confirmdata"
      v-model.trim="showeditpage"
      :confirm-loading="confirmloading"
      :cancelText="$t('btn_cancel')"
      :okText="$t('btn_save')"
    >
      <a-form-model ref="formmodel" :model="editdata" layout="vertical">
        <a-form-model-item
          :label="$t('txt_name')"
          prop="name"
          :rules="[
            {
              required: true,
              message: $t('txt_name_not_null'),
              trigger: 'change',
            },
          ]"
        >
          <a-input
            v-model.trim="editdata.name"
            :placeholder="$t('msg_input')"
            maxlength="64"
            class="defaultinput"
          ></a-input>
        </a-form-model-item>
        <a-form-model-item
          :label="$t('txt_type')"
          prop="effectType"
          :rules="[
            {
              required: true,
              message: $t('msg_select'),
              trigger: 'change'
            }
          ]"
        >
          <a-select v-model.trim="editdata.effectType" :placeholder="$t('msg_select')">
            <a-select-option v-for="(item) in types" :key="item.value">
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import { jwTable } from "jw_frame";
import {
  create,
  update,
  findByContainerOid,
  deleted,
} from "apis/efffectivitydefinition/index";
export default {
  data() {
    return {
      tabledata: [],
      loading: false,
      columns: [
        {
          field: "name",
          title: this.$t("txt_name"),
        },
        {
          field: "effectTypeName",
          title: this.$t("txt_type"),
        },
        {
          // 操作列定义
          field: "operation", //关键字
          title: this.$t("txt_operation"),
          slots: {
            default: "operation",
          },
        },
      ],
      showeditpage: false,
      confirmloading: false,
      editdata: {},
      containerOid: '',

      types: [
        {
          value: 'numberRange',
          name: this.$t('txt_effectivity_number_range'),
        },
        {
          value: 'timeInterval',
          name: this.$t('txt_effectivity_time_interval'),
        },
        {
          value: 'text',
          name: this.$t('txt_effectivity_text'),
        }
      ]
    };
  },
  components: {
    jwTable,
  },
  watch: {
    showeditpage: function (val) {
      if (!val) {
        this.editdata = {};
      }
    },
  },
  created(){
    this.containerOid = this.$route.query.containerOid
    this.loadlist()
  },
  methods: {
    //添加构型有效性
    addeffectivity() {
      this.editdatafun({});
    },
    //编辑
    editdatafun(row) {
      this.editdata = Object.assign({}, row)
      this.showeditpage = true;
    },
    //删除
    deleteddata(row) {
      this.$confirm({
        title: this.$t('txt_delete'),
        content: this.$t('msg_confirm_delete'),
        okText: this.$t('btn_ok'),
        cancelText: this.$t('btn_cancel'),
        onOk: () => {
          // 确认删除吗
          deleted(row.oid).then(resp => {
            this.$success("删除成功")
            this.loadlist()
          }).catch(e => {
            console.error(e)
            this.$error(e.msg)
          }).finally(() => {

          })
        }
      })
    },
    //加载数据
    loadlist(){
      this.loading = true
      findByContainerOid(this.containerOid).then(resp => {
        this.tabledata = resp
      }).catch(e => {
        console.error(e)
        this.$error(e.msg)
      }).finally(() => {
        this.loading = false
      })
    },
    confirmdata() {
      this.$refs['formmodel'].validate(valid => {
        if(valid){
          this.confirmloading = true
          let param = Object.assign({}, this.editdata)
          param.containerOid = this.containerOid

          let req = null
          let successmsg
          if(this.editdata.oid){
            req = update
            successmsg = this.$t('msg_update_success')
          }else{
            req = create
            successmsg = this.$t('txt_add_success')
          }

          req(param).then(resp => {
            this.$success(successmsg)
            this.loadlist()
            this.showeditpage = false
          }).catch(e => {
            console.error(e.msg)
            this.$error(e.msg)
          }).finally(() => {
            this.confirmloading = false
          })
        }
      })
    },
  },
};
</script>

<style lang="less" scoped>
.view-page {
  padding: 0 20px 0 20px;
}

.tablecls {
  height: calc(~"100vh - 120px");
}

 .defaultinput{
  width: 100%;
 }

 .btn-lists{
  display: flex;
  justify-content: space-around;
 }

 .btn-lists i{
  cursor: pointer;
 }
</style>