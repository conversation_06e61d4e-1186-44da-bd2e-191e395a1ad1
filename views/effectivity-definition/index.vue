<template>
  <div style="height: 100%" class="effectivity-page">
    <header>
      <div class="effectivity-header">
        <a-breadcrumb separator=">">
          <a-breadcrumb-item>
            <a href="javascript:void(0)" @click="routerBack">
              {{ breadcrumbname }}
            </a>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            {{ $t("txt_effectivity_definition") }}
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </header>
     <view-page />
  </div>
</template>

<script>
import viewPage from "./components/view-page.vue";
export default {
  data() {
    return {
      breadcrumbname: '',
    };
  },
  components: {
    viewPage
  },
  inject: ["setBreadcrumb"],
  created() {
    this.initBreadcrumb();
    this.breadcrumbname = this.$route.query.containerName
  },
  methods: {
    initBreadcrumb() {
      this.setBreadcrumb([]);
    },
    routerBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="less" scoped>
.effectivity-page {
  display: flex;
  flex-direction: column;
  background-color: #fff;
}
.effectivity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  min-height: 60px;
  line-height: 60px;
  padding: 0 24px;
  padding-right: 40px;
  background: #fff;
}
</style>