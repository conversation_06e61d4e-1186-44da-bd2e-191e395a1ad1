<template>
  <div style="padding:10px;">
    <jw-table
        ref="refTable"
        :columns="columns"
        :data-source.sync="tableData"
        :showPage="false"
    >
      <template #toolbar>
        <div class="toolbar-deliver">
          <div>
          <a-select style="width: 160px" @change='handleChange' allowClear>
            <a-select-option v-for="(item,i) in selectList" :key="i" :value="item">
              {{ item }}
            </a-select-option>
          </a-select>
          <a-input v-model.trim="searchParams.searchKey" @input='delaySearch' style="width: 200px" allowClear placeholder="请输入交付文档类别 ">
            <a-icon slot="prefix" type="search"/>
          </a-input>
          </div>
          <div class="export-button">
            <a-button @click="exportfun">
              <jw-icon type="jwi-iconexport"/>
            </a-button>
          </div>

        </div>
      </template>
      <template #deliverydocument="{ row }">
        <a @click="toDetail(row)">{{ row.deliverydocument }}</a>
      </template>
    </jw-table>
  </div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory"
const getData = ModelFactory.create({
  url: `${Jw.gateway}/customer/deliveryReport/getData`,
  method: "get",
})

const exportApi = ModelFactory.create({
    url: `${Jw.gateway}/customer/deliveryReport/exportDeliveryData`,
    method: "get",
    responseType: 'blob'
})
export default {
    props: {

    },
    components: {

    },
    data() {
      return {
        searchParams: {
          searchKey: undefined
        },
        tableData:[],
        copyTableData:[],
        selectList:[],
        filterStr:"",
        searchStr:""
      }
    },
    created() {
        this.getData()
        this.delaySearch = _.debounce(this.handleSearch, 300);
    },
    mounted() {

    },
    computed: {
        columns() {
            return [
                {
                    field: "indexs",
                    title: '序号',
                    width:80
                },
                {
                  field: "deliveryBigType",
                  title: '阶段',
                  width:120
                },
                {
                    field: "deliverySmallType",
                    title: '交付文档类别'
                },
                // {
                //     field: "deliverySmallType",
                //     title: '交付文档小类'
                // },
                {
                    field: "deliverydocument",
                    title: '交付文档',
                    slots: {
                        default: "deliverydocument",
                    },
                },
                {
                    field: "responPerson",
                    title: '负责人',
                    width:110
                },
                {
                    field: "status",
                    title: '文档状态',
                    width:110
                },
                {
                  field: "jhsj",
                  title: '计划时间',
                  width:130
                },
                {
                  field: "wcsj",
                  title: '完成时间',
                  width:145
                }
            ]
        },
    },
    watch: {

    },
    methods: {
      exportfun() {
        exportApi.execute({containerOid: this.$route.query.oid}).then(resp => {
          if (resp.type === 'application/json') {
            var reader = new FileReader();
            reader.readAsText(resp, 'utf-8');
            reader.onload = (e) => {
              let readerres = reader.result;
              let parseObj = JSON.parse(readerres);
              this.$error(parseObj.msg)
            }
          } else {
            let url = window.URL.createObjectURL(new Blob([resp]))
            let link = document.createElement('a')
            let fileName = "交付清单.xlsx"
            link.href = url
            link.style.display = 'none'
            link.setAttribute('download', fileName)
            document.body.appendChild(link)
            link.click()
          }
        })
      },
      getData() {
        getData.execute({
          containerOid: this.$route.query.oid
        }).then(res => {
          let data = res || []
          data.forEach((ele, index) => {
            ele.indexs = index + 1
            if (!this.selectList.includes(ele.deliveryBigType)) {
              this.selectList.push(ele.deliveryBigType)
            }
          })
          this.tableData = data
          this.copyTableData = data;
        })
      },
      toDetail(row) {
        this.$router.push({
          path: '/detailPage/object-details?type=DocumentIteration&masterType=Document&modelDefinition=JWIGeneralDocument&tabActive=product',
          query: {
            oid: row.documentOid
          }
        })
      },
      handleChange(value) {
        this.filterStr = value;
        this.filterList();
      },
      handleSearch(event) {
        this.searchStr = event.target._value;
        this.filterList();
      },
      filterList() {
        this.tableData = this.copyTableData.filter(item => {
          if (!this.filterStr && !this.searchStr) {
            return true;
          } else {
            if (this.filterStr && item.deliveryBigType !== this.filterStr) {
              return false
            }
            if (this.searchStr && !item.deliverySmallType.toLowerCase().includes(this.searchStr.trim().toLowerCase())) {
              return false
            }
            return true;
          }
        })
      }
    }
}
</script>

<style scoped lang="less">
.toolbar-deliver{
    display: flex;
    justify-content: space-between;
    width: 100%;
}
</style>
