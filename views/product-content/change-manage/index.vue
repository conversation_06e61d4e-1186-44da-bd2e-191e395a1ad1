<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-05-06 17:00:44
 * @LastEditTime: 2022-05-06 17:07:38
 * @LastEditors: <EMAIL>
-->
<template>
  <div class="change-manage">
    <change-management
        :pageCode="pageCode"
        :changeApi="changeApi"
     />
  </div>
</template>

<script>
import changeManagement from "views/change-management/table"
export default {
  name:'changeManage',
  components: {
    changeManagement,
  },
  data() {
    return {
        pageCode: 'changeManage',
        changeApi: `${Jw.gateway}/${Jw.changeServer}/change/findECRByFrom`,
    }
  },
}
</script>

<style lang="less" scoped>
.change-manage{
  height: calc(~"100% - 45px");
}
</style>