<template>
  <a-drawer :maskClosable="false" :title="title" :bodyStyle="bodyStyle" width="50%" :visible="visible" @close="onClose" destroyOnClose>
    <div class="detail-drawer-wrap">
      <div class="detail-drawer-body-wrap">
        <a-form-model ref="ref_model_form" :model="modelData" :label-position="'right'">
          <a-form-model-item v-if='hasSubModel' :label="$t('txt_type')" prop="activeModle" :rules="{ required: true, message: $t('msg_select'), trigger: 'change' }">
            <a-select v-model.trim="modelData.activeModle" allowClear :placeholder="$t('msg_select')" :disabled="paramsData.locationInfo.selectDisabled" @change="onchangeModel">
              <a-select-option v-for="item in subModelOptions" :key="item.name" :value="item.name">
                {{ $t(item.name) }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('txt_position_flex')" prop="activeCatalog" :rules="{ required: true, message: $t('txt_select_position'), trigger: 'change' }">
            <div>
            <location-tree ref="ref_locationTree" :disabled="paramsData.locationInfo.disabled" :locationInfo.sync='paramsData.locationInfo' :activeCatalog.sync='modelData.activeCatalog'></location-tree>
            </div>
          </a-form-model-item>
        </a-form-model>
        <jw-layout-builder v-if="layoutVisible && modelData.activeModle" ref="ref_appBuilder" type="Model" :layoutName="modelInfo.layoutName" :modelName="modelData.activeModle" :instanceData="instanceData" @initModel='onInitModel'>
          <template v-for="(node, slotName) in $scopedSlots" :slot="slotName" slot-scope="slotData">
            <slot :name="slotName" v-bind="slotData"></slot>
          </template>
          <template #number="{formData}">
            <a-input value="" disabled v-if="formData.source==='银河自研'||(formData.extensionContent&&formData.extensionContent.source==='银河自研')"></a-input>
            <a-input v-model="formData.number" allowClear v-else></a-input>
          </template>
        </jw-layout-builder>
      </div>
    </div>
    <div class="detail-drawer-foot-wrap">
      <a-button type="primary" v-if="$refs.ref_appBuilder && $refs.ref_appBuilder.getValue() && $refs.ref_appBuilder.getValue().genericType === 'variable'" :loading="saveLoading" @click="onSave(false, true)">{{ $t('btn_over_effectivity') }}</a-button>
      <a-button type="primary" :loading="saveLoading" @click="onSave(true)">{{ $t('btn_over_next') }}</a-button>
      <a-button class="btn-cancel" :loading="saveLoading" @click="onSave(false)">{{ $t('btn_done') }}</a-button>
    </div>
    <a-modal :title="$t('txt_materials')" width="65%" :visible="visibleMaterals" :okText="$t('txt_continue_create')" :cancelText="$t('txt_create_cancel')" @ok="confirmMaterals" @cancel="cancelMaterals" :confirmLoading="confirmLoading">
      <materialsObject ref="materialsObjectS" :titleShow="false" :createPartList="createPartMaterialsList" />
    </a-modal>
  </a-drawer>
</template>

<script>
import locationTree from "views/product-content/content-manage/location-tree";
import { jwLayoutBuilder } from "jw_frame";
import { findItem } from "utils/util";
import modelStore from "jw_stores/common";
import ModelFactory from "jw_apis/model-factory";
import { findDetail } from "apis/baseapi";
import materialsObject
  from "../../product/contentManagement/workflow/form-task/similar-materials-task/materials-object.vue";

// 获取子类型
const fetchSubModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/query-sub-model`,
  method: "post"
});

// 查询容器详情
const fetchContainerDetail = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/container/product/findDetail`,
  method: "get"
});

// 获取下载和预览按钮权限
const fetchBtnFilter = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.sysconfigServer
  }/preferences/setting/query-config-value`,
  method: "get"
});

const findSamePartData = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/findSamePartData`,
  method: "post"
});

export default {
  name: "createDrawer",
  props: ["territory"],
  components: {
    materialsObject,
    jwLayoutBuilder,
    locationTree
  },
  data() {
    this._initInstanceData = {};
    return {
      visible: false,
      layoutVisible:true,
      bodyStyle: { padding: 0 },
      title: "",
      saveLoading: false,
      instanceData: {},
      modelInfo: {},
      paramsData: {
        locationInfo: {}
      },

      hasSubModel: false,
      subModelOptions: [],
      visibleMaterals: false,
      confirmLoading: false,
      createPartMaterialsList:[],
      modelData: {
        activeModle: undefined,
        activeCatalog: undefined
      },

      reloaddrawer: true
    };
  },
  mounted() {},
  methods: {
    fetchSubModel(options) {
      fetchSubModel
        .execute({
          viewCode: "ENTITY_FILTER",
          objectType: this.modelInfo.modelName,

          //contextType: this.$route.query.type,
          //contextOid: this.$route.query.oid
          contextType: options.params.locationInfo.catalogType,
          contextOid: options.params.locationInfo.catalogOid
        })
        .then(res => {
          if (res.length == 1) {
            this.hasSubModel = false;
          } else {
            this.hasSubModel = true;
          }
          if (this.paramsData.activeModle) {
            this.modelData.activeModle = this.paramsData.activeModle;
          } else {
            this.modelData.activeModle = res[0].name;
          }
          if (this.territory === "electron") {
            res.map((item, index) => {
              if (item.code === "JWIRawMaterial") {
                this.modelData.activeModle = item.code;
              }
            });
          }
          console.log("res", res);
          this.subModelOptions = res.filter(row => {
            return !["Part", "ECAD", "Document"].includes(row.code);
          });
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },

    onchangeModel(val) {
      this.instanceData = _.cloneDeep(this._initInstanceData);
      this.$nextTick(() => {
        this.modelData.activeModle = val;
      });
    },

    async show(options) {
      if (options) {
        this.title = options.title;
        this.instanceData = options.instanceData || {
          modelName: options.modelInfo.modelName
        };
        this._initInstanceData = JSON.parse(JSON.stringify(this.instanceData));
        this.modelInfo = options.modelInfo;
        this.paramsData = options.params;
        this.modelData.activeCatalog = this.paramsData.locationInfo.catalogOid;
      }
      // this.fetchContainerDetail(options.params.locationInfo.containerOid);
      this.fetchSubModel(options);

      //获取分类信息,ecad无分类
      if(this.modelInfo.modelName !== 'ECAD'){
        await this.getClassifyInfo(options.params.locationInfo);
      }
      this.visible = true;
      this.saveLoading = false;
      _.delay(() => {
        this.$refs.ref_model_form && this.$refs.ref_model_form.resetFields();
        this.$refs.ref_appBuilder && this.$refs.ref_appBuilder.clearValidate();
      });
    },
    getClassifyInfo(locationInfo) {
      let { catalogOid, catalogType } = locationInfo;
      return findDetail
        .execute({ oid: catalogOid, type: catalogType })
        .then(res => {
          this._initInstanceData.classificationInfo =
            res.classificationInfo || {};
          this.instanceData.classificationInfo = res.classificationInfo || {};
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },

    onInitModel({ layout }) {
      let rows = layout?.content?.layout;
      rows.forEach(row => {
        row.forEach(item => {
          if (item.fieldName == "classificationInfo") {
            item.disabled = true;
          }
        });
      });
    },

    getBtnFilter(docInfo) {
      fetchBtnFilter
        .execute({
          name: "Delivery_Document_Down_Status"
        })
        .then(res => {
          if (res && res.length) {
            let item = res.find(val => val.value === docInfo.lifecycleStatus);
            let btnFilter = item ? true : false;
            this.instanceData = {
              classificationInfo: {
                ...this.instanceData.classificationInfo,
                btnFilter: !btnFilter
              }
            };
          }
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },
    fetchContainerDetail(containerOid) {
      fetchContainerDetail
        .execute({
          oid: containerOid
        })
        .then(res => {
          this.currentContainerType = res.modelDefinition;
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },
    checkModelForm() {
      let modelForm = this.$refs.ref_model_form;
      if (modelForm) {
        modelForm.validate().catch(err => {
          this.$error("modelForm Error....");
        });
      }
    },
    /**
     * toeffectivity: 是否跳转有效性设置页面
     */
    onSave(show, toeffectivity) {
      this.checkModelForm();
      let appBuilder = this.$refs.ref_appBuilder;
      appBuilder &&
        appBuilder.validate().then(() => {
          let params = appBuilder.getValue();
          params.modelDefinition = this.modelData.activeModle;
          params.locationInfo = this.paramsData.locationInfo;
          if (this.paramsData.targetOid) {
            params.targetOid = this.paramsData.targetOid;
          }
          if (this.paramsData.sourceOid) {
            params.sourceOid = this.paramsData.sourceOid;
          }
          if (
            params.source === "银河自研" ||
            (params.extensionContent &&
              params.extensionContent.source === "银河自研")
          ) {
            params.number = "";
          } else if (params.source === "外协类文件" && !params.number) {
            this.$error("请输入编码");
            return false;
          }
          this.saveLoading = true;
          if (this.modelInfo.modelName === "Part") {
            findSamePartData
                .execute([{ ...params }])
                .then(el => {
                  if (el && !(JSON.stringify(el) === "{}")) {
                    this.createPartMaterialsList = el.create;
                    this.visibleMaterals = true;
                    this.$nextTick(() => {
                      this.$refs.materialsObjectS.show();
                    });
                    this.toeffectivityMaterals = toeffectivity;
                    this.showMaterals = show;
                    this.paramsMaterals = params;
                  } else {
                    this.addCreate(show, toeffectivity, params);
                  }
                })
                .catch(err => {
                  this.saveLoading = false;
                  this.$error(err.msg);
                });
          } else {
            this.addCreate(show, toeffectivity, params);
          }
        });
    },
    addCreate(show, toeffectivity, params) {
      ModelFactory.create({
        url: this.paramsData.url,
        method: "post"
      })
          .execute(params)
          .then(res => {
            this.$success(this.$t("txt_create_success"));

            this.$emit("onRefresh", {
              oid: this.paramsData.locationInfo.catalogOid,
              type: this.paramsData.locationInfo.catalogType,
              clickOid: this.paramsData.targetOid,
              clickType: this.paramsData.targetType,
              itemObj: res
            });

            this.saveLoading = false;

            if (toeffectivity) {
              Jw.jumpToDetail(res, {
                hasPermission: true,
                toUrl: `/object-details?currentTabName=effectivity`,
                blank: false
              });
            }

            if (!show) {
              this.hide();
            } else {
              this.layoutVisible = false;
              this.$nextTick(() => {
                this.layoutVisible = true;
                //缓存分类信息
                if (params.classificationInfo) {
                  this.instanceData = _.cloneDeep(this._initInstanceData);
                  this.instanceData.classificationInfo =
                      params.classificationInfo;
                }
              });
            }
          })
          .catch(err => {
            this.saveLoading = false;
            this.confirmLoading = false;
            console.error(err);
            this.$error(err.msg);
          })
          .finally(() => {
            this.saveLoading = false;
            this.confirmLoading = false;
            this.visibleMaterals = false;
            this.showMaterals = false;
            this.toeffectivityMaterals = false;
            this.paramsMaterals = {};
            this.createPartMaterialsList = [];
        });
    },
    hide() {
      this.visible = false;
      this.saveLoading = false;

      this.instanceData = {};
      this.modelInfo = {};

      this.subModelOptions = [];

      this.modelData = {
        activeModle: undefined,
        activeCatalog: undefined
      };

      this._initInstanceData = {};

      modelStore.set("operationCode", "");
    },
    confirmMaterals() {
      this.addCreate(
          this.showMaterals,
          this.toeffectivityMaterals,
          this.paramsMaterals
      );
      this.saveLoading = false;
      this.confirmLoading = true;
    },
    cancelMaterals() {
      this.saveLoading = false;
      this.confirmLoading = false;
      this.visibleMaterals = false;
      this.showMaterals = false;
      this.toeffectivityMaterals = false;
      this.paramsMaterals = {};
      this.createPartMaterialsList = [];
    },
    onClose() {
      this.hide();
    }
  }
};
</script>

<style lang="less" scoped>
.detail-drawer-wrap {
  .detail-drawer-body-wrap {
    height: calc(~"100vh - 126px");
  }
}
</style>
