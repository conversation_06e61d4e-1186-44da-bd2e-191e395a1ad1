<template>
  <a-modal v-model="visible" :title="$t('from_create_folder')" @ok="confirm" @cancel="cancel" :confirmLoading="confirmLoading">
    <a-tree v-loading='loading' class="tree-select" v-model="checkedKeys" checkable  :tree-data="treeData"
      :replaceFields="replaceFields" />
    <!-- 自定义footer -->
    <div slot="footer" class="modal-footer">
      <a-button @click="cancel">{{ $t('btn_cancel') }}</a-button>
      <a-button type="primary" :loading="confirmLoading" @click="confirm">{{ $t('btn_confirm') }}</a-button>
      <a-button type="primary" :loading="copyLoading" @click="confirmAndCopy">{{ $t('confirm_and_copy') }}</a-button>
    </div>
  </a-modal>
</template>

<script>
import {fetchfolderTree, createFolderTree, createFolderTreeWithContainerTeam} from "/apis/part";
export default {
  props: {
    currentTree: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      treeData: [],
      checkedKeys: [],
      replaceFields: {
        key: "oid",
        title: "name",
        children: "children",
      },
      //加载
      confirmLoading: false,
      copyLoading: false,
    }
  },
  created() {
    // this.loadTreeData()
  },
  methods: {
    cancel() {
      this.visible = false
      this.treeData = []
    },
    // 新增的 confirmAndCopy 逻辑
    confirmAndCopy() {
      if (this.checkedKeys.length === 0) {
        this.$error(this.$t('choose_folder'))
        return
      }
      this.copyLoading = true
      let params = {
        folderOid: this.currentTree.oid,
        fromOids: this.checkedKeys,
        isCopy: true  // 增加的参数
      }
      createFolderTreeWithContainerTeam.execute(params).then(resp => {
        this.$emit('reloadTree')
        this.$success(this.$t('txt_create_success'))
        this.visible = false
      }).catch(e => {
        console.log(e)
        this.$error(e.msg)
      }).finally(() => {
        this.copyLoading = false
      })
    },
    confirm() {
      //验证是否选中树
      if (this.checkedKeys.length === 0) {
        this.$error(this.$t('choose_folder'))
        return
      }
      this.confirmLoading = true
      let params = {
        folderOid: this.currentTree.oid,
        fromOids: this.checkedKeys
      }
      createFolderTree.execute(params).then(resp => {
        this.$emit('reloadTree')
        this.$success(this.$t('txt_create_success'))
        this.visible = false
      }).catch(e => {
        console.log(e)
        this.$error(e.msg)
      }).finally(() => {
        this.confirmLoading = false
      })
    },
    show() {
      this.selectedKeys = []
      this.visible = true
      this.loadTreeData()
    },
    //加载树数据
    loadTreeData() {
      this.loading = true;
      let params = {
        containerModel: 'ProductContainer'
      }
      fetchfolderTree.execute(params).then(resp => {
        this.treeData = resp
        this.loading = false;
      })
    }
  }
}
</script>

<style lang="less" scoped>
.tree-select{
  height: 50vh;
  overflow: auto;
}
</style>
