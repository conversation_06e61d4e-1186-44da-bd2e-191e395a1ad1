<template>
  <a-dropdown @visibleChange="loadingList" :trigger="['click']" placement="bottomRight">
    <slot>
      <span class="jwi-iconellipsis" :title="$t('btn_more')" style="cursor: pointer"></span>
    </slot>
    <a-menu slot="overlay" @click="actionBtn">
      <a-spin v-if="loading" style="margin: 20px 65px" />
      <template v-else v-for="(item) in btnList">
        <a-menu-item :key="item.code" :disabled="item.status === 'disable'"
          v-if="!(item.code === 'batchdownload' && currentRow.modelDefinition === 'CADDrawing')">
          <span :class="item.icon" style="margin-right: 8px"></span>{{ $t(item.internationalizationKey) }}
        </a-menu-item>
      </template>
    </a-menu>
  </a-dropdown>
</template>

<script>
import {
  getDropdownList
} from 'apis/part'
export default {
  props: {
    folder: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      loading: false,
      btnList: [],
    }
  },
  methods: {
    actionBtn({ key }) {
      switch (key) {
        case "create":
          this.$emit("create")
          break;
        case "rename":
          this.$emit("rename")
          break;
        case "delete":
          this.$emit("delete")
          break;
        case "createByUse":
          this.$emit("createByUse")
          break;
        default:
          break;
      }
    },
    loadingList(val) {
      if(!val){
        return
      }
      this.loading = true
      let params = {
        viewCode: 'FOLDERINSTANCEOPERATION',
        objectOid: this.folder.oid,
      };
      getDropdownList.execute(params).then(resp => {
        this.btnList = resp
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style lang="less" scoped>
.btnarea {
  background-color: red;
  height: 100%;
  width: 20px;

}
</style>