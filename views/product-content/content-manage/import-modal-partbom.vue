<template>
  <jw-base-color-modal
    :title="$t('txt_import_partbom')"
    :visible.sync="visible"
    :ok-btn-loading="saveLoading"
    :ok-text="totalcount + ' ' + $t('btn_ok') + $t('btn_import')"
    :cancel-text="$t('btn_cancel')"
    :dialogClass="'dialog-class'"
    :mask="false"
    @ok="onSave"
    @cancel="onClose"
  >
    <div class="btn-wrap">
      <a-upload-dragger
        name="file"
        :accept="'.xlsx'"
        action="string"
        :fileList.sync="files"
        :customRequest="onUpload"
        :remove="removelist"
      >
        <div class="appendixs-label">
          <jw-icon type="#jwi-shangzhuanwenjian"></jw-icon>
          <div>
            <span
              >{{ $t("txt_feil_drop") }}
              <span class="upload-btn">{{ $t("txt_click_upload") }}</span>
            </span>
            <div>
              ({{
                $t("txt_feil_size_1000") +
                "," +
                $t("txt_uplaod_type") +
                ".xlsx"
              }})
            </div>
          </div>
        </div>
      </a-upload-dragger>
    </div>
    <template #footer-before-btn>
      <a-button
        type="link"
        style="float: left; padding: 0"
        :loading="downloadLoading"
        @click="downexameplefile"
      >
        {{ $t("btn_download") + $t("txt_doc_temp") }}
      </a-button>
    </template>
  </jw-base-color-modal>
</template>

<script>
import { jwBaseColorModal } from "jw_frame";
import ModelFactory from "jw_apis/model-factory";
import { getCookie } from "jw_utils/cookie";
import downloadModal from "./download-modal.vue";
const importExcel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part-export/upload/createPartAndUploadBom`,
  method: "post",
});

export default {
  name: "batchImportModal",
  components: {
    jwBaseColorModal,
    downloadModal,
  },
  props: ["visible", "currentTree"],
  data() {
    return {
      file: null,
      downloadLoading: false,
      testLoading: false,
      saveLoading: false,
      totalcount: "",
      files: [],
    };
  },
  methods: {
    removelist(){
      this.files = []
      this.file = null
    },
    onUpload(info) {
      this.file = info.file;
      let filetype = this.file.name.substring(this.file.name.lastIndexOf("."));
      if (filetype !== ".xlsx") {
        this.$error(this.$t("nonsupport_filetype") + filetype);
        return;
      }
      this.files = [this.file]
    },
    downexameplefile() {
      this.downloadLoading = true;
      fetch(
        `${Jw.gateway}/${Jw.partBomMicroServer}/part-export/export/createPartAndUploadBomTemplate?containerOid=${this.currentTree.catalogOid}`,
        {
          method: "get",
          headers: {
            "Content-Type": "application/json;charset=utf8",
            appName: Jw.appName,
            accesstoken: getCookie("token"),
            tenantAlias: getCookie("tenantAlias"),
            tenantOid: getCookie("tenantOid"),
          },
        }
      )
        .then((response) => {
          return response.blob();
        })
        .then((res) => {
          let url = window.URL.createObjectURL(
            new Blob([res], {
              type: "application/vnd.ms-excel",
            })
          );
          let link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", "example.xlsx");
          document.body.appendChild(link);
          link.click();
          this.$success(this.$t("txt_export_success"));
          this.downloadLoading = false;
        })
        .catch((err) => {
          this.downloadLoading = false;
          this.$error(err.msg);
        });
    },
    onClose() {
      this.removelist()
      this.$emit("close");
    },
    onSave() {
      if (!this.file) {
        this.$warning(this.$t("txt_import_first"));
        return;
      }
      let formData = new FormData();
      formData.append("file", this.file);
      let dataParams = {
        containerOid: this.currentTree.containerOid,
        containerType: this.currentTree.containerType,
        catalogOid: this.currentTree.oid,
        catalogType: this.currentTree.type,
      };
      formData.append("partImport", JSON.stringify(dataParams));
      this.saveLoading = true
      importExcel
        .execute(formData)
        .then((res) => {
          this.$success(this.$t('txt_import_success'))
          this.$emit("getList");
         
          this.onClose();
        })
        .catch((err) => {
          this.$error(err.msg);
        }).finally(() => {
          this.saveLoading = false
        })
    },
  },
};
</script>

<style lang="less" scoped>
.appendixs-label {
  display: flex;
  align-items: center;
  margin-left: 16px;
  text-align: left;
}
.jw-icon {
  font-size: 32px;
  margin-right: 8px;
}
</style>
<style lang="less">
.dialog-class {
  top: 160px;
  margin-right: 76px;
}
</style>
