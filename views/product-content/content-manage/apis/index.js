import Vue from "vue";
import ModelFactory from 'jw_apis/model-factory';
import { downloadTempFile } from "utils/util";
import { getCookie } from "jw_utils/cookie"
//表格导出导入
export const getExcelList = (type) => {
  let _this = Vue.prototype
  let list = [
    {
      title: _this.$t('批量导入全局替换'),
      type: 'batchImportGLOBAL',
      //是否是选择多个字属性
      multiple: true,
      visibleDown: false,
      //上传api
      upload: () => {
        return ModelFactory.create({
          url: `${Jw.gateway}/${Jw.customerServer}/common/batchImportGLOBAL`,
          method: 'post',
        });
      },
      //下载模板
      download: (typeList) => {
        //需要在 batch-import-model-file.vue中的 downloadTemp 中增加类型，以支持直接下载
        downloadTempFile('ecadExcelTemplate','全局替代关系模板')
      }
    },
    {
      title: _this.$t('批量导入BOM'),
      type: 'batchImportBOM',
      //是否是选择多个字属性
      multiple: true,
      visibleDown: false,
      //上传api
      upload: () => {
        return ModelFactory.create({
          url: `${Jw.gateway}/${Jw.customerServer}/common/batchImportBOM`,
          method: 'post',
        });
      },
      //下载模板
      download: (typeList) => {
        //需要在 batch-import-model-file.vue中的 downloadTemp中增加类型，以支持直接下载
        downloadTempFile('ecadExcelTemplate','BOM批量导入模板')
      }
    },
    {
      title: _this.$t('批量更新'),
      type: 'deliverUpdate',
      //是否是选择多个字属性
      multiple: true,
      visibleDown: false,
      //上传api
      upload: () => {
        return ModelFactory.create({
          url: `${Jw.gateway}/${Jw.customerServer}/deliveryReport/updateDelivery`,
          method: 'post',
        });
      },
      //下载模板
      download: () => {
        ModelFactory.create({
          url: `${Jw.gateway}/${Jw.customerServer}/deliveryReport/importDeliveryTemplate`,
          method: 'get',
          responseType: "blob"
        }).execute().then(blob => {
          let url = window.URL.createObjectURL(new Blob([blob]))
          let link = document.createElement('a')
          link.href = url
          link.style.display = 'none'
          link.setAttribute('download', "交付清单导入模板.xlsx")
          document.body.appendChild(link)
          link.click()
          link.remove()
        })
      }
    },
    {
      title: _this.$t('part_document_import'),
      type: 'Part',
      //上传api
      upload: () => {
        return ModelFactory.create({
          url: `${Jw.gateway}/${Jw.partBomMicroServer}/part-export/importIncludeFile`,
          method: 'post',
        });
      },
      //下载模板
      download: (type, classModle) => {
        return `${Jw.gateway}/${Jw.partBomMicroServer}/part-export/exportTemplateIncludeFile?name=${type ? type : 'Part'}&clsOid=${classModle ? classModle : ''}`
      }
    },
    {
      title: _this.$t('txt_upload_model_file'),
      type: 'MCAD',
      //上传api
      upload: () => {
        return ModelFactory.create({
          url: `${Jw.gateway}/${Jw.cadService}/mcad/importIncludeFile`,
          method: 'post',
        });
      },
      //下载模板
      download: (type, classModle) => {
        return `${Jw.gateway}/${Jw.cadService}/mcad/exportTemplateIncludeFile?name=${type ? type : 'MCAD'}&clsOid=${classModle ? classModle : ''}`
      }
    },
    {
      title: _this.$t('批量上传'),
      type: 'PartBatch',
      //是否是选择多个字属性
      multiple: true,
      //上传api
      upload: () => {
        return ModelFactory.create({
          url: `${Jw.gateway}/${Jw.partBomMicroServer}/part-export/importIncludeFileBatch`,
          method: 'post',
        });
      },
      //下载模板
      download: (typeList) => {
        return `${Jw.gateway}/${Jw.partBomMicroServer}/part-export/exportTemplateIncludeFileBatch?data=${encodeURIComponent(JSON.stringify(typeList))}`
      }
    },
    {
      title: _this.$t('批量上传'),
      type: 'deliverImport',
      //是否是选择多个字属性
      multiple: true,
      //上传api
      upload: () => {
        return ModelFactory.create({
          url: `${Jw.gateway}/${Jw.customerServer}/deliveryReport/importDelivery`,
          method: 'post',
        });
      },
      //下载模板
      download: () => {
          ModelFactory.create({
            url: `${Jw.gateway}/${Jw.customerServer}/deliveryReport/importDeliveryTemplate`,
            method: 'get',
            responseType: "blob"
          }).execute().then(blob => {
            let url = window.URL.createObjectURL(new Blob([blob]))
            let link = document.createElement('a')
            link.href = url
            link.style.display = 'none'
            link.setAttribute('download', "交付清单导入模板.xlsx")
            document.body.appendChild(link)
            link.click()
            link.remove()
        })
      }
    },
    {
      title: _this.$t('批量上传'),
      type: 'PartTemp',
      //是否是选择多个字属性
      multiple: true,
      //上传api
      upload: () => {
        return ModelFactory.create({
          url: `${Jw.gateway}/${Jw.customerServer}/${Jw.partBomMicroServer}/part-export/importExcelTemp`,
          method: 'post',
        });
      },
      //下载模板
      download: (typeList) => {
        downloadTempFile('partExcelTemplate')
      }
    },
    {
      title: _this.$t('批量上传'),
      type: 'DocumentTemp',
      //是否是选择多个字属性
      multiple: true,
      //上传api
      upload: () => {
        return ModelFactory.create({
          url: `${Jw.gateway}/${Jw.customerServer}/${Jw.docMicroServer}/document/importExcelTemp`,
          method: 'post',
        });
      },
      //下载模板
      download: (typeList) => {
        downloadTempFile('documentExcelTemplate')
      }
    },
    {
      title: _this.$t('批量上传'),
      type: 'EcadTemp',
      //是否是选择多个字属性
      multiple: true,
      //上传api
      upload: () => {
        return ModelFactory.create({
          url: `${Jw.gateway}/${Jw.cadService}/ecad/importExcelTemp`,
          method: 'post',
        });
      },
      //下载模板
      download: (typeList) => {
        downloadTempFile('ecadExcelTemplate')
      }
    },
    {
      title: _this.$t('上传导入文件'),
      type: 'PartLink',
      //是否是选择多个字属性
      multiple: false,
      //上传api
      upload: () => {
        return ModelFactory.create({
          url: `${Jw.gateway}/${Jw.customerServer}/common/importPartLink`,
          method: 'post',
        });
      },
      download: (typeList) => {
        downloadTempFile('product','关联关系导入模板')
      }
    }
  ]
  let res = list.find(item => item.type === type)
  if(!res){
    console.error("为实现类型：" + type)
  }
  return res
}
