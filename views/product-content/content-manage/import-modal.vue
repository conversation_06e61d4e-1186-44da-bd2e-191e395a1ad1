<template>
  <a-modal
    :dialogStyle="{ top: '160px', marginRight: '76px' }"
    :bodyStyle="{padding: '0 24px 16px'}"
    :title="$t('txt_import_part')"
    :mask="false"
    :visible="visible"
    :confirm-loading="confirmLoading"
    @ok="onSubmit"
    @cancel="onCancel"
  >
    <div class="upload-wrap">
      <a-form-model ref="ref_model_form" :model="modelData">
        <a-upload-dragger
          class="upload-wrap"
          accept=".xls, .xlsx"
          :fileList="files"
          :multiple="true"
          :beforeUpload="beforeUpload"
          :remove="onRemoveFile"
        >
          <div class="appendixs-label">
            <jw-icon type="#jwi-shangzhuanwenjian"></jw-icon>
            <div>
              <span
              >{{ $t("txt_feil_drop") }}
              <span class="upload-btn">
                {{ $t("txt_click_upload") }}</span
              ></span
            >
              <div>({{ $t("txt_feil_size_20") }})</div>
            </div>
          </div>
        </a-upload-dragger>
      </a-form-model>
    </div>
    <template slot="footer">
      <div style="float: left">
        <a-button
          style="padding-left: 0"
          key="submit"
          type="link"
          @click="$emit('showDown')"
        >
          {{ $t("btn_download") + $t("txt_part") + $t("txt_doc_temp") }}
        </a-button>
      </div>
      <div style="float: right">
        <a-button key="cancel" @click="onCancel"
          >{{ $t("btn_cancel") }}
        </a-button>
        <a-button
          key="submit"
          type="primary"
          :loading="confirmLoading"
          @click="onSubmit"
        >
          {{ $t("btn_ok") + $t("btn_import") }}
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
// part导入数据
const uploadPartList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part-export/upload`,
  method: "post",
});

export default {
  name: "importModal",
  props: ["visible", "currentTree", "showDown"],
  data() {
    return {
      file: "",
      files: [],
      exportLoading: false,
      confirmLoading: false,
      fileData: {},
      fileName: undefined,
      modelCode: "",
      modelData: {
        activeModle: undefined,
        classModle: undefined,
        fileName: undefined,
      },
      hasSubModel: false,
      subModelOptions: [],
      treeData: [],
      replaceFields: {
        children: "children",
        title: "displayName",
        key: "oid",
        value: "oid",
      },
    };
  },
  watch: {
    visible(val) {
      // console.log("val", val);
      if (val) {
        this.$refs.ref_model_form && this.$refs.ref_model_form.resetFields();
        this.modelData = {
          activeModle: undefined,
          classModle: undefined,
          fileName: undefined,
        };
        this.files = [];
      }
    },
  },
  methods: {
    onCancel() {
      this.fileName = "";
      this.fileData = {};
      this.files = [];
      this.$emit("close");
    },
    beforeUpload(file) {
      let isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message({
          message: this.$t("txt_feil_size_20"),
          type: "error",
        });
        return false;
      }
      this.files = [file];
      return false;
    },
    onRemoveFile(file, fileList) {
      console.log(file);
      console.log(fileList);
      let { files } = this;
      this.files = [].concat(files.filter((item) => item.name != file.name));
    },
    onSubmit() {
      let { treeData, modelData, $route, currentTree, files } = this;
      // 校验是否选择文件
      console.log(files);
      if (files.length === 0) return this.$error(this.$t("txt_please_file"));
      let partImport = {
        catalogOid: currentTree.oid,
        catalogType: currentTree.type,
        containerOid: currentTree.containerOid,
        containerType: currentTree.containerType,
        code: "Part",
      };
      let formData = new FormData();
      formData.append("file", files[0]);
      formData.append("partImport", JSON.stringify(partImport));
      this.confirmLoading = true;
      console.log(formData);
      uploadPartList
        .execute(formData)
        .then((res) => {
          console.log(res);
          this.$success(this.$t("txt_import_success"));
          this.confirmLoading = false;
          this.onCancel();
          this.$emit("getList", currentTree);
        })
        .catch((err) => {
          this.confirmLoading = false;
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
  },
};
</script>

<style lang="less" scoped>
.file-name-input {
  margin-right: 10px;
  color: rgba(0, 0, 0, 0.65);
  /deep/ &.ant-input {
    background: #fff;
  }
}
/deep/ .ant-select-tree {
  height: 200px;
  overflow: scroll;
}
.appendixs-label {
  display: flex;
  align-items: center;
  margin-left: 16px;
  text-align: left;
}
.jw-icon {
  font-size: 32px;
  margin-right: 8px;
}
</style>
