<template>
  <a-tree-select v-model="catalogItem.catalogOid" :disabled="disabled" :tree-data="catalogTreeData" show-search
                 :placeholder="$t('msg_select')" allowClear @change="onSelectCatalog" :filter-tree-node="filterTreeNode"
                 :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }">
    <template slot="folderIconTitle" slot-scope="{ name, childType }">
      <div>
        <jw-icon style="margin-right: 8px" :type="
                      childType === 'child'
                        ? '#jwi-wenjianga-youneiyong'
                        : '#jwi-chanpin'
                    "/>
        <span>{{ name }}</span>
      </div>
    </template>
  </a-tree-select>
</template>
<script>
import {findItem} from "utils/util";

import ModelFactory from "jw_apis/model-factory";
// 文件夹目录
const fetchfolderTree = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/folder/searchTree`,
  method: "get"
});

export default {
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    locationInfo: {
      type: Object,
      default: () => ({})
    },
    activeCatalog: String
  },
  data() {
    this.initCatalogTreeData = [];
    return {
      catalogTreeData: [],
      catalogItem: {catalogOid: undefined}
    };
  },
  created() {
    // this.getFolderTree();
  },
  watch: {
    "locationInfo.containerModelDefinition": {
      immediate: true,
      handler(val) {
        if (val) {
          this.getFolderTree();
        }
      }
    }
  },
  methods: {
    getFolderTree() {
      let { query } = this.$route
      fetchfolderTree
          .execute({
            containerModel: this.locationInfo.containerModelDefinition,
            containerOid: this.locationInfo.containerOid != null ? this.locationInfo.containerOid : query.oid
          })
          .then(res => {
            this.initCatalogTreeData = res;
            this.catalogTreeData = this.recursive(res);
            this.catalogItem.catalogOid = this.locationInfo.catalogOid;
          })
          .catch(err => {
            this.$error(err.msg);
          });
    },
    recursive(data) {
      let simpleData = [];
      const loop = (tree, simpleData) => {
        tree.map(item => {
          let node = {};
          simpleData.push(node);
          node.oid = item.oid;
          node.key = item.oid;
          node.value = item.oid;
          node.name = item.name;
          node.scopedSlots = {title: "folderIconTitle"};
          if (item.children && item.children.length > 0) {
            node.children = [];
            loop(item.children, node.children);
            node.children.map(item => (item.childType = "child"));
          }
        });
      };
      loop(data, simpleData);
      // console.log(simpleData,"文件夹结构")
      return simpleData;
    },
    onSelectCatalog(val) {
      if (!val) {
        this.catalogItem = {};
      } else {
        let item = findItem(this.initCatalogTreeData, val);
        this.catalogItem.catalogOid = val;
        this.catalogItem.catalogType = item.type;
        this.catalogItem.containerOid = item.containerOid;
        this.catalogItem.containerType = item.containerType;
        this.catalogItem.containerModelDefinition =
            item.containerModelDefinition;
      }

      this.$emit("update:locationInfo", this.catalogItem);
      this.$emit("update:activeCatalog", val);
    },
    filterTreeNode: (value, treeNode, field = "name") => {
      const title = treeNode.componentOptions.propsData.dataRef[
          field
          ].toLowerCase();
      return title.indexOf((value || "").trim().toLowerCase()) > -1;
    }
  }
};
</script>

