<template>
  <div class="card-list">
    <a-row v-if="tableList.length" :gutter="16">
      <div class="card-area-list">
        <a-col class="gutter-row card-item" style="padding-left: 0" :span="6" v-for="(item, key) in tableList" :key="key">
          <div class="card-content">
            <ul class="item-info">
              <li class="item-title">
                <div class="item-title-name" @click="gotoDetails(item)">
                  <a-tooltip>
                    <template slot="title">
                      {{ item.lockOwnerAccount }} {{ $t("txt_check_out") }}
                    </template>
                    <jw-icon v-if="item.lockOwnerOid" :type="item.lockOwnerOid === jwUser.oid
                        ? '#jwi-beiwojianchu'
                        : '#jwi-bierenjianchu'
                      " />
                  </a-tooltip>
                  <jwIcon :type="item.modelDefinition == 'CADDrawing' ||
                      item.modelDefinition == 'PCB' ||
                      item.modelDefinition == 'Schematic'
                      ? '#jwi-lianjian'
                      : item.modelIcon
                    " />
                  <a-tooltip :title="item.number">
                    {{ $t("txt_number_of") }}：{{ item.number && item.number.slice(0, 30) }}
                  </a-tooltip>

                </div>
                <div class="item-more">
                  <operation-dropdown :current-record="item" :currentTree="currentTree" :treeName="treeName"
                    :containerOid="$route.query.oid" :containerModel="$route.query.modelDefinition"
                    :containerType="$route.query.type" @batchOperator="batchOperator" @complete="initData"
                    @fetchTable="initData" />
                </div>
              </li>
              <li class="item-productCatalogName" @click="gotoDetails(item)">
                <a-tooltip>
                  <template slot="title"> {{ $t(item.modelDefinition) }} </template>
                  {{ $t("txt_type") }}：
                  <span>{{ $t(item.modelDefinition) }}</span>
                </a-tooltip>
              </li>
              <li class="item-productCatalogName" @click="gotoDetails(item)">
                <a-tooltip>
                  <template slot="title"> {{ item.name }} </template>
                  {{ $t("txt_name") }}：
                  <span v-html="mixContentName(item.name)"></span>
                </a-tooltip>
              </li>
              <li class="item-productCatalogName">
                <a-tooltip>
                  <template slot="title"> {{ $t(item.lifecycleStatus) }} </template>
                  {{ $t("txt_plan_lifecycle") }}：
                  <span>{{ $t(item.lifecycleStatus) }}</span>
                </a-tooltip>
              </li>
              <li class="item-productCatalogName" @click="gotoDetails(item)">
                <a-tooltip>
                  <template slot="title"> {{ item.displayVersion }} </template>
                  {{ $t("txt_plan_version") }}：
                  <span>{{ item.displayVersion }}</span>
                </a-tooltip>
              </li>
              <li @click="gotoDetails(item)">
                等级：
                <a-tag v-if="item.preferenceLevel && levelMap[item.preferenceLevel]"
                  :color="levelMap[item.preferenceLevel].color">
                  {{ levelMap[item.preferenceLevel].name }}
                </a-tag>
              </li>
              <li class="item-thumbnail">
                <div class="thumb-view" :title="$t('txt_filePreview')" @click="onPreview(item)" v-if="item.thumbnailOid &&
                  item.modelDefinition == 'CADDrawing' ||
                  item.modelDefinition == 'PCB' ||
                  item.modelDefinition == 'Schematic'
                  ">
                  <jwIcon type="#jwi-PDFwendang" />
                </div>
                <cad-thumbnail v-if="(item.thumbnailOid || (item.extensionContent
                    && item.extensionContent.thumbnailFile
                    && item.extensionContent.thumbnailFile.length
                    && item.extensionContent.thumbnailFile[0].oid)) &&
                  (item.modelDefinition != 'CADDrawing' &&
                    item.modelDefinition != 'PCB' &&
                    item.modelDefinition != 'Schematic')
                  " :currentRow="item" thumbPosition="right"></cad-thumbnail>
              </li>
            </ul>
          </div>
        </a-col>
      </div>
    </a-row>
    <div v-else class="empty-text">暂无数据</div>
    <a-pagination class="card-list-pagination" :show-total="(total) => $t('txt_total') + ` ${total}` + $t('txt_pieces')"
      :total="pagination.total" :page-size="pagination.pageSize" :default-current="pagination.current"
      :pageSizeOptions="pageSizeOptions" show-size-changer @change="onPageChange" @showSizeChange="onSizeChange" />
  </div>
</template>
<script>
//接口
import ModelFactory from "jw_apis/model-factory";
import {
  jwSimpleTabs,
  jwAvatar,
  jwIcon
  // jwToolbar,
  // jwPage,
} from "jw_frame";
import { formatDate } from "jw_utils/moment-date";
import operationDropdown from "components/operation-dropdown";
import cadThumbnail from "components/cad-thumbnail.vue";
import rules from "../../../utils/rules.js";
import commonStore from "jw_stores/common";

import {
  fetchContainerList,
  createContainer,
  fetchSpectrumTree,
  deleteContainer
} from "apis/product-container";

// 获取查询当前角色是否有新建权限
const createRole = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
  method: "post"
});

// 获取该条数据可操作下拉列表
const getDropdownList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
  method: "post"
});

const previewApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.fileMicroServer}/file/getUrlByOidForPreview`,
  method: "get"
});

export default {
  components: {
    jwSimpleTabs,
    jwAvatar,
    jwIcon,
    operationDropdown,
    cadThumbnail
  },
  inject: ["setBreadcrumb", "addBreadcrumb"],
  props: {
    tableData: {
      default: () => [],
      type: Array
    },
    treeName: {
      default: "",
      type: String
    },
    pagination: {
      default: {
        current: 1,
        pageSize: 20,
        total: 0
      },
      type: Object
    },
    currentTree: {
      default: {},
      type: Object
    }
  },
  watch: {
    tableData(val) {
      console.log(val);
      this.tableList = [..._.cloneDeep(val)];
    },
    currentTree(val) {
      console.log(val);
      this.currentTreeData = { ...val };
    }
  },
  data() {
    this.levelMap = {
      优选: {
        name: "优选",
        color: "green"
      },
      非优选: {
        name: "非优选",
        color: "orange"
      },
      禁选: {
        name: "禁选",
        color: "red"
      }
    };

    return {
      pageSizeOptions: ["10", "20", "50", "100"],
      currentTreeData: {},
      tableList: [],
      jwUser: Jw.getUser(),
      currentCreateRole: false,
      // 另存为模板处理
      saveAsmodalVisible: false,
      saveAsParams: {
        name: ""
      },
      rules,
      confirmText: this.$t("txt_comfrim_save"),
      productRow: {},
      // 数据处理
      flatData: [],
      // 表单弹窗
      containerTemplateOid: "",
      formModalVisible: false,
      formModalData: [
        {
          label: this.$t("txt_name"),
          type: "input",
          prop: "name",
          block: true
        },
        {
          label: this.$t("txt_spectrum"),
          type: "tree",
          prop: "spectrum",
          // value: undefined, // 默认值
          treeData: []
        },
        {
          label: this.$t("txt_team_temp"),
          type: "select",
          prop: "team",
          value: [],
          options: [],
          required: false
        },
        {
          label: this.$t("txt_isPrivate"),
          type: "radio",
          prop: "privateFlag",
          value: true,
          radios: [
            { label: this.$t("txt_yes"), value: true },
            { label: this.$t("txt_no"), value: false }
          ],
          required: false
        }
      ],
      templateVisible: false, // 模板选择模态框
      switchType: true,
      // tab选项
      tabValue: "product",
      tabOption: [
        { value: "product", label: this.$t("txt_product_rq") }
        // { value: "resource", label: "我收藏的" },
        // { value: "provider", label: "我私有的" },
      ],
      deleteModal: false,
      searchKey: "",
      selectedRows: [],
      confirmLoadingStatus: false,
      tableLoading: false,
      total: 0,
      selectRow: []
    };
  },
  computed: {
    getHeader() {
      return [
        {
          field: "name",
          title: this.$t("txt_product_name"),
          sortable: true, // 开启排序
          slots: {
            default: "productContent"
          }
        },
        {
          field: "description",
          title: this.$t("txt_description"),
          sortable: true // 开启排序
        },
        {
          field: "productCatalogName",
          title: this.$t("txt_subordinate_spectrum"),
          sortable: true // 开启排序
        },
        {
          field: "privateFlag",
          title: this.$t("txt_isPrivate"),
          width: "120",
          sortable: true, // 开启排序
          slots: {
            default: "privateSlot"
          }
        },
        {
          field: "updateBy",
          title: this.$t("txt_product_manager"),
          width: "180",
          sortable: true, // 开启排序
          slots: {
            default: "productManagerSlot"
          }
        },
        {
          field: "createdTime",
          title: this.$t("txt_create_date"),
          sortable: true, // 开启排序
          slots: {
            default: "creatdTime"
          }
        },
        {
          // 操作列定义
          field: "operation", //关键字
          slots: {
            default: "operation"
          },
          maxShowBtn: 1,
          btns: [
            {
              icon: "jwi-iconwarning-circle-full",
              title: this.$t("txt_detail"),
              key: "detail"
            },
            // {
            //   icon: "jwi-iconstar-full",
            //   title: "收藏",
            //   key: "star",
            // },iconuser-add
            {
              icon: "jwi-iconsave-as",
              title: this.$t("txt_save_template"),
              key: "saveAs"
            },
            {
              icon: "jwi-iconuser-add",
              title: this.$t("page_team_management"),
              key: "team"
            }
            // {
            //   icon: "jwi-icondelete",
            //   title: "删除",
            //   key: "delete",
            // },
          ]
        }
      ];
    },
    toolbars() {
      return [
        {
          name: this.$t("btn_new_create"),
          position: "before",
          type: "primary",
          key: "create"
        },
        {
          name: this.$t("btn_search"),
          position: "before",
          display: "input",
          value: this.searchKey,
          allowClear: false,
          placeholder: this.$t("search_text"),
          prefixIcon: "jwi-search",
          key: "search"
        },
        // {
        //   name: "批量操作",
        //   display: "dropdown",
        //   position: "after",
        //   key: "batch",
        //   menuList: [
        //     {
        //       name: "删除",
        //       key: "delete",
        //     },
        //   ],
        // },
        {
          name: "",
          position: "after",
          key: "switch",
          prefixIcon:
            this.switchType === true
              ? "jwi-iconlogo-windows-filled"
              : "jwi-iconlist"
        },
        {
          name: this.$t("btn_import"),
          position: "after",
          type: "",
          key: "import"
        },
        {
          name: this.$t("btn_export"),
          position: "after",
          type: "",
          key: "export"
        }
      ];
    }
  },

  created() {
    // 输入回调去抖动
    this.delaySearch = _.debounce(this.onSearch, 500);
  },

  methods: {
    initData(key) {
      this.$emit("reloadData", key);
    },
    onPreview(row) {
      previewApi
        .execute({
          fileOid: row.thumbnailOid
        })
        .then(url => {
          commonStore.set("query", url);
          window.open("#/preview", "_blank");
        })
        .catch(err => {
          if (err.code === -1) {
            this.$error(this.$t("没有可预览的文件"));
          }
        })
        .finally(() => { });
    },
    mixContentName(name) {
      console.log("当前名称", name);
      let lastIndexOf = name.lastIndexOf(".");
      if (lastIndexOf === -1 || lastIndexOf <= 12) {
        return name;
      } else {
        return `${name.substring(
          0,
          6
        )}<b style='color:rgb(37, 94, 215)'>......</b>${name.substring(
          lastIndexOf - 5,
          name.length
        )}`;
      }
      console.log(lastIndexOf);
    },
    gotoDetails(row) {
      Jw.jumpToDetail(row, { tabActive: "info" });
    },
    // 时间格式化转换
    formatDateFn(date) {
      return formatDate(date);
    },
    // 获取 row  part 操作下拉列表
    dropdownVisibleChange(visible, row) {
      if (visible && !row.operationList) {
        this.$set(row, "loading_status", "loading");
        getDropdownList
          .execute({
            type: row.type,
            viewCode: "PRODUCTINSTANCE",
            contextType: "Container",
            objectType: row.masterType,
            objectOid: row.oid,
            contextOid: this.$route.query.oid,
            state: row.lifecycleStatus,
            lockOwnerOid: row.lockOwnerOid,
            version: `${row.version}.${row.iteratedVersion}`,
            levelForSecrecy: row.levelForSecrecy
          })
          .then(data => {
            this.$set(row, "operationList", data);
            this.$set(row, "loading_status", "done");
          })
          .catch(err => {
            this.$error(err.msg || this.$t("msg_failed"));
            this.$set(row, "loading_status", "failed");
          });
      }
    },
    // 查询新建权限
    checkCreateRole() {
      let param = {
        viewCode: "PRODUCTOPERATION",
        state: "Inwork",
        objectType: "Container",
        contextType: "Container"
      };
      createRole
        .execute(param)
        .then(data => {
          console.log("权限查询返回结果", data);
          this.currentCreateRole = data[0].status === "disable" ? false : true;
        })
        .catch(err => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    // 另存为模板
    submitSaveAs(data) {
      console.log("data", data);
      let { productRow } = this;
      let param = {
        containerOid: productRow.oid,
        templateName: data.templateName,
        description: data.description,
        containerType: productRow.type,
        containerOid: productRow.oid,
        isAddPer:data.isAddPer?true:false
      };
      return ModelFactory.create({
        url: `${Jw.gateway}/customer/customerContainer/saveAsTemplate`, ///${Jw.containerService}/container/product/saveAsTemplate
        method: "post"
      })
        .execute(param)
        .then(data => {
          console.log(data);
          this.saveAsmodalVisible = false;
          this.$success(this.$t("msg_success"));
          //刷新列表
          this.fetchTable({ current: 1, pageSize: 10 });
          this.$refs["choose-template"].getContainerTemplate();
        })
        .catch(err => {
          this.tableLoading = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    // 递归添加value值
    dataHandle(data) {
      let _this = this;
      data.value = data.oid;
      data.title = data.name;
      data.key = data.oid;
      if (data.children && data.children.length > 0) {
        for (let j = 0; j < data.children.length; j++) {
          _this.dataHandle(data.children[j]);
        }
      } else {
        data.value = data.oid;
        data.title = data.name;
        data.key = data.oid;
      }
      return data;
    },
    // 获取型谱下拉树数据
    fetchfetchSpectrumList() {
      let _this = this;
      let param = {
        masterOid: "111",
        masterType: "ProductSpectrumRootNode"
      };
      fetchSpectrumTree
        .execute({ current: 1, pageSize: 500, ...param })
        .then(result => {
          console.log(this);
          let deepResult = _this.dataHandle(result);
          // console.log("递归后的数据", deepResult);
          // console.log("初始化产品型谱列表", result);
          // console.log("当前数据类型", this.formModalData);
          this.formModalData[1].treeData = [deepResult];
        })
        .catch(err => {
          console.log(err);
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    formModalConfirm(model) {
      console.log("当前新建表单输入信息", this.formModalData[1].treeData[0]);
      let { containerTemplateOid, formModalData } = this;
      // 组织数据扁平化 根据oid查询当前数据
      let param = {
        ...model,
        productManagerAccount: true,
        containerTemplateOid: containerTemplateOid
      };
      console.log("新增产品容器参数信息", param);
      createContainer
        .execute(param)
        .then(data => {
          console.log("新增提交返回信息", data);
          this.$success(this.$t("msg_save_success"));
          this.formModalVisible = false;
          this.fetchTable({ current: 1, pageSize: 10 });
          this.$refs["create-modal"].handleCancel();
        })
        .catch(err => {
          this.tableLoading = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    // 打开模板选择模态框
    showChoose(flag) {
      console.log(flag);
      this.templateVisible = flag;
      this.$refs["choose-template"].getContainerTemplate();
    },
    // 选择模板后续处理，打开新建产品容器模态框
    chooseAfter(dataSource) {
      console.log(dataSource);
      let { oid } = dataSource;
      this.containerTemplateOid = oid;
      this.templateVisible = false;
      this.formModalVisible = true;
      this.fetchfetchSpectrumList();
      this.$refs["create-modal"].handleShow();
    },
    handleSwitchType() {
      this.switchType = !this.switchType;
    },
    tabChange(option) {
      console.log(option);
      let { value } = option;
      this.tabValue = value;
      this.fetchTable({ current: 1, pageSize: 10 });
    },
    // 选择列回调
    onSelectChange(args) {
      // console.log(args);
      this.selectRow = args;
    },
    // 操作列回调
    onOperateClick(key, row) {
      this.$emit("rowOperation", key, row);
    },
    // 工具栏点击回调
    onToolClick({ key, row }) {
      if (key === "create") {
      } else if (key === "switch") {
        // 切换列表
        this.switchType = !this.switchType;
      } else if (key === "delete") {
        // console.log("this.selectedRows", this.selectedRows);
        this.fetchDelete(this.selectedRows);
      }
    },
    // 工具栏输入回调
    onToolInput({ key }, value) {
      // console.log(value);
      if (key === "search") {
        this.searchKey = value;
        this.delaySearch();
      }
    },
    // 删除
    onDelete(row) {
      this.fetchDelete([row]);
    },
    // 数据请求函数
    fetchTable({ current, pageSize }) {
      // let { tabValue, searchKey } = this;
      // let param = {
      //   index: 1,
      //   size: 100,
      //   searchKey,
      // };
      // this.tableLoading = true;
      // return fetchContainerList
      //   .execute(param)
      //   .then((data) => {
      //     console.log(data);
      //     this.tableLoading = false;
      //     this.tableData = data.rows;
      //     return { data: data.rows.reverse(), total: data.count };
      //   })
      //   .catch((err) => {
      //     this.tableLoading = false;
      //     this.$error(err.msg || this.$t("msg_failed"));
      //   });
    },
    initBreadcrumb() {
      let breadcrumbData = [{ name: this.$t("txt_product_rq"), path: "" }];
      this.setBreadcrumb(breadcrumbData);
    },

    batchOperator(record, type) {
      this.$emit("batchOperator", record, type);
      // this.selectedRows = [record];
      // this.$nextTick(() => {
      //   this.$refs["batch-operator"].validSelect(type);
      // });
    },
    // 删除列操作
    fetchDelete(row) {
      // console.log("当前删除操作的数据", row);
      let { tabValue } = this;
      let param = row.map(item => item.oid);
      this.$confirm({
        width: "280px",
        class: "deleteModal",
        closable: true,
        mask: false,
        title: (
          <p style="font-size:16px;font-weight:500;color:rgba(30, 32, 42, 0.85);">
            {this.$t("txt_delete")}
          </p>
        ),
        content: (
          <p style="font-size:14px;font-weight:400;color:rgba(30, 32, 42, 0.45);">
            {this.$t("txt_is_delete_rq")}
          </p>
        ),
        cancelText: this.$t("btn_cancel"),
        okText: this.$t("btn_ok"),
        onOk: () => {
          deleteContainer
            .execute(param)
            .then(data => {
              // console.log(data);
              this.$success(this.$t("txt_delete_success"));
              //刷新列表
              this.fetchTable({ current: 1, pageSize: 10 });
            })
            .catch(err => {
              this.$error(err.msg || this.$t("msg_failed"));
            });
        }
      });
    },
    // 输入回调刷新表格数据
    onSearch() {
      this.fetchTable({ current: 1, pageSize: 10 });
    },
    onPageChange(page, pageSize) {
      this.$emit("onPageChange", page, pageSize);
    },
    onSizeChange(current, size) {
      this.$emit("onSizeChange", size, current);
    }
  }
};
</script>

<style lang="less" scoped>
.product-container {
  padding: 0;
  box-shadow: none;
}

.card-area-list{
  height: calc(100vh - 215px);
  overflow-y: auto;
}
.ant-spin-container .card-list {
  .ant-row {
    margin-right: 0 !important;
    overflow: auto;
  }

  .empty-text {
    height: 100%;
    text-align: center;
    line-height: 200px;
    color: rgba(30, 32, 42, 0.25);
    font-size: 20px;
  }
}

.card-list-pagination {
  text-align: right;
  padding: 10px 0;
  position: absolute;
  right: 0;
  bottom: 0;
}

.product-list {
  padding: 20px;
}

.product-filter {
  padding: 20px;
  height: 36px;
  margin-bottom: 4px;
}

.no-data-wrap {
  height: 100%;

  .no-data-con {
    height: calc(~"100vh - 130px");
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
      margin-bottom: 25px;
    }

    span {
      color: #255ed7;
      cursor: pointer;
    }

    i {
      cursor: pointer;
    }
  }
}

/deep/.form-model-item {
  &:nth-of-type(2) {
    margin-right: 30px;
  }
}

// 卡片样式
.card-item {
  border: 8px solid #fff;

  .card-content {
    height: 153px;
    padding: 16px;
    background: rgba(30, 32, 42, 0.04);
    border: 1px solid rgba(30, 32, 42, 0.04);
    border-radius: 4px 4px 4px 4px;

    &:hover {
      background: #f0f7ff;
      border: 1px solid #a4c9fc;
      cursor: pointer;
    }

    .item-info {
      margin: 0;
      position: relative;

      .item-title {
        position: relative;
        display: inline-block;
        width: 100%;
        color: rgba(30, 32, 42, 0.85);

        .item-title-name {
          width: 86%;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          word-break: break-all;
          font-weight: 500;
          font-size: 14px;
          color: rgba(30, 32, 42, 0.85);
          cursor: pointer;
        }

        .item-name {
          margin-left: 8px;
          font-weight: 500;
        }

        .item-more {
          position: absolute;
          right: 5px;
          top: 0px;
          z-index: 10;
        }
      }

      .item-description {
        height: 26px;
        line-height: 26px;
        font-size: 12px;
        color: rgba(30, 32, 42, 0.45);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .item-productCatalogName {
        height: 22px;
        line-height: 22px;
        font-size: 12px;
        font-weight: 500;
        color: #1e202a;

        >span {
          display: flex;
          width: 100%;

          >span {
            width: 20px;
            flex-grow: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }

      .item-thumbnail {
        position: absolute;
        right: 0px;
        bottom: 0px;
        width: 4vw;
        height: 4vw;
      }

      .item-avatar {
        margin-top: 4px;
        height: 40px;

        .avatar {
          float: left;
        }

        .private {
          line-height: 40px;
          float: right;
        }
      }
    }
  }
}

// tab页签样式
.wrap-class {
  /deep/.item-class {
    margin: 0 20px 0 0;
    padding: 4px 0;
    color: rgba(30, 32, 42, 0.65);
  }

  /deep/.item-class-active {
    font-size: 14px;
    font-weight: 500;
    color: rgba(30, 32, 42, 0.85);
  }
}

.table-content {
  padding: 20px;
  height: 100%;
  background-color: #fff;
}

.openBtn {
  float: right;
  margin: 10px 20px 0 0;
}

.delteIcon {
  cursor: pointer;
}
</style>
<style>
.deleteModal .ant-modal-body {
  padding: 24px;
}

.deleteModal .ant-modal {
  /* top: 112px;
  left: 42%; */
}

.deleteModal .ant-modal-close-x {
  line-height: 69px;
}

.deleteModal .ant-modal-confirm-body>.anticon+.ant-modal-confirm-title+.ant-modal-confirm-content {
  margin-left: 0;
}

.deleteModal .ant-modal-confirm-btns .ant-btn {
  width: 75px;
  float: right;
}

.deleteModal .ant-modal-confirm-btns .ant-btn.ant-btn-primary {
  margin-right: 8px;
  /* background-color: rgba(37, 94, 215, 1);
  border-color: rgba(37, 94, 215, 1); */
  /* background-color: #1890ff; */
}

.operation-dropdown-overlay {
  /* width: 150px; */
}

.thumb-view {
  display: flex;
  justify-content: center;
  font-size: 25px;
  cursor: pointer;
  height: 100%;
  align-items: center;
}</style>
