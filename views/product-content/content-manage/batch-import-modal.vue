<template>
    <jw-base-color-modal
        :title="epart ? $t('txt_import_epart') : $t('txt_import_document')"
        :visible.sync="visible"
        :ok-btn-loading="saveLoading"
        :ok-text="totalcount +' ' + $t('btn_ok') + $t('btn_import')"
        :cancel-text="$t('btn_cancel')"
        :dialogClass="'dialog-class'"
        :mask="false"
        @ok="onSave"
        @cancel="onClose"
    >
        <div class="btn-wrap">
            <a-upload-dragger
                name="file"
                :accept="'.zip'"
                action="string"
                :showUploadList="false"
                :customRequest="onUpload"
            >
            <div class="appendixs-label">
                <jw-icon type="#jwi-shangzhuanwenjian"></jw-icon>
                <div>
                    <span>{{ $t("txt_feil_drop") }}
                        <span class="upload-btn">{{ $t("txt_click_upload") }}</span>
                    </span>
                    <div>({{ $t("txt_feil_size_1000") + ',' + $t("txt_uplaod_type") + '.zip' }})</div>
                </div>
            </div>
            </a-upload-dragger>

            <download-modal
                :visible="visibleDown"
                :objectType="epart ? 'Part' : 'Document'"
                :currentTree="currentTree"
                :multiple="false"
                :downloadUrl="epart ? epartdownloadUrl : null"
                :epart="epart"
                :contentClsData="contentClsData"
                @close="visibleDown = false"
                @getList="fetchFold"
                @downbatchdown="startDownLoad"
                >
            </download-modal>
        </div>
        <div v-if="isShowTable">
            <jw-table ref="refTable"
                style="margin-top: 10px;"
                :panel="false"
                :toolbars="toolbars"
                :columns="columns"
                :height="470"
                :data-source.sync="tableData"
                :showPage="false"
            >
            </jw-table>
        </div>
        <template #footer-before-btn>
            <a-button type="link"
                style="float:left;padding: 0;"
                :loading="downloadLoading"
                @click="visibleDown = true"
            >
                {{ $t("btn_download") + $t("txt_doc_temp") }}
            </a-button>
        </template>
    </jw-base-color-modal>
</template>

<script>
import { jwBaseColorModal } from 'jw_frame';
import ModelFactory from 'jw_apis/model-factory';
import { getCookie } from 'jw_utils/cookie';
import downloadModal from "./download-modal.vue";
const testImportExcel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.docMicroServer}/excel/testImportExcel`,
    method: 'post',
});

const importExcel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.docMicroServer}/excel/importExcel`,
    method: 'post',
});

//ePart
const epartTestImportExcel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/epart/checkImportExcel`,
    method: 'post',
});

const epartImportExcel = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/epart/batchImport`,
    method: 'post',
});

export default {
    name: 'batchImportModal',
    components: {
        jwBaseColorModal,downloadModal
    },
    props: [
        'visible',
        'currentTree',
        'epart',
        'contentClsData'
    ],
    data() {
        return {
            //epart下载模板
            epartdownloadUrl: `${Jw.gateway}/${Jw.docMicroServer}/epart/exportTemplate`,
            file: null,
            isShowTable: false,
            tableData: [],
            downloadLoading: false,
            testLoading: false,
            saveLoading: false,
            totalcount: '',

            visibleDown: false,
        };
    },
    computed: {
        toolbars() {
            return []
        },
        columns() {
            return [
                {
                    field: 'column',
                    title: this.$t('txt_file_name'),
                },
                {
                    field: 'line',
                    title: this.$t('txt_line_number'),
                },
                {
                    field: 'msg',
                    title: this.$t('txt_error_msg'),
                },
            ];
        },
    },
    methods: {
        startDownLoad(val, clas){
            const { activeModle, classModle } = val
            console.log("下载属性",val)
            this.onDownloadTemp(activeModle, classModle)
        },
        fetchFold(){
        },
        onDownloadTemp(type, classModle) {
            this.downloadLoading = true;
            let url = `${Jw.gateway}/${Jw.docMicroServer}/excel/exportExcel?name=${type ? type : 'Document'}&clsOid=${classModle ? classModle : ''}`

            if(this.epart){
                url = `${Jw.gateway}/${Jw.partBomMicroServer}/epart/exportTemplate?name=${type ? type : 'Document'}&clsOid=${classModle ? classModle : ''}`
            }

            fetch(url, {
                method: 'get',
                headers: {
                    'Content-Type': 'application/json;charset=utf8',
                    appName: Jw.appName,
                    accesstoken: getCookie('token'),
                    tenantAlias: getCookie("tenantAlias"),
                    tenantOid: getCookie("tenantOid"),
                },
            })
            .then((response) => {
                return response.blob();
            })
            .then((res) => {
                let url = window.URL.createObjectURL(
                    new Blob([res], {
                        type: 'application/vnd.ms-excel',
                    })
                );
                let link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', this.epart ? 'Epart.zip' : 'Document.zip');
                document.body.appendChild(link);
                link.click();
                this.$success(this.$t('txt_export_success'));
                this.downloadLoading = false;
                this.visibleDown = false
            }).catch((err) => {
                this.downloadLoading = false;
                this.$error(err.msg);
            });
        },
        onUpload(info) {
           this.totalcount ='',
            this.file = info.file;
            let filetype = this.file.name.substring(this.file.name.lastIndexOf("."))
            if(filetype !== '.zip'){
               this.$error(this.$t('nonsupport_filetype') + filetype) 
               return
            }

            this.testLoading = true;
            this.saveLoading = true
            let formData = new FormData();
            formData.append('file', info.file);
             this.tableData = [];
            let dataParams = {
                containerOid: this.currentTree.containerOid,
                containerType: this.currentTree.containerType,
                catalogOid: this.currentTree.oid,
                catalogType: this.currentTree.type,
            }
            formData.append('data', JSON.stringify(dataParams));
            let api = testImportExcel
            if(this.epart){
                api = epartTestImportExcel
            }
            api.execute(
                formData
            ).then((res) => {
                const { total } = res
                this.totalcount = total
                if (res.list && res.list.length > 0) {
                    this.isShowTable = true;
                    this.tableData = res.list;
                } else {
                    this.isShowTable = false;
                    this.tableData = [];
                }
                this.$success(this.$t('txt_test_import_success'));
                this.testLoading = false;
            }).catch((err) => {
                this.testLoading = false;
                this.$error(err.msg);
            }).finally(() => {
                this.saveLoading = false
            })
        },
        onClose() {
            this.file = null;
            this.isShowTable = false;
            this.tableData = [];
            this.$emit('close');
            this.totalcount=''
        },
        onSave() {
            if (!this.file) {
                this.$warning(this.$t('txt_import_first'));
                return;
            }
            let formData = new FormData();
            formData.append('file', this.file);
            let dataParams = {
                containerOid: this.currentTree.containerOid,
                containerType: this.currentTree.containerType,
                catalogOid: this.currentTree.oid,
                catalogType: this.currentTree.type,
            }
            formData.append('data', JSON.stringify(dataParams));
            let api = importExcel
            if(this.epart){
                api = epartImportExcel
            }
            this.saveLoading = true
            api.execute(
                formData
            ).then((res) => {
                this.$emit('getList','import');
                this.onClose();
            }).catch((err) => {
                this.$error(err.msg);
            }).finally(() => {
                this.saveLoading = false
            })
            this.totalcount=''
        },
    },
};
</script>

<style lang="less" scoped>
.appendixs-label {
  display: flex;
  align-items: center;
  margin-left: 16px;
  text-align: left;
}
.jw-icon {
  font-size: 32px;
  margin-right: 8px;
}
</style>
<style lang="less">
.dialog-class {
    top: 160px;
    margin-right: 76px;
}
</style>
