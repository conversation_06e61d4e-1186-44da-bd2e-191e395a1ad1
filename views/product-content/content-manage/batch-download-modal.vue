<!-- 批量选择类型和分类 -->
<template>
  <a-modal :title="$t('btn_dow_temp_in')" :visible="visible" :mask-closable="false" @cancel="onCancel" width="600px">
    <div class="upload-wrap">
      <a-form-model ref="ref_model_form" layout="inline">
        <div class="upload-wrap-line" v-for="(line, index) in modelList" :key="index">
          <a-form-model-item :label="$t('txt_type')">
            <a-select v-model.trim="line.type" :placeholder="$t('msg_select')" show-search :filter-option="filterOption"
              @change="(val) => onchangeModel(val, line)" style="width:200px">
              <a-select-option v-for="item in subModelOptions" :key="item.name" :value="item.code">
                {{ $t(item.name) }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('txt_classify')">
            <div style="position: relative">
              <a-tree-select ref="treeSelect" style="width: 190px" showSearch treeCheckStrictly :tree-data="line.treeData"
                :replaceFields="replaceFields" :filterTreeNode="filterTreeNode" @select="(val) => selectTree(val, line)" />
            </div>
          </a-form-model-item>
          <span v-if="index === modelList.length - 1" class="jwi-iconadd-circle addicon" @click="addline"></span>
          <span v-else class="jwi-iconyishu addicon" @click="removeline(index)"></span>
        </div>

      </a-form-model>
    </div>
    <template slot="footer">
      <a-button key="submit" type="primary" :loading="exportLoading" @click="onDownloadTemp">
        {{ $t("btn_download") }}
      </a-button>
      <a-button key="cancel" @click="onCancel">{{ $t("btn_cancel") }}
      </a-button>
    </template>
  </a-modal>
</template>

<script>
import ModelFactory from "jw_apis/model-factory"

// 获取子类型
const fetchSubModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/query-sub-model`,
  method: "post",
})
// 根据modelCode类型获取当前部件下的分类
const findClassList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/model/assign/findClassification`,
  method: "get",
})

export default {
  name: "downloadModal",
  props: [
    "visible",
    "currentTree",
    "objectType",
  ],
  data() {
    return {
      modelList: [
        {
          type: '',
          typeName: '',
          clsOid: '',
          treeData: [],
        },
      ],
      rootOid: "",
      exportLoading: false,
      subModelOptions: [],
      replaceFields: {
        children: "children",
        title: "displayName",
        key: "oid",
        value: "oid",
      },
    }
  },
  created() {
  },
  watch: {
    visible(val) {
      if (val) {
        this.modelList = [
          {
            type: '',
            typeName: '',
            clsOid: '',
            treeData: [],
          },
        ]
        this.fetchSubModel()
      }
    },
  },
  methods: {
    //添加行
    addline() {
      this.modelList.push({
        type: '',
        typeName: '',
        clsOid: '',
        treeData: [],
      })
    },
    //移除行
    removeline(index) {
      this.modelList.splice(index, 1)
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      )
    },
    filterTreeNode(input, treeNode){
      return treeNode.data.props.displayName.indexOf(input) !== -1
    },
    onchangeModel(val, line) {
      line.type = val
      let model = this.subModelOptions.find(item => item.code === val)
      if (model) {
        line.typeName = line.type !== this.$t(line.type) ? this.$t(line.type) : null
      }
      this.fetchClassOid(val, line)
    },
    fetchSubModel() {
      fetchSubModel
        .execute({
          viewCode: "ENTITY_FILTER",
          objectType: this.objectType ? this.objectType : "Part",
          // 处理在详情里 已存在type导致参数错误  如果存在containerType表示当前处于详情页
          //containerType 详情的containerType    ||    type  容器的containerType
          contextType:
            this.$route.query.containerType || this.$route.query.type,
          //同理
          //id  容器的contextOid    ||    contextOid  详情的contextOid
          contextOid: this.$route.query.oid || this.$route.query.contextOid,
        })
        .then((res) => {
          this.subModelOptions = res
        })
        .catch((err) => {
          this.$error(err.msg)
        })
    },
    fetchClassOid(modelCode, line) {
      findClassList
        .execute({ modelCode })
        .then((res) => {
          if (res?.oid) {
            this.rootOid = res.oid
            this.fetchClassList(res.oid, line)
          } else {
            line.treeData = []
          }
        })
        .catch((err) => {
          this.$error(err.msg || err.message)
        })
    },
    fetchClassList(rootOid, line) {
      ModelFactory.create({
        url: `${Jw.gateway}/${Jw.foundationServer}/classification/searchTree?rootOid=${rootOid}`,
        method: "get",
      })
        .execute()
        .then((res) => {
          line.treeData = [...res]
        })
        .catch((err) => {
          this.$error(err.msg || err.message)
        })
    },
    selectTree(value, line) {
      line.clsOid = value
    },
    onDownloadTemp() {
      let list = this.modelList.filter(item => item.type).map(item => {
        return {
          type: item.type,
          typeName: item.typeName,
          clsOid: item.clsOid
        }
      })
      if(list.length){
        this.$emit("downbatchdown", list)
        this.modelList = []
      }else{
        this.$warning(this.$t('first_choose_cls'))
      }
      
    },
    onCancel(){
      this.$emit("update:visible", false)
      this.modelList = []
    }
  },
}
</script>

<style lang="less" scoped>
.upload-wrap-line {
  display: flex;
  align-items: center;
}

.upload-wrap {
  margin-top: 10px;
}

.file-name-input {
  margin-right: 10px;
  color: rgba(0, 0, 0, 0.65);

  /deep/ &.ant-input {
    background: #fff;
  }
}

/deep/ .ant-select-tree {
  height: 200px;
  overflow: scroll;
}

.addicon {
  cursor: pointer;
}
</style>
