<template>
    <jw-base-color-modal :title="objInfo.title" :visible.sync="visible" :ok-btn-loading="saveLoading"
        :ok-text="$t('btn_import')" :cancel-text="$t('btn_cancel')" :dialogClass="'dialog-class'" :mask="false" @ok="onSave"
        @cancel="onClose">
        <div class="btn-wrap">
            <a-upload-dragger name="file" :accept="'.zip,.xlsx,.xls'" action="string" :fileList.sync="files" :customRequest="onUpload"
                :remove="removelist">
                <div class="appendixs-label">
                    <jw-icon type="#jwi-shangzhuanwenjian"></jw-icon>
                    <div>
                        <span>{{ $t("txt_feil_drop") }}
                            <span class="upload-btn">{{ $t("txt_click_upload") }}</span>
                        </span>
                        <div>({{ $t("txt_feil_size_1000") + ',' + $t("txt_uplaod_type") + '.zip,.xlsx' }})</div>
                    </div>
                </div>
            </a-upload-dragger>

            <batch-download-modal v-if="objInfo.multiple" :visible.sync="visibleDown" :objectType="'Part'"
                :currentTree="currentTree" @close="visibleDown = false" @downbatchdown="onDownloadTemp">
            </batch-download-modal>

            <download-modal v-else :visible="visibleDown" :objectType="objInfo.type" :currentTree="currentTree"
                :multiple="false" :epart="false" :contentClsData="contentClsData" @close="visibleDown = false"
                @downbatchdown="onDownloadTemp">
            </download-modal>
        </div>
        <template #footer-before-btn>
            <a-button type="link" style="float:left;padding: 0;" :loading="downloadLoading" @click="downloadTemp">
                {{ $t("btn_download") + $t("txt_doc_temp") }}
            </a-button>
        </template>
    </jw-base-color-modal>
</template>

<script>
import { jwBaseColorModal } from 'jw_frame';
import { getCookie } from 'jw_utils/cookie';
import downloadModal from "./download-modal.vue";
import BatchDownloadModal from './batch-download-modal.vue';
export default {
    name: 'batchImportModal',
    components: {
        jwBaseColorModal, downloadModal,
        BatchDownloadModal
    },
    props: [
        'visible',
        'currentTree',
        'contentClsData',
        'objInfo'
    ],
    data() {
        return {
            files: [],
            file: null,
            downloadLoading: false,
            saveLoading: false,
            visibleDown: false,
        };
    },
    methods: {
        downloadTemp(){
          if (this.objInfo.type === 'PartTemp' ||
              this.objInfo.type === 'DocumentTemp' || this.objInfo.type === 'deliverImport'
              || this.objInfo.type === 'EcadTemp' || this.objInfo.type === 'PartLink'
              || this.objInfo.type === 'deliverUpdate' || this.objInfo.type === 'batchImportBOM' || this.objInfo.type === 'batchImportGLOBAL') {
            this.objInfo.download();
          } else {
            this.visibleDown = true;
          }
        },
        removelist() {
            this.files = []
            this.file = null
        },
        onDownloadTemp(val) {
            let { activeModle: type, classModle } = val
            this.downloadLoading = true;
            let url
            if (this.objInfo.multiple) {
                url = this.objInfo.download(val)
                type = '批量模板'
            } else {
                url = this.objInfo.download(type, classModle)
            }
            this.visibleDown = false
            fetch(url, {
                method: 'get',
                headers: {
                    'Content-Type': 'application/json;charset=utf8',
                    appName: Jw.appName,
                    accesstoken: getCookie('token'),
                    tenantAlias: getCookie("tenantAlias"),
                    tenantOid: getCookie("tenantOid"),
                },
            })
                .then((response) => {
                    return response.blob();
                })
                .then((res) => {
                    let url = window.URL.createObjectURL(
                        new Blob([res], {
                            type: 'application/vnd.ms-excel',
                        })
                    );
                    let link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', type + '.zip');
                    document.body.appendChild(link);
                    link.click();
                    this.$success(this.$t('txt_export_success'));
                }).catch((err) => {
                    this.$error(err.msg);
                }).finally(() => {
                    this.downloadLoading = false;
                })
        },
        onUpload(info) {
            this.file = info.file;
            let filetype = this.file.name.substring(this.file.name.lastIndexOf("."))
            if (filetype !== '.zip' && filetype !== '.xlsx') {
                this.$error(this.$t('nonsupport_filetype') + filetype)
                return
            }
            this.files = [this.file]
            console.log(this.files, '文件夹')
        },
        onClose() {
            this.removelist()
            this.$emit('close');
        },
        onSave() {
            if (!this.file) {
                this.$warning(this.$t('txt_import_first'));
                return;
            }
            let formData = new FormData();
            formData.append('file', this.file);
            if(this.objInfo.type === 'deliverImport' || this.objInfo.type === 'deliverUpdate'){
                formData.append('oid', this.currentTree.oid || this.$route.query.oid);
                formData.append('modelDefinition', this.currentTree.modelDefinition || this.$route.query.masterType)
            }else{
                let dataParams = {
                    containerOid: this.currentTree.containerOid,
                    containerType: this.currentTree.containerType,
                    catalogOid: this.currentTree.oid,
                    catalogType: this.currentTree.type,
                }
                formData.append('data', JSON.stringify(dataParams));
            }

            this.saveLoading = true
            this.objInfo.upload().execute(
                formData
            ).then((res) => {
                this.$success(this.$t('txt_import_success'))
                this.$emit('getList', 'import');
                this.onClose();
            }).catch((err) => {
                console.log(err)
                this.$error(err.msg);
            }).finally(() => {
                this.saveLoading = false
            })
        },
    },
};
</script>

<style lang="less" scoped>
.appendixs-label {
    display: flex;
    align-items: center;
    margin-left: 16px;
    text-align: left;
}

.jw-icon {
    font-size: 32px;
    margin-right: 8px;
}
</style>
<style lang="less">
.dialog-class {
    top: 160px;
    margin-right: 76px;
}
</style>
