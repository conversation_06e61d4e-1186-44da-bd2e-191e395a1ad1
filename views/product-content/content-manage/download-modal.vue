<template>
  <a-modal
    :title="$t('btn_dow_temp_in')"
    :visible="visible"
    :confirm-loading="confirmLoading"
    :mask-closable="false"
    @ok="onSubmit"
    @cancel="onCancel"
  >
    <div class="upload-wrap">
      <a-form-model ref="ref_model_form" :model="modelData">
        <a-form-model-item :label="$t('txt_type')" prop="activeModle">
          <a-select
            v-model.trim="modelData.activeModle"
            allowClear
            :placeholder="$t('msg_select')"
            show-search
            :filter-option="filterOption"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            @change="onchangeModel"
          >
            <a-select-option
              v-for="item in subModelOptions"
              :key="item.name"
              :value="item.code"
            >
              {{ $t(item.name) }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :label="$t('txt_classify')" prop="classModle">
          <div style="position: relative">
            <a-tree-select
              ref="treeSelect"
              v-model.trim="modelData.classModle"
              style="width: 100%"
              :tree-checkable="treeCheckable"
              treeCheckStrictly
              :tree-data="treeData"
              :show-checked-strategy="showParent"
              :replaceFields="replaceFields"
              :filter-option="filterOption"
              :getPopupContainer="(triggerNode) => triggerNode.parentNode"
              @select="selectTree"
            />
          </div>
        </a-form-model-item>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button
        key="submit"
        type="primary"
        :loading="exportLoading"
        @click="onDownloadTemp"
      >
        {{ $t("btn_download") }}
      </a-button>
      <a-button key="cancel" @click="onCancel"
        >{{ $t("btn_cancel") }}
      </a-button>
    </template>
  </a-modal>
</template>

<script>
import ModelFactory from "jw_apis/model-factory"
import { TreeSelect } from "ant-design-vue"
import { getCookie } from "jw_utils/cookie"

const SHOW_PARENT = TreeSelect.SHOW_PARENT

// 获取子类型
const fetchSubModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/query-sub-model`,
  method: "post",
})

// part导入数据
const uploadPartList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part-export/upload`,
  method: "post",
})

// 根据modelCode类型获取当前部件下的分类
const findClassList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/model/assign/findClassification`,
  method: "get",
})

const fetchFolderTree = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/folder/searchFoldersAndCount`,
  method: "post",
})

export default {
  name: "downloadModal",
  props: [
    "visible",
    "currentTree",
    "objectType",
    "multiple",
    "epart",
    "contentClsData",
  ],
  data() {
    return {
      treeCheckable: true,
      searchKey: "",
      rootOid: "",
      file: "",
      exportLoading: false,
      confirmLoading: false,
      fileData: {},
      fileName: undefined,
      modelCode: "",
      modelData: {
        activeModle: undefined,
        classModle: [],
        fileName: undefined,
      },
      hasSubModel: false,
      subModelOptions: [],
      treeData: [],
      showParent: SHOW_PARENT,
      replaceFields: {
        children: "children",
        title: "displayName",
        key: "oid",
        value: "oid",
      },
    }
  },
  created() {
    if (this.multiple === false) {
      this.treeCheckable = this.multiple
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.$refs.ref_model_form && this.$refs.ref_model_form.resetFields()
        this.modelData = {
          activeModle: undefined,
          classModle: [],
          fileName: undefined,
        }
        this.fetchSubModel()
      }
    },
  },
  methods: {
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      )
    },
    onchangeModel(val) {
      let _this = this
      if (val) {
        _this.modelData.activeModle = val
        _this.fetchClassOid(val)
      } else {
        _this.modelData.activeModle = undefined
        _this.treeData = []
      }
    },
    fetchSubModel() {
      fetchSubModel
        .execute({
          viewCode: "ENTITY_FILTER",
          objectType: this.objectType ? this.objectType : "Part",
          // 处理在详情里 已存在type导致参数错误  如果存在containerType表示当前处于详情页
          //containerType 详情的containerType    ||    type  容器的containerType
          contextType: this.currentTree.type || this.$route.query.type,
          //同理
          //id  容器的contextOid    ||    contextOid  详情的contextOid
          contextOid: this.currentTree.oid || this.$route.query.contextOid,
        })
        .then((res) => {
          this.subModelOptions = res
          if (res.length > 0) {
            const data = res.filter((item) => item.name === "ElectricalPart")
            if (data.length > 0 && this.epart) {
              this.modelData.activeModle = data[0].name
              this.fetchClassOid(data[0].code)
            } else {
              this.modelData.activeModle = res[0].name
              this.fetchClassOid(res[0].code)
            }
          }
          this.hasSubModel = true
        })
        .catch((err) => {
          this.$error(err.msg)
        })
    },
    fetchClassOid(modelCode) {
      //ElectricalPart 类型查找左侧分类树
      if (this.epart) {
        this.findClassOid()
      } else {
        findClassList
          .execute({ modelCode })
          .then((res) => {
            if (res?.oid) {
              this.rootOid = res.oid
              this.fetchClassList(res.oid)
            } else {
              this.treeData = []
            }
          })
          .catch((err) => {
            this.$error(err.msg || err.message)
          })
      }
    },
    findClassOid() {
      if (this.contentClsData) {
        this.fetchClassList(this.contentClsData.clsOid)
      }
    },
    fetchClassList(rootOid) {
      // let { rootOid, searchKey } = this;
      ModelFactory.create({
        url: `${Jw.gateway}/${Jw.foundationServer}/classification/searchTree?rootOid=${rootOid}`,
        method: "get",
      })
        .execute()
        .then((res) => {
          // this.treeData = res;
          this.getDisabled(res)
        })
        .catch((err) => {
          this.$error(err.msg || err.message)
        })
    },
    selectSearch(value) {
      this.searchKey = value
      this.fetchClassList()
    },
    selectTree(value, node, extra) {
      this.modelData.classModle = value
    },
    onCancel() {
      this.fileName = ""
      this.fileData = {}
      this.$emit("close")
    },
    onDownloadTemp() {
      let { treeData, modelData, $route, currentTree } = this
      let { classModle, activeModle } = modelData

      if (this.objectType) {
        this.$emit("downbatchdown", modelData, classModle)
        return
      }
      // 处理表单--校验类型和分类选择
      // this.$refs["ref_model_form"].validateField("activeModle");
      // this.$refs["ref_model_form"].validateField("classModle");
      let param = []
      let flattenTree = _.cloneDeep(this.flatten(treeData))
      if (classModle.length > 0) {
        let filterClassOid = classModle && classModle.map((item) => item.value)
        let filterClass = flattenTree.filter((item) =>
          filterClassOid.includes(item.oid)
        )
        filterClass.map((item, index) => {
          param.push({
            containerOid: $route.query.oid,
            containerType: $route.query.type,
            catalogType: currentTree.catalogType,
            catalogOid: currentTree.catalogOid,
            code: activeModle,
            clsCode: item.clsCode,
            clsOid: item.oid,
            clsDisplayName: item.displayName,
          })
        })
      } else {
        param = [
          {
            containerOid: $route.query.oid,
            containerType: $route.query.type,
            catalogType: currentTree.catalogType,
            catalogOid: currentTree.catalogOid,
            code: activeModle || "Part",
            clsCode: "",
            clsOid: "",
            clsDisplayName: "",
          },
        ]
      }
      const accesstoken = getCookie("token")
      this.exportLoading = true
      fetch(`${Jw.gateway}/${Jw.partBomMicroServer}/part-export/template`, {
        method: "post",
        body: JSON.stringify(param),
        headers: {
          "Content-Type": "application/json;charset=utf8",
          appName: Jw.appName,
          accesstoken,
          tenantAlias: getCookie("tenantAlias"),
          tenantOid: getCookie("tenantOid"),
        },
      })
        .then((response) => {
          return response.blob()
        })
        .then((data) => {
          this.$success(this.$t("txt_export_success"))
          let url = window.URL.createObjectURL(
            new Blob([data], {
              type: "application/vnd.ms-excel",
            })
          )
          let link = document.createElement("a")
          link.href = url
          link.setAttribute("download", this.$t("txt_part_temp"))
          document.body.appendChild(link)
          link.click()
          this.$emit("close")
          this.exportLoading = false
        })
        .catch((err) => {
          this.exportLoading = false
          this.$error(err.msg || this.$t("msg_failed"))
        })
    },
    beforeUpload(file) {
      this.modelData.fileName = file.name
      this.fileName = file.name
      this.fileData = file
      return false
    },
    // 扁平化树形结构数组
    flatten(array) {
      var flattend = []
      ;(function flat(array) {
        array.forEach(function (el) {
          for (let i in el) {
            if (Object.prototype.toString.call(el[i]) === "[object Array]") {
              flat(el[i])
            }
            if (i == "children") {
              delete el[i]
            }
          }
          flattend.push(el)
        })
      })(array)
      return flattend
    },
    onSubmit() {
      let { treeData, modelData, $route, currentTree, file } = this
      // let { classModle, activeModle } = modelData;
      // 校验表单是否填写
      this.$refs["ref_model_form"].validate().then(() => {
        let partImport = {
          catalogOid: currentTree.catalogOid,
          catalogType: currentTree.catalogType,
          containerOid: $route.query.oid,
          containerType: $route.query.type,
          code: "Part",
        }
        let formData = new FormData()
        formData.append("file", this.fileData)
        formData.append("partImport", JSON.stringify(partImport))
        this.confirmLoading = true
        uploadPartList
          .execute(formData)
          .then((res) => {
            this.$success(this.$t("txt_import_success"))
            this.confirmLoading = false
            this.onCancel()
            this.$emit("getList")
          })
          .catch((err) => {
            this.confirmLoading = false
            if (err.msg) {
              this.$error(err.msg)
            }
          })
      })
    },
    getDisabled(data) {
      data.forEach((item) => {
        if (item.children) {
          item.disabled = true
          this.getDisabled(item.children)
        }
        this.treeData = data
      })
    },
  },
}
</script>

<style lang="less" scoped>
.upload-wrap {
  margin-top: 10px;
}

.file-name-input {
  margin-right: 10px;
  color: rgba(0, 0, 0, 0.65);

  /deep/ &.ant-input {
    background: #fff;
  }
}

/deep/ .ant-select-tree {
  height: 200px;
  overflow: scroll;
}
</style>
