<template>
  <div>
    <a-form-model-item :label="formItem.label" :prop="formItem.name" v-if="formItem.formType == 'input'">
      <a-input v-model.trim="formItem.value" allow-clear :placeholder="$t('msg_input')"></a-input>
    </a-form-model-item>
    <a-form-model-item :label="formItem.label" :prop="formItem.name" v-if="formItem.formType == 'select'">
      <slot :name="formItem.name">
        <a-select v-model.trim="formItem.region" :placeholder="$t('msg_select')"></a-select>
      </slot>
    </a-form-model-item>
  </div>
</template>
<script>
export default {
    name:'',
    props:{
      formItem:{
        type: Object,
        default:null
      }
    }
}
</script>
