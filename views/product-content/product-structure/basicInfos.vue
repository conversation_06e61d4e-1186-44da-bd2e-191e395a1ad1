<template>
  <div class="basic-info-wrap">
    <!-- <div class="title-wrap flex justify-between align-center">
            <span class="title color85 font-700">基本信息</span>
            <a-button type="link" v-if="isEdit" @click="onEdit">编辑信息</a-button>
            <a-button type="link" v-else @click="onSave">保存信息</a-button>
        </div> -->
    <div class="body-wrap">
      <jw-layout-builder
        v-if="rowInfo.modelDefinition"
        ref="ref_appBuilder"
        type="Model"
        :layoutName="layoutName"
        :modelName="rowInfo.modelDefinition"
        :instanceData="rowInfo"
      >
      </jw-layout-builder>
    </div>
  </div>
</template>

<script>
import { jwLayoutBuilder } from "jw_frame";
export default {
  name: "basicInfos",
  props: ["rowInfo"],
  components: {
    jwLayoutBuilder,
  },
  data() {
    return {
      isEdit: true,
      layoutName: "show",
    };
  },
  mounted() {
    console.log("this.rowInfo", this.rowInfo);
  },
  methods: {
    onEdit() {
      this.isEdit = false;
    },
    onSave() {
      this.isEdit = true;
    },
  },
};
</script>

<style lang="less" scoped>
.basic-info-wrap {
  width: 70%;
  // height: calc(100vh - 222px);
  margin: 0 auto;
  padding-top: 20px;
  .title-wrap {
    position: relative;
    margin-bottom: 15px;
    .title {
      margin-left: 10px;
      &:after {
        content: "";
        position: absolute;
        width: 3px;
        height: 20px;
        left: 0;
        top: 2px;
        border-radius: 4px;
        background: #255ed7;
      }
    }
    .ant-btn-link {
      height: auto;
    }
  }
  .ant-btn.ant-btn-link {
    padding: 0;
  }
  .body-wrap {
    // height: calc(100vh - 277px);
    overflow: auto;
    &::-webkit-scrollbar {
      width: 0px;
    }
  }
}
</style>
