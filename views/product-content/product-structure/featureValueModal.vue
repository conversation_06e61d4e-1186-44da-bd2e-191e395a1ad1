<template>
  <a-modal
    :visible="visible"
    :title="featureItem.oid ? $t('txt_update_value') :$t('txt_j_value') "
    :mask-closable="false"
    :footer="null"
    :destroyOnClose="true"
    @cancel="onCancel"
  >
    <div class="create-temp-wrap">
      <a-form-model
        ref="createForm"
        :model="form"
      >
        <a-form-model-item
          :label="$t('txt_features')"
          prop="featureName"
          :rules="[{ required: true, message: $t('msg_select'), trigger: 'change' }]"
        >
          <a-select
            v-model.trim="form.featureName"
            :placeholder="$t('msg_select')"
            showSearch
            filterOption
            option-filter-prop="children"
            @select="getValueByFeature"
            @change="onChangeFeatureName"
          >
            <a-select-option
              v-for="item in featureList"
              :key="item.oid"
              :value="item.name"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          v-if="form.valueType === 'select'"
          :label="$t('txt_specific')"
          prop="featureValue"
          :rules="[{ required: true, message: $t('msg_select'), trigger: 'change' }]"
        >
          <a-select
            v-model.trim="form.featureValue"
            :placeholder="$t('msg_select')"
            showSearch
            filterOption
            option-filter-prop="children"
            @focus="getValueByFeature"
          >
            <a-select-option
              v-for="item in valueList"
              :key="item.oid"
              :value="item.value"
            >
              {{ item.value }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          v-if="form.valueType === 'text'"
          :label="$t('txt_specific')"
          prop="featureValue"
          :rules="[
            { required: true, message: $t('msg_select'), trigger: 'change' },
            { min: 1, max: 50, message: $t('txt_prompt'), trigger: 'change' },
          ]"
        >
          <a-input
            v-model.trim="form.featureValue"
            :max-length="50"
            allow-clear
            :placeholder="$t('txt_input')"
          />
        </a-form-model-item>
        <a-form-model-item class="form-item-btns text-right">
          <a-button
            type="primary"
            :loading="createLoading"
            @click="handleCreate"
          >{{$t('btn_ok')}}</a-button>
          <a-button
            class="form-btn-cancel"
            @click="onCancel"
          >{{$t('btn_cancel')}}</a-button>
        </a-form-model-item>
      </a-form-model>
    </div>
  </a-modal>
</template>

<script>
import {
  fetchFeatureByNode,
  fetchValueByFeature,
  createFeatureValue,
  updateFeatureValue,
} from "apis/product/productStructure";
export default {
  name: "featureValueModal",
  props: ["visible", "nodeInfo", "featureItem"],
  data() {
    return {
      form: {},
      createLoading: false,
      featureList: [],
      valueList: [],
    };
  },
  mounted() {},
  watch: {
    visible(val) {
      if (val) {
        this.getFeatures();
        if (this.featureItem.oid) {
          this.form = {
            ...this.featureItem,
            valueType: this.featureItem.valueType || "select",
          };
        } else {
          this.form = {
            featureName: undefined,
            featureValue: undefined,
            valueType: "select",
          };
        }
      }
    },
  },
  methods: {
    getFeatures() {
      fetchFeatureByNode
        .execute({
          productOid: this.$route.query.oid,
          oid: this.nodeInfo.oid,
          modelType: this.nodeInfo.type,
        })
        .then((res) => {
          this.featureList = res;
        })
        .catch((err) => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
    getValueByFeature() {
      if (this.form.featureName) {
        let featrue = this.featureList.filter(
          (item) => item.name === this.form.featureName
        )[0];
        console.log("featrue", featrue);
        console.log("this.form", this.form);
        fetchValueByFeature
          .execute({
            featureOid: featrue.oid,
            featureType: featrue.type,
          })
          .then((res) => {
            this.valueList = res;
          })
          .catch((err) => {
            if (err.msg) {
              this.$error(err.msg);
            }
          });
      }
    },
    onChangeFeatureName() {
      this.form.featureValue = undefined;
      this.form.valueType = this.featureList.filter(
        (item) => item.name === this.form.featureName
      )[0].valueType;
      this.form.number = this.featureList.filter(
        (item) => item.name === this.form.featureName
      )[0].number;
    },
    onCancel() {
      this.$emit("close");
    },
    handleCreate() {
      // 从具体值列表获取  labelValueOid
      let labelValueObj = this.valueList.filter(
        (item) => item.value === this.form.featureValue
      )[0];
      this.createLoading = true;
      this.$refs.createForm.validate((valid) => {
        if (valid) {
          let api = createFeatureValue;
          let msg = "";
          if (this.featureItem.oid) {
            api = updateFeatureValue;
            msg = this.$t("msg_update_success");
          } else {
            api = createFeatureValue;
            msg = this.$t("txt_add_success");
          }
          api
            .execute({
              productOid: this.$route.query.oid,
              masterOid: this.nodeInfo.oid,
              masterType: this.nodeInfo.inheritModelType,
              featureValueData: {
                oid: this.featureItem.oid,
                featureName: this.form.featureName,
                featureValue: this.form.featureValue,
                number: this.form.number,
                valueType: this.form.valueType,
                labelValueOid: labelValueObj.oid,
              },
            })
            .then((res) => {
              this.$success(msg);
              this.onCancel();
              this.$emit("getList");
            })
            .catch((err) => {
              if (err.msg) {
                this.$error(err.msg);
              }
            })
            .finally(() => {
              this.createLoading = false;
            });
        } else {
          this.createLoading = false;
          return false;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.form-item-btns {
  margin: 30px 0 0;
}
.form-btn-cancel {
  margin-left: 8px;
}
</style>
