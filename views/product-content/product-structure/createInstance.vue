<template>
  <div class="main-view">
    <div class="object-details-toolbar">
      <div class="left">
        <span class="route">
          <router-link :to="{
            path: '/product-content',
            query: {
              ...$route.query,
              tabActive: 'structure',
            },
          }">
            {{ $t("txt_product_config") }}
          </router-link>
          <span>
            &gt;
          </span>
          <span>
            {{ $t("txt_cerete_instance") }}
          </span>
        </span>
      </div>
    </div>
    <div class="create-instance-wrap">
      <div class="head-wrap flex justify-between align-center">
        <div class="flex align-center">
          <span v-if="!isCheck" @click="goCheck">
            <jw-icon type="jwi-iconarrow-left" />
          </span>
          <span class="color45" v-if="isCheck">{{ $t("txt_one_tab") }} </span>
          <span class="color45" v-else>{{ $t("txt_two_tab") }} </span>
        </div>
        <div class="flex">
          <a-button v-if="isCheck" type="primary" @click="onOptional">{{ $t("txt_comfirm_match") }}
          </a-button>
          <a-button v-else type="primary" @click="onCreate">{{ $t("txt_cerete_instance") }}
          </a-button>
          <a-button class="head-cancel-btn" @click="onCancel">{{ $t("btn_cancel") }}
          </a-button>
        </div>
      </div>
      <div class="body-wrap">
        <a-table v-if="isCheck" :columns="columns" :data-source="tableData" rowKey="id" :pagination="false" bordered
          :rowClassName="rowClassName" :loading="loading" :scroll="{ x: 800, y: 'calc(100vh - 225px)' }">
          <template slot="featureValue" slot-scope="text, record">
            <a-select v-model.trim="record.value" :placeholder="$t('msg_select')" v-if="record.valueType === 'select'"
              :disabled="record.disabled" @change="onChangeValue(record)">
              <a-select-option v-for="item in record.labelValueDatas" :key="item.oid" :value="item.value">
                {{ item.value }}
              </a-select-option>
            </a-select>
            <a-input v-if="record.valueType === 'text'" v-model.trim="record.value" :disabled="record.disabled" allow-clear
              :placeholder="$t('msg_select')" @blur="onChangeValue(record)" />
          </template>
        </a-table>
        <a-table v-else :columns="cols" :data-source="listData" rowKey="id" :pagination="false" bordered
          :loading="loading" :scroll="{ x: 800, y: 'calc(100vh - 225px)' }">
          <template slot="name" slot-scope="text, record">
            <div class="flex align-center">
              <jw-icon type="#jwi-bujian" />
              <div class="name-text text-ellipsis" :title="text">
                {{ text }}
              </div>
            </div>
          </template>
          <template slot="number" slot-scope="text">
            <div>{{ text }}</div>
          </template>
          <template slot="version" slot-scope="text">
            <div class="tag-wrap">{{ text }}</div>
          </template>
          <template slot="viewName" slot-scope="text">
            <div class="tag-wrap">{{ text }}</div>
          </template>
          <template slot="count" slot-scope="text, record">
            <a-input-number v-model.trim="record.count" :precision="0" :min="1" />
          </template>
        </a-table>
      </div>
      <DialogWithComponentsbuild slot="dialog" ref="dialogWithComponentbuild" :layoutName="'create'"
        :modelName="'Product'" :slotName="'save'" :title="$t('txt_create_product')" :instanceData="instanceData"
        :delayHide="true" @on-save="saveHandler">
      </DialogWithComponentsbuild>
    </div>
  </div>
</template>

<script>
import { jwIcon, jwPage } from "jw_frame";
import DialogWithComponentsbuild from "./dialog-with-componentbuilder.vue";
import {
  createOptionalInstance,
  fetchConditionInstance,
  fetchOptionalInstance,
  createInstance,
} from "apis/product/productStructure";
export default {
  name: "createInstance",
  components: {
    jwIcon,
    jwPage,
    DialogWithComponentsbuild,
  },
  data() {
    return {
      isCheck: true,
      loading: true,
      columns: [
        {
          title: this.$t("txt_optional"),
          dataIndex: "masterName",
          key: "masterName",
          width: "33%",
          customRender: (text, row, index) => {
            const obj = {
              children: text,
              attrs: {},
            };
            if (row.featureSize >= 1) {
              obj.attrs.rowSpan = row.featureSize;
            } else {
              obj.attrs.rowSpan = 0;
            }
            return obj;
          },
        },
        {
          title: this.$t("txt_features"),
          dataIndex: "name",
          key: "name",
        },
        {
          title: this.$t("txt_specific"),
          dataIndex: "featureValue",
          key: "featureValue",
          scopedSlots: { customRender: "featureValue" },
        },
      ],
      tableData: [],
      cols: [
        {
          title: this.$t("txt_name"),
          dataIndex: "name",
          key: "name",
          scopedSlots: { customRender: "name" },
        },
        {
          title: this.$t("txt_number_of"),
          dataIndex: "number",
          key: "number",
          scopedSlots: { customRender: "number" },
        },
        {
          title: this.$t("txt_version"),
          dataIndex: "version",
          key: "version",
          scopedSlots: { customRender: "version" },
        },
        {
          title: this.$t("txt_view"),
          dataIndex: "viewName",
          key: "viewName",
          scopedSlots: { customRender: "viewName" },
        },
        {
          title: this.$t("txt_num"),
          dataIndex: "count",
          key: "count",
          width: 180,
          scopedSlots: { customRender: "count" },
        },
      ],
      listData: [],
      instanceData: {},
    };
  },
  mounted() {
    this.getConList();
  },
  methods: {
    goBack() { },
    getConList() {
      this.loading = true;
      fetchConditionInstance
        .execute({
          productOid: this.$route.query.oid,
          productType: this.$route.query.type,
        })
        .then((res) => {
          console.log("选配实例清单", res);
          this.tableData = res.map((item) => {
            item.value = undefined;
            item.isShow = true;
            return item;
          });
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
    rowClassName(record) {
      if (!record.isShow) {
        return "hidden-row";
      }
      if (record.readonly) {
        return "readonly-color";
      }
      return "";
    },
    onChangeValue(row) {
      let arr = [];
      this.tableData.forEach((item) => {
        // if (item.sortIndex > row.sortIndex) {
        //   item.value = undefined;
        //   item.disabled = true;
        //   item.readonly = true;
        // }
      });
      this.tableData.forEach((item) => {
        if (item.value) {
          let temp = {
            masterOid: item.masterOid,
            masterName: item.masterName,
            number: item.number,
            name: item.name,
            value: item.value,
          };
          arr.push(temp);
        }
      });
      fetchOptionalInstance
        .execute({
          productOid: this.$route.query.oid,
          data: arr,
        })
        .then((res) => {
          if (res.triggerCount > 0) {
            this.tableData = res.data.map((item) => {
              if (item.labelValueDatas && item.labelValueDatas.length === 1) {
                item.disabled = true;
              } else {
                item.disabled = false;
                item.readonly = false;
              }
              return item;
            });
          }
        })
        .catch((err) => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
    onOptional() {
      if (this.tableData.length > 0) {
        if (this.tableData.every((row) => row.value)) {
          this.isCheck = false;
          this.loading = true;
          let arr = [];
          this.tableData.forEach((item) => {
            let temp = {
              masterOid: item.masterOid,
              name: item.name,
              number: item.number,
              value: item.value,
              condition: "=",
            };
            arr.push(temp);
          });
          createOptionalInstance
            .execute({
              productOid: this.$route.query.oid,
              productType: this.$route.query.type,
              optionDatas: arr,
            })
            .then((res) => {
              this.listData = res;
              this.loading = false;
            })
            .catch((err) => {
              this.loading = false;
              if (err.msg) {
                this.$error(err.msg);
              }
            });
        } else {
          this.$warning(this.$t("txt_complete_list"));
        }
      }
    },
    onCreate() {
      this.$refs.dialogWithComponentbuild.show();
      this.instanceData = {
        masterType: "Container",
        relationName: "ModelModelLink",
        secondaryType: "Product",
      };
    },
    saveHandler(value) {
      this.$refs.dialogWithComponentbuild.saveLoading = true;
      createInstance
        .execute({
          masterType: this.$route.query.type,
          masterOid: this.$route.query.oid,
          modelType: "Product",
          nodeData: value,
          parts: this.listData,
          modelDefinition: "Product",
        })
        .then((res) => {
          this.onCancel()
          this.$refs.dialogWithComponentbuild.saveLoading = false;
          this.$refs.dialogWithComponentbuild.hide();
          this.$success(this.$t("txt_create_success"));
        })
        .catch((err) => {
          this.$refs.dialogWithComponentbuild.saveLoading = false;
          console.log(err);
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
    goCheck() {
      this.isCheck = true;
    },
    onCancel() {
      let jumpObj = {
        ...this.$route.query,
        tabActive: "structure",
      };
      Jw.jumpToDetail({
        ...jumpObj,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.main-view {
  height: 100%;
  background: #fff;
}

.object-details-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 24px;

  >div>span {
    margin-right: 5px;

    >i {
      font-size: 18px;
    }
  }

  .left {
    display: flex;
    align-items: center;

    .title {
      // max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
      background-color: #ddd;
      padding: 5px 13px;
      border-radius: 4px;
    }

    .route {
      color: #a69f9f;

      >button {
        color: #a69f9f;
        padding: 0;

        &:hover {
          color: #40a9ff;
        }
      }
    }

    >i {
      &:hover {
        color: #40a9ff;
        cursor: pointer;
      }
    }
  }

  .right {
    display: flex;
    align-items: center;
    margin-right: 100px;

    >div,
    >button {
      margin-left: 15px;
    }

    .ant-btn {
      width: inherit !important;
    }
  }
}

.create-instance-wrap {
  padding: 16px 20px 16px 20px;
  background: var(--light);
  // box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
  border-radius: 4px;

  .jwifont {
    width: 16px;
    min-width: 16px;
    height: 16px;
    min-height: 16px;
  }

  .head-wrap {
    .head-title {
      margin: 0 16px 0 8px;
      font-size: 20px;
    }

    .head-cancel-btn {
      margin-left: 8px;
    }
  }

  .body-wrap {
    height: calc(70vh);
    margin-top: 12px;
    overflow: auto;

    &::-webkit-scrollbar {
      width: 0px;
    }

    /deep/.ant-table-thead>tr>th {
      padding: 9px 16px;
    }

    /deep/.ant-table-tbody>tr>td {
      padding: 7px 16px;
    }

    /deep/.ant-table .hidden-row {
      display: none;
    }

    /deep/.ant-table .readonly-color {
      .ant-select-disabled .ant-select-selection {
        background: #fff;
      }
    }

    .ant-input-affix-wrapper,
    .ant-select {
      width: 80%;
    }

    .name-text {
      margin-left: 8px;
      color: #255ed7;
    }

    .tag-wrap {
      width: fit-content;
      padding: 2px 8px;
      font-size: 12px;
      color: rgba(30, 32, 42, 0.65);
      background: rgba(30, 32, 42, 0.04);
      border: 1px solid rgba(30, 32, 42, 0.15);
      border-radius: 4px;
    }
  }
}
</style>
