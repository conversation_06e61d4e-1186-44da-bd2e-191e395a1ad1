<template>
  <a-modal
    :visible="visible"
    :title="$t('导入数据')"
    :confirm-loading="confirmLoading"
    :mask-closable="false"
    @ok="onSubmit"
    @cancel="onCancel"
  >
    <a-button @click="onDownloadTemp">{{$t('btn_download_temp')}}</a-button>
    <div class="upload-wrap flex">
      <a-input
        v-model.trim="fileName"
        :placeholder="$t('txt_select_feil')"
        :disabled="true"
        class="file-name-input"
      />
      <a-upload
        name="file"
        action="string"
        :show-upload-list="false"
        accept=".xls,.xlsx"
        :before-upload="beforeUpload"
      >
        <a-button type="primary">{{$t('txt_select_feil')}} </a-button>
      </a-upload>
    </div>
  </a-modal>
</template>

<script>
import { importFeatureFile } from "apis/product/productStructure";
import { getCookie } from "jw_utils/cookie";
const downloadFeatureUrl = `${Jw.gateway}/${Jw.ieconfigService}/ieConfiguration/downloadTempleByCode`;

export default {
  name: "downloadModal",
  props: ["visible"],
  data() {
    return {
      confirmLoading: false,
      fileName: "",
      fileData: {},
    };
  },
  methods: {
    onCancel() {
      this.fileName = "";
      this.fileData = {};
      this.$emit("close");
    },
    onDownloadTemp() {
      let a = document.createElement("a");
      let token = getCookie("token");
      let url = `${downloadFeatureUrl}?access_token=${token}&code=FeatureManImport&appName=pdm`;
      a.href = url;
      a.download = url;
      a.click();
    },
    beforeUpload(file) {
      this.fileName = file.name;
      this.fileData = file;
      return false;
    },
    onSubmit() {
      if (!this.fileName) return;
      this.confirmLoading = true;
      let formData = new FormData();
      formData.append("file", this.fileData);
      formData.append("code", "FeatureManImport");
      importFeatureFile
        .execute(formData)
        .then((res) => {
          console.log(res);
          this.$success($t('txt_import_success'));
          this.confirmLoading = false;
          this.onCancel();
          this.$emit("getList");
        })
        .catch((err) => {
          this.confirmLoading = false;
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
  },
};
</script>

<style lang="less" scoped>
.upload-wrap {
  margin-top: 10px;
}
.file-name-input {
  margin-right: 10px;
  color: rgba(0, 0, 0, 0.65);
  /deep/ &.ant-input {
    background: #fff;
  }
}
</style>
