export default {
    methods: {
        validateName(rule, value, callback) {
            // const reg = /^[\u4e00-\u9fa5A-Za-z0-9]{0,50}$/g;
            const reg = /^\(|\[|\{|\/|\^|\$|\¦|\}|\]|\)|\?|\*|\+|\.$/g;
            if (reg.test(value)) {
                // callback('请输入汉字、字母、数字');
                callback('不能输入( [ { / ^ $ ¦ } ] ) ? * + .')
            } else if (value.indexOf('null')>-1 || value.indexOf('NULL')>-1){
                callback('不能输入null');
            }
            callback();
        },
    }
}
