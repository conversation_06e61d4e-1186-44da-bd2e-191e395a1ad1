<template>
  <a-modal
    :visible="visible"
    :title="$t('txt_add_object')"
    width="60%"
    :mask-closable="false"
    :okText="$t('btn_ok')"
    :footer="null"
    @cancel="onCancel"
  >
    <div class="add-obj-wrap flex">
      <div class="left-wrap">
        <div class="left-head-wrap">
          <a-tabs v-model.trim="activeTab" @change="onTabClick">
            <a-tab-pane key="PartIteration" tab="Part"></a-tab-pane>
            <a-tab-pane key="CADDocumentIteration" tab="CAD"></a-tab-pane>
            <a-tab-pane key="DocumentIteration" :tab="$t('txt_doc')"></a-tab-pane>
          </a-tabs>
          <div class="flex justify-between align-center">
            <a-input-search
              class="search-input"
              v-model.trim="searchKey"
              :placeholder="$t('txt_enter_keyword')"
              @search="onSearch"
              @blur="onSearch"
            />
            <a-pagination
              simple
              :current="page.currentPage"
              :total="page.total"
              @change="onCurrentChange"
            />
          </div>
        </div>
        <a-table
          :columns="columns"
          :data-source="tableData"
          rowKey="oid"
          bordered
          :loading="loading"
          :scroll="{ x: 650, y: 360 }"
          :pagination="false"
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
            onSelect: onSelectRow,
            onSelectAll: onSelectAllRows,
          }"
        >
          <template slot="number" slot-scope="text">
            <div class="number-wrap text-link">{{ text }}</div>
          </template>
        </a-table>
      </div>
      <div class="right-wrap">
        <div class="right-head-wrap flex justify-between align-center">
          <div class="flex align-center">
            <div class="checkbox-left">
              <a-checkbox
                :indeterminate="indeterminate"
                :checked="checkAll"
                @change="onCheckAllChange"
              ></a-checkbox>
            </div>
            <div>{{$t('txt_isChoose')}} {{ deleteData.length }} {{$t('txt_item')}}</div>
          </div>
          <a-button type="link" @click="onDeleteMulti">{{$t('txt_delete')}}</a-button>
        </div>
        <div class="right-body-wrap">
          <a-checkbox-group v-model.trim="deleteData" @change="onChangeObj">
            <div
              class="obj-item flex justify-between align-center"
              v-for="(item, index) of selectData"
              :key="item.oid"
            >
              <a-checkbox :value="item">
                <span
                  :title="`${item.name},${item.number},${item.viewName},${item.lifecycle},${item.version}`"
                >
                  {{ item.name }},{{ item.number }},{{ item.viewName }},{{
                    item.lifecycle
                  }}{{ item.version }}
                </span>
              </a-checkbox>
              <span @click="onDeleteSingle(item, index)">
                <jw-icon type="jwi-icondelete" />
              </span>
            </div>
          </a-checkbox-group>
        </div>
      </div>
    </div>
    <div class="form-item-btns text-right">
      <a-button type="primary" @click="onCreate">{{$t('btn_ok')}}</a-button>
      <a-button class="form-btn-cancel" @click="onCancel">{{$t('btn_cancel')}}</a-button>
    </div>
  </a-modal>
</template>

<script>
import { jwIcon } from "jw_frame";
import { fetchObjects, addRelation } from "apis/product/productStructure";
export default {
  name: "addObjModal",
  components: {
    jwIcon,
  },
  props: ["visible", "nodeInfo"],
  data() {
    return {
      activeTab: "PartIteration",
      searchKey: "",
      loading: true,
      columns: [
        {
          title: this.$t('txt_number'),
          dataIndex: "number",
          key: "number",
          width: 140,
          scopedSlots: { customRender: "number" },
        },
        {
          title:  this.$t('txt_name'),
          dataIndex: "name",
          key: "name",
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: "name" },
        },
        {
          title: this.$t('txt_view') ,
          dataIndex: "viewName",
          key: "viewName",
          width: 100,
        },
        {
          title: this.$t('txt_status'),
          dataIndex: "lifecycle",
          key: "lifecycle",
          width: 100,
        },
        {
          title: this.$t('txt_version'),
          dataIndex: "version",
          key: "version",
          width: 100,
        },
      ],
      tableData: [],
      selectedRowKeys: [],
      page: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      indeterminate: false,
      checkAll: false,
      selectData: [],
      deleteData: [],
    };
  },
  mounted() {},
  watch: {
    visible(val) {
      if (val) {
        this.getObjList();
      }
    },
  },
  methods: {
    onTabClick(key) {
      this.activeTab = key;
      this.page.currentPage = 1;
      this.getObjList();
    },
    onSearch() {
      this.page.currentPage = 1;
      this.page.pageSize = 20;
      this.page.total = 0;
      this.getObjList();
    },
    getObjList() {
      this.loading = true;
      fetchObjects
        .execute({
          //   modelType: this.activeTab,
          type: this.activeTab,
          searchKey: this.searchKey,
          index: this.page.currentPage,
          size: this.page.pageSize,
        })
        .then((res) => {
          this.tableData = res.rows;
          this.page.total = res.count;
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
    onCurrentChange(page, pageSize) {
      this.page.currentPage = page;
      this.getObjList();
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
    },
    onSelectRow(record, selected) {
      if (selected) {
        this.selectData.push(record);
      } else {
        this.selectData = this.selectData.filter((item) => {
          return ![record].find((row) => {
            return item.oid === row.oid;
          });
        });
      }
      if (this.checkAll) {
        this.deleteData = this.selectData;
      }
    },
    onSelectAllRows(selected, selectedRows, changeRows) {
      if (selected) {
        changeRows.forEach((item) => {
          this.selectData.push(item);
        });
      } else {
        this.selectData = this.selectData.filter((item) => {
          return !changeRows.find((row) => {
            return item.oid === row.oid;
          });
        });
      }
    },
    onCheckAllChange(e) {
      Object.assign(this, {
        deleteData: e.target.checked ? this.selectData : [],
        indeterminate: false,
        checkAll: e.target.checked,
      });
    },
    onChangeObj(value) {
      this.indeterminate =
        !!value.length && value.length < this.selectData.length;
      this.checkAll = value.length === this.selectData.length;
    },
    onDeleteSingle(item, index) {
      this.selectedRowKeys = this.selectedRowKeys.filter((val) => {
        return ![item].find((ele) => {
          return ele.oid === val;
        });
      });
      this.selectData.splice(index, 1);
      let tempCheckedData = [];
      this.deleteData.forEach((cd) => {
        if (cd.oid !== item.oid) {
          tempCheckedData.push(cd);
        }
      });
      this.deleteData = tempCheckedData;
      const allChecked =
        this.deleteData.length > 0 &&
        this.deleteData.length === this.selectData.length;
      this.checkAll = allChecked;
      this.indeterminate =
        !!this.deleteData.length &&
        this.deleteData.length < this.selectData.length;
    },
    onDeleteMulti() {
      this.selectedRowKeys = this.selectedRowKeys.filter((val) => {
        return !this.deleteData.find((item) => {
          return item.oid === val;
        });
      });
      this.selectData = this.selectData.filter((item) => {
        return !this.deleteData.find((row) => {
          return item.oid === row.oid;
        });
      });
      this.deleteData = [];
      this.indeterminate = false;
    },
    onCreate() {
      if (this.selectData.length === 0) return;
      let secList = this.selectData.map((item) => {
        return {
          oid: item.oid,
          modelType: item.modelType,
        };
      });
      addRelation
        .execute({
          masterOid: this.nodeInfo.oid,
          masterType: this.nodeInfo.inheritModelType,
          secList: secList,
        })
        .then((res) => {
          this.$success(this.$t('txt_add_success'));
          this.cancelFn();
          this.$emit("close", this.nodeInfo);
        })
        .catch((err) => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
    cancelFn() {
      this.selectedRowKeys = [];
      this.deleteData = [];
      this.selectData = [];
      this.checkAll = false;
      this.indeterminate = false;
      this.page.currentPage = 1;
      this.page.pageSize = 20;
      this.page.total = 0;
      this.searchKey = "";
      this.activeTab = "PartIteration";
    },
    onCancel() {
      this.cancelFn();
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
.add-obj-wrap {
  .jwifont {
    width: 16px;
    min-width: 16px;
    height: 16px;
    min-height: 16px;
  }
  .left-wrap {
    width: 60%;
    .left-head-wrap {
      margin-bottom: 16px;
      /deep/.ant-tabs-nav .ant-tabs-tab {
        margin: 0;
      }
      .search-input {
        width: 216px;
      }
    }
    /deep/.ant-table-content {
      height: 400px;
    }
    /deep/.ant-table-placeholder {
      height: calc(100% - 40px);
    }
    /deep/.ant-table-thead > tr > th {
      padding: 9px 16px;
    }
    .number-wrap {
      color: #255ed7;
    }
  }
  .right-wrap {
    width: calc(40% - 16px);
    margin-left: 16px;
    border: 1px solid rgba(30, 32, 42, 0.06);
    .right-head-wrap {
      height: 40px;
      background: rgba(30, 32, 42, 0.02);
      border-bottom: 1px solid rgba(30, 32, 42, 0.06);
      .checkbox-left {
        width: 40px;
        line-height: 40px;
        text-align: center;
        margin-right: 16px;
        border-right: 1px solid rgba(30, 32, 42, 0.06);
      }
    }
    .right-body-wrap {
      height: 466px;
      padding: 16px;
      overflow: auto;
      .ant-checkbox-group {
        width: 100%;
      }
      .obj-item {
        height: 40px;
        padding: 0 16px;
        margin-bottom: 8px;
        background: rgba(30, 32, 42, 0.02);
        border: 1px solid rgba(30, 32, 42, 0.15);
        border-radius: 6px;
        .ant-checkbox-wrapper {
          display: flex;
          align-items: center;
          width: calc(100% - 20px);
          /deep/.ant-checkbox + span {
            color: #255ed7;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
.form-item-btns {
  margin: 25px 0 0;
}
.form-btn-cancel {
  margin-left: 8px;
}
</style>
