<template>
  <div>
    <a-modal
      width="50%"
      top="20px"
      class="dialog-width-builder-wrapper"
      :visible.sync="dialogFormVisible"
      :title="title"
      :mask-closable="false"
      :confirmLoading="saveLoading"
      :okText="$t('btn_ok')"
      :cancelText="$t('btn_cancel')"
      @ok="save"
      @cancel="close"
    >
      <div @keyup="onEnterHandler">
        <jw-layout-builder
          v-if="dialogFormVisible"
          ref="componentBuilder"
          :layoutName="layoutName"
          :modelName="modelName"
          :instanceData="getInstanceData()"
          @change="onUpdate"
        >
          <div :slot="templateSlot" v-if="'templateSelect' === templateSlot">
            <a-select v-model.trim="prudictTemplateOid" clearable filterable>
              <a-option
                v-for="item in templateList"
                :key="item.label"
                :label="item.name"
                :value="item.label"
              >
              </a-option>
            </a-select>
          </div>
          <a-select :slot="viewSlotName" v-model.trim="viewVersion">
            <a-option
              v-for="item in instanceData.viewVersion"
              :key="item.label"
              :label="item.name"
              :value="item.label"
            ></a-option>
          </a-select>

          <div :slot="baselineSlotName">
            <a-radio-group
              v-model.trim="locationValue"
              @change="baselineLocationChange"
            >
              <a-radio label="0">{{ directory }}</a-radio>
              <a-radio label="1">{{ folder }}</a-radio>
            </a-radio-group>
            <img :src="folderImg" @click="selectfolder" class="folder-list" />
            <span
              v-show="locationValue == '1'"
              @click="selectfolder"
              class="widthLimit"
              :title="baselineLocationPathStr"
            >
              {{ baselineLocationPathStr }}
            </span>
          </div>

          <a-radio-group
            v-model.trim="radioValue"
            :slot="radioSlotName"
            slot-scope="categorySelectionData"
            @change="
              (item) => {
                radioChange(categorySelectionData.onUpdate, item);
              }
            "
          >
            <a-radio label="group">
              {{ $t("PDM.common.group") }}
            </a-radio>
            <a-radio label="user">{{ $t("PDM.common.user") }}</a-radio>
          </a-radio-group>
          <a-select
            :placeholder="selectPlaceholder"
            :slot="selectSlotName"
            slot-scope="nameSelectionData"
            v-model.trim="selectValue"
            @change="
              (item) => {
                onSelected(nameSelectionData.onUpdate, item);
              }
            "
          >
            <a-select-option
              v-for="item in options"
              :key="item.identifier"
              :value="item.identifier"
            >
              {{ item.Supplier_name }}
            </a-select-option>
          </a-select>

          <a-select
            slot="supplier"
            v-model.trim="supplier"
            slot-scope="supplierData"
            @change="
              (item) => {
                supplirtOnSelected(supplierData.onUpdate, item);
              }
            "
          >
            <a-select-option
              v-for="item in options"
              :key="item.oid"
              :value="item.oid"
            >
              {{ item.Supplier_name }}
            </a-select-option>
          </a-select>
        </jw-layout-builder>
        <a-form ref="folder-form">
          <a-form-model-item :label="$t('txt_create_location')" :required="true">
            <a-tree-select
              style="width: 100%"
              :placeholder="$t('txt_select_folder')"
              treeDefaultExpandAll
              :value="folderOid"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :tree-data="contentTreeData"
              @change="treeSelect"
            >
              <template slot="folderIconTitle" slot-scope="{ name, childType }">
                <span>
                  <jw-icon
                    style="margin-right: 8px"
                    :type="
                      childType === 'child'
                        ? '#jwi-wenjianga-youneiyong'
                        : '#jwi-chanpin'
                    "
                  />
                  <span>{{ name }}</span>
                </span>
              </template>
            </a-tree-select>
          </a-form-model-item>
        </a-form>
        <!-- <div class="flex justify-center">
          <a-button size="small" @click="close">{{ cancel }}</a-button>
          <template v-if="title=='新建基线' || title=='CreateBaseline'">
              <a-button type="primary" size="small" @click="nextStep">{{ next }}</a-button>
          </template> 
           <template v-else>
             <a-button type="primary" size="small"  @click="save">{{ comfirm }}</a-button>
           </template>
        </div> -->
      </div>
    </a-modal>
    <!-- <Move ref="move" :oldMasterOid="oldMasterOid" :oid="selectedOid" :titles="titlerss" :oidval="oidval" slot="middle">
    </Move> -->
  </div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { jwLayoutBuilder, jwIcon } from "jw_frame";
// import ServiceComponentBuilder from "abuilder_components/service-component-builder";
// import Move from "components/document/document-move-list";
import { getAllTemplate } from "apis/product/productStructure";
// 文件夹目录
const fetchfolderTree = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/folder/searchTree`,
  method: "get",
});
export default {
  props: [
    "oidval",
    "layoutName",
    "modelName",
    "slotName",
    "templateSlot",
    "intention",
    "title",
    "instanceData",
    "selectSlotName",
    "radioSlotName",
    "options",
    "viewSlotName",
    "delayHide",
    "baselineSlotName",
  ],

  data() {
    return {
      saveLoading:false,
      creattitle: "",
      titlerss: this.$t('txt_folder_path'),
      cancel:  this.$t('btn_cancel'),
      next:  this.$t('btn_next_step'),
      comfirm:  this.$t('btn_ok'),
      directory:  this.$t('txt_directory'),
      folder:  this.$t('txt_select_folder_please'),
      selectPlaceholder:  this.$t('msg_select'),
      folderImg: require("assets/image/folder2.png"),
      selectedOid: null,
      oldMasterOid: null,
      locationValue: "0",
      isEnterSubmit: false,
      dialogFormVisible: false,
      viewVersion: "",
      selectValue: "",
      prudictTemplateOid: "",
      radioValue: "",
      supplier: null,
      templateList: [],
      selectFolderData: {},
      baselineLocationPathStr: "",
      contentTreeData: [],
      folderOid: "",
      locationInfo: {},
    };
  },

  components: {
    jwLayoutBuilder,
    jwIcon,
    // ServiceComponentBuilder,
    // Move
  },
  created() {
    this.onSave = _.debounce(this.save, 200);
    this.getTemplate();
  },

  methods: {
    deepData(data, onlyDirectChildren = false) {
      const scopedSlots = { title: "folderIconTitle" };
      let arr = [];
      const loop = (tree) => {
        tree.map((item, index) => {
          // item.title = item.name;
          item.key = item.oid;
          item.value = item.oid;
          item.scopedSlots = scopedSlots;
          // 只获取第一层
          if (onlyDirectChildren) {
            const temp = {
              ...item,
            };
            delete temp.children;
            arr.push(temp);
            return;
          }
          if (item.children && item.children.length > 0) {
            item.children.map((item) => (item.childType = "child"));
            loop(item.children);
          }
        });
      };
      loop(data);

      return onlyDirectChildren ? arr : data;
    },
    // 获取文件夹树形目录结构
    fetchFold() {
      let { query } = this.$route;
      let param = {
        containerOid: query.oid,
        containerModel: query.masterType,
      };
      fetchfolderTree
        .execute(param)
        .then((data) => {
          this.folderOid = data[0].oid;
          this.locationInfo = {
            ...data[0],
          };
          this.contentTreeData = this.deepData(data);
        })
        .catch((err) => {
          console.log(err);
          this.tableLoading = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    // 递归查找选中的文件夹对象
    flatten(array) {
      var flattend = [];
      (function flat(array) {
        array.forEach(function (el) {
          for (let i in el) {
            if (Object.prototype.toString.call(el[i]) === "[object Array]")
              flat(el[i]);
          }
          flattend.push(el);
        });
      })(array);
      return flattend;
    },
    //树选择
    treeSelect(value, node, extra) {
      // console.log("treeSelect", this.contentTreeData[0].oid);
      // console.log("treeSelect", value, node, extra);
      let { contentTreeData, folderOid } = this;
      this.folderOid = value;
      let flattenArray = [...this.flatten(contentTreeData)];
      // console.log(flattenArray);
      let folderObj = {};
      flattenArray.map((item, index) => {
        if (item.oid === value) {
          console.log(item);
          folderObj = { ...item };
        }
      });
      console.log(folderObj);
      this.locationInfo = {
        ...folderObj,
      };
    },
    // 基线选择创建目录时，radio值变化时触发
    baselineLocationChange() {
      if (this.locationValue == "1" && this.baselineLocationPathStr == "") {
        this.selectfolder();
      }
    },
    nextStep() {
      let componentBuilder = this.$refs.componentBuilder;
      componentBuilder.validate().then(() => {
        let value = componentBuilder.getValue();
        if (value.lifecycle) {
          value.lifecycle = value.lifecycle.val;
        }
        if (value.viewVersion) {
          value.viewVersion = this.viewVersion;
        }
        if ("templateSelect" === this.templateSlot) {
          value.templateOid = this.prudictTemplateOid;
        }
        if ("baseLinePosition" === this.baselineSlotName) {
          // 基线专有数据
          if (this.locationValue == "1") {
            value.baselineFolderType = this.selectFolderData.modelType;
            value.baselineFolderOid = this.selectFolderData.oid;
          }
        }
        this.$emit("on-save", value, true);
        if (!this.delayHide) {
          this.hide();
          componentBuilder.clear();
        }
      });
    },

    // 创建基线时，选择文件夹
    selectfolder() {
      this.locationValue = "1";
      this.$refs.move.show("移动").then((res) => {
        this.selectFolderData = res.data;
        let curData = this.selectFolderData;
        let curNode = res;
        let baselineLocationPathStr = "";
        while (curNode) {
          baselineLocationPathStr =
            curData.name + "/" + baselineLocationPathStr;
          if (curNode == undefined) {
            break;
          }
          curData = curNode.data;
          if ("Product" == curData.modelType) {
            break;
          }
          curNode = curNode.parent;
          curData = curNode.data;
        }
        this.baselineLocationPathStr = baselineLocationPathStr;
      });
    },
    save() {
      let _this = this;
      let { locationInfo } = this;
      let componentBuilder = this.$refs.componentBuilder;
      componentBuilder.validate().then(() => {
        let value = componentBuilder.getValue();
        // 校验表单
        console.log(this.$refs["folder-form"]);
        // 通过folderOid和树形结构的数据，生成参数locationInfo
        value.locationInfo = {
          catalogOid: locationInfo.oid,
          catalogType: locationInfo.type,
          containerOid: locationInfo.containerOid,
          containerType: locationInfo.containerType,
        };
        /**
         *
         * noData参数处理
         *
         * */
        let noData = {
          ...value,
          name: value.name,
          source: value.source,
          defaultUnit: value.defaultUnit,
          levelForSecrecy: value.levelForSecrecy,
          Part_High: value.Part_High,
          description: value.description || "",
          genericType: "standard",
          modelDefinition: "Product",
          locationInfo: {
            ...value.locationInfo,
          },
        };
        // if (value.lifecycle) {
        //   value.lifecycle = value.lifecycle.val;
        // }
        // if (value.viewVersion) {
        //   value.viewVersion = this.viewVersion;
        // }
        // if ("templateSelect" === this.templateSlot) {
        //   value.templateOid = this.prudictTemplateOid;
        // }
        // if ("baseLinePosition" === this.baselineSlotName) {
        //   // 基线专有数据
        //   if (this.locationValue == "1") {
        //     value.baselineFolderType = this.selectFolderData.modelType;
        //     value.baselineFolderOid = this.selectFolderData.oid;
        //   }
        // }
        console.log("noData", noData);
        this.$emit("on-save", noData);
        if (!this.delayHide) {
          this.hide();
          componentBuilder.clear();
        }
      });
    },

    clearFeild() {
      this.prudictTemplateOid = "";
      this.selectValue = "";
      this.radioValue = "";
      this.locationValue = "0";
      this.selectFolderData = {};
      this.baselineLocationPathStr = "";
    },

    onEnterHandler(event) {
      const ENTER_KEY = 13;
      let keyCode = event.keyCode;

      if (keyCode === ENTER_KEY && this.isEnterSubmit) {
        this.onSave();
      }
    },

    onSelected(nameSlotUpdateHandler, item) {
      nameSlotUpdateHandler && nameSlotUpdateHandler(item);
    },

    supplirtOnSelected(nameSlotUpdateHandler, item) {
      console.log(nameSlotUpdateHandler, item, "options123");
      let result = _.find(this.options, (row) => {
        return row.oid == item;
      });

      nameSlotUpdateHandler && nameSlotUpdateHandler(result);
    },

    //radio组件动态获取值
    radioChange(radioSlotUpdateHandler, item) {
      radioSlotUpdateHandler && radioSlotUpdateHandler(item);
      this.selectValue = "";
      if (item == "group") {
        this.$emit("groupData", item);
      } else if (item == "user") {
        this.$emit("userData", item);
      }
    },
    // 查找用户有权限的模板
    getTemplate() {
      // getAllTemplate.execute().then((result) => {
      //   this.templateList = _.map(result, (item) => {
      //     return { label: item.oid, name: item.name };
      //   });
      // });
    },

    getInstanceData() {
      // console.log("this.instanceData", this.instanceData);
      return this.instanceData;
    },

    onUpdate(value) {
      let componentBuilder = this.$refs.componentBuilder;
      componentBuilder.froceUpdateChildComponents();
    },

    show(option) {
      this.getTemplate();
      let isEnterSubmit = option ? option.isEnterSubmit : false;
      this.isEnterSubmit = isEnterSubmit || false;
      this.dialogFormVisible = true;
      this.supplier = null;
      this.fetchFold();
    },

    hide() {
      this.dialogFormVisible = false;
      this.clearFeild();
    },

    close() {
      this.$refs.componentBuilder.clear();
      this.$emit("close");
      this.hide();
    },
  },
};
</script>

<style lang="less">
.slot-title {
  width: 60px;
  text-align: right;
  margin-right: 5px;
}
.slotname {
  display: flex;
  align-items: center;
  margin-left: -60px;
}
.folder-list {
  width: 18px;
  margin-top: 3px;
}

.dialog-width-builder-wrapper {
  .ops-bar {
    padding-bottom: 0;
    width: 200px;
    margin: 0 auto;

    .middle-btn {
      display: none;
    }

    & > div {
      width: auto;
    }
  }

  .layout-body-generated {
    padding-bottom: 0;
  }

  .widthLimit {
    line-height: 15px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 200px;
    display: inline-block;
  }

  .a-form--laba-top .a-form-item__label {
    width: 120px;
    float: left;
    text-align: right;
    padding-right: 5px;
  }

  .a-form--laba-top .a-form-item__content {
    margin-left: 120px;
  }
}

.justify-center {
  justify-content: center;
}
</style>
