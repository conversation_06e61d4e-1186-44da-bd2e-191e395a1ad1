<template>
  <a-drawer
    :title="ruleInfo && ruleInfo.oid ? $t('txt_update_features') : $t('txt_create_features')"
    width="45%"
    :visible="visible"
    :destroyOnClose="true"
    @close="onClose"
  >
    <div class="body-wrap">
      <div
        v-if="ruleInfo && ruleInfo.oid"
        class="title-wrap flex justify-between align-center"
      >
        <span class="title color85 font-700">{{$t('txt_rule_info')}}</span>
        <a-button type="link" v-if="isEdit" @click="onEdit">{{$t('txt_update_infp')}} </a-button>
        <a-button type="link" v-else @click="onCancel">{{$t('txt_cancel_info')}} </a-button>
      </div>
      <a-form-model ref="cresteForm" :model="form" hide-required-mark>
        <a-row :gutter="8">
          <a-col :span="12">
            <a-form-model-item
              :label="$t('txt_rule_name')"
              prop="ruleName"
              :colon="false"
              :rules="[
                { required: true, message: $t('txt_input'), trigger: 'change' },
                {
                  min: 1,
                  max: 50,
                  message: $t('txt_prompt'),
                  trigger: 'change',
                },
                { validator: this.validateName },
              ]"
            >
              <a-input
                v-model.trim="form.ruleName"
                :disabled="isEdit"
                :max-length="50"
                allow-clear
                :placeholder="$t('msg_input')"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item :label="$t('txt_is_enble')" prop="hasEnable" :colon="false">
              <a-switch
                default-checked
                v-model.trim="form.hasEnable"
                :disabled="isEdit"
              >
                <a-icon slot="checkedChildren" type="check" />
                <a-icon slot="unCheckedChildren" type="close" />
              </a-switch>
            </a-form-model-item>
          </a-col>
        </a-row>
        <div class="rule-item-wrap hide-mode-wrap" v-if="isHignMode">
          <div
            class="form-item-label color85 flex justify-between align-center"
          >
            <div>{{$t('txt_rule_code')}} ]</div>
            <a-button type="link" :disabled="isEdit" @click="onChangeMode"
              >{{$t('txt_easay_modul')}} </a-button
            >
          </div>
          <div class="condition-item-wrap">
            <a-form-model-item
              prop="programContent"
              :rules="[
                { required: true, message: $t('msg_select'), trigger: 'change' },
              ]"
            >
              <a-textarea
                v-model.trim="form.programContent"
                allow-clear
                :disabled="isEdit"
                class="textarea-input"
               :placeholder="$t('msg_input')"
              />
            </a-form-model-item>
          </div>
        </div>
        <div class="rule-item-wrap hide-mode-wrap" v-if="isHignMode">
          <div
            class="form-item-label color85 flex justify-between align-center"
          >
            <div>{{$t('txt_describe')}} </div>
          </div>
          <div class="condition-item-wrap">
            <a-form-model-item prop="description">
              <a-textarea
                v-model.trim="form.description"
                allow-clear
                :disabled="isEdit"
                class="textarea-input"
                :placeholder="$t('msg_input')"
              />
            </a-form-model-item>
          </div>
        </div>
        <div class="rule-item-wrap" v-if="!isHignMode">
          <div
            class="form-item-label color85 flex justify-between align-center"
          >
            <div>
            {{$t('txt_as_follows')}}  
              <a-button
                type="link"
                v-if="isConAll"
                :disabled="isEdit"
                @click="onSwitchCon"
                >{{$t('txt_all')}} </a-button
              >
              <a-button
                type="link"
                v-else
                :disabled="isEdit"
                @click="onSwitchCon"
                >{{$t('txt_any_one')}} </a-button
              >
            {{$t('txt_update_features')}}  
            </div>
            <a-button type="link" :disabled="isEdit" @click="onChangeMode"
              >{{$t('txt_adva_model')}} </a-button
            >
          </div>
          <div class="condition-item-wrap">
            <div
              class="condition-item"
              v-for="(item, index) in form.conditions"
              :key="index"
            >
              <a-row :gutter="8">
                <a-col :span="6">
                  <a-form-model-item
                    :prop="'conditions.' + index + '.laborOid'"
                    :rules="[
                      { required: true, message: $t('msg_select'), trigger: 'change' },
                    ]"
                  >
                    <a-select
                      v-model.trim="item.laborOid"
                      :disabled="isEdit"
                      :placeholder="$t('txt_select_node')"
                      showSearch
                      filterOption
                      option-filter-prop="children"
                      @focus="getRulesNodes"
                      @select="getRulesFeatures(item)"
                      @change="onChangeNodeC(item)"
                    >
                      <a-select-option
                        v-for="val in nodeList"
                        :title="val.name"
                        :key="val.oid"
                        :value="val.oid"
                      >
                        {{ val.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="6">
                  <a-form-model-item
                    :prop="'conditions.' + index + '.featureCode'"
                    :rules="[
                      { required: true, message: $t('msg_select'), trigger: 'change' },
                    ]"
                  >
                    <a-select
                      v-model.trim="item.featureCode"
                      :disabled="isEdit"
                      :placeholder="$t('txt_select_features')"
                      showSearch
                      filterOption
                      option-filter-prop="children"
                      @focus="getRulesFeatures(item)"
                      @select="getRulesFValues(item)"
                      @change="onChangeFeatureC(item)"
                    >
                      <a-select-option
                        v-for="val in item.featureList"
                        :title="val.name"
                        :key="val.oid"
                        :value="val.number"
                      >
                        {{ val.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="4">
                  <a-form-model-item
                    :prop="'conditions.' + index + '.condition'"
                    :rules="[
                      { required: true, message: $t('msg_select'), trigger: 'change' },
                    ]"
                  >
                    <a-select
                      v-model.trim="item.condition"
                      :placeholder="$t('txt_conditions_symbol')"
                      :disabled="isEdit"
                      @change="onChangeMarkC(item)"
                    >
                      <a-select-option
                        v-for="val in markList"
                        :title="`${val.label}(${val.value})`"
                        :key="val.value"
                        :value="val.value"
                      >
                        {{ val.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="6">
                  <a-form-model-item
                    :prop="'conditions.' + index + '.value'"
                    v-if="item.valueType === 'text'"
                    :rules="[
                      { required: true, message: $t('msg_select'), trigger: 'change' },
                      {
                        min: 1,
                        max: 50,
                        message: $t('txt_prompt'),
                        trigger: 'change',
                      },
                    ]"
                  >
                    <a-input
                      v-model.trim="item.value"
                      :disabled="isEdit"
                      :max-length="50"
                      allow-clear
                      :placeholder="$t('msg_input')"
                      @change="onChangeValueC(item)"
                    />
                  </a-form-model-item>
                  <a-form-model-item
                    :prop="'conditions.' + index + '.value'"
                    v-else
                    :rules="[
                      { required: true, message:$t('msg_select'), trigger: 'change' },
                    ]"
                  >
                    <a-select
                      v-model.trim="item.value"
                      :disabled="isEdit"
                      :placeholder="$t('txt_select_value')"
                      showSearch
                      filterOption
                      option-filter-prop="children"
                      @focus="getRulesFValues(item)"
                      @change="onChangeValueC(item)"
                    >
                      <a-select-option
                        v-for="val in item.featureValueList"
                        :title="val.value"
                        :key="val.oid"
                        :value="val.value"
                      >
                        {{ val.value }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="2">
                  <span @click="onDeleteCon(index)">
                    <jw-icon type="jwi-icondelete" />
                  </span>
                </a-col>
              </a-row>
            </div>
          </div>
          <a-button class="btn-add" :disabled="isEdit" @click="onAddConditions"
            >{{$t('txt_add_conditions')}}</a-button
          >
        </div>
        <div class="rule-item-wrap" v-if="!isHignMode">
          <div class="form-item-label color85 flex align-center">
            <div>{{$t('txt_followwing_result')}} </div>
          </div>
          <div class="condition-item-wrap">
            <div
              class="condition-item"
              v-for="(item, index) in form.results"
              :key="index"
            >
              <a-row :gutter="8">
                <a-col :span="6">
                  <a-form-model-item
                    :prop="'results.' + index + '.laborOid'"
                    :rules="[
                      { required: true, message:$t('msg_select'), trigger: 'change' },
                    ]"
                  >
                    <a-select
                      v-model.trim="item.laborOid"
                      :disabled="isEdit"
                      :placeholder="$t('txt_select_node')"
                      showSearch
                      filterOption
                      option-filter-prop="children"
                      @focus="getRulesNodes"
                      @select="getRulesFeatures(item)"
                      @change="onChangeNodeR(item)"
                    >
                      <a-select-option
                        v-for="val in nodeList"
                        :title="val.name"
                        :key="val.oid"
                        :value="val.oid"
                      >
                        {{ val.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="6">
                  <a-form-model-item
                    :prop="'results.' + index + '.featureCode'"
                    :rules="[
                      { required: true, message: $t('msg_select'), trigger: 'change' },
                    ]"
                  >
                    <a-select
                      v-model.trim="item.featureCode"
                      :disabled="isEdit"
                      :placeholder="$t('txt_select_features')"
                      showSearch
                      filterOption
                      option-filter-prop="children"
                      @focus="getRulesFeatures(item)"
                      @select="getRulesFValues(item)"
                      @change="onChangeFeatureR(item)"
                    >
                      <a-select-option
                        v-for="val in item.featureList"
                        :title="val.name"
                        :key="val.oid"
                        :value="val.number"
                      >
                        {{ val.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="4">
                  <a-form-model-item
                    :prop="'results.' + index + '.condition'"
                    :rules="[
                      { required: true, message: $t('msg_select'), trigger: 'change' },
                    ]"
                  >
                    <a-select
                      v-model.trim="item.condition"
                      :placeholder="$t('txt_conditions_symbol')"
                      :disabled="isEdit"
                      @change="onChangeMarkR(item)"
                    >
                      <a-select-option
                        v-for="val in markList"
                        :title="`${val.label}(${val.value})`"
                        :key="val.value"
                        :value="val.value"
                      >
                        {{ val.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="6">
                  <a-form-model-item
                    :prop="'results.' + index + '.value'"
                    v-if="item.valueType === 'text'"
                    :rules="[
                      { required: true, message: $t('msg_select'), trigger: 'change' },
                      {
                        min: 1,
                        max: 50,
                        message: $t('txt_prompt'),
                        trigger: 'change',
                      },
                    ]"
                  >
                    <a-input
                      v-model.trim="item.value"
                      :disabled="isEdit"
                      :max-length="50"
                      allow-clear
                      :placeholder="$t('msg_input')"
                      @change="onChangeValueR(item)"
                    />
                  </a-form-model-item>
                  <a-form-model-item
                    :prop="'results.' + index + '.values'"
                    v-else
                    :rules="[
                      { required: true, message: $t('msg_select'), trigger: 'change' },
                    ]"
                  >
                    <a-select
                      v-model.trim="item.values"
                      :placeholder="$t('txt_select_value')"
                      :disabled="isEdit"
                      mode="multiple"
                      @focus="getRulesFValues(item)"
                      @change="onChangeValueR(item)"
                    >
                      <a-select-option
                        v-for="val in item.featureValueList"
                        :title="val.value"
                        :key="val.oid"
                        :value="val.value"
                      >
                        {{ val.value }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="2">
                  <span @click="onDeleteRes(index)">
                    <jw-icon type="jwi-icondelete" />
                  </span>
                  <a-tooltip placement="right">
                    <template slot="title">
                      <span>{{ item.isShow ? $t('txt_features_show') :$t('txt_features_hide')  }}</span>
                    </template>
                    <span v-if="item.isShow" @click="onChangeShow(item)">
                      <jw-icon type="jwi-visible" />
                    </span>
                    <span v-else @click="onChangeShow(item)">
                      <jw-icon type="jwi-invisible" />
                    </span>
                  </a-tooltip>
                </a-col>
              </a-row>
            </div>
          </div>
          <a-button class="btn-add" :disabled="isEdit" @click="onAddResults">
           {{$t('txt_add_result')}} 
          </a-button>
        </div>
      </a-form-model>
    </div>
    <div class="foot-wrap">
      <a-button type="primary" @click="onSave">{{$t('btn_save')}}</a-button>
      <a-button class="btn-cancel" @click="onClose">{{$t('btn_cancel')}} </a-button>
    </div>
  </a-drawer>
</template>

<script>
import { jwIcon } from "jw_frame";
import {
  fetchRulesNodes,
  fetchRulesFeatures,
  fetchRulesFValues,
  createGeneralRules,
  updateGeneralRules,
  createSeniorRules,
  updateSeniorRules,
} from "apis/product/productStructure";
import validateMixin from "./validateMixin";
export default {
  name: "createRules",
  components: {
    jwIcon,
  },
  props: ["visible", "ruleInfo", "toEdit"],
  mixins: [validateMixin],
  data() {
    return {
      form: {},
      nodeList: [],
      markList: [
        { label: this.$t('txt_greater_than'), value: ">" },
        { label:  this.$t('txt_equal'), value: "=" },
        { label:  this.$t('txt_less'), value: "<" },
        { label:  this.$t('txt_not_equal'), value: "≠" },
        { label:  this.$t('txt_greater_equal'), value: ">=" },
        { label:  this.$t('txt_less_equal'), value: "<=" },
      ],
      isHignMode: false,
      isConAll: true,
      isEdit: true,
    };
  },
  mounted() {},
  watch: {
    async visible(val) {
      if (val) {
        if (this.toEdit) {
          this.isEdit = false;
        } else {
          this.isEdit = true;
        }
        if (this.ruleInfo.oid) {
          if (!this.ruleInfo.switchSenior) {
            await this.getRulesNodes();
            this.ruleInfo.conditions.forEach((item) => {
              this.getRulesFeatures(item);
            });
          }
          this.form = { ...this.ruleInfo };
          this.isHignMode = this.ruleInfo.switchSenior;
          if (this.ruleInfo.conditionType === "All") {
            this.isConAll = true;
          } else if (this.form.conditionType === "Any") {
            this.isConAll = false;
          }
        } else {
          this.form = {
            ruleName: "",
            hasEnable: true,
            conditions: [
              {
                laborOid: undefined,
                featureCode: undefined,
                condition: undefined,
                value: undefined,
                featureList: [],
                featureValueList: [],
              },
            ],
            results: [
              {
                laborOid: undefined,
                featureCode: undefined,
                condition: undefined,
                value: "",
                values: [],
                featureList: [],
                featureValueList: [],
                isShow: true,
              },
            ],
            conditionType: "All",
            programContent: "",
            description: "",
          };
        }
      }
    },
  },
  methods: {
    async getRulesNodes() {
      let res = await fetchRulesNodes.execute({
        productOid: this.$route.query.oid,
      });
      this.nodeList = res;
    },
    getRulesFeatures(item) {
      console.log("item", item);
      console.log("this.nodeList", this.nodeList);
      if (item.laborOid) {
        fetchRulesFeatures
          .execute({
            masterOid: item.laborOid,
            masterType: this.nodeList.filter(
              (val) => val.oid === item.laborOid
            )[0].type,
          })
          .then((res) => {
            console.log(res);
            item.featureList = res;
          })
          .catch((err) => {
            console.log(err);
            if (err.msg) {
              this.$error(err.msg);
            }
          });
      }
    },
    getRulesFValues(item) {
      if (item.featureCode && item.featureList.length > 0) {
        console.log("item.featureList", item.featureList);
        fetchRulesFValues
          .execute({
            featureOid: item.featureList.filter(
              (val) => val.number === item.featureCode
            )[0].oid,
            featureType: item.featureList.filter(
              (val) => val.number === item.featureCode
            )[0].type,
          })
          .then((res) => {
            item.featureValueList = res;
          })
          .catch((err) => {
            if (err.msg) {
              this.$error(err.msg);
            }
          });
      }
    },
    onChangeNodeC(item) {
      item.featureCode = undefined;
      item.featureName = "";
      item.featureList = [];
      item.value = undefined;
      item.featureValueList = [];
    },
    onChangeFeatureC(item) {
      item.value = undefined;
      item.featureValueList = [];
      item.valueType = item.featureList.filter(
        (val) => val.number === item.featureCode
      )[0].valueType;
    },
    onChangeValueC(item) {
      if (item.condition && item.value) {
        let reg = /^[+-]?\d+(\.\d+)?$/g;
        let flag = reg.test(item.value);
        if (!flag && item.condition !== "=" && item.condition !== "≠") {
          this.$warning( this.$t('txt_condition_not'));
          item.value = "";
        }
      }
    },
    onChangeMarkC(item) {
      if (item.condition && item.value) {
        let reg = /^[+-]?\d+(\.\d+)?$/g;
        let flag = reg.test(item.value);
        if (!flag && item.condition !== "=" && item.condition !== "≠") {
           this.$warning( this.$t('txt_condition_not'));
          item.condition = "";
        }
      }
    },
    onChangeNodeR(item) {
      item.featureCode = undefined;
      item.featureName = "";
      item.featureList = [];
      item.values = [];
      item.value = "";
      item.featureValueList = [];
    },
    onChangeFeatureR(item) {
      item.valueType = item.featureList.filter(
        (val) => val.number === item.featureCode
      )[0].valueType;
      if (item.valueType === "select") {
        item.values = [];
        item.featureValueList = [];
      } else {
        item.value = "";
      }
    },
    onChangeValueR(item) {
      if (item.condition) {
        let reg = /^[+-]?\d+(\.\d+)?$/g;
        if (
          item.valueType === "select" &&
          item.values &&
          item.values.length > 0
        ) {
          if (item.values.length > 0) {
            let flag = item.values.every((val) => reg.test(val));
            if (!flag && item.condition !== "=" && item.condition !== "≠") {
               this.$warning( this.$t('txt_condition_not'));
              item.values = [];
            }
            if (item.values.length > 1) {
              item.isShow = true;
            }
          }
        } else {
          if (item.value) {
            let flag = reg.test(item.value);
            if (!flag && item.condition !== "=" && item.condition !== "≠") {
               this.$warning( this.$t('txt_condition_not'));
              item.value = "";
            }
          }
        }
      }
    },
    onChangeMarkR(item) {
      if (item.condition !== "=" || item.condition !== "≠") {
        if (item.valueType === "select") {
          if (item.values.length > 1) {
             this.$warning( this.$t('txt_condition_not'));
            item.values = [];
          }
        } else {
          if (item.value) {
            let reg = /^[+-]?\d+(\.\d+)?$/g;
            let flag = reg.test(item.value);
            if (!flag && item.condition !== "=" && item.condition !== "≠") {
               this.$warning( this.$t('txt_condition_not'));
              item.value = "";
            }
          }
        }
      }
    },
    onClose() {
      this.isHignMode = false;
      this.isConAll = true;
      this.$emit("close");
    },
    onEdit() {
      this.isEdit = false;
    },
    onCancel() {
      this.isEdit = true;
    },
    onSave() {
      this.$refs.cresteForm.validate((valid) => {
        if (valid) {
          let params = {};
          let api = "";
          let msg = "";
          console.log(this.isHignMode);
          if (this.isHignMode) {
            params = {
              ruleContextOid: this.$route.query.oid,
              oid: this.ruleInfo.oid,
              ruleName: this.form.ruleName,
              isEnable: this.form.isEnable,
              hasEnable: this.form.hasEnable,
              drlContent: this.form.programContent,
              description: this.form.description,
              switchSenior: true,
            };
            if (this.ruleInfo.oid) {
              api = updateSeniorRules;
              msg =  this.$t('msg_update_success');
            } else {
              api = createSeniorRules;
              msg =  this.$t('msg_save_success');
            }
          } else {
            delete this.form.programContent;
            delete this.form.description;
            let conditions = this.form.conditions.map((item) => {
              item.laborName = this.nodeList.filter(
                (val) => item.laborOid === val.oid
              )[0].name;
              item.featureName = item.featureList.filter(
                (val) => item.featureCode === val.number
              )[0].name;
              item.values = [item.value]
                item.featureList = [].concat(
                item.featureList.filter(
                  (itemChild) => itemChild.number == item.featureCode
                )
              );
              item.featureValueList = [].concat(
                item.featureValueList.filter((itemChild) =>
                  item.values.includes(itemChild.value)
                )
              );
              return item;
            });
            let results = this.form.results.map((item) => {
              item.laborName = this.nodeList.filter(
                (val) => item.laborOid === val.oid
              )[0].name;
              item.featureName = item.featureList.filter(
                (val) => item.featureCode === val.number
              )[0].name;
              item.featureList = [].concat(
                item.featureList.filter(
                  (itemChild) => itemChild.number == item.featureCode
                )
              );
              item.featureValueList = [].concat(
                item.featureValueList.filter((itemChild) =>
                  item.values.includes(itemChild.value)
                )
              );
              return item;
            });
            console.log("results", results);
            params = {
              ruleContextOid: this.$route.query.oid,
              conditions: conditions,
              results: results,
              oid: this.ruleInfo.oid,
              ruleName: this.form.ruleName,
              conditionType: this.form.conditionType,
              isEnable: this.form.isEnable,
              hasEnable: this.form.hasEnable,
              switchSenior: false,
            };
            if (this.ruleInfo.oid) {
              api = updateGeneralRules;
              msg =  this.$t('msg_update_success');
            } else {
              api = createGeneralRules;
              msg =  this.$t('msg_save_success');
            }
          }
          api
            .execute(params)
            .then((res) => {
              this.$success(msg);
              this.onClose();
              this.$emit("getList");
            })
            .catch((err) => {
              if (err.msg) {
                this.$error(err.msg);
              }
            });
        } else {
          return false;
        }
      });
    },
    onAddConditions() {
      this.form.conditions.push({
        laborOid: undefined,
        featureCode: undefined,
        condition: undefined,
        value: undefined,
        featureList: [],
        featureValueList: [],
      });
    },
    onAddResults() {
      this.form.results.push({
        laborOid: undefined,
        featureCode: undefined,
        condition: undefined,
        value: "",
        values: [],
        featureList: [],
        featureValueList: [],
        isShow: true,
      });
    },
    onChangeMode() {
      this.isHignMode = !this.isHignMode;
      if (this.ruleInfo.switchSenior && this.ruleInfo.oid) {
        this.form = {
          ruleName: this.form.ruleName,
          hasEnable: this.form.hasEnable,
          conditions: [
            {
              laborOid: undefined,
              featureCode: undefined,
              condition: undefined,
              value: undefined,
              featureList: [],
              featureValueList: [],
            },
          ],
          results: [
            {
              laborOid: undefined,
              featureCode: undefined,
              condition: undefined,
              value: "",
              values: [],
              featureList: [],
              featureValueList: [],
              isShow: true,
            },
          ],
          conditionType: "All",
          programContent: this.form.programContent,
          description: this.form.description,
        };
      }
    },
    onSwitchCon() {
      this.isConAll = !this.isConAll;
      if (this.isConAll) {
        this.form.conditionType = "All";
      } else {
        this.form.conditionType = "Any";
      }
    },
    onDeleteCon(index) {
      if (this.isEdit) return;
      if (this.form.conditions.length === 1) {
        this.$warning( this.$t('txt_keep_data'));
        return;
      }
      this.form.conditions.splice(index, 1);
    },
    onDeleteRes(index) {
      if (this.isEdit) return;
      if (this.form.results.length === 1) {
       this.$warning( this.$t('txt_keep_data'));
        return;
      }
      this.form.results.splice(index, 1);
    },
    onChangeShow(item) {
      if (this.isEdit) return;
      if (item.valueType === "select") {
        if (item.values.length > 1) {
          item.isShow = true;
        } else {
          item.isShow = !item.isShow;
        }
      } else {
        item.isShow = !item.isShow;
      }
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.ant-drawer-body {
  padding: 0;
  .jwifont {
    width: 16px;
    min-width: 16px;
    height: 16px;
    min-height: 16px;
  }
}
.body-wrap {
  // height: calc(100vh - 125px);
  overflow: auto;
  padding: 24px;
  .title-wrap {
    position: relative;
    margin-bottom: 15px;
    .title {
      margin-left: 10px;
      font-size: 16px;
      &:after {
        content: "";
        position: absolute;
        width: 3px;
        height: 20px;
        left: 0;
        top: 6px;
        border-radius: 4px;
        background: #255ed7;
      }
    }
  }
  .rule-item-wrap {
    margin-bottom: 20px;
    &.hide-mode-wrap {
      margin-bottom: 0;
    }
    .form-item-label {
      margin-bottom: 6px;
    }
    .ant-select {
      width: 100%;
    }
    .textarea-input /deep/.ant-input {
      height: 110px;
    }
    .jwifont {
      margin-top: 12px;
      cursor: pointer;
      &.info-font {
        margin-top: 0;
        margin-left: 16px;
      }
    }
    .condition-item {
      margin-bottom: 12px;
    }
  }
}
.foot-wrap {
  height: 70px;
  line-height: 70px;
  text-align: center;
  border-top: 1px solid rgba(30, 32, 42, 0.06);
  .btn-cancel {
    margin-left: 8px;
  }
}
</style>
