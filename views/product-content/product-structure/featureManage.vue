<template>
  <div class="base-panel all-background" style="padding: 0; background: #fff">
    <div class="object-details-toolbar">
      <div class="left">
        <span class="route">
          <router-link
            :to="{
              path: '/product-content',
              query: {
                ...$route.query,
                tabActive: 'structure',
              },
            }"
          >
          {{$t('txt_product_config')}} 
          </router-link>
          &gt;
          <!-- <router-link
            :to="{
              path: '/product-content',
              query: {
                id: $route.query.id,
                type: $route.query.type,
                containerModel: $route.query.containerModel,
                containerName: $route.query.containerName,
                containerFlag: $route.query.containerFlag,
              },
            }"
          > -->
          <!-- {{ $route.query.containerName }} -->
          {{$t('txt_property_management')}} 
          <!-- </router-link> -->
        </span>
        <!-- <span class="title" :title="objectDetailsData.name">
          <span
            >{{ objectDetailsData.name }}, {{ objectDetailsData.number }},
            {{ objectDetailsData.displayVersion }}</span
          >
        </span> -->
      </div>
    </div>
    <div class="feature-manage-wrap">
      <!-- <div class="head-wrap">
        <div class="flex align-center">
          <span @click="goBack">
            <jw-icon type="jwi-iconarrow-left" />
          </span>
          <span class="head-title color85 font-700">特性管理</span>
        </div>
      </div> -->
      <div class="body-wrap">
        <a-tabs v-model.trim="activeTab" @change="onChangeTab">
          <a-tab-pane key="lists" :tab="$t('txt_property_unit')">
            <feature-list></feature-list>
          </a-tab-pane>
          <a-tab-pane key="rules" :tab="$t('txt_features_rule')">
            <feature-rules></feature-rules>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import { jwIcon, jwPage } from "jw_frame";
import featureList from "./featureList";
import featureRules from "./featureRules";
export default {
  name: "featureManage",
  components: {
    jwIcon,
    jwPage,
    featureList,
    featureRules,
  },
  data() {
    return {
      activeTab: "lists",
    };
  },
  mounted() {},
  methods: {
    goBack() {

    },
    onChangeTab(key) {},
  },
};
</script>

<style lang="less" scoped>
.object-details-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 24px;
  > div > span {
    margin-right: 5px;
    > i {
      font-size: 18px;
    }
  }
  .left {
    display: flex;
    align-items: center;
    .title {
      // max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
      background-color: #ddd;
      padding: 5px 13px;
      border-radius: 4px;
    }
    .route {
      color: #a69f9f;
      > button {
        color: #a69f9f;
        padding: 0;
        &:hover {
          color: #40a9ff;
        }
      }
    }
    > i {
      &:hover {
        color: #40a9ff;
        cursor: pointer;
      }
    }
  }
  .right {
    display: flex;
    align-items: center;
    margin-right: 100px;
    > div,
    > button {
      margin-left: 15px;
    }
    .ant-btn {
      width: inherit !important;
    }
  }
}
.feature-manage-wrap {
  margin: 5px;
  background: var(--light);
  border-radius: 4px;
  .jwifont {
    width: 16px;
    min-width: 16px;
    height: 16px;
    min-height: 16px;
  }
  .head-wrap {
    padding: 16px 20px 0;
    .head-title {
      font-size: 20px;
    }
  }
  .body-wrap {
    overflow: auto;
    &::-webkit-scrollbar {
      width: 0px;
    }
    /deep/.ant-tabs {
      color: rgba(30, 32, 42, 0.65);
    }
    /deep/.ant-tabs-nav .ant-tabs-tab {
      padding: 19px 0;
    }
    /deep/.ant-tabs-bar {
      padding: 0 20px;
      margin-bottom: 0;
    }
    /deep/.ant-tabs-nav .ant-tabs-tab-active {
      color: rgba(30, 32, 42, 0.85);
      // font-weight: 700;
    }
  }
}
</style>
