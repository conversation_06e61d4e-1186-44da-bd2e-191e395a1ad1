<template>
  <div class="content-card">
    <a-modal
      :visible="visible"
      :title="$t('txt_create_demand')"
      :mask-closable="false"
      :footer="null"
      @cancel="handleCancel"
    >
      <div class="create-temp-wrap">
        <a-form-model
          ref="createForm"
          :model="form"
          :rules="rules"
        >
          <a-form-model-item
            :label="$t('txt_titles')"
            prop="name"
          >
            <a-input
              v-model.trim="form.name"
              :max-length="50"
              allow-clear
              :placeholder="$t('txt_input')"
            />
          </a-form-model-item>
          <a-row :gutter="10">
            <a-col :span="12">
              <a-form-model-item
                :label="$t('txt_classify')"
                prop="classifyCode"
              >
                <a-input
                  v-model.trim="form.classifyCode"
                  allow-clear
                  :placeholder="$t('txt_input')"
                />
                <!-- <a-select
                  v-model.trim="form.classifyCode"
                  allow-clear
                  placeholder="请选择"
                  @focus="getRequirementType"
                >
                  <a-select-option
                    v-for="item in classList"
                    :key="item.value"
                    :value="item.value"
                  >
                    {{ item.label }}
                  </a-select-option>
                </a-select> -->
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                :label="$t('txt_from_where')"
                prop="source"
              >
                <a-input
                  v-model.trim="form.source"
                  allow-clear
                  :placeholder="$t('txt_input')"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-form-model-item
            :label="$t('txt_description')"
            prop="description"
          >
            <a-textarea
              v-model.trim="form.description"
              allow-clear
              class="textarea-input"
              :placeholder="$t('txt_input')"
            />
          </a-form-model-item>
          <a-form-model-item
            prop="files"
            :colon="false"
          >
            <span>{{$t('txt_upload_feil')}}：</span>
            <a-upload
              name="file"
              :multiple="true"
              action="string"
              :file-list="fileList"
              :before-upload="() => false"
              @change="handleChangeFile"
            >
              <a-button>
                <a-icon type="upload" />{{$t('txt_click_upload')}}
              </a-button>
            </a-upload>
          </a-form-model-item>
          <a-form-model-item class="form-item-btns text-right">
            <a-button
              type="primary"
              :loading="createLoading"
              @click="beforeCreate"
            >{{$t('btn_ok')}}</a-button>
            <a-button
              class="form-btn-cancel"
              @click="handleCancel"
            >{{$t('btn_cancel')}}</a-button>
          </a-form-model-item>
        </a-form-model>
      </div>
    </a-modal>
  </div>
</template>

<script>
import {
  fetchRequirementType,
  addRequirement,
} from "apis/product/productStructure";
import validateMixin from "./validateMixin";
export default {
  name: "deamndCreate",
  props: ["visible", "nodeInfo"],
  mixins: [validateMixin],
  data() {
    return {
      form: {
        name: "",
        classifyCode: undefined,
        classify: "",
        source: "",
        description: "",
        files: [],
      },
      createLoading: false,
      fileList: [],
      classList: [],
      rules: {
        name: [
          { required: true, message: this.$t("txt_input"), trigger: "change" },
          {
            min: 1,
            max: 50,
            message: this.$t("txt_prompt"),
            trigger: "change",
          },
          { validator: this.validateName },
        ],
        classifyCode: [
          { required: true, message: this.$t("msg_select"), trigger: "change" },
        ],
        source: [
          { required: true, message: this.$t("txt_input"), trigger: "change" },
        ],
      },
    };
  },
  mounted() {},
  methods: {
    getRequirementType() {
      // fetchRequirementType
      //   .execute({
      //     currPage: 1,
      //     pageSize: 50,
      //   })
      //   .then((res) => {
      //     this.classList = res.map((item) => {
      //       return {
      //         value: item.multiLanguageValue[1].valueCode,
      //         label: item.multiLanguageValue[1].valueText,
      //       };
      //     });
      //   })
      //   .catch((err) => {
      //     if (err.msg) {
      //       this.$error(err.msg);
      //     }
      //   });
    },
    handleChangeFile(info) {
      this.fileList = info.fileList;
    },
    handleCancel() {
      this.$refs.createForm.resetFields();
      this.$refs.createForm.clearValidate();
      this.fileList = [];
      this.$emit("close");
    },
    beforeCreate() {
      if (this.fileList.length > 0) {
        let formData = new FormData();
        let xhr = new XMLHttpRequest();
        this.fileList.forEach((item) => {
          formData.append("file", item.originFileObj);
        });
        xhr.open(
          "POST",
          `${Jw.gateway}/${Jw.fileServer}/file/multiUpload-v2`,
          true
        );
        xhr.onload = (res) => {
          const response = JSON.parse(xhr.response);
          response.result.map((item) => (item.name = item.fileName));
          this.form.files = response.result;
          this.handleCreate();
        };
        xhr.send(formData);
      } else {
        this.handleCreate();
      }
    },
    handleCreate() {
      this.createLoading = true;
      this.$refs.createForm.validate((valid) => {
        if (valid) {
          // this.form.classify = _.find(this.classList, (item) => {
          //   return item.value === this.form.classifyCode;
          // }).label;
          this.form.classify = this.form.classifyCode;
          addRequirement
            .execute({
              masterType: this.nodeInfo.type,
              masterOid: this.nodeInfo.oid,
              reqDatas: this.form,
            })
            .then((res) => {
              this.handleCancel();
              this.$emit("getList");
            })
            .catch((err) => {
              if (err.msg) {
                this.$error(err.msg);
              }
            })
            .finally(() => {
              this.createLoading = false;
            });
        } else {
          this.createLoading = false;
          return false;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.textarea-input /deep/.ant-input {
  height: 110px;
}
.form-item-btns {
  margin: 25px 0 0;
}
.form-btn-cancel {
  margin-left: 8px;
}
</style>
