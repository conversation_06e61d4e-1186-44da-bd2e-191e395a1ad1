<template>
  <a-modal
    :visible="visible"
    :title="featureInfo.oid ? $t('txt_update_features') :$t('txt_create_features')"
    :mask-closable="false"
    :footer="null"
    :destroyOnClose="true"
    @cancel="onCancel"
  >
    <div class="create-temp-wrap">
      <a-form-model ref="createFeatureForm" :model="form" :rules="rules">
        <a-form-model-item :label="$t('txt_serial_characteristics')" prop="sortNo">
          <a-input
            v-model.trim="form.sortNo"
            :type="'number'"
            allow-clear
            :placeholder="$t('txt_input')"
          />
        </a-form-model-item>
        <a-form-model-item :label="$t('txt_node_name')" prop="node">
          <a-select
            v-model.trim="form.node"
            :placeholder="$t('msg_select')"
            :disabled="featureInfo.oid ? true : false"
          >
            <a-select-option
              v-for="item in nodeList"
              :key="item.oid"
              :value="item.oid"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :label="$t('txt_serial_name')" prop="name">
          <a-input
            v-model.trim="form.name"
            :max-length="50"
            allow-clear
            :placeholder="$t('txt_input')"
          />
        </a-form-model-item>
        <!-- <a-form-model-item label="特性类型" prop="valueType">
                    <a-radio-group v-model.trim="form.valueType" default-value="select">
                        <a-radio value="select">标签选择</a-radio>
                        <a-radio value="text">文本输入</a-radio>
                    </a-radio-group>
                </a-form-model-item> -->
        <a-form-model-item class="form-item-btns text-right">
          <a-button type="primary" 
            :loading="createLoading" @click="onCreate">{{$t('btn_ok')}} </a-button>
          <a-button class="form-btn-cancel" @click="onCancel">{{$t('btn_cancel')}} </a-button>
        </a-form-model-item>
      </a-form-model>
    </div>
  </a-modal>
</template>

<script>
import {
  fetchStructureNode,
  addFeatures,
  updateFeature,
} from "apis/product/productStructure";
import validateMixin from "./validateMixin";
export default {
  name: "featureAdd",
  props: ["visible", "nodeInfo", "featureInfo"],
  mixins: [validateMixin],
  data() {
    return {
      createLoading:false,
      form: {},
      rules: {
        sortNo: [{ required: true, message: this.$t('txt_input'), trigger: "change" }],
        node: [{ required: true, message: this.$t('msg_select'), trigger: "change" }],
        name: [
          { required: true, message: this.$t('txt_input'), trigger: "change" },
          { min: 1, max: 50, message: this.$t('txt_prompt'), trigger: "change" },
          { validator: this.validateName },
        ],
        // valueType: [
        //     { required: true },
        // ],
      },
      nodeList: [],
    };
  },
  mounted() {},
  watch: {
    visible(val) {
      if (val) {
        this.getNodeList();
        if (this.featureInfo.oid) {
          this.form = { ...this.featureInfo };
          this.form.node = this.nodeInfo.oid;
        } else {
          this.form = {
            node: undefined,
            name: "",
            valueType: "select",
          };
        }
      }
    },
  },
  methods: {
    getNodeList() {
      fetchStructureNode
        .execute({
          productOid: this.$route.query.oid,
        })
        .then((res) => {
          this.nodeList = res;
        })
        .catch((err) => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
    onCancel() {
      this.$emit("close");
    },
    onCreate() {
      this.createLoading=true;
      this.$refs.createFeatureForm.validate((valid) => {
        if (valid) {
          let api = addFeatures;
          let msg = "";
          let formObj = this.nodeList.filter(
            (item) => item.oid === this.form.node
          )[0];
          let featureData = {
              sortNo: this.form.sortNo,
              oid: this.form.oid,
              name: this.form.name,
              valueType: this.form.valueType,
          };
          if (this.featureInfo.oid) {
            api = updateFeature;
            msg = this.$t('msg_update_success');
            featureData.number = formObj.number;
          } else {
            api = addFeatures;
            msg = this.$t('txt_add_success');
          }
          api
            .execute({
              productOid: this.$route.query.oid,
              masterOid: this.form.node,
              masterType: formObj.type,
              featureData: {
                // number: formObj.number,
                sortNo: this.form.sortNo,
                oid: this.form.oid,
                name: this.form.name,
                valueType: this.form.valueType,
              },
            })
            .then((res) => {
              this.$success(msg);
              this.onCancel();
              this.$emit("getList");
            })
            .catch((err) => {
              if (err.msg) {
                this.$error(err.msg);
              }
            }).finally(()=>{
              this.createLoading=false;
            });
        } else {
          this.createLoading=false;
          return false;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.form-item-btns {
  margin: 30px 0 0;
}
.form-btn-cancel {
  margin-left: 8px;
}
</style>
