<template>
  <a-modal
    :visible="visible"
    :title="$t('txt_create_chird')"
    :mask-closable="false"
    :footer="null"
    @cancel="onCancel"
  >
    <a-form-model
      ref="addNodeForm"
      :model="form"
      :rules="rules"
    >
      <a-form-model-item
        :label="$t('txt_node_type')"
        prop="modelType"
      >
        <a-select
          v-model.trim="form.modelType"
          allow-clear
          :placeholder="$t('msg_select')"
          @focus="getNodeType"
        >
          <a-select-option
            v-for="item in typeList"
            :key="item.name"
            :value="item.name"
          >
            {{ $t(item.name) }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item
        :label="$t('txt_node_name')"
        prop="name"
      >
        <a-input
          v-model.trim="form.name"
          :max-length="50"
          allow-clear
          :placeholder="$t('txt_input')"
        />
      </a-form-model-item>
      <a-form-model-item class="form-item-btns text-right">
        <a-button
          type="primary"
          :loading="createLoading"
          @click="onCreate"
        >{{$t('btn_ok')}}</a-button>
        <a-button
          class="form-btn-cancel"
          @click="onCancel"
        >{{$t('btn_cancel')}}</a-button>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
import {
  fetchChildNodeModel,
  createProductNode,
} from "apis/product/productStructure";
import validateMixin from "./validateMixin";
export default {
  name: "addNodeModal",
  props: ["visible", "nodeInfo"],
  mixins: [validateMixin],
  data() {
    return {
      createLoading: false,
      form: {
        modelType: undefined,
        name: "",
      },
      rules: {
        modelType: [
          { required: true, message: this.$t("msg_select"), trigger: "change" },
        ],
        name: [
          { required: true, message: this.$t("msg_input"), trigger: "change" },
          {
            min: 1,
            max: 50,
            message: this.$t("txt_prompt"),
            trigger: "change",
          },
          { validator: this.validateName },
        ],
      },
      typeList: [],
    };
  },
  mounted() {},
  methods: {
    getNodeType() {
      let params = {
        // modelName: this.nodeInfo.inheritModelType,
        modelName: this.nodeInfo.type,
      };
      fetchChildNodeModel
        .execute(params)
        .then((res) => {
          this.typeList = res;
        })
        .catch((err) => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
    onCreate() {
      this.createLoading = true;
      this.$refs.addNodeForm.validate((valid) => {
        if (valid) {
          createProductNode
            .execute({
              masterOid: this.nodeInfo.oid,
              masterType: this.nodeInfo.type,
              data: this.form,
            })
            .then((res) => {
              this.$success(this.$t("txt_add_success"));
              this.$refs.addNodeForm.resetFields();
              this.$refs.addNodeForm.clearValidate();
              this.$emit("close", this.nodeInfo);
            })
            .catch((err) => {
              if (err.msg) {
                this.$error(err.msg);
              }
            })
            .finally(() => {
              this.createLoading = false;
            });
        } else {
          this.createLoading = false;
          return false;
        }
      });
    },
    onCancel() {
      this.$refs.addNodeForm.resetFields();
      this.$refs.addNodeForm.clearValidate();
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
.form-item-btns {
  margin: 30px 0 0;
}
.form-btn-cancel {
  margin-left: 8px;
}
</style>
