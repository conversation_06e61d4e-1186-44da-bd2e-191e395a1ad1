<template>
  <div class="pro-cfg-wrap">
    <div class="cfg-head flex justify-between align-center">
      <a-button type="primary" @click="onCreateInstance">{{$t('txt_cerete_instance')}}</a-button>
      <div class="flex">
        <a-button @click="goFeatureManagePage">{{$t('txt_property_management')}}</a-button>
      </div>
    </div>
    <div class="cfg-body flex">
      <div class="tree-wrap">
        <div class="tree-head flex justify-between">
          <a-input-search
            v-model.trim="searchKey"
            class="search-input"
            allow-clear
            :placeholder="$t('txt_search')"
            @search="onSearch"
          />
          <a-radio-group
            default-value="all"
            v-model.trim="nodeLevel"
            @change="changeLevel"
          >
            <a-radio-button
              v-for="item in levelList >= 3 ? 3 : levelList"
              :key="item"
              :value="item"
            >
              {{ item }}
            </a-radio-button>
          </a-radio-group>
        </div>
        <div class="tree-body">
          <a-table
            rowKey="rowOid"
            :columns="columns"
            :data-source="tableData"
            :show-header="false"
            :pagination="false"
            :defaultExpandAllRows="expandAll"
            :expanded-row-keys.sync="expandedRowKeys"
            @expand="onExpandRow"
            :expandIcon="expandIcon"
            :indentSize="15"
            :loading="loading"
          >
            <template slot="name" slot-scope="text, record">
              <div class="name-wrap">
                <jw-icon :type="record.modelIcon || '#jwi-lianjian'" />
                <span
                  class="name-text"
                  v-if="record.type === 'ProductStructure'"
                  :title="`${record.number},${record.name}`"
                >
                  {{ record.number }},{{ record.name }}
                </span>
                <span
                  class="name-text"
                  v-else
                  :title="`${record.number},${record.name},${record.version},${record.viewName}`"
                >
                  {{ record.number }},{{ record.name }},{{ record.version }},{{
                    record.viewName
                  }}
                </span>
              </div>
            </template>
            <template slot="operation" slot-scope="text, record">
              <div class="flex justify-end ope-wrap">
                <span
                  class="ope-wrap-icon"
                  v-if="record.type === 'ProductStructure'"
                  @click="onOpenAddNodeModal(record)"
                >
                  <jw-icon type="jwi-iconsubItem-add" :title="$t('txt_add_c')" />
                </span>
                <a-popconfirm
                  :title="$t('txt_comfrim_delete')"
                  :ok-text="$t('btn_ok')"
                  :cancel-text="$t('btn_cancel')"
                  @confirm="() => onDeleteNode(record)"
                >
                  <span
                    v-if="
                      record.oid !== topOid &&
                      record.masterInheritType === 'ProductStructure'
                    "
                    class="ope-wrap-icon"
                  >
                    <jw-icon type="jwi-icondelete" :title="$t('btn_delete')" />
                  </span>
                </a-popconfirm>
                <a-dropdown
                  placement="bottomRight"
                  :trigger="['click']"
                  v-if="record.type === 'ProductStructure' && !record.isRename"
                >
                  <a-menu slot="overlay">
                    <a-menu-item @click="onOpenEdit(record)">
                     {{$t('txt_rename')}} 
                    </a-menu-item>
                    <a-menu-item @click="onOpenAddObjModal(record)">
                     {{$t('txt_add_object')}} 
                    </a-menu-item>
                  </a-menu>
                  <jw-icon style="margin-left: 12px" type="jwi-iconellipsis" :title="$t('txt_rename')+$t('btn_more')" />
                </a-dropdown>
                <a-popover
                  v-if="record.isRename"
                  v-model.trim="record.isRename"
                  trigger="click"
                  placement="right"
                >
                  <div slot="title" class="font-700">{{$t('txt_rename')}} </div>
                  <template slot="content">
                    <a-form-model
                      :ref="'renameForm' + record.oid"
                      :model="form"
                      :rules="rules"
                    >
                      <a-form-model-item :label="$t('txt_plan_name')" prop="name">
                        <a-input
                          v-model.trim="form.name"
                          :max-length="50"
                          allow-clear
                          :placeholder="$t('txt_input')"
                        />
                      </a-form-model-item>
                      <a-form-model-item class="text-right">
                        <a-button type="primary" @click="onEditNode(record)">
                         {{$t('btn_ok')}} 
                        </a-button>
                      </a-form-model-item>
                    </a-form-model>
                  </template>
                  <span>
                    <jw-icon type="jwi-iconellipsis" />
                  </span>
                </a-popover>
              </div>
            </template>
          </a-table>
        </div>
      </div>
      <div class="details-wrap">
        <a-tabs v-model.trim="activeTab" @change="onChangeTab">
          <a-tab-pane key="detailInfo" :tab="$t('detailed_info')">
            <basic-infos :rowInfo="rowInfo"></basic-infos>
          </a-tab-pane>
          <a-tab-pane key="featureValue" :tab="$t('txt_specific')" v-if="isShowFV">
            <feature-value
              ref="featureValue"
              :nodeInfo="nodeInfo"
            ></feature-value>
          </a-tab-pane>
          <!-- <a-tab-pane
            key="demand"
            :tab="$t('txt_demand_management')"
            v-if="nodeInfo.type === 'ProductStructure'"
          >
            <demand-manage ref="demandM" :nodeInfo="nodeInfo"></demand-manage>
          </a-tab-pane>
          <a-tab-pane
            key="task"
            :tab="$t('txt_work_management')"
            v-if="nodeInfo.type === 'ProductStructure'"
          >
            <task-manage ref="taskM" :nodeInfo="nodeInfo"></task-manage>
          </a-tab-pane> -->
        </a-tabs>
      </div>
    </div>
    <add-node-modal
      :visible="visibleAddNode"
      :nodeInfo="nodeInfo"
      @close="onCloseAddNodeModal"
    >
    </add-node-modal>
    <jw-search-engine-modal
      :title="$t('txt_add_object')"
      :onlySearchObject="true"
      :visible.sync="objVisible"
      :model-list="[
        {
          name: $t('txt_part'),
          code: 'PartIteration',
        },
      ]"
      @ok="onAddOk"
    />
  </div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { jwIcon, jwSearchEngineModal } from "jw_frame";
import addNodeModal from "./addNodeModal";
import basicInfos from "./basicInfos";
import featureValue from "./featureValue";
import demandManage from "./demandManage";
import taskManage from "./taskManage";
import validateMixin from "./validateMixin";
import {
  fetchProductTree,
  fetchMaxLevel,
  fetchOpenLevel,
  fetchChildNodes,
  renameProductNode,
  deleteProductNode,
  addRelation,
} from "apis/product/productStructure";

// 获取所有带版本对象
const fetchAllPart = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/fuzzyPage`,
  method: "post",
});

export default {
  name: "configManage",
  components: {
    addNodeModal,
    basicInfos,
    featureValue,
    demandManage,
    taskManage,
    jwIcon,
    jwSearchEngineModal,
  },
  inject: ["setBreadcrumb", "addBreadcrumb"],
  mixins: [validateMixin],
  data() {
    return {
      objVisible: false,
      oid: "",
      modelType: "",
      searchKey: "",
      levelList: [],
      nodeLevel: 1,
      columns: [
        {
          title: "Name",
          dataIndex: "name",
          key: "name",
          scopedSlots: { customRender: "name" },
          customCell: this.customCell,
        },
        {
          title: "Operation",
          dataIndex: "operation",
          key: "operation",
          width: 100,
          scopedSlots: { customRender: "operation" },
          customCell: this.customCellOpe,
        },
      ],
      tableData: [],
      expandAll: false,
      expandedRowKeys: [],
      loading: true,
      activeTab: "detailInfo",
      nodeInfo: {},
      rowInfo: {},
      topOid: "",
      isShowFV: false,
      visibleAddNode: false,
      visibleAddObj: false,
      form: {
        name: "",
      },
      rules: {
        name: [
          { required: true, message: this.$t('txt_input'), trigger: "change" },
          { min: 1, max: 50, message: this.$t('txt_prompt'), trigger: "change" },
          { validator: this.validateName },
        ],
      },
      isClick: false,
    };
  },
  async mounted() {
    this.oid = this.$route.query.oid;
    this.modelType = this.$route.query.type;
    // this.initBreadcrumb();
    await this.getTreeMaxLevel();
    await this.getProductNodeTree();
    this.nodeLevel = 1;
    this.getOpenNode();
  },
  methods: {
    searchModelTable(params) {
      return fetchAllPart.execute(params);
    },
    onAddOk(selectedRows) {
      let _this = this;
      if (selectedRows.length === 0) {
        this.$warning(this.$t('txt_please_seleted_data'));
        return;
      }
      let secList = selectedRows.map((item) => {
        return {
          oid: item.oid,
          modelType: item.type,
        };
      });
      addRelation
        .execute({
          masterOid: this.nodeInfo.oid,
          masterType: this.nodeInfo.type,
          secList: secList,
        })
        .then((res) => {
          this.$success(this.$t('txt_add_success'));
          fetchMaxLevel
            .execute({
              containerOid: this.oid,
              containerType: this.modelType,
            })
            .then((res) => {
              this.levelList = res;
              if (res) {
                _this.getProductNodeTree();
              }
            });
          this.onCloseObjModal();
        })
        .catch((err) => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
    onCloseObjModal() {
      this.objVisible = false;
    },
    initBreadcrumb() {
      let breadcrumbData = [{ name: this.$t('txt_procuct_cu'), path: `/product` }];
      this.setBreadcrumb(breadcrumbData);
      this.addBreadcrumb({
        name: this.$t('txt_product_structure'),
        children: [
          {
            id: "1",
            title: this.$t('txt_content_management'),
            link: `/contentManage/${this.oid}/${this.modelType}`,
          },
          {
            id: "2",
            title: this.$t('txt_product_structure'),
            link: `/productStructure/${this.oid}/${this.modelType}`,
          },
          {
            id: "3",
            title: this.$t('txt_baseline_management'),
            link: `/baseline/${this.oid}/${this.modelType}`,
          },
          {
            id: "4",
            title: this.$t('txt_change_management'),
            link: `/changeManage/${this.oid}/${this.modelType}`,
          },
        ],
      });
    },
    onChangeTab(key) {
      if (key === "demand" && this.nodeInfo.type === "ProductStructure") {
        this.$nextTick(() => {
          this.$refs.demandM.getDemandList();
        });
      } else if (key === "task" && this.nodeInfo.type === "ProductStructure") {
        this.$nextTick(() => {
          this.$refs.taskM.getTaskList();
        });
      } else if (key === "featureValue" && this.nodeInfo.oid !== this.topOid) {
        this.$nextTick(() => {
          this.$refs.featureValue.getFeatureValues();
        });
      } else {
        this.rowInfo = { ...this.nodeInfo };
      }
    },
    async getProductNodeTree() {
      this.loading = true;
      if (this.isClick) return;
      this.isClick = true;
      let params = {
        containerOid: this.oid,
        containerType: this.modelType,
        searchKey: this.searchKey,
      };
      if (this.searchKey) {
        delete params.levelLimit;
      } else {
        if (this.levelList >= 3) {
          params.levelLimit = 4;
        } else {
          params.levelLimit = this.levelList;
        }
      }
      let res = await fetchProductTree.execute(params);
      // if(res.children === null || res.children.length ===0){
      //   delete res.children;
      // }
      if (res.oid) {
        this.tableData = this.handleTreeData([res]);
        this.nodeInfo = { ...this.tableData[0] };
        this.rowInfo = { ...this.tableData[0] };
        console.log("res.oid", res);
        this.topOid = res.oid;
        this.onChangeTab(this.activeTab);
        if (this.nodeInfo.oid !== this.topOid) {
          this.isShowFV = true;
        } else {
          this.isShowFV = false;
        }
        if (this.searchKey) {
          this.$nextTick(() => {
            this.expandedRowKeys = res.expandOids;
          });
        }
      } else {
        this.$warning(this.$t('txt_search_pear'));
      }
      this.loading = false;
      this.isClick = false;
    },
    async getTreeMaxLevel() {
      let res = await fetchMaxLevel.execute({
        containerOid: this.oid,
        containerType: this.modelType,
      });
      this.levelList = res;
    },
    handleTreeData(data) {
      return data.map((item) => {
        if (item.children && item.children.length > 0) {
          item.isRename = false;
          this.handleTreeData(item.children);
        } else {
          item.isRename = false;
        }
        return item;
      });
    },
    async onExpandRow(expanded, record) {
      if (record.children && record.children.length === 0) {
        if (expanded) {
          this.loading = true;
          let res = await fetchChildNodes
            .execute({
              masterOid: record.oid,
              masterType: record.type,
              rowOid: record.rowOid,
            })
            .catch((err) => {
              this.loading = false;
              this.$warning(err.msg);
            });
          record.children = res.map((item) => {
            item.isRename = false;
            return item;
          });
          this.expandedRowKeys.push(record.rowOid);
          this.loading = false;
        } else {
          this.expandedRowKeys.splice(
            this.expandedRowKeys.indexOf(record.rowOid),
            1
          );
        }
      }
    },
    getOpenNode() {
      this.loading = true;
      fetchOpenLevel
        .execute({
          containerOid: this.oid,
          containerType: this.modelType,
          levelLimit: this.nodeLevel,
        })
        .then((res) => {
          this.expandedRowKeys = res;
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
    onSearch() {
      this.getProductNodeTree();
    },
    changeLevel(e) {
      if (!e.target.value) return;
      this.nodeLevel = e.target.value;
      this.getOpenNode();
    },
    expandIcon(props) {
      if (props.record.children && props.record.children.length >= 0) {
        if (props.expanded) {
          // 有数据-展开时候图标
          return (
            <span
              style="margin:0 5px 0 0"
              onClick={(e) => {
                props.onExpand(props.record, e);
              }}
            >
              <a-icon type="caret-down" />
            </span>
          );
        } else {
          // 有数据-未展开时候图标
          return (
            <span
              style="margin:0 5px 0 0"
              onClick={(e) => {
                props.onExpand(props.record, e);
              }}
            >
              <a-icon type="caret-right" />
            </span>
          );
        }
      } else {
        // 无数据-图标
        return <span style="margin-left:19px;"></span>;
      }
    },
    onOpenEdit(row) {
      console.log(row);
      this.form.name = row.name;
      row.isRename = true;
    },
    onEditNode(row) {
      console.log("row", row);
      this.$refs["renameForm" + row.oid].validate(async (valid) => {
        if (valid) {
          let res = await renameProductNode
            .execute({
              masterOid: this.nodeInfo.oid,
              oid: row.oid,
              name: this.form.name,
              modelType: row.type,
            })
            .then((res) => {
              console.log(res);
              this.$success(this.$t('msg_update_success'));
              row.isRename = false;
              this.getProductNodeTree();
              this.getOpenNode();
            })
            .catch((err) => {
              return this.$error(err.msg);
            });
        } else {
          return false;
        }
      });
    },
    onOpenAddNodeModal(row) {
      this.visibleAddNode = true;
      this.nodeInfo = { ...row };
    },
    async onCloseAddNodeModal(row) {
      this.visibleAddNode = false;
      if (row) {
        await this.getTreeMaxLevel();
        await this.getProductNodeTree();
        this.getOpenNode();
      }
    },
    onOpenAddObjModal(row) {
      this.objVisible = true;
      this.visibleAddObj = true;
      this.nodeInfo = { ...row };
    },
    async onCloseAddObjModal(row) {
      this.visibleAddObj = false;
      if (row) {
        await this.getTreeMaxLevel();
        await this.getProductNodeTree();
        this.getOpenNode();
      }
    },
    async onDeleteNode(row) {
      try {
        let res = await deleteProductNode.execute({
          productOid: this.oid,
          masterOid: row.masterOid,
          oid: row.oid,
          modelType: row.type,
        });
        this.$success(this.$t('txt_delete_success'));
        await this.getTreeMaxLevel();
        await this.getProductNodeTree();
        this.getOpenNode();
      } catch (err) {
        if (err.msg) {
          this.$error(err.msg);
        }
      } finally {
      }
    },
    customCell(record, rowIndex) {
      return {
        style: {
          background: this.nodeInfo.oid === record.oid ? "#f0f7ff" : "",
        },
        on: {
          click: () => {
            this.nodeInfo = { ...record };
            if (this.nodeInfo.oid !== this.topOid) {
              this.isShowFV = true;
            } else {
              this.isShowFV = false;
            }
            if (
              this.activeTab === "demand" &&
              this.nodeInfo.type === "ProductStructure"
            ) {
              this.$nextTick(() => {
                this.$refs.demandM.getDemandList();
              });
            } else if (
              this.activeTab === "task" &&
              this.nodeInfo.type === "ProductStructure"
            ) {
              this.$nextTick(() => {
                this.$refs.taskM.getTaskList();
              });
            } else if (
              this.activeTab === "featureValue" &&
              this.nodeInfo.oid !== this.topOid
            ) {
              this.$nextTick(() => {
                this.$refs.featureValue.getFeatureValues();
              });
            } else {
              this.activeTab = "detailInfo";
              this.rowInfo = { ...record };
            }
          },
        },
      };
    },
    customCellOpe(record, rowIndex) {
      return {
        style: {
          background: this.nodeInfo.oid === record.oid ? "#f0f7ff" : "",
        },
      };
    },
    onCreateInstance() {
      let { name } = this.nodeInfo;
      let { oid, type, masterType, modelDefinition } =
        this.$route.query;
      this.$router.push(
        `/createInstance?oid=${oid}&type=${type}&masterType=${masterType}&modelDefinition=${modelDefinition}&containerName=${name}`
      );
    },
    goFeatureManagePage() {
      let { name } = this.nodeInfo;
      let { oid, type, masterType, modelDefinition } =
        this.$route.query;
      this.$router.push(
        `/featureManage?oid=${oid}&type=${type}&masterType=${masterType}&modelDefinition=${modelDefinition}&containerName=${name}`
      );
    },
  },
};
</script>

<style lang="less" scoped>
.pro-cfg-wrap {
  margin: 5px;
  background: var(--light);
  // box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
  border-radius: 4px;
  background: #fff;
  .jwifont {
    width: 16px;
    min-width: 16px;
    height: 16px;
    min-height: 16px;
  }
  .cfg-head {
    height: 60px;
    padding: 0 20px;
    border-bottom: 1px solid rgba(30, 32, 42, 0.15);
    .head-view-btn {
      width: 32px;
      line-height: 35px;
      margin-right: 8px;
      padding: 0;
    }
  }
  .cfg-body {
    min-height: calc(100vh - 176px);
    .tree-wrap {
      width: 34%;
      padding: 16px;
      border-right: 1px solid rgba(30, 32, 42, 0.06);
      .tree-head {
        flex-wrap: wrap;
        .search-input {
          width: 45%;
          max-width: 216px;
        }
        .ant-radio-button-wrapper {
          width: 32px;
          height: 32px;
          line-height: 30px;
          padding: 0;
          text-align: center;
        }
      }
      .tree-body {
        height: calc(70vh);
        margin-top: 20px;
        overflow: auto;
        &::-webkit-scrollbar {
          width: 0px;
        }
        /deep/.ant-table-tbody > tr > td {
          padding: 9px;
          background: rgba(30, 32, 42, 0.04);
          // border: 1px solid transparent;
          border-bottom: 8px solid #fff;
          &:first-child {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
            white-space: nowrap;
          }
          &:last-child {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
          }
        }
        /deep/.ant-table-tbody > tr:hover {
          td {
            cursor: pointer;
            &:first-child {
              background: #f0f7ff;
              // border: 1px solid #a4c9fc;
              // border-right: 0;
            }
            &:last-child {
              background: #f0f7ff;
              // border: 1px solid #a4c9fc;
              // border-left: 0;
            }
          }
        }
        .name-wrap {
          display: inline-flex;
          align-items: center;
          .ant-input-affix-wrapper {
            width: 80%;
          }
          .name-text {
            width: 180px;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-left: 5px;
          }
        }
        .ope-wrap {
          .jwifont {
            margin-right: 8px;
          }
        }
      }
    }
    .details-wrap {
      width: 66%;
      /deep/.ant-tabs {
        color: rgba(30, 32, 42, 0.65);
      }
      /deep/.ant-tabs-nav .ant-tabs-tab {
        padding: 19px 0;
      }
      /deep/.ant-tabs-bar {
        padding: 0 20px;
        margin-bottom: 0;
      }
      /deep/.ant-tabs-nav .ant-tabs-tab-active {
        color: rgba(30, 32, 42, 0.85);
        // font-weight: 700;
      }
    }
  }
}
.ant-table-row .ope-wrap-icon {
  display: none;
}
.ant-table-row:hover .ope-wrap-icon {
  display: initial;
}
</style>
