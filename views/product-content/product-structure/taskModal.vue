<template>
  <a-modal
    :visible="visible"
    :title="$t('txt_create_work')"
    :mask-closable="false"
    :footer="null"
    centered
    @cancel="handleCancel"
  >
    <div class="create-temp-wrap">
      <a-form-model
        ref="createForm"
        :model="form"
        :rules="rules"
      >
        <a-form-model-item
          :label="$t('txt_name')"
          prop="name"
        >
          <a-input
            v-model.trim="form.name"
            :max-length="50"
            allow-clear
            :placeholder="$t('txt_input')"
          />
        </a-form-model-item>
        <a-form-model-item
          :label="$t('txt_assignee')"
          prop="handlerName"
        >
          <a-popover
            trigger="click"
            v-model.trim="visibleUser"
            placement="bottomLeft"
          >
            <template slot="content">
              <a-select
                show-search
                allowClear
                v-model.trim="form.handlerName"
                :placeholder="$t('txt_name_phone_email')"
                style="width: 250px"
                :default-active-first-option="false"
                :show-arrow="false"
                :filter-option="false"
                @focus="handleSearch"
                @search="handleSearch"
                @change="handleChange"
              >
                <a-select-option
                  v-for="item in userData"
                  :key="item.oid"
                  :value="item.oid"
                >
                  <div class="user-wrap flex align-center">
                    <div
                      class="handler-avatar"
                      v-if="item.avatarUrl"
                    >
                      <img
                        :src="item.avatar"
                        alt=""
                      />
                    </div>
                    <div
                      v-else
                      class="handler-avatar"
                    >
                      {{ item.name.slice(0, 1).toUpperCase() }}
                    </div>
                    <div>{{ item.name }}</div>
                  </div>
                </a-select-option>
              </a-select>
            </template>
            <a-button
              v-if="form.handlerName"
              type="primary"
              ghost
              shape="circle"
            >
              <jw-icon type="jwi-iconreplace" />
            </a-button>
            <a-button
              v-else
              type="primary"
              ghost
              shape="circle"
              icon="plus"
            />
          </a-popover>
          <div
            class="handler-wrap inline-flex align-center"
            v-if="form.handlerName"
          >
            <div
              class="handler-avatar"
              v-if="form.handlerUrl"
            >
              <img
                :src="form.handlerUrl"
                alt=""
              />
            </div>
            <div
              v-else
              class="handler-avatar"
            >
              {{ form.handlerName.slice(0, 1).toUpperCase() }}
            </div>
            <div>{{ form.handlerName }}</div>
          </div>
        </a-form-model-item>
        <a-row :gutter="10">
          <a-col :span="12">
            <a-form-model-item
              :label="$t('txt_work_peroid')"
              prop="period"
            >
              <a-input-number
                v-model.trim="form.period"
                :precision="0"
                :min="1"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item
              :label="$t('txt_start_time')"
              prop="startTime"
            >
              <a-date-picker
                v-model.trim="form.startTime"
                valueFormat="YYYY-MM-DD"
                allow-clear
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item
          :label="$t('txt_description')"
          prop="description"
        >
          <a-textarea
            v-model.trim="form.description"
            allow-clear
            class="textarea-input"
            :placeholder="$t('txt_input')"
          />
        </a-form-model-item>
        <a-form-model-item class="form-item-btns text-right">
          <a-button
            type="primary"
            :loading="createLoading"
            @click="handleCreate"
          >{{$t('btn_ok')}}</a-button>
          <a-button
            class="form-btn-cancel"
            @click="handleCancel"
          >{{$t('btn_cancel')}}</a-button>
        </a-form-model-item>
      </a-form-model>
    </div>
  </a-modal>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { userListApi } from "apis/user";
import { addTask } from "apis/product/productStructure";
import validateMixin from "./validateMixin";
const memberListApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/user/search/keyword/page`,
  method: "get",
});
export default {
  name: "taskModal",
  props: ["visible", "nodeInfo", "rowInfo"],
  mixins: [validateMixin],
  components: {},
  data() {
    return {
      form: {
        name: "",
        handlerName: "",
        period: 1,
        startTime: "",
        description: "",
      },
      createLoading: false,
      rules: {
        name: [
          { required: true, message: this.$t("msg_input"), trigger: "change" },
          {
            min: 1,
            max: 50,
            message: this.$t("txt_prompt"),
            trigger: "change",
          },
          { validator: this.validateName },
        ],
        period: [
          { required: true, message: this.$t("msg_input"), trigger: "change" },
        ],
        startTime: [
          { required: true, message: this.$t("msg_input"), trigger: "change" },
        ],
        handlerName: [
          { required: true, message: this.$t("msg_select"), trigger: "change" },
        ],
      },
      visibleUser: false,
      userData: [],
    };
  },
  mounted() {},
  methods: {
    handleSearch(value) {
      //   userListApi
      memberListApi
        .execute({
          pageNum: 1,
          pageSize: 50,
          tenantOid: Jw.getUser().tenantOid,
          searchKey: value,
        })
        .then((res) => {
          this.userData = res.rows.map((item) => {
            if (item.avatar) {
              item.avatarUrl = `${Jw.gateway}/${Jw.fileMicroServer}/file/downloadByOid?oid=${item.avatar}`;
            }
            return item;
          });
        })
        .catch((err) => {
          this.$error(err.msg);
        });
    },
    handleChange(value) {
      if (value) {
        this.form.handlerOid = value;
        this.form.handlerName = this.userData.filter(
          (item) => item.oid === value
        )[0].name;
        let avatar = this.userData.filter((item) => item.oid === value)[0]
          .avatar;
        if (avatar) {
          this.form.handlerUrl = `${Jw.gateway}/${Jw.fileMicroServer}/file/downloadByOid?oid=${avatar}`;
        } else {
          this.form.handlerUrl = "";
        }
        this.visibleUser = false;
      }
    },
    handleCreate() {
      this.createLoading = true;
      this.$refs.createForm.validate((valid) => {
        if (valid) {
          addTask
            .execute({
              productOid: this.$route.query.oid,
              masterOid: this.nodeInfo.oid,
              //   masterType: this.nodeInfo.modelType,
              masterType: this.nodeInfo.type,
              task: this.form,
            })
            .then((res) => {
              this.$success(this.$t("txt_add_success"));
              this.handleCancel();
              this.$emit("getList");
            })
            .catch((err) => {
              if (err.msg) {
                this.$error(err.msg);
              }
            })
            .finally(() => {
              this.createLoading = false;
            });
        } else {
          this.createLoading = false;
          return false;
        }
      });
    },
    handleCancel() {
      this.$refs.createForm.resetFields();
      this.$refs.createForm.clearValidate();
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
.textarea-input /deep/.ant-input {
  height: 110px;
}
.form-btn-cancel {
  margin-left: 8px;
}
.jwifont {
  width: 16px;
  min-width: 16px;
  height: 16px;
  min-height: 16px;
  vertical-align: text-bottom;
}
.handler-wrap {
  margin-left: 8px;
  padding: 4px 8px;
  line-height: 20px;
  background: rgba(30, 32, 42, 0.04);
  border-radius: 4px;
}
.handler-avatar {
  margin-right: 8px;
  width: 24px;
  height: 24px;
  line-height: 20px;
  text-align: center;
  color: #255ed7;
  border-radius: 50%;
  border: 2px solid #255ed7;
  img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
}
.user-wrap {
  padding: 5px 8px;
  border: 1px solid transparent;
}
.ant-input-number,
.ant-calendar-picker {
  width: 100%;
}
</style>
<style lang="less">
.drop-style {
  .ant-select-dropdown-menu-item-active:not(.ant-select-dropdown-menu-item-disabled) {
    background: #fff;
    .user-wrap {
      border-radius: 4px;
      background: #f0f7ff;
      border: 1px solid #a4c9fc;
    }
  }
}
</style>
