<template>
    <div class="delivery-list-basic-info-wrap">
        <div class="header-wrap">
            <div class="header-left-wrap">
                <div class="left-item"
                    v-if="detailInfo.lockOwnerOid">
                    <jw-icon
                        :type="
                            detailInfo.lockOwnerOid === userOid
                            ? '#jwi-beiwojianchu'
                            : '#jwi-bierenjianchu'
                        "
                    />
                </div>
                <div class="left-item2" :title="`${detailInfo.name}，${detailInfo.number}`">
                    <jwIcon :type='detailInfo.modelIcon'></jwIcon>
                    <span v-if="detailInfo.modelDefinition==='Structure'||detailInfo.modelDefinition==='Category'">
                        {{ detailInfo.name }}
                    </span>
                    <span v-else>{{ detailInfo.name }}，{{ detailInfo.number }}</span>
                </div>
            </div>
            <div class="header-right-wrap">
                <a-button
                    v-if="btnFilter&&!isPreview"
                    @click="onOpenPreview"
                >
                    {{$t('btn_open_preview')}}
                </a-button>
                <a-button
                    v-if="btnFilter&&isPreview"
                    @click="onClosePreview"
                >
                    {{$t('btn_close_preview')}}
                </a-button>
                <a-button
                    v-if="btnFilter"
                    @click="onDownload"
                >
                    {{$t('btn_download_temp')}}
                </a-button>
                <a-button
                    v-if="isEdit"
                    :loading="saveLoading"
                    @click="onSave"
                >
                    {{$t('btn_save')}}
                </a-button>
                <a-button
                    v-if="isEdit"
                    @click="onCancelEdit(true)"
                >
                    {{$t('btn_cancel')}}
                </a-button>
            </div>
        </div>
        <div class="body-wrap">
            <div class="body-head">
                <div class="title-left"></div>{{$t('txt_base_info')}}
            </div>
            <div class="body-body">
                <jw-layout-builder
                    v-if="instanceData.oid"
                    ref="ref_layout"
                    type="Model"
                    :layoutName="layoutName"
                    :modelName="instanceData.modelDefinition"
                    :instanceData="instanceData"
                    @initModel='onInitModel'
                >
                    <template #createBy>
                        <user-info :accounts="[instanceData.createBy]" :showname="true"></user-info>
                    </template>
                    <template #updateBy>
                        <user-info :accounts="[instanceData.updateBy]" :showname="true"></user-info>
                    </template>
                    <template #owner>
                        <user-info :accounts="[instanceData.owner]" :showname="true"></user-info>
                    </template>
                </jw-layout-builder>
                <div class="preview-wrap" v-if="isPreview">
                    <iframe width="100%" height="99%" :src="src" frameborder="0"></iframe>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { jwLayoutBuilder } from 'jw_frame';
import ModelFactory from 'jw_apis/model-factory';
import { formatDate } from 'jw_utils/moment-date';
import util from 'jw_common/util';
import { findDetail } from "apis/baseapi";
import userInfo from "components/user-info";

//判断是否有下载权限
const downloadPermission = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/common/doc/checkDownloadAccess`,
  method: 'get'
})

// 更新节点
const updateDelivery = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.productDeliveryServer}/delivery/update`,
    method: 'post',
});

// 获取文档模板列表
const fetchDocumentTemplate = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.docMicroServer}/documentTemplate/find/documentTemplateByClsOid`,
    method: 'get',
});

const previewApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.fileMicroServer}/file/getUrlByOidForPreview`,
  method: "get"
});

// 获取下载和预览按钮权限
const fetchBtnFilter = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.sysconfigServer}/preferences/setting/query-config-value`,
    method: 'get',
});

export default {
    name: 'deliveryListBasicInfo',
    props: [
        'detailInfo',
    ],
    components: {
        jwLayoutBuilder,
        userInfo
    },
    data() {
        return {
            userOid: Jw.getUser().oid,
            isEdit: false,
            saveLoading: false,
            layoutName: 'show',
            instanceData: {},
            fileInfo: {},
            btnFilter: false,
            isPreview: false,
            src: null,
            watermarkTxt: Jw.getUser().name + "  " + formatDate(new Date(),'YYYY-MM-DD'),
        }
    },
    mounted() {

    },
    methods: {
      onInitModel({ layout }) {
        setTimeout(() => {
          if (this.layoutName !== 'update') {
            let btnList = document.getElementsByClassName('jwi-icondownload')
            //若为文档对象，判断不为owner时不允许下载以及预览
            if ('Document' === this.detailInfo.masterType) {
              let account = Jw.getUser().account
              let isOwner = this.detailInfo.owner === account
              let visBtnList = document.getElementsByClassName('jwi-visible')
              let allBtn = [...btnList, ...visBtnList];
              allBtn.forEach(btn => {
                btn.style.display = ''
                if (this.isPrimaryBtn(btn)) {
                  if (!isOwner && "sys_admin" !== account && !Jw.getUser().tenantAdmin) {
                    btn.style.display = 'none'
                  }
                }else {
                  if (this.isSecondaryFileDownBtn(btn) && !isOwner && account !== "sys_admin" && !Jw.getUser().tenantAdmin) {
                    // 先隐藏按钮，校验通过后再显示
                    btn.style.display = 'none';
                    // 校验下载权限
                    downloadPermission.execute({
                      oid: this.detailInfo.oid,
                      type: this.detailInfo.type
                    }).then(resp => {
                      // 如果权限校验通过，显示按钮
                      if (resp) {
                        btn.style.display = '';
                      }
                    }).catch(err => {
                      console.error("下载权限校验失败:", err);
                    });
                  }
                }


              })
            }
          }
        }, 0);
      },
        getObjDetail() {
            this.instanceData = {};
            findDetail.execute({
                oid: this.detailInfo.oid,
                type: this.detailInfo.type,
            }).then(res => {
                this.instanceData = res;
                if (this.detailInfo.modelDefinition === 'Category' && this.detailInfo.clsOid) {
                    fetchDocumentTemplate.execute({
                        clsOid: this.detailInfo.clsOid,
                    }).then((res) => {
                        if (res && res.length > 0) {
                            if (res[0].secondaryFile && res[0].secondaryFile.length > 0) {
                                this.fileInfo = res[0].secondaryFile[0];
                            }
                            this.getBtnFilter(res[0]);
                            this.$emit('getDocTempInfo', {...res[0]});
                        } else {
                            this.fileInfo = {};
                            this.btnFilter = false;
                        }
                    }).catch((err) => {
                        this.$error(err.msg);
                    });
                } else {
                    this.fileInfo = {};
                    this.btnFilter = false;
                }
            }).catch((err) => {
                this.$error(err.msg);
            });
        },
        getBtnFilter(docInfo) {
            fetchBtnFilter.execute({
                name: 'Delivery_Document_Down_Status',
            }).then((res) => {
                if (res && res.length) {
                    let item = res.find(val => val.value === docInfo.lifecycleStatus);
                    this.btnFilter = this.detailInfo.modelDefinition === 'Category' && item ? true : false;
                    this.instanceData = {
                        ...this.instanceData,
                        classificationInfo: {
                            ...this.instanceData.classificationInfo,
                            secondaryFile: [docInfo.secondaryFile[0]],
                            btnFilter: !this.btnFilter,
                        }
                    }
                }
            }).catch(err => {
                this.$error(err.msg);
            })
        },
        onDownload() {
            util.download(
                `${Jw.gateway}/${Jw.fileMicroServer}/file/downloadByOid?fileOid=${
                    this.fileInfo.oid
                }`
            );
        },
        onOpenPreview() {
            this.isPreview = true;
            previewApi.execute({
                fileOid: this.fileInfo.oid,
            }).then(url => {
                let URL = `${Jw.fileOnlineService}/${Jw.filePreviewServer}/preview/onlinePreview?url=`;
                this.src = URL + url + "&watermarkTxt=" + this.watermarkTxt;
            }).catch(err => {
                if (err.code === -1) {
                    this.$error(this.$t('nonsupport_preview'));
                }
            });
        },
        onClosePreview() {
            this.isPreview = false;
            this.src = null;
        },
        onEdit() {
            this.isEdit = true;
            this.layoutName = 'update';
        },
        onSave() {
            let appBuilder = this.$refs.ref_layout;
            return new Promise((resolve, reject) => appBuilder &&
                appBuilder
                .validate()
                .then(() => {
                    this.saveLoading = true;
                    let value = appBuilder.getValue();
                    let params = {...this.detailInfo, ...value};
                    updateDelivery.execute(
                        params
                    ).then((res) => {
                        this.$success(this.$t('msg_update_success'));
                        this.saveLoading = false;
                        this.onCancelEdit(true);
                        this.$emit('onSearch');
                        resolve();
                    }).catch((err) => {
                        this.saveLoading = false;
                        this.$error(err.msg);
                        reject(err);
                    });
                })
            );
        },
        onCancelEdit(flag) {
            this.isEdit = false;
            this.isPreview = false;
            this.src = null;
            this.layoutName = 'show';
            if (flag) this.getObjDetail();
        },
      isPrimaryBtn(btn) {
        if ("secondaryFile" === btn.getAttribute("prop")) {
          return false;
        } else if ("primaryFile" === btn.getAttribute("prop")) {
          return true;
        } else {
          return true;
        }
      },
      isSecondaryFileDownBtn(btn) {
        return "secondaryFile" === btn.getAttribute("prop") && "下载" === btn.getAttribute("title");
      },
    },
}
</script>

<style lang="less" scoped>
.delivery-list-basic-info-wrap {
    height: 100%;
    .header-wrap {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .header-left-wrap {
            display: flex;
            white-space: nowrap;
            .left-item {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 32px;
                height: 32px;
                margin-right: 8px;
                background: rgba(30,32,42,0.04);
                border-radius: 4px;
            }
            .left-item2 {
                line-height: 32px;
                padding: 0 8px;
                color: rgba(30,32,42,0.85);
                background: rgba(30,32,42,0.04);
                border-radius: 4px;
                max-width: 250px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
    .body-wrap {
        height: calc(~"100% - 60px");
        .body-head {
            display: flex;
            align-items: center;
            margin-top: 20px;
            font-size: 16px;
            color: rgba(30,32,42,0.85);
            .title-left {
                width: 3px;
                height: 20px;
                margin-right: 8px;
                background: #255ED7;
                border-radius: 2.5px;
            }
        }
        .body-body {
            height: calc(~"100% - 20px");
            overflow: auto;
            .preview-wrap {
                width: 100%;
                height: 100%;
                margin-top: 20px;
            }
        }
    }
}
</style>
