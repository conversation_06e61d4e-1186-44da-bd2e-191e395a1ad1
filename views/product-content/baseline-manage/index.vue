<template>
    <div class="all-background baseline-manage-page">
        <jw-table ref="ref_table" disableCheck="disableCheck" :data-source.sync="tableData" :columns="columns"
            :selectedRows.sync="selectedRows" :toolbars="toolbars" :fetch='fetchTable' @onToolClick="onToolClick"
            @onToolInput="onToolInput" @onOperateClick="onOperateClick">
            <template #lifecycleStatus='{ row }'>
                <a-tag>{{ $t(row.lifecycleStatus) }}</a-tag>
            </template>
            <template #numberSlot='{ row }'>
                <span @click="gotoDetails(row)" style="cursor:pointer;color: #255ed7">
                    <jwIcon :type='row.modelIcon' />
                    <span>{{ row.number }}</span>
                </span>
            </template>
            <template #operation='{ row }'>
                <baseline-dropdown permission-view-code="BASELINEINSTANCE" :current-record="row"
                    @complete='dropdownAction' />
            </template>
        </jw-table>

        <base-color-modal :title="$t('txt_create_baseline')" :width="512" :ok-text="$t('txt_text_add_object')"
            :cancel-text="$t('txt_create_baseline')" :visible.sync='baseModalVisible' :ok-btn-loading="okBtnLoading"
            @ok='createAndAddObject' @cancel='createBaseline'>
            <jw-layout-builder ref="ref_appBuilder" type="Model" layoutName="create" :modelName="'Baseline'"
                :instanceData="{}">
                <template v-for="(node, slotName) in $scopedSlots" :slot="slotName" slot-scope="slotData">
                    <slot :name="slotName" v-bind="slotData"></slot>
                </template>
            </jw-layout-builder>
        </base-color-modal>
        <jw-search-engine-modal :title="$t('txt_add_object')" only-search-object :visible.sync="addObjectModalVisible"
            :ok-btn-loading="okBtnLoading" :model-list='modelList' @ok='addObjectOk' />
        <!-- 对比-选择基线弹窗 -->
        <form-modal :width="512" :title="$t('txt_seletct_baseline')" confirm-btn-position="left"
            :data-source="formModalData" :visible.sync="formModalVisible" :body-style="{
                height: '388px',
            }" @confirm="formModalConfirm" @cancelBack="formModalCancel">
            <template #currentBaseline='{ item }'>
                <div :title="`${item.number}，${item.name}，${item.detailType == 'null' ? '' : item.detailType}`" :style="{
                    'height': '40px',
                    'line-height': '40px',
                    'background': 'rgba(30,32,42,0.04)',
                    'border-radius': '4px',
                    'color': 'rgba(30,32,42,0.65)',
                    'white-space': 'nowrap',
                    'text-overflow': 'ellipsis',
                    'overflow': 'hidden'
                }">
                    <jwIcon :type='item.modelIcon' style="margin: 0 10px;" />{{ item.number }}，{{ item.name }}，{{
                        item.detailType == 'null' ? '' : item.detailType }}
                </div>
                <a-divider />
            </template>
        </form-modal>
    </div>
</template>

<script>
import formModal from "components/form-modal.vue";
import { formatDate } from "jw_utils/moment-date";
import { jwLayoutBuilder, jwIcon, jwSearchEngineModal } from 'jw_frame';
import ModelFactory from 'jw_apis/model-factory';
import baseColorModal from 'components/base-color-modal';
// import addObjectModal from 'components/add-object-modal';
import BaselineDropdown from './baseline-dropdown.vue';

// 根据类型（）及关键字筛查询
const searchModelTable = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/fuzzyPage`,
    method: 'post',
});
// 查询容器下的基线列表
const getBaselineList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/model/query`,
    method: 'post',
});
// 创建基线
const createBaseline = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/model/create`,
    method: 'post',
});
// 删除基线
const deleteBaseline = oid => ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/model/delete/${oid}`,
    method: 'delete',
});
// 添加对象前 校验
const checkBeforeLink = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/checkBeforeLink`,
    method: 'post',
});
// 添加对象
const batchLink = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/batchLink`,
    method: 'post',
});
// 
const checkPermission = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
    method: "post",
});
// 文件夹目录
const fetchfolderTree = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/folder/searchTree`,
    method: "get",
});
export default {
    components: {
        formModal,
        jwLayoutBuilder,
        baseColorModal,
        // addObjectModal,
        BaselineDropdown,
        jwIcon,
        jwSearchEngineModal,
    },
    data() {
        return {

            // 表格状态
            searchKey: "",
            tableData: [{}],
            selectedRows: [],
            tableLoading: false,
            total: 0,
            selectRow: [],
            currentCreateRole: false,

            baseModalVisible: false,

            addObjectModalVisible: false,

            modelList: [
                {
                    name: this.$t('txt_part'),
                    code: "PartIteration",
                },
                {
                    name: this.$t('txt_document'),
                    code: "DocumentIteration",
                },
                {
                    name: "MCAD",
                    code: "MCADIteration",
                },
                {
                    name: "ECAD",
                    code: "ECADIteration",
                },
            ],
            okBtnLoading: false,
            formModalVisible: false,
            formModalData: [
                {
                    label: this.$t('txt_current_baseline'),
                    prop: 'currentBaseline',
                    block: true,
                    required: false,
                    slotData: {},
                    scopedSlots: {
                        default: 'currentBaseline'
                    }
                },
                // {
                //     label: '上下文',
                //     type: "tree",
                //     placeholder: '请先选择上下文',
                //     prop: "context",
                //     treeData: this.deepData(this.getFoldertree(), true),
                //     block: true,
                //     required: false,
                //     showSearch: true,
                //     treeNodeFilterProp: "title",
                //     filterTreeNode: (value, treeNode, field = 'name') => {
                //         const title = treeNode.componentOptions.propsData.dataRef[field].toLowerCase();
                //         console.log(title, value)
                //         return title.indexOf((value || '').trim().toLowerCase()) > -1
                //     },
                //     change: (newVal, oldVal) => {
                //     const containerOid = (findItem(this.formModalData[0].treeData, newVal) || {}).containerOid;

                //         this.$set(this.formModalData, 0, { ...this.formModalData[0], value: newVal });
                //         this.$set(this.formModalData, 1, { 
                //         ...this.formModalData[1], 
                //         disabled: !newVal,
                //         defaultOptions: [], // 切换上下文时，清空之前搜索的下拉基线列表
                //         searchParams: {
                //             ...this.formModalData[1].searchParams,
                //             containerOid, // 设置 基线搜索 -- 产品容器参数
                //         }
                //         });
                //     },
                //     dropdownStyle: {
                //         height: '276px',
                //         overflowY: 'scroll',
                //     }
                // },
                {
                    label: '',
                    prop: 'targetOid',
                    type: 'select',
                    placeholder: this.$t('search_text'),
                    block: true,
                    fetch: getBaselineList,
                    showSearch: true,
                    searchParams: {
                        index: 1,
                        size: 10,
                        // searchKey: '',
                        containerOid: this.$route.query.oid,
                    },
                    // defaultOptions: [],
                    dropdownStyle: {
                        height: '276px',
                        overflowY: 'scroll',
                    }
                },
            ],
        }
    },

    computed: {
        columns() {
            return [
                {
                    field: "number",
                    title: this.$t('txt_number'),
                    slots: {
                        default: "numberSlot",
                    },
                },
                {
                    field: "name",
                    title: this.$t('txt_name'),
                },
                {
                    field: "lifecycleStatus",
                    title: this.$t('txt_lifecycle'),
                    slots: {
                        default: "lifecycleStatus",
                    },
                },
                {
                    field: "detailType",
                    title: this.$t('txt_type'),
                    formatter: ({ text, row }) => row.detailType == 'null' ? '' : row.detailType,
                },
                {
                    field: "updateDate",
                    title: this.$t('txt_update_date'),
                    sortable: true, // 开启排序
                    // slots: {
                    //     default: "updateDate",
                    // },
                    formatter: ({ text, row }) => {
                        // 数据格式化
                        return row.updateDate
                            ? formatDate(row.updateDate, "YYYY-MM-DD HH:mm:ss")
                            : null;
                    },
                },
                {
                    // 操作列定义
                    field: "operation",
                    title: this.$t('txt_operation'),
                    slots: {
                        default: 'operation',
                    },
                },
            ]
        },
        toolbars() {
            return this.currentCreateRole ? [
                {
                    name: this.$t('btn_new_create'),
                    position: "before",
                    type: "primary",
                    key: "create",
                },
                {
                    name: this.$t('btn_search'),
                    position: "before",
                    display: "input",
                    value: this.searchKey,
                    allowClear: false,
                    placeholder: this.$t('search_text'),
                    // prefixIcon: "jwi-search",
                    key: "search",
                },
                // {
                //   name: "批量操作",
                //   display: "dropdown",
                //   position: "after",
                //   key: "batch",
                //   menuList: [
                //     {
                //       name: "删除",
                //       key: "delete",
                //     },
                //   ],
                // },
            ]
                :
                [
                    {
                        name: this.$t('btn_search'),
                        position: "before",
                        display: "input",
                        value: this.searchKey,
                        allowClear: false,
                        placeholder: this.$t('search_text'),
                        prefixIcon: "jwi-search",
                        key: "search",
                    },
                ];
        },
    },

    created() {
        console.log('---------this.$route.query', this.$route.query)
        // 输入回调去抖动
        this.delaySearch = _.debounce(this.reFetchData, 500);

        checkPermission
            .execute({
                "viewCode": "BASELINEOPERATION",
                "objectType": this.$route.query.type,
                "objectOid": this.$route.query.oid,
            })
            .then((data) => {
                console.log("权限查询返回结果", data);
                this.currentCreateRole = data[0].status === "disable" ? false : true;
            })
            .catch((err) => {
                this.$error(err.msg || this.$t("msg_failed"));
            });

        // 获取所有基线 -- 用于 基线对比下拉选择
        // getBaselineList
        //     .execute({
        //         index: 1,
        //         size: 9999999,
        //         searchKey: '',
        //         containerOid: this.$route.query.id,
        //     })
        //     .then((data) => {
        //         this.formModalData[1].options = data.rows.map(v => ({ value: v.oid, label: v.name }));
        //     })
        //     .catch((err) => {
        //         this.$error(err.msg || this.$t("msg_failed"));
        //     });
    },

    watch: {
        baseModalVisible(val) {
            if (!val && this.$refs.ref_appBuilder) {
                this.$refs.ref_appBuilder.clearValidate();
            }
        }
    },

    methods: {
        //获取选择基线
        getFoldertree() {
            let { query } = this.$route;
            let folderTreeParams = {
                // containerOid: query.id || this.productContainerOid,
                containerModel: query.modelDefinition,
            };
            let param = null
            fetchfolderTree
                .execute(folderTreeParams)
                .then((data) => {
                    param = this.deepData(data, true)

                })
            return param
        },

        gotoDetails(row) {

            this.dropdownAction('details', row);
        },
        searchModelTable(params) {
            return searchModelTable.execute(params);
        },
        reFetchData() {
            this.$refs.ref_table.reFetchData();
        },
        fetchTable({ current, pageSize }) { // 自带内部分页信息，如由外部控制分页，可忽略不用
            let param = {
                index: current,
                size: pageSize,
                searchKey: this.searchKey,
                containerOid: this.$route.query.oid,
            };
            this.tableLoading = true;

            return getBaselineList
                .execute(param)
                .then((data) => {
                    this.tableLoading = false;
                    /*
                    this.$set(
                        this.formModalData, 
                        1, 
                        {
                            ...this.formModalData[1],
                            defaultOptions: data.rows.map(v => ({ value: v.oid, label: v.name }))
                        }
                    );
                    // */
                    return { data: data.rows, total: data.count } // 返回格式固定 {data,total}
                })
                .catch((err) => {
                    this.tableLoading = false;
                    this.$error(err.msg || this.$t("msg_failed"));
                });
        },
        onOperateClick(key, row) {

        },
        // 工具栏点击回调
        onToolClick(item) {
            if (item.key === "create") {
                this.baseModalVisible = true;
            } else if (item.key === "delete") {
                // console.log("this.selectedRows", this.selectedRows);
                this.fetchDelete(this.selectedRows);
            }
        },
        // 工具栏输入回调
        onToolInput({ key }, value) {
            if (key === "search") {
                this.searchKey = value;
                // console.log(value);
                this.delaySearch();
            }
        },
        reFetchData() {
            this.$refs.ref_table.reFetchData();
        },

        createBaseline(callback) {
            if (callback == 'close') {
                this.baseModalVisible = false;
                return;
            }
            let appBuilder = this.$refs.ref_appBuilder;
            appBuilder
                .validate()
                .then(() => {
                    this.okBtnLoading = true;
                    let params = appBuilder.getValue();

                    createBaseline.execute({
                        ...params,
                        containerOid: this.$route.query.oid,
                        containerType: this.$route.query.type,
                        catalogOid: this.$route.query.oid,
                        catalogType: this.$route.query.type,
                    }).then((res) => {
                        this.okBtnLoading = false;
                        this.baseModalVisible = false;
                        this.$success(this.$t('txt_create_success'));

                        this.reFetchData();
                        callback && _.isFunction(callback) && callback(res);
                    }).catch((err) => {
                        this.$error(err.msg);
                        this.okBtnLoading = false;
                    });
                })
        },
        createAndAddObject() {
            this.createBaseline(res => {
                this.editingRow = res;
                this.addObjectModalVisible = true;
            })
        },

        // 添加基线对象
        addObjectOk(selectedRows) {
            console.log(selectedRows)
            if (!selectedRows.length) return this.$warning(this.$t('txt_please_seleted_data'));

            const params = {
                isReplace: false,
                oid: this.editingRow.oid,
                secObjList: selectedRows.map(v => ({
                    name: v.name,
                    number: v.number,
                    status: v.lifecycleStatus,
                    displayVersion: v.displayVersion,
                    wideType: v.type,
                    sourceOid: v.oid,
                    modelDefinition: v.modelDefinition,
                    bizMasterType: v.masterType,
                    // containerName: this.$route.query.containerName,
                    subject: true,
                    children: [],
                    lockedTime: v.lockedTime,
                    lockOwnerAccount: v.lockOwnerAccount,
                    lockOwnerOid: v.lockOwnerOid,
                    lockSourceOid: v.lockSourceOid,
                    modelIcon: v.modelIcon,
                })),
            }

            this.okBtnLoading = true;
            // checkBeforeLink
            //     .execute(params)
            //     .then((data) => {

            batchLink
                .execute(params)
                .then((data) => {
                    this.okBtnLoading = false;
                    this.tableLoading = false;
                    this.$success(this.$t('txt_add_success'));
                    // this.$refs.add_obj_model_ref.cancelFn();
                    this.addObjectModalVisible = false;
                })
                .catch((err) => {
                    this.tableLoading = false;
                    this.okBtnLoading = false;
                    this.$error(err.msg || this.$t("msg_failed"));
                });
        },
        addObjectCancel() {

        },
        dropdownAction(key, row) {
            console.log(key, row);
            if (key == 'edit') {
                Jw.jumpToDetail(
                    row,
                    {
                        layoutName: 'update',
                    }
                );
            }
            else if (key == 'details') {
                Jw.jumpToDetail(
                    row,
                    {
                        layoutName: 'show',
                    }
                );
            }
            else if (key == 'delete') {
                this.$confirm({
                    title: this.$t('txt_sureDelete'),
                    okText: this.$t('btn_ok'),
                    cancelText: this.$t('btn_cancel'),
                    onOk: () => {
                        deleteBaseline(row.oid)
                            .execute()
                            .then((data) => {
                                this.tableLoading = false;
                                this.$success(this.$t('txt_delete_success'));
                                this.reFetchData();
                            })
                            .catch((err) => {
                                this.tableLoading = false;
                                this.$error(err.msg || this.$t("msg_failed"));
                            });
                    }
                });
            }
            else if (key == 'setStatus' || key == 'saveAs') {
                this.reFetchData();
            }
            else if (key == 'contrast') {
                this.formModalData[0].slotData = row;
                /*
                this.$set(this.formModalData, 1, {
                    ...this.formModalData[1],
                    defaultOptions: this.formModalData[1].defaultOptions.filter(v => v.value != row.oid),
                })
                this.formModalData[1].defaultOptions = this.formModalData[1].defaultOptions.filter(v => v.value != row.oid);
                // */
                // return
                this.formModalVisible = true;
            }
        },

        formModalConfirm(model) {
            const sourceData = this.formModalData[0].slotData;
            // return
            this.$router.push({
                name: 'baseline-contrast',
                query: {
                    containerOid: this.$route.query.oid,
                    objectType: 'baseline',
                    sourceOid: sourceData.oid,
                    targetOid: model.targetOid,
                }
            })
        },
        formModalCancel() {
            this.formModalVisible = false;
        },
        deepData(data, onlyDirectChildren = false) {
            const scopedSlots = { title: "folderIconTitle" };

            let arr = [];
            const loop = tree => {
                tree.map((item, index) => {
                    // item.title = item.name;
                    item.key = item.oid;
                    item.value = item.oid;
                    item.scopedSlots = scopedSlots;

                    // 只获取第一层
                    if (onlyDirectChildren) {
                        const temp = {
                            ...item,
                        }
                        delete temp.children;
                        arr.push(temp);
                        return;
                    }

                    if (item.children && item.children.length > 0) {
                        item.children.map((item) => (item.childType = "child"));
                        loop(item.children);
                    }
                });
            }

            loop(data);

            return onlyDirectChildren ? arr : data;
        },
    }
}
</script>
<style lang="less">
.baseline-manage-page {
    // height: calc(~'100% - 45px');
}
</style>