
<template>
    <div class="baseline-contrast-tab-content">
        <!-- :columnTitle="columnTitle" -->
        <jwStructureContrast ref="contrast_ref" :columns="columns" :dataSource="dataSource" :lazy="true" :flagOption="{
            add: '2',
            del: '1',
            modify: '3',
        }" :loadMethod="{
    left: left_loadMethod,
    right: right_loadMethod,
}">
            <template slot="left-table-title">
                <div style="overflow:hidden;white-space:nowrap;text-overflow:ellipsis" :title="columnTitle([], 'left')">{{
                    columnTitle([], 'left') }}</div>
            </template>
            <template slot="right-table-title">
                <div style="overflow:hidden;white-space:nowrap;text-overflow:ellipsis" :title="columnTitle([], 'right')">{{
                    columnTitle([], 'right') }}</div>
            </template>
            <!-- <template #title v-if="$route.query.objectType == 'baseline'">
                <a-button @click="formModalVisible = true">修改对比基线</a-button>
            </template> -->
            <template #numberSlot='{ row }'>
                <span>
                    <jwIcon :type='row.modelIcon' />
                    <span>{{ row.number }}</span>
                </span>
            </template>
            <!-- <template #status='{ row }'>
                <a-tag>{{ row.status }}</a-tag>
            </template> -->
        </jwStructureContrast>
        <!-- 对比-修改基线弹窗 -->
        <form-modal :width="512" :title="$t('txt_seletct_baseline')" confirm-btn-position="left"
            :data-source="formModalData" :visible.sync="formModalVisible" :body-style="{
                height: '388px',
            }" @confirm="formModalConfirm" @cancelBack="formModalCancel">
            <template #currentBaseline='{ item }'>
                <div :style="{
                    'height': '40px',
                    'line-height': '40px',
                    'background': 'rgba(30,32,42,0.04)',
                    'border-radius': '4px',
                    'color': 'rgba(30,32,42,0.65)',
                }">
                    <jwIcon :type='item.modelIcon' style="margin: 0 10px;" />{{ item.number }}，{{ item.name }}，{{
                        item.detailType == 'null' ? '' : item.detailType }}
                </div>
                <a-divider />
            </template>
        </form-modal>
    </div>
</template>

<script>
import jwStructureContrast from "components/structure-contrast/contrast.vue"
// import { jwStructureContrast, jwIcon } from 'jw_frame'
import {  jwIcon } from 'jw_frame'
import ModelFactory from 'jw_apis/model-factory';
import { formatDate } from "jw_utils/moment-date";
import formModal from "components/form-modal.vue";

// 查询详情
const getObjectDetails = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/instance/findByOid`,
    method: "get",
});
// 查询容器下的基线列表
const getBaselineList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/model/query`,
    method: 'post',
});
//
const findEdgeDef = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/model/edgeDef/detail`,
    method: 'get',
});

// 对比基线结构
const compareBaseline = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/compareBom`,
    method: 'post',
});
// 对比部件结构
const compareBomStructure = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/bom/compare`,
    method: 'post',
});

// 对比基线（子结构）对象
const compareBaselineItem = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/compareBaselineItem`,
    method: 'post',
});
//

export default {
    components: {
        jwStructureContrast,
        formModal,
        jwIcon,
    },
    data() {
        return {

            columns: [
                {
                    field: "number",
                    title: this.$t('txt_number'),
                    slots: {
                        default: "numberSlot",
                    },
                },
                {
                    field: "name",
                    title: this.$t('txt_name'),
                },
                {
                    field: "status",
                    title: this.$t('txt_plan_lifecycle'),
                    formatter: ({ row }) => this.$t(row.status)
                },
                {
                    field: "modelDefinition",
                    title: this.$t('txt_type'),
                    formatter: ({ row }) => this.$t(row.modelDefinition)
                },
                {
                    field: "displayVersion",
                    title: this.$t('txt_version'),
                },
              {
                field: "use.extensionContent.position",
                title: this.$t("位号"),
              },
              {
                field: "use.quantity",
                title: this.$t("数量"),
              },
            ],

            sourceData: {},
            targetData: {},
            dataSource: [
                [],
                [],
            ],


            formModalVisible: false,
            formModalData: [
                {
                    label: this.$t('txt_current_baseline'),
                    prop: 'currentBaseline',
                    block: true,
                    required: false,
                    slotData: {
                        // modelIcon: '',
                        // number: '',
                        // name: '',
                        // detailType: '',
                    },
                    scopedSlots: {
                        default: 'currentBaseline'
                    }
                },
                {
                    label: '',
                    prop: 'targetOid',
                    type: 'select',
                    placeholder: this.$t('search_text'),
                    block: true,
                    fetch: getBaselineList,
                    showSearch: true,
                    listFilter: list => list.filter(v => v.oid != this.$route.query.sourceOid),
                    searchParams: {
                        index: 1,
                        size: 10,
                        // searchKey: '',
                        containerOid: this.$route.query.containerOid,
                    },
                    // defaultOptions: [],
                    dropdownStyle: {
                        height: '276px',
                        overflowY: 'scroll',
                    }
                },
            ],
        }
    },

    computed: {
        columnTitle() {
            return (tableData, pos) => {
                const { name, detailType, number, displayVersion } = this.sourceData;
                const { name: name2, number: number2, displayVersion: displayVersion2 } = this.targetData;

                return pos == 'left' ? `${number}，${name}${displayVersion ? '，' + displayVersion : ''}` : `${number2}，${name2}${displayVersion2 ? '，' + displayVersion2 : ''}`;
            }
        },
    },

    created() {
        this.init();
    },

    // 路由钩子函数
    // beforeRouteUpdate(to, from, next) {
    //     console.log('to ==> :', to, from, next)
    //     if (to.query.targetOid != from.query.targetOid)
    //         this.init();

    //     next();
    // },

    methods: {
        left_loadMethod({ row }) {
          console.log("left_row:" , row);
            this.left_row = row;


            return new Promise((resolve, reject) => {
                setTimeout(async () => {
                    console.log('%c left: ', 'color:red', row, this.right_row);
                    const res = await this.loadMethod('left');

                    resolve(res);
                })
            });
        },
        right_loadMethod({ row }) {
          console.log("right_row:" , row);
            this.right_row = row;

            return new Promise((resolve, reject) => {
                setTimeout(async () => {
                    console.log('%c right: ', 'color:green', row, this.left_row);
                    const res = await this.loadMethod('right');

                    resolve(res);
                })
            });
        },
        loadMethod(pos) {

            if (!this.left_row || !this.left_row.oid) {
                if (this.right_row)
                    this.right_row.hasChild = false;

                this.left_row = null;
                this.right_row = null;
                return Promise.resolve([]);
            }
            if (!this.right_row || !this.right_row.oid) {
                if (this.left_row)
                    this.left_row.hasChild = false;

                this.left_row = null;
                this.right_row = null;
                return Promise.resolve([]);
            }

            const primaryOid = this.left_row.oid;
            const targetOid = this.right_row.oid;

            const { objectType } = this.$route.query;

            return (objectType == 'baseline' ? compareBaselineItem : compareBomStructure)
                .execute({
                    size: 9999999,
                    primaryOid,
                    targetOid,
                })
                .then((data) => {
                    const sourceData = data.primary.children || [],
                        targetData = data.target.children || [];

                    if (pos == 'left') {
                        this.left_row.hasChild = !!sourceData.length;
                        sourceData.forEach(v => {
                            if (v.oid && v.flag != '1' && v.flag != '2')
                                v.hasChild = true;

                            this.setTableRowValue(v);
                        });;
                    }
                    else {
                        this.right_row.hasChild = !!targetData.length;
                        targetData.forEach(v => {
                            if (v.oid && v.flag != '1' && v.flag != '2')
                                v.hasChild = true;

                            this.setTableRowValue(v);
                        });
                    }

                    return pos == 'left' ? sourceData : targetData;
                })
                .catch((err) => {
                    this.$error(err.msg || this.$t(this.$t('txt_load_substructure_faile')));
                });
        },
        init() {
            const { sourceOid, targetOid, sourceType, sourceModelType, targetType, targetModelType, objectType } = this.$route.query;

            if (objectType != 'baseline') {
                const promiseArr = [
                    new Promise((resolve, reject) => {
                        findEdgeDef
                            .execute({ fromCode: sourceModelType, toCode: 'Part', relCode: 'USE' })
                            .then((data) => {
                                if (data) {
                                    (data.properties || []).forEach(v => {
                                        const field = v.systemDefault ? `use.${v.code}` : `use.extensionContent.${v.code}`;
                                        if (!this.columns.find(col => col.field == field))
                                            this.columns.push({
                                                field,
                                                title: v.description,
                                                code: v.code,
                                            });
                                    });
                                }

                                resolve();
                            })
                            .catch((err) => {
                                this.$error(err.msg || this.$t("msg_failed"));
                                reject(err);
                            });
                    }),
                ];

                if (targetModelType != sourceModelType)
                    promiseArr.push(
                        new Promise((resolve, reject) => {
                            findEdgeDef
                                .execute({ fromCode: targetModelType, toCode: 'Part', relCode: 'USE' })
                                .then((data) => {
                                    if (data) {
                                        (data.properties || []).forEach(v => {
                                            const field = v.systemDefault ? `use.${v.code}` : `use.extensionContent.${v.code}`;
                                            if (!this.columns.find(col => col.field == field))
                                                this.columns.push({
                                                    field,
                                                    title: v.description,
                                                    code: v.code,
                                                });
                                        });
                                    }

                                    resolve();
                                })
                                .catch((err) => {
                                    this.$error(err.msg || this.$t("msg_failed"));
                                    reject(err);
                                });
                        }),
                    );

                // 映射属性获取之后，再获取对比数据设置表头
                Promise.all(promiseArr)
                    .then(values => this.getCompareData())
                    .catch(err => console.error(err));
            }
            else
                this.getCompareData();



            getObjectDetails
                .execute({ oid: sourceOid, type: objectType == 'baseline' ? 'Baseline' : sourceType })
                .then((data) => {
                    this.sourceData = data;
                    this.$set(this.formModalData, 0, {
                        ...this.formModalData[0],
                        slotData: data
                    });
                })
                .catch((err) => {
                    this.$error(err.msg || this.$t("msg_failed"));
                });

            getObjectDetails
                .execute({ oid: targetOid, type: objectType == 'baseline' ? 'Baseline' : targetType })
                .then((data) => {
                    this.targetData = data;
                })
                .catch((err) => {
                    this.$error(err.msg || this.$t("msg_failed"));
                });
        },
        setTableRowValue(row) {

            const use = row.use || {},
                map = {
                    ...use,
                    ...(use.extensionContent || {}),
                };

            for (const key in map) {
                if (Object.hasOwnProperty.call(map, key)) {
                    const col = this.columns.find(t => t.code == key);
                    if (col) {
                        row[key] = row[col.field];
                    }
                }
            }
        },
        getCompareData() {
            const { sourceOid, targetOid, objectType } = this.$route.query;

            (objectType == 'baseline' ? compareBaseline : compareBomStructure)
                .execute({
                    index: 1,
                    size: 9999999,
                    primaryOid: sourceOid,
                    targetOid,
                })
                .then((data) => {
                    /* TEST
                    (data.primary.children || [])[0].hasChild = true;
                    (data.primary.children || [])[0].children = [];
                    (data.target.children || [])[0].hasChild = true;
                    (data.target.children || [])[0].children = [];

                    this.dataSource = [
                        [ (data.primary.children || [])[0] ],
                        [ (data.target.children || [])[0] ],
                    ];
                    // */

                    const sourceData = data.primary.children || [],
                        targetData = data.target.children || [];

                    sourceData.forEach(v => {
                        if (v.oid && v.flag != '1' && v.flag != '2')
                            v.hasChild = true;

                        this.setTableRowValue(v);
                    });
                    targetData.forEach(v => {
                        if (v.oid && v.flag != '1' && v.flag != '2')
                            v.hasChild = true;

                        this.setTableRowValue(v);
                    });

                    this.dataSource = [
                        sourceData,
                        targetData,
                    ];
                })
                .catch((err) => {
                    this.$error(err.msg || this.$t("msg_failed"));
                });
        },
        formModalConfirm(model) {
            // 3.1.0+ push/replace 返回 Promise

            this.$router.push({
                name: 'baseline-contrast',
                query: {
                    ...this.$route.query,
                    targetOid: model.targetOid,
                }
            })
                .then(to => {
                    this.init();
                    this.formModalVisible = false;
                })
                .catch(err => {
                    console.log('push.catch:', err)
                });
        },
        formModalCancel() {
            this.formModalVisible = false;
        }
    }
}
</script>


<style lang="less" scoped>
.baseline-contrast-tab-content {
    height: 100%;
    background-color: #fff;
}
</style>

