<template>
	<div class="baseline-contrast-page layout">
		<div class="object-details-toolbar">
			<div class="left">
				<span class="route">
					<a @click="goBack">
						{{
							$route.query.objectType == "baseline" ? $t('txt_baseline_management') : $t('txt_content_management')
						}}
					</a>
				</span>
				<span>
					&gt;
				</span>
				<span class="title" :title="$t('txt_baseline_management')">
					<span>{{
						$route.query.objectType == "baseline" ? $t('txt_baseline_comparison') : $t('txt_object_comparison')
					}}</span>
				</span>
			</div>
		</div>
		<jw-simple-tabs :value="tabValue" :options="tabOption" @change="tabChange" />
		<property v-if="tabValue == 'property'" />
		<structure v-if="tabValue == 'structure'" />
	</div>
</template>

<script>
import { jwSimpleTabs } from "jw_frame"
import structure from "./structure.vue"
import property from "./property.vue"

export default {
	components: {
		structure,
		property,
		jwSimpleTabs,
	},
	data() {
		return {
			tabValue:
				this.$route.query.objectType == "baseline" ? "structure" : "property",
			tabOption:
				this.$route.query.objectType == "baseline"
					? [{ value: "structure", label: this.$t('txt_structure') }]
					: [
						{ value: "property", label: this.$t('txt_property') },
						{ value: "structure", label: this.$t('txt_structure') },
					],
		}
	},

	created() {
		console.log("获取objectOid参数=>", this.$route.query)
	},

	// 路由钩子函数
	// beforeRouteUpdate(to, from, next) {
	//     console.log('to ==> :', to, from, next)
	//     if (to.query.targetOid != from.query.targetOid)
	//         this.init();

	//     next();
	// },

	methods: {
		goBack(e) {
			e && e.preventDefault();

			// if (this.$route.query.objectType == "object") {
			this.$router.go(-1)
			// } else {
			// 	this.$router.push({
			// 		name: "productContent",
			// 		query: {
			// 			...this.$route.query,
			// 			id: this.$route.query.containerOid,
			// 			activeTab: this.$route.query.objectType == "baseline" ? "baseline" : "",
			// 		},
			// 	})
			// }
		},
		tabChange(option) {
			let { value } = option
			this.tabValue = value
		},
	},
}
</script>

<style lang="less" scoped>
.baseline-contrast-page.layout {
	height: 100%;
	background-color: #fff;
	display: flex;
	flex-direction: column;

	.wrap-class {
		margin-top: 23px;
		overflow: unset;
	}

	.object-details-toolbar {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 10px 18px 0;
		padding: 0;

		>div>span {
			margin-right: 15px;

			>i {
				font-size: 18px;
			}
		}

		.left {
			display: flex;
			align-items: center;

			.title {
				// max-width: 300px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				display: inline-block;
				background-color: #ddd;
				padding: 5px 13px;
				border-radius: 4px;
			}

			.route {
				color: #a69f9f;

				>button {
					color: #a69f9f;
					padding: 0;

					&:hover {
						color: #40a9ff;
					}
				}
			}
		}

		.right {
			margin-right: 90px;
			display: flex;
			align-items: center;

			>div {
				margin-left: 15px;
			}

			.ant-btn {
				width: inherit !important;
			}
		}
	}
}</style>
