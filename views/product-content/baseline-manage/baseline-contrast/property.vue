<template>
  <div class="baseline-contrast-tab-content">
    <jwPropertyContrast
      :columnWidth="500"
      title=""
      :loading="loading"
      :propertyList="propertyList"
      :dataSource="dataSource"
      :columnTitle="columnTitle"
      @onAdd="onAdd"
    />
  </div>
</template>

<script>
import { formatDate } from "jw_utils/moment-date";
import { jwPropertyContrast } from "jw_frame";
import ModelFactory from "jw_apis/model-factory";
import { changtoJsonValue } from "/utils/util";

// 对比部件属性
const compareBomAttr = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/part/bom/attribute`,
  method: "post",
});

//
const findPropertyAdnCls = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/model/find/withPropertyAndCls`,
  method: "get",
});
// 查询对象详情
const findByOid2 = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/findByOid2`,
  method: "get",
});
// 查询 扩展？ 属性
const findExtendedProperty = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/classification/find/withProperty`,
  method: "get",
});
//查询密级
const getLevelList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.securityService}/secret/specialSearch`,
  method: "get"
});
export default {
  components: {
    jwPropertyContrast,
  },
  data() {
    return {
      columnTitle: (data) => {
        return [data.number, data.name, data.displayVersion].join("，");
      },
      propertyList: [
          /*
        {
          name: "创建时间",
          field: "createtime",
          formatter: ({ text }) => {
            return text ? formatDate(text, "YYYY-MM-DD HH:mm:ss") : null;
          },
        },
        {
          name: "结束时间",
          field: "donetime",
          slots: {
            default: ({ text }, h) => {
              return [
                text ? (
                  <span>{formatDate(text, "YYYY-MM-DD HH:mm:ss")}</span>
                ) : null,
              ];
            },
          },
        },
        {
          name: "可视化和属性",
          field: "test1",
          children: [
            {
              name: "名称",
              field: "name",
            },
            {
              name: "生命周期",
              field: "lifecycle",
            },
            {
              name: "修改人",
              field: "updator",
            },
            {
              name: "修改时间",
              field: "updatetime",
              slots: {
                default: ({ row, column }, h) => {
                  return [
                    <span>
                      {formatDate(
                        _.get(row[column.field]),
                        "YYYY-MM-DD HH:mm:ss"
                      )}
                    </span>,
                  ];
                },
              },
            },
          ],
        },
        {
          name: "常规",
          field: "test2",
          children: [
            {
              name: "来源",
              field: "source",
            },
            {
              name: "单位",
              field: "unit",
            },
            {
              name: "名称",
              field: "othername",
            },
            {
              name: "视图",
              field: "view",
            },
          ],
        },
        // */
      ],
      dataSource: [
        {},
        {}
          /*
        {
          name: "对象RW10102",
          lifecycle: "Reviewing",
          updator: "张三",
          updatetime: 155684884,
          donetime: 155684884,
          // createtime:155684884,
          source: "自制",
          unit: "Kg",
          othername: "WT00002341",
          view: "InWork",
        },
        {
          name: "对象RW10101",
          lifecycle: "Reviewing",
          updator: "张三",
          updatetime: 1556848845,
          // donetime:155684883,
          createtime: 155684884,
          source: "自制",
          unit: "Kg",
          othername: "WT00002341",
          view: "InWork",
        },
        // */
      ],
      loading: false,
      secret: [], 

      //属性映射
      sourceValueMap: {},
      targeValueMap: {}
    };
  },
  created() {
    this.getLevelList()
    this.init();
  },
  methods: {
    pushProperty (fields) {
      fields.forEach(v => {
        const item = {
          name: v.displayName,
          field: v.code,
        }
        if (v.displayName.indexOf('时间') > -1)
          item.formatter = ({ text }) => {
           
            return text ? formatDate(text, "YYYY-MM-DD HH:mm:ss") : null;
          }
        // 密级
        if (v.code == 'levelForSecrecy')
          item.formatter = ({ text }) => (this.secret.find(v => v.val == text) || {}).txt;

        //人员
        if(v.constraintType=="List<User>"){
          item.formatter = ({ text }) => {
            return text?.name||text;
          }
        }  

        //下拉值
        let selectItem= v.validators.find(item=>item.category=='list')
        if(selectItem){
          let content=selectItem.content
            if(content.indexOf('{')>-1 && content.indexOf('}')>-1) {
              try {
                let options=new Function( `return [${content}];` )()

                item.formatter = ({ text }) => {
                  let o= options.find(item=>item.val==text)
                  return o?.txt||text;
                }
                
              }catch(e) {
                console.err("字符串解析成List异常")
              }
            }
        }

        if (this.propertyList.every(t => t.field != v.code))
            this.propertyList.push(item);
      });
    },
    init() {
      const { sourceOid, targetOid, sourceType, sourceModelType, targetType, targetModelType } = this.$route.query;

      // 查 基本属性/扩展属性
      findPropertyAdnCls
        .execute({
          modelName: sourceModelType,
        })
        .then((res) => {
          this.pushProperty(res.fields)
          this.getPropertyCls(res.fields, this.sourceValueMap)
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
      
      // 当两个对象类型不一样时，查 target 对象的 基本属性/扩展属性，取 并集作展示
      if (targetModelType != sourceModelType)
        findPropertyAdnCls
          .execute({
            modelName: targetModelType,
          })
          .then((res) => {
            this.pushProperty(res.fields)
            this.getPropertyCls(res.fields, this.targeValueMap)
          })
          .catch((err) => {
            this.$error(err.msg || this.$t("msg_failed"));
          });

      // 查对象详情，获取 扩展属性 及 分类属性 的值
      findByOid2
        .execute({
          oid:  sourceOid,
          type: sourceType,
        })
        .then((res) => {
          // 分类--的值
          this.dataSource[0].classificationInfo = res.clsDisplayName;
          // 获取分类属性
          if (res.classificationInfo && res.classificationInfo.code)
          {
            findExtendedProperty
              .execute({
                clsCode:  res.classificationInfo.code,
                searchKey: '',
              })
              .then((res) => {
                this.pushProperty(res.fields)
                this.getPropertyCls(res.fields, this.sourceValueMap)
              })
              .catch((err) => {
                // this.$error(err.msg || this.$t("查询分类属性失败"));
              });
          }
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("txt_search_feild"));
        });

      // 获取分类属性，取 并集
      findByOid2
        .execute({
          oid:  targetOid,
          type: targetType,
        })
        .then((res) => {
          // 分类--的值
          this.dataSource[1].classificationInfo = res.clsDisplayName;

          if (res.classificationInfo && res.classificationInfo.code)
          {
            findExtendedProperty
              .execute({
                clsCode:  res.classificationInfo.code,
                searchKey: '',
              })
              .then((res) => {
                this.pushProperty(res.fields)
                this.getPropertyCls(res.fields, this.targeValueMap)
              })
              .catch((err) => {
                // this.$error(err.msg || this.$t("查询分类属性失败"));
              });
          }
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("查询对象详情失败"));
        });
        
      compareBomAttr
        .execute({
          index: 1,
          size: 9999999,
          primaryOid: sourceOid,
          targetOid,
        })
        .then((data) => {
          const d0 = data[0] || {};
          const d1 = data[1] || {};

          this.dataSource = [
              {
                ...this.dataSource[0],
                ...d0,
                ...(d0.clsProperty || {}),
                ...(d0.extensionContent || {}),
              },
              {
                ...this.dataSource[1],
                ...d1,
                ...(d1.clsProperty || {}),
                ...(d1.extensionContent || {}),
              },
          ]
          this.changeKeyValueLabel()
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("txt_search_feild"));
        });
    },

    //转换json属性
    changeKeyValueLabel(){
      //source
      this.changeValueLine(this.dataSource[0], this.sourceValueMap)
      this.changeValueLine(this.dataSource[1], this.targeValueMap)
    },
    changeValueLine(obj, map){
      for (const key in obj) {
        if (Object.hasOwnProperty.call(obj, key)) {
          const element = obj[key];
          if(map[key]){
            let attr = map[key].find(line => line.val === element)
            if(attr){
              obj[key] = attr.txt
            }
          }
        }
      }
    },
    //获取属性
    getPropertyCls(fields, obj){
      fields.forEach(row => {
        const validor = row.validators.find(valid => valid.category === "list" && valid.content.indexOf("{") === 0)
        if(validor){
          let str = validor.content
          try {
            obj[row.code] = changtoJsonValue(str)  
          } catch (error) {
            console.error("枚举属性转换异常：", str, row)
          }
          this.changeKeyValueLabel()
        }
      })
    },
    //查询密级
    getLevelList(){
      getLevelList
        .execute()
        .then(res => {
            this.secret = res
        })
        .catch(err => {
            this.$error(err.msg || this.$t("txt_get_failure"));
        });
    },
    onAdd() {
      /*
      this.loading = true;
      setTimeout(() => {
        this.dataSource.push({
          name: "对象RW10101",
          lifecycle: "Reviewing",
          updator: "张三",
          updatetime: 1556848845,
          donetime: 155684883,
          createtime: 155684884,
          source: "自制2",
          unit: "Kg",
          othername: "WT00002341",
          view: "InWork",
        });
        this.loading = false;
      }, 1000);
      // */
    },
  },
};
</script>


<style lang="less" scoped>
.baseline-contrast-tab-content {
  height: 100%;
  background-color: #fff;
}
</style>