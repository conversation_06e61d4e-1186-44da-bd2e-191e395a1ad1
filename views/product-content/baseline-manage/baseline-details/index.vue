
<template>
  <div class="baseline-detail-page layout">
    <div class="object-details-toolbar">
      <div class="left">
        <span class="route">
          <a @click="goBack">
            {{ $t('txt_baseline_management') }}
          </a>
        </span>
        <span>
            &gt;
          </span>
        <span v-if="objectDetailsData.lockOwnerOid">
          <jw-icon v-if="lock" type="#jwi-beiwojianchu"></jw-icon>
          <jw-icon v-else type="#jwi-bierenjianchu"></jw-icon>
        </span>
        <span class="title" :title="objectDetailsData.name">
          <jw-icon v-if="objectDetailsData.modelIcon &&
            objectDetailsData.modelIcon.includes('#')
            " :type="objectDetailsData.modelIcon" />
          <i v-else :class="objectDetailsData.modelIcon"></i>
          <span>{{ objectDetailsData.name }}, {{ objectDetailsData.number }}
          </span>
        </span>
      </div>
      <div class="right">
        <!-- v-if="operationShow" -->
        <baseline-dropdown permission-view-code="BASELINEINSTANCEOPERATION" :current-record="objectDetailsData"
          :containerOid="containerInfo.containerOid" :containerModel="containerInfo.modelDefinition"
          @complete="dropdownAction">
          <a-button type="primary">
            <i class="jwi-iconmenu-application" style="margin-right: 10px"></i>
            &nbsp;&nbsp;&nbsp;&nbsp;{{ $t('txt_operation') }}
            <a-icon type="down" />
          </a-button>
        </baseline-dropdown>
      </div>
    </div>
    <jw-simple-tabs :value="tabValue" :options="tabOption" @change="tabChange" />
    <basic-info v-if="tabValue == 'basicInfo'" :objectDetailsData='objectDetailsData' @rerender='getObjectDetails' />
    <baseline-list v-if="tabValue == 'baselineList'" :objectDetailsData='objectDetailsData' />
  </div>
</template>
<script>
import { jwSimpleTabs, jwIcon } from "jw_frame";
import basicInfo from "./basic-info.vue";
import baselineList from "./baseline-list.vue";
import BaselineDropdown from '../baseline-dropdown.vue';
import ModelFactory from "jw_apis/model-factory";
import { findDetail } from "apis/baseapi";

/**
 * 基线管理
 */
const deleteBaseline = oid => ModelFactory.create({
  url: `${Jw.gateway}/${Jw.baselineServer}/baseline/model/delete/${oid}`,
  method: 'delete',
});


export default {
  components: {
    jwSimpleTabs,
    basicInfo,
    baselineList,
    BaselineDropdown,
    jwIcon,
  },

  data() {
    return {
      // tab选项
      tabValue: "basicInfo",
      tabOption: [
        { value: "basicInfo", label: this.$t('txt_base_info') },
        { value: "baselineList", label: this.$t('txt_baseline_listing') },
      ],

      operationShow: false,
      objectDetailsData: {},
      routeObject: [],
    };
  },

  computed: {
    containerInfo() {
      return (this.objectDetailsData.containerInfo || [])[0] || {}
    }
  },

  created() {

    /* 下拉列表按钮权限
    checkPermission
        .execute({
            "viewCode":"BASELINEINSTANCEOPERATION",
            "objectOid": this.$route.params.oid,
        })
        .then((data) => {
            console.log("权限查询返回结果", data);
            this.operationShow = data[0].status === "disable" ? false : true;
        })
        .catch((err) => {
            this.$error(err.msg || this.$t("msg_failed"));
        });
      // */

    this.getObjectDetails();
  },

  methods: {
    goBack(e) {
      e && e.preventDefault();
      Jw.jumpToDetail({
        ...this.containerInfo,
        tabActive: 'baseline',
      });
      //   const containerInfo = this.containerInfo;
      //   this.$router.push({
      //     name: 'productContent',
      //     query: {
      //       id: containerInfo.oid,
      //       type: containerInfo.type,
      //       containerName: containerInfo.name,
      //       containerModel: containerInfo.modelDefinition,
      //       containerFlag: containerInfo.modelDefinition == "ProductContainer" ? 'product' : 'resource',
      //       activeTab: 'baseline',
      //     }
      //   });      
    },
    getObjectDetails() {
      const { oid } = this.$route.query;

      findDetail
        .execute({ oid, type: 'Baseline' })
        .then((data) => {
          // this.tableLoading = false;
          this.objectDetailsData = data;
        })
        .catch((err) => {
          // this.tableLoading = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    tabChange(option) {
      console.log(option);
      let { value } = option;
      this.tabValue = value;
    },

    dropdownAction(key, row) {
      console.log(key, row);
      if (key == 'edit') {
        Jw.jumpToDetail(
          row,
          {
            layoutName: 'update',
          }
        );
      }
      else if (key == 'delete') {
        this.$confirm({
          title: this.$t('txt_sureDelete'),
          okText: this.$t('btn_ok'),
          cancelText: this.$t('btn_cancel'),
          onOk: () => {
            deleteBaseline(row.oid)
              .execute()
              .then((data) => {
                this.tableLoading = false;
                this.$success(this.$t('txt_delete_success'));
                // this.reFetchData();
                this.goBack()
              })
              .catch((err) => {
                this.tableLoading = false;
                this.$error(err.msg || err.message || this.$t("msg_failed"));
              });
          }
        });
      }
      // else if (key == 'compare')
      // {

      // }
    }
  },
};
</script>

<style lang="less" scoped>
.baseline-detail-page.layout {
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .wrap-class {
    margin-top: 23px;
    overflow: unset;
  }

  .object-details-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 15px 0;

    >div>span {
      margin-right: 15px;

      >i {
        font-size: 18px;
      }
    }

    .left {
      display: flex;
      align-items: center;

      .title {
        // max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
        background-color: #ddd;
        padding: 5px 13px;
        border-radius: 4px;
      }

      .route {
        color: #a69f9f;

        >button {
          color: #a69f9f;
          padding: 0;

          &:hover {
            color: #40a9ff;
          }
        }
      }
    }

    .right {
      margin-right: 90px;
      display: flex;
      align-items: center;

      >div {
        margin-left: 15px;
      }

      .ant-btn {
        width: inherit !important;
      }
    }
  }
}
</style>