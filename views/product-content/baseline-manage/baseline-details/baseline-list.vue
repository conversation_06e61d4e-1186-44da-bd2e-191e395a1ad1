
<template>
    <div class="all-background baseline-detail-page">
        <jw-table
          ref="ref_table"
          :disableCheck="disableCheckFun"
          :data-source.sync="tableData"
          :columns="columns"
          :selectedRows.sync="selectedRows"
          :toolbars="toolbars"
          :fetch='fetchTable'
          :tree-config="{rowField: 'oid', expandRowKeys}"
          @onToolClick="onToolClick"
          @onToolInput="onToolInput"
          @onOperateClick="onOperateClick"
        >
            <template #numberSlot='{ row }'>
                <span @click="gotoDetails(row)" style="cursor:pointer;">
                    <jwIcon :type='row.modelIcon' />
                    <span style="color: #255ed7">{{ row.number }}</span>
                </span>
            </template>
            <template #status='{ row }'>
                <a-tag>{{ $t(row.status) }}</a-tag>
            </template>
        </jw-table>
        
        <add-object-modal
            :title="$t('txt_collecting_Objects')"
            ref="add_obj_model_ref"
            :width='1280'
            :type-list='typeList'
            :show-page="false"
            :show-context="false"
            :visible.sync='addObjectModalVisible'
            :ok-btn-loading="okBtnLoading"
            :fetch='searchModelTable'
            @ok='addObjectOk'
            @cancel='addObjectCancel'
        />
		<jw-search-engine-modal
            :title="$t('txt_add_baseline_object')"
            only-search-object
            :visible.sync="globalSearchVisible"
            :ok-btn-loading="okBtnLoading"
            :model-list='modelList'
            @ok='addObjectOk'
        />
        <!-- 替代版本 -->
        <form-modal
            :width="512"
            :title="$t('txt_replacement_version')"
            confirm-btn-position="left"
            :data-source="formModalData"
            :visible.sync="formModalVisible"
            :ok-btn-loading="okBtnLoading"
            :body-style="formModalBodyStyle"
            @confirm="formModalConfirm"
            @cancelBack="formModalCancel"
        />
        <!-- 收集BOM结构 -->
        <base-color-modal
            :title="$t('txt_collecting_BOM')"
            :width="512"
            :visible.sync="collectBomVisible"
            :ok-btn-loading="okBtnLoading"
            @ok="onCollectBOM"
            @cancel="onCloseCollectModal"
        >
            <a-row :gutter="20">
                <a-col :span="12">
                    <a-form-model-item :label="$t('txt_select_level')">
                        <a-select v-model.trim="bomLevel"
                            style="width:100%;"
                            :placeholder="$t('msg_select')" 
                            @change="onChangeCollectLevel"
                        >
                            <a-select-option value="all">{{ $t('txt_alldata') }}</a-select-option>
                            <a-select-option value="customerLevel">{{ $t('txt_custom_level') }}</a-select-option>
                        </a-select>
                    </a-form-model-item>
                </a-col>
                <a-col :span="12" v-if="bomLevel=='customerLevel'">
                    <a-form-model-item :label="$t('txt_custom_level')">
                        <a-input-number
                            style="width:100%;"
                            v-model.trim="level"
                            :precision="0"
                            :min="1"
                            :max="20"
                        />
                    </a-form-model-item>
                </a-col>
            </a-row>
        </base-color-modal>
    </div>
</template>

<script>
import ModelFactory from 'jw_apis/model-factory';
import addObjectModal from 'components/add-object-modal';
import formModal from "components/form-modal.vue";
import { findItem, recursiveTree, exportFile } from 'utils/util';
import { jwIcon, jwSearchEngineModal } from 'jw_frame';
import { getCookie } from "jw_utils/cookie"
import baseColorModal from 'components/base-color-modal';

// 
const checkPermission = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
  method: "post",
});

// 获取基线清单
const getBaselineList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.baselineServer}/baseline/searchSubWithPage`,
  method: "post",
});
// 查询子节点
const fetchTreeNodeChild = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.baselineServer}/baseline/find/subStruct`,
  method: "post",
});

// 根据类型（）及关键字筛查询
// const searchModelTable = ModelFactory.create({
//     url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/fuzzyPage`,
//     method: 'post',
// });

// 添加对象前 校验
const checkBeforeLink = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/checkBeforeLink`,
    method: 'post',
});
// 添加对象
const batchLink = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/batchLink`,
    method: 'post',
});
// 删除对象
const batchDelete = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/link/batchDelete`,
    method: 'post',
});

// 获取相关对象
const relationObjsModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.sysconfigServer}/collectionRule/findByAppliedType`,
  method: "get"
});

const collectBom = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.baselineServer}/baseline/collectBom`,
  method: "post"
});

// 获取清单
const relationSearchModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/relatedObject/fuzzy`,
  method: "post"
});
// 获取历史版本列表
const getHistoryList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/findHistory`,
  method: "get",
});
// 替换
const versionReplace = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.baselineServer}/baseline/version/replace`,
  method: "post",
});

export default {
    components: {
        addObjectModal,
        formModal,
        jwIcon,
        jwSearchEngineModal,
        baseColorModal,
    },
    props: ['objectDetailsData'],
    data() {
        return {
            searchKey: "",
            tableData: [],
            selectedRows: [],
            expandRowKeys: [],
            pagination: {
                page: 1,
                size: 10,
                total: 30,
            },
            tableLoading: false,
            total: 0,
            selectRow: [],
            currentCreateRole: false,
            currentExportRole: false,
            
            modalAction: '', // addObject 添加对象， collect 收集
            addObjectModalVisible: false,

            globalSearchVisible: false, // 添加对象--搜索
            okBtnLoading: false,
            modelList: [
                {
                    name: this.$t('txt_part'),
                    code: "PartIteration",
                },
                {
                    name: this.$t('txt_document'),
                    code: "DocumentIteration",
                },
                {
                    name: "MCAD",
                    code: "MCADIteration",
                },
                {
                    name: "ECAD",
                    code: "ECADIteration",
                },
            ],

            typeList: [],
            collectList: [],
            editingRecord: {},

            formModalData: [
                {
                    type: 'select',
                    label: this.$t('txt_version'),
                    prop: 'newOid',
                    options: [],
                    block: true,
                    showSearch: true,
                    dropdownStyle: {
                        maxHeight: '250px',
                        overflowY: 'auto',
                    }
                }
            ],
            formModalVisible: false,
            recordVersionList: [], // 当前被替换的基线对象 的历史版本

            operationBtnShow: false, // 列表操作按钮权限

            collectBomVisible: false,
            bomLevel: 'all',
            level: 1,
        }
    },

    computed: {
        formModalBodyStyle () {

            return {
                height: 70 + this.formModalData[0].options.length * 32 + 'px',
                maxHeight: 70 + 240 + 40 + 'px',
            }
        },
        
        columns() {
            return [
                {
                    field: "number",
                    title: this.$t('txt_plan_number'),
                    // sortable: true,
                    treeNode: true,
                    slots: {
                        default: "numberSlot",
                    },
                },
                {
                    field: "name",
                    title: this.$t('txt_name'),
                    sortable: true,
                    formatter: ({ text, row }) => {
                     return row.cname || row.name
                    }
                },
                {
                    field: "displayVersion",
                    title: this.$t('txt_plan_version'),
                    sortable: true,
                    // slots: {
                    //     default: "versionSlot",
                    // },
                },
                {
                    field: "status",
                    title: this.$t('txt_lifecycle'),
                    sortable: true,
                    slots: {
                        default: 'status',
                    }
                },
                {
                    field: "bizMasterType",
                    title: this.$t('txt_type'),
                    sortable: true,
                    formatter: ({ text, row }) => {
                     return this.$t(row.bizModelDefinition ? row.bizModelDefinition:row.bizMasterType)
                   }
                },
                {
                    // 操作列定义
                    field: "operation", //关键字
                    title:this.$t('txt_operation'),
                    btns: [
                        {
                            icon: "jwi-iconcollect-bom",
                            title: this.$t('txt_collecting_BOM'),
                            key: "collect-bom",
                            isShow: this.operationBtnShow,
                        },
                        {
                            icon: "jwi-iconcollect",
                            title: this.$t('txt_collecting_Objects'),
                            key: "collect",
                            isShow: this.operationBtnShow,
                        },
                        {
                            icon: "jwi-iconreturn",
                            title: this.$t('txt_replacements'),
                            key: "replace",
                            isShow: row => this.operationBtnShow && row.subject,
                        },
                        {
                            icon: "jwi-icondelete",
                            title: this.$t('txt_delete'),
                            key: "delete",
                            isShow: row => this.operationBtnShow && row.subject,
                        },
                    ]
                },
            ];
        },
        toolbars() {
            return [
                {
                    name: this.$t('txt_add_c'),
                    position: "before",
                    type: "primary",
                    key: "create",
                    isVisible: this.currentCreateRole,
                },
                {
                    name: this.$t('btn_search'),
                    position: "before",
                    display: "input",
                    value: this.searchKey,
                    allowClear: false,
                    placeholder: this.$t('search_text'),
                    prefixIcon: "jwi-search",
                    key: "search",
                },
                {
                    name: this.$t('btn_export'),
                    position: "after",
                    type: "primary",
                    key: "export",
                    isVisible: this.currentExportRole,
                },
                {
                  name: this.$t('btn_batch_operation'),
                  display: "dropdown",
                  position: "after",
                  key: "batch",
                  isVisible: this.operationBtnShow,
                  menuList: [
                    {
                      name: this.$t('btn_delete'),
                      key: "delete",
                    },
                  ],
                },
            ];
        },
    },

    created () {
        // 输入回调去抖动
        this.delaySearch = _.debounce(this.reFetchData, 500);
        
        // 添加按钮权限
        checkPermission
            .execute({
                "viewCode":"BASELINEINSTANCEADD",
                "objectOid": this.$route.query.oid,
            })
            .then((data) => {
                if (data && data.length) {
                    let createCode = data.find(val => val.code === 'create');
                    this.currentCreateRole = createCode.status === 'disable' ? false : true;
                    let exportCode = data.find(val => val.code === 'export');
                    this.currentExportRole = exportCode.status === 'disable' ? false : true;
                }
            })
            .catch((err) => {
                this.$error(err.msg || this.$t("msg_failed"));
            });

        // 
        checkPermission
            .execute({
                "viewCode":"BASELINEINSTANCE",
                "objectOid": this.$route.query.oid,
            })
            .then((data) => {
                console.log("权限操纵查询返回结果", data);
                this.operationBtnShow = (data.find(v => v.code == 'edit') || {}).status === "enable";
            })
            .catch((err) => {
                this.$error(err.msg || this.$t("msg_failed"));
            });
        
    },

    methods: {
        gotoDetails (row) {
            let baseRow = {
                type: row.bizType || row.wideType,
                oid:row.sourceOid,
                modelDefinition: row.modelDefinition,
                masterType: row.bizMasterType,
                toUrl: '/object-details',
            };
            console.log('baseRow',baseRow)
            Jw.jumpToDetail(baseRow,{blank:true});
        },
        // table 加载子节点
        loadChildrenMethod ({ row }) {
            return fetchTreeNodeChild.execute({
                oid: row.oid,
            }).then((res) => {
                if (res.children)
                {
                    if (res.children.length === 0)
                        row.hasChild = false;
                    
                    recursiveTree(res.children || [], node => {
                        // node.hasChild = !!(node.children || {}).length;
                        node.hasChild = true; // 展开子节点后，默认展示每个子节点可展开按钮
                    });
                }
                else
                    row.hasChild = false;
                
                this.expandRowKeys.push(row.oid);
                return res.children;
            }).catch((err) => {
                console.error('err: ', err)
                this.$error(err.msg);
            })
        },

        // 版本替换
        formModalConfirm(model) {
            const newItem = this.recordVersionList.find(v => v.oid == model.newOid);
            console.log('newItem',newItem)
            const params = {
                oid: this.$route.query.oid,
                oldOid: this.editingRecord.oid,
                newItem: {
                    containerName: this.editingRecord.containerName,
                    displayVersion: newItem.displayVersion,
                   // modelDefinition: newItem.modelDefinition,
                    name: newItem.name,
                    number: newItem.number,
                    sourceOid: newItem.sourceOid || newItem.oid,
                    status: newItem.lifecycleStatus,
                    subject: !!this.editingRecord.subject,
                    wideType: newItem.type,
                    bizMasterType:newItem.masterType,
                    bizModelDefinition:newItem.modelDefinition
                }
            }
            console.log('params',params)
            
            this.okBtnLoading = true;
            versionReplace
                .execute(params)
                .then((data) => {
                    this.$success(this.$t('msg_save_success'));
                    this.formModalVisible = false;
                    this.okBtnLoading = false;
                    this.reFetchData();
                })
                .catch((err) => {
                    this.okBtnLoading = false;
                    this.$error(err.msg || this.$t("msg_failed"));
                });
        },
        formModalCancel() {
            this.formModalVisible = false;
        },

        // 查询结构相关对象
        searchModelTable (params) {
            let row = this.collectList.find(item => {
                return item.oid == params.type;
            });
            return relationSearchModel.execute({ ...params, ...row, mainObjectOid: this.editingRecord.sourceOid });
        },
        
        reFetchData () {
            this.$refs.ref_table.reFetchData();
        },
        fetchTable({current,pageSize}) { // 自带内部分页信息，如由外部控制分页，可忽略不用
            let param = {
                masterOid: this.$route.query.oid,
                masterType: 'Baseline',
                pageNum: current,
                pageSize: pageSize,
                relationTypeList: ["BASELINEMEMBER"],
                searchKey: this.searchKey,
            };
            this.tableLoading = true;

            return getBaselineList
                .execute(param)
                .then((data) => {
                    this.tableLoading = false;
                    recursiveTree(data.rows, node => {
                        node.hasChild = !!(node.children || {}).length;
                    });
                    console.log('%c res: ', 'color:red;font-size:20px', data.rows)
                    return {data:data.rows,total:data.count} // 返回格式固定 {data,total}
                })
                .catch((err) => {
                    console.error('===> ', err)
                    this.tableLoading = false;
                    this.$error(err.msg || this.$t("msg_failed"));
                });
        },
        disableCheckFun({ row }) {
            if (!row.subject) {
                return true;
            }
            return false;
        },
        onOperateClick (key, row) {
            this.editingRecord = row;

            // 收集bom
            if (key == 'collect-bom')
            {
                this.collectBomVisible = true;
            }
            // 收集对象
            if (key == 'collect')
            {
                let params = {
                    appliedType: `${row.bizModelDefinition}_Related_Object`,
                    mainObjectType: row.bizModelDefinition,
                };
                
                relationObjsModel
                    .execute(params)
                    .then(res => {
                        this.modalAction = 'collect';
                        this.collectList = res;
                        this.typeList = res.map(v => ({
                            key: v.oid,
                            label: v.relationDisplayName,
                        }));
                        this.addObjectModalVisible = true;
                    })
                    .catch(err => {
                        this.$error(this.$t('txt_get_object_list'));
                    });
            }
            else if (key == 'replace')
            {
                getHistoryList
                    .execute({
                        // modelDefinition,
                        oid: row.sourceOid,
                        type: row.wideType,
                    })
                    .then((data) => {
                        this.recordVersionList = data;
                        this.$set(
                            this.formModalData, 
                            0,
                            {
                                ...this.formModalData[0],
                                options: data.map(v => ({
                                    label: `${v.displayVersion} | ${v.lifecycleStatus}`,
                                    value: v.oid,
                                    displayVersion: v.displayVersion,
                                    lifecycleStatus: v.lifecycleStatus,
                                    scopedSlots: {
                                        label: 'version_lifecycle'
                                    }
                                }))
                            },
                        ); 
                        this.formModalVisible = true;
                    })
                    .catch((err) => {
                        console.error(err);
                        this.$error(err.msg || this.$t("msg_failed"));
                    });
            }
            else if (key == 'delete')
            {
                this.$confirm({
                    title: this.$t('txt_sureDelete'),
                    okText:this.$t('btn_ok'),
                    cancelText:this.$t('btn_cancel'),
                    onOk: () => {
                        batchDelete
                            .execute({
                                modelType: 'Baseline',
                                oid: this.$route.query.oid,
                                relationType: 'BASELINEMEMBER',
                                subOidList: [row.oid],
                            })
                            .then((data) => {
                                this.tableLoading = false;
                                this.reFetchData();
                            })
                            .catch((err) => {
                                this.tableLoading = false;
                                this.$error(err.msg || this.$t("msg_failed"));
                            });
                    }
                });
            }
        },
        onCollectBOM() {
            this.okBtnLoading = true;
            let params = {
                oid: this.editingRecord.oid,
                name: this.editingRecord.name,
                number: this.editingRecord.number,
                status: this.editingRecord.status,
                displayVersion: this.editingRecord.displayVersion,
                wideType: this.editingRecord.wideType,
                bizMasterType: this.editingRecord.bizMasterType,
                sourceOid: this.editingRecord.sourceOid,
                modelDefinition: this.editingRecord.modelDefinition,
                containerName: this.editingRecord.containerName,
                subject: this.editingRecord.subject,
                children: this.editingRecord.children,
                modelIcon: this.editingRecord.modelIcon,
                level: this.bomLevel == 'all' ? '' : this.level,
            }
            collectBom.execute(
                params
            ).then(res => {
                this.$success(this.$t('msg_success'));
                this.okBtnLoading = false;
                this.onCloseCollectModal();
                this.reFetchData();
            }).catch(err => {
                this.okBtnLoading = false;
                this.collectBomVisible = false;
                this.$error(this.$t('txt_get_object_list'));
            });
        },
        onCloseCollectModal() {
            this.collectBomVisible = false;
            this.bomLevel = 'all';
            this.level = 1;
        },
        onChangeCollectLevel(value) {
            if (value == 'all') {
                this.level = 1;
            }
        },
        // 工具栏点击回调
        onToolClick(item) {
            // 添加对象
            if (item.key === "create") {
                
                this.modalAction = 'addObject';
                this.globalSearchVisible = true;

            } else if (item.key === "export") {
                if (!this.tableData.length) return;
                
                exportFile(
                    `${Jw.gateway}/${Jw.baselineServer}/baseline/export?baselineOid=${this.$route.query.oid}`,
                    'POST',
                    {},
                    this.objectDetailsData.name,
                    '.xlsx'
                )
                .then(res => this.$success(this.$t('txt_export_success')));

                // let xhr = new XMLHttpRequest();
                // xhr.open('POST', , true);
                // xhr.setRequestHeader('accesstoken', getCookie('token'));
                // xhr.setRequestHeader('appName', 'pdm');
                // xhr.responseType = 'blob';
                // xhr.onload = () => {
                //     var type = xhr.getResponseHeader('Content-Type');
                //     var blob = new Blob([xhr.response]);
                //     var objectUrl = URL.createObjectURL(blob);
                //     var a = document.createElement('a');
                //     a.href = objectUrl;
                //     a.download = `${this.$route.query.name}.xlsx`;
                //     document.body.appendChild(a);
                //     a.click();
                //     a.remove(); 
                // };
                // xhr.onerror = () => this.$error('导出失败');
                // xhr.send();
            }
            else if (item.key == 'delete')
            {
                if (!this.selectedRows.length) return this.$warning(this.$t('txt_please_seleted_data'));
                
                this.$confirm({
                    title: this.$t('txt_comfrim_all'),
                    okText:this.$t('btn_ok'),
                    cancelText:this.$t('btn_cancel'),
                    onOk: () => {
                        batchDelete
                        .execute({
                            modelType: 'Baseline',
                            oid: this.$route.query.oid,
                            relationType: 'BASELINEMEMBER',
                            subOidList: this.selectedRows.map(v => v.oid),
                        })
                        .then((data) => {
                            this.tableLoading = false;
                            this.reFetchData();
                        })
                        .catch((err) => {
                            this.tableLoading = false;
                            this.$error(err.msg || this.$t("msg_failed"));
                        });
                    }
                });
            }
        },
        // 工具栏输入回调
        onToolInput({ key }, value) {
            if (key === "search") {
                this.searchKey = value;
                // console.log(value);
                this.delaySearch();
            }
        },

        
        // 添加基线对象
        addObjectOk (selectedRows) {
            console.log(selectedRows)
            if (!selectedRows.length) return this.$warning(this.$t('txt_please_seleted_data'));

            const params = {
                isReplace: false,
                oid: this.$route.query.oid,
                secObjList: selectedRows.map(v => ({
                    name: v.name,
                    number: v.number,
                    status: v.lifecycleStatus,
                    displayVersion: v.displayVersion,
                    wideType: v.type,
                    sourceOid: v.oid,
                    modelDefinition: v.modelDefinition,
                    bizMasterType: v.masterType,
                    // containerName: this.$route.query.containerName,
                    subject: this.modalAction == 'addObject' || this.modalAction == 'collect', // （基线下）添加对象传 / 收集相关对象 true， 对象--收集bom 传 false
                    children: [],
                    lockedTime: v.lockedTime,
                    lockOwnerAccount: v.lockOwnerAccount,
                    lockOwnerOid: v.lockOwnerOid,
                    lockSourceOid: v.lockSourceOid,
                    modelIcon: v.modelIcon,
                })),
            }
            
            this.okBtnLoading = true;
            // checkBeforeLink
            //     .execute(params)
            //     .then((data) => {

                    batchLink
                    .execute(params)
                    .then((data) => {
                        this.tableLoading = false;
                        this.$success(this.$t('txt_add_success'));
                        this.$refs.add_obj_model_ref && this.$refs.add_obj_model_ref.cancelFn();
                        this.addObjectModalVisible = false;
                        this.globalSearchVisible = false;
                        this.okBtnLoading = false;
                        this.reFetchData();
                    })
                // })
                .catch((err) => {
                    this.tableLoading = false;
                    this.okBtnLoading = false;
                    this.$error(err.msg || this.$t("msg_failed"));
                });
        },
        addObjectCancel () {
            this.addObjectModalVisible = false;
        },
    }
}
</script>