
<template>
    <div class="all-background baseline-info-page">
        <div class="info-head" :style="{width: '60%', margin: '16px auto', textAlign: 'right'}">
            <a-button
                :loading="loading" 
                v-show="rootInfo.layoutName==='update'" 
                @click="onSave"
            >{{$t('btn_save')}}</a-button>
        </div>
        <jw-layout-builder
            ref="ref_appBuilder"
            type="Model"
            :layoutName="rootInfo.layoutName || 'show'"
            :modelName="'Baseline'"
            :instanceData="instanceData"
        >
            <template v-for="(node, slotName) in $scopedSlots"
                :slot="slotName"
                slot-scope="slotData"
            >
                <slot :name="slotName" v-bind="slotData"></slot>
            </template>
        </jw-layout-builder>
    </div>
</template>

<script>
import { jwLayoutBuilder } from 'jw_frame';
import ModelFactory from 'jw_apis/model-factory';


// 更新基线
const updateBaseline = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/model/update`,
    method: 'post',
});

export default {
    props: ['objectDetailsData'],
    components: {
        jwLayoutBuilder,
    },

    inject: ['rootInfo'],

    data() {
        return {
            inner_layoutName: this.$route.query.actionType || 'show',
            instanceData: {},
            loading: false,
        }
    },

    watch: {
        $route (to, from) {
            if (to.query.actionType != from.query.actionType)
                this.inner_layoutName = to.query.actionType;
        },
        objectDetailsData: {
            immediate: true,
            handler(val) {
                this.instanceData = val;
            },
        }
    },
    
    created () {
        // this.getObjectDetails();
    },

    methods: {
        onSave () {
            let appBuilder = this.$refs.ref_appBuilder;
            appBuilder &&
                appBuilder
                .validate()
                .then(() => {
                    let value = appBuilder.getValue();
                    
                    this.loading = true;
                    updateBaseline.execute(
                        value
                    ).then((res) => {
                        this.$success(this.$t('msg_update_success'));
                        this.$emit('rerender');
                        this.loading = false;

                    }).catch((err) => {
                        this.$error(err.msg);
                        this.loading = false;
                    });
                })
        }
    }
}
</script>
<style lang="less" scoped>
    .baseline-info-page {
        overflow-y: auto;
    }
  .layout-grid-generated {
    width: 60%;
    margin: 0 auto;
  }
</style>