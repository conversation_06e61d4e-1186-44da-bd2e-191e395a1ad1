
<template>
  <div>
    <a-dropdown
      :trigger="trigger"
      placement="bottomRight"
      style="width: 200px"
      overlayClassName="operation-dropdown-overlay"
      @visibleChange="dropdownVisibleChange"
    >
      <slot>
        <span class="jwi-iconellipsis" :title="$t('btn_delete')+$t('btn_more')" style="cursor: pointer"></span>
      </slot>

      <a-menu slot="overlay" @click="({ key }) => rowOperation(key)">
        <a-spin
          v-if="currentRow.loading_status == 'loading'"
          style="margin: 20px 65px"
        />
        <a-menu-item
          v-else-if="currentRow.loading_status == 'failed'"
          style="margin: 20px auto"
          >{{$t('txt_get_failure')}}</a-menu-item
        >
        <template v-else v-for="(item, index) in currentRow.operationList">
          <!-- <a-menu-divider
            v-if="item.code == 'delete'"
            :key="index"
            :disabled="item.status === 'disable'"
          /> -->
          <a-menu-item :key="item.code" :disabled="item.status === 'disable'">
            <span :class="item.icon" style="margin-right: 8px"></span
            >{{ $t(item.internationalizationKey) }}
          </a-menu-item>
        </template>
      </a-menu>
    </a-dropdown>
    <!-- 设置状态 -->
    <base-color-modal
      :title="operationTitle"
      :width="992"
      :visible.sync="operationVisible"
      :ok-btn-loading="okBtnLoading"
      dialog-class="operation-modal"
      :ok-text="$t('btn_ok')+`${operationTitle == $t('txt_set_status') ? $t('btn_set_s'): operationTitle}`"
      @ok="modalOk"
      @cancel="operationCancel"
      :body-style="{ height: '545px', overflowY: 'scroll' }"
    >
      <!-- 设置状态 -->
      <div 
        v-if="modalAction == 'setStatus'" 
        class="setStatus-container"
        :style="{ width: statusList.length * 120 > 992 - 24 * 2 ? statusList.length * 120 + 'px' : '100%' }"
      >
        <div
          v-for="(item, index) in statusList"
          :key="item.code"
          :class="[
            'item-box',
            {
              current: item.code == currentRecord.lifecycleStatus,
              active: currentRow.new_lifecycleStatus == item.code,
            },
          ]"
          @click="$set(currentRow, 'new_lifecycleStatus', item.code)"
        >
          <div class="circle">
            <div v-if="index != 0" class="line left-arrow"></div>
            <div v-if="index != statusList.length - 1" class="line right-arrow"></div>
          </div>
          <div class="status">
            <a-tag style="margin: 0 auto">{{ item.displayName }}</a-tag>
          </div>
          <!-- 当前状态 -->
          <div v-if="item.code == currentRecord.lifecycleStatus" class="text">
            <span
              class="jwi-iconflag"
              style="color: #f6445a; margin-right: 4px"
            ></span>
           {{$t('txt_current_state')}} 
          </div>
          <div v-else class="text">{{$t('txt_set_current')}}</div>
        </div>
      </div>
    </base-color-modal>
    <!-- 对比-选择基线弹窗 -->
    <form-modal
      :width="512"
      :title="$t('txt_seletct_baseline')"
      confirm-btn-position="left"
      :data-source="formModalData"
      :visible.sync="formModalVisible"
      :body-style="{
          height: '388px',
      }"
      @confirm="formModalConfirm"
      @cancelBack="formModalCancel"
    >
      <template #currentBaseline='{ item }'>
          <div 
              :title="`${item.number}，${item.name}，${item.detailType == 'null' ? '' : item.detailType }`"
              :style="{
                  'height': '40px', 
                  'line-height': '40px',
                  'background': 'rgba(30,32,42,0.04)',
                  'border-radius': '4px',
                  'color': 'rgba(30,32,42,0.65)',
                  'white-space': 'nowrap',
                  'text-overflow': 'ellipsis',
                  'overflow': 'hidden'
              }"
          ><jwIcon :type='item.modelIcon' style="margin: 0 10px;" />
          {{ item.number }}，
          {{ item.name }}，
          {{ item.detailType == 'null' ? '' : item.detailType }}</div>
          <a-divider />
      </template>
  </form-modal>
    <!-- 发起流程 -->
    <start-process-modal
        :visible="processVisible"
        :pageCode="pageCode"
        :detailInfo="currentRow"
        @close="onCloseProcessModal"
        @getTableData="noticeParent('startProcess')"
    ></start-process-modal>
    <!-- 修改所有者 -->
    <jw-user-modal-v2
        ref="user-modal"
        :isCheckbox="false"
    />
    <!-- 另存 -->
    <base-color-modal
      :title="$t('txt_baseline_save_as')"
      :width="612"
      :visible.sync="saveAsVisible"
      :ok-btn-loading="okBtnLoading"
      @ok="onSaveAs"
      @cancel="onCloseSaveAsModal"
    >
      <jw-layout-builder
        ref="ref_appBuilder_saveAs"
        type="Model"
        :layoutName="'saveAs'"
        :modelName="'Baseline'"
        :instanceData="instanceData"
      >
        <template slot="saveAsSlot">
          <a-row :gutter="10">
            <a-col :span="12">
                <a-form-model-item :label="$t('txt_save_as_baseline_list')" required>
                    <a-radio-group v-model.trim="saveAsForm.needBaselineItem">
                      <a-radio :value="true"> {{$t('txt_yes')}} </a-radio>
                      <a-radio :value="false"> {{$t('txt_no')}} </a-radio>
                    </a-radio-group>
                </a-form-model-item>
            </a-col>
            <a-col :span="12" v-if="saveAsForm.needBaselineItem">
                <a-form-model-item :label="$t('txt_latest_version')" required>
                    <a-radio-group v-model.trim="saveAsForm.needLatestVersion">
                      <a-radio :value="true"> {{$t('txt_yes')}} </a-radio>
                      <a-radio :value="false"> {{$t('txt_no')}} </a-radio>
                    </a-radio-group>
                </a-form-model-item>
            </a-col>
          </a-row>
        </template>
      </jw-layout-builder>
    </base-color-modal>
  </div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import baseColorModal from "components/base-color-modal.vue";
import { formatDate } from "jw_utils/moment-date";
import formModal from "components/form-modal.vue";
import startProcessModal from '/views/product-content/process-manage/start-process-modal';
import { jwIcon, jwUserModalV2, jwLayoutBuilder } from "jw_frame";
import { getCookie } from "jw_utils/cookie"
import { findItem } from 'utils/util';
import { setOwner } from "apis/baseapi";

const SERVER_DOMAIN = {
  Part: `${Jw.gateway}/${Jw.partBomMicroServer}/part`,
  Document: `${Jw.gateway}/${Jw.docMicroServer}/document`,
};


// 查询容器下的基线列表
const getBaselineList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.baselineServer}/baseline/model/query`,
    method: 'post',
});

// 获取该条数据可操作下拉列表
const getDropdownList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
  method: "post",
});
// 查询可设置的状态
const searchStatusList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/lifecycle/findByOid`,
  method: "get",
});
// 设置状态
const part_setStatus = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.baselineServer}/baseline/model/setStatus`,
  method: "post",
});

// 基线另存
const saveAsBaseline = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.baselineServer}/baseline/model/saveAs`,
  method: 'post',
});

// 文件夹目录
const fetchfolderTree = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/folder/searchTree`,
  method: "get",
});
export default {
  name: "operation-dropdown",
  components: {
    baseColorModal,
    formModal,
    startProcessModal,
    jwIcon,
    jwUserModalV2,
    jwLayoutBuilder,
  },
  props: {
    currentRecord: Object,
    permissionViewCode: {
      type: String,
    },
    trigger: {
      type: Array,
      default: () => ["click"],
    },
    // （产品/资源)容器 oid
    containerOid: {
        type: String,
    },
    // （产品/资源)容器 modelDefinition: 'ProductContainer'/'ResourceContainer'
    containerModel: {
        type: String,
    },
  },
  data() {
    return {
      currentRow: {},
      statusList: [],
      operationVisible: false,
      modalAction: "",
      modalTableData: [],
      modalSelectedRows: [],
      selectedTreeNode: {}, // 移动/另存-位置 选中的树节点
      
      okBtnLoading: false,
      formModalVisible: false,
      formModalData: [
          // {
          //     label: '当前基线',
          //     prop: 'currentBaseline',
          //     block: true,
          //     required: false,
          //     slotData: {},
          //     scopedSlots: {
          //         default: 'currentBaseline'
          //     }
          // },
          // {
          //   label: '上下文',
          //   type: "tree",
          //   placeholder: '请先选择上下文',
          //   prop: "context",
          //   treeData: this.getFoldertree(),
          //   block: true,
          //   required: false,
          //   showSearch: true,
          //   treeNodeFilterProp: "title",
          //   filterTreeNode: (value, treeNode, field = 'name') => {
          //       const title = treeNode.componentOptions.propsData.dataRef[field].toLowerCase();
          //       console.log(title, value)
          //       return title.indexOf((value || '').trim().toLowerCase()) > -1
          //   },
          //   change: (newVal, oldVal) => {
          //   const containerOid = (findItem(this.formModalData[0].treeData, newVal) || {}).containerOid;

          //       this.$set(this.formModalData, 0, { ...this.formModalData[0], value: newVal });
          //       this.$set(this.formModalData, 1, { 
          //       ...this.formModalData[1], 
          //       disabled: !newVal,
          //       defaultOptions: [], // 切换上下文时，清空之前搜索的下拉基线列表
          //       searchParams: {
          //           ...this.formModalData[1].searchParams,
          //           containerOid, // 设置 基线搜索 -- 产品容器参数
          //       }
          //       });
          //   },
          //   dropdownStyle: {
          //       height: '276px',
          //       overflowY: 'scroll',
          //   }
          // },
          // {
          //     label: '',
          //     prop: 'targetOid',
          //     type: 'select',
          //     placeholder: '请输入关键词搜索',
          //     block: true,
          //     fetch: getBaselineList,
          //     showSearch: true,
          //     searchParams: {
          //         index: 1,
          //         size: 10,
          //         // searchKey: '',
          //         containerOid: this.$route.query.id,
          //     },
          //     // defaultOptions: [],
          //     dropdownStyle: {
          //         height: '276px',
          //         overflowY: 'scroll',
          //     }
          // },
      ],
      processVisible: false,
      pageCode: 'objectProcess',

      saveAsVisible: false,
      // 基线另存默认数据
      instanceData: {},
      saveAsForm: {
        needBaselineItem: false,
        needLatestVersion: false,
      },
    };
  },
  watch: {
      currentRecord: {
        immediate: true,
        handler(val) {
            this.currentRow = _.cloneDeep(val);
        },
    }
  },
  computed: {
    modalTitle() {
      switch (this.modalAction) {
        case "edit":
        case "checkout":
          return this.$t('txt_check_out');
        case "move":
          return this.$t('txt_mobile');
        case "rename":
          return this.$t('txt_rename');
        case "saveAs":
          return this.$t('txt_save');

        default:
          return this.$t('txt_create_container');
      }
    },
    modalOkText() {
      switch (this.modalAction) {
        case "checkout":
          return this.$t('txt_comfrim_out');
        case "saveAs":
          return this.$t('txt_comfrim_save');

        default:
          return this.$t('btn_done');
      }
    },

    operationTitle() {
      switch (this.modalAction) {
        case "setStatus":
          return this.$t('txt_set_status');
        case "revise":
          return this.$t('txt_revision');
        case "delete":
          return this.$t('txt_delete');

        default:
          break;
      }
    },
    modalColumns() {
      return [
        {
          field: "name",
          title: this.$t('txt_plan_name'),
        },
        {
          field: "number",
          title: this.$t('txt_number_of'),
          sortable: true, // 开启排序
        },
        {
          field: "lifecycleStatus",
          title: this.$t('txt_status'),
          sortable: true, // 开启排序
          slots: {
            default: "status",
          },
        },
        {
          field: "displayVersion",
          title: this.$t('txt_version'),
          sortable: true, // 开启排序
        },
        {
          field: "updateDate",
          title: this.$t('txt_update_date'),
          sortable: true, // 开启排序
          slots: {
            default: "updateDate",
          },
        },
      ];
    },
  },
  created(){
    // this.getFoldertree()
  },
  methods: {
    //获取选择基线
    getFoldertree(){
      let { query } = this.$route;
      let folderTreeParams = {
          // containerOid: query.id || this.productContainerOid,
          containerModel: this.containerModel || query.modelDefinition,
      };

      fetchfolderTree
      .execute(folderTreeParams)
      .then((data) => {
        this.formModalData = [
          {
            label: this.$t('txt_current_baseline'),
            prop: 'currentBaseline',
            block: true,
            required: false,
            slotData: this.currentRecord,
            scopedSlots: {
                default: 'currentBaseline'
            }
          },
          {
            label: this.$t('tabel_context'),
            type: "tree",
            placeholder: this.$t('msg_select'),
            prop: "context",
            treeData: this.deepData(data, true),
            block: true,
            required: false,
            showSearch: true,
            treeNodeFilterProp: "title",
            filterTreeNode: (value, treeNode, field = 'name') => {
                const title = treeNode.componentOptions.propsData.dataRef[field].toLowerCase();
                console.log(title, value)
                return title.indexOf((value || '').trim().toLowerCase()) > -1
            },
            change: (newVal, oldVal) => {
                const containerOid = (findItem(this.formModalData[1].treeData, newVal) || {}).containerOid;

                this.$set(this.formModalData, 1, { ...this.formModalData[1], value: newVal });
                this.$set(this.formModalData, 2, { 
                  ...this.formModalData[2], 
                  disabled: !newVal,
                  defaultOptions: [], // 切换上下文时，清空之前搜索的下拉基线列表
                  searchParams: {
                      ...this.formModalData[2].searchParams,
                      containerOid, // 设置 基线搜索 -- 产品容器参数
                  }
                });
            },
            dropdownStyle: {
                height: '276px',
                overflowY: 'scroll',
            }
          },
          {
              label: '',
              prop: 'targetOid',
              type: 'select',
              placeholder:this.$t('search_text') ,
              block: true,
              disabled: true,
              fetch: getBaselineList,
              value: undefined,
              showSearch: true,
              showIcon: true,
              searchParams: {
                  index: 1,
                  size: 10,
                  // searchKey: '',
                  containerOid: this.containerOid || this.$route.query.oid,
              },
              // defaultOptions: [],
              dropdownStyle: {
                  height: '276px',
                  overflowY: 'scroll',
              }
          },
        ];
        
        this.formModalVisible = true;
      });
    },

    formatDateFn(date) {
      return formatDate(date);
    },
    // 获取 currentRecord  part 操作下拉列表
    dropdownVisibleChange(visible) {
      const row = this.currentRecord;
      
      if (visible 
        // && !row.operationList
        ) {
        this.$set(this.currentRow, "loading_status", "loading");
        getDropdownList
          .execute({
            viewCode: this.permissionViewCode,
            objectOid: row.oid,
          })
          .then((data) => {
            this.$set(this.currentRow, "operationList", data);
            this.$set(this.currentRow, "loading_status", "done");
          })
          .catch((err) => {
            this.$error(err.msg || this.$t("msg_failed"));
            this.$set(this.currentRow, "loading_status", "failed");
          });
      }
    },

    noticeParent(key, data) {
      this.$emit("complete", key, data);
    },

    // 列表操作
    rowOperation(key) {
      const { modelDefinition, oid, type, name, masterType, lifecycleOid } = this.currentRecord;

        if (key == 'setStatus')
        {
            searchStatusList
              .execute({
                oid: lifecycleOid,
              })
              .then((res) => {
                this.statusList = (res || { context: {} }).context.states || [];
                this.operationVisible = true;
              })
              .catch((err) => {
                console.error(err);
                this.$error(err.msg || this.$t("msg_failed"));
              });
        }
        else if (key == 'contrast')
        {
          this.getFoldertree();
        }
        else if (key == 'saveAs')
        {
          this.instanceData = { ...this.currentRecord };
          this.saveAsVisible = true;
        }
        else if (key == 'export')
        {
          let xhr = new XMLHttpRequest();
          xhr.open('POST', `${Jw.gateway}/${Jw.baselineServer}/baseline/export?baselineOid=${oid}`, true);
          xhr.setRequestHeader('accesstoken', getCookie('token'));
          xhr.setRequestHeader('tenantAlias', getCookie("tenantAlias"));
          xhr.setRequestHeader('tenantOid', getCookie("tenantOid"));
          xhr.responseType = 'blob';
          xhr.onload = () => {
              var type = xhr.getResponseHeader('Content-Type');
              var blob = new Blob([xhr.response]);
              var objectUrl = URL.createObjectURL(blob);
              var a = document.createElement('a');
              a.href = objectUrl;
              a.download = `${name}.xlsx`;
              document.body.appendChild(a);
              a.click();
              a.remove(); 
          };
          xhr.onerror = () => this.$error(this.$t('txt_export_failure'));
          xhr.send();
        } else if (key == 'startProcess') {
            this.processVisible = true;
        } else if (key == 'updateOwner') {
            this.$refs['user-modal'].show({
                    type: 'User',
                }).then((res) => {
                    setOwner.execute({
                        oid: oid,
                        type: type,
                        ownerAccount: res.account,   
                    }).then((res) => {
                        this.$success(this.$t('msg_success'));
                        this.noticeParent(key);
                    }).catch((err) => {
                        this.$error(err.msg);
                    });
                })
        }
        else
            this.noticeParent(key, this.currentRecord);

      this.modalAction = key;
    },
    
    // 对比-确认
    formModalConfirm (model) {
        const sourceData = this.formModalData[0].slotData;
        // return
        this.$router.push({
            name: 'baseline-contrast',
            query: {
                ...this.$route.query,
                containerOid: this.containerOid || this.$route.query.oid,
                objectType: 'baseline',
                sourceOid: sourceData.oid,
                targetOid: model.targetOid,
            }
        })
    },
    formModalCancel () {
        this.formModalVisible = false;
    },

    operationCancel() {
        delete this.currentRow.new_lifecycleStatus;
      this.operationVisible = false;
    },
    // 设置状态 -- 弹窗确定
    modalOk() {
      const { modelDefinition, oid, type, detailType } =
        this.currentRecord;
        const {new_lifecycleStatus} = this.currentRow;

      switch (this.modalAction) {
        // 设置状态
        case "setStatus":
          {
            this.okBtnLoading = true;
            part_setStatus
              .execute({
                modelInfo: {
                  modelDefinition: detailType,
                  oid,
                  type,
                },
                status: new_lifecycleStatus,
              })
              .then((res) => {
                this.okBtnLoading = false;
                this.$success(this.$t('msg_success'));
                delete this.currentRow.new_lifecycleStatus;
                this.operationVisible = false;
                this.noticeParent(this.modalAction);
              })
              .catch((err) => {
                this.$error(err.msg || this.$t("msg_failed"));
                this.okBtnLoading = false;
              });
          }
          break;

        default:
          break;
      }
    },
    onCloseProcessModal() {
        this.processVisible = false;
    },
    deepData(data, onlyDirectChildren = false) {
        const scopedSlots = { title: "folderIconTitle" };

        let arr = [];
        const loop = tree => {
            tree.map((item, index) => {
            // item.title = item.name;
            item.key = item.oid;
            item.value = item.oid;
            item.scopedSlots = scopedSlots;

            // 只获取第一层
            if (onlyDirectChildren) {
                const temp = {
                ...item,
                }
                delete temp.children;
                arr.push(temp);
                return;
            }

            if (item.children && item.children.length > 0) {
                item.children.map((item) => (item.childType = "child"));
                loop(item.children);
            }
            });
        }

        loop(data);
        
        return onlyDirectChildren ? arr : data;
    },
    onSaveAs() {
      let appBuilder = this.$refs.ref_appBuilder_saveAs;
      appBuilder
        .validate()
        .then(() => {
          this.okBtnLoading = true;
          let params = appBuilder.getValue();
          params.needBaselineItem = this.saveAsForm.needBaselineItem;
          params.needLatestVersion = this.saveAsForm.needLatestVersion;
          saveAsBaseline.execute(
              params
          ).then((res) => {
              this.okBtnLoading = false;
              this.saveAsVisible = false;
              this.$success(this.$t('msg_success'));
              this.noticeParent(this.modalAction);
          }).catch((err) => {
              this.$error(err.msg);
              this.okBtnLoading = false;
          });
        })
    },
    onCloseSaveAsModal() {
        this.saveAsVisible = false;
    },
  },
};
</script>

<style lang="less" scoped>
.operation-dropdown-overlay {
//   width: 150px;
}

.operation-modal {
  .setStatus-container {
    display: flex;
    justify-content: center;
    margin-top: 300px;

    .item-box {
      width: 120px;
      height: 142px;
      padding: 20px 12px 8px;
      border-radius: 5px;
      margin-right: 12px;
      cursor: pointer;
      transition: all 0.3s;
      flex-basis: 120px;
      border: 1px solid transparent;

      .circle {
        width: 16px;
        height: 16px;
        margin: 0 auto;
        border: 12px solid #a4c9fc;
        border-radius: 50%;
        box-sizing: content-box;
        position: relative;

        .line {
          position: absolute;
          top: 6px;
        }

        .left-arrow,
        .right-arrow {
          height: 1px;
          background: rgba(30, 32, 42, 0.15);
        }
        .left-arrow {
          width: 38px;
          left: -58px;

          &::after {
            content: "";
            display: block;
            position: absolute;
            right: -8px;
            top: -4px;
            width: 0;
            height: 0;
            border-top: 4px solid transparent;
            border-left: 8px solid rgba(30, 32, 42, 0.15);
            border-bottom: 4px solid transparent;
          }
        }
        .right-arrow {
          width: 42px;
          right: -58px;

          &::after {
            content: "";
            display: block;
            position: absolute;
            left: -8px;
            top: -4px;
            width: 8px;
            height: 8px;
            background: rgba(30, 32, 42, 0.15);
            border-radius: 50%;
          }
        }
      }
      .status {
        text-align: center;
        margin-top: 8px;
      }
      .text {
        text-align: center;
        opacity: 0;
        transition: all 0.3s;
      }

      &:not(.current) {
        .text {
          margin-top: 10px;
          height: 32px;
          line-height: 32px;
          background: #ffffff;
          border: 1px solid rgba(30, 32, 42, 0.15);
          border-radius: 4px;
        }
      }
      &:hover {
        background: rgba(30, 32, 42, 0.02);
        border-color: rgba(30, 32, 42, 0.15);

        .text {
          opacity: 1;
        }
      }
      &.current {
        background: #f0f7ff;
        border: 1px solid #a4c9fc;

        .text {
          opacity: 1;
          margin-top: 15px;
          height: 22px;
        }
      }
      &.active {
        background: rgba(30, 32, 42, 0.02);
        border-color: rgba(30, 32, 42, 0.15);

        .text {
          opacity: 1;
        }
      }
    }
  }
}
</style>