<template>
  <div class="main-content">
    <header>
      <div class="header-left">
        <a-breadcrumb separator=">">
          <a-breadcrumb-item>
            <a href="javascript:void(0)" @click="routerBack">
              {{
                $route.query.masterType === "ProductContainer"
                  ? "产品容器"
                  : "资源容器"
              }}
            </a>
          </a-breadcrumb-item>
          <a-breadcrumb-item>{{ productName }}</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </header>
    <jw-simple-tabs
      :value="tabValue"
      :options="tabOption"
      @change="tabChange"
    />
    <dz-content-manage v-if="tabValue === 'dz-product'" :territory="this.territory"/>
    <content-manage v-if="tabValue === 'product'" />
    <product-structure v-if="tabValue === 'structure'" />
    <baseline-manage v-if="tabValue === 'baseline'" />
    <change-manage v-if="tabValue === 'change'" />
    <process-manage v-if="tabValue === 'process'" />
    <delivery-list v-if="tabValue === 'delivery'" />
    <structure-browse
      v-if="tabValue === 'structure-browse'"
      :objectDetailsData="{}"
    />
    <delivery-document v-if="tabValue === 'document'" />
  </div>
</template>

<script>
import { jwSimpleTabs, jwPage } from "jw_frame"
import contentManage from "./content-manage/index.vue"
import dzContentManage from "./dz-content-manage/index.vue"
import productStructure from "./product-structure/index.vue"
import baselineManage from "./baseline-manage/index.vue"
import changeManage from "./change-manage/index.vue"
import processManage from "./process-manage/index.vue"
import deliveryList from "./delivery-list/index.vue"
import structureBrowse from "./structure-browse"
import { detailContainer } from "apis/resource-container"
import deliveryDocument from './delivery-document'

const TABSMAP = {
  ProductContainer: [
    "product",
    "structure-browse",
    "structure",
    "change",
    "baseline",
    "process",
    "delivery",
    "document"
  ],
  ResourceContainer: ["product", "change", "baseline", "process", "delivery","document"],
  electron: ["dz-product", "change", "baseline", "process", "delivery","document"],
}

export default {
  components: {
    jwSimpleTabs,
    contentManage,
    productStructure,
    baselineManage,
    changeManage,
    processManage,
    deliveryList,
    jwPage,
    structureBrowse,
    dzContentManage,
    deliveryDocument
  },
  inject: ["setBreadcrumb", "addBreadcrumb"],
  data() {
    this.iniTabOption = [
      { value: "dz-product", label: this.$t("内容管理") },
      { value: "product", label: this.$t("txt_content_management") },
      { value: "structure-browse", label: this.$t("txt_product_structure") },
      { value: "structure", label: this.$t("txt_product_config") },
      { value: "change", label: this.$t("txt_change_management") },
      { value: "baseline", label: this.$t("txt_baseline_management") },
      { value: "process", label: this.$t("txt_process_management") },
      { value: "delivery", label: this.$t("txt_delivery_list") },
      { value: "document", label: "交付文档报表" },
    ]
    return {
      // tab选项
      productName: "",
      tabValue: this.$route.query.tabActive,
      tabOption: [],
      territory:"",
    }
  },

  created() {
    this.getContainerDetail()
    // console.log("======获取objectOid参数=>",this.$router.query)
    // console.log("productContainerInfo",productContainerInfo.ProductContainer["instance-info"]);
    let masterType = this.$route.query.masterType
    let rowInfoStore = JSON.parse(sessionStorage.getItem("row-info-store"))
    this.productName =
      this.$route.query.containerName ||
      rowInfoStore[masterType]["instance-info"].name ||
      rowInfoStore[masterType]["instance-info"].containerName ||
      rowInfoStore[masterType]["instance-info"].bizName

    // 产品结构显示调整：产品容器下显示产品结构，资源容器下不显示
    // if (masterType === "ResourceContainer") {
    //   this.tabOption.splice(1, 1);
    // }
    this.initBreadcrumb()
    window.localStorage.setItem("index_", 1)
  },

  methods: {
    // 获取当前容器详情
    getContainerDetail() {
      let { oid } = this.$route.query

      detailContainer
        .execute({ oid })
        .then((data) => {
          let key = data.modelDefinition
          if (data.territory && data.territory == "electron") {
            key = data.territory
          }
          this.initTabs(key)
          this.territory = data.territory
        })
        .catch((err) => {
          this.$error(err.msg)
        })
    },
    initTabs(key) {
      let tabs = TABSMAP[key]
      let masterType = this.$route.query.masterType
      if (masterType === "ResourceContainer") {
        this.iniTabOption = this.iniTabOption.filter(
          (item) => item.value !== "delivery"
        )
        this.tabOption = this.iniTabOption.filter((item) => {
          return tabs.includes(item.value)
        })
      } else {
        this.tabOption = this.iniTabOption.filter((item) => {
          return tabs.includes(item.value)
        })
      }
      if (!tabs.includes(this.tabValue)) {
        this.tabValue = this.tabOption[0].value
      }
    },
    // 使用自定义页面标题 设置为空，防止公共部分影响
    initBreadcrumb() {
      let breadcrumbData = []
      this.setBreadcrumb(breadcrumbData)
    },
    tabChange(option) {
      let { value } = option
      this.tabValue = value
      let { query } = this.$route

      this.$router.replace({
        name: "product-content",
        path: "/detailPage/product-content",
        query: { ...query, tabActive: value },
      })
    },
    routerBack() {
      if (this.$route.query.masterType === "ProductContainer") {
        this.$router.push({ path: "/product" })
      } else {
        this.$router.push({ path: "/resource-container" })
      }
    },
  },
}
</script>

<style lang="less" scoped>
// tab页签样式
.wrap-class {
  background-color: #fff;
  overflow: initial;

  /deep/ .item-class {
    margin: 0 20px 0 0;
    padding: 4px 0;
    color: rgba(30, 32, 42, 0.65);
  }

  /deep/ .item-class-active {
    font-size: 14px;
    font-weight: 500;
    color: rgba(30, 32, 42, 0.85);
  }
}

.wrap-class + div {
  height: 20px;
  flex-grow: 1;
}

.main-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

header {
  display: flex;
  justify-content: space-between;
  height: 60px;
  background-color: #fff;
  align-items: center;
  padding: 0 110px 0 24px;

  > .header-left {
    font-size: 20px;
    display: flex;

    a {
      color: #40a9ff;
    }
  }
}
</style>
