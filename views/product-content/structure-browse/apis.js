

// import Vue from 'vue'
import ModelFactory from "jw_apis/model-factory"

// import uuidv1 from "uuid/v1";
import { v4 as uuidv4 } from 'uuid'


/**
 * 获取类型
 * @param {*} params 
 * @returns 
 */
 const getFuzzyByModelDefinition = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/fuzzyByModelDefinition`,
        method: "GET",
    }).execute(params)
}

/**
 * 获取类型
 * @param {*} params {containerOid, searchKey}
 * @returns  
 */
 const fuzzyProductTopModelInstance = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/fuzzyProductTopModelInstance`,
        method: "GET",
    }).execute(params)
}

/**
 * 查询构型
 * @param {*} params 
 * @returns 
 */
const getBuzzyUseTreeByEffectivity = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/fuzzyUseTree/page/byEffectivity`,
        method: "POST",
    }).execute(params)
}

export {
    fuzzyProductTopModelInstance,
    getBuzzyUseTreeByEffectivity,
}