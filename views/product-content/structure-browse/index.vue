<template>
  <div>
    <div class="all-background structure-browse">
      <div class="form-wrapper">
        <a-form layout="inline" ref="formRef" :model="formState" v-bind="formItemLayoutWithOutLabel">
          <a-form-item :label="$t('txt_model_number')" name="oid">
            <a-select v-model.trim="formState.oid" :placeholder="$t('txt_model_number')" style="width: 200px;"
              :options="modelList.map(item => ({ value: item.oid, label: item.name }))" @change="onModelChange">
            </a-select>
          </a-form-item>
          <a-form-item :label="$t('txt_number_of')" name="numberLike">
            <a-input v-model.trim="formState.numberLike" :placeholder="$t('txt_number_of')" />
          </a-form-item>
          <template v-for="(field, index) in fieldList">
            <a-form-item v-if="field.effectType === 'numberRange'" :label="field.name"
              :name="['fieldList', index, 'value']" :rules="{
              }" :key="'number' + index">
              <a-input-number v-model.trim="field.value" :placeholder="field.name" style="width: 200px;" />
            </a-form-item>
            <a-form-item v-if="field.effectType === 'text'" :label="field.name" :name="['fieldList', index, 'value']"
              :rules="{
              }" :key="'text' + index">
              <a-input v-model.trim="field.value" :placeholder="field.name" style="width: 200px;" />
            </a-form-item>
            <a-form-item v-if="field.effectType === 'timeInterval'" :label="field.name"
              :name="['fieldList', index, 'value']" :rules="{
              }" :key="'time' + index">
              <a-date-picker v-model.trim="field.value" style="width: 200px;" :placeholder="field.name" />
            </a-form-item>
          </template>

          <a-form-item>
            <a-button type="primary" :disabled="canResearch" @click="onSubmit">{{ $t('btn_search') }}</a-button>
          </a-form-item>
        </a-form>
      </div>
      <div class="content-wrapper">
        <div class="left-wrapper">

          <vxe-toolbar ref="xToolbar1" custom zoom :tools="[
            { code: 'myPrint', name: $t('btn_export') }
          ]" @tool-click="onToolClick">
            <template #buttons>
              <a-radio-group v-model.trim="expandLevel">
                <a-radio-button :value="null">ALL</a-radio-button>
                <a-radio-button :value="1">1</a-radio-button>
                <a-radio-button :value="2">2</a-radio-button>
                <a-radio-button :value="3">3</a-radio-button>
                <a-radio-button :value="4">4</a-radio-button>
              </a-radio-group>
            </template>
          </vxe-toolbar>
          <div class="table-wrapper">
            <div class="adjust" id="table-container">
              <vxe-table v-if="fieldListHasFetched" ref="xTable" class="roll-tree-table" :height="tableHeight"
                :loading="loading" :scroll-y="{ enabled: true, }"
                :row-config="{ isCurrent: true, keyField: 'reluuid', isHover: true, height: rowHeight }"
                :menuConfig="menuConfig" @cell-menu="onCellMenuClick" @menu-click="onContextMenuClick"
                @current-change="onCurrentChange">
                <vxe-column type="seq" width="60" :title="$t('txt_line_number')" show-overflow></vxe-column>
                <vxe-column field="thumbnailOid" width="36" show-overflow class-name="thumb-wrapper">
                  <template #default="{ row }">
                    <cad-thumbnail v-if="row.thumbnailOid" :currentRow="row"
                      :style="{ height: '36px', width: '36px' }"></cad-thumbnail>
                  </template>
                </vxe-column>
                <vxe-column field="number" :title="$t('txt_number_of')" min-width="300" :show-overflow="true">
                  <template #default="{ row, rowIndex }">
                    <!-- 线条 -->
                    <div class="tree-area">
                      <div class="area-line-tree" :style="{
                        left: 3 + row.leave * 15 + 'px',
                        height:
                          (row.preChildCount ? row.preChildCount + 1 : 1) * lineHeight +
                          'px',
                        top:
                          -(
                            (row.preChildCount ? row.preChildCount + 1 : 1) *
                            lineHeight -
                            lineHeight / 2
                          ) + 'px',
                        borderWidth: `0 0 ${!row.treestate && rowIndex === 0 ? 0 : '1px'
                          } ${rowIndex === 0 ? 0 : '1px'}`,
                      }"></div>
                    </div>
                    <!-- 显示内容 -->
                    <div class="tree-node-wrapper" :style="{ paddingLeft: (row.leave - 1) * 15 + 'px' }">
                      <div class="btn-wrapper" @click="onExpand(row, rowIndex)">
                        <jwIcon v-if="row.treestate" :type="row.treestate"></jwIcon>
                        <div v-else class="blankarea"></div>
                      </div>
                      <div class="table-number-name-wrap">
                        <div class="name-con">
                          <a-tooltip>
                            <template slot="title">{{ row.lockOwnerAccount }}</template>
                            <jw-icon v-if="row.lockOwnerOid" :type="row.lockOwnerOid === userInfo.oid
                                ? '#jwi-beiwojianchu'
                                : '#jwi-bierenjianchu'
                              " />
                          </a-tooltip>
                          <jwIcon :type="row.modelIcon"></jwIcon>
                          <span class="name-item">{{ row.number }}</span>
                          <a-tooltip :title="$t('txt_replacement_parts')">
                            <jw-icon v-if="row.substitutedFlag" type="#jwi-tidaijian" />
                          </a-tooltip>
                        </div>
                        <div v-if="$refs['childDetail'] && $refs['childDetail'].activeTab === 'visualization'" class="view-btn">
                          <a-tooltip :title="$t('txt_hidden')" v-if="row.show3d">
                            <span @click.stop="toggle3dx(row)">
                              <jw-icon type="jwi-invisible"></jw-icon>
                            </span>
                          </a-tooltip>
                          <a-tooltip :title="$t('txt_hidden')" v-else>
                            <span @click.stop="toggle3dx(row)">
                              <jw-icon type="jwi-visible"></jw-icon>
                            </span>
                          </a-tooltip>
                        </div>
                      </div>
                    </div>

                  </template>
                </vxe-column>
                <vxe-column field="name" :title="$t('txt_name')" width="100" show-overflow>
                  <template #default="{ row }">
                    {{ row.name }}
                  </template>
                </vxe-column>
                <vxe-column field="modelDefinition" :title="$t('txt_type')" width="100" show-overflow>
                  <template #default="{ row }">
                    {{ $t(row.modelDefinition) }}
                  </template>
                </vxe-column>
                <vxe-column field="lifecycleStatus" :title="$t('txt_plan_lifecycle')" width="100" show-overflow>
                  <template #default="{ row }">
                    {{ $t(row.lifecycleStatus) }}
                  </template>
                </vxe-column>
                <vxe-column field="displayVersion" :title="$t('txt_version')" width="100" show-overflow>
                  <template #default="{ row }">
                    {{ row.displayVersion }}
                  </template>
                </vxe-column>
                <vxe-column field="description" :title="$t('txt_description')" width="100" show-overflow>
                  <template #default="{ row }">
                    {{ row.description }}
                  </template>
                </vxe-column>
                <vxe-column field="responsible" :title="$t('txt_responsible')" width="100" show-overflow>
                  <template #default="{ row }">
                    {{ row.owner }}
                  </template>
                </vxe-column>
                <vxe-column field="genericType" :title="$t('text_genericType')" width="100" show-overflow>
                  <template #default="{ row }">
                    {{ row.genericType }}
                  </template>
                </vxe-column>
                <vxe-colgroup v-if="fieldList.length > 0" :title="$t('txt_effectivity_definition')" align="center">
                  <vxe-column v-for="(field, index) in fieldList" :field="field.effectType" :title="field.name"
                    width="100" show-overflow :key="'filed' + index">
                    <template #default="{ row }">
                      {{
                        (row.effectivities.find((item, index) => {
                          return item.name === field.name
                        }) || {})
                          .displayValue
                      }}
                    </template>
                  </vxe-column>
                </vxe-colgroup>
              </vxe-table>
            </div>
          </div>
        </div>
        <div class="right-wrapper">
          <div class="adjust" v-if="detailInfo.oid">
            <child-detail ref="childDetail" :visible="childVisible" :detailInfo="detailInfo" @onSearch="onSearch"
              @close="onCloseChildDrawer"></child-detail>
          </div>

        </div>
      </div>
    </div>

    <create-drawer ref="cerateDrawer" :objectDetailsData="objectDetailsData" @fetchTable="onAddPart"></create-drawer>
    <jw-search-engine-modal :title="$t('txt_adding_business_object')" :onlySearchObject="true"
      :visible.sync="jwSearchEngineModalVisible" :pageCode="pageCode" :model-list='[
        {
          name: $t("txt_part"),
          code: "PartIteration"
        }
      ]' @ok="onAddOk" />
    <part-move-modal :visible="partMoveModalVisible" :detailInfo="currentRow" :objectDetailsData="objectDetailsData"
      @getTableData="onSearch" @close="partMoveModalVisible = false"></part-move-modal>
    <import-modal :visible="visibleExport" :currentRow="currentRow" :currentTree="objectDetailsData"
      @close="visibleExport = false" @renderData="onSearch"></import-modal>

    <!-- checkin-modal -->
    <form-modal :width="512" :title="$t('txt_check_in')" confirm-btn-position="left" :data-source="formModalData"
      :visible.sync="formModalVisible" @confirm="formModalConfirm">

    </form-modal>
  </div>
</template>

<script>
import { v4 as uuidv4 } from 'uuid'
import { findByContainerOid } from 'apis/efffectivitydefinition'

import XEUtils from "xe-utils";
import { getCookie } from 'jw_utils/cookie';
import { fuzzyProductTopModelInstance, getBuzzyUseTreeByEffectivity } from './apis'
import {
  getRelationList,
  deleteUse,
  batchAddUse,
  addTo,
  replaceObj,
  partCheckout
} from '../../product-container/object-details/structure/apis'
import { jwIcon, jwSearchEngineModal } from 'jw_frame';

import formModal from "components/form-modal.vue";
import cadThumbnail from 'components/cad-thumbnail.vue';
import childDetail from '../../product-container/object-details/structure/child-detail';
import partMoveModal from '../../product-container/object-details/structure/part-move-modal';
import importModal from "../../product-container/object-details/structure/import-modal.vue";
import createDrawer from './create-drawer';

import {
  part_checkIn,
  part_checkOut,
  part_undocheckout,
} from 'apis/part'

const DEFAULT_LEVEL = 2;

function makeOperationList($t, template = 'tableMenu', disableMap = {}) {
  const baseOperation = {
    partDetails: { code: 'partDetails', name: $t('txt_detail'), disabled: true, visible: true },
    checkout: { code: 'checkout', name: $t('txt_check_out'), disabled: true, visible: true },
    checkin: { code: 'checkin', name: $t('txt_check_in'), disabled: true, visible: true },
    undoCheckOut: { code: 'undoCheckOut', name: $t('txt_undo_out'), disabled: true, visible: true },
    partEdit: { code: 'partEdit', name: $t('btn_edit'), disabled: true, visible: true },
    createPart: { code: 'createPart', name: $t('txt_add_part'), disabled: true, visible: true },
    createHasPart: { code: 'createHasPart', name: $t('txt_add_existing_part'), disabled: true, visible: true },

    remove: { code: 'remove', name: $t('txt_remove'), disabled: true, visible: true },
    replace: { code: 'replace', name: $t('txt_replacements'), disabled: true, visible: true },
    copyMove: { code: 'copyMove', name: $t('txt_add_to_bom'), disabled: true, visible: true },
    partMove: { code: 'partMove', name: $t('txt_mobile'), disabled: true, visible: true },
  }

  const templateConfig = {
    tableMenu: [
      ['partDetails', 'checkout', 'checkin', 'undoCheckOut', 'partEdit', 'createPart', 'createHasPart'],
      ['remove', 'replace', 'copyMove', 'partMove']],
    toolMenu: []
  }

  let list = templateConfig[template].map((codeList) => {
    return codeList.map((code, index) => {
      return {
        ...baseOperation[code],
        className: 'menu-option',
        prefixIcon: disableMap[code]?.icon,
        disabled: disableMap[code]?.status !== 'enable'
      }
    })
  })

  return list;
}
import tableTreeMix from './table-tree.mix';
export default {
  mixins: [tableTreeMix()],
  inject: ["setBreadcrumb", "addBreadcrumb"],
  components: {
    jwIcon,
    formModal,
    cadThumbnail,
    childDetail,
    createDrawer,
    jwSearchEngineModal,
    partMoveModal,
    importModal,
    // jwTableTreeScroller,
  },
  props: {
    objectDetailsData: {
      type: Object,
      default: {}
    }
  },
  data() {

    const formItemLayoutWithOutLabel = {
    };
    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
    };

    let ROW_HEIGHT = 40;
    let NUM_PAGE = 20; // Math.ceil(window.innerHeight / ROW_HEIGHT);
    let WINDOW_SIZE = NUM_PAGE * 2;

    return {
      userInfo: Jw.getUser(),
      // form
      formItemLayout,
      formItemLayoutWithOutLabel,
      modelList: [],
      fieldList: [], // 动态加载的有效性规则字段
      fieldListHasFetched: false, // 标识有效性字段是否加载了，有效性字段属于表头的一部分，等其加载完，才能执行表格和工具栏关联
      formState: {
        oid: '',
        numberLike: '',
      },

      tableHeight: 20,
      // table
      loading: false,
      expandLevel: DEFAULT_LEVEL, // 0: 全部; 1; 2; 3; 4;
      menuConfig: {
        body: {
          options: makeOperationList(this.$t)
        },
        trigger: 'cell',
        className: 'cell-menu-wrap',
      },

      // part预览抽屉
      childVisible: true,
      detailInfo: {}, // 保存选中行的信息

      currentRow: {},
      pageCode: '',
      jwSearchEngineModalVisible: false,

      visibleExport: false,

      partMoveModalVisible: false,


      formModalVisible: false,
      formModalData: [],


      // 分页代理状态
      tableSource: [],

      rowHeight: ROW_HEIGHT,
      numPage: NUM_PAGE,
      windowSize: WINDOW_SIZE,
      unfoldNodes: [],
      foldNodes: [],
      startIndex: 0, // 渲染的片段数据在总数据里的起始位置。

      scrollTop: 0,
      scrollLeft: 0,

      isTriggerFetchingTableData: false,
      isLoadEnd: false,
    };
  },
  methods: {
    onSubmit() { // 搜索
      this.onSearch();
    },
    onAddPart() {
      this.onSearch();
    },
    initTableScrollState() {
      this.tableSource = []

      this.unfoldNodes = []
      this.foldNodes = []
      this.startIndex = 0

      this.scrollTop = 0
      this.scrollLeft = 0

      this.isTriggerFetchingTableData = false
      this.isLoadEnd = false

    },

    onCurrentChange({ row }) {
      this.detailInfo = { ...row };
      this.setCurrentRow({ ...row })
    },
    onSearch(appendParams = {}) {
      this.loading = true;

      // 处理自动签出顶层节点，oid发生变化的场景
      // currentRow
      return fuzzyProductTopModelInstance({ containerOid: this.$route.query?.oid, searchKey: null })
        .then((list = []) => {

          // 先判断有没有变化
          let changedItem = list.find((item) => { return this.formState.oid === item.oid })
          if (!changedItem) {
            // 找出变化的
            changedItem = list.find((item, index) => {
              return this.modelList.findIndex((_item) => { return item.oid === _item.oid }) < 0
            })
            this.formState.oid = changedItem.oid;
            this.modelList = list;
          }


          let params = {
            maxLevel: null,// this.expandLevel,
            ...this.collectFormParams(),
            ...appendParams,
          }
          return getBuzzyUseTreeByEffectivity(params)
            .then((res = []) => {

              this.initTableData(res)
              if (res.length > 0) {
                setTimeout(() => {
                  // alert(this.$refs?.xTable?.getRowById(this.detailInfo.reluuid))
                  let row = this.$refs?.xTable?.getRowById(this.detailInfo.reluuid) || res[0]
                  this.setCurrentRow(row)
                  this.detailInfo = { ...row }
                })
              }


            }).catch((err) => {
              this.$error(err.msg || err.message);
            }).finally(() => {
              this.loading = false;
            })

        })



    },

    onModelChange(a, b) { // 模型选项改变时，清空其他搜索值
      this.fieldList.forEach((item, index) => {
        item.value = '';
      })
      this.formState.numberLike = '';
    },
    onToolClick(code, tool, $event) {
      let columns = this.$refs.xTable.getColumns()
      let headList = columns.filter((item) => {
        return !!item.title
      })
        .map((item) => {
          return [item.title]
        })

      let params = this.collectFormParams()
      params.headList = [["层级"], ["父项编码"], ["父项名称"]].concat(headList)

      const accesstoken = getCookie('token');
      fetch(`${Jw.gateway}/${Jw.partBomMicroServer}/part-export/export/configurationBom`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json;charset=utf8",
          appName: Jw.appName,
          accesstoken,
          tenantAlias: getCookie("tenantAlias"),
          tenantOid: getCookie("tenantOid"),
        },
        body: JSON.stringify(params),
      })
        .then((response) => {
          return response.blob();
        })
        .then((res) => {
          let url = window.URL.createObjectURL(
            new Blob([res], {
              type: "application/vnd.ms-excel",
            })
          );
          let link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", `产品结构导出.xlsx`);
          document.body.appendChild(link);
          link.click();
          this.$success(this.$t('txt_export_success'));
        }).catch((err) => {
          this.$error(err.msg);
        });


    },
    //切换显示隐藏3d
    toggle3dx(row) {
      //切换选中值
      this.onOpenChildDrawer(row)
      //切换状态
      this.$set(row, 'show3d', row.show3d === undefined ? true : !row.show3d)
      setTimeout(() => {
        this.$refs['childDetail'].toggle3dx()
      }, 0)
    },
    onOpenChildDrawer(row) { // 预览PART节点侧滑

      this.childVisible = true;
      this.detailInfo = { ...row };
    },
    onCloseChildDrawer() {
      this.childVisible = false;
      // this.resetDisableList();
    },
    // 添加已有PART
    onAddOk(selectedRows) {
      if (!this.pageCode) {
        if (selectedRows.length === 0) {
          this.$warning(this.$t('txt_please_seleted_data'));
          return;
        }
        let selected = [];
        selectedRows.forEach(item => {
          let temp = {
            fromOid: this.currentRow.oid,
            fromType: this.currentRow.type,
            toOid: item.oid,
            toType: item.type,
            quantity: 1,
          }
          selected.push(temp);
        })
        batchAddUse.execute(
          selected
        ).then((res) => {
          this.$success(this.$t('msg_save_success'));
          this.jwSearchEngineModalVisible = false;
        }).catch((err) => {
          this.$error(err.msg);
        });
      } else {
        if (selectedRows.length === 0) {
          this.$warning(this.$t('txt_seletct_target'));
          return;
        }
        let api, params;
        let clickOid, clickType;
        if (this.currentRow.opeKey === 'copyMove') {
          api = addTo;
          params = {
            partOid: this.currentRow.oid,
            targetOid: selectedRows[0].oid,
          }
          clickOid = selectedRows[0].oid;
          clickType = selectedRows[0].type;
        } else if (this.currentRow.opeKey === 'replace') {
          // let treeData = this.$refs.xTable.getTableData().fullData;
          // let parent = getParent(treeData, this.currentRow.id, 'id');
          let parent = this.getParentRow(this.currentRow.uuid)
          api = replaceObj;
          params = {
            newPartOid: selectedRows[0].oid,
            originalPartOid: this.currentRow.oid,
            parentOid: parent.oid,
          }
          clickOid = parent.oid;
          clickType = parent.type;
        }
        api.execute(
          params
        ).then((res) => {
          this.jwSearchEngineModalVisible = false;
          this.$success(this.$t('msg_success'));
        }).catch((err) => {
          this.$error(err.msg);
        });
      }
    },

    // 右击菜单
    onCellMenuClick({ row, $event, column }) {
      this.detailInfo = { ...row }
      this.setCurrentRow(row)
      if ($event.isMenuShow) return
      let parent = row?.use?.fromOid || row.oid
      let instance = getRelationList.execute({
        viewCode: 'PARTBOMINSTANCE',
        objectOid: row.oid,
        parentOid: parent,
      })
      let relation = getRelationList.execute({
        viewCode: 'PARTBOMRELATEION',
        objectOid: row.oid,
        parentOid: parent,
      })
      let operationStateMap = {}
      Promise.all([instance, relation]).then((values) => {
        values.forEach((itemList) => {
          itemList.forEach((item) => {
            operationStateMap[item.code] = item
          })
        })
        this.menuConfig.body.options = makeOperationList(this.$t, 'tableMenu', operationStateMap)
        $event.isMenuShow = true
        this.$refs.xTable.handleGlobalContextmenuEvent($event)
      })
        .catch((err) => {
          // this.$error(err.msg);
        });
    },
    onContextMenuClick({ row, menu }) {// 点击菜单选项
      this.currentRow = row;
      let {
        masterType,
        modelDefinition,
        oid,
        type,
      } = row
      switch (menu.code) {
        case 'partDetails': // 详情
          this.gotoDetails(row)
          break;
        case 'partEdit': // 编辑
          if (!row.lockOwnerOid) {
            partCheckout.execute({
              modelDefinition: row.modelDefinition,
              oid: row.oid,
              type: row.type,
            }).then((res) => {
              this.gotoDetails(res)
            }).catch((err) => {
              this.$error(err.msg);
            });
          } else {
            this.gotoDetails(row)
          }
          break;
        case 'createPart': // 添加part
          this.$refs.cerateDrawer.show({
            title: this.$t('txt_reate_part'),
            modelInfo: {
              layoutName: 'create',
              modelName: 'Part',
            },
            params: {
              targetOid: row.oid,
              targetType: row.type,
              url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/createThenUse`,
              locationInfo: {
                catalogOid: row.catalogOid,
                catalogType: row.catalogType,
                containerOid: row.containerOid,
                containerType: row.containerType,
                containerModelDefinition: row.containerModelDefinition,
              }
            },
          });
          break;
        case 'createHasPart': // 添加已有PART
          this.pageCode = '';
          this.jwSearchEngineModalVisible = true;
          break;
        case 'replace': // 替代
          this.pageCode = 'structurePage';
          this.currentRow.opeKey = 'replace';
          this.jwSearchEngineModalVisible = true;
          break;
        case 'checkin':
          this.formModalVisible = true
          this.formModalData = [
            {
              label: this.$t("txt_remark"),
              type: "textarea",
              prop: "lockNote",
              block: true,
              required: false,
            },
          ]
          break;
        case 'checkout':
          part_checkOut(masterType)
            .execute({
              modelDefinition,
              oid,
              type,
            })
            .then((data) => {
              this.$success(this.$t("msg_checkout_success"));
              // this.formModalVisible = false;
              // this.noticeParent(this.modalAction, data);
              // let ModeItem = this.modelList.find((item) => { return item.oid === row.oid })
              // if (ModeItem) {
              //   ModeItem.oid = data.oid
              // }
              // if (this.formState.oid === row.oid) {
              //   this.formState.oid = data.oid
              // }
              this.onSearch()
            })
            .catch((err) => {
              console.error(err);
              this.$error(this.$t("msg_checkout_failed"));
            });
          break;
        case 'undoCheckOut':
          part_undocheckout(masterType)
            .execute({
              modelDefinition,
              oid,
              type,
            })
            .then((data) => {
              this.$success(this.$t("msg_success"));
              // this.noticeParent(key, data);
              // let ModeItem = this.modelList.find((item) => { return item.oid === row.oid })
              // if (ModeItem) {
              //   ModeItem.oid = data.oid
              // }
              // if (this.formState.oid === row.oid) {
              //   this.formState.oid = data.oid
              // }
              this.onSearch()
            })
            .catch((err) => {
              console.error(err);
              this.$error(err.msg || this.$t("msg_failed"));
            });
          break;
        case 'partMove':
          this.currentRow = { ...row, rootOid: this.formState.oid };
          this.partMoveModalVisible = true;
          break;
        case 'copyMove':
          this.pageCode = 'structurePage';
          this.currentRow.opeKey = 'copyMove';
          this.jwSearchEngineModalVisible = true;
          break;
        case 'remove':
          this.$confirm({
            title: this.$t('txt_comfirm_remove'),
            okText: this.$t('btn_ok'),
            cancelText: this.$t('btn_cancel'),
            onOk: () => {
              return deleteUse.execute(
                row.use
              ).then((res) => {
                this.$success(this.$t('txt_remove_success'));
                this.onSearch()

              }).catch((err) => {
                if (err.msg) {
                  this.$error(err.msg);
                }
              });
            },
          });
          break;
        case 'export':
          // this.onExport(this.currentRow);
          break;
        case 'import':
          this.visibleExport = true;
          break;
        default:
          break;
      }
    },
    renderData(data, row) {// 通知页面刷新的？

    },
    formModalConfirm(model) {
      let {
        masterType,
        modelDefinition,
        oid,
        type,
      } = this.currentRow
      // 检入
      part_checkIn(masterType)
        .execute({
          modelDefinition,
          oid,
          type,
          ...model,
        })
        .then((data) => {
          this.$success(this.$t("txt_check_in_success"));
          this.formModalVisible = false;
          this.onSearch()
        })
        .catch((err) => {
          console.error(err);
          this.$error(err.msg || this.$t("msg_failed"));
        });

    },
    collectFormParams() {
      let name2Value = {}
      this.fieldList.forEach((item) => {
        if (item?.value) {
          name2Value[item.name] = item.value
        }
      })
      let params = { ...this.formState, name2Value }
      return params;
    },
    loadMethod({ row }) {
      let params = {
        ...this.collectFormParams(),
        oid: row.oid,
        maxLevel: 1,
        startNode: 0,
        number: 1000
      }
      return getBuzzyUseTreeByEffectivity(params)
        .then((res = []) => {
          if (res[0]?.children?.length > 0) {
            XEUtils.eachTree(res,
              (row, index, items, path, parent, nodes) => {
                row.id = uuidv4();
                row.hasChild = true;
              })
            return res[0].children
          } else {
            row.hasChild = false;
            this.$refs.xTable.loadRow(row)
          }

        })

    },

    gotoDetails(row) {
      Jw.jumpToDetail(row, {
        query: {
          oid: row.lockSourceOid || row.oid,
          type: row.type,
          tabActive: 'info'
        },
        blank: true
      });
      // this.$router.push({
      //   path: "/detailPage/object-details",
      //   query: {
      //     oid: row.oid,
      //     type: row.type,
      //     tabActive: 'info'
      //   },
      // });
    },
    getRowByLevel(fullData, byLevel) {
      let list = []
      const loop = (tree, level) => {
        tree.forEach(item => {
          list.push(item)
          if (level + 1 < byLevel && item.children) {
            loop(item.children, level + 1);
          }
        });
      };

      loop(fullData, 0);
      return list;
    }
  },
  watch: {
    // expandLevel(val) {
    //   // this.onSearch()
    // },
    jwSearchEngineModalVisible(value) {
      if (!value) {
        this.onSearch()
      }
    }
  },
  computed: {
    canResearch() {
      return !this.formState.oid
    }
  },
  created() {
    this.tableData = []

    let containerOid = this.$route.query?.oid


    findByContainerOid(containerOid).then((list) => {
      this.fieldList = list.map((item) => {
        return { ...item, value: '' }
      })
    })
      .then(() => {
        return fuzzyProductTopModelInstance({ containerOid, searchKey: null })
          .then((list) => {

            let oid = list[0]?.oid;
            this.formState.oid = oid;
            this.modelList = list;
            // this.modelListHasFetched = true;
            this.fieldListHasFetched = true;

            return oid
          })
          .then((oid) => {
            this.tableHeight = document.querySelector('#table-container').clientHeight;
            if (oid) {
              this.$nextTick(() => {
                // 手动将表格和工具栏进行关联 
                this.$refs?.xTable.connect(this.$refs.xToolbar1)

                this.onSearch({ oid })
              })
            }
          })
      }).catch(() => {

      })

  },
  mounted() {

  },
}
</script>
<style lang="less" scoped>
.detail-drawer-wrap {
  // display: none;
}

// .tree-node-wrapper {
//   display: flex;
//   position: relative;
//   z-index: 1;

//   .btn-wrapper {
//     width: 16px;
//     height: 16px;
//   }

//   span i {
//     &:before {
//       width: 10px;
//       height: 10px;
//       background-color: #fff;
//     }
//   }
// }

.line-wrapper {
  position: absolute;
  left: 18px;
  bottom: 50%;
  z-index: 0;

  .line {
    box-sizing: border-box;
    height: 36px;
    width: 8px;
    border-width: 0 0 1px 1px;
    border-style: dotted;
    border-color: #255ed7;

  }
}

.structure-browse {
  display: flex;
  flex-direction: column;

}

.form-wrapper {}

.content-wrapper {
  flex: 10px 1 0;
  display: flex;

  .left-wrapper {
    width: 50%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .table-wrapper {
      flex: 10px 1 0;
      position: relative;

      .adjust {
        position: absolute;
        width: 100%;
        height: 100%;
        box-sizing: border-box;
      }
    }
  }

  .right-wrapper {
    height: 100%;
    flex: 10px 1 1;

    position: relative;

    .adjust {
      position: absolute;
      width: 100%;
      height: 100%;
      padding: 50px 0 0 10px;
      box-sizing: border-box;

      .detail-drawer-wrap {
        border: 1px solid #dcdde3;
        box-sizing: border-box;
        height: 100%;

        /deep/ .ant-tabs-tab {
          margin-right: 20px !important;
        }

        /deep/ .detail-drawer-body-wrap {
          height: calc(~'100% - 44px');
          padding: 0 10px;
          overflow: hidden;
        }

        /deep/ .info-body {
          height: calc(~'100% - 48px');
        }
      }
    }
  }
}


/deep/.jwi-iconshouqi-2:before,
/deep/.jwi-iconzhankai-2:before {
  color: blue;
}

/deep/ .thumb-wrapper .vxe-cell {
  padding: 0;
}

/deep/ .vxe-table--render-default .vxe-cell {
  white-space: normal;
}

// /deep/ .vxe-body--row {
//   &:hover {
//     background-color: #f0f7ff;
//   }

//   &:nth-child(even) {
//     background-color: #fafafa;
//   }

// }

/deep/ .vxe-table.is--tree-line .vxe-body--column,
/deep/ .vxe-table.is--tree-line .vxe-header--column {
  border-right: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.table-number-name-wrap {
  display: flex;
  justify-content: space-between;
  cursor: pointer;

  .name-con {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    .name-item {
      color: #255ed7;
      cursor: pointer;
    }
  }

  .view-btn {
    position: absolute;
    right: 0;
  }
}

// tree table 

.roll-tree-table {
  .tree-column {
    /deep/ .vxe-cell {
      overflow: visible !important;
    }
  }
}

.tree-area {
  .area-line-tree {
    position: absolute;
    width: 17px;
    border-width: 0 0 1px 1px;
    border-color: #255ed7;
    border-style: dotted;
  }
}

.tree-node-wrapper {
  display: flex;
  position: relative;
  z-index: 1;

  span i {
    &:before {
      width: 10px;
      height: 10px;
      background-color: #fff;
    }
  }
}

.btn-wrapper {
  /deep/ i {
    color: blue;
  }
}

.blankarea {
  width: 16px;
  height: 16px;
}
</style>