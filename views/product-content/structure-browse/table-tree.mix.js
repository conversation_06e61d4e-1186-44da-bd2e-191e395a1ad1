

export default function (ref) {
  return {
    props: {
      lineHeight: {
        type: Number,
        default: 40,
      },
    },
    data() {
      return {
        //所有层级树
        listdata: [],
        //当前显示树
        nowlist: [],
        iconconfig: ['', 'jwi-iconshouqi-2', 'jwi-iconzhankai-2'],
        userInfo: Jw.getUser(),
        checkrow: null,
        //选中状态的 reluuid字段
        expandnodeoids: [],
      }
    },
    watch: {
      expandLevel: function (val) {
        this.resetlist(this.listdata)
        this.replaceLeave()
      },
    },
    methods: {
      menuclickrow(val) {
        this.$emit('menu-click', val)
      },
      checkcurrentrow(val) {
        this.$emit('current-change', val)
        let { row } = val
        this.setCurrentRow(row)
      },
      //设置table数据
      initTableData(list) {
        this.listdata = this.treetolist(list)
        this.replaceLeave()
      },
      //设置高亮行
      setCurrentRow(row) {
        if (row) {
          this.checkrow = row
          this.$refs['xTable'].setCurrentRow(row)
        }
      },
      onExpand(row, index) {
        if (row.treeflag) {
          //收起
          this.onclosetree(row, index)
        } else {
          //展开
          this.onopentree(row, index)
        }
      },
      //根据当前层级确定展开还是收起
      validleavetreeleave(row) {
        row.treeflag = row.hasChild
          ? this.expandLevel !== null
            ? row.leave < this.expandLevel
            : true
          : null
      },
      setrowicon(row) {
        let index = row.treeflag === null ? 0 : row.treeflag ? 1 : 2
        this.$set(row, 'treestate', this.iconconfig[index])
      },
      onopentree(row, index, flag = true) {
        this.$set(row, 'treeflag', true)
        if (!this.expandnodeoids.includes(row.reluuid)) {
          this.expandnodeoids.push(row.reluuid)
        }
        this.setrowicon(row)
        this.addchildrenrow(row, index, flag)
      },
      removeexpand(reluuid) {
        let index = this.expandnodeoids.indexOf(reluuid)
        if (index >= 0) {
          this.expandnodeoids.splice(index, 1)
          return true
        }
        return false
      },
      onclosetree(row, index) {
        this.$set(row, 'treeflag', false)
        this.removeexpand(row.reluuid)
        this.setrowicon(row)
        let nowleave = row.leave
        let havechildren = false
        let lastchildren = null
        for (let i = index; i < this.nowlist.length; i++) {
          let element = this.nowlist[i]
          //当前节点的下一个节点,下一个节点层级小于当前节点层级，则无下级节点
          if (i === index + 1 && element.leave <= nowleave) {
            break
          }
          if (i > index && nowleave + 1 <= element.leave) {
            havechildren = true
            //遍历到最后一个子节点
            if (i === this.nowlist.length - 1) {
              lastchildren = i + 1
            }
          } else if (havechildren && element.leave <= nowleave) {
            //检索到最后一个子节点
            lastchildren = i
            break
          }
        }
        if (lastchildren !== null) {
          let startarr = this.nowlist.slice(0, index + 1)
          let endarr = this.nowlist.slice(lastchildren)
          this.nowlist = endarr ? [...startarr, ...endarr] : startarr
          //树线条使用
          row.childCount = 0
          this.setTableData(this.nowlist)
        }
      },
      //重置树数据
      resetlist(nowlist) {
        this.expandnodeoids = []
        nowlist.forEach((item) => {
          //手动点击折叠展开
          this.validleavetreeleave(item)
          //显示展开折叠的图标
          this.setrowicon(item)
        })
      },
      //添加子节点
      addchildrenrow(row, index, flag) {
        let start = Date.now()
        let nextchildren = this.getchildrenlist(row, index)
        //设置折叠状态,所有子节点为折叠状态
        nextchildren.forEach((item) => {
          item.treeflag = item.hasChild ? false : null
          this.setrowicon(item)
        })

        console.log(
          '获取子节点耗时' + (Date.now() - start) + '毫秒',
          nextchildren.length + '个'
        )
        if (nextchildren.length === 0) {
          row.treeflag = null
          row.treestate = null
        } else {
          let startarr = this.nowlist.slice(0, index + 1)
          let endarr = index == 0 ? [] : this.nowlist.slice(index + 1)
          this.nowlist = [...startarr, ...nextchildren, ...endarr]
          row.childCount = nextchildren.length
          this.setTableData(this.nowlist, flag)
        }
      },

      getchildrenlist(row) {
        let allIndex = row.uuid
        let nowleave = row.leave
        //当前层级下级节点
        let nextchildren = []
        for (let i = allIndex; i < this.listdata.length; i++) {
          const element = this.listdata[i]
          //当前节点的下一个节点,下一个节点层级小于当前节点层级，则无下级节点
          if (i === allIndex + 1 && element.leave <= nowleave) {
            break
          }
          if (i > allIndex && nowleave + 1 === element.leave) {
            nextchildren.push(element)
          } else if (nextchildren.length > 0 && element.leave <= nowleave) {
            //检索到最后一个子节点
            break
          }
        }
        return nextchildren
      },
      //计算leave层级并计算是否包含子节点hasChildren，平铺树型结构
      treetolist(tree) {
        let loop = (list, parent, alllist) => {
          list.forEach((row) => {
            row.leave = parent ? parent.leave + 1 : 1
            row.hasChild = row.children && row.children.length > 0
            //手动点击折叠展开
            this.validleavetreeleave(row)
            //显示展开折叠的图标
            this.setrowicon(row)

            //添加唯一标识
            row.uuid = alllist.length

            row.parentUuid = parent ? parent.uuid : null

            //生成包含所有父的唯一id，方便查找子节点和父节点
            row.reluuid =
              (parent ? parent.reluuid + '-' : '') + this.getStringHash(row.number)
            alllist.push(row)
            if (row.hasChild) {
              loop(row.children, row, alllist)
            }
            row.children = []
          })
        }
        let res = []
        let start = Date.now()
        loop(tree, null, res)
        console.log('添加初始数据执行时间', Date.now() - start)
        return res
      },
      //获取字符串hash值
      getStringHash(val) {
        var hash = 0, i, chr
        if (val.length === 0) return hash
        for (i = 0; i < val.length; i++) {
          chr = val.charCodeAt(i)
          hash = (hash << 5) - hash + chr
          hash |= 0 // Convert to 32bit integer
        }
        return hash
      },

      replaceLeave() {
        let nowlist = null
        if (this.expandLevel) {
          nowlist = this.listdata.filter((item) => item.leave <= this.expandLevel)
        } else {
          nowlist = this.listdata
        }
        //出现重复oid时无法正确折叠展开
        this.nowlist = nowlist
        this.setdefaultexpand()
        this.setTableData(this.nowlist)
      },
      //设置默认展开节点
      setdefaultexpand() {
        if (this.expandnodeoids.length > 0) {
          this.expandnodeoids.forEach((reluuid) => {
            let nowrowIndex = this.nowlist.findIndex(
              (row, index) => row.reluuid === reluuid
            )
            if (nowrowIndex >= 0) {
              this.onopentree(this.nowlist[nowrowIndex], nowrowIndex, false)
            }
          })
        }
      },
      setTableData(data, flag = true) {
        const startTime = Date.now()
        this.compPreChildCount(data)
        this.nowlist = data
        if (flag) {
          this.$refs.xTable.reloadData(data).then(() => {
            this.setCurrentRow(this.checkrow)
            console.log(
              `渲染${data.length}行，用时 ${Date.now() - startTime}毫秒`
            )
          })
        }
      },
      //根据当前节点uuid获取父节点数据
      getParentRow(uuid) {
        let row = this.listdata[uuid]
        if (row.parentUuid !== null) {
          return this.listdata[row.parentUuid]
        }
      },
      //计算上级包含的子节点数量
      compPreChildCount(data) {
        let start = Date.now()
        let leaveCount = new Map()
        let maxleave = 0
        let preleave = 0
        data.forEach((item) => {
          if (item.leave > maxleave) {
            maxleave = item.leave
          }

          //计算当前leave层级数量
          leaveCount.set(
            item.leave,
            leaveCount.has(item.leave) ? leaveCount.get(item.leave) + 1 : 1
          )

          let allcount = 0
          if (preleave > item.leave) {
            for (let i = item.leave + 1; i <= maxleave; i++) {
              if (leaveCount.has(i)) {
                allcount += leaveCount.get(i)
              }
            }
          }
          item.preChildCount = allcount
          //leave层级开始升，清理之后的map数量
          if (preleave < item.leave) {
            for (let i = item.leave + 1; i <= maxleave; i++) {
              if (leaveCount.has(i)) {
                leaveCount.delete(i)
              }
            }
            maxleave = item.leave
          }
          preleave = item.leave
        })
        console.log(`计算树线条所费时间${Date.now() - start}`)
      },
    },

  }
}
