<template>
	<div class="base-panel page-container">
        <workflow-page
            :pageCode="'processManage'"
            :listApi="listApi"    
        ></workflow-page>
	</div>
</template>

<script>
import workflowPage from './workflow-page';

export default {
	name: 'processManage',
	data() {
		return {
            listApi: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/fuzzyPage/byContainer`,
		}
	},
	components: {
        workflowPage,
	},
	mounted() {

    },
	methods: {
		
	},
}
</script>

<style lang="less" scoped>
.page-container {
	height: calc(~"100% - 45px");
}
</style>
