<template>
  <a-modal v-if="visible" class="slot-dialog-warp" :title="title" :visible="visible" :footer="null" width='80%' @cancel="handleCancel">
    <div v-loading="confirmLoading">
      <a-steps class="steps-nav" v-model="current" type="navigation" size="small">
        <a-step v-for="item in steps" :key="item.title" :title="item.title" style=" pointer-events: none;"/>
      </a-steps>
      <div class="steps-content">
        <keep-alive>
          <component :ref="steps[current].key" :is="steps[current].content" :tabData='steps[current]' :currentRow='currentRow' :stepsInfo="stepsInfo" :defaultTableList='defaultTableList' :baseInfo="baseInfo" />
        </keep-alive>
      </div>
      <div class="steps-action">
        <a-button v-if="current!=0" style="margin-left: 8px" @click="prev">
          上一步
        </a-button>
        <a-button :disabled="current ===steps.length - 1" type="primary" @click="next">
          下一步
        </a-button>
        <template v-if="steps[current].key=='workflowTemplate'">
          <a-button @click="onSaveDrafts('save')">{{ $t("保存草稿") }}</a-button>
          <a-button type="primary" @click="handleOk">
            启动流程
          </a-button>

        </template>

        <a-button @click="handleCancel">
          取消
        </a-button>

      </div>
    </div>
  </a-modal>

</template>
<script>
import baseInfo from "./base-info";
import workflowInfo from "./workflow-info.vue";
import workflowObject from "./workflow-object";
import workflowTemplate from "./workflow-template";

import ModelFactory from "jw_apis/model-factory";
// import {findPartBom} from "../../../batch-operator-dialog/api";
import {
  findPartBom,
} from 'views/batch-operator-dialog/api/index.js'
// 创建并启动流程
const createThenStartProcess = ModelFactory.create({
  url: `${Jw.gateway}/customer/ding/task/createSendTask`,
  method: "post"
});

// 保存草稿
const createProcess = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/create`,
  method: "post"
});

const docCreateProcess = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/ding/task/createSendTask`,
  method: 'post'
})

const preNextStepCheck =  ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/process/preNextStepCheck`,
  method: 'post'
})

export default {
  components: {
    baseInfo,
    workflowObject,
    workflowTemplate,
    workflowInfo
  },
  props: {
    delayHide: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      visible: false,
      confirmLoading: false,
      title: "启动工作流",
      current: 0,
      steps: [
        // {
        //   title: "基本信息",
        //   key: "baseInfo",
        //   content: "baseInfo"
        // },
        // {
        //   title: "基本信息",
        //   key: "workflowInfo",
        //   content: "workflowInfo"
        // },
        {
          title: "选择工作流对象",
          key: "workflowObject",
          content: "workflowObject"
        },
        {
          title: "选择工作流模板",
          key: "workflowTemplate",
          content: "workflowTemplate"
        }
      ],
      currentRow: {},
      defaultTableList: [],
      submitParams: {},
      stepsInfo: {
        additionaltranslation: {
          outgoingType: '电子版本',
          outgoingNumber: 1,
          isReview: null,
          isOutgoing: null,
        },
      },

      baseInfo: {},
    };
  },
  methods: {
    show(options) {
      if (options) {
        let { title, currentRow = {}, defaultTableList } = options;
        if (title) {
          this.title = otitle;
        }

        this.defaultTableList = defaultTableList;
        this.currentRow = currentRow;
      }
      this.current = 0;

      this.visible = true;

      return new Promise((resolve, reject) => {
        this.handleCancel = () => {
          this.hide();
          reject("cancel");
        };

        this.handleOk = async () => {
          await this.checkData();
          //文件外发和发起
          let {selectWorkflow} = this.$refs.workflowTemplate.getValue();
          //只有文件归档流程检测，去掉文件外发校验 是否函审和是否外发
          if (selectWorkflow.key === 'cn_jwis_jswdfb') {
            await this.checkIsReview();
          }
          if (selectWorkflow.key === 'cn_jwis_wjwf') {
            await this.checkIsHasBom();
          }
          this.onStart();
          resolve();
        };
      });
    },

    onSaveDrafts(flag) {
      this.getParams();
      this.submitParams.locationInfo.catalogOid = this.submitParams.locationInfo.containerOid
      this.submitParams.locationInfo.catalogType = this.submitParams.locationInfo.containerType
      let api = createProcess;
      this.confirmLoading = true;
      api
        .execute(this.submitParams)
        .then(res => {
          this.$success(this.$t("msg_success"));
          this.$emit("getTableData");
          this.hide();
          this.confirmLoading = false;
        })
        .catch(err => {
          this.confirmLoading = false;
          this.$error(err.msg);
        });
    },
    checkNodeAssignee(){
      let {
        selectWorkflow,
        variableList
      } = this.$refs.workflowTemplate.getValue();
      let key=selectWorkflow.key
      let checkKeys=[] //会签节点要检查的流程key
      let teamContent = [];
      for (let i = 0; i < variableList.length; i++) {
        const item = variableList[i];
        let isCheck=!item.countersignFlag||checkKeys.includes(key)
        if (!item.users.length&&isCheck) {
        this.$warning("流程角色请选择用户");
        throw new Error("流程角色请选择用户");
        }
      }
    },
    onStart(flag) {
      this.checkNodeAssignee()
      this.getParams();

      let api = createThenStartProcess;
      // if (this.detailInfo.oid && this.pageCode !== "objectProcess") {
      //   this.submitParams.oid = this.detailInfo.oid;
      //   api = updateThenStartProcess;
      // } else {
      //   api = createThenStartProcess;
      // }
      this.confirmLoading = true;
      api
        .execute(this.submitParams)
        .then(res => {
          this.$success(this.$t("msg_success"));
          this.$emit("getTableData");
          this.hide();
          this.confirmLoading = false;
          this.clearReviewAndOutgoing(); // 清除状态值
        })
        .catch(err => {
          this.confirmLoading = false;
          this.$error(err.msg);
        });
    },
    // 独立的清除方法
    clearReviewAndOutgoing() {
      this.stepsInfo.additionaltranslation.isReview = null;
      this.stepsInfo.additionaltranslation.isOutgoing = null;
    },
    getParams() {
      let { docInfo, bizObjects, additionaltranslation } = this.stepsInfo;
      let {
        teamContent,
        selectWorkflow
      } = this.$refs.workflowTemplate.getValue();

      if(teamContent) {
        teamContent.forEach(item=>{
          item.userAccounts = item.users
        })
      }
      let {
        catalogOid,
        catalogType,
        containerOid,
        containerType,
        containerModelDefinition,
        modelDefinition
      } = this.currentRow;
      this.submitParams = {
        locationInfo: {
          catalogOid,
          catalogType,
          containerOid,
          containerType,
          containerModelDefinition
        },
        modelDefinition: "ProcessOrder",

        bizObjects,
        processModelId: selectWorkflow.id,
        teamContent,
        name: selectWorkflow.name,
        extensionContent: {
          ...this.baseInfo,
          docInfo
        },
        ...additionaltranslation
      };
    },

    hide() {
      this.visible = false;
    },
    async next() {
      await this.checkData();
      const { bizObjects } = this.stepsInfo
      this.preCheck(bizObjects);
    },
    preCheck(bizObjects) {
      preNextStepCheck.execute(bizObjects)
          .then(res => {
            this.confirmLoading = false;
            let underReviews = bizObjects.find(item => item.lifecycleStatus === 'UnderReview')
            if (underReviews) {
              this.$warning("存在审阅中的数据，请移除！")
              return;
            }
            const hasDraftStatus = bizObjects.some(item => item.lifecycleStatus === 'Released');
            if (hasDraftStatus) {
              // 如果存在 '已发布' 状态的数据，开启校验
              const {isStatusConsisTency, invalidItem1} = this.checkLifecycleStatusConsistency(bizObjects);
              if (!isStatusConsisTency) {
                console.log(invalidItem1);
                this.$warning(`对象的状态不一致，请确认编码为 ${invalidItem1.number} 名称为 ${invalidItem1.name} 的对象`);
                return;
              }
            }

            this.current++;
          })
          .catch(err => {
            this.confirmLoading = false;
            this.$error(err.msg);
          });
    },
    prev() {
      this.current--;
    },

    async checkData() {
      let key = this.steps[this.current].key;
      let ref = this.$refs[key];
      if (ref && ref.validate) {
        await ref.validate();
      }
      if (ref && ref.getValue) {
        let val = ref.getValue();
        Object.assign(this.stepsInfo, val);
      }
    },
    async checkIsReview() {
      // 检查“是否函审”和“是否外发”是否已填写
      const { isReview, isOutgoing } = this.stepsInfo.additionaltranslation;
      if (!isReview) {
        this.$error('请填写是否函审');
        throw new Error("Missing field: isReview");  // 阻止流程继续执行
      }
      if (!isOutgoing) {
        this.$error('请填写是否外发');
        throw new Error("Missing field: isOutgoing");  // 阻止流程继续执行
      }
    },
    checkLifecycleStatusConsistency(bizObjects) {
      console.log(bizObjects)

      // 如果对象数组为空，直接返回校验不通过
      if (!bizObjects.length) return { isStatusConsisTency: false, invalidItem1: null };

      // 获取第一个对象的lifecycleStatus属性值
      const firstLifecycleStatus = bizObjects[0].lifecycleStatus;

      // 查找第一个不一致的对象
      const invalidItem1 = bizObjects.find(obj => obj.lifecycleStatus !== firstLifecycleStatus);

      // 返回校验结果
      return { isStatusConsisTency: !invalidItem1, invalidItem1 };
    },
    // handleOk:_.noop
    async checkIsHasBom() {
      console.log("文件外发流程检测是否有BOM");
      console.log("主对象集合：", this.defaultTableList);

      const allMasterTypesArePart = this.defaultTableList.every(item => item.masterType === "Part");

      if (allMasterTypesArePart) {
        // 获取 bizObjects 并检查长度是否与 defaultTableList 相同
        const { bizObjects } = this.stepsInfo;

        if (bizObjects.length === this.defaultTableList.length) {
          // 如果长度相同，提示并抛出错误
          const missingName = bizObjects.map(obj => obj.name || obj.number || obj.oid).join(", ");
          // this.$warning(`未找到下级物料，本流程只允许发送BOM，请确认BOM准确！以下对象缺少 BOM：${missingName}`);
          // throw new Error("bizObjects 和 defaultTableList 的长度相同，缺少bom！");
        }
      }

      // 处理所有 BOM 查询，等待所有请求完成
      const results = await Promise.all(
          this.defaultTableList.map(async (item) => {
            if (item.masterType !== "Part") {
              console.log(`跳过检查: ${item.name || item.number || item.oid} (masterType: ${item.masterType})`);
              return { item, missing: false }; // 跳过检查，直接返回没有缺失
            }
            try {
              let bomdata = await findPartBom(item.oid);

              // 记录调试信息
              console.log(`正在检查对象: ${item.name || item.number || item.oid}`);
              console.log("findPartBom 返回数据:", bomdata);

              if (!bomdata || bomdata.length === 0) {
                console.warn(` [BOM 缺失] ${item.name || item.number || item.oid} -> findPartBom 为空或长度为 0`);
                return { item, missing: true };
              }

              let firstBomItem = bomdata[0];  // 获取第一个 BOM 对象
              console.log("当前 BOM 数据:", firstBomItem);

              if (!firstBomItem) {
                console.warn(` [BOM 缺失] ${item.name || item.number || item.oid} -> BOM 对象为空`);
                return { item, missing: true };
              }

              // 深入检查第一个对象的 children
              if (!Array.isArray(firstBomItem.children) || firstBomItem.children.length === 0) {
                console.warn(`[BOM 缺失] ${item.name || item.number || item.oid} -> BOM 对象的 children 不存在或长度为 0`);
                return { item, missing: true };
              }

              console.log(` [BOM 存在] ${item.name || item.number || item.oid} -> 通过检查`);
              return { item, missing: false };
            } catch (error) {
              console.error(` 查询 BOM 失败: ${error.message}`, error);
              return { item, missing: true }; // 发生异常也视为缺失 BOM
            }
          })
      );

      // 过滤出缺少 BOM 的对象
      const missingBOMObjects = results.filter(result => result.missing).map(result => result.item);

      console.log("缺少 BOM 的对象集合：", missingBOMObjects);

      if (missingBOMObjects.length > 0) {
        const missingNames = missingBOMObjects.map(obj => obj.name || obj.number || obj.oid).join(", ");
        this.$warning(`未找到下级物料，本流程只允许发送BOM，请确认BOM准确！以下对象缺少 BOM：${missingNames}`);
        throw new Error(`Missing BOM for objects: ${missingNames}`);
      }
    },
  }
};
</script>
<style lang="less">
.slot-dialog-warp {
  .steps-nav.ant-steps {
    .ant-steps-item {
      background: rgba(30, 32, 42, 0.04);
      margin: 0;
      padding: 5px 0;
      &::after,&::before{
        display: none;
      }
      .ant-steps-item-container{
        padding: 0;
        .ant-steps-item-title{
          color: rgba(0, 0, 0, 0.65);
        }
      }
      &.ant-steps-item-active{
        background: #a4c9fc;
      }
    }
  }
  .steps-content {
    margin-top: 10px;
    height: 500px;
  }
  .ant-modal-body {
    padding: 10px;
    // height: 400px;
    .steps-action {
      text-align: right;
    }
  }
}
</style>
