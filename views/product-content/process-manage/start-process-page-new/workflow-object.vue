<template>
  <select-table-data :selectedRows.sync="workflowRows" :defaultTableList="defaultTableList" />
</template>
<script>
import SelectTableData from "views/product-content/process-manage/select-table-data.vue";
export default {
  components: {
    SelectTableData
  },
  props: ["defaultTableList"],
  data() {
    return {
      workflowRows: []
    };
  },
  methods: {
    getValue() {
      this.workflowRows.forEach(item => {
        item.primary = item.selectParendOid ? false : true;
        return item;
      });
      return {
        bizObjects:this.workflowRows
      };
    }
  }
};
</script>

