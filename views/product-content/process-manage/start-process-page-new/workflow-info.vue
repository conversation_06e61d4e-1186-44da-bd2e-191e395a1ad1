<template>
  <div class="main-step">
    <a-form-model ref="addForm" class="form-container" layout="vertical" :model="formdata">
      <a-row :gutter="10">
        <a-col :span="12">
          <a-form-model-item :label="$t('txt_from_type')" prop="modelDefinition">
            <a-select v-model="formdata.modelDefinition" :placeholder="$t('msg_select')"
              :loading="modelDefinitionLoading">
              <a-select-option v-for="val in typeOpts" :key="val.oid" :value="val.name">
                {{ val.displayName }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <jw-layout-builder v-if="formdata.modelDefinition" ref="ref_appBuilder" type="Model" :layoutName="'create'"
      :modelName="formdata.modelDefinition" :instanceData="baseInfo">
    </jw-layout-builder>
  </div>
</template>

<script>

import {
  jwLayoutBuilder,
} from "jw_frame";
import ModelFactory from "jw_apis/model-factory";
// 获取申请单类型
const fetchSubModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/model/flapTree`,
  method: "get"
});
export default {
  props: ["baseInfo"],
  components: {
    jwLayoutBuilder
  },
  data() {
    return {
      modelDefinitionLoading: false,
      formdata: {},
      typeOpts: [],
    }
  },
  created() {
    this.getSubModel()
  },
  methods: {
    valchange(val) {
      this.formdata.modelDefinition = val
    },
    getContainerList() {
      fetchContainerList
        .execute({
          index: 1,
          size: 1000,
          searchKey: ""
        })
        .then(data => {
          this.conOpts = data.rows;
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },
    onChangeConList(val) {
      this.containerInfo = this.conOpts.find(item => item.oid === val) || {};
    },
    getSubModel() {
      this.modelDefinitionLoading = true;
      fetchSubModel
        .execute({
          modelName: "ProcessOrder"
        })
        .then(res => {
          this.typeOpts = res;
          setTimeout(() => {
            if (this.typeOpts.length > 0) {
              this.$set(this.formdata, "modelDefinition", this.typeOpts[0].name)
            }
          }, 0);

        })
        .catch(err => {
          this.$error(err.msg);
        })
        .finally(() => {
          this.modelDefinitionLoading = false;
        });
    },
  }
}
</script>

<style lang="less" scoped>
.main-step {
  overflow-y: auto;
  height: 100%;
  overflow-x: hidden;
}
</style>