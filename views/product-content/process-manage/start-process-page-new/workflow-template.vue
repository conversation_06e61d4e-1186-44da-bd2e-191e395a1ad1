<template>
  <div class="workflow-template-wrap">
    <div class="workflow-template-list">
      <a-input-search v-model.trim="searchKey" class="search-input" allow-clear :placeholder="$t('search_text')" @search="onSearch" />
      <div class="radio-wrap">
        <a-radio-group v-model.trim="selectWorkflow" @change="onChangeRadio">
          <a-radio v-for="item in workflowListData" :key="item.key" :value="item">
            {{ item.name }}
          </a-radio>
        </a-radio-group>
      </div>
    </div>
    <div class="workflow-template-info">
      <div class="right-title">{{ selectWorkflow.name }}
        <span class="right-tip"> {{ selectWorkflow.metaInfo }} </span>
      </div>

      <div class="right-process-img">
        <img :src="workflowImageUrl" alt="" />
      </div>

      <!-- 项目文档产品化流程单独处理 -->
      <htDoc v-if="selectWorkflow.key=='cn_jwis_xmcph'" ref="ref_ht_doc" :currentRow='currentRow'></htDoc>

      <!-- cn_jwis_wjwf -->
      <div v-if="selectWorkflow.key === 'cn_jwis_wjwf'">
        <div class="formLable">文件外发类型</div>
        <a-select v-model="stepsInfo.additionaltranslation.outgoingType" default-value="电子版本" class="form-item">
          <a-select-option key="电子版本">电子版本</a-select-option>
          <a-select-option key="纸质版本">纸质版本</a-select-option>
        </a-select>
        <div class="formLable">文件外发数量</div>
        <a-input-number v-model="stepsInfo.additionaltranslation.outgoingNumber" :min="1" :max="200000" class="form-item"/>
        <div class="formLable">外发单位</div>
        <a-input v-model="stepsInfo.additionaltranslation.outgoingCompany" class="form-item"></a-input>
      </div>

      <!-- 文档归档及函审 -->
      <a-form-model :model="stepsInfo.additionaltranslation" >
        <div v-if="selectWorkflow.key === 'cn_jwis_jswdfb'">
          <a-form-model-item
              label="是否函审"
              prop="isReview"
              :rules="[{ required: true, message: '请选择是否函审' }]"
          >
            <a-select
                v-model="stepsInfo.additionaltranslation.isReview"
                placeholder="请选择"
                class="form-item"
            >
              <a-select-option value="是">是</a-select-option>
              <a-select-option value="否">否</a-select-option>
            </a-select>
          </a-form-model-item>

          <a-form-model-item
              label="是否外发（包括自研单机方）"
              prop="isOutgoing"
              :rules="[{ required: true, message: '请选择是否外发' }]"
          >
            <a-select
                v-model="stepsInfo.additionaltranslation.isOutgoing"
                placeholder="请选择"
                class="form-item"
            >
              <a-select-option value="是">是</a-select-option>
              <a-select-option value="否">否</a-select-option>
            </a-select>
          </a-form-model-item>
        </div>
      </a-form-model>

      <!-- <div class="right-temp-wrap">
        <div>{{ $t("txt_team_temp") }}</div>
        <a-select v-model.trim="teamTemp" :placeholder="$t('msg_select')" show-search :filter-option="filterOption" allowClear @change="getRoleWithUser">
          <a-select-option v-for="val in teamTempOpts" :key="val.oid" :value="val.oid">
            {{ val.name }}
          </a-select-option>
        </a-select>
      </div> -->
      <div class="right-handler-wrap" v-show="variableList.length > 0">
        <div class="handler-item" v-for="item in variableList" :key="item.name">
          <div class="item-head">
            {{ item.displayName }}
            <i class="jwi-iconrefresh"></i>
          </div>
          <div class="item-body">
            <div class="handler-add-btn" @click="onAddUser(item)">
              <i class="jwi-iconuser-add"></i>
            </div>
            <div class="handlers" v-show="!!item.users.length">
              <div class="handler" v-for="val in item.users" :key="val.account">
                <i class="jwi-iconclose-circle-full close-icon" @click="onDeleteUser(item.users, val)"></i>
                <div class="avatar">
                  <img v-if="val.avatar" :src="val.avatar" alt="" />
                  <a-avatar v-else>{{ val.name | filterName }}</a-avatar>
                </div>
                <div>{{ val.name }}（{{ val.account }}）</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <userModalV3 v-if="['cn_jwis_wlbmsq','cn_jwis_wjwf','cn_jwis_jswdfb', 'errortest'].includes(selectWorkflow.key)" ref="ref_user_modal" :isCheckbox="ischeckBox" />
    <jw-user-modal-v2 v-else ref="ref_user_modal" :isCheckbox="ischeckBox" />
  </div>
</template>
<script>
import htDoc from "./base-info";
import { jwUserModalV2 } from "jw_frame";
import { getCookie } from "jw_utils/cookie";
import ModelFactory from "jw_apis/model-factory";
import userModalV3 from "components/user-modal-v3";
// 查询已部署的最新流程模板
const fetchDeployed = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.customerServer
  }/workflow-micro/workflow/model/deployed/latest`,
  method: "post"
});

// 获取需要配置的角色
const fetchVariables = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.workflowMicroServer
  }/workflow/repository/start/variables`,
  method: "get"
});

// 获取角色对应的用户
const fetchRoleWithUser = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.accountMicroServer
  }/team/templaterole/search/roleWithUser`,
  method: "get"
});

// 获取流程团队
const findProcessTeamApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/pdmFindAllTeam`,
  method: "get"
});


export default {
  components: {
    htDoc,
    jwUserModalV2,
    userModalV3
  },
  props: ["currentRow", 'defaultTableList', 'stepsInfo'],
  data() {
    return {
      searchKey: "",
      selectWorkflow: {},
      ischeckBox: true,
      workflowImageUrl: "",
      teamTemp: "",
      teamTempOpts: [],
      variableList: [],
      onlyWorkflowModelId: "",
      workflowList: [],
    };
  },
  async created() {
    await this.getDeployed();
    this.getVariables();
    this.getTeam();
  },
  computed: {
    workflowListData() {
      if (this.onlyWorkflowModelId) {
        let choosearr = this.workflowList.filter(
          p => p.id == this.onlyWorkflowModelId
        );
        if (choosearr.length > 0) {
          return choosearr;
        } else {
          console.error(
            "未找到模型id,组织管理中优先级比系统管理中绑定的模型优先级高",
            this.onlyWorkflowModelId
          );
          return this.workflowList;
        }
      } else {
        return this.workflowList || [];
      }
    }
  },
  filters: {
    filterName: function(name) {
      if (!name) {
        return name;
      }
      // 是否含有中文
      const hasCh = /[\u4E00-\u9FA5]/.test(name);
      let showName = "";
      if (hasCh) {
        // 用户 含有中文取后两个字符
        showName = name.slice(-2);
      } else {
        // 没有中文
        showName = name.slice(-2).toLocaleUpperCase();
      }
      return showName;
    }
  },
  methods: {
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    getTeam() {
      findProcessTeamApi
        .execute()
        .then(res => {
          this.teamTempOpts = res;
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },
    async validate() {
      if (this.$refs.ref_ht_doc) {
        await this.$refs.ref_ht_doc.validate();
      }
    },
    getValue() {
      let docInfo = {};
      if (this.$refs.ref_ht_doc) {
        let value = this.$refs.ref_ht_doc.getValue();
        docInfo = value.docInfo;
      }

      let teamContent = this.variableList.map(item => {
        return {
          roleName: item.name,
          name:item.displayName,
          users: item.users.map(val => val.account)
        };
      });
      return {
        teamContent,
        selectWorkflow: this.selectWorkflow,
        docInfo,
        variableList:this.variableList
      };
    },
    onChangeRadio() {
      this.teamTemp = undefined;
      this.variableList = [];
      this.workflowImageUrl = `${Jw.gateway}/${
        Jw.workflowMicroServer
      }/workflow/repository/process-definitions/image/byKey?key=${
        this.selectWorkflow.key
      }&tenantId=${this.selectWorkflow.tenantId}&appName=${Jw.appName}`;
      this.getVariables();
    },
    onAddUser(item) {
      this.$refs.ref_user_modal
        .show({
          type: "User"
        })
        .then(data => {
          let oids = item.users.map(item => item.oid);
          data.forEach(val => {
            if (!oids.includes(val.oid)) {
              item.users.push(val);
            }
          });
        });
    },
    onDeleteUser(list, item) {
      let index = list.indexOf(item);
      list.splice(index, 1);
    },
    findAutoRoleAct(){
      //自动填充人员
      const findAutoRole = ModelFactory.create({
        url: `${Jw.gateway}/${Jw.customerServer}/process/team/autoUser?workflowId=${this.selectWorkflow.key||''}`,
        method: 'post',
      })
      findAutoRole.execute(this.defaultTableList).then(resp => {
        this.variableList.forEach((row) => {
          let role = resp.find(item => item.name === row.name)
          if(role){
            row.users = role.users
          }else{
            row.users = []
          }
        })
      })
    },
    getRoleWithUser() {
      if (!this.teamTemp) {
        this.variableList.forEach(item => {
          item.users = [];
        });
        return;
      }
      fetchRoleWithUser
        .execute({
          teamTemplateOid: this.teamTemp
        })
        .then(res => {
          this.variableList.forEach(item => {
            const info = res.find(val => val.name == item.name);
            item.users = info ? info.users : [];
          });
        })
        .catch(err => {
          this.variableList = [];
          this.$error(err.msg);
        });
    },
    onSearch() {
      this.getDeployed();
    },
    getVariables() {
      if (!this.selectWorkflow.deploymentId) {
        return;
      }
      fetchVariables
        .execute({
          deploymentId: this.selectWorkflow.deploymentId
        })
        .then(res => {
          this.variableList = res.map(item => {
            item.users = [];
            return item;
          });
          this.findAutoRoleAct()
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },
    async getDeployed() {
      console.log('Document, Part, ECAD, MCAD 在外发，获取允许发起的所有流程')
      const { bizObjects } = this.stepsInfo
      let models = bizObjects.map(item => {
        return {
          modelType: item.type,
          status: item.lifecycleStatus,
          btnType: item.btnType,
        }
      })
      let res = await fetchDeployed.execute({ searchKey: this.searchKey, models});
       //如果不是变更申请 过滤掉变更流程
      if(this.currentRow.type!="ECR"&&!_.isEmpty(res)){
        res=res.filter(item=>!item.name.includes("变更"))
      }
      this.workflowList = res;
      if (this.workflowListData.length > 0) {
        this.selectWorkflow = this.workflowListData[0];
        this.workflowImageUrl = `${Jw.gateway}/${
          Jw.workflowMicroServer
        }/workflow/repository/process-definitions/image/byKey?key=${
          this.selectWorkflow.key
        }&tenantId=${this.selectWorkflow.tenantId}&appName=${Jw.appName}`;
      } else {
        this.selectWorkflow = {
          name: ""
        };
      }
    }
  }
};
</script>
<style lang="less" scoped>
.form-item{
  width: 60%;
}

.formLable{
  margin: 15px 0;
}

.workflow-template-wrap {
  height: 100%;
  display: flex;
  .workflow-template-list {
    width: 300px;
    padding-right: 10px;
    overflow: auto;
    .ant-radio-wrapper {
      display: block;
      line-height: 32px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .workflow-template-info {
    flex: 1;
    border-left: 1px solid rgba(30, 32, 42, 0.15);
    color: rgba(30, 32, 42, 0.85);
    overflow: auto;
    padding: 0 2px 0 16px;
    .right-title {
      color: rgba(30, 32, 42, 0.85);
      font-size: 16px;
      font-weight: 700;
      .right-tip {
        font-size: 14px;
        margin-left: 10px;
      }
    }
    .right-process-img {
      margin: 10px 0;
      background: rgba(30, 32, 42, 0.04);
      border: 1px solid rgba(30, 32, 42, 0.15);
      border-radius: 5px;
      height: 300px;
      width: 100%;
      > img {
        height: 100%;
        width: 100%;
      }
    }
    .right-temp-wrap {
      margin: 15px 0;
      .ant-select {
        width: 60%;
        margin-top: 8px;
      }
    }
    .right-handler-wrap {
      .handler-item {
        margin-bottom: 16px;
        .item-head {
          margin-bottom: 8px;
          i {
            margin-left: 8px;
          }
        }
        .item-body {
          display: flex;
        }
        .handler-add-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          min-width: 32px;
          height: 32px;
          margin-right: 8px;
          background: #f0f7ff;
          border: 1px solid #a4c9fc;
          border-radius: 50%;
          cursor: pointer;
        }
        .handlers {
          display: flex;
          flex-wrap: wrap;
        }
        .handler {
          position: relative;
          display: flex;
          align-items: center;
          padding: 3px 8px;
          margin: 0 8px 8px 0;
          background: rgba(30, 32, 42, 0.04);
          border: 1px solid transparent;
          border-radius: 4px;
          cursor: pointer;
          .close-icon {
            position: absolute;
            top: -13px;
            right: -8px;
            visibility: hidden;
          }
          .avatar {
            width: 24px;
            height: 24px;
            margin-right: 8px;
            img {
              width: 100%;
              height: 100%;
              border-radius: 50%;
            }
            .ant-avatar {
              width: 24px;
              height: 24px;
              line-height: 24px;
            }
          }
          &:hover {
            background: #f0f7ff;
            border: 1px solid #a4c9fc;
            .close-icon {
              visibility: visible;
            }
          }
        }
      }
    }
  }
}
</style>


