<template>
  <a-form-model ref="ref_base_info" class="workflow-base-info" layout="vertical" :model="currentRow" :rules="rules">
    <a-row :gutter="10">
      <a-col :span="13">
        <a-form-model-item :label="$t('上下文')" prop="catalogOid">
          <a-tree-select v-model.trim="currentRow.catalogOid" 
          :show-search="true" :placeholder="$t('请选择')" 
          :tree-node-filter-prop="treeNodeFilterProp" 
          :filter-tree-node="filterTreeNode" 
          :dropdown-style="dropdownStyle" :tree-data="treeData" @change="treeSelect">
            <template slot="folderIconTitle" slot-scope="{ name, childType }">
              <div>
                <jw-icon style="margin-right: 8px" :type="childType === 'child' ? '#jwi-wenjianga-youneiyong' : '#jwi-chanpin'" />
                <span>{{ name }}</span>
              </div>
            </template>
          </a-tree-select>
        </a-form-model-item>
      </a-col>
      <a-col :span="13">
        <a-form-model-item :label="$t('名称')" prop="name">
          <a-input v-model.trim="currentRow.name" placeholder="请输入" />
        </a-form-model-item>
      </a-col>
    </a-row>
  </a-form-model>
</template>
<script>
import ModelFactory from "jw_apis/model-factory";
// 文件夹目录
export const fetchfolderTree = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/pdmfolder/searchFolders`,
  method: "post"
});
export default {
  props: ["currentRow"],
  data() {
    let {masterType} =this.$route.query
    this._containerModel=masterType
    return {
      rules: {
        catalogOid: { required: true, message: "请选择", trigger: "change" },
        name: { required: true, message: "请输入", trigger: "change" }
      },
      treeData: [],

      dropdownStyle: {
        height: "400px",
        overflowY: "auto"
      },
      treeNodeFilterProp: "title"
    };
  },
  created() {
    let {
      catalogOid,
      catalogType,
      containerOid,
      containerType
    } = this.currentRow;
    this.source = {
      catalogOid,
      catalogType,
      containerOid,
      containerType
    };
    this.initFolder();
  },
  methods: {
    getValue() {
      let docInfo = {
        node: this.currentRow,
        source: this.source
      };
      console.log('docInfo888',docInfo)
      return { docInfo };
    },
    async validate() {
      let res = await this.$refs.ref_base_info.validate();
      if (!res) {
        throw new Error("校验失败");
      }
    },
    treeSelect(value, label, extra) {
      console.log(' extra.triggerNode', extra.triggerNode.dataRef)
      let { dataRef } = extra.triggerNode;
     /* let {
        catalogOid,
        catalogType,
        containerOid,
        containerType
      } =this.dataRef;*/
      console.log(' dataRef',dataRef)
      this.currentRow.containerOid= extra.triggerNode.dataRef.containerOid
      /*Object.assign(this.currentRow, {
        catalogOid,
        catalogType,
        containerOid,
        containerType
      });*/
     // this.getValue()
      console.log(value, label, extra);
    },
    filterTreeNode(value, treeNode, field = "name") {
      const title = treeNode.componentOptions.propsData.dataRef[
        field
      ].toLowerCase();
      return title.indexOf((value || "").trim().toLowerCase()) > -1;
    },
    initFolder() {
      fetchfolderTree
        .execute({
          containerModel: ["ProductContainer","ResourceContainer"],
        })
        .then(data => {
          this.listdata = data;
          this.treeData = this.deepData(data);
        })
        .catch(err => {
          console.error(err);
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    //加载下层树
    deepData(data, onlyDirectChildren = false) {
      const scopedSlots = { title: "folderIconTitle" };

      let arr = [];
      const loop = tree => {
        tree.map((item, index) => {
          item.key = item.oid;
          item.value = item.oid;
          item.scopedSlots = scopedSlots;

          // 只获取第一层
          if (onlyDirectChildren) {
            const temp = {
              ...item
            };
            delete temp.children;
            arr.push(temp);
            return;
          }

          if (item.children && item.children.length > 0) {
            item.children.map(item => (item.childType = "child"));
            loop(item.children);
          }
        });
      };

      loop(data);

      return onlyDirectChildren ? arr : data;
    }
  }
};
</script>
<style lang="less" scoped>
.workflow-base-info {
  overflow: hidden;
}
</style>


