<template>
  <div class="workflow-page-wrap">
    <jw-table
      ref="refTable"
      :toolbars="pageCode === 'processRecord' ? [] : toolbars"
      :columns="columns"
      :data-source.sync="tableData"
      :selectedRows.sync="selectedRows"
      :fetch="fetchTable"
      @onToolClick="onToolClick"
      @onToolInput="onToolInput"
      @onOperateClick="onOperateClick"
    >
      <template #name="{ row }">
        <div class="name-wrap">
          <div class="name-con" @click="goToDetail(row)">
            <jwIcon :type="row.modelIcon"></jwIcon>
            <span class="name-item"> {{ row.number ? row.number + '，' : '' }}{{ row.name }}</span>
          </div>
        </div>
      </template>
      <template #currentTaskAssignees="{ row }">
        <user-info :accounts="row.currentTaskAssignees" :showname="true"></user-info>
      </template>
      <template #currentTaskNames="{ row }">
        {{
          row.currentTaskNames && row.currentTaskNames.length > 0
            ? row.currentTaskNames.join(",")
            : ""
        }}
      </template>
    </jw-table>
    <start-process-modal
      :visible="visible"
      :pageCode="pageCode"
      :detailInfo="detailInfo"
      :objectDetailsData="objectDetailsData"
      @close="onCloseModal"
      @getTableData="onSearch"
    ></start-process-modal>
    <change-handler-modal
      :visible="userVisible"
      :detailInfo="detailInfo"
      @close="onCloseUserModal"
      @getTableData="onSearch"
    ></change-handler-modal>
  </div>
</template>

<script>
import startProcessModal from "./start-process-page"
import changeHandlerModal from "./change-handler-modal"
import ModelFactory from "jw_apis/model-factory"
import userInfo from "components/user-info";


// 发送DING消息
const sendDING = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/common/workflow/dingUserById`,
  method: "get",
})

// 查询是否有创建权限
const getOperationFilter = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
  method: "post",
})

// 挂起流程
const hangUpProcecss = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/runtime/hangUp`,
  method: "get",
})

// 取消流程
const cancelProcecss = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/runtime/cancel`,
  method: "get",
})

// 挂起流程重启动
const activateProcecss = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/runtime/activate`,
  method: "get",
})

const deleteProcess = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/delete`,
  method: "post",
})

export default {
  name: "workflowPage",
  props: ["pageCode", "listApi", "objectDetailsData"],
  data() {
    return {
      searchKey: "",
      tableData: [],
      selectedRows: [],
      visible: false,
      detailInfo: {},
      userVisible: false,
      permissionBtnList: [],
    }
  },
  components: {
    startProcessModal,
    changeHandlerModal,
    userInfo
  },
  computed: {
    toolbars() {
      return [
        {
          name: this.$t("btn_new_create"),
          key: "create",
          type: "primary",
          isVisible: this.permissionBtnList.includes("create"),
        },
        {
          name: this.$t("btn_search"),
          position: "before",
          display: "input",
          value: this.searchKey,
          allowClear: true,
          placeholder: this.$t("search_text"),
          suffixIcon: "jwi-iconsearch",
          key: "search",
        },
      ]
    },
    columns() {
      return [
        {
          field: "name",
          title: this.$t("flow_name"),
          minWidth: 300,
          slots: {
            default: "name",
          },
        },
        {
          field: "modelDefinition",
          title: this.$t("txt_type"),
          minWidth: 160,
          formatter: ({ row }) => this.$t(row.modelDefinition),
        },
        {
          field: "processState",
          title: this.$t("txt_plan_lifecycle"),
          minWidth: 120,
          cellRender: {
            name: "tag",
            render: ({ row }) => {
              if (row.processState === "onging") {
                return {
                  text: this.$t("txt_ongoing"),
                  color: "blue",
                }
              } else if (row.processState === "done") {
                return {
                  text: this.$t("txt_over"),
                  color: "green",
                }
              } else if (row.processState === "hang") {
                return {
                  text: this.$t("txt_hang"),
                  color: "red",
                }
              } else if (row.processState === "draft") {
                return {
                  text: this.$t("txt_drafts"),
                }
              // 钉钉流程状态
              } else if (row.processState === "finish") {
                return {
                  text: this.$t("txt_over"),
                  color: "green",
                }
              } else if (row.processState === "agree") {
                return {
                  text: this.$t("txt_over"),
                  color: "green",
                }
              } else if (row.processState === "terminate") {
                return {
                  text: '已终止',
                }
              } else if (row.processState === "refuse") {
                return {
                  text: '已驳回',
                }
              } else if (row.processState === "error") {
                return {
                  text: '结束异常',
                }
              } else if (row.processState === "cancel") {
                return {
                  text: '已取消',
                }
              } else if (row.processState === "PDM complete!") {
                return {
                  text: 'PDM结束',
                }
              } else {
                return {
                  text: '',
                }
              }
            },
          },
        },
        {
          field: "currentTaskNames",
          title: this.$t("txt_current_work"),
          minWidth: 180,
          slots: {
            default: "currentTaskNames",
          },
        },
        {
          field: "currentTaskAssignees",
          title: this.$t("txt_current_handler"),
          minWidth: 200,
          slots: {
            default: "currentTaskAssignees",
          },
        },
        {
          field: "createDate",
          title: this.$t("msg_starttime"),
          minWidth: 180,
          formatter: "date",
        },
        {
          field: "updateDate",
          title: this.$t("txt_recently_time"),
          minWidth: 180,
          formatter: "date",
        },
        {
          field: "operation",
          title: this.$t("txt_operation"),
          btns: [
            {
              icon: "jwi-iconactivation",
              title: this.$t("txt_start"),
              key: "start",
              isShow: (row) => {
                if ((Jw.getUser().systemAdmin || Jw.getUser().tenantAdmin || Jw.getUser().account === row.createBy) && row.type === 'ProcessOrder') {
                  if (
                    row.processState === "draft" ||
                    row.processState === "hang"
                  ) {
                    return true
                  }
                }
              },
            },
            {
              icon: "jwi-iconsuspension",
              title: this.$t("txt_stop"),
              key: "pause",
              isShow: (row) => {
                if ((Jw.getUser().systemAdmin || Jw.getUser().tenantAdmin ) && row.type === 'ProcessOrder') {
                  if (row.processState === "onging") {
                    return true
                  }
                }
              },
            },
            {
              icon: "jwi-iconuser-delegate",
              title: this.$t("txt_update_task"),
              key: "change",
              isShow: (row) => {
                if ((Jw.getUser().systemAdmin || Jw.getUser().tenantAdmin ) && row.type === 'ProcessOrder') {
                  if (row.processState === "onging") {
                    return true
                  }
                }
              },
            },
            {
              icon: "jwi-iconclose-circle",
              title: this.$t("btn_cancel"),
              key: "cancel",
              isShow: (row) => {
                if ((Jw.getUser().systemAdmin || Jw.getUser().tenantAdmin ) && row.type === 'ProcessOrder') {
                  if (
                    row.processState === "onging" || row.processState === "hang"
                  ) {
                    return true
                  }
                }
              },
            },
            {
              icon: "jwi-icondelete",
              title: this.$t("btn_delete"),
              key: "delete",
              isShow: (row) => {
                if ((Jw.getUser().systemAdmin || Jw.getUser().tenantAdmin || Jw.getUser().account === row.createBy) && row.type === 'ProcessOrder') {
                  if (
                      row.processState === "draft"
                  ) {
                    return true
                  }
                }
              },
            },
            {
              icon: "jwi-iconstate-set",
              title: this.$t("发送DING消息"),
              key: "ding",
              isShow: (row) => {
                if ((Jw.getUser().systemAdmin || Jw.getUser().tenantAdmin || Jw.getUser().account === row.createBy) && row.type === 'DingTaskRecord') {
                  if (row.processState === "" && row.name.includes("技术文档发布流程")) {
                    return true
                  }
                }
              },
            },
          ],
        },
      ]
    },
  },
  created() {
    this.delaySearch = _.debounce(this.onSearch, 500)
    if (this.pageCode !== "myProcess") {
      this.operationFilter()
    }
  },
  mounted() {},
  methods: {
    operationFilter() {
      let params = {
        objectType: this.$route.query.type,
        objectOid: this.$route.query.oid,
        viewCode: "PROCESSORDER",
      }
      getOperationFilter
        .execute(params)
        .then((res) => {
          if (res) {
            this.permissionBtnList = res.map((item) => {
              if (item.status == "enable") {
                return item.code
              }
            })
          }
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"))
        })
    },
    onSearch() {
      this.$refs.refTable.reFetchData()
    },
    fetchTable({ current, pageSize }) {
      let params = {}
      if (this.pageCode === "myProcess") {
        params = {
          searchKey: this.searchKey,
          index: current,
          size: pageSize,
        }
      } else if (this.pageCode === "processManage") {
        params = {
          catalogOid: this.$route.query.oid,
          catalogType: this.$route.query.type,
          searchKey: this.searchKey,
          index: current,
          size: pageSize,
        }
      } else if (this.pageCode === "processRecord") {
        params = {
          bizOid: this.$route.query.oid,
          bizType: this.$route.query.type,
          searchKey: this.searchKey,
          index: current,
          size: pageSize,
        }
      }
      return ModelFactory.create({
        url: this.listApi,
        method: "post",
      })
        .execute(params)
        .then((res) => {
          return {
            data: res.rows,
            total: res.count,
          }
        })
        .catch((err) => {
          this.$error(err.msg)
        })
    },
    onToolClick({ key }) {
      if (key === "create") {
        this.detailInfo = {}
        this.visible = true
      }
    },
    onToolInput({ key }, value) {
      if (key === "search") {
        this.searchKey = value
        this.delaySearch()
      }
    },
    onOperateClick(key, row) {
      if (key === "start") {
        if (row.processState === "draft") {
          this.onStartProcess(row)
        } else if (row.processState === "hang") {
          this.onActivateProcess(row)
        }
      } else if (key === "pause") {
        this.onHangUpProcecss(row)
      } else if (key === "change") {
        this.onChangeHandler(row)
      } else if (key === "cancel") {
        this.onCancleProcess(row)
      } else if(key === "delete") {
        this.onDeleteProcess(row)
      } else if(key === "ding") {
        sendDING
            .execute({
              processInstanceId: row.processInstanceId,
              dingCorPid:`${Jw.dingCorPid}`
            })
            .then((res) => {
              this.$success(this.$t("msg_success"))
            })
            .catch((err) => {
              this.$error(err.msg)
            })
      }
    },
    onStartProcess(row) {
      this.detailInfo = { ...row }
      this.visible = true
    },
    onActivateProcess(row) {
      activateProcecss
        .execute({
          oid: row.oid,
        })
        .then((res) => {
          this.$success(this.$t("msg_success"))
          this.onSearch()
        })
        .catch((err) => {
          this.$error(err.msg)
        })
    },
    onHangUpProcecss(row) {
      hangUpProcecss
        .execute({
          oid: row.oid,
        })
        .then((res) => {
          this.$success(this.$t("msg_success"))
          this.onSearch()
        })
        .catch((err) => {
          this.$error(err.msg)
        })
    },
    onChangeHandler(row) {
      this.detailInfo = { ...row }
      this.userVisible = true
    },
    onCancleProcess(row) {
      cancelProcecss
        .execute({
          oid: row.oid,
        })
        .then((res) => {
          this.$success(this.$t("msg_success"))
          this.onSearch()
        })
        .catch((err) => {
          this.$error(err.msg)
        })
    },
    onDeleteProcess(row){
      deleteProcess
          .execute([row.oid])
          .then((res) => {
            this.$success(this.$t("msg_success"))
            this.onSearch()
          })
          .catch((err) => {
            this.$error(err.msg)
          })
    },
    onCloseModal() {
      this.visible = false
    },
    onCloseUserModal() {
      this.userVisible = false
    },
    goToDetail(row) {
      if("DingTaskRecord" == row.type) {
        open(this.getDingUrl(row.processInstanceId))
        return
      }
      // 通过当前路由判断是在内容页面，我的流程
      let name = this.$route.name
      let backName = ""
      let isBlank = false
      if (name == "product-content") {
        backName = this.$t("txt_process_management")
        isBlank = false
      }
      if (name == "object-details") {
        backName = this.$t("txt_process_records")
        isBlank = true
        sessionStorage.setItem("currentTabName", "processRecord")
      } else {
        backName = this.$t("txt_my_work")
        isBlank = false
      }
      row.masterType = "ProcessOrder"
      Jw.jumpToDetail(row, {
        hasPermission: true,
        toUrl: `/detailed-task?backName=${backName}`,
        blank: isBlank,
      })
    },
    getDingUrl(instanceId) {
      let url = `https://aflow.dingtalk.com/dingtalk/mobile/homepage.htm?corpid=${Jw.dingCorPid}&dd_share=false&showmenu=false&dd_progress=false&back=native&procInstId=${instanceId}&taskId=&swfrom=isv&dinghash=approval&dtaction=os&dd_from=corp#approval`
      return `dingtalk://dingtalkclient/page/link?url=${encodeURIComponent(url)}&pc_slide=true`
    }
  },
}
</script>

<style lang="less" scoped>
.workflow-page-wrap {
  height: 100%;
  padding: 10px;
  background: #fff;
  .name-wrap {
    display: flex;
    justify-content: space-between;
    .name-con {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      .name-item {
        color: #255ed7;
        cursor: pointer;
      }
    }
  }
}
</style>
