<template>
    <a-modal
        :visible="visible"
        :title="$t('txt_task_handler')"
        width="50%"
        :mask-closable="false"
        @cancel="onCancel"
    >
        <jw-table
			ref="refTable"
			:panel="false"
			:toolbars="toolbars"
			:columns="columns"
            :height="500"
			:data-source.sync="tableData"
			:fetch="fetchTable"
            :showPage="false"
			@onToolInput="onToolInput"
		>
            <template #assigneeUser="{ row }">
                <div class="edit-user-wrap">
                    <jw-avatar tag show-name :data="row.assigneeUser" />
                    <div class="icon-wrap" :title="$t('txt_update_task')">
                        <i class="jwi-iconreplace" @click="onOpenUserModal(row)"></i>
                    </div>
                </div>
            </template>
		</jw-table>
        <template slot="footer">
            <a-button type="primary" :loading="loading" @click="onOk">{{$t('btn_ok')}}</a-button>
            <a-button class="form-btn-cancel" @click="onCancel">{{$t('btn_cancel')}}</a-button>
        </template>
        <jw-user-modal-v2
			ref="user-modal"
			:isCheckbox="ischeckBox"
		/>
    </a-modal>
</template>

<script>
import { jwUserModalV2, jwAvatar } from 'jw_frame';
import ModelFactory from 'jw_apis/model-factory';

// 获取当前任务
const fetchCurrentTask = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/findCurrentTask`,
    method: 'get',
});

// 修改处理人
const batchChangeUser = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.workflowMicroServer}/workflow/runtime/task/delegate/batch`,
    method: 'post',
});

export default {
    name: 'changeHandlerModal',
    components: {
        jwUserModalV2,
        jwAvatar,
    },
    props: [
        'visible',
        'detailInfo',
    ],
    data() {
		return {
            searchKey: '',
			tableData: [],
            ischeckBox: false,
            loading: false,
        }
    },
    watch: {
        visible(val) {
            if (val) {
                this.$nextTick(() => {
                    this.onSearch();
                })
            }
        },
    },
    computed: {
		toolbars() {
			return [
				{
					name: this.$t('btn_search'),
					position: 'before',
					display: 'input',
					value: this.searchKey,
					allowClear: true,
					placeholder: this.$t('search_text'),
					suffixIcon: 'jwi-iconsearch',
					key: 'search',
				},
			]
		},
		columns() {
			return [
				{
					field: 'name',
					title: this.$t('task_name'),
					minWidth: 150,
					cellRender: {
						name: 'link',
					},
				},
				{
					field: 'description',
					title: this.$t('txt_description'),
					minWidth: 160,
				},
				{
					field: 'assigneeUser',
					title: this.$t('txt_current_handler'),
					minWidth: 200,
                    slots: {
                        default: 'assigneeUser',
                    },
				},
			]
		},
	},
    mounted() {

	},
	methods: {
        onSearch() {
			this.$refs.refTable.reFetchData();
		},
		fetchTable({ current, pageSize }) {
			return fetchCurrentTask.execute({
                oid: this.detailInfo.oid,
            }).then((res) => {
                return {
                    data: res,
                }
            }).catch((err) => {
                this.$error(err.msg);
            })
		},
        onToolInput({ key }, value) {
			if (key === 'search') {
				this.searchKey = value;
				this.onSearch();
			}
		},
        onOpenUserModal(row) {
            this.$refs['user-modal'].show({
                type: 'User',
            }).then((data) => {
                row.assigneeUser = data;
            })
        },
        onOk() {
            this.loading = true;
            let params = this.tableData.map(item => {
                return {
                    consignee: item.assigneeUser.account,
                    taskId: item.id
                }
            })
            batchChangeUser.execute(
                params
            ).then((res) => {
                this.$success(this.$t('msg_update_success'));
                this.$emit('getTableData');
                this.onCancel();
                this.loading = false;
            }).catch((err) => {
                this.loading = false;
                this.$error(err.msg);
            });
        },
        onCancel() {
            this.$emit('close');
        },
  	},
}
</script>

<style lang="less" scoped>
.edit-user-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .icon-wrap {
        width: 24px;
        height: 24px;
        line-height:  24px;
        text-align: center;
        background: rgba(30, 32, 42, 0.06);
        border-radius: 4px;
        cursor: pointer;
    }
}
</style>
