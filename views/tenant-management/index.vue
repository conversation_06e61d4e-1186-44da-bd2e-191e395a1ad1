<template>
	<div class="all-background">
		<div class="maintable">
			<div
				class="title-box-statement"
				v-permission="'tenantManagement:threemanagement'"
				style="text-align: right; margin-bottom: 20px"
			>
				<span slot="title">三员管理</span>
				<a-switch
					@change="switchClick"
					v-model.trim="switchStatus"
					checked-children="启用"
					un-checked-children="禁用"
					default-checked
				/>
			</div>
			<a-table
				:columns="columns"
				:pagination="pagination"
				:style="{ height: fullHeight - 125 + 'px' }"
				:scroll="{ y: fullHeight - 190 }"
				:data-source="tableData"
			>
				<template slot="permissionsVoList" slot-scope="permissionsVoList">
					<span v-for="(item, index) in permissionsVoList" :key="index"
						>{{ item.nameZh
						}}{{ permissionsVoList.length - 1 > index ? "、" : "" }}</span
					>
				</template>
				<template slot="operation" slot-scope="text, record">
					<a-button
						type="link"
						block
						v-permission="'permissionsGroup:editor'"
						@click="editorClick(record)"
						>修改</a-button
					>
					<!-- <span
						class="link-name"
						v-permission="'permissionsGroup:delete'"
						@click="deleteClick(record)"
						>{{$t('txt_delete')}}</span
					> -->
				</template>
			</a-table>

			<!-- <div class="card-pagination text-center">
				<a-pagination
					:default-current="currentPage"
					:total="total"
					:show-total="(total) => `共 ${total} 条`"
					show-size-changer
					@change="handleCurrentChange"
					@showSizeChange="handleSizeChange"
				/>
			</div> -->
		</div>
		<formModal
			:title="modalAction == 'create' ? '编辑IP绑定' : '编辑IP绑定'"
			:okText="modalAction == 'create' ? '确认修改' : '确认修改'"
			:data-source="formModalData"
			:visible.sync="modalVisible"
			@confirm="modalCallback"
			:confirmLoadingStatus="confirmLoadingStatus"
		/>
	</div>
</template>

<script>
import formModal from "components/form-modal.vue"
import {
	getThreeUserRole,
	updateThreeAdminIp,
	deleteThreeAdminIp,
	createThreeAdminIp,
} from "../../apis/tenant/index"
import { disableEnable, threeEnable, threStatus } from "../../apis/secret"
export default {
	name: "todoTasks",
	inject: ["setBreadcrumb", "addBreadcrumb"],
	components: {
		formModal,
	},
	data() {
		return {
			route: this.$route.path,
			pagination: false,
			switchStatus: false,
			modalVisible: false,
			confirmLoadingStatus:false,
			modalAction: "create",
			permissionData: [],
			pages: {
				pageIndex: 1,
				pageSize: 10,
				searchKey: null,
				val: null,
			},

			pageSize: 10,
			total: 0,
			currentPage: 1,
			oid: "",
			columns: [
				{
					title: "管理员角色",
					dataIndex: "roleName",
					key: "roleName",
					align: "left",
					ellipsis: true,
					scopedSlots: { customRender: "name" },
				},
				{
					title: "管理员账号",
					dataIndex: "userName",
					key: "userName",
					align: "center",
					ellipsis: true,
				},

				{
					title: "绑定IP地址",
					dataIndex: "ip",
					align: "center",
					key: "ip",
					scopedSlots: { customRender: "permissionsVoList" },
					customRender: (text) => text || "-",
				},

				{
					title: "操作",
					dataIndex: "operation",
					align: "center",
					key: "operation",
					scopedSlots: { customRender: "operation" },
					ellipsis: true,
				},
			],
			tableData: [],
			fullHeight: document.documentElement.clientHeight - 104,
			form: {
				nameLike: "",
				active: false,
				isDueDate: false,
				order: "desc",
				sort: "createTime",
				processInstanceNameLike: "",
			},
			formModalData: [
				{
					label: "管理员角色",
					type: "input",
					value: "",
					block: true,
					disabled: true,
					prop: "adminUser",
				},
				{
					label: "管理员账号",
					block: true,
					type: "input",
					value: "",
					disabled: true,
					prop: "adminAccount",
				},

				{
					label: "绑定IP地址",
					type: "input",
					block: true,
					placeholder: "请输入IP地址",
					codeType: "ipCode",
					required: false,
					value: "",
					prop: "ipAddress",
				},
			],
		}
	},
	methods: {
		initBreadcrumb() {
			let breadcrumbData = [
				{ name: "三员策略管理", path: "/tenant-management" },
			]
			this.setBreadcrumb(breadcrumbData)
		},
		//获取三元管理下的用户列表
		getThreeUserRole() {
			getThreeUserRole.execute().then((res) => {
				this.tableData = res
			})
		},
		//获取三元管理初始状态
		getThreeStatus() {
			threStatus(this.$route.path)
				.execute()
				.then((res) => {
					this.switchStatus = res
				})
		},
		switchClick(e) {
			// console.log(e)
			if (e) {
				threeEnable(this.$route.path)
					.execute()
					.then((res) => {
						this.$success("设置成功")
					})
					.catch((err) => {
						this.$error("操作失败，请重试！")
					})
			} else {
				disableEnable(this.$route.path)
					.execute()
					.then((res) => {
						this.$success("设置成功")
					})
					.catch((err) => {
						this.$error("操作失败，请重试！")
					})
			}
		},
		handleCurrentChange(page, size) {
			this.pages.pageIndex = page
			this.pages.pageSize = size
			this.currentPage = page
			this.getPermissionList()
		},

		handleSizeChange(curren, size) {
			this.pages.pageIndex = curren
			this.pages.pageSize = size
			this.currentPage = curren
			this.getPermissionList()
		},
		//编辑
		editorClick(row) {
			this.$set(this.formModalData, 0, {
				...this.formModalData[0],
				value: row.roleName,
			})
			this.$set(this.formModalData, 1, {
				...this.formModalData[1],
				value: row.userName,
			})
			this.$set(this.formModalData, 2, {
				...this.formModalData[2],
				value: row.ip || "",
			})
			this.oid = row
			this.modalVisible = true
		},
		//确认提交
		modalCallback(modal) {
			this.confirmLoadingStatus = true
			if (!modal.ipAddress) {
				let query = {
					oid: this.oid.oid,
					ip: modal.ipAddress,
				}
				deleteThreeAdminIp(query)
					.execute(query)
					.then((res) => {
						this.$success("修改成功")
						this.modalVisible = false
						this.confirmLoadingStatus = false
						this.getThreeUserRole()
					})
					.catch((err) => {
						this.confirmLoadingStatus = false
						this.$error(err.msg || "保存失败，请重试")
					})

			}else{
				if (this.oid.oid) {
					let query = {
						oid: this.oid.oid,
						ip: modal.ipAddress,
					}
					//更新IP绑定
					updateThreeAdminIp
						.execute(query)
						.then((res) => {
							this.$success("保存成功")
							this.modalVisible = false
							this.confirmLoadingStatus = false
							this.getThreeUserRole()
						})
						.catch((err) => {
							this.confirmLoadingStatus = false
							this.$error(err.msg || "保存失败，请重试")
						})
				} else {
					let parmes = {
						roleId: this.oid.roleId,
						roleName: this.oid.roleName,
						userId: this.oid.userId,
						userName: this.oid.userName,
						ip: modal.ipAddress,
					}
					//创建IP绑定
					createThreeAdminIp
						.execute(parmes)
						.then((res) => {
							this.$success("保存成功")
							this.modalVisible = false
							this.confirmLoadingStatus = false
							this.getThreeUserRole()
						})
						.catch((err) => {
							this.confirmLoadingStatus = false
							this.$error(err.msg || "保存失败，请重试")
						})
				}
			}
			
		},
	},

	mounted() {},
	created() {
		this.initBreadcrumb()
		this.getThreeUserRole()
		this.getThreeStatus()
	},
}
</script>

<style scoped>
.maintable {
	padding: 0px 20px 15px 20px;
	box-sizing: border-box;
	flex: 1;
	overflow-y: auto;
}
.baselist-info {
	padding: 20px;
	display: flex;
	flex-direction: column;
	margin: 5px;
	background: var(--light);
	box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
	border-radius: 4px;
}
</style>
