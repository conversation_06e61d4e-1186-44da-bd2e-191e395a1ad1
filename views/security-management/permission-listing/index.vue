<template>

	<div class="all-background authority-management">
		
		<a-tabs
			:default-active-key="defaultKey"
			:tabBarGutter="10"
			@change="callback"
		>
			<a-tab-pane v-for="pane in panes" :key="pane.key" :tab="pane.title">
				<!-- style="min-height: 500px;max-height:900px" -->
				<div>
					<jw-table
						:columns="columns"
						:toolbars="toolbars"
						:data-source.sync="listingDataSource"
						:pagerConfig="pagerConfig"
						@onPageChange="onPageChange"
						@onSizeChange="onSizeChange"
						@onOperateClick="onOperateClick"
						@onToolClick="onToolClick"
						@onToolInput="onToolInput"
						@checkbox-chang="onSelectChange"
						:selectedRows.sync="selectedRows"
					>
						<template #subPolicyItemList="{ row }">
							<span v-for="(item, index) in row.subPolicyItemList" :key="index">
								<a-tag>{{ item.name }}</a-tag>
							</span>
						</template>
						<template #displayName="{ row }">
							<span v-for="(item, index) in row.roles" :key="index">
								<a-tag>{{ item.displayName||item.name }}</a-tag>
							</span>
						</template>
						<template #allState="{row}">
							{{row.state?row.state:"所有状态"}}
						</template>
						<template #permissionListing="{ row }">
							<span v-for="(item, index) in row.policyItemDtos" :key="index">
								<a-tag>{{ item.name }}</a-tag>
							</span>
						</template>
					</jw-table>
				</div>
			</a-tab-pane>
		</a-tabs>
		<!-- 权限策略 -->
		<!-- <form-modal
			:title="'新建权限策略'"
			:visible.sync="modalVisible"
			@confirm="modalCallback"
			@treeSelect="treeSelect"
			@checkboxAllGroup="checkboxAllGroup"
			:data-source="pormissFromData"
			@clearClick="clearClick"
			:confirmLoadingStatus="confirmLoadingStatus"
		/> -->

		<a-modal
			title="新建"
			:visible="modalVisible"
			width="40%"
			@ok="modalCallback"
			@cancel="handleCancel"
			>
			<a-form-model :model="addParams" layout="vertical" hide-required-mark>
						<a-row :gutter="16">
							
							<a-col :span="12">
								<a-form-model-item label="对象类型">
									<!-- <a-input type="input" v-model.trim="addParams.bizModelType" /> -->
									<a-tree-select
										:labelInValue="true"
										placeholder="请选择"
										v-model.trim="addParams.bizModelType"
										:tree-data="objectTreeData"
										:load-data="objectLoadData"
									>
									</a-tree-select>
								</a-form-model-item>
							</a-col>

							<a-col :span="12">
								<a-form-model-item label="生命周期状态">
									<a-select :labelInValue="true" placeholder="请选择" v-model.trim="addParams.state">
										<a-select-option
											v-for="(val, index) in selectOptions"
											:key="index"
											:value="val.value"
											>{{ val.label }}</a-select-option
										>
									</a-select>
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="角色">
									<a-select :labelInValue="true" :filter-option="filterOption" placeholder="请选择" mode="multiple" v-model.trim="addParams.roles">
										<a-select-option
											v-for="(val, index) in UserRoles"
											:key="index"
											:value="val.value"
											>{{ val.label }}</a-select-option>
									</a-select>
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="权限清单">
									<div  @click="clearClick" class="deleteClacc">清空重选</div>
									<div style="height:170px;overflow: scroll;">
									<a-checkbox-group :value="addParams.policyItemDtos">
										<a-checkbox
											style="
												width: 250px;
												margin-left: 0px;
												padding: 5px 10px;
												background: rgba(30, 32, 42, 0.04);
												margin: 0 8px 8px 0;
											"
											@change="checkboxAllGroup"
											v-for="(val, item) in permissionList"
											:key="item"
											:value="val.oid"
										>
											{{ val.name }}
										</a-checkbox>
									</a-checkbox-group>
									</div>
								</a-form-model-item>
							</a-col>
							
						</a-row>
					</a-form-model>
			</a-modal>
		<!-- 权限清单 -->
		<jw-modal-form
			:key="'1'"
			v-if="addVisible"
			:visible.sync="addVisible"
			:title="title"
			layout="inline"
			:formDatas="JSON.parse(JSON.stringify(addParams))"
			:keys="formModalData"
			@submit="addItem"
		/>
		<!-- 抽屉弹框 -->
		<a-drawer
			width="55%"
			:visible="visible"
			@close="onClose"
		>
			<div style="color: #333; font-size: 16px">权限策略详情</div>
			<a-tabs default-active-key="1">
				<a-tab-pane key="1" tab="详细信息">
					<a-form-model  :model="addParams" layout="vertical" hide-required-mark>
						<a-row :gutter="16">
							<!-- <a-col :span="24">
								<a-form-model-item label="名称">
									<a-input
										type="input"
										v-model.trim="addParams.bizModelType"
									/>
								</a-form-model-item>
							</a-col> -->
							<a-col :span="12">
								<a-form-model-item label="对象类型">
									<!-- <a-input type="input" v-model.trim="addParams.bizModelType" /> -->
									<a-tree-select
										:disabled="true"
										v-model.trim="addParams.bizModelType"
										:tree-data="objectTreeData"
										:load-data="objectLoadData"
									>
									</a-tree-select>
								</a-form-model-item>
							</a-col>

							<a-col :span="12">
								<a-form-model-item label="生命周期状态">
									<a-select v-model.trim="addParams.state">
										<a-select-option
											v-for="(val, index) in selectOptions"
											:key="index"
											:value="val.value"
											>{{ val.label }}</a-select-option
										>
									</a-select>
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="角色">
									<a-select mode="multiple" :filter-option="filterOption"   v-model.trim="addParams.roles">
										<a-select-option
											v-for="(val, index) in UserRoles"
											:key="index"
											:value="val.value"
											>{{ val. label}}
										</a-select-option>
									</a-select>
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="权限清单">
									<div  @click="clearClick" class="deleteClacc">清空重选</div>
									<a-checkbox-group :value="addParams.policyItemDtos">
										<a-checkbox
											style="
												width: 250px;
												margin-left: 0px;
												padding: 5px 10px;
												background: rgba(30, 32, 42, 0.04);
												margin: 0 8px 8px 0;
											"
											@change="checkboxAllGroup"
											v-for="(val, item) in permissionList"
											:key="item"
											:value="val.oid"
										>
											{{ val.name }}
										</a-checkbox>
									</a-checkbox-group>
								</a-form-model-item>
								
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="描述">
									<a-input type="textarea" v-model.trim="addParams.description" />
								</a-form-model-item>
							</a-col>
							<!-- <a-col :span="12">
								<a-form-model-item label="创建者">
									<jw-avatar
										tag
										show-name
										:data="{ name: addParams.createBy, src: '' }"
									/>
								</a-form-model-item>
							</a-col>
							<a-col :span="12">
								<a-form-model-item label="创建时间">
									<a-input :disabled="true" v-model.trim="addParams.createDate" />
								</a-form-model-item>
							</a-col> -->
						</a-row>
					</a-form-model>
				</a-tab-pane>
			</a-tabs>

			<div
				:style="{
					position: 'absolute',
					right: 0,
					bottom: 0,
					width: '100%',
					borderTop: '1px solid #e9e9e9',
					padding: '10px 16px',
					background: '#fff',
					textAlign: 'right',
					zIndex: 1,
				}"
			>
				<a-button :style="{ marginRight: '8px' }" @click="onClose">
					取消
				</a-button>
				<a-button type="primary" @click="editorSubmit"> 确认提交 </a-button>
			</div>
		</a-drawer>
	</div>
</template>

<script>
const codeName = (rule, value, callback) => {
	let reg = /^[a-zA-Z0-9*-]+$/
	if (!reg.test(value)) {
		callback(new Error("仅支持数字、字母、-、*"))
	} else {
		callback()
	}
}
import { jwTable, jwModalForm, jwAvatar } from "jw_frame"
import formModal from "../components/index.vue"
import {
	savePermisssionList,
	deletePermisssionList,
} from "../apis/permissions-group"

import {
	getUserList,
	getLifeStatus,
	getFuzzy,
	getFuzzyByFrom,
	savePermission,
	getPagePermission,
	updatePermission,
	relationshipContains,
	deletePermission,
} from "../apis/index"
import { getCookie } from "jw_utils/cookie"
export default {
	components: {
		jwTable,
		jwModalForm,
		
		formModal,
	},
	inject: ['setBreadcrumb', 'addBreadcrumb'],
	data() {
		return {
			panes: [
				{ title: "权限清单", key: "1" },
				{ title: "权限策略", key: "2" },
			],
			visible: false, //抽屉显示与否
			modalVisible: false, //权限策略新建弹窗显示隐藏
			confirmLoadingStatus: false, //表单提交loading
			tabKey: 1,
			defaultKey: `${this.$route.query.type}` || "1",
			listingDataSource: [], //权限清单渲染数据
			pagerConfig: {
				current: 1,
				pageSize: 20,
				total: 0,
			}, //分页配置
			selectedRows: [], //多选分组
			searchKey: "", //搜索值
			addVisible: false, //控制新增表单弹窗显示隐藏
			addParams: {}, //表单值
			title: "新建清单",
			listPage: {
				index: 1,
				size: 20,
				searchKey: null,
			},
			saveType: "",
			pages: {
				currentPage: 1,
				size: 20,
				searchKey: "",
			},

			objectType: {},
			arrList: [],
			UserRoles: [],
			objectTreeData: [],
			selectOptions: [],
			// 编辑选择对象类型
			objectLoadData: (treeNode) => {
				return new Promise((resolve, reject) => {
					const { key, value } = treeNode.dataRef
					getFuzzyByFrom()
						.execute({ parentOid: key, searchKey: "" })
						.then((result) => {
							this.objectTreeData.find((v) => {
								if (v.key == key) {
									this.$set(
										v,
										"children",
										v.children.concat(
											result.map((t) => ({
												title: t.name,
												value: t.oid,
												key: t.oid,
												selectable: true,
												pid: v.oid, // 父节点id
												isLeaf: true,
											}))
										)
									)
									return true
								}
								return false
							})

							resolve()
						})
						.catch((err) => reject(err))
				})
			},
			pormissFromData: [
				{
					label: "对象类型",
					type: "tree",
					required: true,
					value: "",
					labelInValue: "labelInValue",
					block: false,
					prop: "objectType",
					treeData: [],
					loadData: (treeNode) => {
						return new Promise((resolve, reject) => {
							const { key, value } = treeNode.dataRef
							getFuzzyByFrom()
								.execute({ parentOid: key, searchKey: "" })
								.then((result) => {
									this.pormissFromData[0].treeData.find((v) => {
										if (v.key == key) {
											this.$set(
												v,
												"children",
												v.children.concat(
													result.map((t) => ({
														title: t.name,
														value: t.oid,
														key: t.oid,
														selectable: true,
														pid: v.oid, // 父节点id
														isLeaf: true,
													}))
												)
											)
											return true
										}
										return false
									})

									resolve()
								})
								.catch((err) => reject(err))
						})
					},
				},
				{
					label: "生命周期状态",
					type: "select",
					labelInValue: "labelInValue",
					value: undefined,
					required: false,
					block: false,
					prop: "lifeType",
					options: [],
				},
				{
					label: "角色",
					type: "select",
					required: true,
					labelInValue: "labelInValue",
					value: undefined,
					multiple: true,
					block: true,
					prop: "role",
					options: [],
				},
				{
					label: "权限清单",
					type: "checkbox",
					required: false,
					block: true,
					itemClick:'清空重选',
					prop: "permissionsList",
					options: [],
				},
			],
			formModalData: [
				{
					prop: "logicKey",
					label: "唯一标识",
					rules: [
						{
							required: true,
							message: "请输入",
							trigger: "blur",
						},
						{ validator: codeName, trigger: "blur" },
					],
					itemStyle: "display:inline-block; width:50%; padding-right:10px;",
					props: {
						is: "a-input",
						value: "logicKey",
						placeholder: "唯一标识",
						disabled: false,
					},
				},
				{
					prop: "name",
					label: "显示名称",
					rules: [
						{
							required: true,
							message: "请输入",
							trigger: "blur",
						},
					],
					itemStyle: "display:inline-block; width:50%; padding-right:0px;",
					props: {
						is: "a-input",
						value: "name",
						placeholder: "显示名称",
						disabled: false,
						layout: "horizontal",
					},
				},
				{
					prop: "description",
					label: "描述",
					props: {
						is: "a-input",
						value: "description",
						placeholder: "描述",
						disabled: false,
					},
				},
			],
			columns: [
				{
					title: "唯一标识",
					key: "logicKey", // 点击回调判断唯一值
					field: "logicKey",
				},
				{
					title: "显示名称",
					key: "name",
					field: "name",
				},
				{
					title: "包含项",
					key: "subPolicyItemList",
					slots: {
						default: "subPolicyItemList",
					},
				},
				{
					title: this.$t('txt_description'),
					key: "description",
					field: "description",
				},
				{
					field: "operation",
					btns: [
						{
							title: "编辑",
							key: "editor",
							icon: "jwi-iconedit",
						},
						// {
						// 	title: "删除",
						// 	key: "delete",
						// 	icon: "jwi-icondelete",
						// },
						// {
						// 	title: "国际化",
						// 	key: "international",
						// 	icon: "jwi-iconinternet",
						// },
					],
				},
			],
			//oid
			pormissOid: "",
			roleListArr: [],
			permissionList: [],
			//包含关系所有的列表
			selectBetweenList: [],
		}
	},
	created() {
		let breadcrumbData = [{ name: "权限管理", path: '/permissions-listing' }];
        this.setBreadcrumb(breadcrumbData);
		//权限清单
		this.getPermissionList()
		this.getRoleList()
		this.getLifeStatusList()
		this.getObjiectType()
		//权限策略列表
		// this.getPagePermission()
		//包含项目
		this.selectBetween()
	},
	methods: {
		callback(val) {
			console.log(val)
			this.tabKey = val
			this.searchKey = ""
			// let breadcrumbData = [{ name: this.tabKey==1?'权限清单':'权限策略', path: '/permissions-listing' }];
        	// this.setBreadcrumb(breadcrumbData);
			if (val == 1) {
				this.columns = [
					{
						title: "唯一标识",
						key: "logicKey", // 点击回调判断唯一值
						field: "logicKey",
					},
					{
						title: "显示名称",
						key: "name",
						field: "name",
					},
					{
						title: "包含项",
						key: "subPolicyItemList",
						field: "subPolicyItemList",
						slots: {
							default: "subPolicyItemList",
						},
					},
					{
						title: this.$t('txt_description'),
						key: "description",
						field: "description",
					},
					{
						field: "operation",
						btns: [
							{
								title: "编辑",
								key: "editor",
								icon: "jwi-iconedit",
							},
						],
					},
				]
				this.getPermissionList()

			} else {
				this.getRoleList()
				
				// 权限策略渲染表单
				this.columns = [
					{
						title: "对象类型",
						key: "bizModelType", // 点击回调判断唯一值
						field: "bizModelType",
					},
					{
						title: "角色",
						key: "displayName",
						field: "displayName",
						slots: {
							default: "displayName",
						},
					},
					{
						title: "生命周期状态",
						key: "state",
						field: "state",
						slots: {
							default: "allState",
						},
					},
					{
						title: "权限清单",
						key: "permissionListing",
						field: "permissionListing",
						slots: {
							default: "permissionListing",
						},
					},

					{
						field: "operation",
						btns: [
							{
								title: "编辑",
								key: "editor",
								icon: "jwi-iconedit",
							},
							{
								title: "删除",
								key: "delete",
								icon: "jwi-icondelete",
							},
						],
					},
				]
				this.getPagePermission()
			}
		},
		onSelectChange(args) {
			console.log(args)
		},
		// 权限策略选择权限清单包含关系查询
		selectBetween(oid) {
			relationshipContains()
				.execute()
				.then((res) => {
					this.selectBetweenList = res
				})
		},
		
		//联动处理
		checkboxAllGroup(e) {
			console.log(e)
			let value = e.target.value
			let checked = e.target.checked
			console.log(value, checked)
			let list = this.selectBetweenList
			let arr = list[value]
			console.log(arr)
			if (checked && list[value]) {
				console.log(1)
				this.arrList.push.apply(this.arrList, list[value])
				this.arrList.push(value)
			} else if (!checked && list[value]) {
				// this.arrList.map((v,i)=>{
				// 	if(v==value){
				// 		this.arrList.splice(i,1)
				// 	}
				// })


				console.log(2)
				let listNew = [...new Set(this.arrList)]
				listNew.push(value)
				// arr.push(value)
				// arr.map((v) => {
				// 	listNew.map((val,i) => {
				// 		if (val == v) {
				// 			listNew.splice(i,1)
				// 		}
				// 	})
				// })

				// console.log(listNew)
				this.arrList = listNew
			} else if (checked && !list[value]) {
				console.log(3)
				this.arrList.push(value)
			} else if (!checked && !list[value]) {
				console.log(4)
				// let a = this.arrList
				let index = this.arrList.indexOf(value)
				this.arrList.splice(index, 1)
				// this.arrList = a
			}

			let newArr = [...new Set(this.arrList)]
			this.$set(this.addParams,'policyItemDtos',newArr)
			
		},
		//重新选择
		clearClick(){
			this.arrList= []
			this.$set(this.addParams,'policyItemDtos',[])
			
		},
		// 角色列表
		getRoleList() {
			getUserList()
				.execute({
					containerOid: getCookie("tenantOid"),
					keyword: "",
					containerType: "Tenant",
					pageNum: 1,
					enable:true,
					pageSize: 500,
				})
				.then((res) => {
					let roleList = res.rows.map((v) => ({
						label: v.displayName,
						value: v.oid,
						name: v.displayName
					}))
					this.UserRoles = roleList
					this.roleListArr = res.rows
					this.pormissFromData[2].options = roleList
				})
		},
		filterOption(input, option) {
			return (
				option.componentOptions.children[0].text
					.toLowerCase()
					.indexOf(input.toLowerCase()) >= 0
			)
		},
		// 权限清单列表
		getPermissionList() {
			savePermisssionList("query")
				.execute(this.listPage)
				.then((res) => {
					let permissionList = res.rows.map((v) => ({
						label: v.name,
						value: v.oid,
					}))
					// console.log(permissionList)
					this.permissionList = res.rows
					this.pagerConfig.total = res.count
					this.listingDataSource = res.rows
					this.pormissFromData[3].options = permissionList
				})
				.catch((err) => {
					if (err.msg) {
						this.$error(err.msg)
					}
				})
		},
		//获取权限策略列表
		getPagePermission() {
			let query = {
				index: this.pages.currentPage,
				size: this.pages.size,
				searchKey: this.searchKey,
			}
			getPagePermission()
				.execute(query)
				.then((res) => {
					this.pagerConfig.total = res.count
					// let arr = []
					// this.listingDataSource.map(v=>{
					// 	res.rows.map((k)=>{
					// 		if(k.oid==v.oid){
					// 			k.name=v.name
					// 		}
					// 	})
					// })
					// console.log(res.rows)
					this.listingDataSource = res.rows
				})
		},
		//获取对象类型
		getObjiectType() {
			getFuzzy()
				.execute({ searchKey: "" })
				.then((res) => {
					let arr = res.map((v) => ({
						title: v.name,
						value: v.oid,
						oid: v.oid,
						key: v.oid,
						children: [],
						selectable: true,
						isLeaf: false,
						pid: 0,
					}))

					// 设置 tree-select 一级节点（即对象类型分组）
					this.objectTreeData = arr
					this.pormissFromData[0].treeData=arr
					// this.$set(this.pormissFromData[0], "treeData", arr)
				})
		},
		//类型状态选择去获取生命周期
		treeSelect(value, label, extra) {
			console.log(value, label, extra)
			this.objectType = {
				oid: value,
				name: label[0],
			}
			this.getLifeStatusList(value)
		},
		//获取生命周期状态
		getLifeStatusList(value) {
			getLifeStatus()
				.execute({
					searchKey: "",
				})
				.then((res) => {
					let list = []
					res.map((v) => {
						list.push({
							value: v.code,
							label: v.displayName,
						})
					})
					list.push({label:'all',value:''})
					this.selectOptions = list
					this.$set(this.pormissFromData[1], "options", list)
				})
				.catch((err) => {
					this.$error(err.msg || "获取生命周期状态失败")
				})
		},
		// 操作列回调
		onOperateClick(key, row) {
			console.log(key, row)
			const that = this
			this.title = "编辑"
			if (key === "editor") {
				if (that.tabKey == 2) {
					let list = {
						bizModelType: row.bizModelType,
						state: row.state,
						roles: this.getOid(row.roles),
						policyItemDtos: this.getOid(row.policyItemDtos),
						description: row.description,
						createBy: row.createBy,
						createDate: row.createDate,
					}
					this.pormissOid = row.oid
					this.addParams = list

					this.visible = true
				} else {
					this.addParams = {
						name: row.name,
						oid: row.oid,
						logicKey: row.logicKey,
						description: row.description,
					}
					this.saveType = row.oid
					this.addVisible = true
				}
			} else if (key === "delete") {
				this.$confirm({
					title: "提示",
					content: `确认删除‘${row.bizModelType}’吗？`,
					okText: "确认",
					cancelText: "取消",
					onOk() {
						that.onDelete(row.oid)
					},
				})
			}
		},
		// 获取oid
		getOid(data) {
			let list = []
			if (data && data.length > 0) {
				data.map((v) => {
					list.push(v.oid)
				})
			}
			return list
		},
		handleCancel(){
			this.modalVisible=false
		},
		// 工具栏点击回调
		onToolClick({ key }) {
			this.addParams = {}
			const that = this
			this.title = "新建"
			if (key === "create") {
				this.saveType = ""
				if (this.tabKey == 2) {
					this.arrList = []
					this.modalVisible = true
				} else {
					that.addVisible = true
				}
			} else if (key === "compare") {
				//
			} else if (key === "delete") {
				// this.getUnitManagementList()
			}
		},
		// 工具栏输入回调
		onToolInput({ key }, value) {
			console.log(value)
			if (key === "search" && this.tabKey == 1) {
				this.listPage.searchKey = value
				this.searchKey = value
				this.getPermissionList()
			}else if(key === "search" && this.tabKey == 2){
				this.searchKey = value
				this.getPagePermission()
			}

		},
		// 删除
		onDelete(oid) {
			deletePermission("delete")
				.execute({ oid: oid })
				.then((res) => {
					this.$success("删除成功")
					this.getPagePermission()
				})
				.catch((err) => {
					this.$error(err.msg || "删除失败")
				})
		},
		//创建表单提交事件
		addItem(val) {
			val.oid = this.saveType ? this.saveType : ""

			console.log(val)
			savePermisssionList(this.saveType ? "update" : "create")
				.execute(val)
				.then((res) => {
					// console.log(res);
					this.$success("保存成功")
					this.getPermissionList()
					this.getRoleList()
					this.addVisible = false
				})
				.catch((err) => {
					this.$error(err.msg || "保存失败")
				})
		},
		//抽屉开关
		onClose() {
			this.visible = false
		},
		//获取全量数据
		allData(data, value) {
			console.log(data)
			let list = []
			if (value && value.length > 0) {
				data.map((v) => {
					value.map((k) => {
						console.log(v.oid, k)
						if (v.oid == k) {
							console.log(v)
							list.push(v)
						}
					})
				})
			}

			return list
		},
		//编辑表单提交
		editorSubmit() {
			let aray = this.addParams
			let query = {
				// bizModelType: aray.bizModelType,
				// bizModelOid: this.objectTreeData.map((v) => {
				// 	if(v.name==aray.bizModelType){
				// 		return v.oid
				// 	}
				// }),
				oid: this.pormissOid,
				state: aray.state?aray.state:'allState',
				roles: this.allData(this.roleListArr, aray.roles),
				policyItemDtos: this.allData(this.permissionList, aray.policyItemDtos),
				description: aray.description,
			}
			console.log(query)
			// return
			updatePermission("update")
				.execute(query)
				.then((res) => {
					this.getPagePermission()
					this.getRoleList()
					this.$success("保存成功")
					this.visible = false
				})
				.catch((err) => {
					this.$error(err.msg || "修改失败")
				})
		},
		// 新建权限策略保存
		modalCallback(val) {
			console.log(val)
			console.log(this.addParams)
			// return
			let policyItemDtos = []
			this.permissionList.map((v) => {
				this.addParams.policyItemDtos.map((k) => {
					if (k == v.oid) {
						policyItemDtos.push(v)
					}
				})
			})
			console.log("策略新增清单12", policyItemDtos)
			let query = {
				bizModelOid: this.addParams.bizModelType.value,
				bizModelType: this.addParams.bizModelType.label,
				state:  this.addParams.state?this.addParams.state.key:'',
				policyItemDtos: policyItemDtos,
				roles: this.addParams.roles
					?  this.addParams.roles.map((v) => ({
							name: v.label,
							oid: v.key,
					  }))
					: [],
			}

			// return
			savePermission()
				.execute(query)
				.then((res) => {
					this.$success("保存成功")
					this.getPagePermission()
					this.modalVisible = false
				})
				.catch((err) => {
					this.$error(err.msg || "保存失败")
				})
		},
		// 时间戳转换
		formatDate(time) {
			console.log(date)

			let date = new Date(time)
			//  var date = new Date(timestamp * 1000);//时x/间戳为10位需*1000，时间戳为13位的话不需乘1000
			let Y = date.getFullYear() + "-"
			let M =
				(date.getMonth() + 1 < 10
					? "0" + (date.getMonth() + 1)
					: date.getMonth() + 1) + "-"
			let D = date.getDate() + " "
			return Y + M + D
		},
		//分页操作
		onPageChange(page, pageSize) {
			console.log(pageSize, page)
			if (this.tabKey == 1) {
				//清单分页
				this.listPage = {
					index: page,
					size: pageSize,
					searchKey: this.pages.searchKey,
				}
				this.getPermissionList()
			} else {
				//策略分页
				this.pages = {
					currentPage: page,
					size: pageSize,
					searchKey: this.pages.searchKey,
				}
				this.getPagePermission()
			}
			this.pagerConfig.current = page
			this.pagerConfig.pageSize = pageSize
		},
		onSizeChange(pageSize, page) {
			if (this.tabKey == 1) {
				console.log(pageSize, page)
				//清单分页
				this.listPage = {
					index: page,
					size: pageSize,
					searchKey: this.pages.searchKey,
				}
				this.getPermissionList()
			} else {
				//策略分页
				this.pages = {
					currentPage: page,
					size: pageSize,
					searchKey: this.pages.searchKey,
				}
				this.getPagePermission()
			}
			this.pagerConfig.current = page
			this.pagerConfig.pageSize = pageSize
		},
	},
	computed: {
		toolbars() {
			return [
				{
					name: "新建",
					position: "before",
					type: "primary",
					key: "create",
					// prefixIcon: "jwi-plus",
				},
				{
					name: "搜索",
					position: "before",
					display: "input",
					value: this.searchKey,
					allowClear: true,
					placeholder: this.tabKey==1?'唯一标识、名称':'对象类型、角色',
					prefixIcon: "jwi-search",
					key: "search",
				},
				{
					name: "",
					position: "after",
					key: "compare",
					prefixIcon: "jwi-iconfilter",
				},
				// {
				// 	name: "批量操作",
				// 	display: "dropdown",
				// 	position: "after",
				// 	key: "batch",
				// 	menuList: [
				// 		{
				// 			name: "删除",
				// 			key: "delete",
				// 		},
				// 	],
				// },
				{
					name: "导入",
					position: "after",
					type: "",
					key: "import",
					// prefixIcon: "jwi-plus",
				},
				{
					name: "导出",
					position: "after",
					type: "",
					key: "export",
					// prefixIcon: "jwi-plus",
				},
			]
		},
	},
}
</script>
<style scoped>
.deleteClacc{
		text-align: right;
		color: blue;
		font-size: 14px;
		cursor: pointer;
		margin-bottom: 10px;
		padding-right:20px ;
	}
</style>
<style lang="less">
.authority-management{
	padding: 0 20px;
	.ant-tabs {
		height: 100%;
		display: flex;
    	flex-direction: column;
		.ant-tabs-content {
			height: 20px;
			flex-grow: 1;
				.ant-tabs-tabpane{
					height: 100%;
					>div{
						height: 100%;
					}
				}
		}
	}
}
</style>
