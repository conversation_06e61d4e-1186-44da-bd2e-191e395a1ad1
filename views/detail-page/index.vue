<!--
 * 公共界面处理详情通用的逻辑
   1.鉴权 2.记录日志 3.判断跳转查看 还是编辑详情 4.将当前实例 根据masterType session缓存

   /** 内部不能区分的 外部传入
     * viewCode：权限code
     * toUrl:目标路由
     * hasPermission：是否需要鉴权【ture false】非必填
     * blank：是否新开页签,
     * history:历史记录
     *
    */
-->
<template>
  <div class="empty-page" v-loading='loading'>
    <router-view :key="currentOid"></router-view>
  </div>
</template>
<script>
import Row_Store from "jw_stores/instance-info";

import ModelFactory from "jw_apis/model-factory";
import { findDetail } from "apis/baseapi";
// 通用权限接口
const permissionApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
  method: "post"
});

// 新建浏览记录埋点
const browsingHistoryApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.sysconfigServer}/homepage/create/browsingHistory`,
  method: "post"
});


// 根据实例的父类型映射 viewCode 【无法区分的外部传入】
const modelMapviewCode = Jw.modelMapviewCode;

// 容器详情 根据容器的modelDefinition映射 【无法区分的外部传入】
const ContainerMapRoute = Jw.ContainerMapRoute;

// 实例详情 根据 【容器的modelDefinition映射->实例的masterType映射->单独处理】
const instanceMapRoute = Jw.instanceMapRoute;

let instanceInfo = {};
let containerInfo = {};

// 根据实例数据匹配目标路由
const mapTargetRoute = instanceInfo => {
  let targetPath = "";
  let type = instanceInfo.masterType;

  if (instanceInfo.toUrl) {
    return instanceInfo.toUrl;
  }
  // 容器详情 根据容器的modelDefinition映射
  if (instanceInfo.type == "Container") {
    targetPath = ContainerMapRoute[instanceInfo.modelDefinition];
  } else {
    //实例详情 根据 【实例的masterType映射，容器的modelDefinition映射】
    targetPath =
      instanceMapRoute[instanceInfo.masterType] ||
      instanceMapRoute[containerInfo.modelDefinition];
  }
  // 单独处理
  if (
    containerInfo.modelDefinition == "ProcessPlanContainer" &&
    instanceInfo.masterType == "Part"
  ) {
    let key = `${instanceInfo.masterType}.${instanceInfo.viewName}`;
    targetPath = instanceMapRoute[key];
  }
  return targetPath;
};

// 跳转目标路由
const toInfoPage = async (instanceInfo, next, that) => {
  let query = null;
  //流程实例详情界面
  if (instanceInfo.masterType == "ProcessOrder") {
    query = {
      processOrderOid: instanceInfo.oid,
      processInstanceId: instanceInfo.processInstanceId
    };
  } else {
    query = {
      oid: instanceInfo.oid,
      type: instanceInfo.type,
      masterType: instanceInfo.masterType,
      modelDefinition: instanceInfo.modelDefinition,
      tabActive: instanceInfo.tabActive,
      processDefinitionId:instanceInfo.processDefinitionId,
      name:instanceInfo.name
    };
  }

  Row_Store.set(`${instanceInfo.masterType}`, instanceInfo);
  if (instanceInfo.type != "Container" && instanceInfo.containerOid) {
    await getContainerInfo(instanceInfo);
  }
  let targetPath = mapTargetRoute(instanceInfo);
  let path = "/detailPage" + targetPath;

  if (instanceInfo.blank) {
    const url = Jw.mainView.$router.resolve({
      path,
      query
    });
    window.open(url.href, "_blank");
  } else {
    next({ path, query });
  }

  browsingHistoryApi
    .execute({ bizOid: instanceInfo.oid, bizType: instanceInfo.type })
    .catch(err => {
      console.log("记录浏览详情接口异常");
    });
};

// 鉴权
const checkPermission = async (instanceInfo, next, that) => {
  let viewCode =
    instanceInfo.viewCode || modelMapviewCode[instanceInfo.masterType];
  let checkPermissionParams = {
    viewCode,
    objectOid: instanceInfo.oid
  };
  await permissionApi
    .execute(checkPermissionParams)
    .then(res => {
      let has = res.find(p => p.code === "details");
      if (has && has.status === "enable") {
        toInfoPage(instanceInfo, next, that);
      } else {
        next({
          path: "/noPermission"
        });
      }
    })
    .catch(err => {
      next({
        path: "/noPermission"
      });
    });
};

const getContainerInfo = instanceInfo => {
  return findDetail
    .execute({
      oid: instanceInfo.containerOid || instanceInfo.oid,
      type: instanceInfo.containerType || "Container"
    })
    .then(res => {
      containerInfo = res;
    });
};

export default {
  beforeRouteEnter(to, from, next) {
    if (to.path == "/detailPage") {

      instanceInfo = { ...to.params, ...to.query };
      // 实例的父类型masterType【没有父类型 取 modelDefinition】
      if (!instanceInfo.masterType) {
        //单独处理issue问题
        if(instanceInfo.type === "Issue"){
          instanceInfo.masterType = instanceInfo.type;
        }else{
          instanceInfo.masterType = instanceInfo.modelDefinition;
        }

      }
      if (instanceInfo.hasPermission||instanceInfo.type == "Container"||instanceInfo.type=="ProcessOrder")  {
        toInfoPage(instanceInfo, next);
      } else {
        checkPermission(instanceInfo, next);
      }
    } else {
      next();
    }
  },
  beforeRouteUpdate(to, from, next) {
    if (to.path == "/detailPage") {
      instanceInfo = { ...to.params };
      // 实例的父类型masterType【没有父类型 取 modelDefinition】
      if (!instanceInfo.masterType) {
        instanceInfo.masterType = instanceInfo.modelDefinition;
      }
      // 切换历史数据 tab 栏还原
      if (from.query.tabActive && !instanceInfo.history) {
        instanceInfo.tabActive = from.query.tabActive;
      }

      // oid 不变 路由无法刷新
      if (instanceInfo.oid == from.query.oid) {
        this.currentOid = instanceInfo.oid + ++this.random;
        this.$set(this.queryObj, "refresh", true);
      }

      if (instanceInfo.hasPermission||instanceInfo.type == "Container"||instanceInfo.type=="ProcessOrder") {
        toInfoPage(instanceInfo, next);
      } else {
        checkPermission(instanceInfo, next);
      }
    } else {
      next();
      if (from.path == to.path) {
        this.currentOid = instanceInfo.oid + ++this.random;
      }
    }
  },
  watch: {},
  data() {
    return {
      loading: false,
      currentOid: "",
      random: 1,
      containerInfo: null,
      queryObj: {}
    };
  },

  provide: function() {
    return {
      rootInfo: this
    };
  },
  created() {
    window.localStorage.setItem("index_", 2)
  },
  computed: {
    isEdit() {
      let { oid, masterType } = (this.queryObj = this.$route.query);
      let storeRow = Row_Store.get(`${masterType}`);
      storeRow = storeRow.oid == oid ? storeRow : null;
      if (storeRow) {
        // 有检入逻辑的根据实例信息判断
        if (storeRow.lockSourceOid) {
          return true;
        }
        // 无检入逻辑的外部传入
        if (storeRow.layoutName == "update") {
          return true;
        }
      }

      return false;
    },
    layoutName() {
      return this.isEdit ? "update" : "show";
    }
  },

  methods: {}
};
</script>
<style scoped>
.empty-page {
  background: #fff;
  height: 100%;
}
</style>


