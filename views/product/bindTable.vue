<template>
    <div class="binding-table">
        <div class="binding-table-header">
            <div class="table-header-left flex justify-start align-center">
                <span class="teat-title">团队001</span>
                <svg class="jwifont" aria-hidden="true">
                    <use xlink:href="#jwi-edit"></use>
                </svg>
            </div>
            <div class="table-header-right">
                <a-radio-group class="browse-wrap" default-value="all" v-model.trim="team"
                    button-style="solid" @change="changeTeam">
                    <a-radio-button value="all">All</a-radio-button>
                    <a-radio-button v-for="item in 4" :key="item" :value="item">{{item}}</a-radio-button>
                </a-radio-group>
                <a-input-search placeholder="输入关键词搜索" class="header-search-input" @search="onSearch" />
            </div>
        </div>
        <div class="binding-table-con" style="height: calc(100vh - 182px)">
            <a-table
                :data-source="treeData"
                :defaultExpandAllRows="true"
                :expandIcon="expandIcon"
                :scroll="{ x: 750, y: 'calc(100vh - 270px)' }"
                :pagination="false">
                <a-table-column title="对象名称" dataIndex="name" key="name" :width="200" ellipsis="true">
                    <template slot-scope="text, record">
                        <svg class="jwifont" aria-hidden="true">
                            <use :xlink:href="record.flag==='成品'?'#jwi-end-product':'#jwi-part'"></use>
                        </svg>
                        <span>{{ text }}</span>
                    </template>
                </a-table-column>
                <a-table-column title="同步子集" dataIndex="isSync" key="isSync" align="center" :width="90">
                    <template slot-scope="text, record">
                        <a-switch :default-checked="record.isSync" v-if="record.flag==='成品'">
                            <a-icon slot="checkedChildren" type="check" />
                            <a-icon slot="unCheckedChildren" type="close" />
                        </a-switch>
                    </template>
                </a-table-column>
                <a-table-column title="None" dataIndex="None" key="None" align="center" :width="90">
                    <template slot-scope="text, record">
                        <div class="auth1-wrap">
                            <div :class="['auth-dot',record.currentStep>0?'dot-check':'']"
                                @click="clickAuth(record, 1)"
                                @mouseenter="mouseenterAuth(record, 1)"
                                @mouseleave="mouseleaveAuth(record, 1)">
                                <a-icon v-if="record.currentStep>0" type="check" class="auth-check" />
                            </div>
                        </div>
                    </template>
                </a-table-column>
                <template v-for="item in authList">
                    <a-table-column :title="item.value" :dataIndex="item.value" :key="item.value" align="center" :width="90">
                        <template slot-scope="text, record">
                            <div class="flex align-center" style="margin-left:calc(-50% + 8px);">
                                <div :class="['auth-line',record.currentStep>item.level?'line-check':'']"></div>
                                <div :class="['auth-dot',record.currentStep>item.level?'dot-check':'']"
                                    @click="clickAuth(record, item.level+1)"
                                    @mouseenter="mouseenterAuth(record, item.level+1)"
                                    @mouseleave="mouseleaveAuth(record, item.level+1)">
                                    <a-icon v-if="record.currentStep>item.level" type="check" class="auth-check" />
                                </div>
                            </div> 
                        </template>
                    </a-table-column>
                </template>
            </a-table>
        </div>
    </div>
</template>

<script>
const treeData = [
    {
        name: '成品001',
        key: '1',
        flag: '成品',
        isSync: false,
        clickFlag: true,
        currentStep: 2,
        children: [
            {
                name: '零件1-1',
                key: '1-1',
                flag: '零件',
                clickFlag: true,
                currentStep: 2,
                children: [
                    { name: '零件1-1-1', key: '1-1-1', flag: '零件', clickFlag: true, currentStep: 1 },
                    { name: '零件1-1-2', key: '1-1-2', flag: '零件', clickFlag: true, currentStep: 1 },
                ],
            },
        ],
    },
    {
        name: '成品002',
        key: '2',
        flag: '成品',
        isSync: true,
        clickFlag: true,
        currentStep: 3,
        children: [
            {
                name: '零件2-1',
                key: '2-1',
                flag: '零件',
                clickFlag: true,
                currentStep: 2,
                children: [
                    { name: '零件2-1-1', key: '2-1-1', flag: '零件', clickFlag: true, currentStep: 1 },
                    { name: '零件2-1-2', key: '2-1-2', flag: '零件', clickFlag: true, currentStep: 0 },
                ],
            },
        ],
    },
    {
        name: '成品003',
        key: '3',
        flag: '成品',
        isSync: false,
        clickFlag: true,
        currentStep: 0,
        children: [
            {
                name: '零件3-1',
                key: '3-1',
                flag: '零件',
                clickFlag: true,
                currentStep: 1,
                children: [
                    { name: '零件3-1-1', key: '3-1-1', flag: '零件', clickFlag: true, currentStep: 1 },
                    { name: '零件3-1-2', key: '3-1-2', flag: '零件', clickFlag: true, currentStep: 2 },
                    { name: '零件3-1-3', key: '3-1-3', flag: '零件', clickFlag: true, currentStep: 3 },
                    { name: '零件3-1-4', key: '3-1-4', flag: '零件', clickFlag: true, currentStep: 4 },
                ],
            },
        ],
    },
];
export default {
    name: 'bindingTable',
    props: [
        
    ],
    components: {

    },
    data() {
        return {
            team: 'all',
            authList: [
                {level: 1, value: 'Scan'},
                {level: 2, value: 'View'},
                {level: 3, value: 'Edit'},
                {level: 4, value: 'Manage'},
            ],
            treeData,
        };
    },
    computed: {
    },
    watch: {},
    methods: {
        changeTeam() {

        },
        onSearch() {

        },
        expandIcon(props) {
            if (props.record.children && props.record.children.length > 0) {
                if (props.expanded) { // 有数据-展开时候图标
                    return (
                        <span style="margin:0 8px 0 16px"
                            onClick = {(e) => { props.onExpand(props.record, e); }}>
                            <a-icon type="caret-down" />
                        </span>
                    );
                } else { // 有数据-未展开时候图标
                    return (
                        <span style="margin:0 8px 0 16px"
                            onClick = {(e) => { props.onExpand(props.record, e); }}>
                            <a-icon type="caret-right" />
                        </span>
                    );
                }
            } else { // 无数据-图标
                return (
                    <span style="margin-left:40px;"></span>
                );
            }
        },
        clickAuth(row, index) {
            if (index === row.currentStep && index === row.dftAuth) {
                row.currentStep = 0;
                row.clickFlag = false;
            } else {
                row.currentStep = index;
                row.clickFlag = true;
            }
            row.dftAuth = row.currentStep;
        },
        mouseenterAuth(row, index) {
            if (!row.clickFlag) {
                return;
            }
            row.dftAuth = row.currentStep;
            row.currentStep = index;
        },
        mouseleaveAuth(row, index) {
            row.clickFlag = true;
            row.currentStep = row.dftAuth;
        },
    },
    created() {
        
    },
    mounted() {

    },
};
</script>

<style lang="less" scoped>
.binding-table {
    .jwifont {
        width: 13px;
        height: 13px;
    }
    .binding-table-header {
        padding: 10px 20px;
        height: 52px;
        border-bottom: 1px solid rgba(30,32,42,0.15);
        display: flex;
        justify-content: space-between;
        .table-header-left {
            .teat-title {
                margin-right: 10px;
                color: #000;
            }
            .jwifont {
                min-width: 14px;
            }
        }
        .table-header-right {
            .header-search-input {
                width: 216px;
                margin-left: 8px;
            }
            .ant-radio-button-wrapper {
                width: 32px;
                height: 32px;
                line-height: 30px;
                padding: 0;
                text-align: center;
            }
        }
    }
    .binding-table-con {
        padding: 20px;
        /deep/.ant-table-thead > tr > th {
            height: 48px;
            line-height: 48px;
            padding: 0 16px;
            background: rgba(30, 32, 42, 0.04);
            border-bottom: 0;
        }
        /deep/.ant-table-tbody > tr > td {
            position: relative;
            height: 54px;
            line-height: 54px;
            padding: 0;
        }
        .jwifont {
            margin-right: 5px;
        }
        .auth1-wrap {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .ant-switch-checked {
            background-color: #255ed7;
        }
        .auth-dot {
            position: relative;
            padding: 1px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: rgba(30, 32, 42, 0.06);
            text-align: left;
            cursor: pointer;
            &.dot-check {
                border: 3px solid #a4c9fc;
                background: #255ed7;
            }
            .auth-check {
                position: absolute;
                font-size: 8px;
                color: #fff;
            }
        }
        .auth-line {
            width: calc(70% - 13px);
            height: 3px;
            min-height: 3px;
            background: rgba(30, 32, 42, 0.06);
            &.line-check {
                background: #a4c9fc;
            }
        }
    }
}
</style>
