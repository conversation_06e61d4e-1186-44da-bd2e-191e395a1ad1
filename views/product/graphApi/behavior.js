// 点选项目
export default G6 => {
	// 注册hover 交互
	G6.registerBehavior('hover-node', {
		getEvents() {
			return {
				'node:mouseenter': 'onNodeEnter',
				'node:mouseleave': 'onNodeLeave',
			};
		},
		onNodeEnter(e) {
			// 显示当前节点的锚点
			const graph = this.graph;
			const item = e.item;
			graph.setItemState(item, 'hover', true);
		},
		onNodeLeave(e) {
			// 将锚点再次隐藏
			const graph = this.graph;
			const item = e.item;
			graph.setItemState(item, 'hover', false);
		},
	});
};
