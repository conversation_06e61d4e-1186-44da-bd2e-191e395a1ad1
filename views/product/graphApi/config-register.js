/**
 * g6 流程图构造器
 * 将G6封装后返回
 */

import registerFactory from './register-factory';

export default (G6, config) => {
	const container = document.getElementById(config.container);
	const width = container.scrollWidth;
	const height = container.scrollHeight || 150;
	const options = Object.assign({
		container: 'container',
		width,
		height,
		linkCenter: true,
		renderer: 'svg',
		modes: {
			default: ['drag-canvas', 'zoom-canvas', 'hover-node'],
		},
		maxZoom: '2',
		minZoom: '0.65',
		defaultNode: {
			type: 'tree-node',
			fill: '#fff',
		},
		nodeStateStyles: {
			cancelHover:{
				stroke: 'rgba(30, 32, 42, 0.15)',
				lineWidth: 1,
			},
			hover: {
				stroke: '#1890ff',
				lineWidth: 1,
			},
		},
		defaultEdge: {
			type: 'flow-line',
			style: {
				stroke: 'rgba(30, 32, 42, 0.15)',
			}
		},
		layout: {
			type: 'compactBox',
			direction: 'TB',
			getId: function getId(d) {
				return d.id;
			},
			getHeight: function getHeight() {
				return 16;
			},
			getWidth: function getWidth() {
				return 16;
			},
			getVGap: function getVGap() {
				return 40;
			},
			getHGap: function getHGap() {
				return 100;
			}
		}
		},
		config
	);

	const el =
		typeof options.container === 'string' ?
		document.getElementById(options.container) :
		options.container;

	if (el) {
		setTimeout(() => {
			for (let i = 0; i < el.children.length; i++) {
				const dom = el.children[i];
				if (dom.nodeName === 'CANVAS') {
					dom.$id = `${options.container}-canvas`;
					dom.setAttribute('dx', 0);
					dom.setAttribute('dy', 0);

					/* 监听键盘事件 */
					document.addEventListener('click', (e) => {
						// 内部键盘事件是否可被触发
						dom.setAttribute('isFocused', e.target.$id === dom.$id);
					});
					break;
				}
			}
		});
		// 注册自定义节点/边等
		registerFactory(G6);
	} else {
		console.warn('未找到注册节点!');
	}
	return options;
};
