export default G6 => {
	G6.registerNode('tree-node', {
		draw: (cfg, group) => {
			const r = 4;
			const color = 'rgba(30, 32, 42, 0.15)';
			const w = cfg.size[0];
			const h = cfg.size[1];

			const keyShape = group.addShape('rect', {
				attrs: {
					x: -w / 2,
					y: -h / 2,
					width: w,
					height: h,
					stroke: color,
					lineWidth: 1,
					radius: r,
					...cfg.style,
				},
				name: 'box',
			});

			if (cfg.name.indexOf('团队') > -1) {
				group.addShape('dom', {
					attrs: {
						x: -w / 2 + 12,
						y: -8,
						width: 16,
						height: 16,
						html: `
							<svg class="jwifont bind-icon-back" aria-hidden="true"
								style="width:16px;height:16px;">
								<use xlink:href="#jwi-user-groups"></use>
							</svg>
						`,
					},
				});
				group.addShape('text', {
					attrs: {
						text: '...',
						x: w / 2 - 30,
						y: 8,
						fill: 'rgba(0, 0, 0, 1)',
						fontSize: 20,
						cursor: 'pointer',
					},
					name: 'more-icon',
				});
			}
			
			if (cfg.name.indexOf('用户') > -1) {
				group.addShape('dom', {
					attrs: {
						x: -w / 2 + 12,
						y: -8,
						width: 16,
						height: 16,
						html: `
							<svg class="jwifont bind-icon-back" aria-hidden="true"
								style="width:16px;height:16px;">
								<use xlink:href="#jwi-uesr"></use>
							</svg>
						`,
					},
				});
				group.addShape('dom', {
					attrs: {
						x: w / 2 - 30,
						y: -8,
						width: 16,
						height: 16,
						html: `
							<svg class="jwifont bind-icon-back" aria-hidden="true"
								style="width:16px;height:16px;cursor:pointer;" @click="onDelete">
								<use xlink:href="#jwi-close-circle-fill"></use>
							</svg>
						`,
					},
					name: 'delete-icon',
				});
			}

			group.addShape('text', {
				attrs: {
					text: cfg.name.length > 5 ?
						cfg.name.substr(0, 5) + '...' : cfg.name,
					x: -w / 2 + 36,
					y: 10,
					fill: 'rgba(30, 32, 42, 0.85)',
					fontSize: 14,
				},
				name: 'title',
			});
			return keyShape;
		},
		setState(name, value, item) {
			if (name === 'hover') {
				const attrs = item.get('group').getFirst().attr();
				const box = item.get('group').find(ele => ele.get('name') === 'box');
				const hoverStyle = value ? attrs.hover : attrs.cancelHover;
				box.attr(hoverStyle);
			}
		}
	});
	G6.registerEdge('flow-line', {
		draw(cfg, group) {
			const startPoint = cfg.startPoint;
			const endPoint = cfg.endPoint;

			const { style } = cfg;
			const shape = group.addShape('path', {
				attrs: {
					stroke: style.stroke,
					path: [
						['M', startPoint.x, startPoint.y + 27],
						['L', startPoint.x, (startPoint.y + endPoint.y) / 2],
						['L', endPoint.x, (startPoint.y + endPoint.y) / 2],
						['L', endPoint.x, endPoint.y - 27]
					]
				}
			});
			return shape;
		}
	});
};
