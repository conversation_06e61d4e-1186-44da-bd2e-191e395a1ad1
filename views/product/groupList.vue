<template>
    <div class="group-list-wrap">
        <div class="group-head">自定义分组1</div>
        <div class="group-body">
            <div class="body-top flex justify-between">
                <a-popover
                    trigger="click"
                    v-model.trim="visible"
                    placement="bottomLeft"
                >
                    <div slot="title" class="font-700">新建分组</div>
                    <template slot="content">
                        <a-form-model
                            ref="'createForm"
                            :model="form"
                            :rules="rules"
                        >
                            <a-form-model-item
                                label="分组名称"
                                prop="name">
                                <a-input
                                    v-model.trim="form.name"
                                    :max-length="50"
                                    allow-clear
                                    placeholder="请填写分组名称" />
                            </a-form-model-item>
                            <a-form-model-item class="text-right">
                                <a-button type="primary" @click="onSubmit">
                                    确认添加
                                </a-button>
                            </a-form-model-item>
                        </a-form-model>
                    </template>
                    <a-button type="primary" class="add-btn">新建分组</a-button>
                </a-popover>
                <a-auto-complete
                    v-model.trim="searchKey"
                    placeholder="请输入搜索关键字"
                    @search="onSearch"
                    @blur="onSearch"
                >
                    <template slot="dataSource">
                        <a-select-option
                            v-for="item in dataSource"
                            :key="item.id">
                            {{ item.name }}
                        </a-select-option>
                    </template>
                    <a-input>
                        <a-icon slot="suffix" type="search" />
                    </a-input>
                </a-auto-complete>
            </div>
            <div class="body-con">
                <div class="pro-item flex align-center"
                    v-for="item of groupData"
                    :key="item.id">
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#jwi-tag"></use>
                    </svg>
                    <span class="text-ellipsis">{{ item.name }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'groupList',
    props: [

    ],
    data() {
        return {
            searchKey: '',
            dataSource: [],
            groupData: [],
            visible: false,
            form: {
                name: '',
            },
            rules: {
                name: [
                    { required: true, message: '请填写分组名称', trigger: 'blur' },
                ],
            },
        };
    },
    mounted() {
        for (let i = 0; i < 35; i++ ) {
            this.groupData.push({
                id: `${i}`,
                name: `产品名称 ${i}`,
            })
        }
    },
    methods: {
        onSearch() {

        },
        onSubmit() {
            this.$refs.createForm.validate((valid) => {
                if (valid) {
                    this.form.name = '';
                    this.visible = false;
                } else {
                    return false;
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
.group-list-wrap {
    background: var(--light);
    box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
    border-radius: 4px;
    .group-head {
        padding: 0 20px;
        margin-bottom: 13px;
        height: 52px;
        line-height: 52px;
        font-size: 14px;
        box-sizing: border-box;
        border-bottom: 1px solid rgba(30, 32, 42, 0.15);
    }
    .group-body {
        height: calc(100vh - 64px - 40px - 65px);
        .body-top {
            padding: 0 20px;
            .add-btn {
                width: 80px;
                margin-right: 8px;
            }
            .ant-select-auto-complete {
                width: 100%;
            }
        }
        .body-con {
            margin: 10px 0px;
            padding: 0 20px;
            height: calc(100vh - 64px - 40px - 65px - 52px);
            overflow-y: auto;
            &::-webkit-scrollbar{
                width: 1px;
            }
            .pro-item {
                margin-bottom: 10px;
                .icon {
                    width: 16px;
                    min-width: 16px;
                    height: 16px;
                    min-height: 16px;
                    margin-right: 12px;
                }
            }
        }
    }
}
</style>
