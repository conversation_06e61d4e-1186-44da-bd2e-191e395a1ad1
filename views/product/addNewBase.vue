<template>
  <div>
    <a-modal
      :visible="visibleVal"
      :confirm-loading="confirmLoading"
      class="alert-model"
      :footer="null"
      centered
      @cancel="cancerBtn"
    >
      <span slot="title" class="tltles">{{ baselinetitle }}</span>
      <a-form-model
        ref="ruleForm"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
        labelAlign="left"
      >
        <a-form-model-item prop="name" :label="$t('txt_name')">
          <a-input v-model.trim="form.name" :placeholder="$t('msg_input')" />
        </a-form-model-item>

        <a-form-model-item ref="oid" prop="oid" :label="$t('txt_seletct_baseline')">
          <a-select
            :placeholder="$t('msg_select')"
            v-model.trim="form.oid"
            class="baseline-select"
          >
            <a-select-option value="jack">Jack</a-select-option>
            <a-select-option value="lucy">Lucy</a-select-option>
            <a-select-option value="Yiminghe">yiminghe</a-select-option>
          </a-select>
        </a-form-model-item>

        <a-form-model-item prop="effectdate" :label="$t('txt_start_date')">
          <a-date-picker
           @change="ondateChange"
            class="date"
            v-model.trim="form.effectdate"
            :format="dateFormat"
          />
        </a-form-model-item>

       
        <div class="comfirm-btn-info">
          <a-button type="primary" class="comfirm-btn" @click="comfirmAdd"
            >{{$t('btn_ok')}}</a-button
          >
          <a-button class="cancer" @click="cancerBtn">{{$t('btn_cancel')}}</a-button>
        </div>
      </a-form-model>
    </a-modal>
  </div>
</template>


<script>
export default {
  name: "addNewBase",
  props:['visible'],
  data() {
    return {
      dateFormat: 'YYYY-MM-DD',
      visibleVal:null,
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      baselinetitle: this.$t('txt_create_baseline'),
      confirmLoading: true,
      neweffectdate: null,
      form: {
        name: "",
        oid: undefined,
        effectdate: '',
      },
      rules: {
        name: [{ required: true, message: this.$t('placeholder_name'), trigger: "change" }],
        oid: [{ required: true, message: this.$t('msg_select'), trigger: "change" }],
        effectdate: [{ required: true, message: this.$t('txt_start_date_null'), trigger: "change" }],
      },
    };
  },
  methods: {
   ondateChange(date, dateString) {
      console.log(dateString);
       this.neweffectdate = dateString
    },
     cancerBtn() {
      this.$refs.ruleForm.resetFields();
       this.$emit('closed',false)
    },
    comfirmAdd() {
      this.$refs.ruleForm.validate((valid) => {
        //to do 接口联调, 待做
        if (valid) {
         let param = {
           name: this.form.name,
           oid: this.form.oid,
           date: this.neweffectdate
         }
          console.log("param", param);
          //  createBaseApi
          // .execute(param)
          // .then(data => {
          //    this.$success(err.msg)
          //    this.visible = false
          //    this.$refs.ruleForm.resetFields()
          // })
          // .catch(err => {
          //  if(err.msg) {
          //    this.$error(err.msg)
          //  }
          // });
        }
      });
    },
  },

   watch: {
    visible(newV) {
      this.visibleVal = newV;
    }   
  },
  mounted() {
    this.visibleVal = this.visible;
  },
};
</script>


<style scoped>
.cancer {
  width: 52px;
  padding: 0;
  text-align: center;
  letter-spacing: 0;
}
.comfirm-btn {
  background: #255ed7;
  margin: 0 8px 0 0;
  width: 80px;
  padding: 0;
}

.comfirm-btn-info {
  display: flex;
  justify-content: flex-end;
}
.baseline-select,.date {
  width: 100%;
}
.tltles {
  font-size: 16px;
  color: #292a2c;
  font-weight: 700;
}

.alert-model >>> .ant-modal-body {
  padding: 0 20px 22px 20px;
}

.alert-model >>> .ant-form-item-label {
  height: 20px;
  line-height: 20px;
}
.alert-model >>> .ant-modal-header {
  border-bottom: none;
  padding: 17px 24px;
}
</style>