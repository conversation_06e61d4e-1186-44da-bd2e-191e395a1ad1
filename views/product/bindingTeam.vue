<template>
    <div class="binding-team">
        <div class="binding-team-header flex justify-start align-center">
            <svg class="jwifont bind-icon-back" aria-hidden="true" @click="closeBinding">
                <use xlink:href="#jwi-back"></use>
            </svg>
            <span>{{$t('txt_binding_team')}}</span>
        </div>
        <a-row class="shadow-wrap">
            <a-col :span="12">
                <bind-tree :fullHeight="fullHeight"></bind-tree>
            </a-col>
            <a-col :span="12">
                <bind-table :fullHeight="fullHeight"></bind-table>
            </a-col>
        </a-row>
    </div>
</template>

<script>
import bindTree from './bindTree';
import bindTable from './bindTable';
export default {
    name: 'bindingTeam',
    props: [
        'fullHeight',
    ],
    components: {
        bindTree,
        bindTable,
    },
    data() {
        return {
            
        };
    },
    computed: {
    },
    watch: {},
    methods: {
        closeBinding() {
            this.$emit('switchBinding', false);
        },
    },
    created() {
        
    },
    mounted() {

    },
};
</script>

<style lang="less" scoped>
.shadow-wrap {
    margin: 0 5px;
    background: var(--light);
    box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
    border-radius: 4px;
}
.jwifont {
    width: 16px;
    height: 16px;
}
.binding-team {
    .binding-team-header {
        padding-left: 5px;
        margin-bottom: 10px;
        color: #1e202a;
    }
    .bind-icon-back {
        margin-right: 5px;
        cursor: pointer;
    }
}
</style>
