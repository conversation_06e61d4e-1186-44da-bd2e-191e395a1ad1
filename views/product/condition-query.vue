<template>
  <!-- 自定义查询 -->
  <div class="custom">
    <!-- 过滤： -->
    <div class="custom-filter">
      <p>过滤：</p>
      <!-- clearable -->
      <a-select
        size="mini"
        v-model.trim="programmeName"
        style="width: 65%"
        filterable
        :placeholder="$t('msg_select')"
      >
        <a-select-option
          v-for="(item, index) in conditionNameCode"
          :key="index"
          :value="item.value"
        >{{item.label}}</a-select-option>
      </a-select>
     
    </div>

    <!-- 搜索条件：(展开、收起) -->
    <div class="custom-open">
      <p class="custom-open-name">{{$t('txt_custom_search')}}:</p>
      <p class="custom-open-list" @click="searchCode" v-show="!showSF">{{$t('txt_an')}}</p>
      <p class="custom-open-list" @click="searchCode" v-show="showSF">{{$t('txt_pack_up')}}</p>
      <i
        class="custom-open-icons"
        :class="showSF ? 'el-icon-caret-top' : 'el-icon-caret-bottom'"
        @click="searchCode"
      ></i>
    </div>

    <!-- 条件 -->
    <div class="custom-condition" v-show="showSF">
      <div class="custom-condition-matching" style="margin: 10px 0 14px 5px">
        <p style="display: inline-block">{{$t('txt_filter_matching')}}</p>
        <a-select
          size="mini"
          v-model.trim="filterCode"
          class="select-list"
          :placeholder="$t('msg_select')"
        >
          <a-select-option value="and">And({{$t('txt_all_filter')}})</a-select-option>
          <a-select-option value="or">Or({{$t('txt_filter_one')}})</a-select-option>
        </a-select>
      </div>

      <div class="condition-button">
        <a-button
          class="condition-button-list01"
          type="primary"
          @click="queryModel"
          >{{$t('txt_search_data')}}</a-button
        >
        <a-button class="condition-button-list02" @click="emptyModel"
          >{{$t('txt_empty')}}</a-button
        >
        <a-button class="condition-button-list02" @click="preservationsCode"
          >{{$t('btn_save')}}</a-button
        >
        <a-button class="condition-button-list02" @click="delectModelsCode"
          >{{$t('btn_delete')}}</a-button
        >
      </div>

      <!-- 条件 -->
      <a-form-model
        :model="baseDataConfig"
        ref="baseDataConfig"
        class="condition-content"
      >
        <a-form-model-item
          class="condition-content-forms"
          :rules="baseDataConfig.rules"
          v-for="(item, index) in tables"
          :key="index"
        >
          <a-select
            v-model.trim="item.conditionKey"
            prop="conditionKey"
            :placeholder="$t('msg_select')"
            style="margin-right: 10px"
          >
            <a-select-option
              v-for="item in options"
              :key="item.label"
              :label="item.label"
              :value="item.value"
            ></a-select-option>
          </a-select>
          <a-select
            v-model.trim="item.compareOperator"
            prop="compareOperator"
             :placeholder="$t('msg_select')"
            style="margin-right: 10px"
          >
            <a-select-option
              v-for="item in operatorList"
              :disabled="item.disabled"
              :key="item.label"
              :label="item.label"
              :value="item.value"
            ></a-select-option>
          </a-select>
          <el-date-picker
            v-if="item.conditionKey == 'updatedTime' || item.conditionKey == 'createdTime' "
            v-model.trim="item.conditionValue"
            style="width: 230px"
            type="date"
            value-format="yyyy-MM-dd"
             :placeholder="$t('txt_select_date')"
          >
          </el-date-picker>
          <a-input
            v-else
            v-model.trim="item.conditionValue"
            clearable
            prop="conditionValue"
            @change="optionsText03(index)"
            style="width: 230px"
           :placeholder="$t('msg_select')"
          ></a-input>

          <span
            class="condition-content-forms-increase"
            :title="$t('txt_add_c')"
            @click="addMo"
          >
            <i class="el-icon-plus"></i>
          </span>
          <span
            class="condition-content-forms-increase"
            :title="$t('btn_delete')"
            @click="deleteMo(index)"
          >
            <i class="el-icon-minus"></i>
          </span>
        </a-form-model-item>
      </a-form-model>
    </div>

    <!-- 弹框 -->
    <a-modal
      :title="BaoDel === true ? $t('txt_svae_conditions') :$t('txt_delete_condtions')"
      :visible ="dialogShowSF"
      width="40%"
    >
      <span>{{$t('txt_name')}}</span>
      <a-input
        v-show="BaoDel"
        v-model.trim="conditionName"
        :placeholder="$t('txt_input')"
        style="margin-top: 10px"
      ></a-input>

      <a-select
        v-show="!BaoDel"
        v-model.trim="programmeDelectName"
        :placeholder="$t('msg_select')"
        style="width: 100%; margin-top: 10px"
      >
        <a-select-option
          v-for="item in conditionNamedelete"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></a-select-option>
      </a-select>

      <span slot="footer">
        <a-button @click="dialogShowSF = false">{{$t('btn_cancel')}}</a-button>
        <a-button type="primary" @click="queding">{{$t('btn_confirm')}}</a-button>
      </span>
    </a-modal>
  </div>
</template>
<script>

import ModelFactory from "jw_apis/model-factory"

// 查询自定义保存的条件--对应页面的对应方案
let QueryModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.baseServer}/program/getByTableOid`,
  method: "Get",
});

// 保存条件--对应页面的对应方案
let PreservationModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.baseServer}/program/saveProgram`,
  method: "Post",
});

// 删除条件--对应页面的对应方案
let DeletesModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.baseServer}/program/deleteProgram`,
  method: "Post",
});

export default {


  computed: {
    operatorList() {
      console.log(this.tables[0])
      if (this.tables[0]["conditionKey"] === "updatedTime" || this.tables[0]["conditionKey"] === "createdTime") {
        //当第一个查询条件为更新时间,且第二个查询条件为包含或者不包含
        if (
          this.tables[0]["compareOperator"] == this.$t('txt_contains') ||
          this.tables[0]["compareOperator"] == this.$t('txt_not_contains')
        ) {
          this.tables[0]["compareOperator"] = this.symbolCode[0].value;
        }
        console.log(this.symbolCode)
        const codeArr = _.cloneDeep(this.symbolCode);
        codeArr.map((item) => {
          if (item.value == this.$t('txt_contains') || item.value == this.$t('txt_not_contains')) {
            item.disabled = true;
          }
        });
        return codeArr;
      } else {
        return this.symbolCode;
      }
    },
  },
  props: ["DictionariesCode", "tableData", "onlyData", "DictionariesCode_Data"],
  
  data() {
    return {
      signName: "",
      BaoDel: false,
      dialogShowSF: false,
      showSF: false,
      showis: true,
      curLanguage:Jw.getSelectedLanguageCode(),
      symbolCode: [
        { label: "=", value: "=", disabled: false },
        { label: ">", value: ">", disabled: false },
        { label: "<", value: "<", disabled: false },
        { label: ">=", value: ">=", disabled: false },
        { label: "<=", value: "<=", disabled: false },
        { label: "!=", value: "!=", disabled: false },
        { label: this.$t('txt_contains'), value: this.$t('txt_contains'), disabled: false },
        { label: this.$t('txt_not_contains'), value: this.$t('txt_not_contains'), disabled: false },
        // { label: '包含', value: 'like', disabled: false },
        // { label: '不包含', value: 'not_like', disabled: false },
      ],
      conditionNamedelete: [],
      conditionNameCode: [{ label: this.$t('all_text'), value: "" }],
      typesof: {},
      generalTableCondition: "", // 条件的参数
      conditionCode: "", // 条件的参数
      conditionName: "", // 保存条件名字
      programmeName: "", // 选择查询方案
      programmeDelectName: "", // 删除某查询方案
      filterCode: "and", // 过滤条件匹配

      tables: [
        //条件的参数--text01:第一个下拉框--text02:第二个下拉框--text03:最后一个input
        { conditionKey: "", compareOperator: "", conditionValue: "" },
      ],

      options: [], // 第一个下拉框里的值
      baseDataConfig: {},

      differenceCode: [],
    };
  },
  watch: {
    DictionariesCode: {
      handler: function (val, oldval) {
        console.log(val);
        this.selectDatas();
      },
    },
    programmeName: {
      handler: function (val, oldval) {
        if (this.programmeName) {
          this.showSF = true;
          // 先赋值
          this.tables = JSON.parse(JSON.stringify(this.programmeName));
          for (let k in this.tables) {
            this.tables[k].conditionKey = this.tables[k].conditionTransferKey;
          }

          // 判断--conditionValue--是否为空
          for (let k in this.tables) {
            if (this.tables[k].conditionValue == "") {
              return;
            }
          }

          this.$emit(
            "transmitValue",
            this.programmeName == "" || undefined ? [] : this.programmeName
          ); // 点击改变过滤里的数据，返回一个拼接的字符串
        } else {
          this.$emit(
            "transmitValue",
            this.programmeName == "" || undefined ? [] : this.programmeName
          ); // 点击改变过滤里的数据，返回一个拼接的字符串
        }
      },
    },
    tableData: {
      handler: function (val, oldval) {
        if (this.tableData) {
          if (this.showis == true) {
            let abss = this.tableData[0];
            this.typesof = {};
            for (let k in abss) {
              if (typeof abss[k]) {
                this.typesof[k] = typeof abss[k];
              } else {
                this.typesof[k] = typeof abss[k];
              }
            }
            console.log(this.typesof, "typesof");
            this.showis = false;
          }
        }
      },
    },

    tables: {
      handler: function (val, oldval) {
        if (this.showSF == true) {
          this.$emit("showLength", this.tables.length); // 条件个数改变，触发返回个数
        }
      },
    },
  },

  mounted() {
    console.log("-------------" + this.DictionariesCode);
    this.signName = Jw.getUser().account;
    this.selectDatas();
    this.querys();
    this.pangduan();
  },
  methods: {
    pangduan() {
      if (this.tableData) {
        if (this.showis == true) {
          let abss = this.tableData[0];
          this.typesof = {};
          for (let k in abss) {
            if (typeof abss[k]) {
              this.typesof[k] = typeof abss[k];
            } else {
              this.typesof[k] = typeof abss[k];
            }
          }
          this.showis = false;
        }
      }
    },
    // 搜索
    searchCode() {
      this.showSF = !this.showSF;
      // if (this.showSF == false) {
      //   this.tables = [{ text01: '', text02: '', text03: '' }],
      //     this.generalTableCondition = ''
      // }
      this.$emit("searchShowCode", this.showSF); // 点击 展开或收起 触发，返回一个true或false
    },

    findDisplay(multiLanguageValue){
      let item , result;
      for(let index in multiLanguageValue){
        item = multiLanguageValue[index];
        if(item.languageCode==this.curLanguage){
          result = item.valueText;
          break
        }
      }
      if (result){
        return this.$t(result)|| result
      }
      return multiLanguageValue[1].valueText;
    },

    // 第一个下拉框的值
    selectDatas() {
      // 数据字典--我的任务
      let DictionariesModel = ModelFactory.create({
        url: this.DictionariesCode,
        method: "Get",
      });

      DictionariesModel.execute({
        currPage: 1,
        pageSize: 100,
      })
        .then((datas) => {
          this.options = [];
          for (let k in datas) {
            let a = { label: "", value: "" };
            a.label = this.findDisplay(datas[k].multiLanguageValue);
            a.value = datas[k].multiLanguageValue[1].valueCode;
            this.options.push(a);
          }
        })
        .catch((error) => {
          this.$alert(
            error.msg || this.$t('txt_faileure'),
            "Error"
          );
        });

      if (this.DictionariesCode_Data) {
        // 数据字典--我的任务--（优先级）
        let DictionariesCode_DataModel = ModelFactory.create({
          url: this.DictionariesCode_Data,
          method: "Get",
        });

        DictionariesCode_DataModel.execute({
          currPage: 1,
          pageSize: 100,
        })
          .then((datas) => {
            for (let k in datas) {
              let a = { key: "", value: "", text: "" };
              a.key = datas[k].multiLanguageValue[0].valueText;
              a.value = datas[k].multiLanguageValue[0].valueCode;
              a.text = datas[k].multiLanguageValue[1].valueText;
              this.differenceCode.push(a);
            }
          })
          .catch((error) => {
            this.$alert(
              error.msg || this.$t('txt_faileure'),
              "Error"
            );
          });
      }
    },
    // 查询过滤条件--接口
    querys() {
      
      this.signName = Jw.getUser().account;
      QueryModel.execute({
        tableOid: this.onlyData, // 一个页面的唯一标识
        signName: this.signName, // 一个页面的唯一标识
      })
        .then((datas) => {
          
          this.programmeName = "";
          this.conditionNameCode = [{ label: this.$t('all_text'), value: "" }];
          this.conditionNamedelete = [];

          for (let k in datas) {
            let a = { label: "", value: "" };
            a.label = datas[k].programName;
            a.value = datas[k].conditionList;
            this.conditionNameCode.push(a);

            let b = { label: "", value: "" };
            b.label = datas[k].programName;
            b.value = datas[k].programName;
            this.conditionNamedelete.push(b);
          }
        })
        .catch((error) => {
          this.$alert(
            error.msg || this.$t('txt_faileure'),
            "Error"
          );
        });
        
       
    },
    // 保存--接口
    fetch(tablesCode) {
      PreservationModel.execute({
        tableOid: this.onlyData, // 一个页面的唯一标识
        programName: this.conditionName, // 条件名字
        conditionList: tablesCode, // 条件参数
        signName: this.signName, // 登录的人员名称
        // programContent: this.generalTableCondition,   // 条件参数
      })
        .then((data) => {
          this.$success(this.$t('msg_success'));
          this.dialogShowSF = false;
          this.querys();
        })
        .catch((error) => {
          this.$alert(
            error.msg || this.$t('txt_faileure'),
            "Error"
          );
        });
    },
    // 删除一条条件
    delectModelsCode() {
      this.programmeDelectName = "";
      this.BaoDel = false;
      this.dialogShowSF = true;
    },
    // 删除条件的方法
    delectModels() {
      for (let k in this.conditionNamedelete) {
        if (this.conditionNamedelete[k].value == this.programmeDelectName) {
          DeletesModel.execute({
            tableOid: this.onlyData,
            programName: this.programmeDelectName,
            signName: this.signName,
          })
            .then((data) => {
              this.$success(this.$t('txt_delete_success'));
              this.dialogShowSF = false;
              this.querys();
            })
            .catch((error) => {
              this.$alert(
                error.msg || this.$t('txt_faileure'),
                "Error"
              );
            });
        }
      }
    },
    queding() {
      if (this.BaoDel == true) {
        this.preservations();
      } else if (this.BaoDel == false) {
        this.delectModels();
      }
    },
    // 保存按钮
    preservationsCode() {
      this.conditionName = "";
      this.BaoDel = true;
      this.dialogShowSF = true;
    },
    // 保存条件按钮的方法
    preservations() {
      for (let k in this.tables) {
        // 判断条件是否为空
        if (
          this.tables[k].conditionKey == "" ||
          this.tables[k].compareOperator == ""
        ) {
          this.$warning(this.$t('txt_mandatory'));
          return;
        }
        // 判断参数的值不能是and、or、=、>、<、!= 等等。。。
        if (
          this.tables[k].conditionValue == "and" ||
          this.tables[k].conditionValue == "or"
        ) {
          this.$warning(this.$t('txt_conditions_value'));
          return;
        }
      }
      this.conditionName = this.conditionName.trimStart();
      if (this.conditionName == "") {
        this.$warning(this.$t('txt_name_not_null'));
        return;
      }

      for (let j in this.conditionNameCode) {
        if (this.conditionNameCode[j].label == this.conditionName) {
          this.$warning(this.$t('txt_name_exists'));
          return;
        }
      }
      let tablesCode = JSON.parse(JSON.stringify(this.tables));
      this.tiaojianpangduan(tablesCode);

      for (let index in this.tables) {
        tablesCode[index].conditionTransferKey = this.tables[
          index
        ].conditionKey;
      }
      this.fetch(tablesCode);
    },
    hideDialog() {
      this.showSF = false;
    },
    clearSelect() {
      this.programmeName = "";
    },
    // 清空
    emptyModel() {
      this.tables = [
        { conditionKey: "", compareOperator: "", conditionValue: "" },
      ];
      this.generalTableCondition = "";
      this.conditionCode = "";
    },
    // 查询按钮
    queryModel() {
      for (let k in this.tables) {
        // 判断条件是否为空
        if (this.tables.length > 1) {
          if (
            this.tables[k].conditionKey == "" &&
            this.tables[k].compareOperator == "" &&
            this.tables[k].conditionValue == ""
          ) {
          } else if (
            this.tables[k].conditionKey != "" &&
            this.tables[k].conditionValue == ""
          ) {
            this.$warning(this.$t('txt_mandatory')+"02");
            return;
          } else if (
            this.tables[k].conditionKey == "" &&
            this.tables[k].conditionValue != ""
          ) {
            this.$warning(this.$t('txt_mandatory')+"03");
            return;
          } else if (
            this.tables[k].compareOperator != "" &&
            this.tables[k].conditionValue == ""
          ) {
            this.$warning(this.$t('txt_mandatory')+"04");
            return;
          }
        } else if (this.tables.length == 1) {
          if (
            this.tables[k].conditionKey != "" &&
            this.tables[k].conditionValue == ""
          ) {
            this.$warning(this.$t('txt_mandatory')+"02");
            return;
          } else if (
            this.tables[k].conditionKey == "" &&
            this.tables[k].conditionValue != ""
          ) {
             this.$warning(this.$t('txt_mandatory')+"03");
            return;
          } else if (
            this.tables[k].compareOperator != "" &&
            this.tables[k].conditionValue == ""
          ) {
             this.$warning(this.$t('txt_mandatory')+"04");
            return;
          }
        }
        // 判断参数的值不能是and、or、=、>、<、!= 等等。。。
        if (
          this.tables[k].conditionValue == "and" ||
          this.tables[k].conditionValue == "or" ||
          this.tables[k].conditionValue == " " ||
          this.tables[k].text03 == " and"
        ) {
          this.$warning(this.$t('txt_conditions_value'));
          return;
        }
      }

      let tablesCode = JSON.parse(JSON.stringify(this.tables));

      this.tiaojianpangduan(tablesCode);
      if (tablesCode.length == 1) {
        if (
          tablesCode[0].compareOperator == "" &&
          tablesCode[0].conditionValue == "" &&
          tablesCode[0].conditionKey == []
        ) {
          tablesCode = "";
        }
      }
      this.$emit(
        "transmitValue",
        tablesCode == "" || tablesCode == undefined ? [] : tablesCode
      ); // 点击查询返回一个数组
    },

    tiaojianpangduan(tablesCode) {
      for (let k in tablesCode) {
        if (tablesCode[k].conditionKey != "") {
          tablesCode[k].joinOperator = this.filterCode;
        }

        let dataCode = JSON.parse(JSON.stringify(tablesCode));
        let jsons = dataCode[k].conditionKey.substring(0, 5);
        if (jsons == "json_") {
          tablesCode[k].conditionType = "String";
        }
        for (let w in this.differenceCode) {
          if (tablesCode[k].conditionKey == this.differenceCode[w].key) {
            if (tablesCode[k].conditionValue == this.differenceCode[w].text) {
              tablesCode[k].conditionValue = this.differenceCode[w].value;
            }
          }
        }

        for (let j in this.typesof) {
          if (tablesCode[k].conditionKey == j) {
            if (this.typesof[j] == "number") {
              tablesCode[k].conditionType = "Number";
            } else if (this.typesof[j] == "string") {
              tablesCode[k].conditionType = "String";
            }
          }
        }

        tablesCode[k].conditionKey = tablesCode[k].conditionKey.split("");

        // if(jsons != 'json_'){
        //   for (let i in tablesCode[k].conditionKey) {
        //     if (tablesCode[k].conditionKey[i] < 'a' || tablesCode[k].conditionKey[i] > 'z') {
        //       tablesCode[k].conditionKey[i] = tablesCode[k].conditionKey[i].toLowerCase()
        //       tablesCode[k].conditionKey.splice(i, 0, '_')
        //     }
        //   }
        // }
        tablesCode[k].conditionKey = tablesCode[k].conditionKey.join("");
      }
    },

    // 新增条件
    addMo() {
      if (this.tables.length > 4) {
        // if (this.tables.length == this.options.length) {
        this.$warning((this.$t('txt_five_conditions') ).replace("Placeholder",5));
        return;
      }
      let a = { conditionKey: "", compareOperator: "", conditionValue: "" };
      this.tables.push(a);
    },
    // 删除
    deleteMo(index) {
      if (this.tables.length == 1) {
        this.tables = [
          { conditionKey: "", compareOperator: "", conditionValue: "" },
        ];
        this.$warning((this.$t('txt_bl_one') ).replace("Placeholder",1));
        return;
      }
      this.tables.splice(index, 1);
    },
  },
};
</script>
<style lang="less">
.custom {
  width: 100%;
  background: #fff;
  line-height: 45px;
  .custom-filter {
    width: 22%;
    margin-left: 20px;
    float: right;
    > p {
      display: inline-block;
    }
    > el-select {
      width: 65%;
    }
  }
  .custom-open {
    margin-left: 20px;
    .custom-open-name {
      display: inline-block;
    }
    .custom-open-list {
      display: inline-block;
      cursor: pointer;
      color: blue;
    }
    .custom-open-icons {
      font-size: 22px;
      display: inline-block;
      cursor: pointer;
      color: blue;
    }
  }
  .custom-condition {
    position: relative;
    margin: 10px 20px 0 20px;
    border: 1px solid #ccc;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
    .custom-condition-matching {
      margin: 10px 0 14px 5px;
      display: inline-block;
      > p {
        display: inline-block;
      }
      .select-list {
        position: absolute;
        left: 120px;
        top: 5px;
      }
    }
    .condition-button {
      width: 450px;
      height: 30px;
      display: inline-block;
      float: right;
      margin-top: 5px;
      .condition-button-list01 {
        float: right;
        margin: 0 10px 10px 0;
        height: 30px;
        line-height: 6px;
      }
      .condition-button-list02 {
        float: right;
        margin: 0 5px 10px 0;
        height: 30px;
        line-height: 6px;
      }
    }
    .condition-content {
      margin: 10px 0 0 120px;
      .condition-content-forms {
        margin-bottom: 10px;
        .condition-content-forms-increase {
          width: 25px;
          height: 25px;
          background: blue;
          display: inline-block;
          cursor: pointer;
          line-height: 25px;
          > i {
            color: #fff;
            margin: 5px;
            font-weight: bold;
          }
        }
      }
    }

    .el-form-item__content {
      display: flex;
      width: 100%;
      align-items: center;
    }
  }
}
</style>