<template>
    <div class="height-full">
        <a-row :gutter="[16, 10]" style="margin: 0" v-if="!isBinding">
            <a-col :span="6" style="padding-left: 5px">
                <groupList></groupList>
            </a-col>
            <a-col :span="18" style="padding-right: 5px">
                <div class="main-wrap">
                    <searchInfo
                        @switchBath="switchBathBtn"
                        @switchStatus="switchStatus"
                        @onSearchFn="onSearchFn"
                    ></searchInfo>
                    <tableInfo v-if="isList"
                        ref="refTable"
                        @switchBinding="switchBinding">
                    </tableInfo>
                    <cardList v-else
                        ref="refList">
                    </cardList>
                </div>
            </a-col>
        </a-row>
        <binding-team
            v-else
            @switchBinding="switchBinding"
        ></binding-team>
        <customGroup
            :visiblecustom="visiblecustom"
            :dataArr="dataArr"
            @closed="closed"
        ></customGroup>
    </div>
</template>

<script>
import groupList from './groupList';
import searchInfo from './searchInfo';
import tableInfo from './tableinfo';
import cardList from './cardList';
import bindingTeam from './bindingTeam';
import customGroup from './customGroup';
import { fetchProductList } from 'apis/product/';
export default {
    components: {
        groupList,
        tableInfo,
        cardList,
        bindingTeam,
        customGroup,
        searchInfo,
    },
    inject: ['setBreadcrumb', 'addBreadcrumb'],
    data() {
        return {
            dataArr: [],
            visiblecustom: false,
            isList: true,
            isBinding: false,
        }; 
    },
    created() {
        this.initBreadcrumb();
    },
    mounted() {
        this.$refs.refTable.getProductList();
    },
    methods: {
        initBreadcrumb() {
            let breadcrumbData = [{ name: this.$t('txt_procuct_cu'), path: "/product" }];
            this.setBreadcrumb(breadcrumbData);
            this.addBreadcrumb({ name: this.$t('txt_list') });
        },
        onSearchFn(keyword) {
            if (this.isList) {
                this.$nextTick(() => {
                    this.$refs.refTable.page.currentPage = 1;
                    this.$refs.refTable.page.total = 0;
                    this.$refs.refTable.searchKey = keyword;
                    this.$refs.refTable.getProductList();
                })
            } else {
                this.$nextTick(() => {
                    this.$refs.refList.searchKey = keyword;
                    this.$refs.refList.getProductList();
                })
            }
        },
        fetchDataList() {
            fetchProductList
                .execute()
                .then(res => {
                    this.dataArr = res.rows;
                })
                .catch(err => {
                });
        },
        switchStatus(flag, keyword) {
            this.isList = flag;
            this.onSearchFn(keyword);
        },
        switchBathBtn(flag) {
            this.visiblecustom = flag;
        },
        closed(flag) {
            this.visiblecustom = flag;
        },
        switchBinding(flag) {
            this.isBinding = flag;
        },
    },
};
</script>

<style scoped lang="less">
.height-full {
    .main-wrap {
        background: var(--light);
        box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
        border-radius: 4px;
        padding: 0px;
    }
}
</style>
