<template>
    <div class="feature-value-wrap">
        <div class="head-wrap flex align-center">
            <a-button type="primary" @click="onAdd">添加特性值</a-button>
            <a-input-search v-model.trim="searchKey" class="search-input"
                placeholder="输入关键词搜索" @search="onSearch" @blur="onSearch" />
        </div>
        <div class="body-wrap">
            <a-spin :spinning="loading">
                <a-row :gutter="[8, 8]" v-if="featureValueData.length>0">
                    <a-col :span="12" v-for="item of featureValueData" :key="item.oid">
                        <div class="feature-card flex justify-between align-center"
                            @mouseenter="onShowIcon(item, true)"
                            @mouseleave="onShowIcon(item, false)">
                            <div class="feature-item-left">
                                <div class="feature-name flex align-center">
                                    <svg class="jwifont" aria-hidden="true">
                                        <use xlink:href="#jwi-tag"></use>
                                    </svg>
                                    <span>{{item.featureName}}</span>
                                </div>
                                <div class="feature-value">{{item.featureValue}}</div>
                            </div>
                            <div class="feature-item-right">
                                <svg class="jwifont" aria-hidden="true"
                                    v-if="item.isShowBtn" @click="onEditFeature(item)">
                                    <use xlink:href="#jwi-edit"></use>
                                </svg>
                                <svg class="jwifont" aria-hidden="true"
                                    v-if="item.isShowBtn" @click="onDelete(item)">
                                    <use xlink:href="#jwi-delete"></use>
                                </svg>
                            </div>
                        </div>
                    </a-col>
                </a-row>
                <a-empty v-else />
            </a-spin>
        </div>
        <feature-value-modal
            :visible="visibleAdd"
            :nodeInfo="nodeInfo"
            :featureItem="featureItem"
            @close="onClose"
            @getList="getFeatureValues">
        </feature-value-modal>
    </div>
</template>

<script>
import featureValueModal from './featureValueModal';
import {
    fetchFeatureValue,
    deleteFeatureValue,
} from 'apis/product/productStructure';
export default {
    name: 'featureValue',
    props: [
        'nodeInfo',
    ],
    components: {
        featureValueModal,
    },
    data() {
        return {
            searchKey: '',
            featureValueData: [],
            loading: true,
            visibleAdd: false,
            featureItem: {},
        };
    },
    mounted() {
        this.getFeatureValues();
    },
    methods: {
        goBack() {
            this.$router.push(`/productStructure/${this.$route.params.oid}/${this.$route.params.type}`);
        },
        onSearch() {
            this.getFeatureValues();
        },
        getFeatureValues() {
            this.loading = true;
            fetchFeatureValue.execute({
                masterOid: this.nodeInfo.oid,
                masterType: this.nodeInfo.modelType,
                searchKey: this.searchKey,
            }).then((res) => {
                this.featureValueData = res.map(item => {
                    item.isShowBtn = false;
                    return item;
                });
                this.loading = false;
            }).catch((err) => {
                this.loading = false;
                if (err.msg) {
                    this.$error(err.msg);
                }
            });
        },
        onShowIcon(item, flag) {
            item.isShowBtn = flag;
        },
        onAdd() {
            this.visibleAdd = true;
        },
        onClose() {
            this.visibleAdd = false;
            this.featureItem = {};
        },
        onEditFeature(item) {
            this.featureItem = { ...item };
            this.visibleAdd = true;
        },
        onDelete(item) {
            deleteFeatureValue(item.oid).execute(

            ).then((res) => {
                this.$success('删除成功！');
                this.getFeatureValues();
            }).catch((err) => {
                if (err.msg) {
                    this.$error(err.msg);
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
.feature-value-wrap {
    padding: 16px 20px;
    .jwifont {
        width: 16px;
        min-width: 16px;
        height: 16px;
        min-height: 16px;
    }
    .head-wrap {
        .search-input {
            width: 216px;
            margin-left: 8px;
        }
    }
    .body-wrap {
        height: calc(100vh - 301px);
        margin-top: 15px;
        overflow: auto;
        &::-webkit-scrollbar {
            width: 1px;
        }
        .ant-row {
            width: 100%;
            display: flex;
            align-items: stretch;
            flex-wrap: wrap;
        }
        .feature-card {
            height: 100%;
            padding: 16px 20px;
            border: 1px solid rgba(30, 32, 42, 0.15);
            border-radius: 4px;
            .feature-item-left {
                margin-right: 10px;
                .feature-name {
                    margin-bottom: 20px;
                    span {
                        margin-left: 8px;
                    }
                }
                .feature-value {
                    width: fit-content;
                    line-height: 22px;
                    padding: 0 8px;
                    font-size: 12px;
                    border-radius: 4px;
                    border: 1px solid rgba(30, 32, 42, 0.15);
                    background: rgba(30, 32, 42, 0.04);
                }
            }
            .feature-item-right {
                min-width: 60px;
                white-space: nowrap;
                .jwifont {
                    cursor: pointer;
                    &:first-child {
                        margin-right: 20px;
                    }
                }
            }
        }
        .ant-empty {
            margin-top: 30px;
        }
    }
}
</style>
