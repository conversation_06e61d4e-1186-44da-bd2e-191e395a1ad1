<template>
    <div class="demand-mana-wrap">
        <div class="head-wrap flex justify-between">
            <div class="flex">
                <a-button type="primary" :auto-insert-space-in-button="true" @click="onCreate">新建</a-button>
                <a-input-search v-model.trim="searchKey" class="search-input" allow-clear
                    placeholder="输入关键词搜索" @search="onSearch" @blur="onSearch" />
            </div>
            <div class="flex">
                <a-dropdown  class="header-batch-btn">
                    <a-menu slot="overlay">
                        <a-menu-item @click="onDeleteBatch">批量删除</a-menu-item>
                    </a-menu>
                    <a-button>批量操作<a-icon type="down" /></a-button>
                </a-dropdown>
            </div>
        </div>
        <div class="body-wrap">
            <a-table
                rowKey="oid" 
                :columns="columns" 
                :data-source="tableData"
                :pagination="false"
                :scroll="{ x: 800, y: 'calc(100vh - 420px)' }"
                :row-selection="{selectedRowKeys: selectedRowKeys,onChange: onSelectionChange}"
                :customRow="onRowClick"
                bordered
                :loading="loading"
            >
                <template slot="status" slot-scope="text">
                    <div :class="['status-tag',
                        text === 'cancel' ? 'status-red' :
                        text === 'draft' ? 'status-blue' :
                        'status-green']">
                        {{ text === 'draft' ? '草稿' :
                            text === 'reviewed' ? '已评审' :
                            text === 'accepted' ? '已接纳' :
                            text === 'achieved' ? '已实现' :
                            '已取消'
                        }}
                    </div>
                </template>
                <template slot="operation" slot-scope="text, record">
                    <div class="flex">
                        <svg class="jwifont" aria-hidden="true"
                            @click="onEdit(record)">
                            <use xlink:href="#jwi-edit"></use>
                        </svg>
                        <a-popconfirm
                            title="确定要删除吗?"
                            @confirm="() => onDeleteRow(record)"
                            >
                            <svg class="jwifont" aria-hidden="true" @click.stop>
                                <use xlink:href="#jwi-delete"></use>
                            </svg>
                        </a-popconfirm>
                    </div>
                </template>
            </a-table>
        </div>
        <div class="foot-wrap">
            <a-pagination
                show-size-changer
                :default-current="page.currenrPage"
                :pageSize.sync="page.pageSize"
                :total="page.total"
                :show-total="total => `共 ${total} 条`"
                @change="onCurrentChange"
                @showSizeChange="onSizeChange"
                />
        </div>
        <demand-create
            :visible="visible"
            :nodeInfo="nodeInfo"
            @close="onClose"
            @getList="getDemandList">
        </demand-create>
        <demand-detail
            :visible="detailVisible"
            :detailRow="detailRow"
            :toEdit="toEdit"
            @close="onCloseDetail"
            @getList="getDemandList">
        </demand-detail>
    </div>
</template>

<script>
import demandCreate from './demandCreate';
import demandDetail from './demandDetail';
import {
    fetchRequirementList,
    deleteRequirement,
} from 'apis/product/productStructure';
export default {
    name: 'demandManage',
    props: [
        'nodeInfo',
    ],
    components: {
        demandCreate,
        demandDetail,
    },
    data() {
        return {
            searchKey: '',
            loading: true,
            columns: [
				{
					title: '标题',
					dataIndex: 'name',
					key: 'name',
                    width: 150,
                    ellipsis: true,
				},
                {
					title: '分类',
					dataIndex: 'classify',
					key: 'classify',
                    width: 180,
                    ellipsis: true,
				},
                {
					title: '来源',
					dataIndex: 'source',
					key: 'source',
                    width: 100,
                    ellipsis: true,
				},
				{
					title: '状态',
					dataIndex: 'status',
					key: 'status',
                    width: 100,
                    scopedSlots: { customRender: 'status' },
				},
                {
					title: '描述',
					dataIndex: 'description',
					key: 'description',
					ellipsis: true,
				},
				{
					title: '操作',
					dataIndex: 'operation',
					key: 'operation',
					width: 100,
                    align: 'center',
                    fixed: 'right',
					scopedSlots: { customRender: 'operation' },
				},
			],
            tableData: [],
            selectedRowKeys: [],
            page: {
                currentPage: 1,
                pageSize: 20,
                total: 0,
            },
            visible: false,
            detailVisible: false,
            detailRow: {},
            toEdit: false,
        };
    },
    mounted() {
        this.getDemandList();
    },
    methods: {
        onSearch() {
            this.page.currentPage = 1;
            this.page.total = 0;
            this.getDemandList();
        },
        getDemandList() {
            this.loading = true;
            fetchRequirementList.execute({
                masterOid: this.nodeInfo.oid,
                searchKey: this.searchKey,
                currentPage: this.page.currentPage,
                pageSize: this.page.pageSize,
            }).then((res) => {
                this.tableData = res.data;
                this.page.total = res.count;
                this.loading = false;
            }).catch((err) => {
                this.loading = false;
                if (err.msg) {
                    this.$error(err.msg);
                }
            });
        },
        onCurrentChange(page, pageSize) {
            this.page.currentPage = page;
            this.getDemandList();
        },
        onSizeChange(current, size) {
            this.page.pageSize = size;
            this.page.currentPage = 1;
            this.getDemandList();
        },
        onSelectionChange(selectedRowKeys) {
            this.selectedRowKeys = selectedRowKeys;
        },
        onCreate() {
            this.visible = true;
        },
        onClose() {
            this.visible = false;
        },
        onEdit(row) {
            this.detailRow = JSON.parse(JSON.stringify(row));
            this.detailVisible = true;
            this.toEdit = true;
        },
        onCloseDetail() {
            this.detailVisible = false;
        },
        onRowClick(record, index) {
            return {
                on: {
                    click: (event) => {
                        this.detailRow = JSON.parse(JSON.stringify(record));
                        this.detailVisible = true;
                        this.toEdit = false;
                    },
                },
            }
        },
        onDeleteRow(row) {
            this.onDeleteFun([row.oid]);
        },
        onDeleteBatch() {
            if (this.selectedRowKeys.length > 0) {
                this.onDeleteFun(this.selectedRowKeys);
            }
        },
        onDeleteFun(ids) {
            deleteRequirement.execute(
                ids
            ).then((res) => {
                this.$success('删除成功！');
                this.getDemandList();
            }).catch((err) => {
                if(err.msg){
                    this.$error(err.msg);
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
.demand-mana-wrap {
    .jwifont {
        width: 16px;
        min-width: 16px;
        height: 16px;
        min-height: 16px;
    }
    .head-wrap {
        padding: 16px 20px;
        .search-input {
            width: 216px;
            margin-left: 8px;
        }
    }
    .body-wrap {
        height: calc(100vh - 359px);
        padding: 0 20px 20px;
        /deep/.ant-table-thead > tr > th {
            padding: 9px 16px;
        }
        /deep/.ant-table-tbody > tr > td {
            padding: 13px 16px;
        }
        .jwifont {
            margin: 0 9px;
            cursor: pointer;
        }
        .status-tag {
            width: fit-content;
            padding: 1px 8px;
            border-radius: 4px;
            font-size: 12px;
            &.status-green {
                color: #69c833;
                background: #f8fff0;
                border: 1px solid #ccedaf;
            }
            &.status-red {
                color: #f6445a;
                background: #fff0f0;
                border: 1px solid #ffc2c3;
            }
            &.status-blue {
                color: #255ed7;
                background: #f0f7ff;
                border: 1px solid #a4c9fc;
            }
        }
    }
    .foot-wrap {
        padding: 20px 12px 20px 20px;
		border-top: 1px solid rgba(30, 32, 42, 0.15);
        text-align: right;
    }
}
</style>
