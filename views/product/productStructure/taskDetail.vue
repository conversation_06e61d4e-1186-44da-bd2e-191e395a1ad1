<template>
    <a-drawer
        :title="title"
        width="45%"
        :visible="visible"
        @close="onClose"
    >
        <div class="detail-wrap">
            <div class="title-wrap flex justify-between align-center">
                <span class="title color85 font-700">基本信息</span>
                <a-button type="link" v-if="isEdit" @click="onEdit">编辑信息</a-button>
                <a-button type="link" v-else @click="onSave">保存信息</a-button>
            </div>
            <a-form-model
                ref="createForm"
                :model="detailRow"
                :rules="rules">
                <a-form-model-item label="名称" prop="name">
                    <a-input v-model.trim="detailRow.name" :disabled="isEdit"
                        :max-length="50" allow-clear placeholder="请输入" />
                </a-form-model-item>
                <a-form-model-item label="处理人" prop="handlerName">
                    <a-popover
                        trigger="click"
                        v-model.trim="visibleUser"
                        placement="bottomLeft">
                        <template slot="content">
                            <a-select
                                :disabled="isEdit"
                                show-search
                                allowClear
                                v-model.trim="detailRow.handlerName"
                                placeholder="请输入姓名、手机号、邮箱"
                                style="width: 250px"
                                :default-active-first-option="false"
                                :show-arrow="false"
                                :filter-option="false"
                                dropdownClassName="drop-style"
                                @focus="handleSearch"
                                @search="handleSearch"
                                @change="handleChange"
                            >
                                <a-select-option v-for="item in userData" :key="item.oid"
                                    :value="item.oid">
                                    <div class="user-wrap flex align-center">
                                        <div class="handler-avatar" v-if="item.avatarUrl">
                                            <img :src="item.avatarUrl" alt="">
                                        </div>
                                        <div v-else class="handler-avatar">{{item.name.slice(0,1).toUpperCase()}}</div>
                                        <div>{{ item.name }}</div>
                                    </div>
                                </a-select-option>
                            </a-select>
                        </template>
                        <a-button v-if="detailRow.handlerName"
                            :disabled="isEdit"
                            type="primary" ghost shape="circle">
                            <svg class="jwifont" aria-hidden="true">
                                <use xlink:href="#jwi-change"></use>
                            </svg>
                        </a-button>
                        <a-button v-else
                            type="primary" ghost shape="circle" icon="plus" />
                    </a-popover>
                    <div class="handler-wrap inline-flex align-center" v-if="detailRow.handlerName">
                        <div class="handler-avatar" v-if="detailRow.handlerUrl">
                            <img :src="detailRow.handlerUrl" alt="">
                        </div>
                        <div v-else class="handler-avatar">{{detailRow.handlerName.slice(0,1).toUpperCase()}}</div>
                        <div>{{ detailRow.handlerName }}</div>
                    </div>
                </a-form-model-item>
                <a-row :gutter="10">
                    <a-col :span="12">
                        <a-form-model-item label="任务周期（天）" prop="period">
                            <a-input-number v-model.trim="detailRow.period"
                                :disabled="isEdit" :precision="0" :min="1" />
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-model-item label="开始日期" prop="startTime">
                            <a-date-picker v-model.trim="detailRow.startTime" :disabled="isEdit"
                                valueFormat="YYYY-MM-DD" allow-clear />
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-form-model-item label="描述" prop="description">
                    <a-textarea v-model.trim="detailRow.description" :disabled="isEdit" allow-clear
                        class="textarea-input" placeholder="请输入" />
                </a-form-model-item>
            </a-form-model>
        </div>
    </a-drawer>
</template>

<script>
import { userListApi } from 'apis/user';
import {
    updateTask,
} from 'apis/product/productStructure';
import validateMixin from './validateMixin';
export default {
    name: 'taskDetail',
    props: [
        'visible',
        'nodeInfo',
        'detailRow',
        'toEdit',
    ],
    mixins: [validateMixin],
    data() {
        return {
            title: '',
            rules: {
                name: [
                    { required: true, message: '请输入', trigger: 'change' },
                    { min: 1, max: 50, message: '不能超过50个字符', trigger: 'change' },
                    { validator: this.validateName },
                ],
                period: [{ required: true, message: '请输入', trigger: 'change' }],
                startTime: [{ required: true, message: '请输入', trigger: 'change' }],
                handlerName: [{ required: true, message: '请选择', trigger: 'change' }],
            },
            visibleUser: false,
            userData: [],
            isEdit: true,
        }
    },
    mounted() {
        
    },
    watch: {
        visible(val) {
            if (val) {
                this.title = this.detailRow.name;
                if (this.toEdit) {
                    this.isEdit = false;
                }
            }
        }
    },
    methods: {
        handleSearch(value) {
            userListApi.execute({
                pageNum: 1,
                pageSize: 50,
                tenantOid: Jw.getUser().tenantOid,
                keyword: value,
            }).then(res => {
                this.userData = res.rows.map(item => {
                    if (item.avatar) {
                        item.avatarUrl = `${Jw.gateway}/${Jw.fileServer}/file/downloadByOid?oid=${item.avatar}`;
                    }
                    return item;
                });
            }).catch(err => {
                this.$error(err.msg);
            });
        },
        handleChange(value) {
            if (value) {
                this.detailRow.handlerOid = value;
                this.detailRow.handlerName = this.userData.filter(item => item.oid === value)[0].name;
                let avatar = this.userData.filter(item => item.oid === value)[0].avatar;
                if (avatar) {
                    this.detailRow.handlerUrl = `${Jw.gateway}/${Jw.fileServer}/file/downloadByOid?oid=${avatar}`;
                } else {
                    this.detailRow.handlerUrl = '';
                }
                this.visibleUser = false;
            }
        },
        onClose() {
            this.isEdit = true;
            this.$emit('close');
        },
        onEdit() {
            this.isEdit = false;
        },
        onSave() {
            this.$refs.createForm.validate((valid) => {
				if (valid) {
					updateTask.execute({
                        productOid: this.$route.params.oid,
                        masterOid: this.nodeInfo.oid,
                        masterType: this.nodeInfo.modelType,
                        task: this.detailRow,
                    }).then((res) => {
                        this.$success('修改成功！');
                        this.isEdit = true;
                        this.title = this.detailRow.name;
                        this.$emit('close');
                        this.$emit('getList');
                    }).catch((err) => {
                        this.isEdit = false;
                        if (err.msg) {
                            this.$error(err.msg);
                        }
                    });
				} else {
					return false;
				}
			});
        },
    },
}
</script>

<style lang="less" scoped>
.title-wrap {
    position: relative;
    margin-bottom: 15px;
    .title {
        margin-left: 10px;
        font-size: 16px;
        &:after {
            content: "";
            position: absolute;
            width: 3px;
            height: 20px;
            left: 0;
            top: 6px;
            border-radius: 4px;
            background: #255ED7;
        }
    }
}
.textarea-input /deep/.ant-input {
    height: 110px;
}
.jwifont {
    width: 16px;
    min-width: 16px;
    height: 16px;
    min-height: 16px;
    vertical-align: text-bottom;
}
.handler-wrap {
    margin-left: 8px;
    padding: 4px 8px;
    line-height: 20px;
    background: rgba(30, 32, 42, 0.04);
    border-radius: 4px;
}
.handler-avatar {
    margin-right: 8px;
    width: 24px;
    height: 24px;
    line-height: 20px;
    text-align: center;
    color: #255ed7;
    border-radius: 50%;
    border: 2px solid #255ed7;
    img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
    }
}
.user-wrap {
    padding: 5px 8px;
    border: 1px solid transparent;
}
.ant-input-number,
.ant-calendar-picker {
    width: 100%;
}
</style>
<style lang="less">
.drop-style {
    .ant-select-dropdown-menu-item-active:not(.ant-select-dropdown-menu-item-disabled) {
        background: #fff;
        .user-wrap {
            border-radius: 4px;
            background: #f0f7ff;
            border: 1px solid #a4c9fc;
        }
    }
}
</style>
