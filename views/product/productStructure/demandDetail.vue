<template>
    <a-drawer
        :title="title"
        width="45%"
        :visible="visible"
        @close="onClose"
    >
        <div class="detail-wrap">
            <div class="title-wrap flex justify-between align-center">
                <span class="title color85 font-700">基本信息</span>
                <a-button type="link" v-if="isEdit" @click="onEdit">编辑信息</a-button>
                <a-button type="link" v-else @click="beforeCreate">保存信息</a-button>
            </div>
            <a-form-model
                ref="createForm"
                :model="detailRow"
                :rules="rules">
                <a-row :gutter="10">
                    <a-col :span="12">
                        <a-form-model-item label="标题" prop="name">
                            <a-input v-model.trim="detailRow.name" :disabled="isEdit"
                                :max-length="50" allow-clear placeholder="请输入" />
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-model-item label="状态" prop="status">
                            <a-select v-model.trim="detailRow.status" :disabled="isEdit"
                                allow-clear placeholder="请选择">
                                <a-select-option
                                    v-for="item in statusList"
                                    :key="item.value"
                                    :value="item.value">
                                    {{ item.label }}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row :gutter="10">
                    <a-col :span="12">
                        <a-form-model-item label="分类" prop="classify">
                            <a-select v-model.trim="detailRow.classify" :disabled="isEdit"
                                allow-clear placeholder="请选择"
                                @focus="getRequirementType">
                                <a-select-option
                                    v-for="item in classList"
                                    :key="item.value"
                                    :value="item.label">
                                    {{ item.label }}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-model-item label="来源" prop="source">
                            <a-input v-model.trim="detailRow.source" :disabled="isEdit"
                                allow-clear placeholder="请输入" />
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-form-model-item label="描述" prop="description">
                    <a-textarea v-model.trim="detailRow.description" :disabled="isEdit" allow-clear
                        class="textarea-input" placeholder="请输入" />
                </a-form-model-item>
                <a-form-model-item prop="files" :colon="false">
                    <span>上传附件：</span>
                    <a-upload
                        name="file"
                        :multiple="true"
                        action="string"
                        :file-list="fileList"
                        :before-upload="() => false"
                        @change="handleChangeFile"
                    >
                        <a-button :disabled="isEdit"> <a-icon type="upload" /> 点击上传 </a-button>
                    </a-upload>
                </a-form-model-item>
                <div class="appendixs-wrap">
                    <div class="appendixs-item flex justify-between align-center"
                        v-for="item in detailRow.files"
                        :key="item.oid">
                        <span class="appendixs-name" @click="downLoadAttach(item.oid)">{{ item.fileName }}</span>
                        <a-icon type="delete" @click="onDeleteFile(item)" />
                    </div>
                </div>
            </a-form-model>
        </div>
    </a-drawer>
</template>

<script>
import {
    fetchRequirementType,
    updateRequirement,
} from 'apis/product/productStructure';
import validateMixin from './validateMixin';
export default {
    name: 'demandDetail',
    props: [
        'visible',
        'detailRow',
        'toEdit',
    ],
    mixins: [validateMixin],
    data() {
        return {
            title: '',
            statusList: [
                {label: '草稿', value: 'draft' },
                {label: '已评审', value: 'reviewed' },
                {label: '已接纳', value: 'accepted' },
                {label: '已实现', value: 'achieved' },
                {label: '已取消', value: 'cancel' },
            ],
            classList: [],
            rules: {
                name: [
                    { required: true, message: '请输入', trigger: 'change' },
                    { min: 1, max: 50, message: '不能超过50个字符', trigger: 'change' },
                    { validator: this.validateName },
                ],
                classify: [{ required: true, message: '请选择', trigger: 'change' }],
                source: [{ required: true, message: '请输入', trigger: 'change' }],
            },
            isEdit: true,
            fileList: [],
        }
    },
    mounted() {
        
    },
    watch: {
        visible(val) {
            if (val) {
                this.getRequirementType();
                this.title = this.detailRow.name;
                if (this.toEdit) {
                    this.isEdit = false;
                }
            }
        }
    },
    methods: {
        getRequirementType() {
            fetchRequirementType.execute({
                currPage: 1,
                pageSize: 50,
            }).then((res) => {
                this.classList = res.map(item => {
                    return {
                        value: item.multiLanguageValue[1].valueCode,
                        label: item.multiLanguageValue[1].valueText,
                    }
                });
            }).catch((err) => {
                if (err.msg) {
                    this.$error(err.msg);
                }
            });
        },
        onClose() {
            this.isEdit = true;
            this.$emit('close');
        },
        onEdit() {
            this.isEdit = false;
        },
        handleChangeFile(info) {
            this.fileList = info.fileList;
        },
        downLoadAttach(oid) {
            let a = document.createElement('a');
            let url = `${Jw.gateway}/${Jw.fileServer}/file/downloadByOid?oid=${oid}`;
            a.href = url;
            a.download = url;
            a.click();
        },
        onDeleteFile(item) {
            if (this.isEdit) return;
            this.detailRow.files.forEach((el, index) => {
                if (el.oid === item.oid) {
                    this.detailRow.files.splice(index, 1);
                }
            })
        },
        beforeCreate() {
            if (this.fileList.length > 0) {
                let formData = new FormData();
                let xhr = new XMLHttpRequest();
                this.fileList.forEach(item => {
                    formData.append('file', item.originFileObj); 
                })
                xhr.open('POST', `${Jw.gateway}/${Jw.fileServer}/file/multiUpload-v2`, true);
                xhr.onload = (res) => {
                    const response = JSON.parse(xhr.response);
                    this.detailRow.files =  [...this.detailRow.files, ...response.result];
                    this.onSave();
                };
                xhr.send(formData);
            } else {
                this.onSave();
            }
        },
        onSave() {
            this.$refs.createForm.validate((valid) => {
				if (valid) {
					updateRequirement.execute(
                        this.detailRow,
                    ).then((res) => {
                        this.$success('修改成功！');
                        this.isEdit = true;
                        this.title = this.detailRow.name;
                        this.fileList = [];
                        this.$emit('close');
                        this.$emit('getList');
                    }).catch((err) => {
                        this.isEdit = false;
                        if (err.msg) {
                            this.$error(err.msg);
                        }
                    });
				} else {
					return false;
				}
			});
        },
    },
}
</script>

<style lang="less" scoped>
.title-wrap {
    position: relative;
    margin-bottom: 15px;
    .title {
        margin-left: 10px;
        font-size: 16px;
        &:after {
            content: "";
            position: absolute;
            width: 3px;
            height: 20px;
            left: 0;
            top: 2px;
            border-radius: 4px;
            background: #255ED7;
        }
    }
}
.textarea-input /deep/.ant-input {
    height: 110px;
}
.appendixs-wrap {
    .appendixs-item {
        margin: 10px 0;
        padding: 0 7px;
        background: rgba(30, 32, 42, 0.04);
        border: 0;
        .appendixs-name {
            cursor: pointer;
            &:hover {
                color: #255ed7;
                text-decoration: underline;
            }
        }
    }
}
</style>
