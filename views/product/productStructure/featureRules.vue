<template>
    <div class="feature-rules-wrap">
        <div class="head-wrap flex justify-between">
            <div class="flex">
                <a-button type="primary" :auto-insert-space-in-button="true" @click="onCreate">新建</a-button>
                <a-input-search v-model.trim="searchKey" class="search-input" allow-clear
                    placeholder="输入关键词搜索" @search="onSearch" @blur="onSearch" />
            </div>
            <div class="flex">
                <a-dropdown  class="header-batch-btn">
                    <a-menu slot="overlay">
                        <a-menu-item @click="onDeleteBatch">批量删除</a-menu-item>
                        <a-menu-item @click="onEnableRule(true)">批量启用</a-menu-item>
                        <a-menu-item @click="onEnableRule(false)">批量弃用</a-menu-item>
                    </a-menu>
                    <a-button>批量操作<a-icon type="down" /></a-button>
                </a-dropdown>
            </div>
        </div>
        <div class="body-wrap">
            <a-table
                rowKey="oid" 
                :columns="columns" 
                :data-source="tableData"
                :pagination="false"
                :loading="loading"
                :scroll="{ x: 800, y: 'calc(100vh - 402px)' }"
                :customRow="onRowClick"
                :row-selection="{selectedRowKeys: selectedRowKeys, onChange: onSelectionChange}"
                bordered
            >
                <template slot="ruleName" slot-scope="text">
                    <div class="flex align-center">
                        <svg class="jwifont" aria-hidden="true">
                            <use xlink:href="#jwi-rule"></use>
                        </svg>
                        <div class="text-ellipsis rule-name">{{ text }}</div>
                    </div>
                </template>
                <template slot="isEnable" slot-scope="text, record">
                    <a-switch v-model.trim="record.isEnable" @click="onChangeEnable(record)">
                        <a-icon slot="checkedChildren" type="check" />
                        <a-icon slot="unCheckedChildren" type="close" />
                    </a-switch>
                </template>
                <template slot="operation" slot-scope="text, record">
                    <div class="flex">
                        <svg class="jwifont" aria-hidden="true"
                            @click="onEditRow(record)">
                            <use xlink:href="#jwi-edit"></use>
                        </svg>
                        <a-popconfirm 
                            title="确定要删除吗?" 
                            @confirm="() => onDeleteRow(record)"
                        >
                            <svg class="jwifont" aria-hidden="true" @click.stop>
                                <use xlink:href="#jwi-delete"></use>
                            </svg>
                        </a-popconfirm>
                    </div>
                </template>
            </a-table>
        </div>
        <div class="foot-wrap">
            <a-pagination
                show-size-changer
                :default-current="page.currenrPage"
                :pageSize.sync="page.pageSize"
                :total="page.total"
                :show-total="total => `共 ${total} 条`"
                @change="onCurrentChange"
                @showSizeChange="onSizeChange"
                />
        </div>
        <create-rules
            :visible="visible"
            :ruleInfo="ruleInfo"
            :toEdit="toEdit"
            @close="onClose"
            @getList="getRuleList">
        </create-rules>
    </div>
</template>

<script>
import createRules from './createRules';
import {　
    fetchRuleList,
    deleteRule,
    batchEnable,
}　from 'apis/product/productStructure'

export default {
    name: 'featureRules',
    props: [

    ],
    components: {
        createRules,
    },
    data() {
        return {
            searchKey: '',
            loading: true,
            columns: [
				{
					title: '名称',
					dataIndex: 'ruleName',
					key: 'ruleName',
					scopedSlots: { customRender: 'ruleName' },
				},
				{
					title: '描述',
					dataIndex: 'description',
					key: 'description',
					ellipsis: true,
				},
                {
					title: '是否启用',
					dataIndex: 'isEnable',
					key: 'isEnable',
                    width: 170,
					scopedSlots: { customRender: 'isEnable' },
				},
				{
					title: '操作',
					dataIndex: 'operation',
					key: 'operation',
					width: 100,
                    align: 'center',
                    fixed: 'right',
					scopedSlots: { customRender: 'operation' },
				},
			],
            tableData: [],
            selectedRowKeys: [],
            page: {
                currentPage: 1,
                pageSize: 20,
                total: 0,
            },
            visible: false,
            ruleInfo: {},
            toEdit: false,
        };
    },
    mounted() {
        this.getRuleList()
    },
    methods: {
        onSearch() {
            this.page.currentPage = 1;
            this.page.total = 0;
            this.getRuleList();
        },
        getRuleList() {
            this.loading = true;
            fetchRuleList.execute({
                contextOid: this.$route.params.oid,
                currentPage: this.page.currentPage,
                pageSize: this.page.pageSize,
                searchKey: this.searchKey,
            }).then(res => {
                this.tableData = res.data;
                this.page.total = res.count;
                this.loading = false;
            }).catch(err => {
                this.loading = false;
                if (err.msg) {
                    this.$error(err.msg);
                }
            })
        },
        onCurrentChange(page, pageSize) {
            this.page.currentPage = page;
            this.getRuleList();
        },
        onSizeChange(current, size) {
            this.page.pageSize = size;
            this.page.currentPage = 1;
            this.getRuleList();
        },
        onSelectionChange(selectedRowKeys) {
            this.selectedRowKeys = selectedRowKeys;
        },
        onChangeEnable(row) {
            row.disabled = true;
            batchEnable.execute({
                enableFlag: row.isEnable,
                oids: [row.oid],
            }).then((res) => {
                this.$success('操作成功！');
                this.getRuleList();
                row.disabled = false;
            }).catch((err) => {
                if(err.msg){
                    this.$error(err.msg);
                }
            });
        },
        onEditRow(row) {
            this.ruleInfo = JSON.parse(JSON.stringify(row));
            this.visible = true;
            this.toEdit = true;
        },
        onRowClick(record, index) {
            return {
                on: {
                    click: (event) => {
                        if (record.disabled) return;
                        this.ruleInfo = JSON.parse(JSON.stringify(record));
                        this.visible = true;
                        this.toEdit = false;
                    },
                },
            }
        },
        onDeleteRow(row) {
            this.onDeleteFun([row.oid]);
        },
        onDeleteBatch() {
            if (this.selectedRowKeys.length > 0) {
                this.onDeleteFun(this.selectedRowKeys);
            }
        },
        onDeleteFun(ids) {
            deleteRule.execute(
                ids
            ).then((res) => {
                this.$success('删除成功！');
                this.getRuleList();
            }).catch((err) => {
                if(err.msg){
                    this.$error(err.msg);
                }
            });
        },
        onEnableRule(flag) {
            if (this.selectedRowKeys.length > 0) {
                batchEnable.execute({
                    enableFlag: flag,
                    oids: this.selectedRowKeys,
                }).then((res) => {
                    this.$success('操作成功！');
                    this.getRuleList();
                }).catch((err) => {
                    if(err.msg){
                        this.$error(err.msg);
                    }
                });
            } else {
                this.$warning('请至少选择一条数据');
            }
        },
        onCreate() {
            this.visible = true;
            this.toEdit = true;
        },
        onClose() {
            this.visible = false;
            this.ruleInfo = {};
        },
    },
};
</script>

<style lang="less" scoped>
.feature-rules-wrap {
    .jwifont {
        width: 16px;
        min-width: 16px;
        height: 16px;
        min-height: 16px;
    }
    .head-wrap {
        padding: 16px 20px;
        .search-input {
            width: 216px;
            margin-left: 8px;
        }
    }
    .body-wrap {
        height: calc(100vh - 345px);
        padding: 0 20px 20px;
        /deep/.ant-table-thead > tr > th {
            padding: 9px 16px;
        }
        /deep/.ant-table-tbody > tr > td {
            padding: 13px 16px;
        }
        .jwifont {
            margin: 0 9px;
            cursor: pointer;
        }
        .rule-name {
            color: #255ed7;
        }
    }
    .foot-wrap {
        padding: 20px 12px 20px 20px;
		border-top: 1px solid rgba(30, 32, 42, 0.15);
        text-align: right;
    }
}
</style>
