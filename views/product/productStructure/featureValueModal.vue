<template>
    <a-modal
        :visible="visible"
        :title="featureItem.oid?'编辑特性值':'添加特性值'"
        :mask-closable="false"
        :footer="null"
        :destroyOnClose="true"
        @cancel="onCancel"
    >
        <div class="create-temp-wrap">
            <a-form-model ref="createForm" :model="form">
                <a-form-model-item label="特性" prop="featureName"
                    :rules="[
                        { required: true, message: '请选择', trigger: 'change' },
                    ]">
                    <a-select v-model.trim="form.featureName" placeholder="请选择"
                        showSearch
                        filterOption
                        option-filter-prop="children"
                        @select="getValueByFeature"
                        @change="onChangeFeatureName">
                        <a-select-option
                            v-for="item in featureList"
                            :key="item.oid"
                            :value="item.name">
                            {{ item.name }}
                        </a-select-option>
                    </a-select>
                </a-form-model-item>
                <a-form-model-item
                    v-if="form.valueType==='select'"
                    label="特性值" prop="featureValue"
                    :rules="[
                        { required: true, message: '请选择', trigger: 'change' },
                    ]">
                    <a-select v-model.trim="form.featureValue" placeholder="请选择"
                        showSearch
                        filterOption
                        option-filter-prop="children"
                        @focus="getValueByFeature">
                        <a-select-option
                            v-for="item in valueList"
                            :key="item.oid"
                            :value="item.value">
                            {{ item.value }}
                        </a-select-option>
                    </a-select>
                </a-form-model-item>
                <a-form-model-item
                    v-if="form.valueType==='text'"
                    label="特性值" prop="featureValue"
                    :rules="[
                        { required: true, message: '请选择', trigger: 'change' },
                        { min: 1, max: 50, message: '不能超过50个字符', trigger: 'change' },
                    ]">
                    <a-input
                        v-model.trim="form.featureValue"
                        :max-length="50"
                        allow-clear
                        placeholder="请输入" />
                </a-form-model-item>
                <a-form-model-item class="form-item-btns text-right">
                    <a-button type="primary" @click="handleCreate">{{$t('btn_ok')}}</a-button>
                    <a-button class="form-btn-cancel" @click="onCancel">{{$t('btn_cancel')}}</a-button>
                </a-form-model-item>
            </a-form-model>
        </div>
    </a-modal>
</template>

<script>
import {
    fetchFeatureByNode,
    fetchValueByFeature,
    createFeatureValue,
    updateFeatureValue,
} from 'apis/product/productStructure';
export default {
    name: 'featureValueModal',
    props: [
        'visible',
        'nodeInfo',
        'featureItem',
    ],
    data() {
		return {
            form: {},
            featureList: [],
            valueList: [],
		}
    },
    mounted() {
        
	},
    watch: {
        visible(val) {
            if (val) {
                this.getFeatures();
                if (this.featureItem.oid) {
                    this.form = { ...this.featureItem };
                } else {
                    this.form = {
                        featureName: undefined,
                        featureValue: undefined,
                        valueType: 'select',
                    }
                }
            }
        }
    },
	methods: {
        getFeatures() {
            fetchFeatureByNode.execute({
                productOid: this.$route.params.oid,
                oid: this.nodeInfo.oid,
                modelType: this.nodeInfo.modelType,
            }).then((res) => {
                this.featureList = res;
            }).catch((err) => {
                if (err.msg) {
                    this.$error(err.msg);
                }
            });
        },
        getValueByFeature() {
            if (this.form.featureName) {
                let featrue = this.featureList.filter(item => item.name === this.form.featureName)[0];
                fetchValueByFeature.execute({
                    featureOid: featrue.oid,
                    featureType: featrue.modelType,
                }).then((res) => {
                    this.valueList = res;
                }).catch((err) => {
                    if (err.msg) {
                        this.$error(err.msg);
                    }
                });
            }
        },
        onChangeFeatureName() {
            this.form.featureValue = undefined;
            this.form.valueType = this.featureList.filter(item => item.name === this.form.featureName)[0].valueType;
            this.form.number = this.featureList.filter(item => item.name === this.form.featureName)[0].number;
        },
        onCancel() {
            this.$emit('close');
        },
        handleCreate() {
            this.$refs.createForm.validate((valid) => {
				if (valid) {
					let api = createFeatureValue;
                    let msg = '';
                    if (this.featureItem.oid) {
                        api = updateFeatureValue;
                        msg = '修改成功！';
                    } else {
                        api = createFeatureValue;
                        msg = '添加成功！';
                    }
                    api.execute({
                        productOid: this.$route.params.oid,
                        masterOid: this.nodeInfo.oid,
                        masterType: this.nodeInfo.modelType,
                        featureValueData: {
                            oid: this.featureItem.oid,
                            featureName: this.form.featureName,
                            featureValue: this.form.featureValue,
                            number: this.form.number,
                            valueType: this.form.valueType,
                        }
                    }).then((res) => {
                        this.$success(msg);
                        this.onCancel();
                        this.$emit('getList');
                    }).catch((err) => {
                        if (err.msg) {
                            this.$error(err.msg);
                        }
                    });
				} else {
					return false;
				}
			});
        },
  	},
}
</script>

<style lang="less" scoped>
.form-item-btns {
    margin: 30px 0 0;
}
.form-btn-cancel {
    margin-left: 8px;
}
</style>
