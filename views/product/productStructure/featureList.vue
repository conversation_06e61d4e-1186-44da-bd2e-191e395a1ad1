<template>
    <div class="feature-list-wrap">
        <div class="head-wrap flex justify-between">
            <div class="flex align-center">
                <a-button type="primary" @click="onAdd">新建特性</a-button>
                <a-input-search v-model.trim="searchKey" class="search-input" allow-clear
                    placeholder="输入关键词搜索" @search="onSearch" @blur="onSearch" />
            </div>
            <div class="flex">
                <a-select v-model.trim="searchNode" allow-clear placeholder="选择节点"
                    class="node-input" @focus="getAllNodes" @change="onChangeNode">
                    <a-select-option
                        v-for="item in nodeList"
                        :key="item.oid"
                        :value="item.oid">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
                <a-button class="import-btn" @click="onOpenImportModal">导入</a-button>
                <a-button @click="onExport">导出</a-button>
            </div>
        </div>
        <div class="body-wrap">
            <a-spin :spinning="loading">
                <div class="feature-item" v-for="feature of featureData" :key="feature.oid">
                    <div class="feature-title font-700 flex align-center">
                        <span>{{feature.name}}</span>
                        <svg class="jwifont" aria-hidden="true"
                            v-if="feature.isFold" @click="onFold(feature)">
                            <use xlink:href="#jwi-arrow-down"></use>
                        </svg>
                        <svg class="jwifont" aria-hidden="true"
                            v-else @click="onFold(feature)">
                            <use xlink:href="#jwi-arrow-up"></use>
                        </svg>
                    </div>
                    <a-row :gutter="[16, 16]" v-show="!feature.isFold">
                        <a-col :span="8" v-for="item of feature.labelDatas" :key="item.oid">
                            <div class="feature-card flex justify-between align-center"
                                @mouseenter="onShowIcon(item, true)"
                                @mouseleave="onShowIcon(item, false)">
                                <div class="feature-item-left">
                                    <div class="feature-name flex align-center">
                                        <svg class="jwifont" aria-hidden="true">
                                            <use xlink:href="#jwi-tag"></use>
                                        </svg>
                                        <span>{{item.name}}</span>
                                    </div>
                                    <div class="feature-value flex" v-if="item.valueType==='select'">
                                        <a-popover
                                            trigger="click"
                                            v-model.trim="item.visible"
                                            placement="bottomLeft">
                                            <div slot="title" class="rename-title font-700">新建标签</div>
                                            <template slot="content">
                                                <a-form-model
                                                    :ref="'addLabelForm'+item.oid"
                                                    :model="form"
                                                    :rules="rules">
                                                    <a-form-model-item
                                                        label="名称"
                                                        prop="name">
                                                        <a-input
                                                            v-model.trim="form.name"
                                                            :max-length="50"
                                                            allow-clear
                                                            placeholder="请输入" />
                                                    </a-form-model-item>
                                                    <a-form-model-item class="text-right">
                                                        <a-button type="primary"
                                                            @click="onCreateLabel(item)">
                                                            确认新建
                                                        </a-button>
                                                    </a-form-model-item>
                                                </a-form-model>
                                            </template>
                                            <div class="flex align-center feature-tag tag-add">
                                                <svg class="jwifont" aria-hidden="true">
                                                    <use xlink:href="#jwi-plus"></use>
                                                </svg>
                                                <span>标签</span>
                                            </div>
                                        </a-popover>
                                        <div class="flex align-center feature-tag tag-value"
                                            v-for="val in item.labelValueDatas" :key="val.oid">
                                            <span>{{ val.value }}</span>
                                            <svg class="jwifont" aria-hidden="true"
                                                @click="ondeleteLabel(val)">
                                                <use xlink:href="#jwi-close"></use>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="feature-value flex" v-if="item.valueType==='text'">
                                        <div class="flex align-center feature-tag tag-value">
                                            <span>文本输入</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="feature-item-right">
                                    <svg class="jwifont" aria-hidden="true"
                                        v-if="item.isShowBtn" @click="onEditFeature(item, feature)">
                                        <use xlink:href="#jwi-edit"></use>
                                    </svg>
                                    <svg class="jwifont" aria-hidden="true"
                                        v-if="item.isShowBtn" @click="onDelete(item, feature)">
                                        <use xlink:href="#jwi-delete"></use>
                                    </svg>
                                </div>
                            </div>
                        </a-col>
                    </a-row>
                </div>
                <a-empty v-if="featureData.length===0" />
            </a-spin>
        </div>
        <feature-add
            :visible="visibleAdd"
            :nodeInfo="nodeInfo"
            :featureInfo="featureInfo"
            @close="onClose"
            @getList="getAllFeatures">
        </feature-add>
        <download-modal
            :visible="visibleDown"
            @close="onCloseDownModal"
            @getList="getAllFeatures">
        </download-modal>
    </div>
</template>

<script>
import featureAdd from './featureAdd';
import downloadModal from './downloadModal';
import {
    fetchRulesNodes,
    fetchFeatures,
    deleteFeature,
    addLabelValue,
    deleteLabelValue,
} from 'apis/product/productStructure';
import validateMixin from './validateMixin';
import {getCookie} from 'jw_utils/cookie';
export default {
    name: 'featureList',
    props: [

    ],
    components: {
        featureAdd,
        downloadModal,
    },
    mixins: [validateMixin],
    data() {
        return {
            searchKey: '',
            productName: '',
            searchNode: undefined,
            nodeList: [],
            featureData: [],
            loading: true,
            visibleAdd: false,
            form: {
                name: '',
			},
			rules: {
                name: [
					{ required: true, message: '请输入', trigger: 'change' },
                    { min: 1, max: 50, message: '不能超过50个字符', trigger: 'change' },
                    // { validator: this.validateName },
				],
			},
            nodeInfo: {},
            featureInfo: {},
            visibleDown: false,
        };
    },
    mounted() {
        this.getAllFeatures();
    },
    methods: {
        goBack() {
            this.$router.push(`/productStructure/${this.$route.params.oid}/${this.$route.params.type}`);
        },
        onSearch() {
            this.getAllFeatures();
        },
        getAllNodes() {
            fetchRulesNodes.execute({
                productOid: this.$route.params.oid,
            }).then((res) => {
                this.nodeList = res;
            }).catch((err) => {
                if (err.msg) {
                    this.$error(err.msg);
                }
            });
        },
        onChangeNode() {
            this.getAllFeatures();
        },
        getAllFeatures() {
            this.loading = true;
            fetchFeatures.execute({
                productOid: this.$route.params.oid,
                searchKey: this.searchKey,
                masterOid: this.searchNode,
            }).then((res) => {
                this.featureData = res.map(item => {
                    item.isFold = false;
                    if (item.labelDatas) {
                        item.labelDatas.forEach(val => {
                            val.isShowBtn = false;
                        })
                    }
                    return item;
                });
                this.loading = false;
            }).catch((err) => {
                this.loading = false;
                if (err.msg) {
                    this.$error(err.msg);
                }
            });
        },
        onAdd() {
            this.nodeInfo = {};
            this.featureInfo = {};
            this.visibleAdd = true;
        },
        onClose() {
            this.visibleAdd = false;
            this.nodeInfo = {};
            this.featureInfo = {};
        },
        onEditFeature(item, feature) {
            this.nodeInfo = { ...feature };
            this.featureInfo = { ...item };
            this.visibleAdd = true;
        },
        onShowIcon(item, flag) {
            item.isShowBtn = flag;
        },
        onDelete(item, feature) {
            item.isShowBtn = true;
            deleteFeature.execute({
                productOid: this.$route.params.oid,
                masterOid: feature.oid,
                masterType: feature.modelType,
                featureOids: [item.oid],
            }).then((res) => {
                this.$success('删除成功！');
                this.getAllFeatures();
            }).catch((err) => {
                if (err.msg) {
                    this.$error(err.msg);
                }
            });
        },
        onCreateLabel(item) {
            this.$refs['addLabelForm'+item.oid][0].validate((valid) => {
				if (valid) {
					addLabelValue.execute({
                        featureOid: item.oid,
                        featureType: item.modelType,
                        labelValueData:{
                            value: this.form.name,
                        },
                    }).then((res) => {
                        this.$success('添加成功！');
                        item.visible = false;
                        this.$refs['addLabelForm'+item.oid][0].resetFields();
                        this.$refs['addLabelForm'+item.oid][0].clearValidate();
                        this.getAllFeatures();
                    }).catch((err) => {
                        if (err.msg) {
                            this.$error(err.msg);
                        }
                    });
				} else {
					return false;
				}
			});
        },
        ondeleteLabel(item) {
            deleteLabelValue(item.oid).execute(
                
            ).then((res) => {
                this.$success('删除成功！');
                this.getAllFeatures();
            }).catch((err) => {
                if (err.msg) {
                    this.$error(err.msg);
                }
            });
        },
        onFold(item) {
            item.isFold = !item.isFold;
        },
        onOpenImportModal() {
            this.visibleDown = true;
        },
        onCloseDownModal() {
            this.visibleDown = false;
        },
        onExport() {
            let xhr = new XMLHttpRequest();
            xhr.open('POST', `${Jw.gateway}/${Jw.configMicroService}/featuremanage/exportExcel`, true);
            // xhr.setRequestHeader('Content-Type', 'application/json');
            // xhr.setRequestHeader('Accept', 'application/json, text/plain, */*');
            xhr.setRequestHeader('accesstoken', getCookie('token'));
            xhr.setRequestHeader('tenantAlias', getCookie("tenantAlias"));
            xhr.setRequestHeader('tenantOid', getCookie("tenantOid"));
            xhr.setRequestHeader('appName', 'pdm');
            xhr.responseType = 'blob';
            xhr.onload = () => {
                var type = xhr.getResponseHeader('Content-Type');
                var blob = new Blob([xhr.response], {type: type});
                var objectUrl = URL.createObjectURL(blob);
                var a = document.createElement('a');
                a.href = objectUrl;
                a.download = 'featureManageExport.xlsx';
                document.body.appendChild(a);
                a.click();
                a.remove(); 
            };
            xhr.send(JSON.stringify({
                code: 'FeatureManExport',
                param: {
                    oids: [],
                    productOid: this.$route.params.oid,
                }
            }));
        },
    },
};
</script>

<style lang="less" scoped>
.feature-list-wrap {
    padding: 16px 20px;
    .jwifont {
        width: 16px;
        min-width: 16px;
        height: 16px;
        min-height: 16px;
    }
    .head-wrap {
        .search-input {
            width: 216px;
            margin-left: 8px;
        }
        .node-input {
            width: 160px;
            margin-right: 8px;
        }
        .import-btn {
            margin-right: 8px;
        }
    }
    .body-wrap {
        height: calc(100vh - 287px);
        margin-top: 15px;
        overflow: auto;
        &::-webkit-scrollbar {
            width: 1px;
        }
        .feature-item {
            margin-bottom: 20px;
            .feature-title {
                position: relative;
                padding-left: 10px;
                margin-bottom: 16px;
                font-size: 16px;
                color: rgba(30,32,42,0.85);
                &:after {
                    content: "";
                    position: absolute;
                    width: 3px;
                    height: 20px;
                    left: 0;
                    top: 2px;
                    border-radius: 3px;
                    background: #255ed7;
                }
                span {
                    margin-right: 8px;
                }
            }
            .ant-row {
                width: 100%;
                display: flex;
                align-items: stretch;
                flex-wrap: wrap;
            }
            .feature-card {
                height: 100%;
                padding: 16px 20px;
                border: 1px solid rgba(30, 32, 42, 0.15);
                border-radius: 4px;
                .feature-item-left {
                    align-self: flex-start;
                    margin-right: 10px;
                    .feature-name {
                        margin-bottom: 20px;
                        span {
                            margin-left: 8px;
                        }
                    }
                    .feature-value {
                        flex-wrap: wrap;
                        .feature-tag {
                            line-height: 22px;
                            padding: 0 8px;
                            margin-right: 8px;
                            margin-bottom: 8px;
                            font-size: 12px;
                            border-radius: 4px;
                            .jwifont {
                                width: 12px;
                                min-width: 12px;
                                height: 12px;
                                min-height: 12px;
                            }
                            &.tag-add {
                                border: 1px dashed rgba(30, 32, 42, 0.15);
                                background: #fff;
                                cursor: pointer;
                                .jwifont {
                                    margin-right: 8px;
                                }
                            }
                            &.tag-value {
                                border: 1px solid rgba(30, 32, 42, 0.15);
                                background: rgba(30, 32, 42, 0.04);
                                .jwifont {
                                    margin-left: 8px;
                                    cursor: pointer;
                                }
                            }
                        }
                    }
                }
                .feature-item-right {
                    min-width: 60px;
                    white-space: nowrap;
                    .jwifont {
                        cursor: pointer;
                        &:first-child {
                            margin-right: 20px;
                        }
                    }
                }
            }
        }
        .ant-empty {
            margin-top: 30px;
        }
    }
}
</style>
