<template>
    <a-modal
        :visible="visible"
        :title="featureInfo.oid?'编辑特性':'新建特性'"
        :mask-closable="false"
        :footer="null"
        :destroyOnClose="true"
        @cancel="onCancel"
    >
        <div class="create-temp-wrap">
            <a-form-model ref="createFeatureForm" :model="form" :rules="rules">
                <a-form-model-item label="节点名称" prop="node">
                    <a-select v-model.trim="form.node" placeholder="请选择"
                        :disabled="featureInfo.oid?true:false">
                        <a-select-option
                            v-for="item in nodeList"
                            :key="item.oid"
                            :value="item.oid">
                            {{ item.name }}
                        </a-select-option>
                    </a-select>
                </a-form-model-item>
                <a-form-model-item label="特性名称" prop="name">
                    <a-input
                        v-model.trim="form.name"
                        :max-length="50"
                        allow-clear
                        placeholder="请输入" />
                </a-form-model-item>
                <!-- <a-form-model-item label="特性类型" prop="valueType">
                    <a-radio-group v-model.trim="form.valueType" default-value="select">
                        <a-radio value="select">标签选择</a-radio>
                        <a-radio value="text">文本输入</a-radio>
                    </a-radio-group>
                </a-form-model-item> -->
                <a-form-model-item class="form-item-btns text-right">
                    <a-button type="primary" @click="onCreate">{{$t('btn_ok')}}</a-button>
                    <a-button class="form-btn-cancel" @click="onCancel">{{$t('btn_cancel')}}</a-button>
                </a-form-model-item>
            </a-form-model>
        </div>
    </a-modal>
</template>

<script>
import {
    fetchStructureNode,
    addFeatures,
    updateFeature,
} from 'apis/product/productStructure';
import validateMixin from './validateMixin';
export default {
    name: 'featureAdd',
    props: [
        'visible',
        'nodeInfo',
        'featureInfo',
    ],
    mixins: [validateMixin],
    data() {
		return {
            form: {},
			rules: {
                node: [
                    { required: true, message: '请选择', trigger: 'change' },
				],
                name: [
					{ required: true, message: '请输入', trigger: 'change' },
                    { min: 1, max: 50, message: '不能超过50个字符', trigger: 'change' },
                    { validator: this.validateName },
				],
                // valueType: [
                //     { required: true },
                // ],
			},
            nodeList: [],
		}
    },
    mounted() {
        
	},
    watch: {
        visible(val) {
            if (val) {
                this.getNodeList();
                if (this.featureInfo.oid) {
                    this.form = { ...this.featureInfo };
                    this.form.node = this.nodeInfo.oid;
                } else {
                    this.form = {
                        node: undefined,
                        name: '',
                        valueType: 'select',
                    }
                }
            }
        }
    },
	methods: {
        getNodeList() {
            fetchStructureNode.execute({
                productOid: this.$route.params.oid,
            }).then((res) => {
                this.nodeList = res;
            }).catch((err) => {
                if (err.msg) {
                    this.$error(err.msg);
                }
            });
        },
        onCancel() {
            this.$emit('close');
        },
        onCreate() {
            this.$refs.createFeatureForm.validate((valid) => {
				if (valid) {
                    let api = addFeatures;
                    let msg = '';
                    if (this.featureInfo.oid) {
                        api = updateFeature;
                        msg = '修改成功！';
                    } else {
                        api = addFeatures;
                        msg = '添加成功！';
                    }
					api.execute({
                        productOid: this.$route.params.oid,
                        masterOid: this.form.node,
                        masterType: this.nodeList.filter(item => item.oid === this.form.node)[0].modelType,
                        featureData: {
                            oid: this.form.oid,
                            name: this.form.name,
                            valueType: this.form.valueType,
                        }
                    }).then((res) => {
                        this.$success(msg);
                        this.onCancel();
                        this.$emit('getList');
                    }).catch((err) => {
                        if (err.msg) {
                            this.$error(err.msg);
                        }
                    });
				} else {
					return false;
				}
			});
        },
  	},
}
</script>

<style lang="less" scoped>
.form-item-btns {
    margin: 30px 0 0;
}
.form-btn-cancel {
    margin-left: 8px;
}
</style>
