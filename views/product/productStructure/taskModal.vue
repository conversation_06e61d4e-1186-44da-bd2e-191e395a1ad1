<template>
    <a-modal
        :visible="visible"
        title="新建任务"
        :mask-closable="false"
        :footer="null"
        centered
        @cancel="handleCancel"
    >
        <div class="create-temp-wrap">
            <a-form-model
                ref="createForm"
                :model="form"
                :rules="rules">
                <a-form-model-item label="名称" prop="name">
                    <a-input v-model.trim="form.name" :max-length="50" allow-clear placeholder="请输入" />
                </a-form-model-item>
                <a-form-model-item label="处理人" prop="handlerName">
                    <a-popover
                        trigger="click"
                        v-model.trim="visibleUser"
                        placement="bottomLeft">
                        <template slot="content">
                            <a-select
                                show-search
                                allowClear
                                v-model.trim="form.handlerName"
                                placeholder="请输入姓名、手机号、邮箱"
                                style="width: 250px"
                                :default-active-first-option="false"
                                :show-arrow="false"
                                :filter-option="false"
                                @focus="handleSearch"
                                @search="handleSearch"
                                @change="handleChange"
                            >
                                <a-select-option v-for="item in userData" :key="item.oid"
                                    :value="item.oid">
                                    <div class="user-wrap flex align-center">
                                        <div class="handler-avatar" v-if="item.avatarUrl">
                                            <img :src="item.avatarUrl" alt="">
                                        </div>
                                        <div v-else class="handler-avatar">{{item.name.slice(0,1).toUpperCase()}}</div>
                                        <div>{{ item.name }}</div>
                                    </div>
                                </a-select-option>
                            </a-select>
                        </template>
                        <a-button v-if="form.handlerName"
                            type="primary" ghost shape="circle">
                            <svg class="jwifont" aria-hidden="true">
                                <use xlink:href="#jwi-change"></use>
                            </svg>
                        </a-button>
                        <a-button v-else
                            type="primary" ghost shape="circle" icon="plus" />
                    </a-popover>
                    <div class="handler-wrap inline-flex align-center" v-if="form.handlerName">
                        <div class="handler-avatar" v-if="form.handlerUrl">
                            <img :src="form.handlerUrl" alt="">
                        </div>
                        <div v-else class="handler-avatar">{{form.handlerName.slice(0,1).toUpperCase()}}</div>
                        <div>{{ form.handlerName }}</div>
                    </div>
                </a-form-model-item>
                <a-row :gutter="10">
                    <a-col :span="12">
                        <a-form-model-item label="任务周期（天）" prop="period">
                            <a-input-number v-model.trim="form.period" :precision="0" :min="1" />
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-model-item label="开始日期" prop="startTime">
                            <a-date-picker v-model.trim="form.startTime" valueFormat="YYYY-MM-DD" allow-clear />
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-form-model-item label="描述" prop="description">
                    <a-textarea v-model.trim="form.description" allow-clear
                        class="textarea-input" placeholder="请输入" />
                </a-form-model-item>
                <a-form-model-item class="form-item-btns text-right">
                    <a-button type="primary" @click="handleCreate">{{$t('btn_ok')}}</a-button>
                    <a-button class="form-btn-cancel" @click="handleCancel">{{$t('btn_cancel')}}</a-button>
                </a-form-model-item>
            </a-form-model>
        </div>
    </a-modal>
</template>

<script>
import { userListApi } from 'apis/user';
import {
    addTask,
} from 'apis/product/productStructure';
import validateMixin from './validateMixin';
export default {
    name: 'taskModal',
    props: [
        'visible',
        'nodeInfo',
        'rowInfo',
    ],
    mixins: [validateMixin],
    components: {

    },
    data(){
		return {
            form: {
                name: '',
                handlerName: '',
                period: 1,
                startTime: '',
                description: '',
            },
            rules: {
                name: [
                    { required: true, message: '请输入', trigger: 'change' },
                    { min: 1, max: 50, message: '不能超过50个字符', trigger: 'change' },
                    { validator: this.validateName },
                ],
                period: [{ required: true, message: '请输入', trigger: 'change' }],
                startTime: [{ required: true, message: '请输入', trigger: 'change' }],
                handlerName: [{ required: true, message: '请选择', trigger: 'change' }],
            },
            visibleUser: false,
            userData: [],
		}
    },
	mounted() {

	},
	methods: {
        handleSearch(value) {
            userListApi.execute({
                pageNum: 1,
                pageSize: 50,
                tenantOid: Jw.getUser().tenantOid,
                keyword: value,
            }).then(res => {
                this.userData = res.rows.map(item => {
                    if (item.avatar) {
                        item.avatarUrl = `${Jw.gateway}/${Jw.fileServer}/file/downloadByOid?oid=${item.avatar}`;
                    }
                    return item;
                });
            }).catch(err => {
                this.$error(err.msg);
            });
        },
        handleChange(value) {
            if (value) {
                this.form.handlerOid = value;
                this.form.handlerName = this.userData.filter(item => item.oid === value)[0].name;
                let avatar = this.userData.filter(item => item.oid === value)[0].avatar;
                if (avatar) {
                    this.form.handlerUrl = `${Jw.gateway}/${Jw.fileServer}/file/downloadByOid?oid=${avatar}`;
                } else {
                    this.form.handlerUrl = '';
                }
                this.visibleUser = false;
            }
        },
        handleCreate() {
            this.$refs.createForm.validate((valid) => {
				if (valid) {
					addTask.execute({
                        productOid: this.$route.params.oid,
                        masterOid: this.nodeInfo.oid,
                        masterType: this.nodeInfo.modelType,
                        task: this.form,
                    }).then((res) => {
                        this.$success('添加成功！');
                        this.handleCancel();
                        this.$emit('getList');
                    }).catch((err) => {
                        if (err.msg) {
                            this.$error(err.msg);
                        }
                    });
				} else {
					return false;
				}
			});
        },
        handleCancel() {
            this.$refs.createForm.resetFields();
            this.$refs.createForm.clearValidate();
            this.$emit('close');
        },
  	},
}
</script>

<style lang="less" scoped>
.textarea-input /deep/.ant-input {
    height: 110px;
}
.form-btn-cancel {
    margin-left: 8px;
}
.jwifont {
    width: 16px;
    min-width: 16px;
    height: 16px;
    min-height: 16px;
    vertical-align: text-bottom;
}
.handler-wrap {
    margin-left: 8px;
    padding: 4px 8px;
    line-height: 20px;
    background: rgba(30, 32, 42, 0.04);
    border-radius: 4px;
}
.handler-avatar {
    margin-right: 8px;
    width: 24px;
    height: 24px;
    line-height: 20px;
    text-align: center;
    color: #255ed7;
    border-radius: 50%;
    border: 2px solid #255ed7;
    img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
    }
}
.user-wrap {
    padding: 5px 8px;
    border: 1px solid transparent;
}
.ant-input-number,
.ant-calendar-picker {
    width: 100%;
}
</style>
<style lang="less">
.drop-style {
    .ant-select-dropdown-menu-item-active:not(.ant-select-dropdown-menu-item-disabled) {
        background: #fff;
        .user-wrap {
            border-radius: 4px;
            background: #f0f7ff;
            border: 1px solid #a4c9fc;
        }
    }
}
</style>
