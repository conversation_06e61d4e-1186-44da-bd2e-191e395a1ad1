<template>
    <a-modal
        :visible="visible"
        title="新建子节点"
        :mask-closable="false"
        :footer="null"
        @cancel="onCancel"
    >
        <a-form-model ref="addNodeForm" :model="form" :rules="rules">
            <a-form-model-item label="节点类型" prop="modelType">
                <a-select v-model.trim="form.modelType" allow-clear placeholder="请选择"
                    @focus="getNodeType">
                    <a-select-option
                        v-for="item in typeList"
                        :key="item.name"
                        :value="item.name">
                        {{ item.displayName }}
                    </a-select-option>
                </a-select>
            </a-form-model-item>
            <a-form-model-item label="节点名称" prop="name">
                <a-input
                    v-model.trim="form.name"
                    :max-length="50"
                    allow-clear
                    placeholder="请输入" />
            </a-form-model-item>
            <a-form-model-item class="form-item-btns text-right">
                <a-button type="primary" @click="onCreate">确认新建</a-button>
                <a-button class="form-btn-cancel" @click="onCancel">{{$t('btn_cancel')}}</a-button>
            </a-form-model-item>
        </a-form-model>
    </a-modal>
</template>

<script>
import {
    fetchChildNodeModel,
    createProductNode,
} from 'apis/product/productStructure';
import validateMixin from './validateMixin';
export default {
    name: 'addNodeModal',
    props: [
        'visible',
        'nodeInfo',
    ],
    mixins: [validateMixin],
    data() {
		return {
            form: {
                modelType: undefined,
                name: '',
			},
			rules: {
                modelType: [
					{ required: true, message: '请选择', trigger: 'change' },
				],
                name: [
					{ required: true, message: '请输入', trigger: 'change' },
                    { min: 1, max: 50, message: '不能超过50个字符', trigger: 'change' },
                    { validator: this.validateName },
				],
			},
            typeList: [],
		}
    },
    mounted() {

	},
	methods: {
        getNodeType() {
            fetchChildNodeModel
            .execute()
            .then((res) => {
                this.typeList = res;
            })
            .catch((err) => {   
                if (err.msg) {
                    this.$error(err.msg)
                }
            });
        },
        onCreate() {
            this.$refs.addNodeForm.validate((valid) => {
				if (valid) {
					createProductNode.execute({
                        masterOid: this.nodeInfo.oid,
                        masterType: this.nodeInfo.modelType,
                        data: this.form,
                    }).then((res) => {
                        this.$success('添加成功！');
                        this.$refs.addNodeForm.resetFields();
                        this.$refs.addNodeForm.clearValidate();
					    this.$emit('close', this.nodeInfo);
                    }).catch((err) => {
                        if (err.msg) {
                            this.$error(err.msg)
                        }
                    });
				} else {
					return false;
				}
			});
        },
        onCancel() {
            this.$refs.addNodeForm.resetFields();
            this.$refs.addNodeForm.clearValidate();
            this.$emit('close');
        },
  	},
}
</script>

<style lang="less" scoped>
.form-item-btns {
    margin: 30px 0 0;
}
.form-btn-cancel {
    margin-left: 8px;
}
</style>
