<template>
    <div class="create-instance-wrap">
        <div class="head-wrap flex justify-between align-center">
            <div class="flex align-center">
                <svg class="jwifont text-link" aria-hidden="true"
                    v-if="isCheck" @click="goBack">
                    <use xlink:href="#jwi-back"></use>
                </svg>
                <svg class="jwifont text-link" aria-hidden="true"
                    v-else @click="goCheck">
                    <use xlink:href="#jwi-back"></use>
                </svg>
                <span class="head-title color85 font-700">创建实例</span>
                <span class="color45" v-if="isCheck">第一步：配置相关清单，生成配置清单</span>
                <span class="color45" v-else>第二步：预览并确认清单项，保存并创建实例。</span>
            </div>
            <div class="flex">
                <a-button v-if="isCheck" type="primary" @click="onOptional">确认选配</a-button>
                <a-button v-else type="primary" @click="onCreate">创建实例</a-button>
                <a-button class="head-cancel-btn" @click="onCancel">{{$t('btn_cancel')}}</a-button>
            </div>
        </div>
        <div class="body-wrap">
            <a-table
                v-if="isCheck"
                :columns="columns" 
                :data-source="tableData" 
                rowKey="id"
                :pagination="false"
                bordered
                :rowClassName="rowClassName"
                :loading="loading"
                :scroll="{ x: 800, y: 'calc(100vh - 225px)' }"
            >
                <template slot="featureValue" slot-scope="text, record">
                    <a-select v-model.trim="record.value" placeholder="请选择"
                        v-if="record.valueType==='select'"
                        :disabled="record.disabled"
                        @change="onChangeValue(record)">
                        <a-select-option
                            v-for="item in record.labelValueDatas"
                            :key="item.oid"
                            :value="item.value"
                        >
                            {{ item.value }}
                        </a-select-option>
                    </a-select>
                    <a-input
                        v-if="record.valueType==='text'"
                        v-model.trim="record.value"
                        :disabled="record.disabled"
                        allow-clear
                        placeholder="请输入"
                        @blur="onChangeValue(record)" />
                </template>
            </a-table>
            <a-table
                v-else
                :columns="cols" 
                :data-source="listData" 
                rowKey="id"
                :pagination="false"
                bordered
                :loading="loading"
                :scroll="{ x: 800, y: 'calc(100vh - 225px)' }"
            >
                <template slot="name" slot-scope="text, record">
                    <div class="flex align-center">
                        <svg class="jwifont" aria-hidden="true">
                            <use xlink:href="#jwi-part"></use>
                        </svg>
                        <div class="name-text text-ellipsis" :title="text">{{ text }}</div>
                    </div>
                </template>
                <template slot="number" slot-scope="text">
                    <div>{{ text }}</div>
                </template>
                <template slot="version" slot-scope="text">
                    <div class="tag-wrap">{{ text }}</div>
                </template>
                <template slot="viewName" slot-scope="text">
                    <div class="tag-wrap">{{ text }}</div>
                </template>
                <template slot="count" slot-scope="text, record">
                    <a-input-number v-model.trim="record.count"
                        :precision="0" :min="1" />
                </template>
            </a-table>
        </div>
       <!--  <DialogWithComponentsbuild
            slot="dialog"
            ref="dialogWithComponentbuild"
            :layoutName="'create'"
            :modelName="'EndProduct'"
            :slotName="'save'"
            :title="'新建成品'"
            :instanceData="instanceData"
            :delayHide="true"
            @on-save="saveHandler">
        </DialogWithComponentsbuild> -->
    </div>
</template>

<script>
// import DialogWithComponentsbuild from '../../source-store/components/detail/dialog-with-componentbuilder';
import {
    createOptionalInstance,
    fetchConditionInstance,
    fetchOptionalInstance,
    createInstance,
} from 'apis/product/productStructure';
export default {
    name: 'createInstance',
    components: {
        // DialogWithComponentsbuild,
    },
    data() {
        return {
            isCheck: true,
            loading: true,
            columns: [
				{
                    title: '可选项',
                    dataIndex: 'masterName',
                    key: 'masterName',
                    width: '33%',
                    customRender: (text, row, index) => {
                        const obj = {
                            children: text,
                            attrs: {},
                        };
                        if (row.featureSize >= 1) {
                            obj.attrs.rowSpan = row.featureSize;
                        } else {
                            obj.attrs.rowSpan = 0;
                        }
                        return obj;
                    },
                },
                {
                    title: '特性',
                    dataIndex: 'name',
                    key: 'name',
                },
                {
                    title: '特性值',
                    dataIndex: 'featureValue',
                    key: 'featureValue',
                    scopedSlots: { customRender: 'featureValue' },
                },
			],
            tableData: [],
            cols: [
				{
                    title: '名称',
                    dataIndex: 'name',
                    key: 'name',
                    scopedSlots: { customRender: 'name' },
                },
                {
                    title: '编码',
                    dataIndex: 'number',
                    key: 'number',
                    scopedSlots: { customRender: 'number' },
                },
                {
                    title: '版本',
                    dataIndex: 'version',
                    key: 'version',
                    scopedSlots: { customRender: 'version' },
                },
                {
                    title: '视图',
                    dataIndex: 'viewName',
                    key: 'viewName',
                    scopedSlots: { customRender: 'viewName' },
                },
                {
                    title: '数量',
                    dataIndex: 'count',
                    key: 'count',
                    width: 180,
                    scopedSlots: { customRender: 'count' },
                },
			],
            listData: [],
            instanceData: {},
        };
    },
    mounted() {
        this.getConList();
    },
    methods: {
        goBack() {
            this.$router.push(`/productStructure/${this.$route.params.oid}/${this.$route.params.type}`);
        },
        getConList() {
            this.loading = true;
            fetchConditionInstance.execute({
                productOid: this.$route.params.oid,
                productType: this.$route.params.type,
            }).then(res => {
                this.tableData = res.map(item => {
                    item.value = undefined;
                    item.isShow = true;
                    return item;
                });
                this.loading = false;
            }).catch(err => {
                this.loading = false;
                if (err.msg) {
                    this.$error(err.msg);
                }
            })
        },
        rowClassName(record) {
            if (!record.isShow) {
                return 'hidden-row';
            }
            if (record.readonly) {
                return 'readonly-color';
            }
            return '';
        },
        onChangeValue(row) {
            let arr = [];
            this.tableData.forEach(item => {
                if (item.sortIndex > row.sortIndex) {
                    item.value = undefined;
                    item.disabled = true;
                    item.readonly = true;
                }
            })
            this.tableData.forEach(item => {
                if (item.value) {
                    let temp = {
                        masterOid: item.masterOid,
                        masterName: item.masterName,
                        number: item.number,
                        name: item.name,
                        value: item.value,
                    }
                    arr.push(temp);
                }
            })
            fetchOptionalInstance.execute({
                productOid: this.$route.params.oid,
                data: arr,
            }).then(res => {
                if (res.triggerCount > 0) {
                    this.tableData = res.data.map(item => {
                        if (item.labelValueDatas && item.labelValueDatas.length === 1) {
                            item.disabled = true;
                        } else {
                            item.disabled = false;
                            item.readonly = false;
                        }
                        return item;
                    });
                }
            }).catch(err => {
                if (err.msg) {
                    this.$error(err.msg);
                }
            })
        },
        onOptional() {
            if (this.tableData.length > 0) {
                if (this.tableData.every(row => row.value)) {
                    this.isCheck = false;
                    this.loading = true;
                    let arr = [];
                    this.tableData.forEach(item => {
                        let temp = {
                            masterOid: item.masterOid,
                            name: item.name,
                            number: item.number,
                            value: item.value,
                            condition: '=',
                        }
                        arr.push(temp);
                    })
                    createOptionalInstance.execute({
                        productOid: this.$route.params.oid,
                        productType: this.$route.params.type,
                        optionDatas: arr,
                    }).then(res => {
                        this.listData = res;
                        this.loading = false;
                    }).catch(err => {
                        this.loading = false;
                        if (err.msg) {
                            this.$error(err.msg);
                        }
                    })
                } else {
                    this.$warning('请完整填写选配清单！');
                }
            }
        },
        onCreate() {
            this.$refs.dialogWithComponentbuild.show();
            this.instanceData = {
                masterType: 'Container',
                relationName: 'ModelModelLink',
                secondaryType: 'EndProduct',
            };
        },
        saveHandler(value) {
            if (_.isObject(value.view)) {
                value.viewName = value.view.name;
                value.viewOid = value.view.oid;
            }
            createInstance.execute({
                masterType: this.$route.params.type,
                masterOid: this.$route.params.oid,
                modelType: 'EndProduct',
                nodeData: value,
                parts: this.listData,
            }).then(res => {
                this.$refs.dialogWithComponentbuild.hide();
                this.$success('创建成功！');
                this.$router.push(`/productStructure/${this.$route.params.oid}/${this.$route.params.type}`);
            }).catch(err => {
                if (err.msg) {
                    this.$error(err.msg);
                }
            });
        },
        goCheck() {
            this.isCheck = true;
        },
        onCancel() {
            this.$router.push(`/productStructure/${this.$route.params.oid}/${this.$route.params.type}`);
        },
    },
};
</script>

<style lang="less" scoped>
.create-instance-wrap {
    margin: 5px;
    padding: 16px 20px;
    background: var(--light);
    box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
    border-radius: 4px;
    .jwifont {
        width: 16px;
        min-width: 16px;
        height: 16px;
        min-height: 16px;
    }
    .head-wrap {
        .head-title {
            margin: 0 16px 0 8px;
            font-size: 20px;
        }
        .head-cancel-btn {
            margin-left: 8px;
        }
    }
    .body-wrap {
        height: calc(100vh - 180px);
        margin-top: 12px;
        overflow: auto;
        &::-webkit-scrollbar {
            width: 0px;
        }
        /deep/.ant-table-thead > tr > th {
            padding: 9px 16px;
        }
        /deep/.ant-table-tbody > tr > td {
            padding: 7px 16px;
        }
        /deep/.ant-table .hidden-row {
            display: none;
        }
        /deep/.ant-table .readonly-color {
            .ant-select-disabled .ant-select-selection {
                background: #fff;
            }
        }
        .ant-input-affix-wrapper,
        .ant-select {
            width: 80%;
        }
        .name-text {
            margin-left: 8px;
            color: #255ed7;
        }
        .tag-wrap {
            width: fit-content;
            padding: 2px 8px;
            font-size: 12px;
            color: rgba(30,32,42,0.65);
            background: rgba(30,32,42,0.04);
            border: 1px solid rgba(30,32,42,0.15);
            border-radius: 4px;
        }
    }
}
</style>
