<template>
    <div class="feature-manage-wrap">
        <div class="head-wrap">
            <div class="flex align-center">
                <svg class="jwifont text-link" aria-hidden="true" @click="goBack">
                    <use xlink:href="#jwi-back"></use>
                </svg>
                <span class="head-title color85 font-700">特性管理</span>
            </div>
        </div>
        <div class="body-wrap">
            <a-tabs v-model.trim="activeTab" @change="onChangeTab">
                <a-tab-pane key="lists" tab="特性清单">
                    <feature-list></feature-list>
                </a-tab-pane>
                <a-tab-pane key="rules" tab="特性规则">
                    <feature-rules></feature-rules>
                </a-tab-pane>
            </a-tabs>
        </div>
    </div>
</template>

<script>
import featureList from './featureList';
import featureRules from './featureRules';
export default {
    name: 'featureManage',
    components: {
        featureList,
        featureRules,
    },
    data() {
        return {
            activeTab: 'lists',
        };
    },
    mounted() {

    },
    methods: {
        goBack() {
            this.$router.push(`/productStructure/${this.$route.params.oid}/${this.$route.params.type}`);
        },
        onChangeTab(key) {

        },
    },
};
</script>

<style lang="less" scoped>
.feature-manage-wrap {
    margin: 5px;
    background: var(--light);
    box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
    border-radius: 4px;
    .jwifont {
        width: 16px;
        min-width: 16px;
        height: 16px;
        min-height: 16px;
    }
    .head-wrap {
        padding: 16px 20px 0;
        .head-title {
            margin-left: 8px;
            font-size: 20px;
        }
    }
    .body-wrap {
        overflow: auto;
        &::-webkit-scrollbar {
            width: 0px;
        }
        /deep/.ant-tabs {
            color: rgba(30, 32, 42, 0.65);
        }
        /deep/.ant-tabs-nav .ant-tabs-tab {
            padding: 19px 0;
        }
        /deep/.ant-tabs-bar {
            padding: 0 20px;
            margin-bottom: 0;
        }
        /deep/.ant-tabs-nav .ant-tabs-tab-active {
            color: rgba(30, 32, 42, 0.85);
            font-weight: 700;
        }
    }
}
</style>
