<template>
    <div >
         <div class="baselist-info">
            <search-info @searchBtn="searchBtn" :selectData="selectData" :fullHeight="fullHeight" @deleteData="deleteData"></search-info>
            <base-table :dataArr="dataArr" ref="basetable" :fullHeight="fullHeight" @changePage="changePage" @changePageSize="changePageSize" @getSelectData="getSelectData"></base-table>
         </div>
    </div>
</template>

<script>
import searchInfo from './search'
import baseTable from './baseTable'
export default {
    name:'baseline',
    components: {
       searchInfo,
       baseTable,
    },
    inject: ["setBreadcrumb", "addBreadcrumb"],
    data(){
        return {
            selectData:[],
            visible:false,
            form:{
              keyword: '',
              page: 1,
              pageSize: 10,
            },
            fullHeight:document.documentElement.clientHeight - 234,
            dataArr: [{
                id:'1',
                name:'基线名称',
                type:'文件夹',
                version:'A.1',
                view:'-',
                lifecycle:'-',
                creatime:'2021-06-20 10:35',
            },{
                id:'2',
                name:'基线名称',
                type:'文件夹',
                version:'A.2',
                view:'-',
                lifecycle:'-',
                creatime:'2021-09-20 10:35',
            },{
                id:'3',
                name:'基线名称',
                type:'文件夹',
                version:'A.1',
                view:'-',
                lifecycle:'-',
                creatime:'2021-06-20 10:35',
            },{
                id:'4',
                name:'基线名称',
                type:'文件夹',
                version:'A.2',
                view:'-',
                lifecycle:'-',
                creatime:'2021-09-20 10:35',
            },{
                id:'5',
                name:'基线名称',
                type:'文件夹',
                version:'A.1',
                view:'-',
                lifecycle:'-',
                creatime:'2021-06-20 10:35',
            },{
                id:'6',
                name:'基线名称',
                type:'文档',
                version:'A.2',
                view:'-',
                lifecycle:'-',
                creatime:'2021-09-20 10:35',
            }]
        }
    },
  methods: {
    deleteData(selectData) {
       
        this.dataArr = this.dataArr.filter(item => {
          return !selectData.find(row => {
            return row == item.id;
          });
        });
         
    },
    getSelectData(selectData){
          this.selectData = selectData
          console.log('this.selectData', this.selectData)
    },
      searchBtn(keyword){
          this.form.keyword = keyword
          this.fetchSearchData()
      },
      changePage(page){
          this.form.page = page
          this.fetchSearchData()
      },
      changePageSize(page,pageSize){
          this.form.page = page
          this.form.pageSize = pageSize
          this.fetchSearchData()
      },
      initBreadcrumb() {
        let breadcrumbData = [{ name: '产品库', path: '/product' }];
        this.setBreadcrumb(breadcrumbData);
        this.addBreadcrumb(
            {
                name: '基线管理',
                children: [
                    { id: '1', title: '内容管理', link: `/contentManage/${this.$route.params.oid}/${this.$route.params.type}` }, 
                    { id: '2', title: '产品结构', link: `/productStructure/${this.$route.params.oid}/${this.$route.params.type}` }, 
                    { id: '3', title: '基线管理', link: `/baseline/${this.$route.params.oid}/${this.$route.params.type}` },
                    { id: '4', title: '变更管理', link: `/changeManage/${this.$route.params.oid}/${this.$route.params.type}` },
                ]
            },
        );
    },
    fetchSearchData(){ //to do 接口联调, 待做
        // this.dataArr =
        //  searchBaselistApi
                // .execute(this.form)
                // .then(data => {
                // })
                // .catch(err => {
        // });
    }
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        this.fullHeight = document.documentElement.clientHeight - 234;
      })();
    }
    console.log('fetchSearchData')
    this.fetchSearchData()
  },

   created() {
    this.initBreadcrumb();
  },
    
}
</script>

<style scoped lang="less">
  .baselist-info {
    background: var(--light);
    box-shadow: 0 2px 8px 0px rgba(30, 32, 42 ,0.25);
    border-radius: 4px;
    padding: 0;
    }
</style>