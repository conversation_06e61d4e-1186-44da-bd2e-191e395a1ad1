<template>
    <div>
        <div class="main-body">
             <div :style="{ height: fullHeight  + 40 + 'px' }" class="maintable">
                <JwTable :options="columns" rowKey="id" :datasource="dataArr"  @selectData="selectData" :isSelect="isSelect" :height="fullHeight + 20" :pagination="pagination">
                   <template slot="name" slot-scope="{ text }">
                      <div class="product-info">
                          <svg class="icon-product" aria-hidden="true">
                              <use xlink:href="#jwi-baseline"></use>
                          </svg>
                          <a href="javascript:;">{{ text }}</a>
                      </div>
                    </template>

                 <template slot="operation" slot-scope="text, record, $index">
                    <div class="operation-btn">
                    <a-popover
                        trigger="click"
                        ref="popover4"
                        placement="bottomLeft"
                    >
                    <span slot="title" class="group-titles">{{$t('txt_rename')}} </span>
                        <template slot="content">
                        <a-form-model
                            ref="ruleForm"
                            :model="form"
                            :rules="rules"
                            :label-col="labelCol"
                            :wrapper-col="wrapperCol"
                            :overlayStyle="{ width: '255px' }"
                        >
                            <a-form-model-item
                            ref="name"
                            :label="$t('txt_name')"
                            prop="name"
                            labelAlign="left"
                            >
                            <a-input
                                v-model.trim="form.name"
                                @focus="focus"
                                :placeholder="$t('placeholder_name')"
                            />
                            </a-form-model-item>
                            <a-form-model-item>
                            <a-button
                                type="primary"
                                @click="onSubmit($index)"
                                :disabled="disabled"
                                class="add-new-btn"
                                >{{$t('btn_ok')}}</a-button>
                            </a-form-model-item>
                        </a-form-model>
                        </template>
                        <a>{{$t('txt_rename')}}</a>
                    </a-popover>
                 

                     <div class="ellipsis">
                        <a-dropdown>
                        <a class="ant-dropdown-link" @click="e => e.preventDefault()">
                            <svg class="ellipsis-icon" aria-hidden="true">
                            <use xlink:href="#jwi-ellipsis"></use>
                            </svg>
                        </a>
                        <a-menu slot="overlay">
                            <a-menu-item>
                            <a href="javascript:;">{{$t('txt_characteristics')}}</a>
                            </a-menu-item>
                            <a-menu-item>
                            <a href="javascript:;">{{$t('txt_product_structure')}}</a>
                            </a-menu-item>
                            <a-menu-item>
                            <a href="javascript:;">{{$t('txt_save_template')}}</a>
                            </a-menu-item>
                            <a-menu-item>
                            <a href="javascript:;">{{$t('txt_structure_details')}}</a>
                            </a-menu-item>
                        </a-menu>
                        </a-dropdown>
                    </div>
                 </div>
                </template>
                </JwTable>
          </div>
       
        </div>
    </div>
</template>


<script>
import JwTable from "jw_components/table"
export default {
    name:'basetable',
    props:['dataArr','fullHeight'],
    components: {
      JwTable
    },
    data(){
       return {
           isSelect: true,
           pagination: true,
           total:13,
           pageSize:10,
           ischecked: false,
           indeterminate:false,
           visible: [],
           selectedRowKeys:[],
           sortedInfo: null,
           filteredInfo: null,           
           disabled: true,
           form: { name: "" },
           labelCol: { span: 24 },
           wrapperCol: { span: 24 },
           rules: {
                name: [
                { required: true, message: this.$t('placeholder_name'), trigger: "blur" },
                ],
            },
       }
    },
   computed: {
    
    columns() {
      const columns = [
        {
          title: this.$t('txt_username'),
          dataIndex: "name",
          key: "name",
          ellipsis: true,
          scopedSlots: { customRender: "name" },
        },
        {
          title: this.$t('txt_type'),
          dataIndex: "type",
          key: "type",
          ellipsis: true,
        },
        {
          title: this.$t('txt_plan_version'),
          dataIndex: "version",
          key: "version",
          ellipsis: true,
        },

         {
          title: this.$t('txt_view'),
          dataIndex: "view",
          key: "view",
          ellipsis: true,
        },
        {
          title: this.$t('txt_lifecycle'),
          dataIndex: "lifecycle",
          key: "lifecycle",
          ellipsis: true,
        },

        {
          title: this.$t('txt_create_date'),
          dataIndex: "creatime",
          key: "creatime",
          ellipsis: true,
        },

        {
          title: this.$t('txt_operation'),
          dataIndex: "operation",
          key: "operation",
          scopedSlots: { customRender: "operation" },
          ellipsis: true,
        },
      ];
      return columns;
    },
   
  },
  methods: {
      selectData(selectedRowKeys,selectedRow) {       
         this.selectedRowKeys = selectedRowKeys
         this.$emit('getSelectData',selectedRowKeys)
      },
     onSubmit(index) {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.form.name = ""        
          this.visible[index] = false
          let param = {
              name: this.form.name
          }
          this.fetchCreateRename(param)
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },

    fetchCreateRename(param){ //to do 接口联调, 待做
             //  createRenameApi
                // .execute(param)
                // .then(data => {
                // })
                // .catch(err => {
                // });
    },
    focus() {
      this.disabled = false;
    }
  },
  mounted(){
       for(let i=0; i< this.dataArr.length; i++){
         this.visible.push(false)
      }
  }
}
</script>

<style scoped>
.select-checkout{
    margin-right: 16px;
}
.maintable{
    padding: 0 20px;
    box-sizing: border-box;
}
.operation-btn {
  display: flex;
  align-items: center;
}
.operation-btn a {
  margin-right: 16px;
}
.ellipsis-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
}
.ellipsis {
  width: 16px;
  height: 16px;
}
.ant-form-item:last-child{
    margin-bottom: 10px;
}
.add-new-btn{
  margin-bottom:0px;
  margin-top: 0px;
  float: right;
}
div>>>.ant-col-24{
  margin-top: -8px;
}

.new-group{
  width: 80px;
  margin-right: 8px;
}
.group-titles{
  font-size: 16px;
  color: #292a2c;
  font-weight: 600;
  height: 46px;
  line-height: 46px;
  display: block;
}
.product-info {
  display: flex;
  align-items: center;
}
.icon-product{
    width: 15px;
    height: 15px;
    margin-right: 10.5px;
}
.page-list {
  display: flex;
  justify-content: flex-end;
}
.page-info {
  margin: 0;
  padding: 19px 16px 3px 42px;
  box-sizing: border-box;
  border-top:1px solid rgba(30, 32, 42, 0.15);
  display: flex;
  justify-content: flex-end;
}
.main-body {
       padding: 16px 0px;
       box-sizing: border-box;
   }
</style>