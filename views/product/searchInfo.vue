<template>
    <div class="search-wrap flex justify-between align-center">
        <div class="flex">
            <a-button type="primary" @click="onOpenAddModal">{{$t('btn_new_create')}}</a-button>
            <a-input-search v-model.trim="searchKey" class="search-input" allow-clear
                :placeholder="$t('txt_search')" @search="onSearch" @blur="onSearch" />
        </div>
        <div class="flex">
            <a-dropdown>
                <a-menu slot="overlay">
                    <a-menu-item>{{$t('txt_txt_custom_group')}}</a-menu-item>
                    <a-menu-item>{{$t('txt_delete')}}</a-menu-item>
                </a-menu>
                <a-button>{{$t('btn_batch_operation')}} <a-icon type="down" /> </a-button>
            </a-dropdown>
            <a-button class="view-btn" @click="switchCard">
                <svg class="icon" aria-hidden="true" style="margin-right:0">
                    <use :xlink:href="isList?'#jwi-appstore':'#jwi-list'"></use>
                </svg>
            </a-button>
        </div>
        <a-modal
            :visible="visible"
            :title="$t('txt_create_product_a')"
            :mask-closable="false"
            :footer="null"
            @cancel="onCancel"
        >
            <a-form-model ref="createForm" :model="form" :rules="rules">
                <a-form-model-item
                    :label="$t('txt_prodcut_name')"
                    prop="name">
                    <a-input
                        v-model.trim="form.name"
                        :max-length="50"
                        allow-clear
                        :placeholder="$t('txt_input')" />
                    </a-form-model-item>
                <a-form-model-item
                    :label="$t('txt_prodcut_type')"
                    prop="name">
                    <a-select v-model.trim="form.name" allow-clear :placeholder="$t('msg_select')">
                        <a-select-option
                            v-for="item in productList"
                            :key="item.name"
                            :value="item.name">
                            {{ item.displayName }}
                        </a-select-option>
                    </a-select>
                </a-form-model-item>
                <a-form-model-item
                    :label="$t('txt_promission_temp')"
                    prop="name">
                    <a-select v-model.trim="form.template" allow-clear :placeholder="$t('msg_select')">
                        <a-select-option
                            v-for="item in productList"
                            :key="item.name"
                            :value="item.name">
                            {{ item.displayName }}
                        </a-select-option>
                    </a-select>
                </a-form-model-item>
                <a-form-model-item
                    :label="$t('txt_projict')"
                    prop="name">
                    <a-select v-model.trim="form.template" allow-clear :placeholder="$t('msg_select')">
                        <a-select-option
                            v-for="item in productList"
                            :key="item.name"
                            :value="item.name">
                            {{ item.displayName }}
                        </a-select-option>
                    </a-select>
                </a-form-model-item>
                <a-form-model-item class="form-item-btns text-right">
                    <a-button type="primary" @click="onCreate">{{$t('btn_ok')}}</a-button>
                    <a-button class="form-btn-cancel" @click="onCancel">{{$t('btn_cancel')}}</a-button>
                </a-form-model-item>
            </a-form-model>
        </a-modal>
    </div>
</template>

<script>
export default {
    name: 'searchInfo',
    data() {
        return {
            searchKey: '',
            visible: false,
            isList: true,
            form: {
                name: undefined,
                template: undefined,
            },
            rules: {
                name: [
                    { required: true, message: this.$t('msg_select'), trigger: 'change' },
                ],
                template: [
                    { required: true, message: this.$t('msg_select'), trigger: 'change' },
                ],
            },
            productList: [],
        };
    },
    methods: {
        switchCard() {
            this.isList = !this.isList;
            this.$emit('switchStatus', this.isList, this.searchKey);
        },
        onOpenAddModal() {
            this.visible = true;
        },
        onCreate() {
            this.$refs.createForm.validate((valid) => {
                if (valid) {
                    this.visible = false;
                } else {
                    return false;
                }
            });
        },
        onCancel() {

        },
        onSearch() {
            this.$emit('onSearchFn', this.searchKey);
        },
    },
};
</script>

<style lang="less" scoped>
.search-wrap {
    height: 52px;
    padding: 0 20px;
    border-bottom: 1px solid rgba(30, 32, 42, 0.15);
    .search-input {
        width: 216px;
        margin-left: 8px;
    }
    .view-btn {
        width: 32px;
        line-height: 35px;
        margin-left: 8px;
        padding: 0;
        .icon {
            width: 16px;
            height: 16px;
        }
    }
    .ant-select {
        width: 240px;
    }
}
.form-btn-cancel {
    margin-left: 8px;
}
</style>
