<template>
    <div class="card-wrap">
        <a-checkbox-group @change="onChange">
            <a-row :gutter="[16, 16]" style="margin: 0">
                <a-col :xs="8" :sm="8" :md="8" :lg="8" :xl="6" :xxl="6"
                    v-for="item of cardData" :key="item.oid">
                    <div class="card-item">
                        <div class="card-head flex justify-between align-center">
                            <div class="head-left">
                                <a-checkbox :value="item.oid" @click.stop></a-checkbox>
                                <span class="creator-name">{{ item.creatorName }}</span>
                            </div>
                            <a-dropdown>
                                <a-icon type="ellipsis" />
                                <a-menu slot="overlay">
                                    <a-menu-item>{{$t('txt_characteristics')}}</a-menu-item>
                                    <a-menu-item @click="goStructurePage(item)">{{$t('txt_product_structure')}}</a-menu-item>
                                    <a-menu-item>{{$t('txt_save_template')}}</a-menu-item>
                                </a-menu>
                            </a-dropdown>
                        </div>
                        <div class="card-body flex align-center">
                            <svg class="icon" aria-hidden="true">
                                <use xlink:href="#jwi-product"></use>
                            </svg>
                            <div class="card-con flex flex-column justify-around">
                                <div class="text-ellipsis color85" :title="item.name">{{ item.name }}</div>
                                <div class="color45 font-12">{{ formatDateFn(item.createdTime) }}</div>
                            </div>
                        </div>
                    </div>
                </a-col>
            </a-row>
        </a-checkbox-group>
    </div>
</template>

<script>
import { formatDate } from 'jw_utils/moment-date';
import { fetchProductList } from 'apis/product/';
export default {
    name: 'cardList',
    data() {
        return {
            searchKey: '',
            cardData: [],
        };
    },
    props: [

    ],
    mounted() {

    },
    methods: {
        getProductList() {
            fetchProductList.execute({
                searchKey: this.searchKey,
                page: 1,
                size: 100,
            }).then((res) => {
                this.cardData = res.rows;
            }).catch((err) => {
                if (err.msg) {
                    this.$error(err.msg);
                }
            });
        },
        formatDateFn(date) {
            return formatDate(date);
        },
        onChange(val) {

        },
        goStructurePage(item) {
            this.$router.push(`/productStructure/${item.oid}/${item.modelType}`);
        },
    },
};
</script>

<style lang="less" scoped>
.card-wrap{
    height: calc(100vh - 64px - 40px - 52px);
    padding: 5px 12px;
    overflow: auto;
    &::-webkit-scrollbar{
        width: 1px;
    }
    .ant-checkbox-group {
        width: 100%;
    }
    .card-item {
        border-radius: 4px;
        box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
        border: 1px solid rgba(30, 32, 42, 0.06);
        cursor: pointer;
        &:hover {
            border: 1px solid #3282fa;
            box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
        }
    }
    .card-head {
        height: 40px;
        padding: 0 16px;
        background: rgba(30, 32, 42, 0.06);
        .head-left {
            .creator-name {
                margin-left: 8px;
            }
        }
    }
    .card-body {
        padding: 16px;
        .icon {
            width: 36px;
            min-width: 36px;
            height: 36px;
            min-height: 36px;
            margin-right: 15px;
        }
        .card-con {
            width: calc(100% - 46px);
        }
    }
}
</style>
