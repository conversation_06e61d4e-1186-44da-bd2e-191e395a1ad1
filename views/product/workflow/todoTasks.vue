<template>
  <div class="baselist-info">
    <div class="search-main">
      <div class="search-content">
        <a-input-search placeholder="输入任务名称搜索" class="seach-input" @search="onSearch" />
      </div>
      <div class="search-info-right">
        <span class="current"><img src="../../../assets/image/filter.png"></span>
      </div>
    </div>
    <div class="maintable">
      <a-table :columns="columns" rowKey="id" :pagination="pagination" :style="{ height: fullHeight - 125 + 'px' }"
        :scroll="{ y: fullHeight - 190 }" :data-source="tableData">
        <template slot="processInstanceName" slot-scope="text">
          <div class="product-info">
            <img src="../../../assets/image/workflow.png" class="workflow-icon">
            <span class="link-name">{{ text }}</span>
          </div>
        </template>
        <template slot="operation" slot-scope="text, record">
          <span class="link-name" @click="onOperateClick(record)">处理</span>
        </template>
      </a-table>
      <div class="card-pagination text-center">
        <a-pagination :default-current="currentPage" :total="total" :show-total="total => `共 ${total} 条`"
          show-size-changer @change="handleCurrentChange" @showSizeChange="handleSizeChange" />
      </div>
    </div>
  </div>
</template>

<script>

import taskModel from '../../../apis/workflow/all-tasks'
export default {
  name: 'todoTasks',
  inject: ["setBreadcrumb", "addBreadcrumb"],
  data() {
    return {
      pagination: false,
      pageSize: 10,
      total: 0,
      currentPage: 1,
      columns: [
        {
          title: '流程名称',
          dataIndex: 'processInstanceName',
          key: 'processInstanceName',
          sorter: true,
          ellipsis: true,
          width: 350,
          scopedSlots: { customRender: 'processInstanceName' },
        },
        {
          title: '任务名称',
          dataIndex: 'name',
          key: 'name',
          sorter: true,
          ellipsis: true,
          scopedSlots: { customRender: 'name' },
        },
        {
          title: '委派状态',
          dataIndex: 'processDefinitionName',
          sorter: true,
          key: 'processDefinitionName',
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          key: 'createTime',
          sorter: true,
          ellipsis: true,
        },

        {
          title: '操作',
          dataIndex: 'operation',
          key: 'operation',
          scopedSlots: { customRender: 'operation' },
          ellipsis: true,
        },

      ],
      tableData: [],
      fullHeight: document.documentElement.clientHeight - 104,
      form: {
        nameLike: '',
        active: false,
        isDueDate: false,
        order: 'desc',
        sort: 'createTime',
        processInstanceNameLike: ''
      },
    }
  },
  methods: {

    handleCurrentChange(page, size) {
      this.currentPage = page
      this.pageSize = size
      this.fetch()
    },

    handleSizeChange(curren, size) {
      this.currentPage = curren
      this.pageSize = size
      this.fetch()
    },
    onOperateClick(row) {
      let DefinitionId = row.processDefinitionId.split(':')[0]
      let ruls = `/task/${row.processInstanceId}/${row.id}/${DefinitionId}`
      console.log('ruls', ruls)
      this.$router.push({
        path: ruls
      })
    },
    //   getsearchInstance(){
    //       searchInstance
    //       .execute({processInstanceId:'55001'})
    //       .then(data => {

    //       }).catch(err => {
    //         if (err.msg) {
    //           this.$error(err.msg);
    //         }
    //       })
    //  },
    pick(data) {
      let empty = {}
      _.each(data, (value, attr) => {
        if (true === value || !_.isEmpty(value)) {
          empty[attr] = value
        }
      })
      return empty
    },

    fetch() {
      let param = _.extend(
        {
          tenantId: 'pdm', //this.tenantId,
          assignee: 'HLL',//this.assignee,
          start: (this.currentPage - 1) * this.pageSize, // tableConf.start,
          size: this.pageSize
        },
        this.pick(this.form)
      )

      taskModel.execute(param).then(data => {
        this.tableData = data.data.map(item => {
          let arr = item.processInstanceName.split(',');
          if (arr[arr.length - 1] === 'null') {
            arr = arr.slice(0, 2);
          }
          item.processInstanceName = arr.join(',');

          return item;
        })
        this.total = data.total
      }).catch(error => {
        this.$error(error)
      })

    },


    initBreadcrumb() {
      let breadcrumbData = [{ name: "产品库", path: "/workflow/list" }];
      this.setBreadcrumb(breadcrumbData);
      this.addBreadcrumb({ name: "任务列表" });
    },

    onSearch(val) {
      console.log('val', val)
      this.form.nameLike = val
      this.fetch()
    },
  },

  mounted() {
    window.onresize = () => {
      return (() => {
        this.fullHeight = document.documentElement.clientHeight - 104;
      })();
    }


    this.fetch()
  },
  created() {
    this.initBreadcrumb();
  }

}
</script>

<style scoped>
.search-info-right span img {
  width: 16px;
  height: 16px;
}

.search-info-right span.current {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0 7px;
}

.search-info-right span {
  height: 32px;
  line-height: 32px;
  display: block;
  box-sizing: border-box;
}

.search-info-right {
  display: flex;
}

.workflow-icon {
  margin-right: 8px;
}

.link-name {
  color: #255ED7;
  cursor: pointer;
}

.page-list {
  display: flex;
  justify-content: flex-end;
}

.page-info {
  margin: 0;
  padding: 19px 16px 3px 42px;
  box-sizing: border-box;
  border-top: 1px solid rgba(30, 32, 42, 0.15);
  display: flex;
  justify-content: flex-end;
  height: 70px;
}

.operation-btn {
  display: flex;
  align-items: center;
}

.operation-btn a {
  margin-right: 16px;
}

.ellipsis-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.ellipsis {
  width: 16px;
  height: 16px;
}

.add-new-btn {
  margin-bottom: 0px;
  margin-top: 0px;
  float: right;
}

.group-titles {
  font-size: 16px;
  color: #292a2c;
  font-weight: 600;
  height: 46px;
  line-height: 46px;
  display: block;
}

.product-info {
  display: flex;
  align-items: center;
}

.icon-product {
  width: 15px;
  height: 15px;
  margin-right: 10.5px;
}

.maintable {
  padding: 0px 20px 15px 20px;
  box-sizing: border-box;
  flex: 1;
  overflow-y: auto;
}

.export-btn {
  display: flex;
}

.seach-input {
  width: 216px;
}

.search-content {
  width: 100%;
  display: flex;
  align-items: center;
}

.search-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding: 10px 20px;
  height: 52px;
}

.baselist-info {
  padding: 0;
  display: flex;
  flex-direction: column;
  margin: 5px;
  background: var(--light);
  box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
  border-radius: 4px;
}</style>