<template>
    <div>

         <div class="workflow-main">
             <div class="tishi-info">
                 <img src="../../../assets/image/info.png" class="info-icon">
                 <span>系统会自动默认选择相关对象的创建人员为默认会签人，您需要确认相关对象变更的会签人员是否正确，您也可以直接修改相关会签人。</span>
             </div>

             <div class="form-content">
                 <a-form-model :model="ruleForm" :rules="rules" size="small" ref="ruleForm" :label-position="labelPosition" class="demo-ruleForm">
                        <a-form-model-item label="ECR信息">
                            <div class="ecrinfoBtn">
                                <span @click="viewDetail(ecrinfo)">{{ecrinfo.name +','+ ecrinfo.number}}</span>
                             </div>
                        </a-form-model-item>

                       <a-form-model-item label="审核结果" props="assignee">
                         <a-radio-group v-model.trim="ruleForm.variables.agree" @change="switchResult" style="width:300px" :default-value="0">
                                  <a-radio :value="0" v-if="tableData.length !=0">通过</a-radio>
                                  <a-radio :value="1">驳回</a-radio>
                                  <a-radio :value="2" v-if="tableData.length ==0">快速变更</a-radio>
                                </a-radio-group>
                       </a-form-model-item>

                       <a-form-model-item label="补充说明" v-if="ruleForm.variables.agree==0" prop="comment">
                             <a-input v-model.trim="ruleForm.comment" class="long-int" rows="3" type="textarea" />
                       </a-form-model-item>

                        <a-form-model-item label="驳回原因" v-if="ruleForm.variables.agree==1" prop="comment">
                             <a-input v-model.trim="ruleForm.comment" class="long-int" rows="3" type="textarea" />
                       </a-form-model-item>

                         <a-form-model-item label="分配会签任务" v-if="flag && tableData.length !=0" prop="sign">
                                    <a-select  v-model.trim="countersignerName" style="width:328px" :disabled="disabled"  @change="editaccount">
                                        <a-select-option v-for="item in taskpersonnel" :key="item.oid"  :value="item.name">{{ item.name}}</a-select-option>
                                   </a-select>
                         </a-form-model-item>
                 </a-form-model>
             </div>

            
             
             <div class="classtable" v-if="flag">
                <a-table :columns="columns" :data-source="tableData" :pagination="false" :row-selection="{ selectedRowKeys:this.selectedRowKeys, onChange: this.onSelectChange}">
                            <template slot="name" slot-scope="text,  record ">
                            <div class="product-info">
                                <svg class="icon-product" aria-hidden="true">
                                    <use xlink:href="#jwi-baseline"></use>
                                </svg>
                               <span class="numbers" @click="jumplink(record)">{{ text }}</span>
                             </div>
                            </template>


                            <template slot="countersignerName" slot-scope="text, record">
                                <div class="select-sign">
                                    <div class="edit-info">
                                        <span v-if="!record.isEdit">{{text}}</span>
                                        <a-select  v-else v-model.trim="record.countersignerName" size="small" class="select-option" @change="getaccount($event, record)"  placeholder="请选择">
                                           <a-select-option v-for="item in taskpersonnel" :key="item.oid"  :value="item.name">{{ item.name }}</a-select-option>
                                        </a-select>
                                    </div>
                                </div>
                            </template>
                </a-table>
              </div>

              <div class="create-btn">
                      <a-button type="primary" class="new-btn-create" @click="submitData">提交</a-button>
                      <a-button class="cancer" @click="goback">{{$t('btn_cancel')}}</a-button>
               </div>
         </div>


         
    </div>
</template>

<script>

import ModelFactory from "jw_apis/model-factory"
const searchAuditList = ModelFactory.create({
  //列表数据
  url: `${Jw.gateway}/${Jw.changeServer}/ecr/countersignature/getAllEffect`,
  method: "get"
});

let searchAudit = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountServer}/v2/user/searchByKeywordAndPaging`,
  method: "get"
});

let createAudit = ModelFactory.create({
  //提交保存
  url: `${Jw.gateway}/${Jw.changeServer}/ecWorkflow/ecr/examine`,
  method: "post"
});

export default {
    name:'task-audit',
    props: ["ecrinfo", "taskId", "workflowId",'fullHeight'],
    data(){
        return {
            ecrinfolist: null,
            columns: [
            {
              title: '名称',
              dataIndex: 'name',
              key: 'name',
              sorter: true,
              scopedSlots: { customRender: 'name' },
            },
            {
              title: '编码',
              dataIndex: 'number',
              sorter: true,
              key: 'number',
            },
            {
              title: '类型',
              dataIndex: 'modelType',
              key: 'modelType',
              sorter: true,
              ellipsis: true,
            },

            {
              title: '视图',
              dataIndex: 'viewName',
              sorter: true,
              key: 'viewName',
            },
            {
              title: '版本',
              dataIndex: 'version',
              key: 'version',
              sorter: true,
              ellipsis: true,
            },
            {
              title: '会签人',
              dataIndex: 'countersignerName',
              key: 'countersignerName',
              sorter: true,
              scopedSlots: { customRender: 'countersignerName' },
              customCell: this.executePersonCustomCell,
              ellipsis: true,
            },
          
             ],
            tableData: [],
            selectedRowKeys: [],
            labelPosition: "top",
            form: {
              searchKey: "",
              pageNum: "1",
              pageSize: "50",
              tenantId: 1,
              tenantOid: '********-e05f-11eb-a9ba-000c29724790',
              // tenantOid: Jw.getUser().tenantOid
            },
             newArr: [],
             countersignerName: null,
             countersignerAccount: null,
             isAllSelect: false,
             flag: true,
             disabled: true,
             ruleForm: {
                sendTask: "",
                taskId: "",
                processInstanceId: "",
                // assignee: Jw.getUser().account,
                assignee: 'HLL',
                comment: "",
                variables: { agree: 0, sendTask: "" },
                ecrOid: "",
                countersignatureInfo: {
                  ecrOid: "",
                  countersignatureList: []
                }
            },
            taskpersonnel:[],
            rules: {
                comment: [{ required: true, message: '不能为空', trigger: "change" }]
            },
            multipleSelection:[]
         }
    },
   
    methods: {
      jumplink(scoped) {    
      var type = scoped.inheritModelType ? scoped.inheritModelType: scoped.modelType
      type = type =='Part' ? 'Part-detail' :'detail'
      console.log('type', type)
      let text = this.$router.resolve({
        path: `/${type}/${scoped.oid}/${scoped.modelType}/?from=Product`
      });

      window.open(text.href, "_blank");
    },

    viewDetail(item) {
      let text = this.$router.resolve({
        path: `/changeManage/detail/${item.modelType}/${item.oid}`,
        query: {
          name: item.name,
          lifecycle: item.lifecycle
        }
      });

      window.open(text.href, "_blank");
    },

       searchAuditList(ecrOid) {
      let param = {
        ecrOid: ecrOid
      };
      searchAuditList.execute(param)
        .then(data => {
          for (let i = 0; i < data.length; i++) {
            data[i].newmodelType = data[i].inheritModelType
              ? data[i].inheritModelType
              : data[i].modelType;
          }
          this.tableData = data;
          if (data.length == 0) {
            this.ruleForm.variables.agree = 1;
            this.flag = false;
          }
        })
        .catch(err => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },

    

      onSelectChange(selectedRowKeys, selectedRows) {
            this.selectedRowKeys = selectedRowKeys
            this.multipleSelection = selectedRows
        },
        getaccount(event, rows) {
              for (let i = 0; i < this.taskpersonnel.length; i++) {
                if (this.taskpersonnel[i].name == event) {
                      this.$set(rows, "countersignerAccount", this.taskpersonnel[i].account);
                  }
               }
            this.$set(rows, "isEdit", false);
        },
        executePersonCustomCell(record){
            return {
              on: {
                 click: (event) => {
                    this.$set(record, 'isEdit', true)
                 }
              }
            }
        },
        submitData() {//提交保存
         this.$refs.ruleForm.validate(valid => {
         if (valid) {
          this.ruleForm.taskId = this.taskId;
          this.ruleForm.processInstanceId = this.workflowId;
          this.ruleForm.ecrOid = this.ecrinfo.oid;
        
          this.ruleForm.countersignatureInfo.ecrOid = this.ecrinfo.oid;
        
          if (this.tableData.length > 0) {
            this.newArr = [];
            var tableData = this.tableData;
            this.ruleForm.countersignatureInfo.countersignatureList = tableData;
          }
          if (this.ruleForm.sendTask) {
            this.ruleForm.variables.sendTask = this.ruleForm.sendTask;
          }
          
          delete this.ruleForm.sendTask;
          createAudit.execute(this.ruleForm)
            .then(data => {
              this.$success("提交成功");
              this.$router.push("/my-task");
            })
            .catch(err => {
              if (err.msg) {
                this.$error(err.msg);
              }
            })
         }
      });
    },
        goback() {
        this.$router.go(-1);
        },
        select(selectedRowKeys) {       
            this.selectedRowKeys = selectedRowKeys
        },

      fetchAuditList() {
        searchAudit
        .execute(this.form)
        .then(data => {
          this.taskpersonnel = data.rows
          console.log("taskpersonnel", this.taskpersonnel);
        })
        .catch(err => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
        editaccount(val) {
            var countersignerAccount = null;
            for (let k = 0; k < this.taskpersonnel.length; k++) {
                if (this.taskpersonnel[k].name == val) {
                countersignerAccount = this.taskpersonnel[k].account;
                }
            }

            if (this.multipleSelection.length > 0) {
                for (let i = 0; i < this.multipleSelection.length; i++) {
                this.$set(this.multipleSelection[i], "countersignerName", val);
                this.$set(this.multipleSelection[i], "countersignerAccount", countersignerAccount);
                }
            } 
        },
          switchResult(val) {
            var selectVal = val.target.value
            if (selectVal == 0) {
                this.flag = true;
            } else if (selectVal == 1) {
                this.flag = false;
            } else {
                this.flag = false;
                this.queryval = true;
            }
          },
    },
    mounted(){
        console.log('this.ecrinfo', this.ecrinfo)
       if (this.ecrinfo) {
          this.searchAuditList(this.ecrinfo.oid);
        }
      this.fetchAuditList(); //获取任务分发人
    }
}
</script>

<style scoped>
.select-option{
  width: 100px;
}
.form-content>>>.ant-form-item {
  margin-bottom: 0;
}
.classtable {
  margin: 0;
  padding: 0 20px 20px 20px;
  
}
.edit-info {
  width: 80%;
  height: 32px;
  line-height: 32px;
  box-sizing: border-box;
}
.select-sign {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.cancer {
  width: 57px;
  height: 32px;
  background: #fff;
  padding: 0;
}
.new-btn-create {
  width: 66px;
  height: 32px;
  background: #255ed7;
  border-radius: 4px;
  text-align: center;
  padding: 0;
  margin-right: 10px;
}
.create-btn {
  border-top: 1px solid rgba(30, 32, 42, 0.15);
  width: 100%;
  display: flex;
  justify-content: left;
  align-items: center;
  background: #fff;
  height: 60px;
  line-height: 60px;
  box-sizing: border-box;
  z-index: 1000;
  padding-left: 24px;
}
.product-info {
  display: flex;
  align-items: center;
}
.icon-product{
    width: 15px;
    height: 15px;
    margin-right: 10.5px;
}
.long-int {
  width: 552px;
}
.ecrinfoBtn span {
  font-size: 14px;
  color: #255ed7;
  cursor: pointer;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  height: 32px;
  line-height: 32px;
}
.ecrinfoBtn {
  width: 552px;
  height: 32px;
  background: rgba(30, 32, 42, 0.04);
  border: 1px solid rgba(30, 32, 42, 0.15);
  border-radius: 4px;
  padding: 0 16px;
}
.form-content {
  padding: 16px 20px;
}
.tishi-info span {
  font-size: 14px;
  color: rgba(30, 32, 42, 0.65);
}
.tishi-info {
  margin: 0 20px;
  height: 42px;
  background: #f0f7ff;
  border: 1px solid #a4c9fc;
  border-radius: 4px;
  display: flex;
  align-items: center;
  padding: 0 16px;
}
.workflow-main {
  background: #fff;
  box-shadow: 0 2px 8px 0 rgb(30 32 42 / 25%);
  border-radius: 4px;
  padding: 20px 0 0 0;
  margin: 8px 0 0 0;
  overflow: auto;
  transform: translate(0, 0);
  display: flex;
  flex-direction: column;
}

</style>