<template>
    <div>
          <div class="workflow-main">
               <div class="tishi">
                   <img src="../../../assets/image/info.png" class="info-icon">
                   <span>系统会自动默认选择相关对象的创建人员为默认会签人，您需要确认相关对象变更的会签人员是否正确，您也可以直接修改相关会签人。</span>
               </div>
               <div class="form-content">
                     <a-form-model :model="ruleForm" :rules="rules" size="small" ref="ruleForm" :label-position="labelPosition" class="demo-ruleForm">
                         <a-form-model-item label="ECR信息">
                            <div class="ecrinfoBtn">
                                <span @click="viewDetail(ecrinfolist)">变更编码+名称</span>
                             </div>
                        </a-form-model-item>

                         <a-form-model-item label="计划完成时间" prop="plantime">
                            <a-date-picker  v-model.trim="deadline" style="width:328px" disabled class="long-int" />
                        </a-form-model-item>

                     </a-form-model>

                      <div class="change-info">
                           <a-row :gutter="20">
                                <a-col :span="6">
                                   <div class="titles">变更对象：</div>
                                     <div class="change-object">
                                           <div class="switch-menu">
                                                 <div class="menu-list">
                                                      <li :class="{current: num==index}" v-for="(item, index) of menuArr" :key="index" @click="switchmenu(index, item)">{{item}}</li>
                                                 </div>
                                                  <ul>
                                                    <li :class="{current: numbers==index}" v-for="(item, index) of newArrData" @click="switchobject(index,item)" :key="index">
                                                        <span :title="item.number + ', ' + item.name + ', '+ item.version">{{item.number}},{{item.name}},{{item.version}}</span>
                                                    <img src="../../../assets/image/jiantou.png" class="jiantou"></li>
                                                    </ul>
                                           </div>
                                     </div>
                                </a-col>
                                <a-col :span="18">
                                     <template v-if="modelType == 'CADDocument'">
                                                <div class="titles">变更方案：</div>
                                                <cadocument :selectItem="CADDocumentData" v-if="CADDocumentData" :status="'edit'"  @change="changecad" :key="CADDocumentData.oid"></cadocument>
                                            </template>
                                                <template v-if="modelType == 'Part'">
                                                <div class="titles">变更方案：</div>
                                                <div class="layout-main">
                                                <div class="switch-menu-list">
                                                    <li v-for="(item, index) of menuListArr" @click="switchPart(index)" :class="{current: xuhao == index}" :key="index">{{ item.name }}</li>
                                                </div>
                                                
                                                <ModuleView class="relation-wrapper" v-show="currentxh ==0 && PartDataList">
                                                        <ServiceComponentBuilder
                                                        ref="versionBuilder"
                                                        slot="middle"
                                                        layoutName="edit"
                                                        @change="handleChange"
                                                        :instanceData="PartDataList"
                                                        modelName="Part"
                                                        ></ServiceComponentBuilder>
                                                </ModuleView>
                                                <bomDetail :bomData="bomData" class="bomdata" :fullHeight="fullHeight" v-show="currentxh == '1'&& bomData"></bomDetail>
                                                </div>
                                            </template>

                                            <template v-if="modelType=='Document'">
                                                <div class="titles">变更方案：：</div>
                                                <div class="layout-main" v-show="DocumentData">
                                                <div class="switch-menu-list">
                                                    <li  class="current">{{DocumentData.name}}</li>
                                                </div>
                                                <ModuleView class="relation-wrapper">
                                                        <ServiceComponentBuilder
                                                        ref="versionBuilder"
                                                        slot="middle"
                                                        @change="handleChangelist"
                                                        layoutName="edit"
                                                        :instanceData="DocumentData"
                                                        modelName="Document"
                                                        ></ServiceComponentBuilder>
                                                </ModuleView>
                                                </div>
                                            </template>
                                </a-col>
                            </a-row>
                      </div>
               </div>

            <div class="create-btn">
                <a-button type="primary" class="new-btn-create" @click="submitData">提交</a-button>
                <a-button class="cancer" @click="goback">{{$t('btn_cancel')}}</a-button>
            </div>

           </div>
    </div>
</template>

<script>
import cadocument from './cadocument'
import bomDetail from './bomDetail'
// import ModuleView from "jw_components/module-view";
// import ServiceComponentBuilder from "components/model-propertie/components/service-component-builder";
export default {
    name: 'task-detail-eca',
    components: {
        cadocument,
        bomDetail
    },
    data(){
        return {
           newArrData:[],
           fullHeight: 520,
           deadline:'',
           xuhao:0,
           menuListArr:[{id: '1', name: 'Part'},{id: '2', name: 'Bom'}],
           currentxh: 0,
           activeName:'first',
           modelType:'',
           bomData:{},
           DocumentData:{},
           CADDocumentData:{},
           PartDataList:{},
           numbers:0,
           objectList:[],
            num:0,
            menuArr:['Part','CAD','Document'],
            activeName:'1',
            labelPosition:'top',
            ruleForm:{
                plantime:''
            },
            rules:{

            },
            ecrinfolist: null,
        }
    },
    methods: {
          handleChange(val) {
            if(!this.PartDataList.isModify) {
                  this.PartDataList.isModify = true
            }
            Object.assign(this.PartDataList,val)
       },
       handleChangelist(val){
           if(!this.DocumentData.isModify) {
                  this.DocumentData.isModify = true
             }
            this.DocumentData.isModify = true
            Object.assign(this.DocumentData,val)
       },
       changecad() {
         if(!this.CADDocumentData.isModify) {
                  this.CADDocumentData.isModify = true
             }
       },
      viewDetail(item) {
        let text = this.$router.resolve({
            path: `/changeManage/detail/${item.modelType}/${item.oid}`,
            query: {
                name: item.name,
                lifecycle: item.lifecycle,
            }
        });
       window.open(text.href, "_blank");
    },

        switchPart(index){
          this.xuhao = index
          this.currentxh = index
        },
        switchobject(index,item){
            this.numbers = index
            this.getObjectData(item)
        },
      
        switchmenu(index, item){
           this.num = index
           this.numbers = 0
           let modelType = ''
          switch(index) {
            case 0:
              modelType = 'Part';
            break;
            case 1:
              modelType = 'CADDocument';
            break;
            case 2:
              modelType = 'Document';
            break;
          }
         this.getBaseData(modelType)
          console.log('this.num',this.num)
        },

        getBaseData(Type) {
            let newArr = []
             for(let k = 0; k < this.objectList.length; k++) {
               let modelType = this.objectList[k].inheritModelType ? this.objectList[k].inheritModelType : this.objectList[k].modelType
               if(Type == modelType) {
                    newArr.push(this.objectList[k])
               }
            }
            this.newArrData = newArr
            this.getShceme(Type,newArr[0])
        },
      
        submitData(){
            let changeScheme = this.conversionData(this.objectList)
            let param = {
              ecrOid: this.ecrinfolist.oid,
              changeScheme: changeScheme,
            }
          
            updateApi.execute(param)
                .then(data=>{
                     this.createWork()
                })
                .catch(err=>{
                    if(err.msg) {
                        this.$error(err.msg)
                    }
             })
        },


         createWork(){
           let param = {
               taskId: this.taskId,
               processInstanceId: this.workflowId
           }
          
             completeTask.execute(param).then(data => {
                 this.$success(this.$t('pdm.baseline.successful'))
                 this.$router.push('/my-task')
              })
               .catch(err => {
                  if (err.msg) {
                     this.$error(err.msg)
                 }
             })
        },
        conversionData(allData) {
          var changeScheme = {}
           for(let i = 0; i < allData.length; i++){
                let objectOid = allData[i].oid
                let newModelType = allData[i].inheritModelType ? allData[i].inheritModelType : allData[i].modelType
                
                if(newModelType=='Part') {
                  var bomData = allData[i]
                  var partData = _.clone(allData[i])
                  delete partData.children
                  delete partData.newProperties
                  delete partData.newRelationData
                  delete partData.relationData
                  // delete partData.partUnit
                
                   if(allData[i].changeScheme&&allData[i].changeScheme.partFieldChange) {
                    //    delete partData.changeScheme.partFieldChange.changeScheme
                           changeScheme[objectOid] = {
                            partFieldChange: partData.changeScheme.partFieldChange,
                            bomChange: bomData.changeScheme.bomChange,
                          }   
                   }else {
                         delete allData[i].changeScheme
                          changeScheme[objectOid] = {
                            partFieldChange: partData,
                            bomChange: bomData,
                          }  
                   }
               
                 }else if(newModelType=='Document') {

                        // console.log(allData[i])
                        //  changeScheme[objectOid] = {
                        //           docChange:allData[i]
                        //  }

                          if(allData[i].changeScheme&&allData[i].changeScheme.docChange) {
                                changeScheme[objectOid] = {
                                    docChange:allData[i].changeScheme.docChange
                               }
                          }else {
                            delete allData[i].changeScheme
                                changeScheme[objectOid] = {
                                    docChange:allData[i]
                               }
                          }
                         
                      } else {
                        
                          if(allData[i].changeScheme&&allData[i].changeScheme.cadChange) {
                                changeScheme[objectOid] = {
                                    cadChange:allData[i].changeScheme.cadChange
                               }
                          }else {
                            delete allData[i].changeScheme
                                changeScheme[objectOid] = {
                                    cadChange:allData[i]
                               }
                          }
                         
                  }
                
               }

              
              return changeScheme
        },
         goback(){
           this.$router.go(-1)
        },

        async getBeforeVersionInfo(item){
            let param = {
                oid: item.oid,
                version: item.changeScheme.originalVersion,
                modelType: item.modelType
            };
            return await findBeforeVersionInfoModel.execute(param)
        },

        async getShceme(Type, item) {
             console.log('Type', Type)
              this.modelType = Type
              if(item) {
                 if(Type=='Part') { //part, Bom
                  let beforeData=await this.getBeforeVersionInfo(item)  
                  this.PartDataList = item.changeScheme.partFieldChange||item;
                  this.PartDataList.beforeData = beforeData
                  this.bomData = item.changeScheme.bomChange||item;
                
                
                }else if(Type=='Document'){    //document
                let beforeData=await this.getBeforeVersionInfo(item)  
                 this.DocumentData = item.changeScheme.docChange||item
                 this.DocumentData.beforeData=beforeData;
                  
                  
                }else if(Type=='CADDocument'){  //cad

                if(!item.changeScheme.cadChange)  {
                          this.CADDocumentData = item
                  }else {
                      this.CADDocumentData = item.changeScheme.cadChange
                      this.CADDocumentData.currentVersion=item.version
                  }
                      
                }else {
                  return ''
                }
              }else {
                console.log('没有数据2')
                if(Type=='Part') {
                    this.PartDataList = ''
                    this.bomData = ''
                }else if(Type=='Document') {
                  this.DocumentData = ''
                }else if(Type=='CADDocument'){
                     this.CADDocumentData = ''
                }else {
                  return ''
                }
              }
        },
       
         getObjectData(item) {
             let modelType = item.inheritModelType ? item.inheritModelType : item.modelType
           
             this.getShceme(modelType, item)
          },

          fecthsearchEcr(ecrOid) {
              let param = {
                 ecaOid: ecrOid
             }
             searchEcrInfo.execute(param)
                .then(data=>{
                     this.ecrinfolist = data
                     console.log('data', data)
                })
                .catch(err=>{
                    if(err.msg) {
                        this.$error(err.msg)
                    }
                })
          },

        fetchsearchPlan(ecrOid){
             let param = {
                 ecaOid: ecrOid
             }
             searchPlan.execute(param)
                .then(data=>{
                   this.deadline =  data.deadline
                    console.log('this.data', data.deadline)
                })
                .catch(err=>{
                    if(err.msg) {
                        this.$error(err.msg)
                    }
                })
        },
        fetchObjectList(ecrOid) {
           let param = {
                 ecaOid: ecrOid
             }
               searchObjectList.execute(param)
                .then(data=>{                    
                       this.objectList = data
                       console.log('this.objectList',this.objectList)
                       this.newArrData = data
                       this.getBaseData('Part')
                      // this.getObjectData(data[0])
                })
                .catch(err=>{
                    if(err.msg) {
                        this.$error(err.msg)
                    }
                })
        }
    }
}
</script>

<style scoped>
.ecrinfoBtn span {
    font-size: 14px;
    color: #255ED7;
    cursor: pointer;
    display: block;
     overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.ecrinfoBtn {
    width: 552px;
    height: 32px;
    background: rgba(30,32,42,0.04);
    border: 1px solid rgba(30,32,42,0.15);
    border-radius: 4px;
    padding: 0 16px;
    line-height: 32px;
}
.bomdata {
  height: 640px;
  padding: 0 20px;
}
.layout-main{
  border: 1px solid rgba(30,32,42,0.15);
  
}
.switch-menu-list li.current {
  border-bottom: 2px solid #255ED7;
}
.switch-menu-list li {
  margin: 0 20px;
  list-style-type: none;
  cursor: pointer;
}
.switch-menu-list {
    height: 54px;
    width: 100%;
    line-height: 54px;
    display: flex;
    border-bottom: 1px solid rgba(30,32,42,0.15);
}
.relation-wrapper {
  height: 700px;
  padding: 0 20px;
}
.titles {
    height: 22px;
    line-height: 22px;
    margin-bottom: 8px;
}
.jiantou {
    width: 16px;
    height: 16px;
}
.menu-list{
    height: 54px;
    line-height: 54px;
    display: flex;
}
.menu-list li {
    margin: 0 20px;
    list-style-type: none;
    float: left;
    font-size: 14px;
    color: rgba(30,32,42,0.85);
    cursor: pointer;
}
.menu-list li.current {
    border-bottom: 2px solid #255ED7;
}
.change-info {
    width: 100%;
    box-sizing: border-box;    
}
.change-object {
   border: 1px solid rgba(30,32,42,0.15);
   height: 755px;
}

.switch-menu {
    display: block;
    height: 54px;
    width: 100%;
    border-bottom: 1px solid rgba(30,32,42,0.15);
}
.switch-menu ul {
    margin: 0;
    height: 700px;
    overflow-y: auto;
}
.switch-menu ul li {
    height: 54px;
    line-height: 54px;
    border-bottom: 1px solid rgba(30,32,42,0.06);
    display: flex;
    align-items: center;
    padding: 0 16px;
    justify-content: space-between;
    cursor: pointer;
}
.switch-menu ul li.current {
    background: rgba(30,32,42,0.06);
}
.switch-menu ul li span {
    flex: 1;
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.effect {
  color: #F2994A;
}
.noeffect {
  color: rgba(30,32,42,0.65);
}
.cancer {
  width: 57px;
  height: 32px;
  background: #fff;
  padding: 0;
}
.new-btn-create {
  width: 66px;
  height: 32px;
  background: #255ed7;
  border-radius: 4px;
  text-align: center;
  padding: 0;
  margin-right: 10px;
}
.workflow-main {
    background: #fff;
    box-shadow: 0 2px 8px 0 rgb(30 32 42 / 25%);
    border-radius: 4px;
    padding: 20px 0px 0 0;
    margin: 8px 0 0 0;
    overflow: auto;
    transform: translate(0,0);
 }
.plm-icon-back{
  width: 16px;
  height: 16px;
  margin-right: 8px;
  
}
 .workflow-title{
    height: 22px;
    line-height: 22px;
    display: flex;
    align-items: center;
    padding: 0px;
}

.workflow-title span {
    font-size: 14px;
    color: #1E202A;
    font-weight: 600;
}
.selectask{
    width: 328px;
}

.long-int {
    width: 552px;
}
.form-content {
    padding: 16px 20px;
}
.tishi span {
    font-size: 14px;
    color: rgba(30,32,42,0.65);
}
.info-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
}
.tishi{
    margin: 0 20px;
    height: 42px;
    background: #F0F7FF;
    border: 1px solid #A4C9FC;
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding: 0 16px;
}
  .create-btn {
    border-top: 1px solid rgba(30,32,42,0.15);
    width: 100%;
    display: flex;
    justify-content: left;
    align-items: center;
   
    background: #fff;
    height: 72px;
    line-height: 72px;
    box-sizing: border-box;
    z-index: 1000;
    
    padding-left: 24px;
  }
</style>