<template>
    <div>
         <div class="workflow-title">
              <div class="tishi"><img src="../../../assets/image/back.png" class="info-icon">
                    <span>任务名称</span>
               </div>
         </div>
       <TaskAudit v-if="xuhao=='0'" :taskId="taskId" :workflowId="workflowId" :ecrinfo="ecrinfo"  :fullHeight="fullHeight"></TaskAudit>
       <TaskSign v-if="xuhao=='1'" :taskId="taskId" :workflowId="workflowId" :ecrinfo="ecrinfo" :fullHeight="fullHeight"></TaskSign>
       <TaskApproval v-if="xuhao=='2'" :fullHeight="fullHeight"></TaskApproval>
       <IssuedChangeTask v-if="xuhao=='3'" :fullHeight="fullHeight"></IssuedChangeTask>
       <TaskDetailEca v-if="xuhao=='4'" :fullHeight="fullHeight"></TaskDetailEca>
       <Rejected v-if="xuhao=='5'" :fullHeight="fullHeight"></Rejected>
       <standardizationAudit v-if="xuhao=='6'" :taskId="taskId" :workflowId="workflowId" :ecrinfo="ecrinfo" :fullHeight="fullHeight"></standardizationAudit>
       <changeEcaConfirm v-if="xuhao=='7'" :taskId="taskId" :workflowId="workflowId" :ecrinfo="ecrinfo" :fullHeight="fullHeight"></changeEcaConfirm>
    </div>
</template>

<script>
import TaskAudit from './task-audit'  //审核
import TaskSign from './task-sign'  //会签
import TaskApproval from './task-approval'  //批准
import IssuedChangeTask from './issued-change-task'   //下发变更任务
import TaskDetailEca from './task-detail-eca'  //变更方案
import standardizationAudit from './standardizationAudit'   //变更审批
import changeEcaConfirm from './changesEcaConfirm'
import Rejected from './rejected'  //驳回

export default {
    name: 'handle-task',
    data(){
        return {
            fullHeight: document.documentElement.clientHeight,
            xuhao: 1,
            taskId:'',
            workflowId:'',
            ecrinfo:{
                modelType: "ECR",
                name: "tsetest",
                number: "ECR000557",
                oid: "96c3d3d2e9f04b8e9e28aa449264f8b9",
                tenantId: null,
                typeCode: "6654",
                typeValue: "设计缺陷",
                updatedTime: 1632448642639,
                updatorId: "315e8222-e05f-11eb-a9ba-000c29724790",
                updatorName: "测试账号HLL",
            },
        }
    },
    components: {
        TaskAudit,
        TaskSign,
        TaskApproval,
        IssuedChangeTask,
        TaskDetailEca,
        Rejected,
        standardizationAudit,
        changeEcaConfirm
    },
    mounted(){
         let paramsObj = this.$route.params
         this.taskId = paramsObj.taskId
         this.workflowId = paramsObj.workflowId
         window.onresize = () => {
            return (() => {
                this.fullHeight = document.documentElement.clientHeight
            })()
         }
     }
}
</script>

<style scoped>
.tishi span {
  font-size: 14px;
  color: #1E202A;
  font-weight: bold;
}
.info-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}
.tishi {
  margin: 0;
  height: 24px;
  display: flex;
  align-items: center;
  padding: 0;
}

</style>