<template>
    <div>
          <div class="workflow-main">
                <div class="tishi">
                    <img src="../../../assets/image/info.png" class="info-icon">
                    <span>系统会自动默认选择相关对象的创建人员为默认会签人，您需要确认相关对象变更的会签人员是否正确，您也可以直接修改相关会签人。</span>
                </div>

                  <div class="form-content">
                        <a-form-model :model="ruleForm" :rules="ruleForm" size="small" ref="ruleForm" :label-position="labelPosition"  class="demo-ruleForm">
                            <a-form-model-item label="ECR信息" prop="name">
                                <div class="ecrinfoBtn">                         
                                    <span @click="viewDetail(ecrinfo)"> 变更名称 +  编码</span>
                                </div>
                            </a-form-model-item>

                            <a-form-model-item label="计划完成时间" prop="deadline">
                                 <a-date-picker v-model.trim="deadline" class="long-int" />
                            </a-form-model-item>
                            
                             <a-form-model-item>
                                 <a-button type="primary"  class="create-task" @click="createTask" style="width:108px">创建变更任务</a-button>
                                <template  v-if="multipleSelection.length== 1&& Modify">
                                        <a-button  class="create-task" @click="batchModify">批量修改</a-button>
                                </template>
                                <template  v-else>
                                        <a-button  disabled class="create-task" @click="batchModify">批量修改</a-button>
                                </template>
                                <a-button size="small" class="create-task" @click="clealAll">置空</a-button>
                             </a-form-model-item>
                        </a-form-model>
                  </div>


                   <a-table :columns="columns" :rowClassName="setRowClassName" class="classtable" :data-source="tableData"  :pagination="false" :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"></a-table>
                    
                    <div class="create-btn">
                        <a-button type="primary" class="new-btn-create" @click="submitData">提交</a-button>
                        <a-button class="cancer" @click="goback">{{$t('btn_cancel')}}</a-button>
                   </div>
             </div>


             <a-modal v-model.trim="dialogVisible" :title="titles" @ok="comfirnCreate" :width="width" class="model">
                 <a-form-model :model="dialogform" :label-position="labelPosition" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
                    <a-form-model-item  label="任务名称" prop="taskname">
                        <a-input v-model.trim="dialogform.taskname"  placeholder="请输入"></a-input>
                    </a-form-model-item>
                   
                    <a-form-model-item label="审核人" prop="approverName">

                      <a-select v-model.trim="dialogform.approverName"  @change="getAuditList">
                          <a-select-option v-for="item in taskpersonnel" :key="item.oid"  :value="item.name">{{ item.name }}</a-select-option>
                     </a-select>
                    
                    </a-form-model-item>

                     <a-form-model-item label="变更执行人" prop="executorName">
                        <a-select v-model.trim="dialogform.executorName" @change="getExecutorList" style="width:100%" placeholder="请选择">
                           <a-select-option v-for="item in taskpersonnel" :key="item.oid"  :value="item.name">{{ item.name }}</a-select-option>
                        </a-select>
                    </a-form-model-item>
               </a-form-model>
                 
            </a-modal>
    </div>
</template>

<script>

import ModelFactory from "jw_apis/model-factory"
const searchAuditList = ModelFactory.create({ //列表数据
  url: `${Jw.gateway}/${Jw.changeServer}/ecr/findECTarget`,
  method: 'get'
})

let searchAudit = ModelFactory.create({  //获取任务分发人
  url: `${Jw.gateway}/${Jw.accountServer}/v2/user/searchByKeywordAndPaging`,
  method: 'get'
})


let createIssued = ModelFactory.create({  //提交保存 
  url: `${Jw.gateway}/${Jw.changeServer}/ecWorkflow/distributeTask`,
  method: 'post'
})


export default {
    name: 'issued-change-task',
    props: ['fullHeight'],
    data(){
        return {
           tableData: [
              {
                key: '1',
                name: '风扇001',
                number: 'AJ0012345',
                modelType: '文档',
                viewName:'Design',
                version:'A.1',
                isEdit:false,
                taskname:'',
                executorName:'',
                approverName:''
              },
              {
                key: '2',
                name: '风扇001',
                number: 'AJ0012345',
                modelType: '文档',
                viewName:'Design',
                version:'A.1',
                isEdit:false,
                taskname:'',
                executorName:'',
                approverName:''
              },
              {
                key: '3',
                name: '风扇001',
                number: 'AJ0012345',
                modelType: '文档',
                viewName:'Design',
                version:'A.1',
                isEdit:false,
                taskname:'',
                executorName:'',
                approverName:''
              },
              {
                key: '4',
                name: '风扇001',
                number: 'AJ0012345',
                modelType: '文档',
                viewName:'Design',
                version:'A.1',
                isEdit: false,
                taskname:'',
                executorName:'',
                approverName:''
              },
              {
                key: '5',
                name: '风扇001',
                number: 'AJ0012345',
                modelType: '文档',
                viewName:'Design',
                version:'A.1',
                isEdit:false,
                taskname:'',
                executorName:'',
                approverName:''
              },
              {
                key: '6',
                name: '风扇0032',
                number: 'AJ0012345',
                modelType: '文档',
                viewName:'Design',
                version:'A.1',
                isEdit:false,
                taskname:'',
                executorName:'',
                approverName:''
              },
              {
                key: '7',
                name: '风扇0033',
                number: 'AJ0012345',
                modelType: '文档',
                viewName:'Design',
                version:'A.1',
                isEdit:false,
                taskname:'',
                executorName:'',
                approverName:''
              }
            ],
           columns:[
              {
              title: '任务名称',
              dataIndex: 'taskname',
              key: 'taskname',
              align:'left',
              sorter: true,
              scopedSlots: { customRender: 'taskname' },
                customRender: (value, row, index)=> {
                if(value) {
                      const obj = {
                      children: value,
                      attrs: {},
                    }
                    this.mergeArr(obj, row, index, 'taskname')
                    return obj
                  }
               }
            },{
              title: '名称',
              dataIndex: 'name',
              key: 'name',
              align:'left',
              sorter: true,
              scopedSlots: { customRender: 'name' },
            },
            {
              title: '编码',
              dataIndex: 'number',
              align:'center',
              sorter: true,
              key: 'number',
            },
            {
              title: '类型',
              dataIndex: 'modelType',
              key: 'modelType',
              align:'center',
              sorter: true,
              ellipsis: true,
            },
            {
              title: '视图',
              dataIndex: 'viewName',
              sorter: true,
              align:'center',
              key: 'viewName',
            },
            {
              title: '版本',
              dataIndex: 'version',
              key: 'version',
              align:'center',
              sorter: true,
              ellipsis: true,
            },
          {
              title: '执行人',
              dataIndex: 'executorName',
              key: 'executorName',
              align:'center',
              sorter: true,
              scopedSlots: { customRender: 'executorName' },
              ellipsis: true,
               customRender: (value, row, index)=> {
                if(value) {
                      const obj = {
                      children: value,
                      attrs: {},
                    }
                    this.mergeArr(obj, row, index, 'executorName')
                    return obj
                  }
               }
            },

            {
              title: '变更人',
              dataIndex: 'approverName',
              key: 'approverName',
              align:'center',
              sorter: true,
              scopedSlots: { customRender: 'approverName' },
              ellipsis: true,
              customRender: (value, row, index)=> {
                if(value) {
                      const obj = {
                      children: value,
                      attrs: {},
                    }
                    this.mergeArr(obj, row, index, 'approverName')
                    return obj
                  }
               }
            }],
           titles: '创建变更任务',
           selectedRowKeys:[],
           spanArr:[],
           labelPosition:'top',
           isEdit: false,
           width:'480px',
           multipleSelection:[],
           deadline:'',
           dialogVisible: false,
           dialogform: {
               taskname:'',
               executorName:'',
               executorAccount:'',
               approverName:'',
               approverAccount:'',
           },
           form:{
                searchKey: '',
                pageNum: '1',
                pageSize:'50',
                tenantId: 1,
                tenantOid: '********-e05f-11eb-a9ba-000c29724790',
                // tenantOid: Jw.getUser().tenantOid,
            },
           taskpersonnel:[],
           ecrinfolist: null,
           labelPosition: 'top',
           position:0,
           flag: true,
           newArr:[],
           newArrList:[],
           selectData:[],
           ruleForm:{
             deadline:'',
             ecrOid:'',
             ecaList:[],
             taskId:'',
             processInstanceId:'',
             variables:{agree: 0},
             assignee: Jw.getUser().account,
           },
           Modify: false,
           isMerge: false,
           tasknameval: null,
           ruleForm: {
              deadline:[{ required: true, message:`计划完成时间, 不能为空!`, trigger: 'change' }]
           },
          rules: {
            taskname: [
              { required: true, message: `任务名称不能为空!`, trigger: 'change' },
            ],
            executorName: [
              { required: true, message: `变更人不能为空!`, trigger: 'change' },
            ],
              approverName: [
              { required: true, message: `审核人不能为空!`, trigger: 'change' },
            ],
          }
        }
    },
    methods: {

      mergeArr(obj, row, index, key){
            let arr = this.tableData.filter((res)=> {
                return res[key] == row[key]
               })
            if(index ==0 || this.tableData[index-1][key] != row[key]){
                obj.attrs.rowSpan = arr.length
            }else {
              obj.attrs.rowSpan = 0
          }
      },
      setRowClassName(record, index){
            if(record.ishadow) {
                return "warning-row"
            }
      },

     comfirnCreate() {
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    let isvalidation = null
                         if(this.isEdit) {  //修改
                            isvalidation = true
                         }else {
                           isvalidation = this.validationName(this.tableData,this.dialogform)
                         }
                         if(isvalidation) {
                                        if(this.isEdit) {
                                             var tableData =  this.handlerDatas(this.tableData, '')
                                             for(let k = 0; k < tableData.length; k++) {
                                                   if(tableData[k].name == this.tasknameval) {
                                                       var childrenVal = tableData[k].changeNodeOids
                                                       for(let x =0; x < childrenVal.length;x++) {
                                                             this.changeTaskName(childrenVal[x],this.dialogform)
                                                       }
                                                   }
                                             }
                                        }else {
                                            for(let k = 0; k < this.multipleSelection.length; k++) {
                                            this.$set(this.multipleSelection[k], 'approverName', this.dialogform.approverName) 
                                            this.$set(this.multipleSelection[k], 'executorName', this.dialogform.executorName)
                                            this.$set(this.multipleSelection[k], 'executorAccount', this.dialogform.executorAccount) 
                                            this.$set(this.multipleSelection[k], 'approverAccount', this.dialogform.approverAccount)
                                            this.$set(this.multipleSelection[k], 'taskname', this.dialogform.taskname)
                                            this.$set(this.multipleSelection[k], 'group', true) 
                                            this.$set(this.multipleSelection[k], 'ishadow', true) 
                                            this.dialogVisible = false
                                            this.selectedRowKeys = []
                                        }
                             }
                          if(!this.isEdit) {
                                   let initData = this.classifiyData(this.tableData, this.dialogform)
                                    this.tableData = initData
                                    this.rowspan(initData)
                                }
                                this.dialogVisible = false
                                this.selectedRowKeys = []
                          }else {
                                this.dialogVisible = false
                                this.selectedRowKeys = []
                          }
                           
                    }
                })
        },
      onSelectChange(selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.multipleSelection = selectedRows
        this.selectData = selectedRows
        if(selectedRows[0].taskname) {
               this.Modify = true
           }else {
               this.Modify = false
           }
       
      },
      batchModify(){
            this.tasknameval = this.selectData[0].taskname
            if(this.selectData[0].taskname) {
                    this.dialogform.approverName = this.selectData[0].approverName
                    this.dialogform.executorName = this.selectData[0].executorName
                    this.dialogform.taskname = this.selectData[0].taskname
                    this.dialogVisible = true
                    this.isEdit = true
            }
        },
       
        clealAll() {
             this.spanArr = [1,1,1,1,1,1,1,1,1]
           for(let k = 0; k < this.tableData.length; k++) {
                 if(this.tableData[k].ishadow) {
                    this.tableData[k].ishadow = false
                    this.tableData[k].approverName = ''
                    this.tableData[k].approverAccount = ''
                    this.tableData[k].executorName = ''
                    this.tableData[k].executorAccount = ''
                    this.tableData[k].taskname = ''
                 }
           }

           this.selectedRowKeys = []
           
             
        },
        getAuditList(val) {   
        
             for(let j = 0; j< this.taskpersonnel.length; j++) {
                   if(this.taskpersonnel[j].name==val) {
                       this.dialogform.approverAccount = this.taskpersonnel[j].account
                   }
             }
        },
        getExecutorList(val) {
           
            for(let j = 0; j< this.taskpersonnel.length; j++) {
                   if(this.taskpersonnel[j].name==val) {
                       this.dialogform.executorAccount = this.taskpersonnel[j].account
                   }
             }
        },
        
       viewDetail(item) {
        let text = this.$router.resolve({
            path: `/changeManage/detail/${item.modelType}/${item.oid}`,
            query: {
                name: item.name,
                lifecycle: item.lifecycle,
            }
        });
       window.open(text.href, "_blank");
       },
       
       rowspan() {
                  if(this.multipleSelection.length > 0) {
                     this.spanArr = []
                      this.tableData.forEach((item, index) => {
                        if (index === 0) {
                            this.spanArr.push(1);
                            this.position = 0;

                        } else {
                            if(this.tableData[index].taskname) {
                                     if (this.tableData[index].taskname === this.tableData[index - 1].taskname) {
                                        this.spanArr[this.position] += 1;
                                        this.spanArr.push(0);
                                    }else {
                                          this.spanArr.push(1);
                                          this.position = index;
                                    }
                            }
                            else {
                                this.spanArr.push(1);
                                this.position = index;
                            }
                        }
                    })

                  }

                   this.$refs.multipleTable.clearSelection();
                    
         },

        tableRowClassName(rowData) {
                    let row = rowData.row
                    if (row.ishadow) {
                      return "warning-row";
                    }else {
                        return ''
                    }
            },
       changeTaskName(NodeOids, dialogform){
           for(let j = 0; j < this.tableData.length; j++) {
                  if(this.tableData[j].oid == NodeOids) {
                        this.$set(this.tableData[j], 'approverName', dialogform.approverName) 
                        this.$set(this.tableData[j], 'executorName', dialogform.executorName)
                        this.$set(this.tableData[j], 'executorAccount', dialogform.executorAccount) 
                        this.$set(this.tableData[j], 'approverAccount', dialogform.approverAccount)
                        this.$set(this.tableData[j], 'taskname', dialogform.taskname)
                  }
           }
       },
       
       
        handlerDatas(arr, detalineval){
                let obj = {};
                arr.forEach((item, index) => {
                    let { taskname } = item;
                    if (!obj[taskname]) {
                        obj[taskname] = {
                            taskname,
                            children: []
                        }
                    }
                    obj[taskname].children.push(item);
                });
            let data = Object.values(obj); // 最终输出
          
          let newArr = []
            for(let j=0; j < data.length; j++) {
                if(data[j].children &&data[j].children.length>0) {
                    var item = data[j].children[0]
                    var newArrOid = []
                    var objlist = {
                            name:data[j].taskname,
                            executorAccount:item.executorAccount,
                            executorName:item.executorName,
                            approverAccount:item.approverAccount,
                            approverName:item.approverName,
                            confirmorName:Jw.getUser().name,
                            confirmorAccount:Jw.getUser().account,
                            deadline: detalineval,
                            changeNodeOids:newArrOid
                        }
                    for(let z = 0; z < data[j].children.length; z++) {
                        newArrOid.push(data[j].children[z].oid)
                    }
                }

                newArr.push(objlist)
            }

            return newArr

           
            
        },
       validationName(tableData, dialogform) {  // 验证任务名称一致情况下, 审核人与变更执行人不一样的情况
             for(let i = 0; i <  tableData.length; i++) {
                    var noeque = tableData[i].executorName == dialogform.executorName && tableData[i].approverName == dialogform.approverName 
                    if(tableData[i].taskname == dialogform.taskname && !noeque){
                          this.$error('任务名称相同时, 审核人与变更执行人必须一样!')
                          // this.$refs.multipleTable.clearSelection()
                          return false
                    }else {
                        return true
                    }
             }
       },
       classifiyData(tableData, dialogform) {
             let selectArr = []
             let original = []
             let mergeArr = []
             for(let k = 0; k < tableData.length; k++) {
                  if(tableData[k].group) {
                        selectArr.push(this.tableData[k])
                        this.$set(tableData[k],'group', false)
                  }else if(tableData[k].taskname == dialogform.taskname && tableData[k].approverName == dialogform.approverName && tableData[k].executorName == dialogform.executorName) {
                         selectArr.push(this.tableData[k])
                  }else  {
                      original.push(this.tableData[k])
                  }
             }
             mergeArr = [...selectArr,...original]
             console.log('mergeArr',mergeArr)
             return mergeArr
       },
        createTask(){
           if(this.multipleSelection.length > 0) {
                 this.dialogVisible = true
                 this.isEdit = false
                 this.$refs.ruleForm.resetFields();
                 this.dialogform.taskname='ECA_'+ this.multipleSelection[0].name
           }else {
               this.$error(`请至少选择一条数据!`)
           }
        },
      
        jumplink(scoped) {    
                var type = scoped.inheritModelType ? scoped.inheritModelType: scoped.modelType
                type = type =='Part' ? 'Part-detail' :'detail'
                console.log('type', type)
                let text = this.$router.resolve({
                    path: `/${type}/${scoped.oid}/${scoped.modelType}/?from=Product`
                });
            window.open(text.href, "_blank");
        },
       numberTo(val) {
           return val <= 9 ? '0'+ val : val
       },
        submitData(){  //提交保存
         this.$refs.ruleForm.validate((valid) => {
          if (valid) {
             if(this.deadline) {
               let monthVal = this.deadline.getMonth() + 1
               monthVal = this.numberTo(monthVal)
               let dataVal = this.deadline.getDate()
               dataVal = this.numberTo(dataVal)
               let detalineval = this.deadline.getFullYear() + '-' + monthVal + '-' + dataVal
               console.log('this.tableData',this.tableData)
               let tableData =  this.handlerDatas(this.tableData, detalineval)
               console.log('tableData', tableData)
               this.ruleForm.ecaList = tableData
               this.ruleForm.ecrOid = this.ecrinfo.oid
               this.ruleForm.taskId = this.taskId
               this.ruleForm.deadline = detalineval
               this.ruleForm.processInstanceId = this.workflowId
               var flag = true
               for(let i = 0; i < this.tableData.length; i++) {
               if(!this.tableData[i].taskname){
                  flag = false
               }
            }
            if(!flag) {
                this.$error(  '每个变更任务都需要指定!')
                return false
            }
               createIssued.execute(this.ruleForm).then(data => {
                      this.$success(this.$t('digitalPlm.change.submittedSuccessfully'))
                      this.$router.push('/my-task')
                  })
                .catch(err => {
                    if (err.msg) {
                        this.$error(err.msg)
                    }
                 })

                }else {
                    this.$error(`${this.$t('digitalPlm.change.plannedTime')} ${this.$t('digitalPlm.common.notEmpty')}!`)
                }

                 
              }
          })
        },

        dataToconversion(tableData) {
             for(let i = 0; i < tableData.length; i++) {
                   this.newArr.push({
                       changeObjectType: tableData[i].modelType,
                       changeObjectOid: tableData[i].oid,
                       countersignerAccount: tableData[i].countersignerAccount ? tableData[i].countersignerAccount : '',
                       countersignerName: tableData[i].countersignerName ? tableData[i].countersignerName : '',
                   })
                   
                   if(tableData[i].children && tableData[i].children.length > 0) {                      
                       for(let j = 0; j < tableData[i].children.length; j++){
                            this.dataToconversion(tableData[i].children)
                       }
                   }
             }
           return this.newArr
        },

        fetchAuditList(){
           searchAudit.execute(this.form).then(data => {
           this.taskpersonnel = data.rows
           console.log('this.taskpersonnel',this.taskpersonnel)
        })
        .catch(err => {
            if (err.msg) {
                this.$error(err.msg)
            }
         })
        },

        switchResult(val){
            console.log('val', val)
            this.flag = val=='0' ? false : true
        },
        searchAuditList(ecrOid){
             let param = {
                 oid: ecrOid
             }
             searchAuditList.execute(param)
                .then(data=>{
                    for(let i  =0; i < data.length; i++) {
                         data[i].newmodelType = data[i].inheritModelType ? data[i].inheritModelType : data[i].modelType
                    }
                    this.tableData = data
                    this.newArrList = data
                    console.log('this.tableData', this.tableData)
                })
                .catch(err=>{
                    if(err.msg) {
                        this.$error(err.msg)
                    }
                })
        },
        
        goback(){
           this.$router.go(-1)
        },

        selectBlur(row){
           this.$set(row, 'isEdit', false)
        },

        selectFocus(row){
           this.$set(row, 'isEdit', true)
        },
        dataAddToEdit(childrendata, row) {
                for(let j = 0; j < childrendata.length; j++) {
                      if(childrendata[j].id != row.id) {
                          this.$set(childrendata[j], 'isEdit', false)
                      }
                       if(childrendata[j].children && childrendata[j].children.length > 0) {  //如果存在子集, 则采用递归方式
                              this.dataAddToEdit(childrendata[j].children, row)
                       }
                }
        },
        dataToEdit(childrendata, row) {
                 if(childrendata && childrendata.length > 0) {
                    for(let j = 0; j < childrendata.length; j++) {
                          if(childrendata[j].id == row.id) {
                              this.$set(childrendata[j], 'flag', true)
                          }else {
                             this.$set(childrendata[j], 'flag', false)
                          }

                          if(childrendata[j].children && childrendata[j].children.length > 0) {  //如果存在子集, 则递归
                              this.dataToEdit(childrendata[j].children, row)
                          }
                    }
              } 
        }
    },
    mounted(){
        this.fetchAuditList()  //获取任务分发人
    }
}
</script>

<style scoped>
@import "./tablestyle.css";
.model>>>.ant-modal-body {
  padding: 10px 20px;
}
.classtable {
    padding: 0 20px 20px 20px;
}
.ecrinfoBtn span {
    font-size: 14px;
    color: #255ED7;
    cursor: pointer;
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    height: 32px;
    line-height: 32px;
}
.ecrinfoBtn {
    width: 552px;
    height: 32px;
    background: rgba(30,32,42,0.04);
    border: 1px solid rgba(30,32,42,0.15);
    border-radius: 4px;
    padding: 0 16px;
}
.bom-add-row{
    background: #ddd;
}
.confrim-btn {
    height: 32px;
    background: #255ED7;
    border-radius: 4px;
    color: #fff;
    font-size: 14px;
    margin: 0;
    padding: 0 5px;
}
.cancer {
    width: 52px;
    height: 32px;
    padding: 0;
}
.dialog>>>.el-form-item {
    margin-bottom: 0px;
}
.dialog>>>.el-dialog__body {
   padding: 10px 20px 0 20px;
}
.dialog>>>.el-dialog__footer {
  padding: 10px 20px 22px 20px;
  display: flex;
  justify-content: flex-end;
}
.create-task {
    width: 108px;
    height: 32px;
    padding: 0;
    margin-right: 10px;
}
.numbers {
  color: #409eff;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.parts-icon {
  width: 16px;
  height: 16px;
  margin-right:5px;
}
.edit-info{
    width:80%;
    height: 32px;
    line-height: 32px;
    box-sizing: border-box;
}
.select-sign {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.edit-icon {
    width: 16px;
    height: 16px;
}
.effect {
  color: #F2994A;
}
.noeffect {
  color: rgba(30,32,42,0.65);
}
.cancer {
  width: 57px;
  height: 32px;
  background: #fff;
  padding: 0;
}
.new-btn-create {
  width: 66px;
  height: 32px;
  background: #255ed7;
  border-radius: 4px;
  text-align: center;
  padding: 0;
  margin-right: 10px;
}
  .create-btn {
    border-top: 1px solid rgba(30,32,42,0.15);
    width: 100%;
    display: flex;
    justify-content: left;
    align-items: center;
   
    background: #fff;
    height: 72px;
    line-height: 72px;
    box-sizing: border-box;
    z-index: 1000;
    
    padding-left: 24px;
  }
.mergecolumn {
    width: 552px;
    display: flex;
    justify-content: space-between;
}
.demo-ruleForm>>>.el-form-item__label {
    height: 32px;
    line-height: 32px;
    padding: 0;
}
.selectask{
    width: 328px;
}

.long-int {
    width: 328px;
}
.form-content {
    padding: 16px 20px;
}
.tishi span {
    font-size: 14px;
    color: rgba(30,32,42,0.65);
}
.info-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
}
.tishi{
    margin: 0 20px;
    height: 42px;
    background: #F0F7FF;
    border: 1px solid #A4C9FC;
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding: 0 16px;
}
.workflow-main {
    background: #fff;
    box-shadow: 0 2px 8px 0 rgb(30 32 42 / 25%);
    border-radius: 4px;
    padding: 20px 0px 0 0;
    margin: 8px 0 0 0;
    overflow: auto;
    transform: translate(0,0);
 }
.plm-icon-back{
  width: 16px;
  height: 16px;
  margin-right: 8px;
  
}
 .workflow-title{
    height: 22px;
    line-height: 22px;
    display: flex;
    align-items: center;
    padding: 0px;
}

.workflow-title span {
    font-size: 14px;
    color: #1E202A;
    font-weight: 600;
}
</style>