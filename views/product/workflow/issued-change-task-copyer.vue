<template>
   <div class="workflow-main">
      <a-table :columns="columns" :data-source="data" bordered>
        <template slot="name" slot-scope="text">
          <a>{{ text }}</a>
        </template>
      </a-table>
   </div>
</template>
<script>

const renderContent = (value, row, index) => {
  const obj = {
    children: value,
    attrs: {},
  };
  
  if (index === 4) {
    obj.attrs.colSpan = 0;
  }
  return obj;
};

const data = [
  {
    key: '1',
    name: '<PERSON>',
    age: 32,
    tel: '0571-22098909',
    phone: 18889898989,
    address: 'New York No. 1 Lake Park',
  },
  {
    key: '2',
    name: '<PERSON>',
    tel: '0571-22098333',
    phone: 18889898888,
    age: 42,
    address: 'London No. 1 Lake Park',
  },
  {
    key: '3',
    name: '<PERSON>',
    age: 32,
    tel: '0575-22098909',
    phone: 18900010002,
    address: 'Sidney No. 1 Lake Park',
  },
  {
    key: '4',
    name: '<PERSON>',
    age: 18,
    tel: '0575-22098909',
    phone: 18900010002,
    address: 'London No. 2 Lake Park',
  },
  {
    key: '5',
    name: '<PERSON>',
    age: 18,
    tel: '0575-22098909',
    phone: 18900010002,
    address: 'Dublin No. 2 Lake Park',
  },
];

export default {
  data() {
    const columns = [
      {
        title: 'Name',
        dataIndex: 'name',
        customRender: (text, row, index) => {
          if (index < 4) {
            return <a href="javascript:;">{text}</a>;
          }
          if(index == 4) {
                 return {
                  children: <a href="javascript:;">{text}</a>,
                  attrs: {
                    colSpan: 5,
                  },
                };
          }
        },
      },
      {
        title: 'Age',
        dataIndex: 'age',
        customRender: renderContent,
      },
      {
        title: 'Home phone',
        colSpan: 2,
        dataIndex: 'tel',
        customRender: (value, row, index) => {
          const obj = {
            children: value,
            attrs: {},
          };
          if (index === 2) {
            obj.attrs.rowSpan = 2;
          }
          // These two are merged into above cell
          if (index === 3) {
            obj.attrs.rowSpan = 0;
          }
          if (index === 4) {
            obj.attrs.colSpan = 0;
          }
          return obj;
        },
      },
      {
        title: 'Phone',
        colSpan: 0,
        dataIndex: 'phone',
        customRender: renderContent,
      },
      {
        title: 'Address',
        dataIndex: 'address',
        customRender: renderContent,
      },
    ];
    return {
      data,
      columns,
    };
  },
};
</script>


<style scoped>
.workflow-main {
    background: #fff;
    box-shadow: 0 2px 8px 0 rgb(30 32 42 / 25%);
    border-radius: 4px;
    padding: 20px;
    margin: 8px 0 0 0;
    overflow: auto;
    transform: translate(0,0);
 }
</style>