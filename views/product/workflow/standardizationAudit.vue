<template>
    <div>
         <div class="workflow-main">
             <div class="tishi"><img src="../../../assets/image/info.png" class="info-icon"><span>系统会自动默认选择相关对象的创建人员为默认会签人，您需要确认相关对象变更的会签人员是否正确，您也可以直接修改相关会签人。</span></div>
            <div class="form-content">
                <a-form-model :model="ruleForm" :rules="rules"  ref="ruleForm" :label-position="labelPosition" class="demo-ruleForm">
                        <a-form-model-item label="ECR信息" prop="name">
                            <div class="ecrinfoBtn">                         
                             <span @click="viewDetail(ecrinfolist)">{{ecrinfolist.name +','+ ecrinfolist.number}}</span>
                        </div>
                        </a-form-model-item>

                        <div class="mergecolumn">
                                <a-form-model-item label="审核结果" prop="assignee">
                                        <a-radio-group v-model.trim="ruleForm.agree" @change="switchResult">
                                                <a-radio value="0">批准</a-radio>
                                                <a-radio value="1">驳回</a-radio>
                                        </a-radio-group>
                                </a-form-model-item>

                               
                        <a-form-model-item label="计划完成时间" prop="plantime" v-if="flag">
                            <a-date-picker placeholder="选择日期" @change="onChange" disabled v-model.trim="deadline" style="width:328px" class="long-int" />
                        </a-form-model-item>
                      </div>

                         <a-form-model-item label="驳回原因" prop="comment" v-if="!flag">
                             <a-input type="textarea" class="long-int" rows="3" v-model.trim="ruleForm.comment"></a-input>
                       </a-form-model-item>

                </a-form-model>

                  <div class="change-info" v-if="flag">
                     <a-row :gutter="20">
                        <a-col :span="6">
                            <div class="titles">变更对象：</div>
                            <div class="change-object">
                                <div class="switch-menu">
                                    <div class="menu-list">
                                        <li :class="{current: num==index}" v-for="(item, index) of menuArr" :key="index" @click="switchmenu(index, item)">{{item}}</li>
                                    </div>
                                     <ul>
                                       <li :class="{current: numbers==index}" :title="item.number + ',' + item.name + ', '+ item.version" v-for="(item, index) of newArrData" @click="switchobject(index,item)" :key="index">
                                        <span class="nametext">{{item.number}},{{item.name}},{{item.version}}</span>
                                       <img src="../../../assets/image/jiantou.png" class="jiantou"></li>
                                     </ul>
                                </div>
                            </div>
                        </a-col>
                        <a-col :span="18">
                           <template v-if="modelType == 'CADDocument'">
                              <div class="titles">变更方案：</div>
                            <cadocument :selectItem="CADDocumentData" v-if="CADDocumentData" :key="CADDocumentData.oid" :status="'show'"></cadocument>
                           </template>
                            <template v-if="modelType == 'Part'">
                              <div class="titles">变更方案：</div>
                              <div class="layout-main">
                               <div class="switch-menu-list">
                                 <li v-for="(item, index) of menuListArr" @click="switchPart(index)" :class="{current: xuhao == index}" :key="index">{{ item.name }}</li>
                               </div>
                               <!-- <ModuleView class="relation-wrapper" v-show="currentxh ==0 && PartDataList">
                                    <ServiceComponentBuilder
                                    ref="versionBuilder"
                                    slot="middle"
                                    layoutName="show"
                                    :instanceData="PartDataList"
                                    modelName="Part"
                                    >
                                    <div slot="view" v-if="PartDataList.viewName"><div>{{ PartDataList.viewName  }}</div></div>
                                    </ServiceComponentBuilder>
                              </ModuleView> -->
                              <bomDetail :bomData="bomData" class="bomdata" :fullHeight="fullHeight" :status="'show'" v-show="currentxh == '1' && bomData"></bomDetail>
                             </div>
                          </template>

                          <template v-if="modelType=='Document'">
                            <div class="titles">变更方案：</div>
                              <div class="layout-main" v-show="DocumentData">
                               <div class="switch-menu-list">
                                 <li  class="current">{{DocumentData.name}}</li>
                               </div>
                               <!-- <ModuleView class="relation-wrapper">
                                    <ServiceComponentBuilder
                                    v-if="DocumentData"
                                    ref="versionBuilder"
                                    slot="middle"
                                    layoutName="show"
                                    :instanceData="DocumentData"
                                    modelName="Document"
                                    ></ServiceComponentBuilder>
                              </ModuleView> -->
                             </div>
                          </template>
                          
                        </a-col>
                    </a-row>
                  </div>
             </div>

            <div class="create-btn">
                <a-button type="primary" class="new-btn-create" @click="submitData">提交</a-button>
                <a-button class="cancer" @click="goback">{{$t('btn_cancel')}}</a-button>
            </div>

        </div>
    </div>
</template>

<script>

import ModelFactory from "jw_apis/model-factory"

const searchEcrInfo = ModelFactory.create({ //获取ECR信息
  url: `${Jw.gateway}/${Jw.changeServer}/eca/findECRByEcaOid`,
  method: 'get'
})
const searchPlan = ModelFactory.create({ //获取计划完成时间
  url: `${Jw.gateway}/${Jw.changeServer}/eca/findByOid`,
  method: 'get'
})

const searchObjectList = ModelFactory.create({ //获取变更对象
  url: `${Jw.gateway}/${Jw.changeServer}/eca/getChangeEntityAndShceme`,  
  method: 'get'
})

const updateApi = ModelFactory.create({ //更新接口
  url: `${Jw.gateway}/${Jw.changeServer}/ecr/updateChangeScheme`,
  method: 'post'
})

let completeTask = ModelFactory.create({  //完成任务  驳回
  url: `${Jw.gateway}/${Jw.workflowServer}/workflow/task/finishTask`,
  method: 'post'
})

let throughTask = ModelFactory.create({  //完成任务  通过
  url: `${Jw.gateway}/${Jw.changeServer}/ecWorkflow/eca/examine`,
  method: 'post'
})


const findBeforeVersionInfoModel = ModelFactory.create({
  //变更节点获取上一版本对比信息
  url: `${Jw.gateway}/${Jw.baseServer}/dynamic/instance/searchByVersion`,
  method: "get"
});



import cadocument from './cadocument'
import bomDetail from './bomDetail'
// import ModuleView from "jw_components/module-view";
// import ServiceComponentBuilder from "components/model-propertie/components/service-component-builder";
export default {
    name:'task-detail-eca',
    props:['ecrinfo','ecrinfo1','taskId','workflowId','statusinfo'],
    components: {
        // ServiceComponentBuilder,
        // ModuleView,
        cadocument,
        bomDetail,
    },
    data(){
        return {
           newArrData:[],
           fullHeight: 520,
           flag:true,
           deadline:'',
           xuhao:0,
           menuListArr:[{id: '1', name: 'Part'},{id: '2', name: 'Bom'}],
           currentxh: 0,
           activeName:'first',
           modelType:'',
           bomData:{},
           DocumentData:{},
           CADDocumentData:{},
           PartDataList:{},
           numbers:0,
           objectList:[],
            num:0,
            menuArr:['Part','CAD','文档'],
            activeName:'1',
            labelPosition:'top',
            ruleForm:{
                plantime:'',
                agree: 0,
                comment:''
            },
            rules:{
               comment: [{ required: true, 
               message: `驳回原因不能为空`, 
               trigger: 'change' }]
            },
            ecrinfolist: null,
            ecainfo:{}
        }
    },
    
    methods: {
         switchResult(val){
            val = val.target.value
            this.flag = val=='1' ? false : true
        },
     
        viewDetail(item) {
        let text = this.$router.resolve({
            path: `/changeManage/detail/${item.modelType}/${item.oid}`,
            query: {
                name: item.name,
                lifecycle: item.lifecycle,
            }
        });
       window.open(text.href, "_blank");
       },

        switchPart(index){
          console.log('index', index)
          this.xuhao = index
          this.currentxh = index
        },
        switchobject(index,item){

        
            this.numbers = index
            this.getObjectData(item)
        },
      
        switchmenu(index, item){
           this.num = index
           this.numbers = 0
           let modelType = ''
          switch(index) {
            case 0:
              modelType = 'Part';
            break;
            case 1:
              modelType = 'CADDocument';
            break;
            case 2:
              modelType = 'Document';
            break;
          }
          this.getBaseData(modelType)
         
        },

      
        
          getBaseData(Type) {
            let newArr = []
             for(let k = 0; k < this.objectList.length; k++) {
               let modelType = this.objectList[k].inheritModelType ? this.objectList[k].inheritModelType : this.objectList[k].modelType
                
               if(Type == modelType) {
                    newArr.push(this.objectList[k])
               }
            }
             if(newArr.length>0) {
                    this.newArrData = newArr
                     this.getShceme(Type,newArr[0])
             } else {
                
                 this.newArrData = ''
                 this.getShceme(Type, this.newArrData)
             }
          
        },
      
        submitData(){
            this.$refs.ruleForm.validate( valid => {
                if (valid) {
                let param = {
                    taskId: this.taskId,
                    processInstanceId: this.workflowId,
                    variables: {agree: this.ruleForm.agree},
                    comment:this.ruleForm.comment,
                    ecaOid: this.ecainfo.oid
                }

           if(this.ruleForm.agree==0) {  //通过
               throughTask.execute(param).then(data => {
                 this.$success('成功提交!')
                 this.$router.push('/my-task')
              })
               .catch(err => {
                  if (err.msg) {
                     this.$error(err.msg)
                 }
             })

           }else {  //驳回

              completeTask.execute(param).then(data => {
                 this.$success('成功驳回!')
                 this.$router.push('/my-task')
              })
               .catch(err => {
                  if (err.msg) {
                     this.$error(err.msg)
                 }
             })

           }
            
          }
        })
           
        },

        conversionData(allData) {
          var changeScheme = {}
           for(let i = 0; i < allData.length; i++){
                   let objectOid = allData[i].oid
                   let newModelType = allData[i].inheritModelType ? allData[i].inheritModelType : allData[i].modelType
                  if(newModelType=='Part') {
                        changeScheme[objectOid] = {
                          partFieldChange: allData[i],
                          bomChange: allData[i],
                        }
                        }else if(newModelType=='Document') {
                            changeScheme[objectOid] = {
                                docChange:allData[i]
                            }
                        } else {
                            changeScheme[objectOid] = {
                                cadChange:allData[i]
                            }
                    }
               }
              return changeScheme
        },
         goback(){
           this.$router.go(-1)
        },

        async getBeforeVersionInfo(item){
            let param = {
                oid: item.oid,
                version: item.changeScheme.originalVersion,
                modelType: item.modelType
            };
            return await findBeforeVersionInfoModel.execute(param)
        },
       

         async getShceme(modelType, item) {
              this.modelType = modelType
              if(item) {
                 if(modelType=='Part') { //part, Bom
                 // 变更版本-》历史版本
                    let beforeData=await this.getBeforeVersionInfo(item)
                    console.log(beforeData,'历史版本数据')
                    this.PartDataList = item.changeScheme.partFieldChange||item;
                    this.PartDataList.beforeData = beforeData
                    this.bomData = item.changeScheme.bomChange||item;
                
                }else if(modelType=='Document'){    //document
                  let beforeData=await this.getBeforeVersionInfo(item)  
                  this.DocumentData = item.changeScheme.docChange||item;
                  this.DocumentData.beforeData=beforeData
                  
                  
                }else if(modelType=='CADDocument'){  //cad

                if(!item.changeScheme.cadChange)  {
                          this.CADDocumentData = item
                  }else {
                    this.CADDocumentData = item.changeScheme.cadChange;
                    this.CADDocumentData.currentVersion=item.version
                  }
                }else {
                  return ''
                }
              }else {
               
                if(modelType=='Part') {
                    this.PartDataList = ''
                    this.bomData = ''
                }else if(modelType=='Document') {
                  this.DocumentData = ''
                }else if(modelType=='CADDocument'){
                     this.CADDocumentData = ''
                }else {
                  return ''
                }
              }
        },
       
         getObjectData(item) {
             let modelType = item.inheritModelType ? item.inheritModelType : item.modelType
             
             this.getShceme(modelType, item)
          },

          fecthsearchEcr(ecrOid) {
              let param = {
                 ecaOid: ecrOid
             }
             searchEcrInfo.execute(param)
                .then(data=>{
                     this.ecrinfolist = data
                     console.log('data', data)
                })
                .catch(err=>{
                    if(err.msg) {
                        this.$error(err.msg)
                    }
                })
          },

        fetchsearchPlan(ecrOid){
             let param = {
                 ecaOid: ecrOid
             }
             searchPlan.execute(param)
                .then(data=>{
                   this.ecainfo = data
                   this.deadline =  data.deadline
                    console.log('this.ecainfo', this.ecainfo)
                })
                .catch(err=>{
                    if(err.msg) {
                        this.$error(err.msg)
                    }
                })
        },
        fetchObjectList(ecrOid) {
           let param = {
                 ecaOid: ecrOid
             }
               searchObjectList.execute(param)
                .then(data=>{                    
                       this.objectList = data
                       this.newArrData = data
                       this.getBaseData('Part')
                      // this.getObjectData(data[0])
                })
                .catch(err=>{
                    if(err.msg) {
                        this.$error(err.msg)
                    }
                })
        }
    },
    mounted() {
        if(this.ecrinfo) {
             this.ecrinfolist = this.ecrinfo.number + ',' + this.ecrinfo.name
             this.fetchsearchPlan(this.ecrinfo.oid)
             this.fecthsearchEcr(this.ecrinfo.oid)
             this.fetchObjectList(this.ecrinfo.oid)
        }
    }
}
</script>


<style scoped>
.switch-menu ul li span.nametext {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: block;
}
.ecrinfoBtn span {
    font-size: 14px;
    color: #255ED7;
    cursor: pointer;
    display: block;
     overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.ecrinfoBtn {
    width: 552px;
    height: 32px;
    background: rgba(30,32,42,0.04);
    border: 1px solid rgba(30,32,42,0.15);
    border-radius: 4px;
    padding: 0 16px;
}
.mergecolumn {
    width: 552px;
    display: flex;
    justify-content: space-between;
}
.bomdata {
  height: 690px;
  padding: 0 20px;
}
.layout-main{
  border: 1px solid rgba(30,32,42,0.15);
  
}
.switch-menu-list li.current {
  border-bottom: 2px solid #255ED7;
}
.switch-menu-list li {
  margin: 0 20px;
  list-style-type: none;
  cursor: pointer;
}
.switch-menu-list {
    height: 54px;
    width: 100%;
    line-height: 54px;
    display: flex;
    border-bottom: 1px solid rgba(30,32,42,0.15);
}
.relation-wrapper {
  height: 700px;
  padding: 0 20px;
}
.titles {
    height: 22px;
    line-height: 22px;
    margin-bottom: 8px;
}
.jiantou {
    width: 16px;
    height: 16px;
}
.menu-list{
    height: 54px;
    line-height: 54px;
    display: flex;
}
.menu-list li {
    margin: 0 20px;
    list-style-type: none;
    float: left;
    font-size: 14px;
    color: rgba(30,32,42,0.85);
    cursor: pointer;
}
.menu-list li.current {
    border-bottom: 2px solid #255ED7;
}
.change-info {
    width: 100%;
    box-sizing: border-box;    
}
.change-object {
   border: 1px solid rgba(30,32,42,0.15);
   height: 756px;
}

.switch-menu {
    display: block;
    height: 54px;
    width: 100%;
    border-bottom: 1px solid rgba(30,32,42,0.15);
}
.switch-menu ul {
    margin: 0;
    height: 540px;
    overflow-y: auto;
}
.switch-menu ul li {
    height: 54px;
    line-height: 54px;
    border-bottom: 1px solid rgba(30,32,42,0.06);
    display: flex;
    align-items: center;
    padding: 0 16px;
    justify-content: space-between;
    cursor: pointer;
}
.switch-menu ul li.current {
    background: rgba(30,32,42,0.06);
}
.effect {
  color: #F2994A;
}
.noeffect {
  color: rgba(30,32,42,0.65);
}
.cancer {
  width: 57px;
  height: 32px;
  background: #fff;
  padding: 0;
}
.new-btn-create {
  width: 66px;
  height: 32px;
  background: #255ed7;
  border-radius: 4px;
  text-align: center;
  padding: 0;
  margin-right: 8px;
}
.workflow-main {
    background: #fff;
    box-shadow: 0 2px 8px 0 rgb(30 32 42 / 25%);
    border-radius: 4px;
    padding: 20px 0px 0 0;
    margin: 8px 0 0 0;
    overflow: auto;
    transform: translate(0,0);
 }
.plm-icon-back{
  width: 16px;
  height: 16px;
  margin-right: 8px;
  
}
 .workflow-title{
    height: 22px;
    line-height: 22px;
    display: flex;
    align-items: center;
    padding: 0px;
}

.workflow-title span {
    font-size: 14px;
    color: #1E202A;
    font-weight: 600;
}
.selectask{
    width: 328px;
}

.long-int {
    width: 552px;
}
.form-content {
    padding: 16px 20px;
}
.tishi span {
    font-size: 14px;
    color: rgba(30,32,42,0.65);
}
.info-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
}
.tishi{
    margin: 0 20px;
    height: 42px;
    background: #F0F7FF;
    border: 1px solid #A4C9FC;
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding: 0 16px;
}
  .create-btn {
    border-top: 1px solid rgba(30,32,42,0.15);
    width: 100%;
    display: flex;
    justify-content: left;
    align-items: center;
   
    background: #fff;
    height: 72px;
    line-height: 72px;
    box-sizing: border-box;
    z-index: 1000;
    
    padding-left: 24px;
  }
</style>