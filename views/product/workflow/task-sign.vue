<template>
    <div>
           <div class="workflow-main">
                <div class="tishi-info">
                    <img src="../../../assets/image/info.png" class="info-icon">
                    <span>系统会自动默认选择相关对象的创建人员为默认会签人，您需要确认相关对象变更的会签人员是否正确，您也可以直接修改相关会签人。</span>
                </div>

                <div class="form-content">
                      <a-form-model :model="ruleForm" :rules="rules" size="small" ref="ruleForm" :label-position="labelPosition" class="demo-ruleForm">
                        <a-form-model-item label="ECR信息">
                            <div class="ecrinfoBtn">
                                 <span @click="viewDetail(ecrinfo)">{{ecrinfo.name +','+ ecrinfo.number}}</span>
                             </div>
                        </a-form-model-item>

                         <a-form-model-item>
                            <div class="search-info-list">
                                <a-button class="add-object"  @click="addChangeObject">添加变更对象</a-button>
                                  <a-dropdown>
                                    <a-menu slot="overlay" @click="handleMenuClick">
                                        <a-menu-item key="0">需要变更 </a-menu-item>
                                        <a-menu-item key="1">无需变更 </a-menu-item>
                                    </a-menu>
                                    <a-button style="margin-left: 8px"> 批量会签 <a-icon type="down" /> </a-button>
                                </a-dropdown>
                                <a-input placeholder="请输入关键词搜索" class="search-btn" @change="searchInfoBtn" suffix-icon="el-icon-search" v-model.trim="keyword"></a-input>
                                    
                            </div>
                        </a-form-model-item>

                      </a-form-model>
                </div>


                <a-table :columns="columns"  class="classtable" :data-source="newTableArr" :pagination="false" :row-selection="{ selectedRowKeys:this.selectedRowKeys, onChange: this.onSelectChange}">
                            <template slot="name" slot-scope="scope, record">
                            <div class="product-info">
                                <svg class="icon-product" aria-hidden="true">
                                    <use xlink:href="#jwi-baseline"></use>
                                </svg>
                               <span class="numbers" @click="jumplink(record)">{{ scope }}</span>
                             </div>
                            </template>


                            <template slot="countersignerName" slot-scope="text, record">
                                <div class="select-sign">
                                    <div class="edit-info">
                                     <span v-if="!record.isEdit">{{  record.evaluation | filterevaluation }}</span>
                                     <a-select size="small" v-else v-model.trim="record.evaluation" class="select-option" @change="getaccount($event, record)"  placeholder="请选择">
                                           <a-select-option v-for="item in options" :key="item.id"  :value="item.value">{{ item.label }}</a-select-option>
                                        </a-select>
                                    </div>
                                </div>
                            </template>
              </a-table>

                <div class="create-btn">
                      <a-button type="primary" class="new-btn-create" @click="submitData">提交</a-button>
                      <a-button class="cancer" @click="goback">{{$t('btn_cancel')}}</a-button>
               </div>
           </div>

             <a-modal
                class="model"
                :title="Title"
                :width="width"
                :visible="dialogTableVisible"
                :confirm-loading="confirmLoading"
                @ok="handleOk"
                @cancel="handleCancel"
                >
                <changeObjectDialog ref="changeObject"></changeObjectDialog>
             </a-modal>
    </div>
</template>

<script>
import changeObjectDialog from './changeObjectDialog'

import ModelFactory from "jw_apis/model-factory"
const searchSignList = ModelFactory.create({ //列表数据
  url: `${Jw.gateway}/${Jw.changeServer}/ecr/countersignature/effect`,
  method: "get"
});

let createSign = ModelFactory.create({ //提交保存
  url: `${Jw.gateway}/${Jw.changeServer}/ecr/countersignature/evaluation`,
  method: "post"
});

let completeTask = ModelFactory.create({ //完成任务
  url: `${Jw.gateway}/${Jw.workflowServer}/workflow/task/finishTask`,
  method: "post"
});
export default {
    name:'task-sign',
    props:['fullHeight','ecrinfo', 'taskId', 'workflowId'],
    data(){
        return {
          confirmLoading: false,
          Title: '选择变更对象',
          tableData: [
              {
                key: '1',
                name: '风扇001',
                number: 'AJ0012345',
                modelType: '文档',
                viewName:'Design',
                version:'A.1',
                isEdit: false,
                evaluation: true
              },
              {
                key: '2',
                name: '风扇001',
                number: 'AJ0012345',
                modelType: '文档',
                viewName:'Design',
                version:'A.1',
                isEdit:false,
                evaluation: false
              },
              {
                key: '3',
                name: '风扇001',
                number: 'AJ0012345',
                modelType: '文档',
                viewName:'Design',
                version:'A.1',
                isEdit:false,
                evaluation: false
              },
              {
                key: '4',
                name: '风扇001',
                number: 'AJ0012345',
                modelType: '文档',
                viewName:'Design',
                version:'A.1',
                isEdit:false,
                evaluation: false
              },
              {
                key: '5',
                name: '风扇001',
                number: 'AJ0012345',
                modelType: '文档',
                viewName:'Design',
                version:'A.1',
                isEdit:false,
                evaluation: false
              },
              {
                key: '6',
                name: '风扇0032',
                number: 'AJ0012345',
                modelType: '文档',
                viewName:'Design',
                version:'A.1',
                isEdit:false,
                evaluation: true
              },
              {
                key: '7',
                name: '风扇0033',
                number: 'AJ0012345',
                modelType: '文档',
                viewName:'Design',
                version:'A.1',
                isEdit:false,
                evaluation: true
              },{
                key: '8',
                name: '风扇001',
                number: 'AJ0012345',
                modelType: '文档',
                viewName:'Design',
                version:'A.1',
                isEdit: false,
                evaluation: true
              },
              {
                key: '9',
                name: '风扇0032',
                number: 'AJ0012345',
                modelType: '文档',
                viewName:'Design',
                version:'A.1',
                isEdit: false,
                evaluation: true,
              }
            ],
          columns:[
              {
                title: '名称',
                dataIndex: 'name',
                key: 'name',
                sorter: true,
                scopedSlots: { customRender: 'name' },
              },
              {
                title: '编码',
                dataIndex: 'number',
                sorter: true,
                key: 'number',
              },
              {
                title: '类型',
                dataIndex: 'modelType',
                key: 'modelType',
                sorter: true,
                ellipsis: true,
              },

              {
                title: '视图',
                dataIndex: 'viewName',
                sorter: true,
                key: 'viewName',
              },
              {
                title: '版本',
                dataIndex: 'version',
                key: 'version',
                sorter: true,
                ellipsis: true,
              },
            {
                title: '会签结果',
                dataIndex: 'countersignerName',
                key: 'countersignerName',
                sorter: true,
                scopedSlots: { customRender: 'countersignerName' },
                 customCell: this.executePersonCustomCell,
                ellipsis: true,
              },
            ],
          keyword: null,
          width: 960,
          taskpersonnel:[],
          multipleSelection: [],
          newTableArr:[],
          dialogTableVisible: false,
          selectedRowKeys:[],
          labelPosition: "top",
          options: [
            {
              id: "1",
              label:'需要变更',
              value: true
            },
            {
              id: "2",
              label: '无需变更',
              value: false
            }
          ],
          ruleForm: {
          },
          rules: {
          }
        }
    },
    
    components: {
       changeObjectDialog
    },
    filters: {
         filterevaluation(val){
            return  val ? '需要变更' : '无需变更'
         }
    },
    methods: {

    viewDetail(item) {
      let text = this.$router.resolve({
        path: `/changeManage/detail/${item.modelType}/${item.oid}`,
        query: {
          name: item.name,
          lifecycle: item.lifecycle
        }
      });
      window.open(text.href, "_blank");
    },
  
       fetchsignList(ecrOid) {
      let param = {
        ecrOid: ecrOid
      };
      searchSignList
        .execute(param)
        .then(data => {
          for (let i = 0; i < data.length; i++) {
            data[i].newmodelType = data[i].inheritModelType
              ? data[i].inheritModelType
              : data[i].modelType;
            this.$set(data[i], "evaluation", true);
            if (data[i].children && data[i].children.length > 0) {
              this.dataToEvaluation(data[i].children);
            }
          }
          this.tableData = data;
          this.newTableArr = data;
          this.invariant = data;
          this.oldDataArr = [];
          this.getChildrenData(data);
        })
        .catch(err => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
      getaccount(event, rows) {
            this.$set(rows, "isEdit", false);
          },
       executePersonCustomCell(record){
            return {
              on: {
                 click: (event) => {
                    this.$set(record, 'isEdit', true)
                 }
              }
            }
        },
        createWork(ecrinfo) {
          ecrinfo.taskId = this.taskId;
          ecrinfo.assignee = this.statusinfo.assignee;
          ecrinfo.processInstanceId = this.workflowId;
          completeTask
            .execute(ecrinfo)
            .then(data => {
              this.$success(this.$t('pdm.baseline.successful'));
              this.$router.push("/my-task");
            })
            .catch(err => {
              if (err.msg) {
                this.$error(err.msg);
              }
            });
        },
        submitData(){  //提交保存
           this.ruleForm.ecrOid = this.ecrinfo.oid;
            if (this.tableData.length > 0) {
              this.newArr = [];
              var tableData = this.tableData;
              this.ruleForm.countersignatureList = tableData;
            }
            createSign
              .execute(this.ruleForm)
              .then(data => {
                this.createWork(this.ecrinfo1[0]);
              })
              .catch(err => {
                if (err.msg) {
                  this.$error(err.msg);
                }
              });

        },
         onSelectChange(selectedRowKeys, selectedRows) {
            this.selectedRowKeys = selectedRowKeys
            this.multipleSelection = selectedRows
            console.log('this.multipleSelection',this.multipleSelection)
         },
         goback() {
           this.$router.go(-1);
        },
        handleCancel() {
            this.dialogTableVisible = false
        },
        handleOk(){
            let newAddData = [...this.$refs.changeObject.selectData, ...this.$refs.changeObject.newselectData]
            this.newTableArr = [...this.newTableArr,...newAddData]
            this.dialogTableVisible = false
        },
        searchInfoBtn(val) {
            this.newTableArr = [];
            for (let j = 0; j < this.tableData.length; j++) {
              var flag = this.tableData[j].number.indexOf(val);
              var isflag = this.tableData[j].name.indexOf(val);
              if (flag >= 0 || isflag >= 0) {
                this.newTableArr.push(this.tableData[j]);
              } else {
                if (
                  this.tableData[j].children &&
                  this.tableData[j].children.length > 0
                ) {
                  this.searchRecursive(this.tableData[j].children, val, j);
                }
              }
            }
        },

        searchRecursive(tablechildren, val, j) {
      for (let z = 0; z < tablechildren.length; z++) {
        var flag = tablechildren[z].number.indexOf(val);
        var isflag = tablechildren[z].name.indexOf(val);
        if (flag >= 0 || isflag >= 0) {
          this.newTableArr.push(this.tableData[j]);
        } else {
          if (
            tablechildren[z].children &&
            tablechildren[z].children.length > 0
          ) {
            this.searchRecursive(tablechildren[z].children, val, j);
          }
        }
      }
        },
        addChangeObject(){
            this.dialogTableVisible = true;
        },
          handleMenuClick(command) {
            if(multipleSelection.length > 0) {
                    if (command.key == "1") {
                    for (let i = 0; i < this.multipleSelection.length; i++) {
                    this.$set(this.multipleSelection[i], "evaluation", false);
                  }
                } else {
                    for (let i = 0; i < this.multipleSelection.length; i++) {
                    this.$set(this.multipleSelection[i], "evaluation", true);
                    }
                }
            }else {
              
            }
           
        },
    },
    mounted(){
       this.fetchsignList(this.ecrinfo.oid)
    }
}
</script>


<style scoped>
.select-option{
  width: 100px;
}
.model>>>.ant-modal-header {
    border-bottom: none;
}
.model>>>.ant-modal-body {
  padding: 0 20px;
}
.model>>>.ant-modal-footer{
    border-top: none;
}
.cancer {
  width: 57px;
  height: 32px;
  background: #fff;
  padding: 0;
}
.new-btn-create {
  width: 66px;
  height: 32px;
  background: #255ed7;
  border-radius: 4px;
  text-align: center;
  padding: 0;
  margin-right: 10px;
}
.create-btn {
  border-top: 1px solid rgba(30, 32, 42, 0.15);
  width: 100%;
  display: flex;
  justify-content: left;
  align-items: center;
  background: #fff;
  height: 60px;
  line-height: 60px;
  box-sizing: border-box;
  z-index: 1000;
  padding-left: 24px;
}

.product-info {
  display: flex;
  align-items: center;
}
.icon-product{
    width: 15px;
    height: 15px;
    margin-right: 10.5px;
}
.classtable {
  margin: 0;
  padding: 0 20px 20px 20px;
  
}
.search-btn {
  width: 216px;
  margin-left: 8px;
}
.add-object {
  background: #255ed7;
  border-radius: 4px;
  color: #fff;
  width: 108px;
  margin: 0;
  padding: 0;
}
.search-info-list {
  display: flex;
  align-items: center;
  margin-top: 15px;
}

.dropdown {
  margin-left: 8px;
}

.form-content {
  padding: 16px 20px;
}

.ecrinfoBtn span {
  font-size: 14px;
  color: #255ed7;
  cursor: pointer;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  height: 32px;
  line-height: 32px;
}
.ecrinfoBtn {
  width: 552px;
  height: 32px;
  background: rgba(30, 32, 42, 0.04);
  border: 1px solid rgba(30, 32, 42, 0.15);
  border-radius: 4px;
  padding: 0 16px;
}

.form-content>>>.ant-form-item {
  margin-bottom: 0;
}
.tishi-info span {
  font-size: 14px;
  color: rgba(30, 32, 42, 0.65);
}
.tishi-info {
  margin: 0 20px;
  height: 42px;
  background: #f0f7ff;
  border: 1px solid #a4c9fc;
  border-radius: 4px;
  display: flex;
  align-items: center;
  padding: 0 16px;
}
.workflow-main {
  background: #fff;
  box-shadow: 0 2px 8px 0 rgb(30 32 42 / 25%);
  border-radius: 4px;
  padding: 20px 0 0 0;
  margin: 8px 0 0 0;
  overflow: auto;
  transform: translate(0, 0);
  display: flex;
  flex-direction: column;
}
</style>