<template>
    <div>
        <a-form-model ref="form" :model="form" label-width="80px">
                            <div class="change-plan">
                                <div class="product-name"><span>{{selectItem.name}}</span></div>
                                <div class="before-info">
                                        <div class="upload-socure">
                                            <span>原视图</span>
                                            <em v-show="status=='show'" @click="downLoadAttach(selectItem)">{{selectItem.number}},{{selectItem.name}},<lable v-if="selectItem.viewName">{{selectItem.viewName}},</lable>{{selectItem.version}}</em>
                                            </div>

                                        <div class="beforeChange-contant">
                                            <el-upload
                                                class="upload-demo"
                                                drag
                                                action="before"
                                                v-if="status!='show'"
                                                :http-request="UploadImage"
                                                >
                                                <div class="file-list">
                                                    <div class="file-describe">
                                                        <p class="ant-upload-drag-icon">
                                                        <img
                                                            src="../../../assets/image/uploadImg.png"
                                                            class="upload-img"
                                                        />
                                                        </p>
                                                        <p class="upload-hint">将文件拖拽到此处，或点击上传</p>
                                                    </div>
                                                    <div>
                                                        <p class="file-detail">上传文件大小不能超过20M</p>
                                                    </div>
                                                    </div>
                                            </el-upload>
                                            <div class="pic-list">
                                                <li v-for="(item, index) of selectItem.newProperties.beforeChange.screenshots" :key="index">
                                                    <div v-if="status!='show'" class="closed-icon" @click="deletePic('beforeChange',index)"><img src="../../../assets/image/delete.png" class="delete-icon"></div>
                                                    <img :src="item">
                                                </li>
                                            </div>
                                    </div>
                               </div>


                                <div class="before-info">
                                        <div class="upload-socure">
                                            <span>修改视图</span>
                                             <em v-show="status=='show'" @click="downLoadAttach(selectItem)">{{selectItem.number}},{{selectItem.name}},<lable v-if="selectItem.viewName">{{selectItem.viewName}},</lable>{{selectItem.currentVersion}}</em>
                                        </div>

                                        <div class="beforeChange-contant">
                                            <el-upload
                                                class="upload-demo"
                                                drag
                                                action="after"
                                                 v-if="status!='show'"
                                                :http-request="UploadImage"
                                                >
                                                <div class="file-list">
                                                    <div class="file-describe">
                                                        <p class="ant-upload-drag-icon">
                                                        <img
                                                            src="../../../assets/image/uploadImg.png"
                                                            class="upload-img"
                                                        />
                                                        </p>
                                                        <p class="upload-hint">将文件拖拽到此处，或点击上传</p>
                                                    </div>
                                                    <div>
                                                        <p class="file-detail">上传文件大小不能超过20M</p>
                                                    </div>
                                                    </div>
                                            </el-upload>
                                          
                                             <div class="pic-list">
                                                <li v-for="(item, index) of selectItem.newProperties.afterChange.screenshots" :key="index">
                                                    <div v-if="status!='show'" class="closed-icon" @click="deletePic('afterChange',index)">
                                                        <img src="../../../assets/image/delete.png" class="delete-icon"></div>
                                                    <img :src="item">
                                                </li>
                                            </div>
                                    </div>
                               </div>


                               <!-- <div class="schemes-info">
                                   <quillEditor v-model.trim="selectItem.newProperties.schemes" v-if="status=='show'" disabled style="height:110px" />
                                   <quillEditor v-model.trim="selectItem.newProperties.schemes" v-else  style="height:110px" />
                                 </div> -->

                      </div>
        </a-form-model>
    </div>
</template>


<script>
// import util from 'jw_common/util';
// import { quillEditor } from "vue-quill-editor";
export default {
    name:'cadocument',
    props: ['selectItem','status','versionVal'],
    // components: {
    //     quillEditor
    // },
    data(){
       return {
             files:[],
             fileList:[],
             bomData: {},
             form: {
                beforeChange: {
                   filename:'',
                   oid:'',
                   screenshots:[]
                },
                afterChange:{
                   filename:'',
                   oid:'',
                   screenshots:[]
                }
            }
       }
    },
     watch: {
        selectItem(newV){
              if(!this.selectItem.hasOwnProperty("newProperties")){
                   this.selectItem.newProperties = this.form
            }
        },
        
      form: {
        deep: true,
        handler: function(newVal) {
             this.$emit('change')
        }
      }
    },
    methods: {
        deletePic(item, index){
               if(item =='beforeChange') {
                    this.selectItem.newProperties.beforeChange.screenshots.splice(index, 1)
               }else {
                    this.selectItem.newProperties.afterChange.screenshots.splice(index, 1)
               }
         },
         downLoadAttach(item) {
             console.log('item', item)
            // util.download(
            //     `${Jw.gateway}/${Jw.fileServer}/file/downloadByOid`,
            //     { oid: item.file.oid },
            //     'get',
            // )
        },
         UploadImage(param){
           let flag =  this.validationImage(param.file, param.action)
           if(flag) {
                 this.files.push(param.file)
                 this.uploadImg(this.files, param.action)
               }
           },
            validationImage(file, action) {
            if(action == 'before' || action == 'after' ) {
                const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png" || file.type === "image/bmp" || file.type === "image/gif" || file.type === "image/tif"
                if (!isJpgOrPng) {
                            this.$error('只能上传图片格式');
                            return false;
                    }
                const isLt2M = file.size / 1024 / 1024 < 20;
                if (!isLt2M) {
                        this.error('上传文件大小不能超过20M');
                        return false;
                    }

                    if(isLt2M && isJpgOrPng) {
                        return true
                    }
    
                } else {
                    let len = file.name.split('.').length
                    const isJpgOrPng = file.name.split('.')[len - 1]
                    console.log('isJpgOrPng', isJpgOrPng)
                    if (!isJpgOrPng) {
                            this.$error('只能上传CAD格式');
                            return false;
                        }else {
                            return true
                     }
                 }
            },

            uploadImg(param, action) {  //图片上传
                if (param.length > 0) {
                let formData = new FormData();
                let xhr = new XMLHttpRequest();
                param.forEach(function (file) {
                    formData.append("file", file, file.name); // 因为要上传多个文件，所以需要遍历一下才行
                });
                xhr.open("POST", `${Jw.gateway}/${Jw.fileServer}/file/multiUpload-v2`, true);
                xhr.onload = (res) => {
                    const response = JSON.parse(xhr.response)
                    let result = response.result[0]
                    let screenshots = `${Jw.gateway}/${Jw.fileServer}/file/downloadByOid?oid=${result.oid}`
                    if(action=='before') {
                        this.selectItem.newProperties.beforeChange.screenshots.push(screenshots) 
                    }else  {
                        this.selectItem.newProperties.afterChange.screenshots.push(screenshots) 
                    } 
                    this.files = []
                };
                xhr.send(formData);
                } else {
                
                }
            },
    },
     created() {
          console.log('newVddd',this.selectItem)
            if(!this.selectItem.hasOwnProperty("newProperties")){
                   this.selectItem.newProperties = this.form
            }
     },
}
</script>

<style scoped>
.schemes-info{
    margin-top: 10px;
}
.delete-icon{
    width: 16px;
    height: 16px;
    cursor: pointer;
}
.closed-icon{
    position: absolute;
    top: 0;
    right: 0;
    width: 16px;
    height: 16px;
}
.titles {
    height: 22px;
    line-height: 22px;
    margin-bottom: 8px;
}
.before-info {
    height: 270px;
}
.upload-demo>>>.el-upload-list{
    display: none;
}
.upload-demo>>>.el-upload {
    width: 100%;
}
.upload-demo>>>.el-upload-dragger{
    height: 50px;
    width: 100%;
    display: flex;
    align-items: center;
    background: rgba(30,32,42,0.04);
}
.pic-list {
    margin-top: 20px;
    width: 100%;
    overflow-y: auto;
    height: 130px;
}
.pic-list li {
    position: relative;
}
.pic-list li img {
    width: 100%;
}
.upload-img {
  width: 28px;
  height: 28px;
  margin-right: 12px;
}
.file-describe {
  display: flex;
  align-items: center;
}
.file-list {
  display: flex;
  align-items: center;
  padding: 0 20px;
  justify-content: space-between;
  width: 100%;
}

.beforeChange-contant{
    padding: 15px;
    overflow-y: scroll;
    box-sizing: border-box;
}
.upload-socure em {
    font-size: 14px;
    color: #255ED7;
    font-style: normal;
    cursor: pointer;
}
.upload-socure span {
    font-size: 14px;
    color: rgba(30,32,42,0.65);
}
.upload-socure {
    height: 36px;
    line-height: 36px;
    border-bottom: 1px solid rgba(30,32,42,0.15);
    border-top: 1px solid rgba(30,32,42,0.15);
    padding: 0 18px;
    display: flex;
    justify-content: space-between;
}
.product-name{
    height: 54px;
    display: flex;
    padding: 0 18px;
    align-items: center;
}
.product-name span {
    font-size: 14px;
    color: rgba(30,32,42,0.85);
}
.change-plan {
   border: 1px solid rgba(30,32,42,0.15);
   height: 595px;
}
</style>