<template>
  <div>
    <div class="workflow-main">
      <div class="tishi">
        <img src="../../../assets/image/info.png" class="info-icon" />
        <span>系统会自动默认选择相关对象的创建人员为默认会签人，您需要确认相关对象变更的会签人员是否正确，您也可以直接修改相关会签人。</span>
      </div>

      <div class="form-content">
        <a-form-model
          :model="ruleForm"
          :rules="rules"
          size="small"
          ref="ruleForm"
          :label-position="labelPosition"
          class="demo-ruleForm"
        >
          <a-form-model-item label="ECR信息">
            <div class="ecrinfoBtn">
              <span>变更编码+名称</span>
            </div>
          </a-form-model-item>

          <div class="mergecolumn">
            <a-form-model-item label="审核结果" prop="assignee">
              <a-radio-group
                v-model.trim="ruleForm.variables.agree"
                @change="switchResult"
                style="width:500px"
              >
                <a-radio :value="0">通过</a-radio>
                <a-radio :value="1">驳回</a-radio>
                <a-radio :value="2">驳回指定会签</a-radio>
              </a-radio-group>
            </a-form-model-item>

            <a-form-model-item label="分发任务人" prop="account" v-if="flag && !isSelection">
              <a-select v-model.trim="ruleForm.account" class="selectask">
                <a-select-option
                  v-for="item in taskpersonnel"
                  :key="item.oid"
                  :label="item.name"
                  :value="item.account"
                >{{ item.name }}</a-select-option>
              </a-select>
            </a-form-model-item>
          </div>

          <a-form-model-item label="备注" prop="comment" v-if="flag">
            <a-input v-model.trim="ruleForm.comment" class="long-int" rows="3" type="textarea" />
          </a-form-model-item>

          <a-form-model-item label="驳回说明" prop="comment" v-else>
            <a-input v-model.trim="ruleForm.comment" class="long-int" rows="3" type="textarea" />
          </a-form-model-item>

          <a-form-model-item label="会签结果" v-if="flag">
            <a-table
              :columns="columns"
              class="classtable"
              :data-source="tableData"
              :pagination="false"
              :row-selection="rowSelection"
            >
              <template slot="evaluation" slot-scope="text, record">
                <span v-if="record.evaluation" class="noeffect">需要变更</span>
                <span v-else class="effect">无影响</span>
              </template>
            </a-table>
          </a-form-model-item>
        </a-form-model>
      </div>

      <div class="create-btn">
        <a-button type="primary" class="new-btn-create" @click="submitData">提交</a-button>
        <a-button class="cancer" @click="goback">{{$t('btn_cancel')}}</a-button>
      </div>
    </div>
  </div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
const searchApproval = ModelFactory.create({
  //列表数据
  url: `${Jw.gateway}/${Jw.changeServer}/ecr/allEffectWithCountersignatureInfo`,
  method: "get"
});

let searchAudit = ModelFactory.create({
  //分发任务人
  url: `${Jw.gateway}/${Jw.accountServer}/v2/user/searchByKeywordAndPaging`,
  method: "get"
});

let createApproval = ModelFactory.create({
  //提交保存
  url: `${Jw.gateway}/${Jw.changeServer}/ecWorkflow/ecr/approval/afterCountersignature`,
  method: "post"
});

export default {
  name: "task-approval",
  props: ["fullHeight"],
  data() {
    return {
      columns: [
        {
          title: this.$t('txt_name'),
          dataIndex: "name",
          key: "name",
          sorter: true,
          scopedSlots: { customRender: "name" }
        },
        {
          title:this.$t('txt_number'),
          dataIndex: "number",
          sorter: true,
          key: "number"
        },
        {
          title: this.$t('txt_type'),
          dataIndex: "modelType",
          key: "modelType",
          sorter: true,
          ellipsis: true
        },

        {
          title: "视图",
          dataIndex: "viewName",
          sorter: true,
          key: "viewName"
        },
        {
          title: this.$t('txt_version'),
          dataIndex: "version",
          key: "version",
          sorter: true,
          ellipsis: true
        },
        {
          title: "会签结果",
          dataIndex: "evaluation",
          key: "evaluation",
          sorter: true,
          scopedSlots: { customRender: "evaluation" },
          ellipsis: true
        }
      ],
      tableData: [
        {
          key: "1",
          name: "风扇001",
          number: "AJ0012345",
          modelType: "文档",
          viewName: "Design",
          version: "A.1",
          isEdit: false,
          evaluation: true
        },
        {
          key: "2",
          name: "风扇001",
          number: "AJ0012345",
          modelType: "文档",
          viewName: "Design",
          version: "A.1",
          isEdit: false,
          evaluation: true
        },
        {
          key: "3",
          name: "风扇001",
          number: "AJ0012345",
          modelType: "文档",
          viewName: "Design",
          version: "A.1",
          isEdit: false,
          evaluation: false
        },
        {
          key: "4",
          name: "风扇001",
          number: "AJ0012345",
          modelType: "文档",
          viewName: "Design",
          version: "A.1",
          isEdit: false,
          evaluation: false
        },
        {
          key: "5",
          name: "风扇001",
          number: "AJ0012345",
          modelType: "文档",
          viewName: "Design",
          version: "A.1",
          isEdit: false,
          evaluation: true
        },
        {
          key: "6",
          name: "风扇0032",
          number: "AJ0012345",
          modelType: "文档",
          viewName: "Design",
          version: "A.1",
          isEdit: false,
          evaluation: true
        },
        {
          key: "7",
          name: "风扇0033",
          number: "AJ0012345",
          modelType: "文档",
          viewName: "Design",
          version: "A.1",
          isEdit: false,
          evaluation: true
        },
        {
          key: "8",
          name: "风扇001",
          number: "AJ0012345",
          modelType: "文档",
          viewName: "Design",
          version: "A.1",
          isEdit: false,
          evaluation: true
        },
        {
          key: "9",
          name: "风扇0032",
          number: "AJ0012345",
          modelType: "文档",
          viewName: "Design",
          version: "A.1",
          isEdit: false,
          evaluation: false
        }
      ],
      labelPosition: "top",
      taskpersonnel: [],
      flag: true,
      isSelection: false,
      multipleSelection: [],
      selectedRowKeys: [],
      ruleForm: {
        ecrOid: "",
        account: null,
        taskId: "",
        processInstanceId: "",
        tenantOid: "********-e05f-11eb-a9ba-000c29724790",
        // assignee: Jw.getUser().account,
        comment: "",
        variables: { agree: 0, sendTask: "" },
        recountersignatureList: [],
        selectedRowKeys: []
      },
      form: {
        searchKey: "",
        pageNum: "1",
        pageSize: "50",
        tenantId: 1,
        tenantOid: "********-e05f-11eb-a9ba-000c29724790"
        // tenantOid: Jw.getUser().tenantOid
      },
      rules: {
        account: [
          { required: true, message: "请选择分发任务人", trigger: "change" }
        ],
        comment: [{ required: true, message: "驳回必填", trigger: "change" }]
      }
    };
  },
  computed: {
    rowSelection() {
      if (this.ruleForm.variables.agree == "2") {
        return {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange
        };
      }
    }
  },
  methods: {
    jumplink(scoped) {
      var type = scoped.inheritModelType
        ? scoped.inheritModelType
        : scoped.modelType;
      type = type == "Part" ? "Part-detail" : "detail";
      let text = this.$router.resolve({
        path: `/${type}/${scoped.oid}/${scoped.modelType}/?from=Product`
      });
      window.open(text.href, "_blank");
    },

    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.multipleSelection = selectedRows;
    },

    viewDetail(item) {
      let text = this.$router.resolve({
        path: `/changeManage/detail/${item.modelType}/${item.oid}`,
        query: {
          name: item.name,
          lifecycle: item.lifecycle
        }
      });
      window.open(text.href, "_blank");
    },

    searchApprovalList(ecrOid) {
      let param = {
        ecrOid: ecrOid
      };
      searchApproval
        .execute(param)
        .then(data => {
          for (let i = 0; i < data.length; i++) {
            data[i].newmodelType = data[i].inheritModelType
              ? data[i].inheritModelType
              : data[i].modelType;
          }

          console.log("table", data);
          this.tableData = data;
        })
        .catch(err => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
    switchResult(val) {
      console.log("val.target.value", val.target.value);
      var selectVal = val.target.value;
      if (selectVal == 0) {
        this.flag = true;
        this.isSelection = false;
      } else if (selectVal == 1) {
        this.flag = false;
        this.isSelection = false;
      } else if (selectVal == 2) {
        this.flag = true;
        this.isSelection = true;
      }
    },
    goback() {
      this.$router.go(-1);
    },
    fetchApprovalList() {
      searchAudit
        .execute(this.form)
        .then(data => {
          this.taskpersonnel = data.rows;
          console.log("sss", this.taskpersonnel);
        })
        .catch(err => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
    submitData() {
      //提交保存
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.ruleForm.ecrOid = this.ecrinfo.oid;
          this.ruleForm.taskId = this.taskId;
          this.ruleForm.processInstanceId = this.workflowId;
          if (this.ruleForm.variables.agree == 0) {
            this.ruleForm.recountersignatureList = [];
            this.ruleForm.variables.sendTask = this.ruleForm.account;
          }
          if (this.ruleForm.variables.agree == 1) {
            this.ruleForm.recountersignatureList = [];
            this.ruleForm.variables.sendTask = "";
          }
          if (this.ruleForm.variables.agree == 2) {
            this.ruleForm.recountersignatureList = this.multipleSelection;
            this.ruleForm.variables.sendTask = "";
          }
          delete this.ruleForm.account;
          console.log("this.ruleForm", this.ruleForm);
          createApproval
            .execute(this.ruleForm)
            .then(data => {
              console.log(data);
              this.$router.push("/my-task");
            })
            .catch(err => {
              if (err.msg) {
                this.$error(err.msg);
              }
            });
        }
      });
    },

    dataToconversion(tableData) {
      for (let i = 0; i < tableData.length; i++) {
        this.newArr.push({
          changeObjectType: tableData[i].modelType,
          changeObjectOid: tableData[i].oid,
          countersignerAccount: tableData[i].countersignerAccount
            ? tableData[i].countersignerAccount
            : "",
          countersignerName: tableData[i].countersignerName
            ? tableData[i].countersignerName
            : ""
        });

        if (tableData[i].children && tableData[i].children.length > 0) {
          for (let j = 0; j < tableData[i].children.length; j++) {
            this.dataToconversion(tableData[i].children);
          }
        }
      }

      return this.newArr;
    }
  },
  mounted() {
    // if (this.ecrinfo) {
    //     this.ecrinfolist = this.ecrinfo.number + "," + this.ecrinfo.name;
    //     this.searchApprovalList(this.ecrinfo.oid);
    //   }

    this.fetchApprovalList(); //获取任务分发人
  }
};
</script>

<style scoped>
.long-int {
  width: 552px;
}
.ecrinfoBtn span {
  font-size: 14px;
  color: #255ed7;
  cursor: pointer;
  display: block;
  height: 32px;
  line-height: 32px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.ecrinfoBtn {
  width: 552px;
  height: 32px;
  background: rgba(30, 32, 42, 0.04);
  border: 1px solid rgba(30, 32, 42, 0.15);
  border-radius: 4px;
  padding: 0 16px;
}
.numbers {
  color: #409eff;
  cursor: pointer;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.effect {
  color: #f2994a;
}
.noeffect {
  color: rgba(30, 32, 42, 0.65);
}
.cancer {
  width: 57px;
  height: 32px;
  background: #fff;
  padding: 0;
}
.new-btn-create {
  width: 66px;
  height: 32px;
  background: #255ed7;
  border-radius: 4px;
  text-align: center;
  padding: 0;
  margin-right: 10px;
}
.create-btn {
  border-top: 1px solid rgba(30, 32, 42, 0.15);
  width: 100%;
  display: flex;
  justify-content: left;
  align-items: center;
  background: #fff;
  height: 72px;
  line-height: 72px;
  box-sizing: border-box;
  z-index: 1000;
  padding-left: 24px;
}
.mergecolumn {
  width: 552px;
  display: flex;
  flex-flow: wrap;
  justify-content: space-between;
}
.demo-ruleForm >>> .el-form-item__label {
  height: 32px;
  line-height: 32px;
  padding: 0;
}
.selectask {
  width: 285px;
}

.long-int {
  width: 552px;
}
.form-content {
  padding: 16px 20px;
}
.tishi span {
  font-size: 14px;
  color: rgba(30, 32, 42, 0.65);
}
.info-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}
.tishi {
  margin: 0 20px;
  height: 42px;
  background: #f0f7ff;
  border: 1px solid #a4c9fc;
  border-radius: 4px;
  display: flex;
  align-items: center;
  padding: 0 16px;
}
.workflow-main {
  background: #fff;
  box-shadow: 0 2px 8px 0 rgb(30 32 42 / 25%);
  border-radius: 4px;
  padding: 20px 0px 0 0;
  margin: 8px 0 0 0;
  overflow: auto;
  transform: translate(0, 0);
}
.plm-icon-back {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}
.workflow-title {
  height: 22px;
  line-height: 22px;
  display: flex;
  align-items: center;
  padding: 0px;
}

.workflow-title span {
  font-size: 14px;
  color: #1e202a;
  font-weight: 600;
}
</style>