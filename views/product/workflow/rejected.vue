<template>
    <div>
        
        <div class="workflow-main">
             <div class="tishi"><img src="../../../assets/image/info.png" class="info-icon">
             <span>当前任务已被驳回，请仔细阅读驳回说明，点击“编辑ECR”前往ECR管理页面修改并重新提交审核。</span></div>
            <div class="form-content">
             <a-form-model :model="ruleForm" :rules="rules" size="small" ref="ruleForm" :label-position="labelPosition" class="demo-ruleForm">
                    <a-form-model-item label="ECR信息" prop="name">
                        <div class="ecrinfoBtn" @click="viewDetail(ecrinfo)">                         
                            <span >{{ecrinfo.name +','+ ecrinfo.number}}</span>
                        </div>
                    </a-form-model-item>
                   
                     <div class="mergecolumn">
                        <el-form-item :label="`${$t('digitalPlm.change.reviewResut')}：`" prop="assignee">
                                <el-radio-group v-model.trim="agreeVal">
                                    <el-radio disabled :label="0">{{$t('digitalPlm.change.common.approve')}}</el-radio>
                                    <el-radio disabled :label="1">{{$t('digitalPlm.change.common.reject')}}</el-radio>
                                </el-radio-group>
                        </el-form-item>
                      </div>

                      <a-form-model-item label="驳回原因">
                             <a-input type="textarea" disabled placeholder="ECR需要修改：1，修改这里" class="long-int" rows="3" v-model.trim="comment"></a-input>
                      </a-form-model-item>


                        <div class="mergecolumn">
                          <a-form-model-item label="确认结果" prop="assignee">
                                    <a-radio-group v-model.trim="ruleForm.variables.agree">
                                            <a-radio :label="0">重新提交</a-radio>
                                            <a-radio :label="1">取消任务</a-radio>
                                        </a-radio-group>
                           </a-form-model-item>
                        </div>

                        <a-form-model-item label="备注：" prop="comment">
                             <a-input type="textarea"  class="long-int" rows="3" v-model.trim="ruleForm.comment"></a-input>
                       </a-form-model-item>
                     </a-form-model>
                   </div>

          <div class="create-btn">
            <a-button type="primary" class="new-btn-create" @click="submitData">提交</a-button>
            <a-button class="cancer" @click="goback">{{$t('btn_cancel')}}</a-button>
        </div>

        </div>
    </div>
</template>

<script>
// import ModelFactory from 'jw_models/model-factory'
// const searchRejectedList = ModelFactory.create({ //列表数据
//   url: `${Jw.gateway}/${Jw.workflowServer}/workflow/history/historic-taskComment`,
//   method: 'post'
// })

// let completeTask = ModelFactory.create({  //完成任务
//   url: `${Jw.gateway}/${Jw.workflowServer}/workflow/task/finishTask`,
//   method: 'post'
// })

export default {
    name:'task-rejected',
    props:['ecrinfo','taskId','workflowId'],
    data(){
       return {
          agreeVal: 1,
          labelPosition:'top',
          ruleForm:{
              variables: {agree: 0},
              comment:'',
              taskId:'',
              processInstanceId:''
          },
          rules: {
              comment: [{ required: true, message:  "补充说明必填", trigger: "change" }]
          },
          comment:''
       }
    },
    methods: {
           goback(){
           this.$router.go(-1)
        },
      viewDetail(item) {
        let text = this.$router.resolve({
            path: `/changeManage/detail/${item.modelType}/${item.oid}`,
            query: {
                name: item.name,
                lifecycle: item.lifecycle,
            }
        });
       window.open(text.href, "_blank");
    },
       submitData() {
          this.$refs.ruleForm.validate((valid) => {
          if (valid) {

             this.ruleForm.taskId = this.taskId
            this.ruleForm.processInstanceId = this.workflowId
            completeTask.execute(this.ruleForm)
            .then(data => {
                   this.$success( '提交成功!')
                   this.$router.push('/my-task')
              })
               .catch(err => {
                  if (err.message) {
                     this.$error(err.message)
                 }
             })

          }

      })
         

       },
       searchRejectedList(workflowId) {
              searchRejectedList.execute({processInstanceId:workflowId}).then(data => {
                  this.comment = data[1].comment.comment
              })
               .catch(err => {
                  if (err.msg) {
                     this.$error(err.msg)
                 }
             })
       },
    },
    mounted() {
      this.searchRejectedList(this.workflowId)
    }
}
</script>

<style scoped>
.ecrinfoBtn span {
    font-size: 14px;
    color: #255ED7;
    cursor: pointer;
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.ecrinfoBtn {
    width: 552px;
    height: 32px;
    background: rgba(30,32,42,0.04);
    border: 1px solid rgba(30,32,42,0.15);
    border-radius: 4px;
    padding: 0 16px;
}
.numbers {
  color: #409eff;
  cursor: pointer;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.parts-icon {
  width: 16px;
  height: 16px;
  margin-right:5px;
}
.edit-info{
    width:80%;
    height: 32px;
    line-height: 32px;
    box-sizing: border-box;
}
.select-sign {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.edit-icon {
    width: 16px;
    height: 16px;
}
.effect {
  color: #F2994A;
}
.noeffect {
  color: rgba(30,32,42,0.65);
}
.cancer {
  width: 57px;
  height: 32px;
  background: #fff;
  padding: 0;
}
.new-btn-create {
  width: 81px;
  height: 32px;
  background: #255ed7;
  border-radius: 4px;
  text-align: center;
  padding: 0;
  margin-right: 0px;
}
  .create-btn {
    border-top: 1px solid rgba(30,32,42,0.15);
    width: 100%;
    display: flex;
    justify-content: left;
    align-items: center;
   
    background: #fff;
    height: 72px;
    line-height: 72px;
    box-sizing: border-box;
    z-index: 1000;
    
    padding-left: 24px;
  }
.mergecolumn {
    width: 552px;
    display: flex;
    justify-content: space-between;
}
.demo-ruleForm>>>.el-form-item__label {
    height: 32px;
    line-height: 32px;
    padding: 0;
}
.selectask{
    width: 328px;
}

.long-int {
    width: 552px;
}
.form-content {
    padding: 16px 20px;
}
.tishi span {
    font-size: 14px;
    color: rgba(30,32,42,0.65);
}
.info-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
}
.tishi{
    margin: 0 20px;
    height: 42px;
    background: #F0F7FF;
    border: 1px solid #A4C9FC;
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding: 0 16px;
}
.workflow-main {
    background: #fff;
    box-shadow: 0 2px 8px 0 rgb(30 32 42 / 25%);
    border-radius: 4px;
    padding: 20px 0px 0 0;
    margin: 8px 0 0 0;
    overflow: auto;
    transform: translate(0,0);
 }
.plm-icon-back{
  width: 16px;
  height: 16px;
  margin-right: 8px;
  
}
 .workflow-title{
    height: 22px;
    line-height: 22px;
    display: flex;
    align-items: center;
    padding: 0px;
}

.workflow-title span {
    font-size: 14px;
    color: #1E202A;
    font-weight: 600;
}
</style>