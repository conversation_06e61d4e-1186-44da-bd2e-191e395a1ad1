<template>
    <div>
     <a-row :gutter="20">
            <a-col :span="16">
                <div class="switch-search">
                     <div class="switch-menu">
                        <li v-for="(item, index) of listArr" :class="{current: num==index}" @click="switchObject(index)" :key="index">{{item.name}}</li>
                     </div>
                    <div class="dialog-search">
                        <a-input
                            placeholder="请输入内容"
                            @change="searchBtn"
                            size="small"
                            class="search-info-dialog"
                            suffix-icon="el-icon-search"
                            clearable
                            v-model.trim="form.searchKey"
                        ></a-input>
                    </div>
                    </div>

                       <div class="main-table">
                           <a-table :columns="columns" ref="multipleTable" :data-source="tableData" rowKey="oid" :pagination="false" :row-selection="{ selectedRowKeys:this.selectedRowKeys,onSelect:onSelect, onChange: this.onSelectChange}" :scroll="{y: 440}"></a-table>
                       </div>
            </a-col>
            <a-col :span="8">
                 <div class="selectObject">
                       <div class="titles">
                             <div class="checkbox-list-all">
                                    <a-checkbox :indeterminate="isIndeterminate" v-model.trim="checkedVal" @change="allSelect"></a-checkbox>
                                    <em>已选择 {{selectData.length + newselectData.length + oldselectData.length}} </em>
                              </div>
                             <div @click="clearBtn"><span class="clear-btn">{{$t('txt_delete')}}</span></div>
                       </div>
                        <ul>
                        <li v-for="(item, index) of selectData" :key="item.oid + item.id">
                             <div class="object-info">
                                <a-checkbox
                                class="checkbox-list"
                                v-model.trim="item.checked"
                                @change="changeSelect"
                                ></a-checkbox>
                                <div class="list-icon" >
                                <i :class="item.iconURL"></i>
                                </div>
                                <div
                                class="numbers"
                                @click="jumplinkdetails(item)"
                                :title="item.name + ',' + item.number + ',' + item.version"
                                >
                                {{ item.name }},{{ item.number }},{{ item.version }}
                                </div>
                            
                            </div>
                            <div
                                class="select-data-delete"
                                @click="deleteBtn(index, item,'1')"
                            >
                                <img src="../../../assets/image/delete.png" />
                            </div>
                        </li>

                        <li v-for="(item, index) of newselectData" :key="item.oid + item.id">
                            <div class="object-info">
                                <a-checkbox
                                class="checkbox-list"
                                v-model.trim="item.checked"
                                @change="changeSelect"
                                ></a-checkbox>
                                <div class="list-icon" >
                                <i :class="item.iconURL"></i>
                                </div>
                                <div
                                class="numbers"
                                @click="jumplinkdetails(item)"
                                :title="item.name + ',' + item.number + ',' + item.version"
                                >
                                {{ item.name }},{{ item.number }},{{ item.version }}
                                </div>
                            
                            </div>
                            <div
                                class="select-data-delete"
                                @click="deleteBtn(index, item,'2')"
                            >
                                 <img src="../../../assets/image/delete.png" />
                            </div>
                            </li>

                            <li v-for="item of oldselectData" :key="item.oid + item.id">
                            <div class="object-info">
                                <div class="list-icon" >
                                <i :class="item.iconURL"></i>
                                </div>
                                <div
                                class="numbers"
                                @click="jumplinkdetails(item)"
                                :title="item.name + ',' + item.number + ',' + item.version"
                                >
                                {{ item.name }},{{ item.number }},{{ item.version }}
                                </div>
                            
                            </div>
                            
                            </li>

                        </ul>
                 </div>
            </a-col>
      </a-row>
    </div>
</template>

<script>

import ModelFactory from "jw_apis/model-factory"

const getChangeObjecte = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/ecr/getCandidate/byModelType`,
  method: "post",
});
const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    sorter: true,
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '编码',
    dataIndex: 'number',
    sorter: true,
    key: 'number',
  },
 

   {
    title: '视图',
    dataIndex: 'viewName',
    sorter: true,
    key: 'viewName',
  },
  {
    title: '版本',
    dataIndex: 'version',
    key: 'version',
    sorter: true,
    ellipsis: true,
  },

 
];

const data = [
  {
    id: '1',
    name: '风扇001',
    number: 'AJ0012345',
    viewName:'Design',
    version:'A.1',
    
  },
  {
    id: '2',
    name: '风扇001',
    number: 'AJ0012345',
    viewName:'Design',
    version:'A.1',
  },
  {
    id: '3',
    name: '风扇001',
    number: 'AJ0012345',
    viewName:'Design',
    version:'A.1',
  },
  {
    id: '4',
    name: '风扇001',
    number: 'AJ0012345',
    viewName:'Design',
    version:'A.1',
  },
  {
    id: '5',
    name: '风扇001',
    number: 'AJ0012345',
    viewName:'Design',
    version:'A.1',
  },
   {
    id: '6',
    name: '风扇0032',
    number: 'AJ0012345',
    viewName:'Design',
    version:'A.1',
  },
  {
    id: '7',
    name: '风扇0033',
    number: 'AJ0012345',
    viewName:'Design',
    version:'A.1',
  },{
    id: '8',
    name: '风扇001',
    number: 'AJ0012345',
    viewName:'Design',
    version:'A.1',
  },
   {
    id: '9',
    name: '风扇0032',
    number: 'AJ0012345',
    viewName:'Design',
    version:'A.1',
  }
  
];
export default {
    name:'changeObjectDialog',
    data(){
        return {
             columns,
             data,
              checkedVal: false,
              isIndeterminate: false,
              selectData: [],
              checked: false,
              tableData: [],
              newselectData: [],
              newAddArr: [],
              oldselectData: [],
              selectedRowKeys: [],
              form: {
                 searchKey: null,
              },
              num: 0,
              listArr:[{
                    id: '1',
                    name: 'Part'
                },{
                    id: '2',
                    name: 'CAD'
                },{
                    id: '3',
                    name: 'Document'
                }],
        }
    },
    methods: {
         onSelectChange(selectedRowKeys, selectedRows) {
            this.selectedRowKeys = selectedRowKeys
            this.selectData = selectedRows
         },

         onSelect(record, selected){
               let endkeys = this.selectedRowKeys[this.selectedRowKeys.length -1]
               this.$set(record, 'checked', false)
               this.$set(record, 'keyval', endkeys)
         },

     changeSelect(item) {
      let dataLen = this.selectData.length;
      let num = 0;
      for (let j = 0; j < this.selectData.length; j++) {
        if (this.selectData[j].checked) {
          num++;
        }
      }
      if (num > 0 && num < dataLen) {  //部分选中
        this.isIndeterminate = true;
      }
      if (num == dataLen) { //全部选中
        this.isIndeterminate = false;
        this.checkedVal = true;
      }
    },
    deleteBtn(index, item, type) {
      if(type =='1') {
            var newArr = [];
            newArr.push(this.selectData[index]);
            this.selectData.splice(index, 1)
            this.selectedRowKeys.filter((itemlist, xh) => {
                   if(itemlist == item.keyval) {
                        this.selectedRowKeys.splice(xh, 1)
                   }
            })
         
       
      }else {  //之前添加的数据
          this.newselectData.splice(index, 1)
          this.selectedRowKeys.filter((itemlist, xh) => {
                   if(itemlist == item.keyval) {
                        this.selectedRowKeys.splice(xh, 1)
                   }
            })
      }
   
      if (this.selectData.length == 0) {
        this.checkedVal = false;
      }
     
    },
         jumplinkdetails(scoped) {  
            var type = scoped.inheritModelType ? scoped.inheritModelType: scoped.modelType
            type = type =='Part' ? 'Part-detail' :'detail'
            let text = this.$router.resolve({
                path: `/${type}/${scoped.oid}/${scoped.modelType}/?from=Product`
            });

            window.open(text.href, "_blank");
       },
        select(selectedRowKeys) {       
            this.selectedRowKeys = selectedRowKeys
        },
         allSelect(item) {
           let ischecked = item.target.checked
      if (ischecked) {//全选
            this.allSelectBtn(ischecked,this.selectData)
                this.allSelectBtn(ischecked,this.newselectData)
            } else {
                this.allSelectBtn(ischecked,this.selectData)
                this.allSelectBtn(ischecked,this.newselectData)
            }
       },
        allSelectBtn(ischecked,selectData){
        for (let j = 0; j < selectData.length; j++) {
          if(ischecked) {
               if (!selectData[j].checked) {
                this.$set(selectData[j], "checked", true);
              }
          }else {
            if (selectData[j].checked) {
                this.$set(selectData[j], "checked", false);
              }
          }
        }
    },

     exitData(num) {
        for (let z= 0; z < this.newselectData.length; z++) {  //已选
        if (this.newselectData[z].checked) {
            this.newselectData.splice(z, 1);
            z--;
            num++
        }
      }
  },
    clearBtn() {
      var newArr = [];
      var dataLen = this.selectData.length;
      var num = 0;
      for (let j = 0; j < this.selectData.length; j++) { //初选
        if (this.selectData[j].checked) {
           newArr.push(this.selectData[j]);
          this.selectData.splice(j, 1);
          j--;
          num++;
        }
      }

      if(this.newselectData.length > 0) {
          this.exitData(num)
      }
 
       this.selectedRowKeys = this.selectedRowKeys.filter(item => {
              return !newArr.find(row=> {
                    return row.keyval == item
              })
        })
    
      if (num > 0 && num < dataLen) {
        //部分选中
        this.isIndeterminate = true;
      }
      if (num == dataLen) {
        //全部选中
        this.isIndeterminate = false;
        this.checkedVal = false;
      }
    },
            searchBtn(val){
               console.log('val', val)
            },
            switchObject(index) {
                this.num = index
                if(index == 0) {
                this.form.modelType ='Part'
                }else if(index ==1) {
                    this.form.modelType ='CADDocument'
                }else {
                    this.form.modelType ='Document'
                }

                this.fetchSearchInfo()
            },
    },
     fetchSearchInfo(){
        getChangeObjecte
        .execute(this.form)
        .then((data) => {
          let newArr = data.data
          for(let i = 0; i < newArr.length; i++) {
               newArr[i].newModelType = newArr[i].inheritModelType ? newArr[i].inheritModelType :newArr[i].modelType
               this.$set(newArr[i], 'tenantId', '0')
                for(let y =0; y < this.oldselectData.length; y++) {
                     if(this.oldselectData[y].number == newArr[i].number) {
                              this.$set(newArr[i], 'tenantId', '1')
                          }
                }
                 
              } 
          this.tableData = newArr;
        })
        .catch((err) => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
     },
}
</script>


<style scoped>


.checkbox-list-all {
  display: flex;
}
.main-table {
  margin: 0;
  border: 1px solid rgba(30, 32, 42, 0.15);
  height: 517px;
}
.search-info-dialog {
  width: 216px;
  height: 32px;
}
.switch-search {
   display: flex;
   justify-content: space-between;
   align-items: center;
}
.switch-menu {
    height: 54px;
    width: 100%;
    display: flex;
}
.switch-menu li {
   margin: 0 20px;
   height: 52px;
   line-height: 52px;
   cursor: pointer;
}
.switch-menu li.current {
  border-bottom: 2px solid #255ED7;
}


.numbers {
  color: #409eff;
  cursor: pointer;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.cancer {
    height: 32px;
    padding: 0;
    width: 52px;
}
.comfirm-add {
    height: 32px;
    background: #255ED7;
    border-radius: 4px;
    width: 80px;
    padding: 0;
    color: #fff;
}
.comfirm-btn {
    padding: 20px 0 0 0;
    display: flex;
    justify-content: flex-end;
}
.clear-btn {
  cursor: pointer;
  font-size: 14px;
  color: #255ed7;
}
.select-data-delete {
  width: 16px;
  height: 54px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.select-data-delete img {
  width: 16px;
  height: 16px;
}
.product-icon {
  width: 16px;
  height: 16px;
}

.list-icon {
  height: 16px;
  line-height: 16px;
  margin-right: 8px;
}
.object-info {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
}
.selectObject ul li {
  height: 54px;
  line-height: 54px;
  list-style-type: none;
  padding: 0 20px;
  border-bottom: 1px solid rgba(30, 32, 42, 0.06);
  display: flex;
  justify-content: space-between;
}
.selectObject ul {
  margin: 0;
  overflow: auto;
  height: 518px;
}

.checkbox-list {
  margin-right: 8px;
}
.titles em {
  font-style: normal;
  font-size: 14px;
  color: rgba(30, 32, 42, 0.85);
  font-weight: bold;
  margin-left: 8px;
}
.titles {
  height: 54px;
  line-height: 54px;
  padding: 0 20px;
  background: rgba(30, 32, 42, 0.03);
  border-bottom: 1px solid rgba(30, 32, 42, 0.06);
  display: flex;
  justify-content: space-between;
}
.selectObject {
  overflow: hidden;
  height: 570px;
  border: 1px solid rgba(30, 32, 42, 0.15);
}
.main-table {
  margin: 0;
  border: 1px solid rgba(30, 32, 42, 0.15);
  height: 517px;
}
.search-btn {
  width: 216px;
}

.search-info-dialog {
  width: 216px;
  height: 32px;
}
</style>