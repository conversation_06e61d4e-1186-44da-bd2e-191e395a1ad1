<template>
    <div class="binding-tree">
        <div class="binding-tree-header">
            <a-button class="header-role-btn">
                <div class="flex align-center">
                    <svg class="jwifont" aria-hidden="true">
                        <use xlink:href="#jwi-user-groups"></use>
                    </svg>
                    <span>角色</span>
                </div>
            </a-button>
            <a-input-search placeholder="输入关键词搜索" class="header-search-input" @search="onSearch" />
        </div>
        <div class="binding-tree-con" style="height: calc(100vh - 182px)">
            <div id="container">
                <div ref="ref_popper" class="handle-btn" v-click-outside="close" v-if="showPopper">
                    <ul class="col-popover-list">
                        <li @click="onAddNode">添加成员</li>
                        <li class="not-allowed">重命名</li>
                        <li @click="onDeleteNode">{{$t('txt_delete')}}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import G6 from '@antv/g6';
import ClickOutside from './graphApi/outside.js';
import configRegister from './graphApi/config-register';
export default {
    name: 'bindingTree',
    props: [

    ],
    components: {

    },
    directives: {
        'click-outside': ClickOutside,
    },
    data() {
        return {
            graphObj: null,
            showPopper: false,
            selectMoreBtn: false,
        };
    },
    computed: {
    },
    watch: {},
    methods: {
        onSearch() {
            
        },
        createGraphic() {
            let data = {
                name: '团队001',
                size: [236, 52],
                children: [
                    {name: '用户001用户001用户001', size: [144, 52]},
                    {name: '用户002', size: [144, 52]},
                    {name: '用户003', size: [144, 52]},
                    {name: '用户004', size: [144, 52]},
                    {name: '用户005', size: [144, 52]},
                ]
            }
            const container = document.getElementById('container');
            const width = container.scrollWidth;
            const height = container.scrollHeight || 150;
            const cfg = configRegister(G6, {
                container: 'container',
            });
            const graph = (this.graphObj = new G6.TreeGraph(cfg));
            graph.data(data);
            graph.render();
            graph.fitView(20, 20);
            if (typeof window !== 'undefined')
                window.onresize = () => {
                    if (!this.graphObj || this.graphObj.get('destroyed')) return;
                    if (!container || !container.scrollWidth || !container.scrollHeight) return;
                    this.graphObj.changeSize(
                        container.offsetWidth,
                        container.scrollHeight
                    );
                    this.graphObj.fitView(20, 20);
                };

            graph.on('node:click', evt => {
                const { target, canvasX, canvasY } = evt;
                // 更多操作
                if (target.get('name') === 'more-icon') {
                    this.selectMoreBtn = true;
                    evt.stopPropagation();
                    this.showPopper = true;
                    this.$nextTick(() => {
                        this.$refs.ref_popper.style.left =
                            canvasX + (canvasX > width ? -200 : -4) + 'px';
                        this.$refs.ref_popper.style.top =
                            canvasY + (canvasY > height ? -180 : 10) + 'px';
                    });
                }
            });
        },
        close() {
            if (!this.selectMoreBtn) {
                this.showPopper = false;
            }
            this.selectMoreBtn = false;
        },
        onAddNode() {

        },
        onDeleteNode() {

        },
    },
    created() {
        
    },
    mounted() {
        this.createGraphic();
    },
    beforeDestroy() {
        if (!_.isEmpty(this.graphObj)) {
            this.graphObj.destroy();
            this.graphObj = null;
        }
    }
};
</script>

<style lang="less" scoped>
.binding-tree {
    .binding-tree-header {
        height: 52px;
        padding: 10px 20px;
        border-right: 1px solid rgba(30,32,42,0.15);
        border-bottom: 1px solid rgba(30,32,42,0.15);
        .header-role-btn {
            width: 80px;
            .jwifont {
                width: 16px;
                height: 16px;
                margin-right: 3px;
            }
        }
        .header-search-input {
            width: 216px;
            margin-left: 8px;
        }
    }
    .binding-tree-con {
        padding: 5px;
        border-right: 1px solid rgba(30,32,42,0.15);
    }
}
#container {
    position: relative;
    width: 100%;
    height: 100%;
    background: #fff;
    .handle-btn {
        position: absolute;
        left: -10px;
        background: #fff;
        width: 96px;
        border-radius: 4px;
        box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
        padding: 12px;
    }
    .col-popover-list {
        margin-bottom: 0;
        list-style-type: none;
        font-size: 14px;
        color: #545454;
        li {
            line-height: 30px;
            cursor: pointer;
        }
    }
}
</style>
