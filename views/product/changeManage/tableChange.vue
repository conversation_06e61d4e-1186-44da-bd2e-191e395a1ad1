<template>
  <div>
    <div :style="{ height: fullHeight - 60 + 'px' }" class="maintable">
      <JwTable :options="columns" rowKey="id" :datasource="dataArr" :height="fullHeight - 80" :pagination="pagination">
        <template slot="name" slot-scope="{ text }">
          <div class="product-info">
            <svg class="ecn-icon" aria-hidden="true">
              <use xlink:href="#jwi-ecn"></use>
            </svg>
            <router-link to="/changeManage/detail">{{ text }}</router-link>
          </div>
        </template>

         <template slot="type" slot-scope="{text}">
          <div class="product-info">
            <span class="view-text" v-if="text">{{text}}</span>
            <span  v-else>-</span>
          </div>
        </template>

        <template slot="degreeEmergencyValue" slot-scope="text">
          <div class="product-info">
            <span v-if="text == '1'" class="higher">{{$t('txt_very_urgent')}}</span>
            <span v-else-if="text == '2'" class="middler">{{$t('txt_urgent')}}</span>
            <span v-else class="lower">{{$t('txt_general')}}</span>
          </div>
        </template>
          
            <template slot="lifecycle" slot-scope="{text}">
              <div class="product-info">
                  <span v-if="text=='Open'" class="nosubmit">{{$t('txt_been_submitted')}}</span>
                  <span v-else-if="text=='UnderReview'" class="approval">{{$t('txt_approval')}}</span>
                  <span v-else-if="text='Estimate'" class="assessment">{{$t('txt_change_evaluation')}}</span>
                  <span v-else-if="text=='Inwork'" class="perform">{{$t('txt_being_performed')}}</span>
                  <span v-else-if="text=='Released'" class="release">{{$t('txt_published')}}</span>
                  <span v-else class="cancel">{{$t('txt_has_been_cancelled')}}</span>
              </div>
        </template>

      </JwTable>
    </div>

  


  </div>
</template>

<script>
import JwTable from "jw_components/table"
export default {
  name: "tableChange",
  props: ["fullHeight", "dataArr"],
  components: {
    JwTable
  },
  data() {
    return {
            columns: [
               {
                title: '名称',
                dataIndex: 'name',
                key: 'name',
                sorter: true,
                scopedSlots: { customRender: 'name' },
                },
                {
                title: '编码',
                dataIndex: 'code',
                sorter: true,
                width: '400',
                key: 'code',
                ellipsis: true,
                },
                 {
                title: '类型',
                dataIndex: 'type',
                sorter: true,
                key: 'type',
                scopedSlots: { customRender: 'type' },
                },
               {
                title: '紧急程序',
                dataIndex: 'degreeEmergencyValue',
                sorter: true,
                key: 'degreeEmergencyValue',
                scopedSlots: { customRender: 'degreeEmergencyValue' },
                },
              
                {
                title: '状态',
                dataIndex: 'lifecycle',
                sorter: true,
                key: 'lifecycle',
                scopedSlots: { customRender: 'lifecycle' },
                },
                {
                title: '创建人',
                dataIndex: 'creatorName',
                sorter: true,
                key: 'creatorName',
                scopedSlots: { customRender: 'creatorName' },
                },
                
                 {
                title: '创建时间',
                dataIndex: 'createdTime',
                key: 'createdTime',
                sorter: true,
                ellipsis: true,
                scopedSlots: { customRender: 'createdTime' },
                },
             ],
           tableData: [
              {
                id: '1',
                name: '风扇001',
                code:'FSD32',
                type: 'Part',
                degreeEmergencyValue:'crucial',
                lifecycle:'Open',
                creatorName:'张三',
                createdTime:'2021-09-21 13:00',
              },
                {
                id: '2',
                name: '风扇001',
                code:'FSD32',
                type: 'Part',
                degreeEmergencyValue:'crucial',
                lifecycle:'Estimate',
                creatorName:'张三',
                createdTime:'2021-09-21 13:00',
              },
                {
                id: '3',
                name: '风扇001',
                code:'FSD32',
                type: 'Part',
                degreeEmergencyValue:'crucial',
                lifecycle:'UnderReview',
                creatorName:'张三',
                createdTime:'2021-09-21 13:00',
              },
                {
                id: '4',
                name: '风扇001',
                code:'FSD32',
                type: 'Part',
                degreeEmergencyValue:'crucial',
                lifecycle:'Open',
                creatorName:'张三',
                createdTime:'2021-09-21 13:00',
              },
                {
                id: '5',
                name: '风扇001',
                code:'FSD32',
                type: 'Part',
                degreeEmergencyValue:'crucial',
                lifecycle:'Inwork',
                creatorName:'张三',
                createdTime:'2021-09-21 13:00',
              },
                {
                id: '6',
                name: '风扇001',
                code:'FSD32',
                type: 'Part',
                degreeEmergencyValue:'crucial',
                lifecycle:'Released',
                creatorName:'张三',
                createdTime:'2021-09-21 13:00',
              },
                {
                id: '7',
                name: '风扇001',
                code:'FSD32',
                type: 'Part',
                degreeEmergencyValue:'crucial',
                lifecycle:'Open',
                creatorName:'张三',
                createdTime:'2021-09-21 13:00',
              },
                {
                id: '8',
                name: '风扇001',
                code:'FSD32',
                type: 'Part',
                degreeEmergencyValue:'crucial',
                lifecycle:'Open',
                creatorName:'张三',
                createdTime:'2021-09-21 13:00',
              },
                {
                id: '9',
                name: '风扇001',
                code:'FSD32',
                type: 'Part',
                degreeEmergencyValue:'crucial',
                lifecycle:'cancer',
                creatorName:'张三',
                createdTime:'2021-09-21 13:00',
              },
            ],
    
          selectedRowKeys: [],
          pagination:true,
      
    };
  },

  methods: {
      fetchData(){  //当前表格数据
              return new Promise((resolve, reject)=>{
                  resolve({data: this.tableData,total: 9})
              })
          },
     },
  
  mounted() {
 
  },
};
</script>

<style scoped>
.product-info span em{
  width: 8px;
  height: 8px;
  border-radius: 50%;
}
.product-info span em.failer {
   background: #fff1f0;
}
.product-info span em.waitsign {
   background: #fff1f0;
}
.product-info span em.waitaudit {
   background: #fff1f0;
}
.product-info span em.waitapproval {
   background: #fff1f0;
}
.product-info span em.approval {
   background: #fff1f0;
}
.page-list {
  display: flex;
  justify-content: flex-end;
  height: 32px;
  line-height: 32px;
  margin-right: 10px;
  padding: 0;
}
.cancel {
  height: 22px;
  padding: 0 8px;
  background: #fff1f0;
  border: 1px solid #ffc2c3;
  border-radius: 4px;
  font-size: 12px;
  color: #f34f63;
  display: block;
  float: left;
}
.release {
  height: 22px;
  padding: 0 8px;
  background: #f8fff0;
  border: 1px solid #ccedaf;
  border-radius: 4px;
  font-size: 12px;
  color: #52c41a;
  display: block;
  float: left;
}
.perform {
  height: 22px;
  padding: 0 8px;
  background: #f0f7ff;
  border: 1px solid #a4c9fc;
  border-radius: 4px;
  font-size: 12px;
  color: #255ed7;
  display: block;
  float: left;
}
.assessment {
  height: 22px;
  padding: 0 8px;
  background: #e6fffb;
  border: 1px solid #87e8de;
  border-radius: 4px;
  font-size: 12px;
  color: #08979c;
  display: block;
  float: left;
}
.approval {
  height: 22px;
  padding: 0 8px;
  background: #fffaf0;
  border: 1px solid #ffe4bd;
  border-radius: 4px;
  font-size: 12px;
  color: #f2994a;
  display: block;
  float: left;
}
.nosubmit {
  height: 22px;
  padding: 0 8px;
  background: rgba(30, 32, 42, 0.04);
  border: 1px solid rgba(30, 32, 42, 0.15);
  border-radius: 4px;
  font-size: 12px;
  color: rgba(30, 32, 42, 0.65);
  display: block;
  float: left;
}
.lower {
  padding: 0 8px;
  height: 22px;
  /* background: #255ED7; */
  border-radius: 4px;
  color: #255ED7;
  text-align: center;
  border: 1px solid #A4C9FC;
}
.middler{
  height: 22px;
  /* background: #F69C41; */
  border-radius: 4px;
  color: #F69C41;
  padding: 0 8px;
  text-align: center;
  border: 1px solid #FFE4BD;
}

.higher {
  height: 22px;
  /* background: #F6445A; */
  border-radius: 4px;
  padding: 0 8px;
  color: #F6445A;
  text-align: center;
  border: 1px solid #FFC2C3;
}

.failer {
  background: #fff1f0;
  border: 1px solid #ffc2c3;
  border-radius: 4px;
  font-size: 12px;
  padding: 0 8px;
  height: 22px;
  color: #f34f63;
  text-align: center;
}
.group-titles {
  font-size: 16px;
  color: #292a2c;
  font-weight: 600;
  height: 46px;
  line-height: 46px;
  display: block;
}

div >>> .ant-col-24 {
  margin-top: -8px;
}

.ant-form-item:last-child {
  margin-bottom: 10px;
}
.page-info {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  border-top: 1px solid rgba(30, 32, 42, 0.15);
  display: flex;
  justify-content: flex-end;
  height: 72px;
  align-items: center;
}
.new-btn {
  margin-top: 10px;
  float: right;
}

.operation-btn {
  display: flex;
  align-items: center;
}
.operation-btn a {
  margin-right: 16px;
}
.ellipsis-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
}
.ellipsis {
  width: 16px;
  height: 16px;
}
.product-info {
  display: flex;
  align-items: center;
}

.ecn-icon {
  width: 16px;
  height: 16px;
  margin-right: 16px;
}
.maintable {
  padding: 0px 20px 0 20px;
}
</style>