<template>
  <div>

    <div class="main-info">
      <!-- <el-table ref="treeTable" v-if="!_.isEmpty(bomData)" :data="[bomData]" row-key="id" border :height="fullHeight -402" lazy  :load="load" :row-class-name="tableRowClassName" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" @cell-click="tabCellClick">
        <el-table-column prop="code" label="BOM" width="400">
          <template slot-scope="scoped">
            <i class="plm-icon-add" v-show="scoped.treeNode&&scoped.treeNode.level==0" @click="addTreeNode(scoped.row, scoped.treeNode)"></i>
            <i class="plm-icon-reduce" v-show="scoped.treeNode&&scoped.treeNode.level==1" @click="removeTreeNode(scoped)"></i>
            <i class="el-icon-refresh-left" v-show="scoped.treeNode&&scoped.treeNode.level==1" @click="resetOperate(scoped)"></i>

            <i :class="scoped.row.iconURL"></i>
            <span v-if="scoped.row.version">{{ scoped.row.name }}, {{ scoped.row.number }},{{ scoped.row.version }},{{ scoped.row.viewName }}</span>
             <span v-else>{{ scoped.row.name }}, {{ scoped.row.number }},{{ scoped.row.viewName }}</span>
           
          </template>
        </el-table-column>
        <el-table-column :prop="item.prop" :label="item.display" v-for="(item, index) in bomEditColum" sortable :key="item.id" align="center">
          <template slot-scope="scoped">
            <el-input v-if="scoped.row.oid==editRowOid && 'delete'!=scoped.row.bomOperateFlag && item.prop==editColumnProperty" size="small" v-model.trim="scoped.row.newRelationData[item.prop]" :id="scoped.row.oid + index" @blur="inputBlur(scoped.row, item.prop,scoped.$index)">
            </el-input>
            <span v-else>
              <span v-if="scoped.row.relationData[item.prop]==scoped.row.newRelationData[item.prop]">
                <span>{{ scoped.row.relationData[item.prop] }}</span>
              </span>
              <span v-else>
                <span>{{ scoped.row.relationData[item.prop] }}</span>
                <span style="color:red;">{{ scoped.row.newRelationData[item.prop] }}</span>
              </span>

            </span>
          </template>
        </el-table-column>
      </el-table> -->


       <a-table v-if="!_.isEmpty(bomData)" :pagination="pagination" :data-source="[bomData]"  rowKey="id" bordered  :columns="columns">
        </a-table> 
    </div>

    <!-- <DialogWithTable ref="dialogWithTable" @on-dbclick="addDialogSubmit" :isSelection="isSelection">
    </DialogWithTable> -->

  </div>
</template>

<script>
// import DialogWithTable from "../../../components/dialog-with-table";
import ModelFactory from "jw_apis/model-factory"
// import i18nService from "jw_services/i18n/index";
// import createBomLink from "models/bom/bomlink/batch-create";
//获取变更对象
const getTreeDataApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomServer}/bomlink/searchSecBOMMutiCondition`,
  method: "post"
});

const commonSearchPart = ModelFactory.create({
    url: `${Jw.gateway}/${partBomServer}/bomlink/commonSearch`,
  method: "post"
});
// let { lang: languageMap } = i18nService.getLanguageMap();

export default {
  name: "bom",
  props: ["bomData", "fullHeight"],
  components: {
    // DialogWithTable
  },
  data() {
    return {
      pagination:false,
      activeNumber: "",
      tablist: [],
      bomSchemeMap: {},
      isSelection: true,
      masterOid: null,
      editRowOid: "",
      editColumnProperty: "",
      columns: [
        { title: 'BOM', dataIndex: 'code', key: 'code' },
        { title: '行号', dataIndex: 'lineNumber', key: 'lineNumber', sort: true },
        { title: '位号', dataIndex: 'position', key: 'position',sort: true },
        { title: '数量', dataIndex: 'amount', key: 'amount',sort: true},
        { title: '单位', dataIndex: 'unit', key: 'unit', sort: true}
        ],
      bomEditColum: [
        {
          prop: "lineNumber",
          display: '行号',
        },
        {
          prop: "position",
          display: '位号',
        },
        {
          prop: "amount",
          display: '数量',
        },
        {
          prop: "unit",
          display: '单位',
        }
      ],
      tableColumn: [
        {
          prop: "name",
          display: "名称"
        },
        {
          prop: "number",
          display: "编码"
        },
        {
          prop: "version",
          display: "版本号"
        },
        {
          prop: "viewName",
          display: "视图"
        },
        {
          prop: "modelType",
          display:  "类型"
        }
      ],
      bomChangeMap: {},
      bomAddStorage: {},
      selectedTreeNode: {},
      selectedTreeItem: {},
      isflag: false
    };
  },
  watch: {
    // bomData(newV) {
    //   console.log(newV, "bomData");
    //   newV.relationData = {};
    //   newV.newRelationData = {};
    //   newV.hasChildren = true;
    // },
    bomData: {
      handler: function(val, oldVal) {
        console.log(val, "bomData");
        val.relationData = {};
        val.newRelationData = {};
        val.hasChildren = true;
      },
      immediate: true
    }
  },

  methods: {
    // 单击单元格事件
    tabCellClick(row, column) {
      var xuhao = null;
      if (column.property == "lineNumber") {
        xuhao = 0;
      } else if (column.property == "position") {
        xuhao = 1;
      } else if (column.property == "amount") {
        xuhao = 2;
      } else if (column.property == "unit") {
        xuhao = 3;
      }

      if (row.number == this.activeNumber) {
        return;
      }

      this.editColumnProperty = column.property;
      this.editRowOid = row.oid;

      var refval = row.oid + xuhao;
      this.$nextTick(() => {
        var obj = document.getElementById(refval);
        if (obj) {
          console.log("sss");
          obj.focus();
        }
      });
    },
    // 失去焦点初始化
    inputBlur(row, property) {
      this.editColumnProperty = "";
      this.editRowOid = "";

      if (!row.bomOperateFlag) {
        if (row.relationData[property] != row.newRelationData[property]) {
          this.$set(row, "bomOperateFlag", "update");
        }
      }
    },
    addDialogSubmit(rows) {
      if (!rows || rows.length === 0) {
        this.$warning('请至少选择一条数据');
        return;
      }
      let master = this.selectedTreeItem;
      let masterOid = master.oid;
      for (let i = 0, len = rows.length; i < len; i++) {
        if (rows[i].oid == masterOid) {
          this.$error("Part无法与自身关联,请选择其他项");
          return;
        }
      }
      if (!this.checkView(master, rows)) {
        this.$warning("待添加的part应与父part保持相同视图!");
        return;
      }

      // 现充初始标记，以及必须数据
      _.each(rows, item => {
        item.relationData = {};
        item.newRelationData = {}; // 用于展示
        item.bomOperateFlag = "new";
      });
      // 如果这个节点未展开过,则先获取其子节点
      if (this.selectedTreeNode.expanded) {
        let currentChildren = this.getChildOfNode(master);
        currentChildren = this.uniqueAdd(currentChildren, rows);
        this.refreshTreeNode(master, currentChildren);
      } else {
        let queryParam = {
          masterOid: masterOid,
          masterType: master.modelType,
          mutiConditionDTOS: [],
          relationName: "BOMLink"
        };

        // 获取子节点
        getTreeDataApi
          .execute(queryParam)
          .then(datas => {
            if (!datas) {
              datas = [];
            }
            datas = this.uniqueAdd(datas, rows);
            this.refreshTreeNode(master, datas);
          })
          .catch(err => {
            this.$error(err.msg || "子节点加载异常");
          });
      }
    },

    // 获取节点的子节点
    getChildOfNode(node) {
      let children = this.$refs.treeTable.store.states.lazyTreeNodeMap[node.id];
      console.log("children", children);
      return children == undefined ? [] : children;
    },

    // 刷新表格的树形节点添加子节点
    refreshTreeNode(curNode, rows) {
      let table = this.$refs.treeTable;
      let masterId = curNode.id;
      let lazyTreeNodeMap = table.store.states.lazyTreeNodeMap;
      this.$set(lazyTreeNodeMap, masterId, rows);
      curNode.children = rows;

      table.$emit("expand-change", curNode, true);
    },

    // 两个list的非重集合，通过oid判断
    uniqueAdd(leftLis, datas) {
      let result = [];
      let rowMap = {};
      _.each(leftLis, item => {
        rowMap[item.oid] = item;
      });
      _.each(datas, item => {
        let itemOid = item.oid;
        if (!rowMap.hasOwnProperty(itemOid)) {
          result.push(item);
          rowMap[itemOid] = item;
        }
      });
      _.each(leftLis, item => {
        result.push(item);
      });
      return result;
    },

    checkView(data, itemLis) {
      let viewName = data.viewName;
      let flag = true;
      _.each(itemLis, item => {
        if (item.viewName != viewName) {
          flag = false;
        }
      });
      return flag;
    },

    addTreeNode(data, treeNode) {
      this.isSelection = true;
      //选中树项数据
      this.selectedTreeItem = data;
      this.selectedTreeNode = treeNode;
      this.dialogWithTableShow(data, '添加');
    },

    dialogWithTableShow(data, title) {
      let search = commonSearchPart;
      let searchParam = {
        modelType: data.modelType || data.secondaryType,
        extraType: "globle",
        searchKey: ""
      };
      let masterOid = data.oid;
      let tableColumn = this.tableColumn;
      let showPatam = {
        title: title,
        search,
        searchParam,
        tableColumn,
        masterOid
      };

      this.instanceData = {};
      this.$refs.dialogWithTable.show(showPatam);
    },

    // 移除bom树节点
    removeTreeNode(scoped) {
      let row = scoped.row;
      let treeNode = scoped.treeNode;
      if (treeNode.level == 0) {
        return;
      }

      if (row.bomOperateFlag == "new") {
        // 新增项，直接删除
        this.deleteNewAddRow(row);
      } else {
        // 原有项保留,增加删除标记
        this.$set(row, "bomOperateFlag", "delete");
      }
    },
    // 删除新加的节点
    deleteNewAddRow(row) {
      let parent = this.bomData;
      let curChild = this.$refs.treeTable.store.states.lazyTreeNodeMap[
        parent.id
      ];
      let newChild = [];
      if (curChild) {
        _.each(curChild, item => {
          if (!(item.oid == row.oid)) {
            newChild.push(item);
          }
        });
      }
      this.refreshTreeNode(parent, newChild);
    },
    // 重置当前行的操作
    resetOperate(scoped) {
      let row = scoped.row;
      if (row.bomOperateFlag == "new") {
        this.deleteNewAddRow(row);
      } else {
        this.$set(row, "bomOperateFlag", null);
        row.newRelationData = JSON.parse(JSON.stringify(row.relationData));
      }
    },
    editVal(row) {
      this.$set(row, "isEdit", true);
    },
    blurBtn(row) {
      this.$set(row, "isEdit", false);
    },
    handleClick(tab, val) {
      console.log("activeNumber", this.bomSchemeMap[this.activeNumber]);
    },

    load(tree, treeNode, resolve) {
      let queryParam = {
        masterOid: tree.oid,
        masterType: tree.modelType,
        mutiConditionDTOS: [],
        relationName: "BOMLink"
      };
      getTreeDataApi
        .execute(queryParam)
        .then(datas => {
          if (!datas) {
            datas = [];
          }
          _.each(datas, item => {
            item.newRelationData = JSON.parse(
              JSON.stringify(item.relationData)
            );
          });
          if (treeNode.children) {
            let curChild = this.$refs.treeTable.store.states.lazyTreeNodeMap[
              tree.id
            ];
            datas = this.uniqueAdd(datas, curChild);
          }

          this.refreshTreeNode(tree, datas);
          treeNode.loading = false;
          treeNode.expanded = true;
          treeNode.loaded = true;
          // resolve(datas)
        })
        .catch(err => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },

    tableRowClassName(rowData) {
      let row = rowData.row;
      if (row.bomOperateFlag == "new") {
        return "bom-new-row";
      } else if (row.bomOperateFlag == "update") {
        return "bom-update-row";
      } else if (row.bomOperateFlag == "delete") {
        return "bom-delete-row";
      }
      return "";
    }
  }
};
</script>

<style scoped>
.modifyprompt {
  width: 16px;
  height: 16px;
}
.product-name {
  display: flex;
  align-items: center;
}
.parts-icon {
  width: 16px;
  height: 16px;
}
.main-info {
  margin: 10px 0 10px 0;
}

.el-table .bom-new-row {
  background: #ccedaf;
}

.el-table .bom-update-row {
  background: #ffe4bd;
}

.el-table .bom-delete-row {
  background: #ffc2c3;
}

/* .el-table .bom-update-row {
    background: rgb(140, 230, 241);
  } */
</style>