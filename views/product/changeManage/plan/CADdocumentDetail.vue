<template>
    <div>
        <div class="main-document">
            <a-row>
                <a-col :span="12">
                    <div class="beforeChange">
                        <h1>
                            <span>修改前</span>
                            <em v-if="cadDocumentData.file" @click="downLoadAttach(cadDocumentData.file.oid)"><img src="../../../../assets/image/link.png" class="link-icon">{{cadDocumentData.file.fileName}}</em>

                        </h1>
                        <div class="beforeChange-contant">
                         
                            <a-upload-dragger class="upload-demo" :action="uploadUrls" :multiple="true"  :beforeUpload="beforeUpload"  name="before" ref="before">
                                <div class="file-list">
                                    <div class="file-describe">
                                        <p class="ant-upload-drag-icon">
                                            <img src="../../../../assets/image/uploadImg.png" class="upload-img" />
                                        </p>
                                        <p class="upload-hint">将文件拖到此处，或 点击上传</p>
                                    </div>
                                    <div>
                                        <p class="file-detail">（上传文件大小不能超过20M）</p>
                                    </div>
                                </div>
                            </a-upload-dragger>
                            
                            <div class="pic-list" :style="{height:fullHeight - 749 + 'px'}">
                                <li v-for="(item, index) of cadDocumentData.newProperties.beforeChange.screenshots" :key="index">
                                    <div class="closed-icon" @click="deletePic('beforeChange',index)"><img src="../../../../assets/image/delete.png" class="delete-icon"></div>
                                    <img :src="item">
                                </li>
                            </div>
                        </div>
                    </div>
                </a-col>
                <a-col :span="12">
                    <div class="beforeChange noright">
                        <h1>
                            <span>修改后</span>
                            <em v-if="cadDocumentData.newProperties.afterChange.filename" @click="downLoadAttach(cadDocumentData.newProperties.afterChange.oid)"><img src="../../../../assets/image/link.png" class="link-icon">{{cadDocumentData.newProperties.afterChange.filename}}</em>
                            <em v-else>
                            </em>
                        </h1>
                        <div class="beforeChange-contant">
                            <a-upload-dragger class="upload-demo" :action="uploadUrls" name="after" ref="after">
                                <div class="file-list">
                                    <div class="file-describe">
                                        <p class="ant-upload-drag-icon">
                                            <img src="../../../../assets/image/uploadImg.png" class="upload-img" />
                                        </p>
                                       <p class="upload-hint">将文件拖到此处，或 点击上传</p>
                                    </div>
                                    <div>
                                        <p class="file-detail">（上传文件大小不能超过20M）</p>
                                    </div>
                                </div>
                            </a-upload-dragger>
                            <div class="pic-list" :style="{height:fullHeight - 749 + 'px'}">
                                <li v-for="(item, index) of cadDocumentData.newProperties.afterChange.screenshots" :key="index">
                                    <div class="closed-icon" @click="deletePic('afterChange',index)"><img src="../../../../assets/image/delete.png" class="delete-icon"></div>
                                    <img :src="item">
                                </li>
                            </div>
                        </div>
                    </div>
                </a-col>
            </a-row>

            <div class="schemes-info">
                <quillEditor v-model.trim="cadDocumentData.newProperties.schemes" style="height:137px" />
            </div>

        </div>
    </div>

</template>

<script>
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
import { quillEditor } from "vue-quill-editor";
// import util from "jw_common/util";
export default {
  name: "documentDetail",
  props: ["cadDocumentData", "fullHeight"],
  data() {
    return {
      uploadUrls: `${Jw.gateway}/${Jw.fileServer}/file/multiUpload-v2`,
      files: [],
      fileList: []
    };
  },

  watch: {},

  methods: {
    // downLoadAttach(oid) {
    //   util.download(
    //     `${Jw.gateway}/${Jw.fileServer}/file/downloadByOid`,
    //     { oid: oid },
    //     "get"
    //   );
    // },
    deletePic(item, index) {
      if (item == "beforeChange") {
        this.cadDocumentData.newProperties.beforeChange.screenshots.splice(index, 1);
      } else {
        this.cadDocumentData.newProperties.afterChange.screenshots.splice(index, 1);
      }
    },

    beforeUpload(param, fileList) {
      const type = 'before'
      let flag = this.validationImage(param, type);
      if (flag) {
        this.files.push(param);
        this.uploadImg(this.files, type);
      }
    },
    validationImage(file, action) {
      if (action == "before" || action == "after") {
        const isJpgOrPng =
          file.type === "image/jpeg" ||
          file.type === "image/png" ||
          file.type === "image/bmp" ||
          file.type === "image/gif" ||
          file.type === "image/tif";
        if (!isJpgOrPng) {
          this.$error('只能上传图片格式');
          return false;
        }
        const isLt2M = file.size / 1024 / 1024 < 20;
        if (!isLt2M) {
          this.error('图片大小不能超过20MB');
          return false;
        }

        if (isLt2M && isJpgOrPng) {
          return true;
        }
      } else {
        let len = file.name.split(".").length;
        var isJpgOrPng = file.name.split(".")[len - 1];
        isJpgOrPng = isJpgOrPng.toLowerCase();
        if (isJpgOrPng != "cad") {
          this.$error(this.$t('digitalPlm.change.onlyCAD'));
          return false;
        } else {
          return true;
        }
      }
    },

    uploadImg(param, action) {
      //图片上传
      if (param.length > 0) {
        let formData = new FormData();
        let xhr = new XMLHttpRequest();
        param.forEach(function(file) {
          formData.append("file", file, file.name); // 因为要上传多个文件，所以需要遍历一下才行
        });
        xhr.open(
          "POST",
          `${Jw.gateway}/${Jw.fileServer}/file/multiUpload-v2`,
          true
        );
        xhr.onload = res => {
          const response = JSON.parse(xhr.response);
          let result = response.result[0];
          let screenshots = `${Jw.gateway}/${ Jw.fileServer }/file/downloadByOid?oid=${result.oid}`;
         
          if (action == "before") {
            //上传截图前
            this.cadDocumentData.newProperties.beforeChange.screenshots.push(screenshots);
            
          } else if (action == "after") {
            //上传截图后
            this.cadDocumentData.newProperties.afterChange.screenshots.push(
              screenshots
            );
           
          } else if (action == "source-before") {
            //修改前 --上传源文件
            this.cadDocumentData.newProperties.beforeChange.filename = result.fileName;
            this.cadDocumentData.newProperties.beforeChange.oid = result.oid;
            // this.$refs.sourcebefore.clearFiles();
          } else {
            //修改后 --上传源文件
            this.cadDocumentData.newProperties.afterChange.filename = result.fileName;
            this.cadDocumentData.newProperties.afterChange.oid = result.oid;
            // this.$refs.sourceafter.clearFiles();
          }
          this.files = [];
          this.$forceUpdate();
        };
        xhr.send(formData);
      } else {
      }
    }
  },

  components: {
    quillEditor
  }
};
</script>

<style scoped>
.upload-demo >>>.ant-upload-list-item{
  display: none;
}
.main-document{
  border-bottom: 1px solid #ddd;
  box-sizing: border-box;
  overflow: hidden;
}
.beforeChange em {
  cursor: pointer;
}
.delete-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
}
.closed-icon {
  position: absolute;
  top: 0;
  right: 0;
  width: 16px;
  height: 16px;
}
.upload-demo >>> .ant-upload.ant-upload-drag .ant-upload {
  padding: 0;
}

.upload-demo >>>.ant-upload.ant-upload-drag p.ant-upload-drag-icon{
  margin-bottom: 0;
}
.link-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.ant-row {
  border: 1px solid rgba(30, 32, 42, 0.15);
}
.schemes-info {
  margin-top: 18px;
}
.pic-list {
  margin-top: 20px;
  width: 100%;
  overflow-y: auto;
}
.pic-list li {
  position: relative;
  list-style-type: none;
}
.pic-list li img {
  width: 100%;
}
.file-detail {
  font-size: 12px;
  color: rgba(30, 32, 42, 0.45);
}
.upload-hint {
  font-size: 14px;
  color: rgba(30, 32, 42, 0.65);
}
.upload-demo >>> .el-upload {
  width: 100%;
}
.upload-demo >>> .el-upload-dragger {
  height: 50px;
  width: 100%;
  display: flex;
  align-items: center;
  background: rgba(30, 32, 42, 0.04);
}
.upload-btn {
  font-style: normal;
  color: #255ed7;
}
.long-int {
  width: 552px;
}

.upload-img {
  width: 28px;
  height: 28px;
  margin-right: 12px;
}
.file-describe {
  display: flex;
  align-items: center;
}
.file-list {
  display: flex;
  align-items: center;
  padding: 0 20px;
  justify-content: space-between;
  width: 100%;
  height: 50px;
}
.beforeChange-contant {
  padding: 15px;
  overflow-y: scroll;
}
.beforeChange {
  display: flex;
  flex-direction: column;
  border-right: 1px solid rgba(30, 32, 42, 0.15);
}
.beforeChange.noright {
  border-right: none;
}
.beforeChange h1 {
  height: 40px;
  line-height: 40px;
  padding: 0 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(30, 32, 42, 0.15);
}
.beforeChange h1 span {
  font-size: 14px;
  color: rgba(30, 32, 42, 0.65);
  font-weight: normal;
}
.beforeChange h1 em {
  font-style: normal;
  font-size: 14px;
  color: #255ed7;
  font-weight: normal;
  display: flex;
  align-items: center;
}
</style>