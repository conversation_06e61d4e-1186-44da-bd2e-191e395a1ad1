<template>
    <div>
        <div class="main-document">
            <el-form
            ref="form"
            :model="form"
            size="small"
            label-width="90px"
            >
          
            <a-row>
                <a-col :span="12">
                    <div class="beforeChange">
                        <h1><span>修改前</span>
                        <em v-if="selectItem.newProperties.beforeChange.filename"><img src="../../../../assets/image/link.png" class="link-icon">{{selectItem.newProperties.beforeChange.filename}}</em>
                        </h1>
                        <div class="beforeChange-contant">
                            <a-upload
                                class="upload-demo"
                                drag
                                action="before"
                               :http-request="UploadImage"
                                >
                                <div class="file-list">
                                    <div class="file-describe">
                                        <p class="ant-upload-drag-icon">
                                        <img
                                            src="../../../../assets/image/uploadImg.png"
                                            class="upload-img"
                                        />
                                        </p>
                                        <p class="upload-hint">将文件拖到此处，或 点击上传</p>
                                    </div>
                                    <div>
                                         <p class="file-detail">（上传文件大小不能超过20M）</p>
                                    </div>
                                    </div>
                            </a-upload>
                             <div class="pic-list"  :style="{height:fullHeight - 719 + 'px'}">
                                  <li v-for="(item, index) of selectItem.newProperties.beforeChange.screenshots" :key="index">
                                      <img :src="item">
                                  </li>
                             </div>
                        </div>
                    </div>
                </a-col>
                <a-col :span="12">
                    <div class="beforeChange noright" >
                        <h1><span>修改后</span>
                        <em v-if="selectItem.newProperties.afterChange.filename"><img src="../../../../assets/image/link.png" class="link-icon">{{selectItem.newProperties.afterChange.filename}}</em>
                        </h1>
                        <div class="beforeChange-contant">
                            <a-upload
                                class="upload-demo"
                                drag
                                action="after"
                               :http-request="UploadImage"
                                >
                                <div class="file-list">
                                    <div class="file-describe">
                                        <p class="ant-upload-drag-icon">
                                        <img
                                            src="../../../../assets/image/uploadImg.png"
                                            class="upload-img"
                                        />
                                        </p>
                                         <p class="upload-hint">将文件拖到此处，或 点击上传</p>
                                    </div>
                                    <div>
                                         <p class="file-detail">（上传文件大小不能超过20M）</p>
                                    </div>
                                    </div>
                            </a-upload>
                             <div class="pic-list" :style="{height:fullHeight - 719 + 'px'}">
                                  <li v-for="(item, index) of selectItem.newProperties.afterChange.screenshots" :key="index">
                                      <img :src="item">
                                  </li>
                             </div>
                        </div>
                    </div>
                </a-col>
           </a-row>
     
     
                   <div class="schemes-info">
                      <quillEditor v-model.trim="selectItem.newProperties.schemes" :key="selectItem.oid"   style="height:147px" />
                   </div>
             </el-form>
      </div>
    </div>
    
</template>

<script>
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
import { quillEditor } from "vue-quill-editor";
export default {
    name:'documentDetail',
    props:['selectItem','fullHeight'],
    data(){
        return {
            files:[],
            fileList:[],
             form: {
                oid:'',
                schemes:'',
                beforeChange: {
                   filename:'',
                   screenshots:[]
                },
                afterChange:{
                   filename:'',
                   screenshots:[]
                }
            }
        }
    },
    watch: {
        selectItem(newV){
              console.log('newVsss', newV.oid)
              this.form.oid = newV.oid
              if(!this.selectItem.hasOwnProperty("newProperties")){
                   this.selectItem.newProperties = this.form
            }
        }
    },
    created() {
          console.log('newVddd',this.selectItem)
            this.form.oid = this.selectItem.oid
            if(!this.selectItem.hasOwnProperty("newProperties")){
                   this.selectItem.newProperties = this.form
            }
          
   },
     methods: {
         UploadImage(param){
            console.log('param', param.action)
            this.files.push(param.file)
            this.uploadImg(this.files, param.action)
         },
        uploadImg(param, action) {  //图片上传
        if (param.length > 0) {
          let formData = new FormData();
          let xhr = new XMLHttpRequest();
          param.forEach(function (file) {
            formData.append("file", file, file.name); // 因为要上传多个文件，所以需要遍历一下才行
          });
          xhr.open("POST", `${Jw.gateway}/${Jw.fileServer}/file/multiUpload-v2`, true);
          xhr.onload = (res) => {
            const response = JSON.parse(xhr.response)
            let result = response.result[0]
            let screenshots = `${Jw.gateway}/${Jw.fileServer}/file/downloadByOid?oid=${result.oid}`
            if(action=='before') {
                this.selectItem.newProperties.beforeChange.filename = result.fileName
                this.selectItem.newProperties.beforeChange.screenshots.push(screenshots) 
            }else {
                this.selectItem.newProperties.afterChange.filename = result.fileName
                this.selectItem.newProperties.afterChange.screenshots.push(screenshots) 
            }

           

             
          };
          xhr.send(formData);
        } else {
	     
        }
      },
     },
    components: {
        quillEditor
    }
}
</script>

<style scoped>
.upload-demo>>>.el-upload-list{
    display: none;
}
.link-icon{
    width: 16px;
    height: 16px;
    margin-right: 4px;
}
.el-row {
   border: 1px solid rgba(30,32,42,0.15);
}
.schemes-info {
    margin-top: 20px;
}
.pic-list {
    margin-top: 20px;
    width: 100%;
    overflow-y: auto;
}
.pic-list li img {
    width: 100%;
}
.file-detail{
    font-size: 12px;
    color: rgba(30,32,42,0.45);
}
.upload-hint {
   font-size: 14px;
   color: rgba(30,32,42,0.65);
}
.upload-demo>>>.el-upload {
    width: 100%;
}
.upload-demo>>>.el-upload-dragger{
    height: 50px;
    width: 100%;
    display: flex;
    align-items: center;
    background: rgba(30,32,42,0.04);
}
.upload-btn {
  font-style: normal;
  color: #255ed7;
}
  .long-int {
    width: 552px;
  }

.upload-img {
  width: 28px;
  height: 28px;
  margin-right: 12px;
}
.file-describe {
  display: flex;
  align-items: center;
}
.file-list {
  display: flex;
  align-items: center;
  padding: 0 20px;
  justify-content: space-between;
  width: 100%;
}
.beforeChange-contant{
    padding: 15px;
    overflow-y: scroll;
}
.beforeChange{
    display: flex;
    flex-direction: column;
    border-right: 1px solid rgba(30,32,42,0.15);
}
.beforeChange.noright {
    border-right: none;
}
.beforeChange h1{
    height: 40px;
    line-height: 40px;
    padding: 0 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(30,32,42,0.15);
}
.beforeChange h1 span {
    font-size: 14px;
    color: rgba(30,32,42,0.65);
    font-weight: normal;
}
.beforeChange h1 em {
    font-style: normal;
    font-size: 14px;
    color: #255ED7;
    font-weight: normal;
    display: flex;
    align-items: center;
}
 
</style>