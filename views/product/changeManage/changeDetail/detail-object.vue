<template>
    <div class="detail-object-wrap">
        <div class="obj-header-wrap" v-if="!isEditObj">
            <a-input-search class="obj-search-input" v-model.trim="keyword"
                :placeholder="$t('txt_enter_keyword')"
                @change="getECTarget">
            </a-input-search>
        </div>
        <a-table v-if="!isEditObj" ref="objTable" :data-source="objectData"
            :style="{height: fullHeight - 67 + 'px'}"
            :scroll="{y: fullHeight - 121 + 'px'}">
            <a-table-column
                v-for="item in objCols"
                :key="item.prop"
                :dataIndex="item.prop"
                :title="item.label"
                show-overflow-tooltip>
                <template slot-scope="scope">
                    <span v-if="item.prop==='modelType'">{{ scope.row.classification_displayName ?
                        scope.row.classification_displayName :
                        scope.row.modelType}}
                    </span>
                    <span v-else-if="item.prop==='name'">
                        <i :class="scope.row.iconURL"></i>
                        <span>{{ scope.row.name }}</span>
                    </span>
                    <span v-else-if="item.prop==='number'">
                        <span class="link-btn" @click="jumpLink(scope.row)">{{ scope.row.number }}</span>
                    </span>
                    <span v-else>{{scope.row[item.prop]}}</span>
                </template>
            </a-table-column>
        </a-table>
        <div class="obj-edit-wrap" v-if="isEditObj">
            <div class="left-wrap">
                <div class="left-header-wrap">
                    <a-tabs v-model.trim="activeTab" @tabClick="handleTabClick">
                        <a-tab-pane key="Part" tab="Part/Bom"></a-tab-pane>
                        <a-tab-pane key="CADDocument" tab="CAD"></a-tab-pane>
                        <a-tab-pane key="Document" :tab="$t('txt_document')"></a-tab-pane>
                    </a-tabs>
                    <a-input-search class="obj-search-input" v-model.trim="searchKey"
                        :placeholder="$t('txt_enter_keyword')"
                        @change="getChangeObjList('change')">
                    </a-input-search>
                </div>
                <a-table ref="multipleTable" class="edit-table" :data-source="tableData" :style="{height:tableHeight+'px'}"
                    :scroll="{y: fullHeight - 54 + 'px'}" :loading="tableLoading" row-key="oid"
                    :row-selection="{selectedRowKeys: selectedRowKeys,onChange: handleSelectionChange}"
                    @select="handleSelect">
                    <a-table-column
                        type="selection"
                        :reserve-selection="true"
                        width="40">
                    </a-table-column>
                    <a-table-column
                        v-for="item in editObjCols"
                        :key="item.prop"
                        :dataIndex="item.prop"
                        :title="item.label"
                        show-overflow-tooltip>
                        <template slot-scope="scope">
                            <span v-if="item.prop==='name'">
                                <i :class="scope.row.iconURL"></i>
                                <span>{{ scope.row.name }}</span>
                            </span>
                            <span v-else-if="item.prop==='number'">
                                <span class="link-btn" @click="jumpLink(scope.row)">{{ scope.row.number }}</span>
                            </span>
                            <span v-else>{{scope.row[item.prop]}}</span>
                        </template>
                    </a-table-column>
                </a-table>
                <a-pagination
                    class="page-wrap"
                    @showSizeChange="handleSizeChange"
                    @change="handleCurrentChange"
                    :current="page.currentPage"
                    :page-sizes="[10, 20, 50]"
                    :pageSize="page.pageSize"
                    show-size-changer
                    layout="total, prev, pager, next, sizes"
                    :total="page.total">
                </a-pagination>
            </div>
            <div class="right-wrap">
                <div class="right-header">
                    <a-checkbox :indeterminate="isIndeterminate" v-model.trim="checkAll" @change="handleCheckAllChange">已选择{{rightData.length}}个</a-checkbox>
                    <a-button type="text" @click="onDeleteMulti">{{$t('txt_delete')}}</a-button>
                </div>
                <div class="right-body">
                    <a-checkbox-group v-model.trim="checkedObjs" @change="onChangeObjs">
                        <div class="list-wrap" :style="{height:fullHeight-80+'px'}">
                            <div v-for="(item, index) of rightData"
                                :key="item.oid"
                                class="obj-item">
                                <div class="name-wrap">
                                    <a-checkbox :label="item" :key="item.oid">
                                        <i :class="item.iconURL" @click.prevent></i>
                                        <span :title="`${item.name},${item.number},${item.version},${item.viewName}`" @click.prevent="jumpLink(item)">
                                            {{item.name}},{{item.number}},{{item.version}},{{item.viewName}}
                                        </span>
                                    </a-checkbox>
                                    <a-icon type="close" @click="onDeleteSingle(index)" />
                                </div>
                            </div>
                        </div>
                    </a-checkbox-group>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
// import ModelFactory from 'jw_models/model-factory';
import jumpMixin from './jumpMixin.js';

// const fetchECTarget = ModelFactory.create({
//     url: `${Jw.gateway}/${Jw.changeServer}/ecr/findECTarget`,
//     method: 'get',
// })

// const fetchChangeObject = ModelFactory.create({
//   url: `${Jw.gateway}/${Jw.changeServer}/ecr/getCandidate/byModelType`,
//   method: 'post',
// });

export default {
    name: 'detailObject',
    mixins: [jumpMixin],
    props: [
        'fullHeight',
        'isEditObj',
    ],
    data() {
        return {
            keyword: '',
            objCols: [
                {prop: 'name', label: this.$t('txt_name')},
                {prop: 'number', label: this.$t('txt_number')},
                {prop: 'modelType', label: this.$t('txt_type')},
                {prop: 'viewName', label: this.$t('txt_view')},
                {prop: 'version', label: this.$t('txt_version')},
            ],
            objectData: [],
            containerOids: [],
            activeTab: 'Part',
            searchKey: '',
            tableHeight: this.fullHeight - 142,
            editObjCols: [
                {prop: 'name', label: this.$t('txt_name'), width: 200},
                {prop: 'containerName', label: this.$t('txt_their_product')},
                {prop: 'number', label: this.$t('txt_number')},
                {prop: 'lifecycle', label:this.$t('txt_status')},
                {prop: 'version', label: this.$t('txt_version')},
            ],
            tableData: [],
            page: {
                currentPage: 1,
                pageSize: 20,
                total: 0,
            },
            selectedRowKeys: [],
            checkAll: false,
            isIndeterminate: false,
            checkedObjs: [],
            rightData: [],
            tableLoading: true,
        };
    },
    mounted() {

    },
    methods: {
        getECTarget() {
            fetchECTarget
                .execute({
                    oid: this.$route.params.ecrOid,
                    searchkey: this.keyword,
                })
                .then((res) => {
                    this.objectData = res;
                    this.rightData = res;
                })
                .catch((err) => {
                    this.$error(err.msg)
                })
        },
        handleTabClick(tab) {
            this.activeTab = tab.name;
            this.page.currentPage = 1;
            this.getChangeObjList();
        },
        getChangeObjList(flag) {
            if (flag) {
                this.page.currentPage = 1;
            }
            this.tableLoading = true;
            fetchChangeObject
                .execute({
                    page: this.page.currentPage,
                    size: this.page.pageSize,
                    searchKey: this.searchKey,
                    modelType: this.activeTab,
                    containerOids: this.containerOids,
                })
                .then((res) => {
                    this.tableData = res.data;
                    this.page.total = res.count;
                    this.tableLoading = false;
                })
                .catch((err) => {
                    this.$error(err.msg);
                    this.tableLoading = false;
                });
        },
        handleSelectionChange(rows) {
            let oids = this.rightData.map(item => item.oid);
            rows.forEach(item => {
                if (!oids.includes(item.oid)) {
                    this.rightData.push(item);
                }
            })
        },
        handleSelect(selection, row) {
            let oids = this.rightData.map(item => item.oid);
            if (!oids.includes(row.oid)) {
                this.rightData.push(row);
            } else {
                this.rightData.forEach((ele, index) => {
                    if (ele.oid === row.oid) {
                        this.rightData.splice(index, 1);
                    }
                })
            }
        },
        onDeleteMulti() {
            if (this.checkedObjs.length > 0) {
                let arr = [];
                let checked = [];
                let oids = this.checkedObjs.map(item => item.oid);
                this.rightData.forEach(val => {
                    if (!oids.includes(val.oid)) {
                        arr.push(val);
                    } else {
                        checked.push(val)
                    }
                })
                checked.forEach(ele => {
                    this.$refs.multipleTable.toggleRowSelection(ele, false);
                })
                this.rightData = arr;
                this.isIndeterminate = false;
                this.checkAll = false;
                this.checkedObjs = [];
            }
        },
        onDeleteSingle(index) {
            let arr = this.rightData[index];
            this.rightData.splice(index, 1);
            this.$refs.multipleTable.toggleRowSelection(arr, false);
        },
        handleSizeChange(val) {
            this.page.pageSize = val;
            this.page.currentPage = 1;
            this.getChangeObjList();
        },
        handleCurrentChange(val) {
            this.page.currentPage = val;
            this.getChangeObjList();
        },
        handleCheckAllChange(val) {
            this.checkedObjs = val ? this.rightData : [];
            this.isIndeterminate = false;
        },
        onChangeObjs(val) {
            let checkedCount = val.length;
            this.checkAll = checkedCount === this.rightData.length;
            this.isIndeterminate = checkedCount > 0 && checkedCount < this.rightData.length;
        },
    },
};
</script>

<style lang="less" scoped>
.detail-object-wrap {
    padding: 5px 20px 20px;
    .obj-header-wrap {
        display: flex;
        justify-content: space-between;
    }
    .obj-search-input {
        width: 216px;
    }
    .ant-table-wrapper {
        margin-top: 10px;
        border: 1px solid #ebeef5;
        border-bottom: 0;
    }
    /deep/.ant-table th {
        background: rgba(30, 32, 42, 0.03);
    }
    .link-btn {
        color: #255ed7;
        text-decoration: underline;
        cursor: pointer;
    }
    .obj-edit-wrap {
        display: flex;
        .left-wrap {
            width: 68%;
            border: 1px solid rgba(30, 32, 42, 0.15);
            .left-header-wrap {
                display: flex;
                justify-content: space-between;
                align-items: center;
                /deep/.ant-tabs-bar {
                    margin: 0;
                }
                .obj-search-input {
                    margin-right: 16px;
                }
            }
            .edit-table {
                margin-top: 0;
                border: 0;
            }
            .page-wrap {
                padding: 15px 5px;
                text-align: right;
            }
        }
        .right-wrap {
            width: calc(~"32% - 23px");
            margin-left: 20px;
            border: 1px solid rgba(30, 32, 42, 0.15);
            .right-header{
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 52px;
                padding: 0 20px;
                color: rgba(30, 32, 42, 0.85);
                font-weight: 700;
                background: #f8f8f8;
                border-bottom: 1px solid rgba(30, 32, 42, 0.15);
                /deep/.el-checkbox__label {
                    font-weight: 700;
                }
            }
            .right-body {
                .list-wrap {
                    overflow-y: auto;
                    &::-webkit-scrollbar{
                        width: 2px;
                    }
                    .obj-item {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        height: 54px;
                        padding: 0 20px;
                        color: rgba(30, 32, 42, 0.65);
                        border-bottom: 1px solid rgba(30, 32, 42, 0.1);
                        .name-wrap {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            width: 100%;
                            font-size: 14px;
                            /deep/.el-checkbox {
                                width: 90%;
                            }
                            /deep/.el-checkbox__label {
                                width: calc(~"100% - 30px");
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                                vertical-align: bottom;
                                color: #255ed7;
                                i {
                                    color: #606266;
                                }
                            }
                            .el-icon-close {
                                font-size: 16px;
                                cursor: pointer;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
