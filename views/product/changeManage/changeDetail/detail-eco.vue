<template>
    <div class="detail-eco-wrap" :style="{height: fullHeight + 'px'}">
        <div class="eco-item-wrap">
            <div class="detail-info-title">
                <div class="title-left"></div>
                <div>ECR{{$t('txt_base_info')}}</div>
            </div>
            <a-form-model class="basic-forms" :model="basicData" ref="basicForm"
                layout="vertical">
                <a-row :gutter="10">
                    <a-col :span="12">
                        <a-form-model-item :label="$t('txt_plan_name')" prop="name" required>
                            <a-input v-model.trim="basicData.name" disabled></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-model-item :label="$t('txt_type')" prop="typeValue" required>
                            <a-input v-model.trim="basicData.typeValue" disabled></a-input>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row :gutter="10">
                    <a-col :span="12">
                        <a-form-model-item :label="$t('change_emergency')" prop="degreeEmergencyValue" required>
                            <a-input v-model.trim="basicData.degreeEmergencyValue" disabled></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-model-item :label="$t('txt_associated_problems')" prop="issueList">
                            <a-select v-model.trim="basicData.issueList" multiple value-key="label" disabled>
                                <a-select-option v-for="item in issueOpts" :key="item.label" :value="item"
                                    :label="item.issueNumber + '(' + item.value+ ')'">
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-form-model-item :label="$t('txt_reason_change')" prop="changeReason">
                    <a-textarea v-model.trim="basicData.changeReason" disabled
                        class="textarea-input" :placeholder="$t('txt_is_disable')" />
                </a-form-model-item>
                <a-form-model-item :label="$t('txt_detail_descption')" prop="description">
                    <a-textarea v-model.trim="basicData.description" disabled
                        class="textarea-input"  :placeholder="$t('txt_is_disable')" />
                </a-form-model-item>
                <a-form-model-item :label="$t('txt_attachment')" prop="appendixs">
                    <div class="appendixs-wrap">
                        <div class="appendixs-item"
                            v-for="item in basicData.appendixs"
                            :key="item.oid"
                            @click="downLoadAttach(item.oid)">
                            <svg class="jwifont appendixs-img" aria-hidden="true">
                                <use xlink:href="#jwi-document"></use>
                            </svg>
                            <span>{{ item.fileName }}</span>
                        </div>
                    </div>
                </a-form-model-item>
            </a-form-model>
        </div>
        <div class="eco-item-wrap">
            <div class="detail-info-title">
                <div class="title-left"></div>
                <div>{{$t('change_obj')}}</div>
            </div>
            <a-table :data-source="objectData"
                :header-cell-style="{background:'rgba(30, 32, 42, 0.03)'}"
                :cell-style="{background:'rgba(30, 32, 42, 0.06)'}">
                <a-table-column
                    v-for="item in objCols"
                    :key="item.prop"
                    :dataIndex="item.prop"
                    :title="item.label"
                    show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span v-if="item.prop==='modelType'">{{ scope.row.classification_displayName ?
                            scope.row.classification_displayName :
                            scope.row.modelType}}
                        </span>
                        <span v-else-if="item.prop==='name'">
                            <i :class="scope.row.iconURL"></i>
                            <span>{{ scope.row.name }}</span>
                        </span>
                        <span v-else-if="item.prop==='number'">
                            <span class="link-btn" @click="jumpLink(scope.row)">{{ scope.row.number }}</span>
                        </span>
                        <span v-else>{{scope.row[item.prop]}}</span>
                    </template>
                </a-table-column>
            </a-table>
        </div>
        <div class="eco-item-wrap">
            <div class="detail-info-title">
                <div class="title-left"></div>
                <div>{{$t('change_program')}}</div>
            </div>
            <detail-program ref="detailProgram" :fullHeight="595" :isECO="true"></detail-program>
        </div>
    </div>
</template>

<script>
// import ModelFactory from 'jw_models/model-factory';
// import util from 'jw_common/util';
import detailProgram from './detail-program';
import jumpMixin from './jumpMixin.js';


export default {
    name: 'detailECO',
    mixins: [jumpMixin],
    components: {
        detailProgram,
    },
    props: [
        'fullHeight',
    ],
    data() {
        return {
            basicData: {},
            issueOpts: [],
            objCols: [
                {prop: 'name', label: this.$t('txt_name')},
                {prop: 'number', label: this.$t('txt_plan_number')},
                {prop: 'modelType', label: this.$t('txt_type')},
                {prop: 'viewName', label: this.$t('txt_view')},
                {prop: 'version', label: this.$t('txt_version')},
                {prop: 'executorName', label: this.$t('txt_executor')},
                {prop: 'approverName', label: this.$t('txt_reviewer_role')},
            ],
            objectData: [],
        };
    },
    mounted() {
        // this.getIssueMethod('');
    },
    methods: {
        getECOOrder() {
            fetchECOOrder
                .execute({
                    ecrOid: this.$route.params.ecrOid,
                })
                .then((res) => {
                    this.basicData = res.ecr;
                    this.objectData = res.changeEntity;
                    this.$refs.detailProgram.getECTarget();
                })
                .catch((err) => {
                    this.$error(err.msg)
                })
        },
        getIssueMethod(query) {
            fetchRelatedIssues
                .execute({
                    pageNum: 1,
                    pageSize: 99,
                    keyword: query,
                })
                .then((data) => {
                    if (data.rows.length > 0) {
                        this.issueOpts = data.rows.map(item => {
                            return {
                                value: item.name,
                                label: item.oid,
                                issueName: item.name,
                                issueNumber: item.issueNumber,
                            }
                        });
                    } else {
                        this.issueOpts = [];
                    }
                })
                .catch((err) => {
                    this.$error(err.msg);
                });
        },
        downLoadAttach(oid) {
            util.download(
                `${Jw.gateway}/${Jw.fileServer}/file/downloadByOid`,
                { oid: oid },
                'get',
            )
        },
    },
};
</script>

<style lang="less" scoped>
* {
    box-sizing: border-box;
}
.detail-eco-wrap {
    padding: 5px 20px 20px;
    overflow-y: auto;
    &::-webkit-scrollbar{
        width: 1px;
    }
    .eco-item-wrap {
        margin-bottom: 25px;
        .detail-info-title {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            font-size: 16px;
            font-weight: 700;
            color: rgba(30, 32, 42, 0.85);
            .title-left {
                width: 3px;
                height: 20px;
                margin-right: 9px;
                background: #115bdf;
            }
        }
    }
    .basic-forms {
        max-width: 664px;
        /deep/.ant-form-item-label {
            padding: 0;
            line-height: 30px;
        }
        .textarea-input.ant-input {
            height: 122px;
        }
        .appendixs-wrap {
            display: flex;
            flex-wrap: wrap;
            min-height: 80px;
            padding: 10px;
            background-color: #f5f7fa;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
            .appendixs-item {
                display: flex;
                align-items: center;
                margin: 5px;
                padding: 3px 10px;
                background: #fff;
                border: 1px solid rgba(30, 32, 42, 0.15);
                border-radius: 4px;
                cursor: pointer;
                &:hover {
                    color: #255ed7;
                    text-decoration: underline;
                }
                .appendixs-img {
                    width: 32px;
                    height: 32px;
                    margin-right: 5px;
                }
            }
        }
    }
    .ant-table-wrapper{
        border: 1px solid #ebeef5;
        border-bottom: 0;
    }
    .table-name-wrap {
        display: flex;
        align-items: center;
    }
    .link-btn {
        color: #255ed7;
        text-decoration: underline;
        cursor: pointer;
    }
}
</style>
