export default {
    methods: {
        jumpLink(row) {
            Jw.jumpToDetail(row,{
                blank:true
            });
            // let params = this.$route.params;
            // let ecArr = ['ECR', 'ECO', 'ECA'];
            // let ecMap = { 'ECR':'ecrdetail','ECO':'ecodetail','ECA':'ecadetail'};
            // let type = row.inheritModelType ? row.inheritModelType : row.modelType;
            // let url = null;
            // if (type === 'Part') {
            //     url = this.$router.resolve({
            //         path: `/part-detail/${row.oid}/${row.modelType}/?from=Product`,
            //     });
            // } else if (type === 'Document' || type === 'CADDocument') {
            //     url = this.$router.resolve({
            //         path: `/detail/${row.oid}/${row.modelType}/?from=Product`,
            //     });
            // } else if (ecArr.includes(type)) {
            //     url = this.$router.resolve({
            //         path: `/changeManage/${ecMap[type]}/${type}/${row.oid}/${params.modelType}/${params.ecrOid}`,
            //     });
            // }
            // window.open(url.href, '_blank');
        },
    }
}
