<template>
    <div class="change-detail">
        <div class="back-header">
            <svg class="jwifont bind-icon-back" aria-hidden="true" @click="goBack">
                <use xlink:href="#jwi-back"></use>
            </svg>
            <div class="ecr-name" :title="rowInfo.name">{{ rowInfo.name }}</div>
        </div>
        <a-row>
            <a-col :span="showProcess?16:24">
                <div class="shadow-wrap detail-main-wrap">
                    <a-tabs v-model.trim="activeName" @tabClick="handleTabClick">
                        <a-tab-pane key="basic" tab="基本信息">
                            <detail-basic ref="detailBasic" :fullHeight="fullHeight" :isEdit="isEdit" @getInfo="getInfo"></detail-basic>
                        </a-tab-pane>
                        <a-tab-pane key="changeObj" tab="变更对象">
                            <detail-object ref="detailObject" :fullHeight="fullHeight" :isEditObj="isEditObj"></detail-object>
                        </a-tab-pane>
                        <a-tab-pane key="affectedObj" tab="受影响对象">
                            <detail-affected ref="detailAffected" :fullHeight="fullHeight"></detail-affected>
                        </a-tab-pane>
                        <a-tab-pane key="program" tab="变更方案">
                            <detail-program ref="detailProgram" :fullHeight="fullHeight" :isEditProgram="isEditProgram"></detail-program>
                        </a-tab-pane>
                        <a-tab-pane key="eco" tab="ECO" v-if="rowInfo.lifecycle==='Inwork'||rowInfo.lifecycle==='Released'">
                            <detail-eco ref="detailECO" :fullHeight="fullHeight"></detail-eco>
                        </a-tab-pane>
                        <a-tab-pane key="eca" tab="ECA" v-if="rowInfo.lifecycle==='Inwork'||rowInfo.lifecycle==='Released'">
                            <detail-eca ref="detailECA" :fullHeight="fullHeight"></detail-eca>
                        </a-tab-pane>
                    </a-tabs>
                    <div class="edit-btn-wrap" v-if="rowInfo.lifecycle==='Open'&&activeName!=='affectedObj'&&currentUser===rowInfo.creatorId">
                        <span class="edit-tip" v-if="editStatus">正在修改数据...</span>
                        <a-button type="primary" size="small" @click="onSubmit">提交</a-button>
                        <a-button v-if="!editStatus" size="small" @click="onEdit">编辑</a-button>
                        <a-button v-if="editStatus" size="small" @click="onSave">{{$t('btn_save')}}</a-button>
                        <a-button v-if="editStatus" size="small" @click="onCancel">{{$t('btn_cancel')}}</a-button>
                    </div>
                </div>
            </a-col>
            <a-col :span="showProcess?8:0">
                <div class="shadow-wrap">
                    <detail-process ref="detailProcess" :fullHeight="fullHeight" @setColumn="setColumn"></detail-process>
                </div>
            </a-col>
        </a-row>
        <a-modal title="审核" :visible.sync="visible"  append-to-body width="480px">
            <a-form-model :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px">
                <a-form-model-item label="审核人" prop="account" label-position="top">
                    <a-select v-model.trim="account" filterable placeholder="请选择" @change="getAuditList">
                        <a-select-option
                            v-for="item in accountOpts"
                            :key="item.oid"
                            :label="item.name"
                            :value="item.account">
                        </a-select-option>
                    </a-select>
                </a-form-model-item>
                <a-form-model-item>
                    <a-button class="confirm-icon" @click="onConfirm" size="small">提交</a-button>
                    <a-button size="small" @click="visible = false;">{{$t('btn_cancel')}}</a-button>
                </a-form-model-item>
            </a-form-model>
        </a-modal>
    </div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory"
// import ModelFactory from 'jw_models/model-factory';
import detailBasic from './detail-basic';
import detailObject from './detail-object';
import detailAffected from './detail-affected';
import detailProgram from './detail-program';
import detailEco from './detail-eco';
import detailEca from './detail-eca';
import detailProcess from './detail-process';

// const updateProperty = ModelFactory.create({
//     url: `${Jw.gateway}/${Jw.changeServer}/ecr/updateProperty`,
//     method: 'post',
// });

// const updateChangeObj = ModelFactory.create({
//     url: `${Jw.gateway}/${Jw.changeServer}/ecr/updateChangeObj`,
//     method: 'post',
// });

// const updateChangeScheme = ModelFactory.create({
//     url: `${Jw.gateway}/${Jw.changeServer}/ecr/updateChangeScheme`,
//     method: 'post',
// });

// const startUpWorkflow = ModelFactory.create({
//     url: `${Jw.gateway}/${Jw.changeServer}/ecWorkflow/ecr/start`,
//     method: 'post',
// });

// const fetchProcess = ModelFactory.create({
//     url: `${Jw.gateway}/${Jw.changeServer}/ecr/processDetail`,
//     method: 'get',
// });

// let searchAudit = ModelFactory.create({
//     url: `${Jw.gateway}/${Jw.accountServer}/v2/user/searchByKeywordAndPaging`,
//     method: 'get'
// })

export default {
    name: 'changeDetail',
    components: {
        detailBasic,
        detailObject,
        detailAffected,
        detailProgram,
        detailEco,
        detailEca,
        detailProcess,
    },
    data() {
        return {
            fullHeight: document.documentElement.clientHeight - 200,
            activeName: 'basic',
            showProcess: true,
            editStatus: false,
            isEdit: true,
            isEditObj: false,
            isEditProgram: false,
            visible: false,
            ruleForm:{
                searchKey: '',
                pageNum: 1,
                pageSize: 50,
                tenantId: 1,
                // tenantOid:Jw.getUser().tenantOid,
            },
            rules:{
                account:[{ required: true, message: '请选择审核人', trigger: 'blur' }]
            },
            account: '',
            accountOpts: [],
            // currentUser: Jw.getUser().oid,
            rowInfo: '',
        };
    },
    mounted() {
        window.onresize = () => {
            return (() => {
                this.fullHeight = document.documentElement.clientHeight - 200;
            })();
        };
        this.$refs.detailProcess.getProcessData();
    },
    methods: {
        getInfo(row) {
            this.rowInfo = row;
        },
        goBack() {
            this.$router.push('/changeManage/index');
        },
        handleTabClick(tab) {
            if (tab.name === 'basic') {
                this.isEdit = true;
                this.$refs.detailBasic.getECRBasic();
            } else if (tab.name === 'changeObj') {
                this.isEditObj = false;
                this.$refs.detailObject.keyword = '';
                this.$refs.detailObject.searchKey = '';
                this.$refs.detailObject.getECTarget();
            } else if (tab.name === 'affectedObj') {
                this.$refs.detailAffected.getECTarget();
            } else if (tab.name === 'program') {
                this.isEditProgram = false;
                this.$refs.detailProgram.getECTarget();
            } else if (tab.name === 'eco') {
                this.$refs.detailECO.getECOOrder();
            } else if (tab.name === 'eca') {
                this.$refs.detailECA.getECAOrder();
            }
            this.editStatus = false;
        },
        setColumn(val) {
            this.showProcess = val;
        },
        onEdit() {
            this.showProcess = false;
            this.editStatus = true;
            if (this.activeName === 'basic') {
                this.isEdit = false;
            } else if (this.activeName === 'changeObj') {
                this.isEditObj = true;
                this.$refs.detailObject.getECTarget();
                this.$refs.detailObject.getChangeObjList();
            } else if (this.activeName === 'program') {
                this.isEditProgram = true;
            }
        },
        onSubmit() {
            if (this.activeName === 'basic') {
                this.beforeUpdate('submit');
            } else if (this.activeName === 'changeObj') {
                this.updateChangeObjInfo('submit');
            } else if (this.activeName === 'program') {
                this.updateChangeSchemeInfo('submit');
            }
        },
        beforeUpdate(flag) {
            let that = this.$refs.detailBasic;
            if (that.files.length > 0) {
                let formData = new FormData();
                let xhr = new XMLHttpRequest();
                that.files.forEach(function (file) {
                    formData.append('file', file, file.name);
                });
                xhr.open('POST', `${Jw.gateway}/${Jw.fileServer}/file/multiUpload-v2`, true);
                xhr.onload = (res) => {
                    const response = JSON.parse(xhr.response);
                    that.basicData.appendixs = [...that.basicData.appendixs, ...response.result];
                    this.updateBasicInfo(flag);
                };
                xhr.send(formData);
            } else {
                this.updateBasicInfo(flag);
            }
        },
        updateBasicInfo(flag) {
            let that = this.$refs.detailBasic;
            that.$refs.basicForm.validate((valid) => {
                if (valid) {
                    that.basicData.typeValue = _.find(that.typeOpts, item=>{return item.value===that.basicData.typeCode}).label;
                    that.basicData.degreeEmergencyValue = _.find(that.emergencyOpts, item=>{return item.value===that.basicData.degreeEmergencyCode}).label;
                    updateProperty
                        .execute(
                            that.basicData,
                        )
                        .then((res) => {
                            this.editStatus = false;
                            this.isEdit = true;
                            that.files = [];
                            if (flag === 'submit') {
                                this.getProcess();
                            }
                        })
                        .catch((err) => {
                            this.$error(err.msg)
                        })
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        updateChangeObjInfo(flag) {
            let that = this.$refs.detailObject;
            let objArr = that.rightData.map(item => {
                return {
                    oid: item.oid,
                    modelType: item.modelType,
                }
            })
            updateChangeObj
                .execute({
                    ecrOid: this.$route.params.ecrOid,
                    ecTargets: objArr,

                })
                .then((res) => {
                    this.editStatus = false;
                    this.isEditObj = false;
                    this.$refs.detailObject.getECTarget();
                    if (flag === 'submit') {
                        this.getProcess();
                    }
                })
                .catch((err) => {
                    this.$error(err.msg)
                })
        },
        updateChangeSchemeInfo(flag) {
            let that = this.$refs.detailProgram;
            let obj = {};
            that.changeList.map(item => {
                if (item.isEdit) {
                    obj[item.oid] = item.changeInfo;
                }
            })
            updateChangeScheme
                .execute({
                    ecrOid: this.$route.params.ecrOid,
                    changeScheme: obj,

                })
                .then((res) => {
                    this.editStatus = false;
                    this.isEditProgram = false;
                    this.$refs.detailProgram.getECTarget();
                    if (flag === 'submit') {
                        this.getProcess();
                    }
                })
                .catch((err) => {
                    this.$error(err.msg)
                })
        },
        onSave() {
            if (this.activeName === 'basic') {
                this.beforeUpdate('save');
            } else if (this.activeName === 'changeObj') {
                this.updateChangeObjInfo('save');
            } else if (this.activeName === 'program') {
                this.updateChangeSchemeInfo('save');
            }
        },
        startUp() {
            let row = this.$route.params;
            startUpWorkflow
                .execute({
                    ecrOid: row.ecrOid,
                    changeApproval: this.account,
                })
                .then((res) => {
                    this.$router.push({
                        path: `/changeManage/index`,
                    });
                })
                .catch((err) => {
                    this.$error(err.msg);
                });
        },
        getProcess() {
            fetchProcess
                .execute({
                    ecrOid: this.$route.params.ecrOid,
                })
                .then((res) => {
                    if (res.length > 0) {
                        this.startUp();
                    } else {
                        this.visible = true;
                        this.fetchAuditList();
                    }
                })
                .catch((err) => {
                    this.$error(err.msg);
                })
        },
        fetchAuditList(){
            searchAudit
                .execute(
                    this.ruleForm
                )
                .then((res) => {
                    this.accountOpts = res.rows;
                })
                .catch(err => {
                    if (err.msg) {
                        this.$error(err.msg);
                    }
                })
        },
        getAuditList(val){
            this.account = val
        },
        onConfirm() {
            this.startUp();
            this.visible = false;
        },
        onCancel() {
            this.editStatus = false;
            if (this.activeName === 'basic') {
                let that = this.$refs.detailBasic;
                this.isEdit = true;
                that.files = [];
            } else if (this.activeName === 'changeObj') {
                this.isEditObj = false;
                this.$refs.detailObject.searchKey = '';
                this.$refs.detailObject.getECTarget();
            } else if (this.activeName === 'program') {
                this.isEditProgram = false;
                this.$refs.detailProgram.getECTarget();
            }
        },
    },
};
</script>

<style lang="less" scoped>
* {
    box-sizing: border-box;
    .jwifont {
        width: 16px;
        height: 16px;
    }
}
.shadow-wrap {
    margin: 5px;
    background: #fff;
    box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
    border-radius: 4px;
}
.change-detail {
    .back-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        padding-left: 5px;
        color: #1e202a;
        font-weight: 700;
        .bind-icon-back {
            margin-right: 5px;
            cursor: pointer;
        }
        .ecr-name {
            width: 95%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
    /deep/.ant-tabs-nav .ant-tabs-tab {
        padding: 16px;
    }
    /deep/.ant-tabs-tab {
        font-weight: 700;
    }
}
.detail-main-wrap {
    position: relative;
    .edit-btn-wrap {
        position: absolute;
        right: 20px;
        top: 11px;
        .edit-tip {
            margin-right: 14px;
            color: rgba(30, 32, 42, 0.45);
        }
    }
}
</style>
