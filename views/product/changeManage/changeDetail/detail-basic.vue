<template>
    <div class="detail-basic-wrap" :style="{height: fullHeight + 'px'}">
        <a-form-model class="basic-form" :model="basicData" ref="basicForm"
            :rules="rules" layout="vertical">
            <a-form-model-item :label="$t('txt_name')" prop="name">
                <a-input v-model.trim="basicData.name" :disabled="isEdit"
                    allow-clear :placeholder="$t('placeholder_name')"></a-input>
            </a-form-model-item>
            <a-form-model-item :label="$t('txt_type')" prop="typeCode">
                <a-select v-model.trim="basicData.typeCode" :disabled="isEdit"
                    allow-clear :placeholder="$t('msg_select')">
                    <a-select-option v-for="(item, index) in typeOpts"
                        :key="index" :label="item.label" :value="item.value">
                    </a-select-option>
                </a-select>
            </a-form-model-item>
            <a-form-model-item :label="$t('change_emergency')" prop="degreeEmergencyCode">
                <a-select v-model.trim="basicData.degreeEmergencyCode" :disabled="isEdit"
                    allow-clear :placeholder="$t('msg_select')" :popper-append-to-body="false">
                    <a-select-option v-for="(item, index) in emergencyOpts"
                        :key="index" :label="item.label" :value="item.value">
                        <div :class="['emergency-tag',item.value==='crucial'?'emergency-high':
                            item.value==='major'?'emergency-middle':'emergency-low']">{{ item.label }}</div>
                    </a-select-option>
                </a-select>
            </a-form-model-item>
            <a-form-model-item :label="$t('txt_associated_problems')" prop="issueList">
                <a-select v-model.trim="basicData.issueList" :disabled="isEdit" multiple reserve-keyword
                    filterable remote :remote-method="getIssueMethod"  value-key="label"
                    :loading="issueLoading" :placeholder="$t('msg_select')"
                    @focus="getIssueMethod('','focus')">
                    <a-select-option v-for="item in issueOpts" :key="item.label" :value="item"
                        :label="item.issueNumber + '(' + item.value+ ')'">
                    </a-select-option>
                </a-select>
            </a-form-model-item>
            <a-form-model-item :label="$t('txt_reason_change')" prop="changeReason">
                <a-textarea v-model.trim="basicData.changeReason" :disabled="isEdit"
                    class="textarea-input" allow-clear :placeholder="$t('txt_reason_change')" />
            </a-form-model-item>
            <a-form-model-item :label="$t('txt_detail_descption')" prop="description">
                <a-textarea v-model.trim="basicData.description" :disabled="isEdit"
                    class="textarea-input" allow-clear :placeholder="$t('txt_detail_descption')" />
            </a-form-model-item>
            <a-form-model-item :label="$t('txt_attachment')" prop="appendixs" v-if="isEdit">
                <div class="appendixs-wrap">
                    <div class="appendixs-item"
                        v-for="item in basicData.appendixs"
                        :key="item.oid"
                        @click="downLoadAttach(item.oid)">
                        <svg class="jwifont appendixs-img" aria-hidden="true">
                            <use xlink:href="#jwi-document"></use>
                        </svg>
                        <span>{{ item.fileName }}</span>
                    </div>
                </div>
            </a-form-model-item>
            <div class="detail-edit-appendixs" v-else>
                <div class="appendixs-upload-wrap">
                    <div>{{$t('txt_attachment')}}</div>
                    <a-upload-dragger
                        class="upload-wrap"
                        :multiple="true"
                        action="string"
                        :beforeUpload="beforeUpload"
                        :on-remove="onRemoveFile">
                        <div class="appendixs-label">
                            <span>{{$t('txt_feil_drop')}}<span class="upload-btn">{{$t('txt_click_upload')}} </span></span>
                            <div>({{$t('txt_feil_size_20')}})</div>
                        </div>
                    </a-upload-dragger>
                </div>
                <div class="appendixs-wrap">
                    <div class="appendixs-item"
                        v-for="item in basicData.appendixs"
                        :key="item.oid">
                        <i class="a-icon-delete" @click="onDeleteFile(item)"></i>
                        <span class="appendixs-name" @click="downLoadAttach(item.oid)">{{ item.fileName }}</span>
                    </div>
                </div>
            </div>
        </a-form-model>
    </div>
</template>

<script>
// import ModelFactory from 'jw_apis/model-factory';
// import util from 'jw_common/util';


export default {
    name: 'detailBasic',
    props: [
        'fullHeight',
        'isEdit',
    ],
    data() {
        return {
            basicData: {},
            typeOpts: [],
            emergencyOpts: [],
            issueOpts: [],
            issueLoading: false,
            rules: {
                name: [
                    { required: true, message: this.$t('placeholder_name'), trigger: 'change' },
                ],
                typeCode: [{ required: true, message: this.$t('msg_select'), trigger: 'change' }],
                degreeEmergencyCode: [
                    { required: true, message: this.$t('msg_select'), trigger: 'change' },
                ],
            },
            files: [],
        };
    },
    mounted() {
        // this.getECRType();
        // this.getEmergency();
        // this.getIssueMethod('');
        // this.getECRBasic();
    },
    methods: {
        getECRBasic() {
            fetchECRBasic
                .execute({
                    oid: this.$route.params.ecrOid,
                })
                .then((res) => {
                    this.basicData = res;
                    this.$emit('getInfo', res);
                })
                .catch((err) => {
                    this.$error(err.msg)
                })
        },
        getECRType() {
            fetchECRType
                .execute({
                    currPage: 1,
                    pageSize: 15,
                })
                .then((data) => {
                    this.typeOpts = data.map(item => {
                        let result = _.find(item.multiLanguageValue, val => {
                            return val.languageCode === Jw.getSelectedLanguageCode();
                        })
                        return {
                            value: item.multiLanguageValue[1].valueCode,
                            label: result.valueText,
                        }
                    });
                })
                .catch((err) => {
                    this.$error(err.msg);
                });
        },
        getEmergency() {
            fetchEmergency
                .execute({
                    currPage: 1,
                    pageSize: 15,
                })
                .then((data) => {
                    this.emergencyOpts = data.map(item => {
                        let result = _.find(item.multiLanguageValue, val => {
                            return val.languageCode === Jw.getSelectedLanguageCode();
                        })
                        return {
                            value: item.multiLanguageValue[1].valueCode,
                            label: result.valueText,
                        }
                    });
                })
                .catch((err) => {
                    this.$error(err.msg);
                });
        },
        getIssueMethod(query, flag) {
            this.issueLoading = true;
            fetchRelatedIssues
                .execute({
                    pageNum: 1,
                    pageSize: 99,
                    searchKey: query,
                    status: flag?'Open':'',
                })
                .then((data) => {
                    this.issueLoading = false;
                    if (data.rows.length > 0) {
                        this.issueOpts = data.rows.map(item => {
                            return {
                                value: item.name,
                                label: item.oid,
                                issueName: item.name,
                                issueNumber: item.issueNumber,
                            }
                        });
                    } else {
                        this.issueOpts = [];
                    }
                })
                .catch((err) => {
                    this.$error(err.msg);
                    this.issueLoading = false;
                });
        },
        downLoadAttach(oid) {
            util.download(
                `${Jw.gateway}/${Jw.fileServer}/file/downloadByOid`,
                { oid: oid },
                'get',
            )
        },
        beforeUpload(file) {
            let isLt20M = file.size / 1024 / 1024 < 20;
            if (!isLt20M) {
                this.$message({
                    message: this.$t('txt_feil_size_20'),
                    type: 'error',
                })
                return false;
            }
            this.files.push(file.file);
        },
        onRemoveFile(file, fileList) {
            this.files = fileList.map(item => {
                return item.raw;
            })
        },
        onDeleteFile(item) {
            this.basicData.appendixs.forEach((el, index) => {
                if (el.oid === item.oid) {
                    this.basicData.appendixs.splice(index, 1);
                }
            })
        },
    },
};
</script>

<style lang="less" scoped>
* {
    box-sizing: border-box;
}
.detail-basic-wrap {
    padding: 0 20px 20px 20px;
    overflow: auto;
    &::-webkit-scrollbar{
        width: 2px;
    }
    .basic-form {
        max-width: 552px;
        /deep/.ant-form-item-label {
            padding: 0;
            line-height: 30px;
        }
        .textarea-input /deep/.ant-input {
            height: 80px;
        }
        .emergency-tag {
            width: fit-content;
            line-height: 24px;
            padding: 0 8px;
            border-radius: 4px;
            color: #fff;
            &.emergency-high {
                background: #f6445a;
            }
            &.emergency-middle {
                background: #f69c41;
            }
            &.emergency-low {
                background: #255ed7;
            }
        }
        .appendixs-wrap {
            display: flex;
            flex-wrap: wrap;
            min-height: 80px;
            padding: 10px;
            background-color: #f5f7fa;
            border-radius: 4px;
            border: 1px dashed #dcdfe6;
            .appendixs-item {
                display: flex;
                align-items: center;
                margin: 5px;
                padding: 3px 10px;
                background: #fff;
                border: 1px solid rgba(30, 32, 42, 0.15);
                border-radius: 4px;
                cursor: pointer;
                span:hover {
                    color: #255ed7;
                    text-decoration: underline;
                }
                .appendixs-img {
                    width: 32px;
                    height: 32px;
                    margin-right: 5px;
                }
            }
        }
        .detail-edit-appendixs {
            .appendixs-upload-wrap {
                display: flex;
                margin: 15px 0 8px;
            }
            .upload-wrap {
                width: calc(~"100% - 42px");
                height: fit-content;
                /deep/.ant-upload.ant-upload-drag {
                    border: 0;
                    background: #fff;
                }
                /deep/.ant-upload.ant-upload-drag .ant-upload {
                    padding: 0;
                }
                .upload-btn {
                    color: #1890ff;
                }
            }
            .appendixs-label {
                display: flex;
                justify-content: space-between;
            }
            .appendixs-wrap {
                .appendixs-item {
                    background: rgba(30, 32, 42, 0.04);
                    border: 0;
                    .appendixs-name {
                        margin-left: 5px;
                        &:hover {
                            color: #255ed7;
                            text-decoration: underline;
                        }
                    }
                }
            }
        }
    }
}
</style>
