<template>
    <div class="detail-eca-wrap" :style="{height: fullHeight + 'px'}">
        <a-table :data-source="tableData" :style="{height:fullHeight-25+'px'}"
            :header-cell-style="{background:'#f1f1f1',color:'rgba(30, 32, 42, 0.85)'}">
            <a-table-column type="expand">
                <template slot-scope="scope">
                    <a-table :data="scope.row.children"
                        :header-cell-style="{background:'#f1f1f1',color:'rgba(30, 32, 42, 0.75)'}"
                        :cell-style="{background:'#e5e5e5'}">
                        <a-table-column width="65"></a-table-column>
                        <a-table-column
                            v-for="val in childCols"
                            :key="val.prop"
                            :dataIndex="val.prop"
                            :title="val.label"
                            :width="val.width"
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span v-if="val.prop==='modelType'">{{ scope.row.classification_displayName ?
                                    scope.row.classification_displayName :
                                    scope.row.modelType}}
                                </span>
                                <span v-else-if="val.prop==='name'">
                                    <i :class="scope.row.iconURL"></i>
                                    <span>{{ scope.row.name }}</span>
                                </span>
                                <span v-else-if="val.prop==='number'">
                                    <span class="link-btn" @click="jumpLink(scope.row)">{{ scope.row.number }}</span>
                                </span>
                                <span v-else>{{scope.row[val.prop]}}</span>
                            </template>
                        </a-table-column>
                    </a-table>
                </template>
            </a-table-column>
            <a-table-column
                v-for="item in tableCols"
                :key="item.prop"
                :dataIndex="item.prop"
                :title="item.label"
                :width="item.width"
                show-overflow-tooltip>
                <template slot-scope="scope">
                    <span v-if="item.prop==='name'">
                        <i :class="scope.row.iconURL"></i>
                        <span>{{ scope.row.name }}</span>
                    </span>
                    <span v-else>{{scope.row[item.prop]}}</span>
                </template>
            </a-table-column>
        </a-table>
    </div>
</template>

<script>
// import ModelFactory from 'jw_models/model-factory';
import jumpMixin from './jumpMixin.js';

// const fetchECAOrder = ModelFactory.create({
//     url: `${Jw.gateway}/${Jw.changeServer}/ecr/findECAOrder`,
//     method: 'get',
// })

export default {
    name: 'detailECA',
    mixins: [jumpMixin],
    props: [
        'fullHeight',
    ],
    data() {
        return {
            tableCols: [
                {prop: 'name', label: this.$t('txt_name'), width: 200},
                {prop: 'number', label: this.$t('txt_number')},
                {prop: 'executorName', label: this.$t('txt_change_executor')},
                {prop: 'lifecycle', label: this.$t('txt_status')},
                {prop: 'deadline', label: this.$t('txt_completion_time')},
                {prop: 'finishTime', label: this.$t('txt_actual_time')},
            ],
            tableData: [],
            childCols: [
                {prop: 'name', label: this.$t('txt_object_name'), width: 250},
                {prop: 'number', label: this.$t('txt_number')},
                {prop: 'modelType', label: this.$t('txt_type')},
                {prop: 'viewName', label: this.$t('txt_view')},
                {prop: 'version', label: this.$t('txt_version')},
            ],
            childData: [],
        };
    },
    mounted() {

    },
    methods: {
        getECAOrder() {
            fetchECAOrder
                .execute({
                    ecrOid: this.$route.params.ecrOid,
                })
                .then((res) => {
                    this.tableData = res;
                })
                .catch((err) => {
                    this.$error(err.msg)
                })
        },
    },
};
</script>

<style lang="less" scoped>
* {
    box-sizing: border-box;
}
.detail-eca-wrap {
    padding: 5px 20px 20px 20px;
    /deep/.el-table__expanded-cell[class*=cell] {
        padding: 0;
    }
    .link-btn {
        color: #255ed7;
        text-decoration: underline;
        cursor: pointer;
    }
}
</style>
