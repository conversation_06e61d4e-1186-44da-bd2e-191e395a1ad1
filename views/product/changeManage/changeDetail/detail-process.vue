<template>
    <div class="detail-process-wrap" :style="{height: fullHeight + 68 + 'px'}">
        <div class="process-title">实时流程状态</div>
        <div class="process-wrap">
            <div class="process-item" v-for="data in processData" :key="data.id">
                <div class="process-status-wrap">
                    <div v-if="data[0].endTime||data[0].maxEndTime" class="process-status-dot finish-dot">
                        <!-- <i class="el-icon-check"></i> -->
                        <a-icon type="check" />
                        <!-- <svg class="jwifont" aria-hidden="true">
                            <use xlink:href="#jwi-ellipsis-fff"></use>
                        </svg> -->
                    </div>
                    <div v-else class="process-status-dot undone-dot">
                        <i class="el-icon-more"></i>
                    </div>
                    <div class="process-status">
                        {{data[0].name}} 
                        ({{data[0].flag ?
                            data[0].flag:data[0].endTime ?
                            '完成' :
                            '待批准'}})
                    </div>
                </div>
                <div class="process-time-wrap" v-if="data.length===1">
                    <div class="process-time" :class="[data[0].endTime?'finish-time':'']">
                        <div class="process-supplement" v-if="data[0].name!=='会签'">
                            <div class="supplement-title">补充意见：</div>
                            <div class="supplement-content" :title="data[0].comment?data[0].comment.comment:'--'">
                                {{data[0].comment ?
                                    data[0].comment.comment.length > 80 ?
                                    data[0].comment.comment.substring(0, 80) + '...' :
                                    data[0].comment.comment :
                                    '--'}}
                            </div>
                        </div>
                        <div class="flex">
                            <div class="time-item">
                                <div class="time-label">开始时间：</div>
                                <div class="time-value">{{data[0].startTime}}</div>
                            </div>
                            <div class="time-item">
                                <div class="time-label">结束时间：</div>
                                <div class="time-value">{{data[0].endTime?data[0].endTime:'--'}}</div>
                            </div>
                            <div class="time-item-user">
                                <div class="process-user user-orange"
                                    :title="data[0].assignee">
                                    {{data[0].assignee.slice(0, 1).toUpperCase()}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="process-time-wrap" v-if="data.length>1">
                    <div class="process-time" :class="[data[0].endTime?'finish-time':'']">
                        <div class="flex">
                            <div class="time-item">
                                <div class="time-label">开始时间：</div>
                                <div class="time-value">{{data[0].startTime}}</div>
                            </div>
                            <div class="time-item">
                                <div class="time-label">结束时间：</div>
                                <div class="time-value">{{data[0].maxEndTime}}</div>
                            </div>
                            <div class="time-item-user">
                                <div v-if="data[0].isDetails" class="fold-btn" @click="getMoreDetails(data[0])">收起</div>
                                <template v-else>
                                    <div class="process-user"
                                        v-for="(item, index) in data.slice(0,3)"
                                        :key="item.id"
                                        :title="item.assignee"
                                        :style="{'z-index':index}"
                                        :class="[(data.slice(0,3).length-index)%3===2?'user-green'
                                            :(data.slice(0,3).length-index)%3===1?'user-orange'
                                            :'user-blue']">
                                        {{item.assignee.slice(0, 1).toUpperCase()}}
                                    </div>
                                    <div
                                        class="process-user user-more"
                                        title="查看更多"
                                        :style="{'z-index':data.length}"
                                        @click="getMoreDetails(data[0])">
                                        <i class="el-icon-more"></i>
                                    </div>
                                </template>
                            </div>
                        </div>
                        <div class="process-details-wrap" v-if="data[0].isDetails">
                            <div class="process-details-con">
                                <div class="process-details-item item-header">
                                    <div>会签人</div>
                                    <div>会签提交时间</div>
                                </div>
                                <div class="process-details-item item-body"
                                    v-for="val in data"
                                    :key="val.id">
                                    <div>{{val.assignee}}</div>
                                    <div>{{val.endTime?val.endTime:'--'}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
// import commonStore from "stores/recycle";
// import ModelFactory from 'jw_models/model-factory';

// const fetchProcessDetail = ModelFactory.create({
//     url: `${Jw.gateway}/${Jw.changeServer}/ecr/processDetail`,
//     method: 'get'
// })

export default {
    name: 'detailProcess',
    props: [
        'fullHeight',
    ],
    data() {
        return {
            processData: [],
        };
    },
    mounted() {

    },
    methods: {
        getProcessData() {
            // fetchProcessDetail
            //     .execute({
            //         ecrOid: this.$route.params.ecrOid,
            //     })
            //     .then(() => {
                    let res = [{"processDefinitionId":"ECR_Workflow:3:32559","processInstanceId":"37501","durationInMillis":34890,"activityName":"审核","persistentState":{"executionId":"37501","durationInMillis":34890,"endTime":1632819815000,"assignee":"HLL"},"activityId":"sid-3E19B0A4-90F8-4EFF-A00E-499E00682CC3","executionId":"37501","tenantId":"pdm","name":"审核","startTime":1632819780000,"assignee":"HLL","endTime":1632819815000,"id":"37509","time":1632819780000,"activityType":"userTask","taskId":"37510"},{"processDefinitionId":"ECR_Workflow:3:32559","processInstanceId":"37501","durationInMillis":86375,"activityName":"下发变更任务","persistentState":{"executionId":"37501","durationInMillis":86375,"endTime":1632819901000,"assignee":"HLL"},"activityId":"sid-C1C5EBC4-8FDF-422A-A5CE-561FBF37146A","executionId":"37501","tenantId":"pdm","name":"下发变更任务","startTime":1632819815000,"assignee":"HLL","endTime":1632819901000,"id":"37514","time":1632819815000,"activityType":"userTask","taskId":"37515"},{"processDefinitionId":"ECR_Workflow:3:32559","processInstanceId":"37501","durationInMillis":868677,"activityName":"等待ECA流程结束","persistentState":{"executionId":"37501","durationInMillis":868677,"endTime":1632820770000,"assignee":"yangyang"},"activityId":"sid-5ACD7A9A-A9ED-4AA4-A877-7C5EC32C0961","executionId":"37501","tenantId":"pdm","name":"等待ECA流程结束","startTime":1632819901000,"assignee":"yangyang","endTime":1632820770000,"id":"37552","time":1632819901000,"activityType":"userTask","taskId":"37553"},{"processDefinitionId":"ECR_Workflow:3:32559","processInstanceId":"37501","durationInMillis":21334,"activityName":"变更确认","persistentState":{"executionId":"37501","durationInMillis":21334,"endTime":1632820791000,"assignee":"HLL"},"activityId":"sid-58532180-6C6B-45C0-9C9B-FADECC41E301","executionId":"37501","tenantId":"pdm","name":"变更确认","startTime":1632820770000,"assignee":"HLL","endTime":1632820791000,"id":"37585","time":1632820770000,"activityType":"userTask","taskId":"37586"}];
                    if (res.length > 0) {
                        var result = [[]];
                        var tmpArr = [];
                        for (var i of res) {
                            if (tmpArr.length === 0 || i.name === tmpArr[tmpArr.length-1].name) {      
                                tmpArr.push(i);
                            } else {
                                tmpArr = [i];
                                result.push(tmpArr);
                            }
                            result[result.length-1] = tmpArr;
                        }
                        result.forEach((val, index) => {
                            if (val[0].name === '审核' && result[index+1] && result[index+1][0].name === '会签') {
                                val[0].flag = '提交';
                            }
                            if (val[0].name === '审核' && result[index+1] && result[index+1][0].name === '重新提交') {
                                val[0].flag = '驳回';
                            }
                            if (val[0].name === '审核' && result[index+1] && result[index+1][0].name === '下发变更任务') {
                                val[0].flag = '快速变更';
                            }

                            if (val[0].name === '会签' && result[index+1] && result[index+1][0].name === '批准') {
                                val[0].flag = '完成';
                            }

                            if (val[0].name === '批准' && result[index+1] && result[index+1][0].name === '下发变更任务') {
                                val[0].flag = '通过';
                            }
                            if (val[0].name === '批准' && result[index+1] && result[index+1][0].name === '会签') {
                                val[0].flag = '重新会签';
                            }
                            if (val[0].name === '批准' && result[index+1] && result[index+1][0].name === '重新提交') {
                                val[0].flag = '驳回';
                            }

                            if (val[0].name === '重新提交' && result[index+1] && result[index+1][0].name === '审核') {
                                val[0].flag = '提交';
                            }
                            if (val[0].name === '重新提交' && val[0].endTime && !result[index+1]) {
                                val[0].flag = '取消';
                            }

                            if (val.length > 1) {
                                val.sort(this.compare('endTime'));
                                val[0].isDetails = false;
                                let times = [];
                                let hqNotEnd = false;
                                val.forEach(ele => {
                                    if (!ele.endTime) {
                                        hqNotEnd = true;
                                    } else {
                                        times.push(ele.endTime);
                                    }
                                    // ele.endTime = ele.endTime?Jw.dayjs(ele.endTime).format('YYYY-MM-DD HH:mm:ss'):'';
                                    // ele.startTime = Jw.dayjs(ele.startTime).format('YYYY-MM-DD HH:mm:ss');
                                })
                                // val[0].maxEndTime = hqNotEnd?'--':Jw.dayjs(times.sort()[times.length - 1]).format('YYYY-MM-DD HH:mm:ss');
                            } else {
                                // val[0].endTime = val[0].endTime?Jw.dayjs(val[0].endTime).format('YYYY-MM-DD HH:mm:ss'):'';
                                // val[0].startTime = Jw.dayjs(val[0].startTime).format('YYYY-MM-DD HH:mm:ss');
                            }
                        })
                        this.processData = result;
                        this.$emit('setColumn', true);
                    } else {
                        this.$emit('setColumn', false);
                        this.processData = [];
                    }

                    // commonStore.set('processData',this.processData);
                // })
                // .catch((err) => {
                //     this.$error(err.msg);
                // })
        },
        compare(property) {
            return (a,b) => {
                let value1 = a[property];
                let value2 = b[property];
                return value1 - value2;
            }
        },
        getMoreDetails(item) {
            item.isDetails = !item.isDetails;
        },
    },
};
</script>

<style lang="less" scoped>
* {
    box-sizing: border-box;
    .jwifont {
        width: 16px;
        height: 16px;
    }
}
.detail-process-wrap {
    .process-title {
        height: 53px;
        line-height: 53px;
        padding: 0 20px;
        color: rgba(30, 32, 42, 0.85);
        font-weight: 700;
        border-bottom: 2px solid #e8e8e8;
    }
    .process-wrap {
        padding: 20px;
        height: calc(~"100% - 53px");
        overflow: auto;
        &::-webkit-scrollbar{
            width: 2px;
        }
        .process-item {
            margin-bottom: 10px;
            .process-status-wrap {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                .process-status-dot {
                    width: 24px;
                    height: 24px;
                    line-height: 24px;
                    border-radius: 50%;
                    margin-right: 8px;
                    background: #255ed7;
                    color: #fff;
                    text-align: center;
                    &.undone-dot {
                        background: #255ed7;
                    }
                    &.finish-dot {
                        background: #69c833;
                        & + .process-status {
                            color: #52c41a;
                        }
                    }
                }
                .process-status {
                    font-weight: 700;
                }
            }
            .process-time-wrap {
                margin-left: 12px;
                padding: 3px 0 5px 20px;
                border-left: 1px solid rgba(30, 32, 42, 0.15);
                .process-time {
                    width: 100%;
                    padding: 15px 21px 15px 15px;
                    background: #f0f7ff;
                    border: 1px solid #a4c9fc;
                    border-radius: 4px;
                    &.finish-time {
                        background: #f8fff0;
                        border: 1px solid #ccedaf;
                    }
                    .process-supplement {
                        padding-bottom: 15px;
                        margin-bottom: 15px;
                        border-bottom: 1px solid rgba(30,32,42,0.15);
                        .supplement-title {
                            font-size: 12px;
                            color: rgba(30,32,42,0.65);
                            line-height: 20px;
                        }
                        .supplement-content {
                            font-size: 14px;
                            font-weight: 700;
                            color: rgba(30,32,42,0.85);
                            line-height: 22px;
                        }
                    }
                    .time-item {
                        width: 40%;
                    }
                    .time-item-user {
                        width: 20%;
                        display: flex;
                        justify-content: flex-end;
                        align-items: center;
                    }
                    .time-label {
                        font-size: 12px;
                        color: rgba(1, 1, 3, 0.65);
                    }
                    .time-value {
                        color: rgba(30, 32, 42, 0.85);
                        font-weight: 700;
                    }
                    .process-user {
                        width: 24px;
                        min-width: 24px;
                        height: 24px;
                        line-height: 24px;
                        text-align: center;
                        color: #fff;
                        border-radius: 50%;
                        background: #f69c41;
                        margin-right: -6px;
                        cursor: pointer;
                        &.user-blue {
                            background: #255ed7;
                        }
                        &.user-green {
                            background: #69c833;
                        }
                        &.user-orange {
                            background: #f69c41;
                        }
                        &.user-more {
                            color: rgba(30, 32, 42, 0.65);
                            background: #f0f7ff;
                            border: 1px solid #a4c9fc;
                        }
                    }
                    .fold-btn {
                        cursor: pointer;
                    }
                    .process-details-wrap {
                        margin-top: 8px;
                        .process-details-con {
                            margin-top: 15px;
                            border-top: 1px solid rgba(30, 32, 42, 0.15);
                        }
                        .process-details-item {
                            display: flex;
                            align-items: center;
                            padding: 10px;
                            background: #fff;
                            border: 1px solid rgba(30, 32, 42, 0.15);
                            border-top: 0;
                            div {
                                width: 50%;
                            }
                            &.item-header {
                                font-weight: 700;
                                color: rgba(30, 32, 42, 0.85);
                                background: #eaf1e2;
                            }
                            &.item-body {
                                padding: 14px 10px;
                            }
                        }
                    }
                }
            }
            &:last-child {
                .process-time-wrap {
                    border-left: 0;
                }
            }
        }
    }
}
</style>
