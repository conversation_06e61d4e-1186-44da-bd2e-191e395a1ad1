<template>
    <div class="detail-program-wrap" :style="{height:fullHeight + 'px',padding:isECO?'0':'5px 20px 20px 20px'}">
        <div class="program-container">
            <div class="program-left-wrap">
                <a-tabs v-model.trim="activeTab" @tabClick="handleTabClick">
                    <a-tab-pane 
                        v-for="item in tabList"
                        :key="item.name"
                        :tab="item.label">
                        <div class="program-list" :style="{height:isECO?fullHeight-54+'px':fullHeight-79+'px'}">
                            <div v-for="val in programData"
                                :key="val.oid"
                                :class="['program-item',activeItem===val.oid?'active':'']"
                                @click="getProgramDetail(val)">
                                <div class="pro-name" :title="`${val.name},${val.number},${val.version},${val.viewName}`">
                                    <i :class="val.iconURL"></i>
                                    {{val.name}},{{val.number}},{{val.version}},{{val.viewName}}
                                </div>
                                <i class="el-icon-caret-right"></i>
                            </div>
                        </div>
                    </a-tab-pane>
                </a-tabs>
            </div>
            <div v-if="activeTab==='CADDocument'" class="program-right-wrap">
                <div class="view-wrap" v-if="!isEditProgram">
                    <div class="program-detail-title">{{ objName }}</div>
                    <div class="view-title">
                        <div>{{$t('txt_original_view')}}</div>
                        <div v-if="cadData.beforeChange&&cadData.beforeChange.filename" class="view-title-file"
                            @click="downLoadAttach(cadData.beforeChange.filesrc)">
                            <!-- <img src="../../../assets/img/link.png" class="link-icon"> -->
                            {{cadData.beforeChange.filename}}
                        </div>
                    </div>
                    <div class="img-wrap original-view" :style="{height:isECO?'220px':'390px'}">
                        <template v-if="cadData.beforeChange">
                            <img v-for="item of cadData.beforeChange.screenshots" :key="item" :src="item" alt="">
                        </template>
                    </div>
                    <div class="view-title">
                        <div>{{$t('txt_update_view')}}</div>
                        <div v-if="cadData.afterChange&&cadData.afterChange.filename" class="view-title-file"
                            @click="downLoadAttach(cadData.afterChange.filesrc)">
                            <!-- <img src="../../../assets/img/link.png" class="link-icon"> -->
                            {{cadData.afterChange.filename}}
                        </div>
                    </div>
                    <div class="img-wrap modify-view" :style="{height:isECO?'239px':'380px'}">
                        <template v-if="cadData.afterChange">
                            <img v-for="item of cadData.afterChange.screenshots" :key="item" :src="item" alt="">
                        </template>
                    </div>
                </div>
                <div class="view-wrap des-wrap" v-if="!isECO&&!isEditProgram">
                    <div class="program-detail-title des-title">{{$t('txt_change_instructions')}}</div>
                    <!-- <quillEditor v-model.trim="cadData.schemes" disabled /> -->
                </div>
                <template v-if="isEditProgram">
                    <div class="program-detail-title edit-item-title">{{ objName }}</div>
                    <div v-if="isEditProgram">
                        <CADdocumentDetail
                            v-if="objName"
                            ref="CADDocument"
                            :key="activeItem"
                            :cadDocumentData="activeChangeItem"
                            :fullHeight="fullHeight+263">
                        </CADdocumentDetail>
                        <div v-else :style="{height:fullHeight-80+'px',border:'1px solid rgba(30,32,42,0.15)'}"></div>
                    </div>
                </template>
            </div>
            <div v-if="activeTab==='Part'" class="program-right-wrap right-wrap2">
                <a-tabs v-model.trim="activePartTab">
                    <a-tab-pane key="partFieldChange" :tab="$t('txt_property')">
                        <div class="part-wrap" :style="{height:fullHeight-80+'px'}">
                            <!-- <ServiceComponentBuilder
                                v-if="activeType"
                                :layoutName="isEditProgram?'edit':'show'"
                                :instanceData="partFieldData"
                                :modelName="activeType"
                                @change="handleChangePart"
                            >
                            <div slot="view" v-if="partFieldData.viewName">
                                <div>{{ partFieldData.viewName }}</div>
                            </div>
                            </ServiceComponentBuilder> -->
                        </div>
                    </a-tab-pane>
                    <a-tab-pane key="bomChange" :tab="$t('txt_BOM')">
                        <a-table
                            v-if="!isEditProgram"
                            :data-source="bomData"
                            :height="isECO?fullHeight-55:fullHeight-80"
                            row-key="oid"
                            default-expand-all
                            :row-class-name="tableRowClassName"
                            :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
                            <a-table-column
                                v-for="item in objCols"
                                :key="item.prop"
                                :dataIndex="item.prop"
                                :title="item.label"
                                show-overflow-tooltip>
                                <template slot-scope="scope">
                                    <span v-if="item.prop==='name'">
                                        <i :class="scope.row.iconURL"></i>
                                        <span>
                                            {{scope.row.name}},{{scope.row.number}},{{scope.row.version}},{{scope.row.viewName}}
                                        </span>
                                    </span>
                                    <span v-else>
                                        <span v-if="scope.row.relationData[item.prop]==scope.row.newRelationData[item.prop]">
                                            {{ scope.row.relationData[item.prop] }}
                                        </span>
                                        <span v-else>
                                            <span class="table-old-value">{{scope.row.relationData[item.prop]}}</span>
                                            <span class="table-new-value">{{scope.row.newRelationData[item.prop]}}</span>
                                        </span>
                                    </span>
                                </template>
                            </a-table-column>
                        </a-table>
                        <template v-if="isEditProgram">
                            <!-- <bomDetail
                                ref="bomlist"
                                :bomData="editBomData"
                                :fullHeight="fullHeight">
                            </bomDetail> -->
                        </template>
                    </a-tab-pane>
                </a-tabs>
            </div>
            <div v-if="activeTab==='Document'" class="program-right-wrap">
                <div class="view-wrap">
                    <div class="program-detail-title des-title">{{ objName }}</div>
                    <div class="doc-wrap" :style="{height:isECO?fullHeight-57+'px':fullHeight-82+'px'}">
                        <!-- <ServiceComponentBuilder
                            v-if="activeType"
                            :layoutName="isEditProgram?'edit':'show'"
                            :instanceData="docData"
                            :modelName="activeType"
                            @change="handleChangeDoc"
                        > -->
                            <!-- <div slot="preview">
                                <Preview
                                    v-if="docData.file && docData.file.oid && !isEditProgram"
                                    :documentInstance="docData"
                                />
                            </div> -->
                        <!-- </ServiceComponentBuilder> -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
// import commonStore from "stores/recycle";
// import "quill/dist/quill.core.css";
// import "quill/dist/quill.snow.css";
// import "quill/dist/quill.bubble.css";
// import { quillEditor } from "vue-quill-editor";
// import ModelFactory from 'jw_models/model-factory';
// import util from 'jw_common/util';
// import ModuleView from 'jw_components/module-view';
// import ServiceComponentBuilder from "components/model-propertie/components/service-component-builder";
import CADdocumentDetail from '../plan/CADdocumentDetail';
// import bomDetail from './detail-bom';
// import Preview from 'components/doc-detail/preview';

// const fetchECTarget = ModelFactory.create({
//     url: `${Jw.gateway}/${Jw.changeServer}/ecr/findECTarget`,
//     method: 'get',
// })

// const fetchScheme = ModelFactory.create({
//     url: `${Jw.gateway}/${Jw.changeServer}/ecr/findSchemeByOid`,
//     method: 'get',
// })

// const findBeforeVersionInfoModel = ModelFactory.create({
//     //变更节点获取上一版本对比信息
//     url: `${Jw.gateway}/${Jw.changeServer}/ecr/findBeforeVersionInfo`,
//     method: 'get',
// });

export default {
    name: 'detailProgram',
    props: [
        'fullHeight',
        'isECO',
        'isEditProgram',
    ],
    components: {
        // ModuleView,
        // ServiceComponentBuilder,
        // quillEditor,
        CADdocumentDetail,
        // bomDetail,
        // Preview,
    },
    data() {
        return {
            activeTab: 'Part',
            tabList: [
                {name: 'Part', label: 'Part'},
                {name: 'CADDocument', label: 'CAD'},
                {name: 'Document', label:this.$t('txt_document') },
            ],
            listPart: [],
            listCADDocument: [],
            listDocument: [],
            programData: [],
            activeItem: -1,
            objName: '',
            activeType: '',
            activePartTab: 'partFieldChange',
            partFieldData: {},
            bomData: [],
            editBomData: [],
            cadData: {},
            docData: {},
            objCols: [
                {prop: 'name', label: this.$t('txt_name') , width: 200},
                {prop: 'lineNumber', label: this.$t('txt_line_number') },
                {prop: 'position', label:this.$t('txt_a_no')  },
                {prop: 'amount', label: this.$t('txt_num') },
                {prop: 'unit', label: this.$t('txt_unit') },
            ],
            activeChangeItem: {
                newProperties: {
                    oid: '',
                    schemes: '',
                    beforeChange: {
                        filename: '',
                        filesrc: '',
                        screenshots: [],
                    },
                    afterChange:{
                        filename: '',
                        filesrc: '',
                        screenshots: [],
                    }
                },
            },
            changeList: [],
        };
    },
    created() {
      
      
    
    },
    methods: {
        getECTarget() {
            this.activeTab = 'Part';
            this.listPart = [];
            this.listCADDocument = [];
            this.listDocument = [];
            this.programData = [];
            fetchECTarget
                .execute({
                    oid: this.$route.params.ecrOid,
                })
                .then((res) => {
                    if (res.length > 0) {
                        res.forEach(item => {
                            item.isActive = false;
                            item.isEdit = false;
                            this.tabList.forEach(val => {
                                if (item.inheritModelType || item.modelType) {
                                    if (item.inheritModelType === val.name || item.modelType === val.name) {
                                        this['list'+val.name].push(item);
                                    }
                                }
                            })
                        })
                        this.changeList = res;
                        this.programData = this.listPart;
                        this.handleActive();
                    }
                })
                .catch((err) => {
                    this.$error(err.msg)
                })
        },
        handleActive() {
            if (this.programData.length > 0) {
                this.objName = this.programData[0].name;
                this.activeItem = this.programData[0].oid;
                this.activeType = this.programData[0].modelType;
                if (!this.programData[0].isEdit) {
                    this.getScheme(this.programData[0]);
                    this.programData[0].isEdit = true;
                } else {
                    this.partFieldData = this.programData[0].changeInfo.partFieldChange;
                    this.bomData = [this.programData[0].changeInfo.bomChange];
                    this.editBomData = [this.programData[0].changeInfo.bomChange];
                    if (this.programData[0].changeInfo.cadChange && this.programData[0].changeInfo.cadChange.newProperties) {
                        this.cadData = this.programData[0].changeInfo.cadChange.newProperties;
                        this.activeChangeItem = this.programData[0].changeInfo.cadChange;
                    }
                    this.docData = this.programData[0].changeInfo.docChange;
                }
            } else {
                this.objName = '';
                this.activeItem = -1;
                this.activeType = '';
                this.bomData = [];
                this.editBomData = [];
            }
        },
        handleTabClick(tab) {
            this.programData = this['list'+tab.name];
            this.handleActive();
            if (tab.name === 'Part') {
                this.activePartTab = 'partFieldChange';
            }
        },
        getProgramDetail(row) {
            row.isActive = true;
            this.activeItem = row.oid;
            this.objName = row.name;
            this.activeType = row.modelType;
            if (!row.isEdit) {
                this.getScheme(row);
                row.isEdit = true;
            } else {
                this.partFieldData = row.changeInfo.partFieldChange;
                this.bomData = row.changeInfo.bomChange?[row.changeInfo.bomChange]:[];
                this.editBomData = row.changeInfo.bomChange?[row.changeInfo.bomChange]:[];
                if (row.changeInfo.cadChange && row.changeInfo.cadChange.newProperties) {
                    this.cadData = row.changeInfo.cadChange.newProperties;
                    this.activeChangeItem = row.changeInfo.cadChange;
                }
                this.activeChangeItem = row.changeInfo.cadChange;
                this.docData = row.changeInfo.docChange;
            }
        },
        getScheme(row) {
            let api = fetchScheme;
            // todo ECA子流程 获取 变更确认节点
            let processDataStore = commonStore.get('processData');
            let processNodeName=null
            if (processDataStore.length) {
                let processNode = processDataStore.pop()[0];
                 processNodeName = processNode.name;
                // if (processNodeName === '变更确认') {
                //     api = findBeforeVersionInfoModel;
                // }
            }
            
            findBeforeVersionInfoModel
                .execute({
                    ecrOid: this.$route.params.ecrOid,
                    changeEntityOid: row.oid,
                    modelType: row.modelType,
                })
                .then((res) => {
                    row.changeInfo = {};
                    if (this.activeTab === 'Part') {
                        // if (res.partFieldChange) {
                        //     this.partFieldData = res.partFieldChange;
                        // } else {
                        //     this.partFieldData = JSON.parse(JSON.stringify(row));
                        // }
                        if (processNodeName == '变更确认') {
                            this.partFieldData = JSON.parse(JSON.stringify(row));
                            this.partFieldData.beforeData = res.beforeData;
                        } else {
                            this.partFieldData = res.partFieldChange||JSON.parse(JSON.stringify(row));
                            this.partFieldData.beforeData = res.beforeData;
                        }
                        
                        if (res.bomChange) {
                            delete res.bomChange.hasChildren;
                            if (!res.bomChange.relationData) res.bomChange.relationData = {};
                            if (!res.bomChange.newRelationData) res.bomChange.newRelationData = {};
                            this.bomData = [res.bomChange];
                            this.editBomData = [res.bomChange];
                        } else {
                            this.bomData = [JSON.parse(JSON.stringify(row))];
                            this.editBomData = [JSON.parse(JSON.stringify(row))];
                            this.bomData[0].relationData = {};
                            this.bomData[0].newRelationData = {};
                            this.bomData[0].children = [];
                            this.bomData[0].hasChildren = [];
                            this.editBomData[0].relationData = {};
                            this.editBomData[0].newRelationData = {};
                            this.editBomData[0].children = [];
                            this.editBomData[0].hasChildren = [];
                        }
                        row.changeInfo.partFieldChange = this.partFieldData;
                        row.changeInfo.bomChange = this.bomData[0];
                    }
                    if (this.activeTab === 'CADDocument') {
                        if (res.cadChange && res.cadChange.newProperties) {
                            this.cadData = res.cadChange.newProperties;
                            this.activeChangeItem = res.cadChange;
                        } else {
                            this.cadData = JSON.parse(JSON.stringify(row));
                            this.activeChangeItem = JSON.parse(JSON.stringify(row));
                            this.activeChangeItem.newProperties = {
                                oid: '',
                                schemes: '',
                                beforeChange: {
                                    filename: '',
                                    filesrc: '',
                                    screenshots: [],
                                },
                                afterChange:{
                                    filename: '',
                                    filesrc: '',
                                    screenshots: [],
                                }
                            }
                        }
                        row.changeInfo.cadChange = this.activeChangeItem;
                    }
                    if (this.activeTab === 'Document') {
                        if(processNodeName == '变更确认'){
                            this.docData = JSON.parse(JSON.stringify(row));
                            this.docData.beforeData = res.beforeData
                        }else{
                            this.docData = res.docChange||JSON.parse(JSON.stringify(row));
                            this.docData.beforeData = res.beforeData;
                        }
                        row.changeInfo.docChange = this.docData;
                    }
                })
                .catch((err) => {
                    this.$error(err.msg)
                })
        },
        handleChangePart(val) {
            Object.assign(this.partFieldData, val);
        },
        handleChangeDoc(val) {
            Object.assign(this.docData, val);
        },
        tableRowClassName({row, rowIndex}) {
            if (row.bomOperateFlag === 'new') {
                return 'bom-new-row';
            } else if (row.bomOperateFlag === 'update') {
                return 'bom-update-row';
            } else if (row.bomOperateFlag === 'delete') {
                return 'bom-delete-row';
            }
            return '';
        },
        downLoadAttach(oid) {
            util.download(
                `${Jw.gateway}/${Jw.fileServer}/file/downloadByOid`,
                { oid: oid },
                'get',
            )
        },
    },
};
</script>

<style lang="less" scoped>
* {
    box-sizing: border-box;
}
.detail-program-wrap {
    .program-container {
        height: 100%;
        display: flex;
    }
    /deep/.ant-tabs-bar {
        margin-bottom: 0;
    }
    .program-left-wrap {
        width: 32%;
        border: 1px solid rgba(30, 32, 42, 0.15);
        .program-list {
            overflow-y: auto;
            &::-webkit-scrollbar{
                width: 1px;
            }
            .program-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 54px;
                padding: 0 16px;
                color: rgba(30, 32, 42, 0.65);
                border-bottom: 1px solid rgba(30, 32, 42, 0.05);
                cursor: pointer;
                &.active {
                    background: rgba(30, 32, 42, 0.06);
                    font-weight: 700;
                }
                .pro-name {
                    width: 80%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
    }
    .program-right-wrap {
        width: calc(~"68% - 20px");
        height: 100%;
        margin-left: 20px;
        overflow-y: auto;
        &::-webkit-scrollbar{
            width: 1px;
        }
        &.right-wrap2 {
            border: 1px solid rgba(30, 32, 42, 0.15);
            .part-wrap {
                padding: 20px;
                overflow-y: auto;
                &::-webkit-scrollbar{
                    width: 1px;
                }
                .part-form {
                    max-width: 552px;
                    .part-form-title {
                        margin-bottom: 10px;
                        font-size: 16px;
                        font-weight: 700;
                        color: rgba(30, 32, 42, 0.85);
                    }
                    .el-form-item {
                        margin-bottom: 10px;
                    }
                    /deep/.el-form-item__label {
                        padding: 0;
                        line-height: 34px;
                    }
                    /deep/.el-textarea__inner {
                        height: 80px;
                    }
                }
            }
        }
        .view-wrap {
            border: 1px solid rgba(30, 32, 42, 0.15);
            &.des-wrap {
                margin-top: 16px;
            }
            /deep/.ql-toolbar.ql-snow {
                border: 0;
            }
            /deep/.ql-toolbar.ql-snow + .ql-container.ql-snow {
                border: 0;
                border-top: 1px solid rgba(30, 32, 42, 0.15);
            }
            /deep/.ql-container {
                height: 200px;
            }
            .view-title {
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 40px;
                padding: 0 18px;
                color: rgba(30, 32, 42, 0.65);
                font-weight: 700;
                border-top: 1px solid rgba(30, 32, 42, 0.15);
                border-bottom: 1px solid rgba(30, 32, 42, 0.15);
                .view-title-file {
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
            .img-wrap {
                padding: 15px 18px;
                img {
                    width: 100%;
                }
            }
            .original-view {
                overflow-y: auto;
            }
            .modify-view {
                overflow-y: auto;
            }
        }
        .program-detail-title {
            height: 54px;
            line-height: 54px;
            padding: 0 18px;
            font-weight: 700;
            &.des-title {
                border-bottom: 1px solid rgba(30, 32, 42, 0.15);
            }
        }
        .program-detail-title.edit-item-title {
            border: 1px solid rgba(30, 32, 42, 0.15);
            border-bottom: 0;
        }
        .doc-wrap {
            padding: 20px;
            overflow-y: auto;
            &::-webkit-scrollbar{
                width: 1px;
            }
        }
        .table-old-value {
            text-decoration: line-through;
            margin-right: 10px;
            color: #F69C41;
        }
        .table-new-value {
            color: rgba(30,32,42,0.85);
            font-weight: 700;
        }
        .bom-new-row {
            background: #9ce08b;
        }
        .bom-delete-row {
            background: #e26d6d;
        }
        .bom-update-row {
            background: rgb(140, 230, 241);
        }
        
    }
}
</style>
