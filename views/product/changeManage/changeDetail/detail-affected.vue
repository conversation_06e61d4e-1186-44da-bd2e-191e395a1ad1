<template>
    <div class="detail-affect-wrap" :style="{height:fullHeight + 'px'}">
        <div class="affect-container">
            <div class="affect-left-wrap">
                <a-tabs v-model.trim="activeLeft">
                    <a-tab-pane key="changeObj" :tab="$t('change_obj')">
                        <div class="affect-list" :style="{height:fullHeight-79+'px'}">
                            <div v-for="item in objData"
                                :key="item.oid"
                                :class="['affect-item',activeItem===item.oid?'active':'']"
                                @click="getObjProduct(item)">
                                <i :class="item.iconURL"></i>
                                <span class="pro-name" :title="`${item.name},${item.number},${item.version},${item.viewName}`">
                                    {{item.cname || item.name}},{{item.number}},{{item.version}},{{item.viewName}}
                                </span>
                            </div>
                        </div>
                    </a-tab-pane>
                </a-tabs>
            </div>
            <div class="affect-right-wrap">
                <a-tabs v-model.trim="activeRight">
                    <a-tab-pane key="product" :tab="$t('txt_product')">
                        <div :style="{height:fullHeight-79+'px'}">
                            <a-table
                                ref="treeTable"
                                :data-source="productData"
                                :style="{height:fullHeight-79+'px'}"
                                row-key="oid"
                                :loading="tableLoading"
                                @expandedRowsChange="expandChange"
                                :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
                                <a-table-column
                                    v-for="item in objCols"
                                    :key="item.prop"
                                    :dataIndex="item.prop"
                                    :title="item.label"
                                    :width="item.width?nameWidth:0"
                                    show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        <span v-if="item.prop==='modelType'">{{ scope.row.classification_displayName ?
                                            scope.row.classification_displayName :
                                            scope.row.modelType}}
                                        </span>
                                        <span v-else-if="item.prop==='name'">
                                            <i :class="scope.row.iconURL"></i>
                                            <span>{{ scope.row.name }}</span>
                                        </span>
                                        <span v-else-if="item.prop==='relationType'">
                                            <span :class="['relation-tag',scope.row.relationType===$t('txt_user')?'red-tag':
                                                scope.row.relationType===$t('txt_description')?'blue-tag':
                                                scope.row.relationType===$t('txt_replacement')?'orange-tag':'']">{{ scope.row.relationType }}</span>
                                        </span>
                                        <span v-else-if="item.prop==='number'">
                                            <span class="link-btn" @click="jumpLink(scope.row)">{{ scope.row.number }}</span>
                                        </span>
                                        <span v-else>{{scope.row[item.prop]}}</span>
                                    </template>
                                </a-table-column>
                            </a-table>
                        </div>
                    </a-tab-pane>
                </a-tabs>
            </div>
        </div>
    </div>
</template>

<script>
// import ModelFactory from 'jw_models/model-factory';
import jumpMixin from './jumpMixin.js';

// const fetchECTarget = ModelFactory.create({
//     url: `${Jw.gateway}/${Jw.changeServer}/ecr/findECTarget`,
//     method: 'get',
// })

// const fetchEffect = ModelFactory.create({
//     url: `${Jw.gateway}/${Jw.changeServer}/ecr/getEffect`,
//     method: 'get',
// })

export default {
    name: 'detailAffected',
    mixins: [jumpMixin],
    props: [
        'fullHeight',
    ],
    data() {
        return {
            activeLeft: 'changeObj',
            activeRight: 'product',
            objData: [],
            activeItem: -1,
            nameWidth: 200,
            objCols: [
                {prop: 'name', label:this.$t('txt_name') , width: true},
                {prop: 'relationType', label: this.$t('txt_relation')},
                {prop: 'number', label: this.$t('txt_plan_number')},
                {prop: 'modelType', label: this.$t('txt_type')},
                {prop: 'version', label:this.$t('txt_version') },
                {prop: 'viewName', label: this.$t('txt_view')},
                {prop: 'lifecycle', label: this.$t('txt_plan_lifecycle')},
            ],
            productData: [],
            tableLoading: true,
        };
    },
    mounted() {
        
    },
    methods: {
        getECTarget() {
            fetchECTarget
                .execute({
                    oid: this.$route.params.ecrOid,
                })
                .then((res) => {
                    this.objData = res;
                    if (this.objData.length > 0) {
                        this.getObjProduct(this.objData[0]);
                    } else {
                        this.tableLoading = false;
                    }
                })
                .catch((err) => {
                    this.$error(err.msg)
                })
        },
        getObjProduct(item) {
            this.activeItem = item.oid;
            this.tableLoading = true;
            fetchEffect
                .execute({
                    oid: item.oid,
                    modelType: item.modelType,
                })
                .then((res) => {
                    this.productData = res;
                    this.tableLoading = false;
                })
                .catch((err) => {
                    this.$error(err.msg);
                    this.tableLoading = false;
                })
        },
        expandChange(row, expanded) {
            if (expanded) {
                this.nameWidth += 30;
            } else {
                this.nameWidth -= 30;
            }
        },
    },
};
</script>

<style lang="less" scoped>
* {
    box-sizing: border-box;
}
.detail-affect-wrap {
    padding: 5px 20px 20px 20px;
    .affect-container {
        height: 100%;
        display: flex;
    }
    /deep/.ant-tabs-bar {
        margin-bottom: 0;
    }
    /deep/.ant-tabs-tab-active {
        color: rgba(30, 32, 42, 0.85);
        font-weight: 700;
    }
    .affect-left-wrap {
        width: 32%;
        border: 1px solid rgba(30, 32, 42, 0.15);
        .affect-list {
            overflow-y: auto;
            &::-webkit-scrollbar{
                width: 1px;
            }
            .affect-item {
                display: flex;
                align-items: center;
                height: 54px;
                padding: 0 16px;
                color: rgba(30, 32, 42, 0.65);
                border-bottom: 1px solid rgba(30, 32, 42, 0.05);
                cursor: pointer;
                &.active {
                    background: rgba(30, 32, 42, 0.06);
                    font-weight: 700;
                }
                .pro-name {
                    width: 100%;
                    margin-left: 8px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
    }
    .affect-right-wrap {
        width: calc(~"68% - 20px");
        height: 100%;
        margin-left: 20px;
        border: 1px solid rgba(30, 32, 42, 0.15);
        /deep/.el-table .cell {
            line-height: 29px;
        }
        .relation-tag {
            padding: 4px 8px;
            border-radius: 4px;
            &.red-tag {
                color: #f34f63;
                background: #fff1f0;
                border: 1px solid #ffc2c3;
            }
            &.blue-tag {
                color: #255ed7;
                background: #f0f7ff;
                border: 1px solid #a4c9fc;
            }
            &.orange-tag {
                color: #f2994a;
                background: #fffaf0;
                border: 1px solid #ffe4bd;
            }
        }
        .link-btn {
            color: #255ed7;
            text-decoration: underline;
            cursor: pointer;
        }
    }
}
</style>
