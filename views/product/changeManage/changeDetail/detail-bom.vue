<template>
    <div class="bom-edit-wrap">
        <el-table ref="treeTable" :data="bomData" row-key="id" border :height="fullHeight - 80"
            :row-class-name="tableRowClassName" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            lazy :load="load" @cell-click="tabCellClick">
            <el-table-column prop="code" label="BOM" min-width="400" >
                <template slot-scope="scope">
                    <i class="plm-icon-add" v-show="scope.treeNode&&scope.treeNode.level===0" @click="addTreeNode(scope.row, scope.treeNode)"></i>
                    <i class="plm-icon-reduce" v-show="scope.treeNode&&scope.treeNode.level===1" @click="removeTreeNode(scope)"></i>
                    <i class="el-icon-refresh-left" v-show="scope.treeNode&&scope.treeNode.level===1" @click="resetOperate(scope.row)"></i>
                    <i :class="scope.row.iconURL"></i>
                    <span>{{ scope.row.number }},{{ scope.row.name }}</span>
                </template>
            </el-table-column>
            <el-table-column v-for="item in bomEditColum"
                :prop="item.prop" :label="item.display"
                :key="item.id" align="center">
                <template slot-scope="scope">
                    <el-input v-if="scope.row.bomOperateFlag!=='delete'&&scope.row.oid==editRowOid&&item.prop===editColumnProperty"
                        size="small" v-model.trim="scope.row.newRelationData[item.prop]"
                        :id="scope.row.oid" @blur="inputBlur(scope.row, item.prop)">
                    </el-input>
                    <span v-else>
                        <span v-if="scope.row.relationData[item.prop]===scope.row.newRelationData[item.prop]">
                            <span>{{ scope.row.relationData[item.prop] }}</span>
                        </span>
                        <span v-else>
                            <s>{{ scope.row.relationData[item.prop] }}</s>
                            <span style="color:red;">{{ scope.row.newRelationData[item.prop] }}</span>
                        </span>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <DialogWithTable
            ref="dialogWithTable"
            @on-dbclick="addDialogSubmit"
            :isSelection="isSelection">
        </DialogWithTable>
    </div>
</template>

<script>
import DialogWithTable from "../../../components/dialog-with-table";
import ModelFactory from "jw_models/model-factory";

//获取变更对象
const getTreeDataApi = ModelFactory.create({
url: `${Jw.gateway}/${Jw.partBomServer}/bomlink/searchSecBOMMutiCondition`,
method: "post",
});

const commonSearchPart = ModelFactory.create({
url: `${Jw.gateway}/${Jw.partBomServer}/bomlink/commonSearch`,
method: "post",
});



export default {
    name: 'detail-bom',
    props: [
        'bomData',
        'fullHeight'
    ],
    components: {
        DialogWithTable
    },
    data() {
        return {
            activeNumber: '',
            isSelection: false,
            editRowOid: -1,
            editColumnProperty: '',
            bomEditColum: [ {
                    prop: 'lineNumber',
                    display: this.$t('digital.common.linenumber'),
                }, {
                    prop: 'position',
                    display: this.$t('pdm.common.position'),
                }, {
                    prop: 'amount',
                    display: this.$t('digital.common.quantity'),
                }, {
                    prop: 'unit',
                    display: this.$t('digitalPlm.change.unit'),
                }
            ],
            tableColumn: [
                {
                    prop: 'name',
                    display: this.$t('txt_name'),
                },
                {
                    prop: 'number',
                    display: this.$t('txt_number') ,
                },
                {
                    prop: 'version',
                    display: this.$t('txt_version_number') ,
                },
                {
                    prop: 'viewName',
                    display: this.$t('txt_view'),
                },
                {
                    prop: 'modelType',
                    display: this.$t('txt_type') ,
                },
            ],
            selectedTreeNode: {},
            selectedTreeItem: {},
            children: [],
        };
    },
    watch: {
        bomData(newV) {
            if (newV.length > 0) {
                this.activeNumber = this.bomData[0].number;
                this.bomData[0].hasChildren = true;
            }
        },
    },
    mounted() {
        if (this.bomData.length > 0) {
            this.activeNumber = this.bomData[0].number;
            this.bomData[0].hasChildren = true;
        }
    },
    methods: {
        load(tree, treeNode, resolve) {
            setTimeout(() => {
                if (tree.children && tree.children.length > 0) {
                    resolve(tree.children);
                } else {
                    let queryParam = {
                        masterOid: tree.oid,
                        masterType: tree.modelType,
                        mutiConditionDTOS: [],
                        relationName: 'BOMLink',
                    };
                    getTreeDataApi
                        .execute(queryParam)
                        .then(datas => {
                            if (!datas) {
                                datas = [];
                            }
                            _.each(datas, item => {
                                item.newRelationData = JSON.parse(JSON.stringify(item.relationData));
                            });
                            if (treeNode.children) {
                                let curChild = this.$refs.treeTable.store.states.lazyTreeNodeMap[tree.id];
                                datas = this.uniqueAdd(datas, curChild);
                            }
                            this.refreshTreeNode(tree, datas);
                            treeNode.loading = false;
                            treeNode.expanded = true;
                            treeNode.loaded = true;
                        })
                        .catch(err => {
                            if (err.msg) {
                                this.$error(err.msg);
                            }
                        });
                }
            }, 500)
        },
        // 单击单元格事件
        tabCellClick(row, column) {
            if (row.number === this.activeNumber) {
                return;
            }
            this.editColumnProperty = column.property;
            this.editRowOid = row.oid;
            let refval = row.oid;
            this.$nextTick(() => {
                let obj = document.getElementById(refval);
                if (obj) {
                    obj.focus();
                }
            })
        },
        // 失去焦点初始化
        inputBlur(row, property) {
            this.editColumnProperty = '';
            this.editRowOid = -1;
            if (!row.bomOperateFlag) {
                if (row.relationData[property] !== row.newRelationData[property]) {
                    this.$set(row, 'bomOperateFlag', 'update');
                }
            }
        },
        addDialogSubmit(rows) {
            if (!rows || rows.length === 0) {
                this.$warning(this.$t('digitalPlm.common.selectData'));
                return;
            }
            let master = this.selectedTreeItem;
            let masterOid = master.oid;
            for (let i = 0, len = rows.length; i < len; i++) {
                if (rows[i].oid === masterOid) {
                    this.$error(this.$t('txt_part_not_itself'));
                    return;
                }
            }
            if (!this.checkView(master, rows)) {
                this.$warning(this.$t('txt_add_part_view'));
                return;
            }
            // 现充初始标记，以及必须数据
            _.each(rows, item => {         
                item.relationData = {};
                item.newRelationData = {};
                item.bomOperateFlag = 'new';
            })
            if (this.selectedTreeNode.expanded) {
                let currentChildren = this.getChildOfNode(master);
                currentChildren = this.uniqueAdd(currentChildren, rows);
                this.refreshTreeNode(master, currentChildren);
            } else {
                let datas = this.uniqueAdd(this.bomData[0].children, rows);
                this.refreshTreeNode(master, datas);
            }
        },
        // 获取节点的子节点
        getChildOfNode(node) {
            let children = this.$refs.treeTable.store.states.lazyTreeNodeMap[node.id];
            return children == undefined ? [] : children;
        },
        // 刷新表格的树形节点添加子节点
        refreshTreeNode(curNode, rows) {
            let table = this.$refs.treeTable;
            let masterId = curNode.id;
            let lazyTreeNodeMap = table.store.states.lazyTreeNodeMap;
            this.$set(lazyTreeNodeMap, masterId, rows);
            curNode.children = rows;
            table.$emit('expand-change', curNode, true);
        },
        // 两个list的非重集合，通过oid判断
        uniqueAdd(leftLis, datas) {
            let result = [];
            let rowMap = {};
            _.each(leftLis, item => {
                rowMap[item.oid] = item;
            })
            _.each(datas, item => {
                let itemOid = item.oid;
                if (!rowMap.hasOwnProperty(itemOid)) {
                    result.push(item);
                    rowMap[itemOid] = item;
                }
            })
            _.each(leftLis, item => {
                result.push(item);
            })
            return result;
        },
        checkView(data, itemLis) {
            let viewName = data.viewName;
            let flag = true;
            _.each(itemLis, (item) => {
                if (item.viewName !== viewName) {
                    flag = false;
                }
            });
            return flag;
        },
        addTreeNode(data, treeNode) {
            this.isSelection = true;
            this.selectedTreeItem = data;
            this.selectedTreeNode = treeNode;
            this.dialogWithTableShow(data, this.$t('PDM.common.add'));
        },
        dialogWithTableShow(data, title) {
            let search = commonSearchPart;
            let searchParam = {
                modelType: data.modelType || data.secondaryType,
                extraType: 'globle',
                searchKey: '',
            };
            let masterOid = data.oid;
            let tableColumn = this.tableColumn;
            let showParams = {
                title: title,
                search,
                searchParam,
                tableColumn,
                masterOid,
            };
            this.$refs.dialogWithTable.show(showParams);
        },
        // 移除bom树节点
        removeTreeNode(scope) {
            let row = scope.row;
            let treeNode = scope.treeNode;
            if (treeNode.level === 0) {
                return;
            }
            if (row.bomOperateFlag === 'new') {
                // 新增项，直接删除
                this.deleteNewAddRow(row);
            } else { // 原有项保留,增加删除标记
                this.$set(row, 'bomOperateFlag', 'delete');
            }
        },
        // 删除新加的节点
        deleteNewAddRow(row) {
            let parent = this.bomData[0];
            let curChild = this.$refs.treeTable.store.states.lazyTreeNodeMap[parent.id];
            let newChild = [];
            if (curChild) {
                _.each(curChild, item => {
                    if (!(item.oid === row.oid)) {
                        newChild.push(item);
                    }
                })
            }
            this.refreshTreeNode(parent, newChild);
        },
        // 重置当前行的操作
        resetOperate(row) {
            if (row.bomOperateFlag === 'new') {
                this.deleteNewAddRow(row);
            } else {
                this.$set(row, 'bomOperateFlag', null);
                row.newRelationData = JSON.parse(JSON.stringify(row.relationData));
            }
        },
        tableRowClassName({row, rowIndex}) {
            if (row.bomOperateFlag === 'new') {
                return 'bom-new-row';
            } else if (row.bomOperateFlag === 'update') {
                return 'bom-update-row';
            } else if (row.bomOperateFlag === 'delete') {
                return 'bom-delete-row';
            }
            return '';
        },
    },
};
</script>

<style>
.el-table .bom-new-row {
    background: #CCEDAF;
}
.el-table .bom-update-row {
    background: #FFE4BD;
}
.el-table .bom-delete-row {
    background: #FFC2C3;
}
</style>
