<template>
  <div class="jw-panel">
    
    <div class="bg-white border" :style="{height: fullHeight + 20 + 'px'}">
      <!-- <ConditionQuery :DictionariesCode="DictionariesCode" :onlyData="onlyData" @transmitValue="searchMutiCondition" @searchShowCode="searchShowCode" @showLength="showLength"></ConditionQuery>  -->
      <div class="search-info">
        <a-button class="new-btn"   @click="newec">{{$t('btn_new_create')}}</a-button>
        <a-input :placeholder="$t('msg_input')" suffix-icon="el-icon-search" class="search-btn" @change="search"></a-input>
      </div>
      <div class="ec-list-card-group scroll" :style="{height: fullHeight -100 + 'px' }">
        <div class="main-table">
          <a-table :data-source="listData" :columns="columns"  rowKey="oid" :pagination="pagination" >
              <template slot="name" slot-scope="scoped, record">
                <div class="name-list">
                  <img src="../../../assets/image/change-iconer.png" class="change-icon">
                  <span>{{ record.name }}</span>
                </div>
              </template>

                <template slot-scope="scoped, record" slot="number">
                  <span class="numbers" @click="viewDetail(record)">{{ record.number }}</span>
              </template>

               <template slot-scope="scope, record" slot="typeValue">
                    <span v-if="record.typeCode==6654">{{$t('txt_design_flaw')}}</span>
                    <span v-else-if="record.typeCode==6665">{{$t('txt_process_defects')}}</span>
                    <span v-else-if="record.typeCode==6667">{{$t('txt_requirements_change')}}</span>
                    <span v-else>{{$t('txt_purchasing_change')}}</span>
              </template>

              <template slot-scope="scope,record" slot="degreeEmergencyValue">
                <span v-if="record.degreeEmergencyCode=='crucial'" class="higher">{{$t('txt_very_urgent')}}</span>
                <span v-else-if="record.degreeEmergencyCode=='major'" class="middler">{{$t('txt_urgent')}}</span>
                <span v-else class="lower">{{$t('txt_general')}}</span>
              </template>

                <template slot-scope="scoped,record" slot="lifecycle">
                <span v-if="record.lifecycle=='Open'" class="nosubmit">{{$t('txt_been_submitted')}}</span>
                <span v-else-if="record.lifecycle=='UnderReview'" class="approval">{{$t('txt_approval')}}</span>
                <span v-else-if="record.lifecycle=='Estimate'" class="assessment">{{$t('txt_change_evaluation')}}</span>
                <span v-else-if="record.lifecycle=='Inwork'" class="perform">{{$t('txt_being_performed')}}</span>
                <span v-else-if="record.lifecycle=='Released'" class="release">{{$t('txt_published')}}</span>
                <span v-else class="cancel">{{$t('txt_has_cancel')}}</span>
              </template>

               <template slot-scope="scope,record" slot="createdTime">
                <span>{{ formatDateList(record.createdTime) }}</span>
              </template>
               

               <template slot-scope="scope" slot="creatorName">
                <span>{{ scope }}</span>
              </template>

              <template slot-scope="scope,record"  slot="operation">
                <a-button :disabled="record.lifecycle !== 'Open'" type="text" size="small" @click="startUp(record)">{{$t('txt_start')}}</a-button>
              </template>

          </a-table>
        </div>
      </div>
     <div class="card-pagination text-center">
         <a-pagination
            :default-current="currentPage"
            :total="total"
            :show-total="total => $t('txt_total')+`${total} `+$t('txt_pieces')"
            show-size-changer
            @change="handleCurrentChange"
            @showSizeChange="handleSizeChange"
          />
      </div>  
    </div>

   

    <a-modal
      :title="$t('txt_submit_audit')"
      :visible="dialogTableVisible"
      :width="width"
      @ok="confirmBtn"
      @cancel="cancer"
    >
      <a-form-model
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        class="demo-ruleForm"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
      <a-form-model-item :label="$t('txt_reviewer_role')" prop="account">
      <a-select v-model.trim="account" filterable :placeholder="$t('msg_select')" @change="getAuditList">
        <a-select-option v-for="item in options" :key="item.oid" :value="item.account">{{item.name}}</a-select-option>
      </a-select>
    </a-form-model-item>

      </a-form-model>
    </a-modal>

  </div>
</template>

<script>
//组件
// import i18nService from "jw_services/i18n/index";
import ConditionQuery from "../condition-query";
// import searchLink from "models/product-folder/search-link-with-page-list";
// import folderCommon from "../folder-common";
// import ModuleView from "jw_components/module-view";
// import DialogInfo from "./dialogInfo";
import ModelFactory from "jw_apis/model-factory";
import { formatDate } from "jw_utils/moment-date";

const findListApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}` + "/ecr/findList",
  method: "post"
});

const searchLink = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}` + "/ecr/findList",
  method: "post"
});

const UpWorkflow = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/ecr/processDetail`,
  method: "get"
});

const startUpWorkflow = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/ecWorkflow/ecr/start`,
  method: "post"
});

let searchAudit = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountServer}/v2/user/searchByKeywordAndPaging`,
  method: "get"
});

let startProcess = ModelFactory.create({
  url: `${
    Jw.gateway
  }/workbench-workflow/workflow/runtime/startProcessInstanceWithCreateRelation`,
  method: "post"
});

export default {
  // mixins: [folderCommon],
  components: {
    // ModuleView,
    ConditionQuery
  },
  inject: ["setBreadcrumb", "addBreadcrumb"],
  data() {
    return {
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      pagination: false,
      columns:[{
        title: this.$t('txt_name'),
        dataIndex: 'name',
        key: 'name',
        sort: true,
        scopedSlots: { customRender: 'name' },
      },
      {
        title: this.$t('txt_number'),
        dataIndex: 'number',
        key: 'number',
        sort: true,
        scopedSlots: { customRender: 'number' },
      },
      {
        title: this.$t('txt_type'),
        dataIndex: 'typeValue',
        key: 'typeValue',
        sort: true,
        scopedSlots: { customRender: 'typeValue' },
      },
      {
        title: this.$t('change_emergency'),
        dataIndex: 'degreeEmergencyValue',
        key: 'degreeEmergencyValue',
        sort: true,
        scopedSlots: { customRender: 'degreeEmergencyValue' },
      }, 
       {
        title: this.$t('txt_status'),
        dataIndex: 'lifecycle',
        key: 'lifecycle',
        sort: true,
        scopedSlots: { customRender: 'lifecycle' },
      }, {
        title: this.$t('txt_create_date'),
        dataIndex: 'createdTime',
        key: 'createdTime',
        sort: true,
        scopedSlots: { customRender: 'createdTime' },
      }, 
     
       {
        title: this.$t('txt_create_by'),
        dataIndex: 'creatorName',
        key: 'creatorName',
        sort: true,
        scopedSlots: { customRender: 'creatorName' },
      }, {
        title: this.$t('txt_operation'),
        dataIndex: 'operation',
        key: 'operation',
        sort: true,
        scopedSlots: { customRender: 'operation' },
      }],
      modelType: "",
      oid: "",
      account: null,
      options: [],
      width: "480px",
      labelPosition: "top",
      dialogTableVisible: false,
      isopen:null,
      ruleForm: {
        searchKey: "",
        pageNum: "1",
        pageSize: "50",
        tenantId: 1,
        tenantOid: Jw.getUser().tenantOid
      },
      rules: {
        account: [{ required: true, message: this.$t('txt_select_reviewer'), trigger: "blur" }]
      },

      DictionariesCode: `${Jw.gateway }/dict/dictValueDefinition/searchByDictCode/changeList`,
      onlyData: "part-bom/bomlink/searchSecBOMData",
      fullHeight: document.documentElement.clientHeight - 210,
      loading: true,
      targetType: ["Part"],
      listData: [],
      labelList: [],
      keyword: "",
      currentPage: 1,
      total: 0,
      pageNum: 1,
      pageSize: 10,
    };
  },

  created() {
    this.initBreadcrumb();
    this.searchInfo("");
    this.fetchAuditList();
  },

  methods: {
      initBreadcrumb() {
              let breadcrumbData = [{ name: this.$t('txt_procuct_cu'), path: '/product' }];
              this.setBreadcrumb(breadcrumbData);
              this.addBreadcrumb(
                {
                    name: this.$t('txt_change_management'),
                    children: [
                        { id: '1', title: this.$t('txt_content_management'), link: `/contentManage/${this.$route.params.oid}/${this.$route.params.type}` }, 
                        { id: '2', title: this.$t('txt_product_structure'), link: `/productStructure/${this.$route.params.oid}/${this.$route.params.type}` }, 
                        { id: '3', title: this.$t('txt_baseline_management'), link: `/baseline/${this.$route.params.oid}/${this.$route.params.type}` },
                        { id: '4', title: this.$t('txt_change_management'), link: `/changeManage/${this.$route.params.oid}/${this.$route.params.type}` },
                    ]
                },
            );
          },
    getAuditList(val) {
      this.account = val;
      console.log("val", this.account);
    },
    fetchAuditList() {
      searchAudit
        .execute(this.ruleForm)
        .then(data => {
          this.options = data.rows;
        })
        .catch(err => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },

    async confirmBtn() {
    
      this.fetchUpWorkflow();
      this.dialogTableVisible = false;
    },
   
    cancer() {
      this.dialogTableVisible = false;
    },
     switchData(value) {
       
         var searchVal = ""
          if(value == this.$t('digitalPlm.change.open')) {
               searchVal = "Open"
              }else if (value == this.$t('digitalPlm.change.underReview')) {
                searchVal = "UnderReview"
              } else if(value == this.$t('digitalPlm.change.estimate')) {
                 searchVal = "Estimate"
              }else if(value == this.$t('digitalPlm.change.inwork')) {
                searchVal = "Inwork"
              }else if(value == this.$t('digitalPlm.change.released')) {
                 searchVal = "Released"
              }else if(value == this.$t('digitalPlm.change.cancel')) {
                 searchVal = "Cancelled"
              }else {
                 searchVal = ""
              }
         return searchVal || value
         
     },
    searchMutiCondition(value) {
      this.mutiConditionDTOS = value;
      let obj = this.getParams();
      if(this.mutiConditionDTOS[0]) {
          if(this.mutiConditionDTOS[0].conditionKey =='lifecycle') {
             let values = this.mutiConditionDTOS[0].conditionValue
             var statusVal = this.switchData(values)
             this.mutiConditionDTOS[0].conditionValue = statusVal
          }
      }
      obj.mutiConditionDTOS = this.mutiConditionDTOS;
      obj.pageSize = this.pageSize;
      obj.pageNum = 1;
      obj.searchKey = this.keyword;
      this.currentPage = 1;
      this.fetch(obj);
    },

    fetch(obj) {
      // let lang = i18nService.getOtherLanguageMap();
      this.loading = true;
      searchLink
        .execute(obj)
        .then(data => {
          this.list = _.map(data.result.rows, row => {
            row.teamModelType = "Product";
            return row;
          });
          this.currentPage = data.result.currentPage;
          this.total = data.result.count;
          this.loading = false;
          this.listData = data.result.data;
          console.log("this.listData", this.listData);
        })
        .catch(() => {
          this.loading = false;
          // this.$alert(lang["loadingFailAgain"], "Error").then(() => {
            this.fetch(this.getParams());
          //});
        });
    },

    pds() {
      if (this.lengthCode == 1) {
        this.heigs = "58%";
      } else if (this.lengthCode == 2) {
        this.heigs = "53%";
      } else if (this.lengthCode == 3) {
        this.heigs = "47%";
      } else if (this.lengthCode == 4) {
        this.heigs = "40%";
      } else if (this.lengthCode == 5) {
        this.heigs = "35%";
      }
    },
    searchShowCode(value) {
      console.log(value, "value");
      this.isopen = value
      if (value == true) {
        this.pds();
      } else if (value == false) {
        this.heigs = "80%";
      }
    },

    showLength(value) {
      console.log(value, "showLength");
      this.lengthCode = value;
      this.pds();
    },

    formatDateList(date, format = "YYYY-MM-DD") {
      return formatDate(date,"YYYY-MM-DD")
    },
    fecthSearch() {
      this.searchInfo("");
    },

    newec() {
      this.$router.push('/changeStep')
    },

    viewDetail(item) {
      let text = this.$router.resolve({
        path: `/changeManage/detail/${item.modelType}/${item.oid}`,
      });

      window.location = text.href;
    },

    async startUp(row) {
      this.oid = row.oid;
      let res = await UpWorkflow.execute({ ecrOid: row.oid });
      if (res.length == 0 || res == null) {
        this.dialogTableVisible = true;
      } else {
        this.account = "";
        this.fetchUpWorkflow(row);
      }
    },

    fetchUpWorkflow(row) {
     
      let param = {
        ecrOid: this.oid,
        changeApproval: this.account
      };
      startUpWorkflow
        .execute(param)
        .then(res => {
          this.$success(this.$t('txt_success_enble'));
          this.searchInfo("");
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },

    search(val) {
      this.pageNum = 1;
      this.searchInfo(val.target.value);
    },

    searchInfo(value) {
      this.keyword = value;
      findListApi
        .execute({
          searchKey: this.keyword,
          pageSize: this.pageSize,
          pageNum: this.pageNum
        })
        .then(result => {
          this.loading = false;
          this.listData = result.data;
          this.total = result.count;
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },

     handleCurrentChange(page, pageSize) { //当前页码
      this.pageNum = page
      // let obj = this.getParams();
      // obj.mutiConditionDTOS = this.mutiConditionDTOS;
      // if (this.isopen) {
      //   obj.pageSize= this.pageSize; //多少条一页
      //   obj.pageNum = page; //当前页  
      //   obj.searchKey = this.keyword
      //   this.fetch(obj);
      // } else {
      //   this.pageNum = page
      //    this.searchInfo(this.keyword);
      // }
       this.searchInfo(this.keyword)
    },
    handleSizeChange(current, size) {//多少条一页
      this.pageSize = size
      // let obj = this.getParams();
      // obj.mutiConditionDTOS = this.mutiConditionDTOS;
      // if (this.isopen) {
      //   obj.pageSize = size
      //   obj.pageNum = current //当前页
      //   obj.searchKey = this.keyword
      //   this.fetch(obj);
      // } else {
      //    this.searchInfo(this.keyword);
      // }

      this.searchInfo(this.keyword);
    },
   
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        this.fullHeight = document.documentElement.clientHeight - 210;
      })();
    };
  }
};
</script>

<style lang="less" scoped>
.new-btn{
  background: #255ed7;
  margin-right: 8px;
}
.card-pagination{
  margin-top: 15px;
}
.confirm-icon {
  width: 80px;
  height: 32px;
  background: #255ed7;
  border-radius: 4px;
  color: #fff;
}

.bg-white {
  overflow-y: scroll;
  background: #fff;
}
.cancel {
  height: 22px;
  padding: 0 8px;
  background: #fff1f0;
  border: 1px solid #ffc2c3;
  border-radius: 4px;
  font-size: 12px;
  color: #f34f63;
  display: block;
  float: left;
}
.release {
  height: 22px;
  padding: 0 8px;
  background: #f8fff0;
  border: 1px solid #ccedaf;
  border-radius: 4px;
  font-size: 12px;
  color: #52c41a;
  display: block;
  float: left;
}
.perform {
  height: 22px;
  padding: 0 8px;
  background: #f0f7ff;
  border: 1px solid #a4c9fc;
  border-radius: 4px;
  font-size: 12px;
  color: #255ed7;
  display: block;
  float: left;
}
.assessment {
  height: 22px;
  padding: 0 8px;
  background: #e6fffb;
  border: 1px solid #87e8de;
  border-radius: 4px;
  font-size: 12px;
  color: #08979c;
  display: block;
  float: left;
}
.approval {
  height: 22px;
  padding: 0 8px;
  background: #fffaf0;
  border: 1px solid #ffe4bd;
  border-radius: 4px;
  font-size: 12px;
  color: #f2994a;
  display: block;
  float: left;
}
.nosubmit {
  height: 22px;
  padding: 0 8px;
  background: rgba(30, 32, 42, 0.04);
  border: 1px solid rgba(30, 32, 42, 0.15);
  border-radius: 4px;
  font-size: 12px;
  color: rgba(30, 32, 42, 0.65);
  display: block;
  float: left;
}
.search-info {
  height: 68px;
  line-height: 68px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-sizing: border-box;
  border-bottom: 1px solid rgba(30, 32, 42, 0.15);
  width: 100%;
}
.search-btn {
  width: 216px;
  height: 32px;
  line-height: 32px;
}
.new-btn,
.new-btn:hover {
  width: 52px;
  height: 32px;
  line-height: 8px;
  background: #255ed7;
  border-radius: 4px;
  margin-right: 8px;
  color: #fff;
  text-align: center;
  padding: 0;
}
.name-list {
  display: flex;
  align-items: center;
}
.change-icon {
  width: 16px;
  height: 16px;
  margin-right: 16px;
}
.lower {
  padding: 0 8px;
  height: 22px;
  background: #255ed7;
  border-radius: 4px;
  color: #fff;
  text-align: center;
  font-size: 12px;
  display: block;
  float: left;
}
.middler {
  height: 22px;
  background: #f69c41;
  border-radius: 4px;
  color: #fff;
  padding: 0 8px;
  text-align: center;
  display: block;
  font-size: 12px;
  float: left;
}
.higher {
  height: 22px;
  background: #f6445a;
  border-radius: 4px;
  padding: 0 8px;
  color: #fff;
  text-align: center;
  font-size: 12px;
  display: block;
  float: left;
}
.numbers {
  color: #409eff;
  cursor: pointer;
}
.main-table {
  padding: 20px;
}
.cardbtn {
  padding: 0 10px !important;
  height: 32px;
}
.ec-list-card-group {
  overflow-y: scroll;
}
.text-center {
  margin-bottom: 10px;
  text-align: right;
}
.title {
  height: 40px;
  line-height: 40px;
  margin-top: -12px;
}
.ec-list-card-group {
  .list-card {
    width: 95%;
    margin: 20px auto;
    .card-content {
      justify-content: space-between;
      .content-left {
        width: 40%;
        text-indent: 50px;
        padding-right: 50px;
      }

      .content-right {
        width: 40%;
        .subject {
          font-size: 16px;
          margin-right: 10px;
          width: 600px;
        }
        .el-tag {
          text-transform: capitalize;
        }
      }
      .ec-icon {
        background-color: #fff;
        color: #4686ff;
        border: 1px solid #4686ff;
        font-size: 25px;
      }
    }
  }

  .gray-color {
    color: #c0c0c0;
  }
  .p-r-5 {
    //text-align: right;
    padding-right: 5px;
  }
  .m-b-10 {
    margin-bottom: 10px;
    .el-tag {
      display: inline;
      padding: 2px 5px;
    }
  }
  .center-Y {
    margin: auto 0;
  }
  .card-cell-10 {
    display: flex;
    flex: 1;
  }
  .card-cell-2 {
    width: 120px;
    text-align: center;
    display: flex;
    align-items: center;
  }
}
</style>
