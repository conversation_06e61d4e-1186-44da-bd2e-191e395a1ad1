<template>
	<div class="search-info">
		<div class="search-info-left">
			<a-button type="primary" class="new-btn" @click="creatBtn">{{
				$t("btn_new_create")
			}}</a-button>
			<a-input-search
				:placeholder="$t('txt_search')"
				class="search-btn"
				v-model.trim="form.keyword"
				@search="onSearch"
			/>
		</div>
		<div class="operation-btn">
			<span><img src="../../../assets/image/filter.png" /></span>
		</div>
	</div>
</template>

<script>
export default {
	name: "searchInfo",
	data() {
		return {
			form: {
				keyword: "",
			},
		}
	},
	methods: {
		creatBtn() {
			this.$router.push("/changeStep")
		},
		onSearch() {
			this.$emit("onSearch", this.form.keyword)
		},
	},
}
</script>

<style scoped>
.operation-btn span img {
	width: 16px;
	height: 16px;
}
.operation-btn span {
	border: 1px solid rgba(30, 32, 42, 0.15);
	padding: 0 8px;
	height: 32px;
	line-height: 32px;
	border-radius: 4px;
	display: block;
}
.operation-btn {
	height: 32px;
	line-height: 32px;
	margin-top: 0px;
	display: flex;
	margin-right: 20px;
}
.new-btn {
	width: 57px;
	height: 32px;
	background: #255ed7;
	border-radius: 4px;
	text-align: center;
	padding: 0;
	margin-right: 8px;
}

.search-btn {
	width: 216px;
	height: 32px;
	background: #fff;
	border-radius: 4px;
}
.search-info-left {
	height: 32px;
	line-height: 32px;
	padding: 0 20px;
	display: flex;
	align-items: center;
	margin: 16px 0;
}
.search-info {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
</style>
