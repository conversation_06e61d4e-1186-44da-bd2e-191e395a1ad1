<template>
  <div class="mian-dialog">
    <div class="workflow-title">
      <img
        src="../../../../assets/image/back.png"
        @click="goback"
        class="plm-icon-back"
      />
      <span>新建变更</span>
    </div>

    <div class="workflow-main">
      <div class="steps">
        <stepComponents
          @switchStep="switchStep"
          :active="active"
        ></stepComponents>
      </div>

      <div class="step-content" :style="{ height: fullHeight - 240 + 'px' }">
        <keep-alive>
          <component
            :is="'Step' + (active + 1)"
            :ref="'Step' + (active + 1)"
            :fullHeight="fullHeight"
          ></component>
        </keep-alive>
      </div>

      <div class="create-btn" v-if="active === 0">
        <a-button type="primary" class="new-btn-create" @click="nextStep1"
          >{{$t('btn_next_step')}}</a-button
        >
        <a-button class="cancer" @click="goback">{{$t('btn_cancel')}}</a-button>
      </div>

      <div class="create-btn" v-if="active === 1">
        <a-button @click="prevStep2(0)" style="margin-right: 8px"
          >{{$t('btn_pre_step')}}</a-button
        >
        <a-button type="primary" class="new-btn-create" @click="nextStep2"
          >{{$t('btn_next_step')}}</a-button
        >
        <a-button class="cancer" @click="goback">{{$t('btn_cancel')}}</a-button>
      </div>

      <div class="create-btn" v-if="active === 2">
        <a-button @click="prevStep2(1)" style="margin-right: 8px"
          >{{$t('btn_pre_step')}}</a-button
        >
        <a-button type="primary" class="new-btn-create" @click="nextStep3"
          >{{$t('btn_next_step')}}</a-button
        >
        <a-button class="cancer" @click="goback">{{$t('btn_cancel')}}</a-button>
      </div>

      <div class="create-btn" v-if="active === 3">
        <a-button @click="prevStep2(2)" style="margin-right: 8px"
          >{{$t('btn_pre_step')}}</a-button
        >
        <a-button type="primary" class="new-btn-create" @click="onSave"
          >{{$t('btn_save')}}</a-button
        >
        <a-button @click="onSubmitBtn" style="margin-right: 8px">提交</a-button>
        <a-button class="cancer" @click="goback">{{$t('btn_cancel')}}</a-button>
      </div>

      <a-modal
        title="提交审核"
        :visible="dialogTableVisible"
        :width="width"
        @ok="confirmBtn"
        @cancel="cancer"
      >
        <a-form-model
          :model="formlist"
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
        >
          <a-form-model-item label="审核人">
            <a-select
              v-model.trim="formlist.account"
              filterable
              placeholder="请选择"
            >
              <a-select-option
                v-for="item in options"
                :key="item.oid"
                :value="item.account"
                >{{ item.name }}</a-select-option
              >
            </a-select>
          </a-form-model-item>
        </a-form-model>
      </a-modal>
    </div>
  </div>
</template>

<script>
import Step1 from "./step1";
import Step2 from "./step2";
import Step3 from "./step3";
import Step4 from "./step4";
import stepComponents from "./stepComponents";
// import cookie from "jw_common/cookie";

import ModelFactory from "jw_apis/model-factory";
const createChangeInfo = ModelFactory.create({
  //保存
  url: `${Jw.gateway}/${Jw.changeServer}/ecr/create`,
  method: "post",
});

let updateLifeCycle = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/ecr/updateLifeCycle`,
  method: "get",
});

let searchAudit = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountServer}/v2/user/searchByKeywordAndPaging`,
  method: "get",
});

let startProcess = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workbenchWorkflowService}/workflow/runtime/startProcessInstanceWithCreateRelation`,
  method: "post",
});

let fetchCheckChange = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/ecr/checkChange `,
  method: "post",
});

let form = {
  oid: "",
  schemes: "",
  beforeChange: {
    filename: "",
    filesrc: "",
    screenshots: [],
  },
  afterChange: {
    filename: "",
    filesrc: "",
    screenshots: [],
  },
};
let refMap = {};

export default {
  name: "dialoginfo",

  data() {
    return {
      width: "480px",
      labelPosition: "top",
      options: [],
      dialogTableVisible: false,
      title: "发起变更",
      fullHeight: document.documentElement.clientHeight,
      active: 0,
      listArr: [],
      partArr: [],
      documentArr: [],
      cadDocumentArr: [],
      allArrData: [],
      name: "",
      formlist: {
        account: null,
      },

      rules: {
        account: [
          { required: true, message: "请选择审核人", trigger: "change" },
        ],
      },
      ruleForm: {
        searchKey: "",
        pageNum: "1",
        pageSize: "50",
        tenantId: 1,
        tenantOid: Jw.getUser().tenantOid,
      },

      modelType: null,
      oid: null,
      initSelectedRows: [],
    };
  },
  components: {
    Step1,
    Step2,
    Step3,
    Step4,
    stepComponents,
  },
  provide: function () {
    return {
      _selectedRows: () => {
        if (!this.listArr.length) return;
        this.listArr.forEach((item) => {
          if (!item.hasOwnProperty("newModelType")) {
            item.newModelType = item.inheritModelType || item.modelType;
          }
          if (!item.newProperties && item.newModelType == "CADDocument") {
            item.newProperties = _.cloneDeep(form);
          }
        });
        localStorage.setItem("Step2Data", JSON.stringify(this.listArr));
        return this.listArr;
      },
      _initSelectedRows: () => {
        if (!this.listArr.length) return;
        this.listArr.forEach((item) => {
          if (!item.hasOwnProperty("newModelType")) {
            item.newModelType = item.inheritModelType || item.modelType;
          }
          if (!item.newProperties && item.newModelType == "CADDocument") {
            item.newProperties = _.clone(form);
          }
          let some = this.initSelectedRows.some((row) => {
            return row.oid == item.oid;
          });
          if (!some) {
            this.initSelectedRows.push(_.cloneDeep(item));
          }
        });

        return this.initSelectedRows;
      },
    };
  },
  watch: {
    visiable(newV) {
      this.visiableVal = newV;
    },
  },

  mounted() {
    this.visiableVal = this.visiable;
    window.onresize = () => {
      return (() => {
        this.fullHeight = document.documentElement.clientHeight;
      })();
    };
  },

  methods: {
    fetchAuditList() {
      searchAudit
        .execute(this.ruleForm)
        .then((data) => {
          this.options = data.rows;
        })
        .catch((err) => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
    confirmBtn() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          let res = await updateLifeCycle.execute({
            modelType: this.modelType,
            oid: this.oid,
            lifecycle: "UnderReview",
          });
          if (res && res.msg) {
            this.$error(err.msg);
          } else {
            this.startProcessBtn();
          }

          this.dialogTableVisible = false;
          this.$router.push("/changeManage/index");
        }
      });
    },
    startProcessBtn() {
      let variables = [];
      let newArr = [
        { name: "changeApproval", value: this.formlist.account },
        { name: "modelType", value: this.modelType },
        { name: "oid", value: this.oid },
        { name: "gateway", value: `${Jw.gateway}` },
        // { name: "accesstoken", value: cookie.getAuth() }
      ];
      variables = newArr;
      console.log("variables", variables);
      startProcess
        .execute({
          tenantId: Jw.appName,
          variables,
          processDefinitionKey: "ECR_Workflow",
        })
        .then((res) => {
          this.$success("提交成功");
        })
        .catch((err) => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
    cancer() {
      this.dialogTableVisible = false;
    },
    switchStep(flag) {
      this.active = flag;
    },
    onSave() {
      this.fetchAuditList();
      this.onSubmitBtn("start");
    },
    onSubmitBtn(startFlag) {
      this.uploadImg(startFlag);
    },

    submitData(startFlag) {
      let ecr = refMap[1].form || this.$refs.Step1.form; // 第一步基本信息
      let ecTargets = JSON.parse(localStorage.getItem("Step2Data")); //cad, part, bom, document 第二步选择对象
      let allData = this.listArr; //第四步 数据
      var changeScheme = {};
      for (let i = 0; i < allData.length; i++) {
        let objectOid = allData[i].oid;
        let newModelType = allData[i].inheritModelType
          ? allData[i].inheritModelType
          : allData[i].modelType;
        var bomData = allData[i];
        var partData = _.clone(allData[i]);
        delete partData.children;
        delete partData.newProperties;
        delete partData.newRelationData;
        delete partData.relationData;
        delete partData.partUnit;
        if (newModelType == "Part") {
          changeScheme[objectOid] = {
            partFieldChange: partData,
            bomChange: bomData,
          };
        } else if (newModelType == "Document") {
          changeScheme[objectOid] = {
            docChange: allData[i],
          };
        } else {
          changeScheme[objectOid] = {
            cadChange: allData[i],
          };
        }
      }

      let param = {
        ecr: ecr,
        ecTargets: ecTargets,
        changeScheme: changeScheme,
      };
      // console.log("paramsss", param);
      createChangeInfo
        .execute(param)
        .then((data) => {
          if (startFlag === "start") {
            // console.log("start");
            this.modelType = data.modelType;
            this.oid = data.oid;
            this.dialogTableVisible = true;
          } else {
            console.log("保存成功");
            this.$success(
              this.$t("digitalPlm.change.savedSuccessfully")
            );
            this.$router.push("/changeManage/index");
          }
        })
        .catch((err) => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
    nextStep3() {
      this.active = 3;
    },

    prevStep2(flag) {
      this.active = flag;
    },
    nextStep2() {
      let setp2 = this.$refs.Step2;
      let selectedRows = setp2.selectData; // 数据源头
      var newArr = [];
      if (selectedRows.length > 0) {
        for (let k = 0; k < selectedRows.length; k++) {
          newArr.push({
            oid: selectedRows[k].oid,
            modelType: selectedRows[k].modelType,
          });
        }
        fetchCheckChange
          .execute(newArr)
          .then((res) => {
            this.active = 2;
            this.listArr = selectedRows;
          })
          .catch((err) => {
            if (err.msg) {
              this.$warning(err.msg);
            }
          });
      } else {
        this.$error("请至少选择一条数据");
      }
    },

    uploadImg(startFlag) {
      let that = this.$refs.Step1 || refMap[1];
      if (that.files.length > 0) {
        let formData = new FormData();
        let xhr = new XMLHttpRequest();
        that.files.forEach(function (file) {
          formData.append("file", file, file.name); // 因为要上传多个文件，所以需要遍历一下才行
        });
        xhr.open(
          "POST",
          `${Jw.gateway}/${Jw.fileServer}/file/multiUpload-v2`,
          true
        );
        xhr.onload = (res) => {
          const response = JSON.parse(xhr.response);
          that.form.appendixs = response.result;
          this.submitData(startFlag);
        };
        xhr.send(formData);
      } else {
        this.submitData(startFlag);
      }
    },

    nextStep1() {
      refMap[1] = this.$refs.Step1;
      //图片上传
      this.onSubmit();
    },
    onSubmit() {
      //下一步
      let that = this.$refs.Step1 || refMap[1];
      that.$refs.formlist.validate((valid) => {
        if (valid) {
          that.form.typeValue = _.find(that.typeList, (item) => {
            return item.code == that.form.typeCode;
          }).label;
          that.form.degreeEmergencyValue = _.find(
            that.emergencyList,
            (item) => {
              return item.code == that.form.degreeEmergencyCode;
            }
          ).label;
          that.form.typeCode = that.form.typeCode;
          delete that.form.file;

          console.log("变更基本信息", that.form);
          this.active = 1;
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    goback() {
      this.$router.push("/changeManage/index");
    },
    getClassifyData(partArr, documentArr, cadDocumentArr) {
      // this.partArr = partArr;
      // this.documentArr = documentArr;
      // this.cadDocumentArr = cadDocumentArr;
    },

    cancerBtn() {
      this.closedVal = true;
      this.$refs.Step1.clearData();
      this.$emit("closed", false);
    },
    goStep3(step) {
      this.active = step;
      this.flag = true;
    },
    goStep(step) {
      this.active = step;
    },
    onClose() {
      if (this.$refs.Step1) {
        this.$refs.Step1.clearData();
      }
      this.active = 0;
      this.$emit("closed", false);
    },
  },
};
</script>

<style scoped>
.confirm-icon {
  width: 80px;
  height: 32px;
  background: #255ed7;
  border-radius: 4px;
  color: #fff;
}
.plm-icon-back {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}
.cancer {
  width: 57px;
  height: 32px;
  background: #fff;
  padding: 0;
}
.new-btn-create {
  width: 66px;
  height: 32px;
  background: #255ed7;
  border-radius: 4px;
  text-align: center;
  padding: 0;
  margin-right: 8px;
}
.create-btn {
  border-top: 1px solid rgba(30, 32, 42, 0.15);
  width: 100%;
  display: flex;
  justify-content: left;
  align-items: center;
  position: fixed;
  background: #fff;
  height: 72px;
  line-height: 72px;
  box-sizing: border-box;
  z-index: 1000;
  bottom: 0;
  padding-left: 24px;
}

.step1 {
  padding: 0 20px;
}
.workflow-main {
  background: #fff;
  box-shadow: 0 2px 8px 0 rgb(30 32 42 / 25%);
  border-radius: 4px;
  padding: 20px 0px 0 0px;
  margin: 0px 20px;
  overflow: auto;
  transform: translate(0, 0);
  box-sizing: border-box;
}
.step-info {
  height: 60px;
  display: flex;
  align-items: center;
  margin: 0 20px;
  background: rgba(30, 32, 42, 0.06);
  border-radius: 4px;
}
.workflow-title {
  height: 40px;
  line-height: 40px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  cursor: pointer;
  margin-top: -12px;
}

.workflow-title span {
  font-size: 14px;
  color: #1e202a;
  font-weight: 600;
}
.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.step-content {
  margin-top: 15px;
}
.steps {
  margin: 0 auto;
  padding: 0 20px;
}
.dialog-body {
  padding-right: 10px;
  overflow: auto;
}
.fullscreen-btn {
  margin-right: 30px;
}

.mian-dialog >>> .el-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: calc(100% -30px);
  max-width: calc(100% -30px);
}
.mian-dialog >>> .el-dialog .el-dialog__body {
  flex: 1;
  overflow: auto;
}
</style>