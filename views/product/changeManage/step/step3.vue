<template>
  <div>
    <div class="main-table-info">
      <a-row :gutter="20">
        <a-col :span="6">
          <div class="selected-object" :style="{ height: fullHeight - 323 + 'px' }">
            <span>
              <h1>变更对象</h1>
            </span>
            <ul>
              <li v-for="(item, index) of selectedRows" :key="index" :class="{current: item.oid==selected3.oid}" @click="switchData(item,index)">
                <div class="object-list">
                  <i :class="item.iconURL"></i>
                  <div class="object-name" v-if="item.viewName" :title="item.name +'，' + item.number + '，' + item.viewName + '，' + item.version">{{item.name}}，{{item.number}}，{{item.viewName}}，{{item.version}}</div>
                  <div class="object-name" v-else :title="item.name +'，' + item.number + '，' + item.version">{{item.name}}，{{item.number}}，{{item.version}}</div>
                </div>
              </li>
            </ul>
          </div>
        </a-col>
       <a-col :span="18">
          <div class="main-table-list">
            <div class="search-info">
              <span class="activer">产品</span>
               <a-input-search :placeholder="$t('txt_enter_keyword')" class="input-with-select" v-model.trim="form.searchKey"  @search="searchBtn" />
            </div>
            <div class="table3" :style="{height: fullHeight -439 + 'px'}">
              <a-table  :loading='loading' :columns="columns" :pagination="pagination" :dataSource="newDataArr" defaultExpandAllRows :scroll="{y: fullHeight - 439}"  row-key="oid" >
                   <template slot="name" slot-scope="scoped, record">
                    <i :class="record.iconURL"></i>
                    {{ record.name }}
                  </template>
                   <template slot="number" slot-scope="scoped, record">
                      <span class="numbers" @click="jumplink(record.row)">{{record.number}}</span>
                  </template>
                   <template slot="newType" slot-scope="scoped, record">
                    <span>{{ record.newType }}</span>
                  </template>

                  <template slot="lifecycle" slot-scope="scoped, record">
                    <span v-if="record.lifecycle=='Open'">未提交</span>
                    <span v-else-if="record.lifecycle=='UnderReview'">正在审批</span>
                    <span v-else-if="record.lifecycle=='Estimate'">变更评估</span>
                    <span v-else-if="record.lifecycle=='Inwork'">正在执行</span>
                    <span v-else-if="record.lifecycle=='Released'">已发布</span>
                    <span v-else class="cancel">已取消</span>
                  </template>
              </a-table>
            </div>

          </div>
          <div class="process">
            <span>工艺</span>
          </div>
        </a-col>
      </a-row> 
    
    
    
    </div>
  </div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory"

const searchObjectInfo = ModelFactory.create({
  //查询受影响对象接口
  url: `${Jw.gateway}/${Jw.changeServer}/ecr/getEffect`,
  method: "get"
});

const searchObjectType = ModelFactory.create({
  //查询受影响对象接口
  url: `${Jw.gateway}/${Jw.baseServer}/meta/model/displayName/byRootType`,
  method: "post"
});
export default {
  name: "Step3",
  props: ["fullHeight"],
  data() {
    return {
      loading: false,
      num: 0,// total:0,
      multipleSelection: [],
      objectSelectData: [],
      searchVal: "",
      objectTableData: [],
      tableData: [],
      productList: [],
      newDataArr: [],
      pagination:false,
      partArr: [],
      columns:[{
          title: this.$t('txt_name'),
          dataIndex: "name",
          key: "name",
          sorter:true,
          ellipsis: true,
          scopedSlots: { customRender: "name" },
        },{
          title: "关系",
          dataIndex: "relationType",
          key: "relationType",
          sorter:true,
          ellipsis: true,
          scopedSlots: { customRender: "relationType" },
        },{
          title:this.$t('txt_number'),
          dataIndex: "number",
          key: "number",
          sorter:true,
          ellipsis: true,
          scopedSlots: { customRender: "number" },
        },{
          title: this.$t('txt_type'),
          dataIndex: "newType",
          key: "newType",
          sorter:true,
          ellipsis: true,
          scopedSlots: { customRender: "newType" },
        },{
          title: "视图",
          dataIndex: "viewName",
          key: "viewName",
          sorter:true,
          ellipsis: true,
          scopedSlots: { customRender: "viewName" },
        },{
          title: this.$t('txt_version'),
          dataIndex: "version",
          key: "version",
          sorter:true,
          ellipsis: true,
          scopedSlots: { customRender: "version" },
        },{
          title: this.$t('txt_plan_lifecycle'),
          dataIndex: "lifecycle",
          key: "lifecycle",
          sorter:true,
          ellipsis: true,
          scopedSlots: { customRender: "lifecycle" },
        }],
      documentArr: [],
      cadDocumentArr: [],
      tableObjectData: [],
      productObject: {},
      form: {
        searchKey: "",
        page: 1,
        size: 10
      },
      selected3: {}
    };
  },

  inject: ["_selectedRows"],
  computed: {
    selectedRows() {
      let rows = this._selectedRows();
      console.log('rows', rows)
      if (!rows) return;
      this.collectBtn(rows[0]);
      return rows;
    }
  },

  methods: {
    async getObjectList(item) {
      let param = ["Part", "Document", "CADDocument"];
      let result = await searchObjectType.execute(param);
      this.getobjectData(result, item);
    },
    conversionData(childrenData, result) {
      var newTypeVal = "";
      for (let k = 0; k < childrenData.length; k++) {
        for (let key in result) {
          if (key == childrenData[k].modelType) {
            newTypeVal = result[key];
          }
        }
        childrenData[k].newType =  childrenData[k].classification_displayName ? childrenData[k].classification_displayName : newTypeVal;

        if (childrenData[k].children && childrenData[k].children.length > 0) {
             this.conversionData(childrenData[k].children, result);
        }
      }
    },
    getobjectData(result, item) {
      let param = {
        modelType: item.modelType,
        oid: item.oid,
        searchKey: this.form.searchKey
      };
      searchObjectInfo
        .execute(param)
        .then(data => {
          this.loading = false;
          for (let i = 0; i < data.length; i++) {
            var newTypeVal = "";
            for (let key in result) {
              if (key == data[i].modelType) {
                newTypeVal = result[key];
              }
            }
            data[i].newType = data[i].classification_displayName ? data[i].classification_displayName : newTypeVal;

            if (data[i].children && data[i].children.length > 0) {
               this.conversionData(data[i].children, result);
            }
          }
          this.objectTableData = data;
          this.newDataArr = data;

          console.log('this.newDataArr',this.newDataArr)
        })
        .catch(err => {
          this.loading = false;
          this.$error(err.msg || "searchObjectInfo接口异常");
        });
    },
    getRowKey(row) {
      return row.id;
    },

    switchData(item, index) {
      this.num = index;
      this.collectBtn(item);
    },

    jumplink(scoped) {
      var type = scoped.inheritModelType ? scoped.inheritModelType : scoped.modelType;
      type = type == "Part" ? "Part-detail" : "detail";
      let text = this.$router.resolve({
        path: `/${type}/${scoped.oid}/${scoped.modelType}/?from=Product`
      });

      window.open(text.href, "_blank");
    },

    collectBtn(item) {
      if (!item) return;
      this.selected3 = item;
      this.getObjectList(item);
      this.loading = true;
    },

    searchRecursive(tablechildren, val, j) {
      for (let z = 0; z < tablechildren.length; z++) {
        var flag = tablechildren[z].number.indexOf(val);
        var isflag = tablechildren[z].name.indexOf(val);
        if (flag >= 0 || isflag >= 0) {
          this.newDataArr.push(this.objectTableData[j]);
        } else {
          if (
            tablechildren[z].children &&
            tablechildren[z].children.length > 0
          ) {
            this.searchRecursive(tablechildren[z].children, val, j);
          }
        }
      }
    },
    searchBtn(val) {
      if (val) {
        this.newDataArr = [];
        for (let j = 0; j < this.objectTableData.length; j++) {
          var flag = this.objectTableData[j].number.indexOf(val);
          var isflag = this.objectTableData[j].name.indexOf(val);
          if (flag >= 0 || isflag >= 0) {
            this.newDataArr.push(this.objectTableData[j]);
          } else {
            if (
              this.objectTableData[j].children &&
              this.objectTableData[j].children.length > 0
            ) {
              this.searchRecursive(this.objectTableData[j].children, val, j);
            }
          }
        }
      } else {
        this.newDataArr = this.objectTableData;
      }
    }
  }
};
</script>


<style scoped>
.main-table-info{
  margin-top: 25px;
}
.process {
  margin-top: 10px;
  width: 100%;
  height: 50px;
  line-height: 50px;
  border: 1px solid rgba(30, 32, 42, 0.15);
  box-sizing: border-box;
  padding: 0 20px;
}
.object-name {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-left: 3px;
}
.product-name {
  display: flex;
  align-items: center;
}
.parts-icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.table3 >>> .el-table .el-table__row:not([class*='el-table__row--level-']) td:first-child{
  padding-left: 4px;
}
.page-list {
  display: flex;
  justify-content: flex-end;
  height: 32px;
  line-height: 32px;
  margin-right: 10px;
  padding: 0;
}

.page-info {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid rgba(30, 32, 42, 0.15);
  height: 72px;
  align-items: center;
}
.numbers {
  color: #409eff;
  cursor: pointer;
}
.product-info {
  display: flex;
  align-items: center;
}

.input-with-select {
  width: 216px;
  cursor: pointer;
}
.selected-object ul li.current {
  background: rgba(30, 32, 42, 0.06);
}
.list-icon {
  height: 16px;
  line-height: 16px;
}
.product-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}
.object-list {
  display: flex;
  align-items: center;
  width: 100%;
}
.search-info span {
  padding: 0;
  font-weight: 700;
  box-sizing: border-box;
}
.search-info span.activer{
  border-bottom: 2px solid #255ed7;
}
.search-info {
  height: 54px;
  line-height: 54px;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  align-items: center;
}
.main-table-list {
  border: 1px solid rgba(30, 32, 42, 0.15);
}
.main-table-info {
  padding: 0 20px;
}
.clear-btn {
  cursor: pointer;
}
.selected-object ul li a {
  cursor: pointer;
  color: #409eff;
}
.delete-icon img {
  width: 100%;
  height: 100%;
}
.delete-icon {
  width: 16px;
  height: 16px;
  display: flex;
  cursor: pointer;
}
.selected-object {
  border: 1px solid rgba(30, 32, 42, 0.15);
  overflow-y: auto;
}

.selected-object span {
  height: 55px;
  line-height: 55px;
  display: block;
  background: rgba(30, 32, 42, 0.03);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(30, 32, 42, 0.15);
}
.selected-object span h1 {
  font-size: 14px;
  color: rgba(30, 32, 42, 0.85);
  height: 54px;
  line-height: 54px;
  border-bottom: 2px solid #255ed7;
  font-weight: 700;
  margin: 0;
}

.selected-object span p {
  font-size: 14px;
  color: #255ed7;
  line-height: 22px;
  height: 22px;
  margin: 0;
}
.selected-object ul {
  margin: 0;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.selected-object ul li {
  height: 54px;
  line-height: 54px;
  list-style-type: none;
  border-bottom: 1px solid rgba(30, 32, 42, 0.15);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}
</style>