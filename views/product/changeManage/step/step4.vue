<template>
  <div>
    <div class="main-change">
      <a-row :gutter="20">
        <a-col :span="6">
          <objectList :selectedRows='selectedRows' :selectItem="selectItem" ref="objectlist" @getSelectList="getSelectList" :fullHeight="fullHeight"></objectList>
        </a-col>
        <a-col :span="18">
          <objectTable :fullHeight="fullHeight" ref="objectTable" :selectItem="selectItem" v-show="!_.isEmpty(selectItem)"></objectTable>
          <div v-if="_.isEmpty(selectItem)">{{$t('msg_nodata')}}</div>
        </a-col>
      </a-row>
    </div>

  </div>
</template>

<script>
import objectList from "./objectList";
import objectTable from "./objectTable";
export default {
  name: "createStep4",
  props: ["fullHeight"],
  components: {
    objectList,
    objectTable
  },
  data() {
    this.hasListFlag=true
    return {
      selectItem: {},
      
    };
  },
  inject: ["_selectedRows", "_initSelectedRows"],
  computed: {
    selectedRows() {
      let rows = this._selectedRows();
      if (!rows) return;
      console.log("计算属性",this.selectItem);
      return rows;
    },
    initSelectedRows() {
      let initRows = this._initSelectedRows();
      if (!initRows) return;

      return initRows;
    }
  },

  watch: {},

  methods: {
    getSelectList(item, flag=true) {
      // 在切换数据的时候去比较数据是否有修改
      // let init = this.initSelectedRows.find(item => {
      //   return item.oid == this.selectItem.oid;
      // });
      // let isModify = this.diff(init, this.selectItem);

      // if (!isModify) {
      //   this.$set(this.selectItem, "isModify", true);
      // } else {
      //   this.selectItem.isModify &&
      //     this.$set(this.selectItem, "isModify", false);
      // }

      this.selectItem = item;
      this.hasListFlag = flag;
    },

    diff(source, target) {
      let s = _.cloneDeep(source);
      let t = _.cloneDeep(target);
      s = _.omit(s, "isModify", "hasChildren");
      s = _.pick(s, function(value, key, object) {
        if (_.isObject(value) || _.isArray(value)) {
          return !_.isEmpty(value);
        } else {
          return value;
        }
      });
      t = _.omit(t, "isModify", "hasChildren");
      t = _.pick(t, function(value, key, object) {
        if (_.isObject(value) || _.isArray(value)) {
          return !_.isEmpty(value);
        } else {
          return value;
        }
      });
      return _.isEqual(s, t);
    }
  }
};
</script>

<style lang="less" scoped>
.main-change {
  margin: 25px 20px 16px 20px;
}
.create-btn {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>