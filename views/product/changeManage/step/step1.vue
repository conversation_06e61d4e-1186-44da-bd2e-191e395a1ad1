<template>
  <div>
    <div class="step-details" :style="{height:fullHeight  - 320 + 'px'}">
      <a-form-model
        ref="formlist"
        :model="form"
        :rules="rules"
        :layout="layout"
      >
        <a-form-model-item ref="name" label="名称:" prop="name">
          <a-input v-model.trim="form.name" placeholder="请输入" class="long-int" />
        </a-form-model-item>


        <a-form-model-item label="类型:" prop="typeCode">
          <a-select
            v-model.trim="form.typeCode"
            placeholder="请选择"
            class="long-int"
          >
          <a-select-option :value="item.code" v-for="(item, index) in typeList" :key="index">{{item.label}}</a-select-option>     
          </a-select>
        </a-form-model-item>

        <a-form-model-item label="紧急程度:" prop="degreeEmergencyCode">
          <a-select
            v-model.trim="form.degreeEmergencyCode"
            class="long-int"
            placeholder="请选择"
          >
            <a-select-option  v-for="(item, index) in emergencyList" :key="index"
                :value="item.code">{{ item.label }}</a-select-option>
          </a-select>
        </a-form-model-item>

        <a-form-model-item label="关联问题:" prop="issueList">
          <a-select
            mode="multiple"
            placeholder="请选择"
            v-model.trim="form.issueList"
            class="long-int"
            @search="handleSearch"
          >
            <a-select-option
              v-for="item in options"
              :key="item.oid"
              :value="item.name"
            >
              {{ item.issueNumber }} {{item.name}}
            </a-select-option>
          </a-select>
        </a-form-model-item>

        <a-form-model-item label="变更原因:" prop="changeReason">
          <a-input
            v-model.trim="form.changeReason"
            type="textarea"
            class="long-int"
          />
        </a-form-model-item>

        <a-form-model-item label="详细描述:" prop="description">
          <a-input
            v-model.trim="form.description"
            type="textarea"
            class="long-int"
          />
        </a-form-model-item>

        <a-form-model-item label="附件:" prop="file">
          <a-upload-dragger
            name="file"
            :multiple="true"
            :action="uploadUrls"
            :beforeUpload="beforeUpload"
            class="long-int"
          >
            <div class="file-list">
              <div class="file-describe">
                <p class="ant-upload-drag-icon">
                  <img
                    src="../../../../assets/image/uploadImg.png"
                    class="upload-img"
                  />
                </p>
                <p class="ant-upload-hint">
                  将文件拖拽到此处，或<em class="upload-btn">点击上传</em>
                </p>
              </div>
              <div>
                <p class="file-detail">（上传文件大小不能超过20M）</p>
              </div>
            </div>
          </a-upload-dragger>
        </a-form-model-item>

      
      </a-form-model>
    </div>
  </div>
</template>


<script>

import { searchBaseType, searchEmergency, searchAssociated } from 'apis/product/changeManage'
export default {
  name: "step1",
  props: ["fullHeight"],
  data() {
    return {
      uploadUrls: `${Jw.gateway}/${Jw.fileServer}/file/multiUpload-v2`,
      selectedItems: [],
      options:[],
      fileList: [],
      typeList:[],
      emergencyList: [],
      layout: "vertical",
      form: {
        name: "",
        // theme: "",
        typeValue: "",
        typeCode: "",
        degreeEmergencyCode: "",
        degreeEmergencyValue: "",
        issueList: [],
        description: "",
        modelType: "ECR",
        changeReason: "",
        appendixs: [],
        file: "",
      },
      files: [],
      rules: {
        name: [{ required: true, message: "请输入名称", trigger: "change" }],
        theme: [
          { required: true, message: "请输入变更主题", trigger: "change" },
        ],
        typeCode: [
          { required: true, message: "请选择类型", trigger: "change" },
        ],
        degreeEmergencyCode: [
          { required: true, message: "请选择紧急程序", trigger: "change" },
        ],
      },
    }
  },
  mounted(){
     this.fetchBaseType()
     this.fetchEmergency()
   
  },
 
  methods: {
    cancerBtn(){
         this.$router.push('/changeManage')
    },
     fetchBaseType(){  //获取基本类型
         searchBaseType
          .execute({
            currPage: 1,
            pageSize: 15,
          })
          .then((data) => {
              let typeList = [];
              data.forEach(element => {
                  typeList.push({
                      code: element.valueCode,
                      label: element.multiLanguageValue[1].valueText,
                  })
              });
              this.typeList = typeList
              console.log('this.typeList', this.typeList)
          })
          .catch((err) => {
            this.$error(err.msg);
          });
      },
      fetchEmergency(){   //紧急程度
          searchEmergency
          .execute({ currPage: 1, pageSize: 15 })
          .then((data) => {
            for (let item of data) {
              this.emergencyList.push({
                code: item.valueCode,
                label: item.multiLanguageValue[1].valueText,
              })

              console.log('this.emergencyList',this.emergencyList)
            }
          })
          .catch((err) => {
            this.$error(err.msg);
          });
      },
     querySearchAsync(queryString){
        let parma = {
          pageNum: 1, pageSize: 15, projectOid: "", keyword: queryString
        }
        searchAssociated
          .execute(parma)
          .then((data) => {
            let lister = data.rows
            if (lister.length > 0) {
              this.options = lister
              console.log('options', this.options)
            }
          })
          .catch((err) => {
            if (err.msg) {
              this.$error(err.msg);
            }
          });
     },
    handleSearch(query) {
       if (query !== '') {
          this.loading = true;
          setTimeout(() => {
            this.loading = false;
            this.querySearchAsync(query)
          }, 200);
        } else {
          this.options = [];
        }
    },
    onSubmit() {
      console.log('this.form', this.form)
      this.$emit('nextStep1',1)
    },
    beforeUpload(param) {
      const isLt20M = param.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message({
          message: "上传文件大小不能超过 2MB!",
          type: "error",
        });
        return false;
      }
      this.files.push(param);
    },
    nextStep1() {
      if (this.files.length > 0) {
        let formData = new FormData();
        let xhr = new XMLHttpRequest();
        this.files.forEach(function (file) {
          formData.append("file", file, file.name); // 因为要上传多个文件，所以需要遍历一下才行
        });
        xhr.open(
          "POST",
          `${Jw.gateway}/${Jw.fileServer}/file/multiUpload-v2`,
          true
        );
        xhr.onload = (res) => {
          const response = JSON.parse(xhr.response);
          this.form.appendixs = response.result;
          this.onSubmit();
        };
        xhr.send(formData);
      } else {
        this.onSubmit();
      }
    },
  },
};
</script>

<style scoped>
.long-int >>> .ant-upload.ant-upload-drag {
  width: 552px;
}
.cancer {
  width: 57px;
  background: #fff;
}
.long-int {
  width: 552px;
}
.new-btn {
  width: 66px;
  height: 32px;
  background: #255ed7;
  border-radius: 4px;
  text-align: center;
  padding: 0;
  margin-right: 8px;
}
.create-btn {
  height: 72px;
  border-top: 1px solid rgba(30, 32, 42, 0.15);
  padding: 0 0 0 20px;
  margin-left: -20px;
  display: flex;
  align-items: center;
}
.file-detail {
  font-size: 12px;
  color: rgba(30, 32, 42, 0.45);
}
.file-describe {
  display: flex;
  align-items: center;
}
.upload-btn {
  font-style: normal;
  color: #255ed7;
}
.file-list >>> p.ant-upload-drag-icon {
  margin-bottom: 0;
  margin-right: 14px;
}
.file-list {
  display: flex;
  align-items: center;
  padding: 0 20px;
  justify-content: space-between;
}
.upload-img {
  width: 28px;
  height: 28px;
}
.step-details >>> .ant-col.ant-form-item-label {
  height: 22px;
  line-height: 22px;
  padding: 0;
  margin-bottom: 8px;
  font-size: 14px;
  color: rgba(30, 32, 42, 0.65);
}
.step-details {
  margin-top: 24px;
  padding: 0 0 0 20px;
  overflow-y: auto;
}
</style>