<template>
  <div>
    <div class="main-object">
      <div class="main-table-info">
        <a-row type="flex" justify="start" :gutter="0">
          <a-col :span="18">
            <div class="table-object">
              <div class="catetory">
                <div class="cate-info">
                  <a-tabs default-active-key="1" @change="callback">
                    <a-tab-pane key="1" tab="Part/Bom"></a-tab-pane>
                    <a-tab-pane key="2" tab="CAD" force-render></a-tab-pane>
                    <a-tab-pane key="3" :tab="$t('txt_document')"></a-tab-pane>
                    <a-tab-pane key="4" tab="ECAD"></a-tab-pane>
                  </a-tabs>
                </div>

                <div class="search-info">
                  <a-input-search
                    placeholder="输入关键词搜索"
                    class="search-btn"
                    v-model.trim="form.searchKey"
                    allowClear
                    @search="onSearch"
                  />
                </div>
              </div>

              <div class="main-table">
                <a-table
                  :columns="columns"
                  :pagination="false"
                  row-key="id"
                  :scroll="{ y: fullHeight - 492 }"
                  :style="{ height: fullHeight - 438 + 'px' }"
                  :row-selection="{
                    selectedRowKeys: selectedRowKeys,
                    onChange: onSelectChange,
                    onSelect: onSelect,
                  }"
                  :data-source="tableData"
                >
                  <template slot="name" slot-scope="scoped, record">
                    <i :class="record.iconURL" />
                    <span>{{ record.name }}</span>
                  </template>

                  <template slot="containerName" slot-scope="scoped">
                    <span>{{ scoped }}</span>
                  </template>

                  <template slot="number" slot-scope="scoped, record">
                    <span
                      class="numbers"
                      v-if="record.newModelType == 'Part'"
                      @click="jumplink('part-detail', record)"
                      >{{ record.number }}</span
                    >
                    <span
                      v-else
                      class="numbers"
                      @click="jumplink('detail', record)"
                      >{{ scoped }}</span
                    >
                  </template>

                  <template slot-scope="scoped, record" slot="lifecycle">
                    <span v-if="record.lifecycle == 'Open'">未提交</span>
                    <span v-else-if="record.lifecycle == 'UnderReview'"
                      >正在审批</span
                    >
                    <span v-else-if="record.lifecycle == 'Estimate'"
                      >变更评估</span
                    >
                    <span v-else-if="record.lifecycle == 'Inwork'"
                      >正在执行</span
                    >
                    <span v-else-if="record.lifecycle == 'Released'"
                      >已发布</span
                    >
                    <span v-else class="cancel">已取消</span>
                  </template>
                </a-table>

                <div class="page-info">
                  <div class="page-list">
                    <a-pagination
                      show-size-changer
                      :default-current="1"
                      :total="total"
                      @change="onChangePage"
                      @showSizeChange="onShowSizeChange"
                    />
                  </div>
                </div>
              </div>
            </div>
          </a-col>
          <a-col :span="6">
            <div
              class="selected-object"
              :style="{ height: fullHeight - 320 + 'px' }"
            >
              <span
                ><h1>
                  <a-checkbox
                    :indeterminate="isIndeterminate"
                    v-model.trim="checked_All_Flag"
                    @change="handleCheckAllChange"
                    class="all-checkbox"
                  ></a-checkbox>
                  已选择{{ selectData.length }}个
                </h1>
                <p @click="clearBtn" class="clear-btn">清空</p></span
              >
              <ul>
                <li v-for="(item, index) of selectData" :key="index">
                  <a-checkbox
                    class="checkbox-list"
                    :checked="item.checked"
                    @change="changeCheckbox(item, index)"
                    :key="item.oid + item.id"
                  ></a-checkbox>
                  <div class="select-data-left">
                    <!-- <i :class="item.iconURL"></i> -->
                    <div
                      class="numbers"
                      v-if="item.newModelType == 'Part'"
                      @click="jumplink('part-detail', item)"
                      :title="
                        item.name + ',' + item.number + ',' + item.version
                      "
                    >
                      {{ item.name }},{{ item.number }},{{ item.version }}
                    </div>
                    <div
                      class="numbers"
                      @click="jumplink('detail', item)"
                      v-else
                      :title="
                        item.name + ',' + item.number + ',' + item.version
                      "
                    >
                      {{ item.name }},{{ item.number }},{{ item.version }}
                    </div>
                  </div>
                  <div
                    class="select-data-delete"
                    @click="deleteBtn(item, index)"
                  >
                    <img src="../../../../assets/image/delete.png" />
                  </div>
                </li>
              </ul>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>
  </div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
//获取变更对象
const getChangeObjecte = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.changeServer}/ecr/getCandidate/byModelType`,
  method: "post",
});

//获取产品库
const getProductData = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomServer}/part/searchProduct`,
  method: "post",
});

export default {
  name: "step2",
  props: ["fullHeight"],
  data() {
    this._from = this.$route.query.from;
    return {
      isSelect: true,
      pagination: true,
      selectData: [],
      selectedRowKeys: [],
      tableData: [],
      total: 0,
      filteredInfo: null,
      sortedInfo: null,
      columns: [
        {
          title: this.$t('txt_name'),
          dataIndex: "name",
          key: "name",
          sorter: true,
          ellipsis: true,
          scopedSlots: { customRender: "name" },
        },
        {
          title: "所属名称",
          dataIndex: "containerName",
          key: "containerName",
          sorter: true,
          ellipsis: true,
          scopedSlots: { customRender: "containerName" },
        },
        {
          title:this.$t('txt_number'),
          dataIndex: "number",
          key: "number",
          sorter: true,
          ellipsis: true,
          scopedSlots: { customRender: "number" },
        },
        {
          title: "视图",
          dataIndex: "viewName",
          key: "viewName",
          sorter: true,
          ellipsis: true,
        },

        {
          title: this.$t('txt_plan_lifecycle'),
          dataIndex: "lifecycle",
          key: "lifecycle",
          ellipsis: true,
          scopedSlots: { customRender: "lifecycle" },
        },
        {
          title: this.$t('txt_version'),
          dataIndex: "version",
          key: "version",
          sorter: true,
          ellipsis: true,
        },
      ],
      typeVal: "Part",
      xh: 0,
      form: {
        modelType: "",
        searchKey: "",
        page: 1,
        size: 10,
        containerOids: [],
      },
      isIndeterminate: false,
      checked_All_Flag: false,
    };
  },

  methods: {
    changeCheckbox(item, index) {
      var checkedVal = item.checked;
      this.$set(this.selectData[index], "checked", checkedVal ? false : true);
      var len = this.selectData.length;
      var checkedLen = 0;
      for (let k = 0; k < len; k++) {
        if (this.selectData[k].checked) {
          checkedLen++;
        }
      }
      if (checkedLen == 0) {
        this.isIndeterminate = false;
      } else {
        if (checkedLen == len) {
          this.isIndeterminate = true;
          this.checked_All_Flag = true;
        } else {
          this.isIndeterminate = true;
          this.checked_All_Flag = false;
        }
      }
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    onSelect(record, selected) {
      if (selected) {
        this.selectData.push(record);
      } else {
        this.selectData = this.selectData.filter((item) => {
          item.checked = false;
          return ![record].find((row) => {
            return item.id == row.id;
          });
        });
      }
    },
    handleCheckAllChange(val) {
      if (val.target.checked) {
        //选中
        for (let i = 0; i < this.selectData.length; i++) {
          if (!this.selectData[i].checked) {
            this.$set(this.selectData[i], "checked", true);
          }
        }
      } else {
        //取消
        for (let k = 0; k < this.selectData.length; k++) {
          if (this.selectData[k].checked) {
            this.$set(this.selectData[k], "checked", false);
          }
        }
      }
    },
    deleteBtn(item, index) {
      //单个删除
      this.selectedRowKeys = this.selectedRowKeys.filter((itemlist) => {
        return ![item].find((row) => {
          return row.id == itemlist;
        });
      });
      this.selectData.splice(index, 1);
    },

    jumplink(type, scoped) {
      let text = this.$router.resolve({
        path: `/${type}/${scoped.oid}/${scoped.modelType}/?from=Product`,
      });
      window.open(text.href, "_blank");
    },
    fetchProduct() {
      //查询产品库
      let param = {
        page: 1,
        searchKey: "",
        size: 999,
      };
      getProductData
        .execute(param)
        .then((data) => {
          this.productlist = data.rows;
        })
        .catch((err) => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
    callback(key) {
      this.form.searchKey = ''
      this.num = key;
      switch (key) {
        case "1":
          this.typeVal = "Part";
          break;
        case "2":
          this.typeVal = "CADDocument";
          break;
        case "3":
          this.typeVal = "Document";
          break;
        case '4':
          this.typeVal = 'ECAD'
          break
        default:
          return ''
      }
      this.form.modelType = this.typeVal;
      this.form.page = 1;
      this.fetchSearchInfo();
    },
    clearBtn() {
      this.selectData = [];
      this.selectedRowKeys = [];
    },
    fetchSearchInfo() {
      //查询搜索
      // this.form.searchKey = searchVal;
      this.loading = true;
      getChangeObjecte
        .execute(this.form)
        .then((data) => {
          this.loading = false;
          let newArr = data.data;
          for (let i = 0; i < newArr.length; i++) {
            newArr[i].newModelType = newArr[i].inheritModelType
              ? newArr[i].inheritModelType
              : newArr[i].modelType;
          }

          this.tableData = newArr;
          // if (this.form.searchKey) {
          //   this.form.searchKey = "";
          // }
          this.total = data.count;

          console.log("this.total", this.total);
        })
        .catch((err) => {
          this.loading = false;
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    },
    searchBtn(searchVal) {
      this.form.modelType = this.typeVal;
      this.currentpage = 1;
      this.form.page = 1;
      this.fetchSearchInfo(searchVal);
    },
    cancerBtn() {
      this.$router.push("/changeManage");
    },

    onShowSizeChange(pageSize, pageNum) {
      this.form.page = pageSize;
      this.form.size = pageNum;
      this.fetchSearchInfo();
    },
    onSearch(val) {
      // this.from.searchKey = val;
      this.fetchSearchInfo();
    },

    onChangePage(val) {
      this.form.page = val;
      this.fetchSearchInfo();
    },
  },
  mounted() {
    this.form.modelType = this.typeVal;
    this.fetchSearchInfo("");
    this.fetchProduct();
    let select = sessionStorage.getItem('changeStepData') || '{}';
    let selectVal = JSON.parse(select);
    this.selectData = selectVal && Array.isArray(selectVal)?selectVal:[{...selectVal}]
  },

  onSearch(keyword) {
    console.log("keyword", keyword);
  },
};
</script>

<style lang="less">
.select-list {
  li {
    .checkbox-list {
      display: flex;
      align-items: center;
      overflow: hidden;
      .el-checkbox__label {
        display: flex;
        overflow: hidden;
      }
    }
  }
}
</style>


<style scoped>
.main-table-info {
  margin-top: 25px;
}
.select-data-left {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
}
.select-data-delete {
  width: 16px;
  height: 54px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.select-data-delete img {
  width: 16px;
  height: 16px;
}
.deleteBorder.el-tabs_nav-wrap::after {
  height: 0;
}
.all-checkbox {
  margin-right: 8px;
}
.checkbox-list {
  margin-right: 8px;
}
.select-icon {
  display: flex;
  align-items: center;
  width: 100%;
}
.product-info {
  display: flex;
  align-items: center;
}
.list-icon {
  height: 16px;
  line-height: 16px;
  margin-right: 8px;
}
.product-icon {
  width: 16px;
  height: 16px;
}
.clear-btn {
  cursor: pointer;
}
.selected-object ul li a {
  cursor: pointer;
  color: #409eff;
}
.delete-icon img {
  width: 16px;
  height: 16px;
}
.delete-icon {
  width: 16px;
  height: 54px;
  display: flex;
  cursor: pointer;
  align-items: center;
}
.selected-object {
  border: 1px solid rgba(30, 32, 42, 0.15);
  margin-right: 20px;
  overflow-y: auto;
}

.selected-object span {
  height: 55px;
  line-height: 55px;
  display: block;
  background: rgba(30, 32, 42, 0.03);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(30, 32, 42, 0.15);
}
.selected-object span h1 {
  font-size: 14px;
  color: rgba(30, 32, 42, 0.85);
  line-height: 22px;
  height: 22px;
  font-weight: 700;
  margin: 0;
}

.selected-object span p {
  font-size: 14px;
  color: #255ed7;
  line-height: 22px;
  height: 22px;
  margin: 0;
}
.selected-object ul {
  margin: 0;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.selected-object ul li {
  height: 54px;
  line-height: 54px;
  list-style-type: none;
  border-bottom: 1px solid rgba(30, 32, 42, 0.15);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}

.page-list {
  display: flex;
  justify-content: flex-end;
  height: 32px;
  line-height: 32px;
  margin-right: 10px;
  padding: 0;
}

.page-info {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  display: flex;
  justify-content: flex-end;
  height: 72px;
  align-items: center;
}
.input-with-select {
  width: 216px;
}
.cate-info >>> .ant-tabs-bar {
  margin: 0;
}
.catetory {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 44px 0 0;
  width: 100%;
  box-sizing: border-box;
}
.cate-info >>> .el-tabs__header {
  margin: 0 0 0px;
  height: 54px;
  line-height: 54px;
}
.table-object {
  margin: 0 20px;
  border: 1px solid rgba(30, 32, 42, 0.15);
  padding: 0 0px;
  overflow-y: scroll;
}
.numbers {
  color: #409eff;
  cursor: pointer;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: 3px;
}
.page-info {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.menu-list li.activer {
  border-bottom: 1px solid #66b1ff;
}
.menu-list li {
  width: 33.33%;
  list-style-type: none;
  height: 40px;
  line-height: 40px;
  float: left;
  text-align: center;
  cursor: pointer;
}
.menu-list {
  height: 40px;
  line-height: 40px;
  width: 100%;
  box-sizing: border-box;
  border-bottom: 1px solid #ddd;
  display: flex;
  margin: 0 10px;
}
.create-btn {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
.total span {
  display: block;
  height: 40px;
  line-height: 40px;
}
.total {
  height: 56px;
  line-height: 56px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.add-product {
  height: 32px;
}
.input-with-select {
  width: 320px;
}
.main-table {
  padding: 0;
}
.search-list {
  padding: 10px 0;
  height: 30px;
  line-height: 50px;
  display: flex;
  justify-content: flex-end;
}
</style>