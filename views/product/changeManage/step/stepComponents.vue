<template>
    <div>
        <div class="step-main-info">
            <ul>
                <li :class="{current: num==index}" v-for="(item, index) of stepList" :key="item.id">
                    <div class="numbers" v-if="index >= active">{{item.numbers}}</div>
                    <div class="correct" v-else><img src="../../../../assets/image/correct.png"></div>
                    <div class="titles">{{item.name}}</div>
                    <template v-if="index<3">
                    <div class="jiantou" v-if="num==index"><img src="../../../../assets/image/current-right.png"></div>
                    <div class="jiantou" v-else><img src="../../../../assets/image/right.png"></div>
                    </template>
                </li>
            </ul>
        </div>
    </div>
</template>


<script>
export default {
    name: 'stepComponents',
    props:['active'],
    data(){
        return {
            num:0,
            stepList:[{
                id: 1,
                numbers: '1',
                name: this.$t('txt_change_info'),
            },{
                id: 2,
                numbers: '2',
                name: this.$t('change_select_obj'),
            },{
                id: 3,
                numbers: '3',
                name: this.$t('change_select_obj'),
            },{
                id: 4,
                numbers: '4',
                name: this.$t('change_fill_program'),
            }]
        }
    },
    watch: {
        active(newV) {
            this.num = newV
        }
    }
  
}
</script>

<style scoped>
.jiantou{
    width: 16px;
    height: 16px;
    margin-top: 5px;
}
.jiantou img {
    width: 100%;
    height: 100%;
}
.correct{
    width: 24px;
    height: 24px;
    margin-right: 8px;
}
.correct img {
    width: 100%;
    height: 100%;
}
.step-main-info ul li.current {
    background: #F0F7FF;
    border: 1px solid #A4C9FC;
    border-radius: 4px;
    box-sizing: border-box;
}
.step-main-info ul li.current div.titles{
    color: rgba(30,32,42,0.85);
    font-weight: 700;
}
.step-main-info ul li.current div.numbers {
    background: #255ED7;
    color: #fff;
}
.step-main-info ul li div.titles {
    font-size: 14px;
    color: rgba(30,32,42,0.85);
    margin-right: 16px;
}
.step-main-info ul li div.numbers{
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: rgba(30,32,42,0.15);
    text-align: center;
    line-height: 24px;
    margin-right: 8px;
    color: rgba(30,32,42,0.25);
}
.step-main-info ul li {
    width: 25%;
    height: 60px;
    list-style-type: none;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    cursor: pointer;
    
}
.step-main-info ul {
    margin: 0;
    display: flex;
}
 .step-main-info {
     height: 60px;
     background: rgba(30,32,42,0.06);
     border-radius: 4px;
     width: 100%;
 }

</style>