<template>
  <div class="main-object" :style="{height: fullHeight - 323 + 'px' }">
    <div class="titles">
      <h1>变更对象</h1>
      <div class="object-cate">
        <template v-for="(item, index) of btnList">
          <span :class="{current: num==index}" @click="getchange(item,index)" v-if="item.type=='text'" :key="item.id">{{item.info}}</span>
          <span :title="item.show" :class="{current: num==index}" @click="getchange(item,index)" v-else :key="item.id"><img :src="item.info"></span>
        </template>
      </div>
    </div>

    <ul>
      <template>
        <li v-for="(item, index) in type_list" :key="item.id" :class="{current: item.oid==selectItem.oid}" @click="switchobject(item, index)">
          <div class="select-icon">
            <i :class="item.iconURL"></i>
            <div class="object-name" :title="item.name + ', '+ item.number + ', ' + item.version">{{item.name}}, {{item.number}}, {{item.version}}</div>
          </div>
        </li>
      </template>
    </ul>
  </div>
</template>


<script>
import ModelFactory from "jw_apis/model-factory"
const fetchPartInfo = ModelFactory.create({//保存
  url: `${Jw.gateway}/${Jw.baseServer}/layout/searchByName`,
  method: "get"
});

export default {
  name: "objectList",
  props: ["fullHeight", "selectedRows", "selectItem"],
  data() {
    return {
      num: 0,
      btnList: [
        {
          id: "1",
          type: "text",
          show: "",
          info: "All"
        },
        {
          id: "2",
          type: "img",
          show: "Part",
          info: require("../../../../assets/image/finished.png")
        },
        {
          id: "3",
          type: "img",
          show: "Document",
          info: require("../../../../assets/image/document.png")
        },
        {
          id: "4",
          type: "img",
          show: "CADDocument",
          info: require("../../../../assets/image/cadocument.png")
        }
      ],
      objectListArr: [],
      typeval: ""
    };
  },

  computed: {
    type_list() {
      let list = this.selectedRows.filter(row => {
        return this.typeval ? row.newModelType == this.typeval : true;
      });
      this.$emit("getSelectList", list[0] || {}, list[0] ? true : false);
      return list||[];
    }
  },
  methods: {
    getchange(item, index) {
      this.num = index;
      this.typeval = item.show;
    },

    switchobject(item, index) {
      if (this.selectItem.oid == item.oid) return;
      //点击切换数据

      this.$emit("getSelectList", item);
    }
  }
};
</script>


<style scoped>
.edit-info {
  height: 16px;
  line-height: 16px;
  display: flex;
  align-items: center;
}
.list-icon {
  height: 16px;
  line-height: 16px;
  margin-right: 8px;
}
.select-icon {
  display: flex;
  align-items: center;
  flex: 0.9;
  width: 90%;
}
.object-name {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-left: 3px;
}
.product-icon {
  width: 16px;
  height: 16px;
}
.object-cate span {
  height: 32px;
  padding: 0 8px;
  display: flex;
  align-items: center;
  border-right: 1px solid rgba(30, 32, 42, 0.15);
  cursor: pointer;
  box-sizing: border-box;
}
.object-cate span:last-child {
  border-right: none;
}
.object-cate span:first-child.current {
  border: 1px solid #255ed7;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.object-cate span.current {
  border: 1px solid #255ed7;
}
.object-cate span:last-child.current {
  border: 1px solid #255ed7;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.object-cate span img {
  width: 16px;
  height: 16px;
}
.object-cate {
  height: 32px;
  border-radius: 4px;
  display: flex;
  border: 1px solid rgba(30, 32, 42, 0.15);
  align-items: center;
  overflow: hidden;
}
.titles h1 {
  height: 54px;
  line-height: 54px;
  font-size: 14px;
  color: rgba(30, 32, 42, 0.85);
  border-bottom: 2px solid #255ed7;
}
.titles {
  height: 54px;
  line-height: 54px;
  padding: 0 24px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(30, 32, 42, 0.15);
  justify-content: space-between;
  width: 100%;
  box-sizing: border-box;
}
.main-object ul {
  margin: 0;
  display: flex;
  flex-direction: column;
}

.main-object ul li.current {
  background: rgba(30, 32, 42, 0.06);
}
.main-object ul li {
  height: 54px;
  line-height: 54px;
  border-bottom: 1px solid rgba(30, 32, 42, 0.15);
  display: flex;
  align-items: center;
  padding: 0 24px;
  justify-content: space-between;
  cursor: pointer;
}
.main-object {
  border: 1px solid rgba(30, 32, 42, 0.15);
  overflow-y: auto;
}
</style>
