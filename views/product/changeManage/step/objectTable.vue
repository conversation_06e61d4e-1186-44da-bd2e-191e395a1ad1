<template>
  <div>
    <div class="main-object">
      <div class="attr-list">

        <template v-if="attrListArr.length > 0">
          <li v-for="(item, index) of attrListArr" :key="item.id" @click="switchAttr(index)" :class="{current: num==index}">
            <span>{{item.name}}</span>
          </li>
        </template>
        <template v-else>
          <div class="name-info">
            <span>{{ selectItem.name}}</span><img src="../../../../assets/image/help-icon.png" @click="drawer = true" class="help-icon"></div>
        </template>
      </div>

      <div class="main-layout" v-show="attrListArr.length > 0">

        <!-- <ModuleView class="relation-wrapper" :style="{height: fullHeight - 368 + 'px'}" v-show="num==0">
          <ServiceComponentBuilder ref="versionBuilderss" slot="middle" layoutName="edit" @change="handleChange" :instanceData="selectItem" :modelName="typeVal"></ServiceComponentBuilder>
        </ModuleView> -->

        <bomDetail :bomData="bomData" ref="bomlist" :fullHeight="fullHeight" v-show="num==1"></bomDetail>
      </div>

      <div v-show="attrListArr.length ==0">
        <CADdocumentDetail style="overflow: auto;" ref="CADDocument" v-if="typeVal=='CADDocument'" @change="changecad" :key="selectItem.oid" :cadDocumentData="selectItem" :fullHeight="fullHeight + 63"></CADdocumentDetail>

        <div class="main-layout" v-if="typeVal=='Document'">
          <!-- <ModuleView class="relation-wrapper" :style="{height: fullHeight - 368 + 'px'}">
            <ServiceComponentBuilder ref="versionBuilder" slot="middle" layoutName="edit" @change="handleChange" :instanceData="instancePartData" :modelName="typeVal"></ServiceComponentBuilder>
          </ModuleView> -->
        </div>
      </div>

      <a-drawer :title="$t('detailed_info')"  :visible.sync="drawer" :with-header="false">
        <div class="details">
          <!-- <ServiceComponentBuilder class="part-info-swapper" ref="detail" :layoutName="layoutName" :modelName="modelName" :instanceData="selectItem"></ServiceComponentBuilder> -->
        </div>
      </a-drawer>

    </div>
  </div>
</template>

<script>
// import ModuleView from "jw_components/module-view";
// import ServiceComponentBuilder from "abuilder_components/service-component-builder";
import documentDetail from "../plan/documentDetail";
import CADdocumentDetail from "../plan/CADdocumentDetail";
import bomDetail from "../plan/bomDetail";
export default {
  name: "objectTable",
  props: ["fullHeight", "selectItem"],
  data() {
    return {
      drawer: false,
      layoutName: "show",
      typeVal: null,
      modelName: "",
      instanceData: {},
      instancePartData: {},
      num: 0,
      attrListArr: [],
      bomData: {},
      cadDocumentData: {}
    };
  },
  watch: {
    selectItem: {
      handler(newV) {
        this.typeVal = newV.newModelType;
        this.modelName = this.typeVal;
        this.bomData = newV;
        this.instancePartData = newV;
        this.cadDocumentData = newV;
        if (this.typeVal != "Part") {
          this.attrListArr = [];
        } else {
          this.attrListArr = [
            {
              id: 1,
              name: this.$t('txt_property'),
            },
            {
              id: 2,
              name: "BOM"+this.$t('txt_structure'),
            }
          ];
        }
      },
      immediate: true
    }
  },
  components: {
    // ServiceComponentBuilder,
    // ModuleView,
    documentDetail,
    CADdocumentDetail,
    bomDetail
  },
  methods: {
    handleChange(val) {
      Object.assign(this.selectItem, val);
    },

    changecad(val) {},

    switchAttr(index) {
      this.num = index;
      if (index == 1) {
        this.bomData = this.selectItem;
      }
    }
  }
};
</script>


<style scoped>
.details {
  padding: 20px;
}
.main-layout {
  border: 1px solid rgba(30, 32, 42, 0.15);
}
.name-info {
  display: flex;
  align-items: center;
  margin-left: 8px;
}
.help-icon {
  width: 16px;
  height: 16px;
  margin-left: 10px;
}
.main-layout {
  padding: 0 20px;
  box-sizing: border-box;
}
.attr-list li.current span {
  border-bottom: 2px solid #255ed7;
}
.attr-list li span {
  display: block;
  height: 52px;
  line-height: 52px;
}
.attr-list li {
  list-style-type: none;
  padding: 0 10px;
  font-size: 14px;
  color: rgba(30, 32, 42, 0.85);
  cursor: pointer;
}
.attr-list {
  height: 54px;
  line-height: 54px;
  display: flex;
  padding: 0 10px;
  border: 1px solid rgba(30, 32, 42, 0.15);
  border-bottom: none;
}
</style>