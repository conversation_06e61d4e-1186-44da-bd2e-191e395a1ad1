<template>
  <div>
    <div class="search">
      <div class="search-new">
        <a-button type="primary" class="new-btn" @click="creatBtn"
          >{{$t('btn_new_create')}}</a-button
        >
        <a-input-search
          :placeholder="$t('txt_search')"
          class="search-btn"
          @search="onSearch"
        />
      </div>
      <div class="export-btn">
        <a-dropdown >
          <a-button>{{$t('btn_batch_operation')}}<a-icon type="down" /></a-button>
          <a-menu slot="overlay">
            <a-menu-item @click="switchChange" v-for="item of batchArr" :key="item.id" :disabled="item.disabled">{{item.name}}</a-menu-item>
          </a-menu>
        </a-dropdown>
      </div>
     </div>

   <!-- 创建基线-->
   <NewBaseDialog :visible="visible" @closed="closed"></NewBaseDialog>

   <!-- 添加至基线-->
   <addedToBase :visibleBase="visibleBase" @closed="closedBase"></addedToBase>

  </div>
</template>

<script>
import NewBaseDialog from './addNewBase'
import addedToBase from './addedToBase'
export default {
  name: "search",
  props: ['selectData'],
  components: {
      NewBaseDialog,
      addedToBase
  },
  data() {
    return {
      visible:false,
      visibleBase:false,
      batchArr:[{
        id:'1',
        name:this.$t('txt_add_to_baseline'),
        disabled: false,
      },{
        id:'2',
        name:this.$t('txt_start_workflow'),
        disabled: false,
      },{
        id:'3',
        name:this.$t('btn_delete'),
        disabled: false,
      }],
    };
  },
  methods: {
    closed(flag){
       this.visible = flag;
    },
    closedBase(flag){
         this.visibleBase = flag
    },
    switchChange(selectval){
       switch(selectval.key) {
         case '1':  //添加至基线
         this.addedToBase();        
         break;
         case '2':  //工作流启动
         this.$router.push('/workflow')
         break;
         case '3':  //删除
         this.fetchDeleteData();
         break;
         default:
       }
    },
    addedToBase(){
         if(this.selectData.length > 0) {
               this.visibleBase = true
         }else {
            this.$error(this.$t('txt_please_seleted_data'))
            return
         }
    },
    fetchDeleteData(){   //to do 接口联调, 待做
      
        this.$emit('deleteData',this.selectData) //删除数据
        if(this.selectData.length > 0) {
           //  deleteBaseApi
              // .execute(this.selectData)
              // .then(data => {
              //    this.$success(err.msg)
              // })
              // .catch(err => {
              //  if(err.msg) {
              //    this.$error(err.msg)
              //  }
              // });
        }else {
          this.$error(this.$t('txt_please_seleted_data'))
        }
       
    },
    creatBtn() {
      this.visible = true;
    },
    onSearch(val) {
      console.log('val', val)
      this.$emit("searchBtn", val);
    },
   
  },
 
};
</script>

<style scoped>
.export-excel-wrapper {
  margin-left: 8px;
}
.export-btn {
  display: flex;
}



.record-btn {
  width: 80px;
  padding: 0;
}

.search {
  height: 52px;
  box-sizing: border-box;
  border-bottom: 1px solid rgba(30, 32, 42, 0.15);
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.search-new {
  display: flex;
}

.new-btn {
  width: 57px;
  height: 32px;
  background: #255ed7;
  border-radius: 4px;
  text-align: center;
  padding: 0;
  margin-right: 8px;
}

.search-btn {
  width: 216px;
  height: 32px;
  background: #fff;
  border-radius: 4px;
}
</style>