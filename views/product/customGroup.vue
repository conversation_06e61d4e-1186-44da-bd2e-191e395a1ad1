<template>
  <div>
    <a-modal
     
      :visible="customval"
      :confirm-loading="confirmLoading"
      class="alert-model"
      :footer="null"
      centered
      @cancel="handleCancelCustom"
    >
    <span slot="title" class="tltles">{{ customtitle }}</span>
      <a-form-model
        ref="ruleForm"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"       
        labelAlign="left"
      >
        <a-form-model-item>
          <div class="search-grouper">
            <a-radio-group
              default-value="0"
              button-style="solid"
              @change="switchdRadio"
              class="radio-group"
            >
              <template v-if="radioval == 0">
                <a-radio-button value="0" style="background: #255ed7"
                  >{{$t('txt_create_group')}}</a-radio-button
                >
                <a-radio-button value="1">{{$t('txt_existing_grouping')}}</a-radio-button>
              </template>
              <template v-else>
                <a-radio-button value="0">{{$t('txt_create_group')}}</a-radio-button>
                <a-radio-button value="1" style="background: #255ed7"
                  >{{$t('txt_existing_grouping')}}</a-radio-button
                >
              </template>
            </a-radio-group>
            <a-input-search
              v-if="haveGroup"
              :placeholder="$t('txt_search')"
              v-model.trim="form.keyword"
              @search="onSearch"
            />
          </div>
        </a-form-model-item>

        <a-form-model-item
          :label="$t('txt_group_name')"
          ref="groupName"
          prop="groupName"
          v-if="!haveGroup"
        >
          <a-input v-model.trim="form.groupName" :placeholder="$t('placeholder_name')" />
        </a-form-model-item>

        <div class="group-list" v-if="haveGroup">
          <li
            v-for="(item, index) of grouplist"
            :key="index"
            :class="{ activer: num == index }"
            @click="switchGroup(index)"
          >
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#jwi-tag"></use>
            </svg>
            <span>{{ item.name }}</span>
          </li>
        </div>

        <div class="footer">
          <a-form-model-item>
            <a-button type="primary" class="comfirm" @click="handleOkCustom"
              >{{$t('btn_ok')}}</a-button
            >
            <a-button @click="handleCancelCustom">{{$t('btn_cancel')}}</a-button>
          </a-form-model-item>
        </div>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import { createApi, searchApi} from "apis/product/group";
export default {
  name: "customGroup",
  props: ["visiblecustom", "selectData", 'dataArr'],
  data() {
    return {
      num: null,
      customtitle: this.$t('txt_txt_custom_group'),
      confirmLoading: true,
      haveGroup: false,
      radioval: 0,
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      form: {
        groupName: "",
        keyword: "",
        id:'',
      },
      grouplist: [
       
      ],
      customval: false,
      rules: {
        groupName: [
          { required: true, message: this.$t('txt_enter_group_name'), trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    visiblecustom(newV, oldV) {
      this.customval = newV;
    },
  },
  mounted() {
    this.customval = this.visiblecustom;
    
    let param ={
      keyword:''
    }
     this.fetchList(param)
  },
  methods: {
    fetchList(){  //to do 联调接口, 待做
    this.grouplist = [{id:'1', name:'产品库一'},{id:'2', name:'产品库二'},{id:'3', name:'产品库三'},{id:'4', name:'产品库四'},{id:'5', name:'产品库五'},{id:'6', name:'产品库六'}]
        // searchApi
        // .execute()
        // .then(data => {
        //   this.grouplist = data
        // })
        // .catch(err => {
        // });
    },
    switchGroup(index) {
      this.num = index;
      this.form.id = this.grouplist[index].id
    },
    handleOkCustom(val) {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.selectData.length > 0) {           
            for(let i =0; i < this.selectData.length; i++){
               console.log('选择数据:',this.dataArr[i]) 
            }
          } else {
            this.$warning(this.$t('txt_please_seleted_data'))
            return false
          }
          let param = {}
          if(this.haveGroup){
              param = {
                id: this.form.id
              }
          }else{
            param={
              groupName: this.form.groupName
            }
          }
          this.fetchCreateGroup(param)  
          this.form.groupName = "";
          this.form.keyword = "";
          this.$emit("closed", false);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    fetchCreateGroup(param){   //to do 联调接口, 待做
        //  createApi
        // .execute(param)
        // .then(data => {
        // })
        // .catch(err => {
        // });
    },

    handleCancelCustom() {
      this.form.groupName = "";
      this.form.keyword = "";
      this.$emit("closed", false);
    },
    onSearch(searchStr) {
      console.log("searchStr", searchStr);
      let parma = {
        keyword:searchStr
      }
       this.fetchList(parma)
      
    },
    switchdRadio(val) {
      let setval = val.target.value;
      this.radioval = setval;
      this.haveGroup = setval == "0" ? false : true;
    }
  },
};
</script>

<style scoped>

.tltles {
  font-size: 16px;
  color: #292a2c;
  font-weight: 700;
}
.radio-group {
  display: flex;
  width: 400px;
}
.footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}


.alert-model >>> .ant-modal-header {
  border-bottom: none;
  height: 56px;
  line-height: 56px;
}
.alert-model >>> .ant-modal-body {
  padding: 0px 24px 18px 24px;
}
.alert-model >>>.ant-form-item{
  margin-bottom: 0;
}
.comfirm {
  margin-right: 8px;
  background: #255ed7;
}
.search-grouper {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: center;
  padding: 0px;
  margin-bottom: 15px;
}
.icon {
  width: 16px;
  height: 16px;
  vertical-align: 0px;
}
.group-list li span {
  font-size: 14px;
  color: rgba(30, 32, 42, 0.65);
  margin-left: 12px;
}
.group-list li.activer {
  background: #f0f7ff;
}
.group-list li:hover {
  background: rgba(30, 32, 42, 0.06);
}
.group-list li {
  height: 40px;
  line-height: 40px;
  list-style-type: none;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
}
.group-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 268px;
  overflow-y: auto;
}
</style>