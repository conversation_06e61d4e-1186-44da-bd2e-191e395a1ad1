<template>
  <div>
    <div class="content-table">
        <div class="table-wrap" :style="{height: fullHeight - 80 + 'px'}">
			 <JwTable :options="columns" rowKey="id" :datasource="tableData"  :height="fullHeight - 100" 
                   @selectData="selectData" :pagination="pagination" :childrenColumnName="childrenColumnName"
				   :isSelect="isSelect" :selected.sync="selectedRowKeys">
				    <template slot="name" slot-scope="{text, record}">
                    <div class="flex align-center">
                        <svg class="jwifont" aria-hidden="true">
                            <use :xlink:href="record.type==='文件夹'?
								'#jwi-unfolder':record.type==='零件'?
								'#jwi-part':record.type==='成品'?
								'#jwi-end-product':record.type==='半成品'?
								'#jwi-unproduct':record.type==='文档'?
								'#jwi-document':'#jwi-unfolder'"></use>
                        </svg>
                        <span class="link-name" @click="getDetail(record)">{{ text }}</span>
                    </div>
                </template>
				<template slot="view" slot-scope="{text}">
					<span class="view-text">{{text}}</span>
                </template>
				<template slot="version" slot-scope="{text}">
					<span class="view-text">{{text}}</span>
                </template>
		    	<template slot="status" slot-scope="{text}">
                    <a-tag v-if="text=='1'" color="red">被检出</a-tag>
					<a-tag v-else-if="text=='2'" color="red">被他人检出</a-tag>
					<span v-else>-</span>
                </template>
					<template slot="operation" slot-scope="{text, record}">
                    <div class="flex align-center">
						 <a-popover
							trigger="click"
							v-model.trim="record.visible"
							placement="bottomLeft">
							<div slot="title" class="rename-title font-700">重命名</div>
							<template slot="content">
								<a-form-model
									ref="renameForm"
									:model="form"
									:rules="rules">
									<a-form-model-item
										label="名称"
										prop="name">
										<a-input
											v-model.trim="form.name"
											placeholder="请填写名称" />
									</a-form-model-item>
									<a-form-model-item class="text-right">
										<a-button
											type="primary"
											@click="onRename(record)"
											:disabled="disabled">确认
										</a-button>
									</a-form-model-item>
								</a-form-model>
							</template>
							<a class="rename-btn">重命名</a>
						</a-popover> 

						<a class="move-btn">移动</a>
						<a class="delete-btn">{{$t('txt_delete')}}</a>
						<a-dropdown>
							<svg class="jwifont" aria-hidden="true">
								<use xlink:href="#jwi-ellipsis"></use>
							</svg>
							<a-menu slot="overlay">
								<a-menu-item>
									<a href="javascript:;">新建view</a>
								</a-menu-item>
								<a-menu-item>
									<a href="javascript:;" @click="startWorkflow">启动工作流</a>
								</a-menu-item>
								<a-menu-item>
									<a href="javascript:;" @click="addToBaseline">添加至基线</a>
								</a-menu-item>
								<a-menu-item>
									<a href="javascript:;" @click="initiateChange">发起变更</a>
								</a-menu-item>
							</a-menu>
						</a-dropdown>
					</div>
                </template>
			 </JwTable>
          
		</div>
		
    </div>
	<addedToBase :visibleBase="visibleBase" @closed="closedBase"></addedToBase>
  </div>
</template>

<script>
import addedToBase from '../addedToBase'
import JwTable from "jw_components/table"
export default {
    name: 'contentTable',
	components: {
		addedToBase,
		JwTable
	},
    props: [
        'fullHeight',
		'tableData'
    ],
    data(){
		return {
			visibleBase: false,
			childrenColumnName:'children',
			selectedRowKeys:[],
			pagination:true,
			isSelect:true,
			columns: [
				{
					title: '名称',
					dataIndex: 'name',
					key: 'name',
					width: 150,
					sorter: true,
					scopedSlots: { customRender: 'name' },
				},
				{
					title: '编码',
					dataIndex: 'code',
					key: 'code',
					sorter: true,
					ellipsis: true,
				},
				{
					title: '类型',
					dataIndex: 'type',
					key: 'type',
					sorter: true,
					ellipsis: true,
				},
				{
					title: '状态',
					dataIndex: 'status',
					key: 'status',
					width: 110,
					sorter: true,
					scopedSlots: { customRender: 'status' },
				},
				{
					title: '视图',
					dataIndex: 'view',
					key: 'view',
					sorter: true,
					ellipsis: true,
				},
				{
					title: '生命周期',
					dataIndex: 'lifecycle',
					key: 'lifecycle',
					sorter: true,
					ellipsis: true,
				},
					{
					title: '版本',
					dataIndex: 'version',
					key: 'version',
					sorter: true,
					ellipsis: true,
				},
				
				{
					title: '创建时间',
					dataIndex: 'createTime',
					key: 'createTime',
					width: 180,
					sorter: true,
				},
				{
					title: '操作',
					dataIndex: 'operation',
					key: 'operation',
					width: 210,
					scopedSlots: { customRender: 'operation' },
				},
			],
			sortedInfo: null,
			filteredInfo: null,
			form: {
				name: '',
			},
			rules: {
				name: [
					{ required: true, message: '请填写名称', trigger: 'blur' },
				],
			},
		}
    },
	computed: {
	
		disabled() {
			if (this.form.name) {
				return false;
			} else {
				return true;
			}
		},
		
  	},
	methods: {
        
         selectData(selectedRowKeys, selectedRows){  //选择当前表格数据
			 this.selectedRowKeys = selectedRowKeys
			 this.$emit('getSelect',selectedRowKeys)
         },
		startWorkflow(){
           this.$router.push('/workflow')
		},
		initiateChange(){
           this.$router.push('/changeManage')
		},
		addToBaseline(){
            this.visibleBase = true
		},
		closedBase(flag){
            this.visibleBase = flag
		},
		onSelectChange(selectedRowKeys, selectData) {       
			this.selectedRowKeys = selectedRowKeys		    
			this.$emit('getSelect',selectData)
		},
		onRename(row) {
			this.$refs.renameForm.validate((valid) => {
				if (valid) {
					this.form.name = '';
					row.visible = false;
				} else {
					return false;
				}
			});
		},
		fetchContentData(){
			
		},
		handleChange(val){
			console.log(val)
		},
	
        getDetail(row) {
            this.$emit('showDetail', row);
        },
  	},
	mounted() {
		this.fetchContentData();
	},
}
</script>

<style lang="less" scoped>
.delete-btn{
	color:#F6445A
}
.content-table {
	.table-wrap {
		margin: 16px 20px;
	}
	.jwifont {
		width: 16px;
		min-width: 16px;
		height: 16px;
		min-height: 16px;
		margin-right: 8px;
	}
	/deep/.ant-table-thead {
		height: 54px;
	}
	/deep/.ant-table-row {
		height: 54px;
	}
	.move-btn, .rename-btn, .delete-btn {
		margin-right: 16px;
	}
	.page-wrap {
		padding: 20px 12px 20px 20px;
		border-top: 1px solid rgba(30,32,42,0.15);
	}
    .link-name {
        color: #255ed7;
        cursor: pointer;
    }
}
</style>
<style lang="less">
.rename-title {
	line-height: 46px;
	font-size: 16px;
}
.ant-form-item {
	margin-bottom: 8px;
}
</style>
