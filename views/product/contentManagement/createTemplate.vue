<template>
    <div class="content-card">
        <a-modal
            :visible="visible"
            title="新建"
            :mask-closable="false"
            okText="确认添加"
            :footer="null"
            @cancel="handleCancel"
        >
            <div class="create-temp-wrap">
                <a-form-model
                    ref="createForm"
                    :model="form"
                    :rules="rules">
                     <a-form-model-item>
                        <a-radio-group default-value="custom" v-model.trim="form.createType" button-style="solid">
                            <a-radio-button value="custom">自定义新建</a-radio-button>
                            <a-radio-button value="template">模板新建</a-radio-button>
                        </a-radio-group>
                    </a-form-model-item>
                    <a-form-model-item label="名称" prop="name">
                        <a-input v-model.trim="form.name" placeholder="请输入模板名称" />
                    </a-form-model-item>
                    <a-form-model-item label="单位" prop="unit">
                        <a-select v-model.trim="form.unit" placeholder="请选择单位">
                            <a-select-option
                                v-for="item in unitList"
                                :key="item.value"
                                :value="item.label">
                                {{ item.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>
                    <a-form-model-item
                        v-if="form.createType==='custom'"
                        label="分类"
                        prop="classification">
                        <a-select v-model.trim="form.classification" placeholder="请选择分类">
                            <a-select-option
                                v-for="item in classList"
                                :key="item.value"
                                :value="item.label">
                                {{ item.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>
                    <a-form-model-item
                        v-if="form.createType==='custom'"
                        prop="file"
                        class="form-item-upload">
                        <a-upload-dragger
                            name="form.file"
                            :multiple="true"
                            action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                            @change="handleChangeFile"
                        >
                            <div class="flex align-center upload-wrap">
                                <div class="form-upload-icon">
                                    <a-icon type="inbox" />
                                </div>
                                <div class="text-left">
                                    <div>将文件拖拽到此处，或点击上传</div>
                                    <div>(文件大小不能超过1GB)</div>
                                </div>
                            </div>
                        </a-upload-dragger>
                    </a-form-model-item>
                    <a-form-model-item
                        v-if="form.createType==='template'"
                        label="模板"
                        prop="classification">
                        <a-select v-model.trim="form.template" placeholder="请选择模板">
                            <a-select-option
                                v-for="item in tempList"
                                :key="item.value"
                                :value="item.label">
                                {{ item.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-model-item>
                    <a-form-model-item class="form-item-btns text-right">
                        <a-button type="primary" @click="handleCreate">确认添加</a-button>
                        <a-button class="form-btn-cancel" @click="handleCancel">{{$t('btn_cancel')}}</a-button>
                    </a-form-model-item>
                </a-form-model>
            </div>
        </a-modal>
    </div>
</template>

<script>
export default {
    name: 'contentTable',
    props: [
        'visible',
    ],
    data(){
		return {
            form: {
                createType: 'custom',
                name: '',
                unit: '',
                classification: '',
                file: [],
                template: '',
            },
            unitList: [
                {label: 'S', value: 'S'},
                {label: 'M', value: 'M'},
                {label: 'L', value: 'L'},
            ],
            classList: [],
            tempList: [],
            rules: {
                name: [{ required: true, message: '请输入模板名称', trigger: 'change' }],
                unit: [{ required: true, message: '请选择单位', trigger: 'change' }],
                classification: [{ required: true, message: '请选择分类', trigger: 'change' }],
                template: [{ required: true, message: '请选择模板', trigger: 'change' }],
            },
		}
    },
	computed: {

  	},
	methods: {
        handleCancel() {
            this.$emit('close');
        },
        handleChangeFile() {

        },
        handleCreate() {
            this.$refs.createForm.validate((valid) => {
				if (valid) {
					
				} else {
					return false;
				}
			});
        },
  	},
	mounted() {

	},
}
</script>

<style lang="less" scoped>
.form-item-upload {
    margin-top: 20px;
}
.upload-wrap {
    padding: 3px 25px;
}
.form-upload-icon {
    margin-right: 15px;
    font-size: 28px;
    color: #2071ff;
}
.form-item-btns {
    margin: 30px 0 0;
}
.form-btn-cancel {
    margin-left: 8px;
}
</style>
