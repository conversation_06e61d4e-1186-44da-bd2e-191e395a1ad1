<template>
    <div class="search-main">

        <div class="search-info">
              <a-input-search placeholder="请输入搜索关键字" class="search-btn" @search="onSearch" />
        </div>

        <div class="filter-btn">
             <span><img src="../../../../assets/image/filter.png"></span>
             <ul>
                 <li v-for="(item, index) of levelArr" :key="index" :class="{current: num==index}" @click="switchLevel(index)">{{item}}</li>
             </ul>

             <a-dropdown :disabled="isDelete">
                <a-menu slot="overlay">
                    <a-menu-item key="1" @click="deleteBatchData">{{$t('txt_delete')}}</a-menu-item>
                </a-menu>
                <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /> </a-button>
             </a-dropdown>

        </div>

    </div>
</template>

<script>
export default {
    name: 'search-structure',
    props: ['isDelete','selectedRowKeys'],
    data(){
        return {
            num: 0,
           levelArr: ['All', '1','2','3','4']
        }
    },
    methods: {
        onSearch(val){
          
        },
        switchLevel(index){
           this.num = index
        },
        deleteBatchData(){
              if(this.selectedRowKeys.length >0) {
                   this.$emit('deleteBatchData',this.selectedRowKeys)
              }else {
                  this.$warning('请至少选择一条数据!')
              }
              
        },
    }
}
</script>

<style scoped>
.filter-btn {
    height: 32px;
    line-height: 32px;
    display: flex;
}
.filter-btn ul{
    margin: 0;
    padding: 0;
    overflow: hidden;
    display: flex;
    border: 1px solid rgba(30, 32, 42, 0.15);
    border-radius: 4px;
}
.filter-btn ul li.current {
    background: #255ED7;
    color: #fff;
}
.filter-btn ul li {
    height: 32px;
    border-right: 1px solid rgba(30, 32, 42, 0.15);
    padding: 0 10px;
    color: rgba(30, 32, 42, 0.85);
    font-size: 14px;
    list-style-type: none;
    cursor: pointer;
}
.filter-btn ul li:last-child {
    border-right: none;
}
.filter-btn span {
    display: block;
    border-radius: 4px;
    height: 32px;
    border: 1px solid rgba(30, 32, 42, 0.15);
    padding: 0 9px;
    margin-right: 8px;
}
.filter-btn span img {
    width: 16px;
    height: 16px;
}
.search-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.search-btn{
    width: 216px;
}
.search-info {
    height: 64px;
    display: flex;
    align-items: center;
    padding: 0;
}
</style>