<template>
    <div>
        <div class="content-detail" :style="{height: fullHeight - 95 + 'px'}">
            <div class="search-info">
                 <div class="search-info-left">
                     <a-input-search placeholder="请输入搜索关键字" class="search-btn" @search="onSearch" />
                 </div>
                 <div class="search-info-right">
                     <div class="color-piece">
                         <li><em class="attribute"></em><label>属性</label></li>
                         <li><em class="add"></em><label>新增</label></li>
                         <li><em class="cut"></em><label>删减</label></li>
                     </div>
                     <div class="filter-btn">
                        <span><img src="../../../../assets/image/filter.png"></span>
                        <ul>
                            <li v-for="(item, index) of levelArr" :key="index" :class="{current: num==index}" @click="switchLevel(index)">{{item}}</li>
                        </ul>
                    </div>
                 </div>
            </div>
             
             <div class="table-info">
               <a-row>
                <a-col :span="12">
                    <div class="table-header"><span>PA000000324，机柜，Deisgn，A.1</span></div>
                    <JwTable :options="columns" rowKey="id" :datasource="tableData" :height="fullHeight - 200" 
                       :pagination="pagination" :childrenColumnName="childrenColumnName" :isSelect="isSelect" :changeStyle="changeStyle">

                         <template slot-scope="{text, record}" slot="code"> 
                            <span class="link-name" @click="getDetail(record)">{{ text }}</span>
                         </template>

                         <template slot-scope="{text}" slot="viewName"> 
                            <span class="view-text">{{ text }}</span>
                         </template>

                          <template slot-scope="{text}" slot="version"> 
                            <span class="view-text">{{ text }}</span>
                         </template>

                    </JwTable>
                </a-col>
                <a-col :span="12">
                   <div class="table-header"><span>PA000000324，机柜，Deisgn，A.1</span></div>
                   <JwTable :options="columns" rowKey="id" :datasource="tableData" :height="fullHeight - 200" 
                       :pagination="pagination" :childrenColumnName="childrenColumnName" :isSelect="isSelect" :changeStyle="changeStyle">
                       
                         <template slot-scope="{text, record}" slot="code"> 
                            <span class="link-name" @click="getDetail(record)">{{ text }}</span>
                         </template>

                         <template slot-scope="{text}" slot="viewName"> 
                            <span class="view-text">{{ text }}</span>
                         </template>

                          <template slot-scope="{text}" slot="version"> 
                            <span class="view-text">{{ text }}</span>
                         </template>

                    </JwTable>

                </a-col>
               </a-row>
               </div>
        </div>
     </div>
</template>

<script>
import JwTable from "jw_components/table"
export default {
    name: 'historyStructure',
    props: ['fullHeight'],
    components: {
        JwTable
    },
    data(){
        return {
            isSelect:true,
            num: 0,
            childrenColumnName: 'children',
            pagination: false,
            selectedRowKeys: [],
            levelArr: ['All', '1','2','3','4'],
            changeStyle: [{
                name: 'attr',
            },{
                name: 'cut',
            },{
                name: 'add',
            }],
            columns: [
                {
                title: '编码',
                dataIndex: 'code',
                sorter: true,
                width: '400',
                key: 'code',
                ellipsis: true,
                },
                {
                title: '名称',
                dataIndex: 'name',
                key: 'name',
                sorter: true,
                scopedSlots: { customRender: 'name' },
                },
                {
                title: '视图',
                dataIndex: 'viewName',
                sorter: true,
                key: 'viewName',
                scopedSlots: { customRender: 'viewName' },
                },
                 {
                title: '版本',
                dataIndex: 'version',
                key: 'version',
                sorter: true,
                ellipsis: true,
                scopedSlots: { customRender: 'version' },
                },
                {
                title: '数量',
                dataIndex: 'number',
                key: 'number',
                sorter: true,
                width: '130px',
                scopedSlots: { customRender: 'number' },
                ellipsis: true,
                },
             
             ],
            tableData: [
              {
                id: '1',
                name: '风扇001',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: '12',
                status:'attr',
              },
              {
                id: '2',
                name: '风扇001',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: '12',
                status:'cut',
                children: [{
                    id: '112',
                    name: '风扇001',
                    code:'FSD32',
                    version:'A.3',
                    viewName:'Design',
                    number: '12',
                    status:'',
                },{
                    id: '156',
                    name: '风扇001',
                    code:'FSD32',
                    version:'A.3',
                    viewName:'Design',
                    number: '12',
                    status:'',
                }]
              },
              {
                id: '3',
                name: '风扇001',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: '12',
                status:'add',
              },
               {
                id: '5',
                name: '风扇001',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: '12',
                status:'',
              },
                         
            ],
        }
    },
    methods: {
         fetchData(){  //当前表格数据
            return new Promise((resolve, reject)=>{
                resolve({data: this.tableData,total: 14})
            })
         },
        onSearch(val){
           console.log('val', val)
        },
        switchLevel(index){
           this.num = index
        },
       
    }
}
</script>

<style scoped>
.table-header span {
    font-size: 14px;
    color: rgba(30, 32, 42, 0.85);
    font-weight: bold;
}
.table-header {
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: rgba(30, 32, 42, 0.02);
    border: 1px solid rgba(30, 32, 42, 0.06);
    border-bottom: none;
    padding: 0 16px;
}
.view-text{
    background: rgba(30, 32, 42, 0.04);
    border-radius: 4px;
    border: 1px solid rgba(30, 32, 42, 0.15);
    height: 22px;
    line-height: 22px;
    padding: 0 7px;
}
.link-name {
    color: #255ed7;
    cursor: pointer;
    }
.table-info{
    margin: 16px 20px;
}
.color-piece li{
    list-style-type: none;
    display: flex;
    align-items: center;
    margin-right: 30px;
}
.color-piece li em.attribute{
    background: #FFE4BD;
    border: 1px solid #FFE4BD;
}
.color-piece li em.add{
    background: #CCEDAF;
    border: 1px solid #CCEDAF;
}
.color-piece li em.cut{
    background: #FFC2C3;
    border: 1px solid #FFC2C3;
}
.color-piece li em {
    width: 31px;
    height: 22px;
    display: block;
    margin-right: 8px;
}
.color-piece li label {
    color: rgba(30, 32, 42, 0.65);
    font-size: 14px;
}
.color-piece {
    height: 32px;
    line-height: 32px;
    display: flex;
    margin-right: 30px;
}
.search-info-right {
    display: flex;
}
.filter-btn {
    height: 32px;
    line-height: 32px;
    display: flex;
}
.filter-btn ul{
    margin: 0;
    padding: 0;
    overflow: hidden;
    display: flex;
    border: 1px solid rgba(30, 32, 42, 0.15);
    border-radius: 4px;
}
.filter-btn ul li.current {
    background: #255ED7;
    color: #fff;
}
.filter-btn ul li {
    height: 32px;
    border-right: 1px solid rgba(30, 32, 42, 0.15);
    padding: 0 10px;
    color: rgba(30, 32, 42, 0.85);
    font-size: 14px;
    list-style-type: none;
    cursor: pointer;
}
.filter-btn ul li:last-child {
    border-right: none;
}
.filter-btn span {
    display: block;
    border-radius: 4px;
    height: 32px;
    border: 1px solid rgba(30, 32, 42, 0.15);
    padding: 0 9px;
    margin-right: 8px;
}
.filter-btn span img {
    width: 16px;
    height: 16px;
}
.search-btn{
    width: 216px;
}
 .search-info{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
 }
</style>