<template>
    <div>

        <a-tabs default-active-key="1" @change="callback">
          <a-tab-pane key="1" tab="全局">
          </a-tab-pane>
          <a-tab-pane key="2" tab="特定">
          </a-tab-pane>
        </a-tabs>

        <div class="search-top">
            <a-input-search placeholder="请输入搜索关键字" class="search-btn"  @search="onSearch" />
            <a-pagination simple :default-current="2" :total="50" />
        </div>
        
        <div class="classtable">
         <a-table
            :columns="columns"
            :pagination="false"    
            rowKey="id" 
            :scroll="{ y: 550 }"
            :row-selection="{ selectedRowKeys:this.selectedRowKeys, onChange: this.onSelectChange}" 
            :data-source="tableData"
            >
             <template slot-scope="text" slot="version">
                   <span class="view-text">{{text}}</span>  
             </template>

             <template slot-scope="text" slot="viewName">
                   <span class="view-name">{{text}}</span>  
             </template>
            </a-table>
        </div>


    </div>
</template>

<script>
export default {
    name: 'dialog-add-object',
    data() {
        return {
            columns: [
                {
                title: '名称',
                dataIndex: 'name',
                key: 'name',
                sorter: true,
                scopedSlots: { customRender: 'name' },
                },
                {
                title: '编码',
                dataIndex: 'code',
                sorter: true,
                key: 'code',
                },
               
             
                 {
                title: '版本',
                dataIndex: 'version',
                key: 'version',
                sorter: true,
                ellipsis: true,
                scopedSlots: { customRender: 'version' },
                },
               {
                title: '视图',
                dataIndex: 'viewName',
                sorter: true,
                key: 'viewName',
                scopedSlots: { customRender: 'viewName' },
                },
                {
                title: '类型',
                dataIndex: 'type',
                key: 'type',
                sorter: true,
                width: '130px',
                ellipsis: true,
                },
              
             ],
            tableData: [
              {
                key: '1',
                name: '风扇001',
                type: '文件夹',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: '12',
              },
              {
                key: '2',
                name: '风扇001',
                type: '零件',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: '102',
              },
              {
                key: '3',
                name: '风扇001',
                type: '半成品',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: '102',
              },
              {
                key: '4',
                name: '风扇001',
                type: '半成品',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: '102',
              },
              {
                key: '5',
                name: '风扇001',
                type: '文档',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: '102',
              },
              {
                key: '6',
                name: '风扇001',
                type: '文档',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: '102',
              },
              {
                key: '7',
                name: '风扇001',
                code:'FSD32',
                type: '文档',
                version:'A.3',
                viewName:'Design',
                number: '102',
              }, {
                key: '8',
                name: '风扇001',
                type: '文档',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: '102',
              },
              {
                key: '9',
                name: '风扇001',
                type: '文档',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: '102',
              },
              {
                key: '10',
                name: '风扇001',
                code:'FSD32',
                type: '文档',
                version:'A.3',
                viewName:'Design',
                number: '102',
              },
              {
                key: '11',
                name: '风扇001',
                code:'FSD32',
                type: '文档',
                version:'A.3',
                viewName:'Design',
                number: '102',
              },              
            ],
            selectedRowKeys:[],
            multipleSelection:[]
        }
    },
    methods: {
        onSearch(val){
            console.log(val)
        },
        callback(key) {
          console.log(key);
        },
        onSelectChange(selectedRowKeys, selectedRows) {
            this.selectedRowKeys = selectedRowKeys
            this.multipleSelection = selectedRows
        },
    }
}
</script>

<style scoped>
.view-text{
    background: rgba(30, 32, 42, 0.04);
    border-radius: 4px;
    border: 1px solid rgba(30, 32, 42, 0.15);
    height: 22px;
    line-height: 22px;
    padding: 0 7px;
}
.view-name{
    background: #F0F7FF;
    border-radius: 4px;
    border: 1px solid #A4C9FC;
    height: 22px;
    line-height: 22px;
    padding: 0 7px;
    color: #255ED7;
}
.classtable{
    margin-top: 16px;
    height: 615px;
}
.search-top{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
  .search-btn {
      width: 216px;
  }
</style>