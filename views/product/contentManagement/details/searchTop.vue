<template>
    <div>
        <div class="search-info">
            <span>
                 <a-button class="add-btn" @click="addcomparison">添加对比</a-button>
                 <a-input-search placeholder="请输入搜索关键字" class="search-btn" @search="onSearch" />
             </span>
            <div class="operation-btn">
                <span><img src="../../../../assets/image/filter.png"></span>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'searchTop',
    data(){
        return {

        }
    },
    methods: {
      onSearch(val){
            console.log('val', val)
       },
       addcomparison(){
           this.$emit('comparisionBtn')
          
       }
    }
  
}
</script>

<style scoped>
.operation-btn span img {
    width: 16px;
    height: 16px;
}
.operation-btn span {
    border: 1px solid rgba(30, 32, 42, 0.15);
    padding: 0 8px;
    height: 32px;
    line-height: 32px;
    border-radius: 4px;
    display: block;
}
.operation-btn{
    height: 32px;
    line-height: 32px;
    margin-top: 0px;
    display: flex;
}
.search-info {
    display: flex;
    margin-top: 8px;
    justify-content: space-between;
    align-items: center;
    padding: 0;
}
.add-btn{
    background: #255ED7;
    color: #fff;
    margin-right: 8px;
}
.search-btn{
    width: 216px;
}
</style>