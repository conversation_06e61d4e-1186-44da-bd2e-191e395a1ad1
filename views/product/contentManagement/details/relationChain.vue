<template>
    <div>
        <div class="content-detail" :style="{height: fullHeight - 95 + 'px'}">
            relation<PERSON>hain
        </div>
    </div>
</template>

<script>
export default {
    name: 'relation<PERSON>hain',
    props: ['fullHeight'],
    data(){
        return {
            
        }
    }
}
</script>


<style scoped>
.content-detail{
    padding: 5px 20px;
    margin-top: 0px;
}
</style>