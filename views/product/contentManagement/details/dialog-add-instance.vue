<template>
    <div>
         <a-modal
            title="存在实例"
            :visible="visible"
            @ok="handleOk"
            :width="width"
            class="model"
            @cancel="handleCancel"
            >
            <div>
                <div class="search-main">
                      <span>关键字：</span>
                      <a-input-search placeholder="搜索" class="search-btn" @search="onSearch" />
                </div>
                <div class="classtable">
                    <JwTable :options="columns" rowKey="id" :isSelect="isSelect" :height="height" :datasource="tableData" 
                              @selectData="selectDialogData"  :pagination="pagination" :selected.sync="selectedRowKeys">
                               <template slot="name" slot-scope="{text, record}">
                                   <svg class="jwifont" aria-hidden="true">
                                        <use :xlink:href="record.type==='文件夹'?
                                            '#jwi-unfolder':record.type==='零件'?
                                            '#jwi-part':record.type==='成品'?
                                            '#jwi-end-product':record.type==='半成品'?
                                            '#jwi-unproduct':record.type==='文档'?
                                            '#jwi-document':'#jwi-unfolder'"></use>
                                    </svg>
                                    <span class="link-name" @click="getDetail(record)">{{ text }}</span>
                               </template>
                              <template slot="operation" slot-scope="text, record, $index">
                                    <span class="view-text" @click="deleteData($index)"><img src="../../../../assets/image/delete-icon.png" class="delete-icon"></span>
                               </template>
                     </JwTable>
                </div>
            </div>
            </a-modal>
    </div>
</template>

<script>
import JwTable from "jw_components/table"
export default {
    name: 'dialog-add-instance',
    props: ['visible'],
    components: {
       JwTable
    },
    data(){
        return {
              pagination:true,
              selectedRowKeys:[],
              multipleSelection:[],
              isSelect:true, 
              width: 960,
              height: 660,
              columns: [
                {
                    title:'名称',
                    dataIndex: 'name',
                    key: 'name',
                },
                {
                    title: '编码',
                    dataIndex: 'code',
                    key: 'code',
                    align: 'center',
                },
                {
                    title: '生命周期',
                    dataIndex: 'lifecycle',
                    key: 'lifecycle',
                    align: 'center',
                    scopedSlots: { customRender: 'lifecycle' },
                },
                {
                    title: '版本',
                    key: 'version',
                    dataIndex: 'version',
                    align: 'center',
                    scopedSlots: { customRender: 'version' },
                },
                {
                    title: '操作',
                    dataIndex: 'operation',
                    key: 'operation',
                    align: 'center',
                    scopedSlots: { customRender: 'operation' },
                }
               ],
              tableData: [
                {
                    id: '1',
                    name: 'John',
                    code: 'WT00002341',
                    lifecycle: 'Inwork',
                    version: 'A.1',
                },
                 {
                    id: '2',
                    name: 'John',
                    code: 'WT00002341',
                    lifecycle: 'Inwork',
                    version: 'A.1',
                },
                 {
                    id: '3',
                    name: 'John',
                    code: 'WT00002341',
                    lifecycle: 'Inwork',
                    version: 'A.1',
                },
                 {
                    id: '4',
                    name: 'John',
                    code: 'WT00002341',
                    lifecycle: 'Inwork',
                    version: 'A.1',
                },
                 {
                    id: '5',
                    name: 'John',
                    code: 'WT00002341',
                    lifecycle: 'Inwork',
                    version: 'A.1',
                },
                 {
                    id: '6',
                    name: 'John',
                    code: 'WT00002341',
                    lifecycle: 'Inwork',
                    version: 'A.1',
                },
                 {
                    id: '7',
                    name: 'John',
                    code: 'WT00002341',
                    lifecycle: 'Inwork',
                    version: 'A.1',
                },
                 {
                    id: '8',
                    name: 'John',
                    code: 'WT00002341',
                    lifecycle: 'Inwork',
                    version: 'A.1',
                },
                 {
                    id: '9',
                    name: 'John',
                    code: 'WT00002341',
                    lifecycle: 'Inwork',
                    version: 'A.1',
                },
                 {
                    id: '10',
                    name: 'John',
                    code: 'WT00002341',
                    lifecycle: 'Inwork',
                    version: 'A.1',
                }
             ]
        }
    },
    methods: {
         fetchDialogData() {  //弹出窗口数据
             return new Promise((resolve, reject)=>{
                resolve({data: this.tableData,total: 3})
            })
          },
            selectDialogData(selectedRowKeys, selectedRows){  //选择弹出窗口数据
              this.selectedRowKeys = selectedRowKeys
              this.multipleSelection = selectedRows
         },
        handleOk(){
           this.$emit('closed')
        },
        handleCancel(){
           this.$emit('closed')
        },
         onSearch(val){
            console.log('val', val)
        },
    }
}
</script>

<style scoped>
.classtable{
    height: 660px;
}
.search-btn{
    width: 216px;
}
.search-main{
    margin: 5px 0 16px 0;
    display: flex;
    align-items: center;
}
.search-main span {
    margin-right: 10px;
}
.delete-icon{
  width: 16px;
  height: 16px;
}

.page-wrap {
    padding: 20px 0 20px 20px;
}
.model>>>.ant-modal-body{
    padding: 10px 20px;
}
.model>>>.ant-modal-footer {
    border-top: none;
}
</style>