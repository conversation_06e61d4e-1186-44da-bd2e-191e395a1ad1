<template>
        <div class="content-detail">
            <back-header :title="backTitle" @back="goBack"></back-header>
            <div class="shadow-wrap">
                    <a-tabs @change="callback">
                        <a-tab-pane key="1" tab="详细信息">
                             <historyInformation :fullHeight="fullHeight"></historyInformation>
                        </a-tab-pane>
                        <a-tab-pane key="2" tab="结构">
                             <historyStructure :fullHeight="fullHeight"></historyStructure>
                        </a-tab-pane>
                    </a-tabs>
            </div>
        </div>
</template>

<script>
import backHeader from '../../back-header'
import historyInformation from './historyInformation'
import historyStructure from './historyStructure'
export default {
    name: 'versionComparison',
    inject: ['setBreadcrumb', 'addBreadcrumb'],
    components: {
         backHeader,
         historyInformation,
         historyStructure
    },
    data(){
        return {
           backTitle:'历史版本对比',
           fullHeight: document.documentElement.clientHeight - 104,
           currentKey: null
        }
    },
    methods: {
         initBreadcrumb() {
            let breadcrumbData = [{ name: '历史版本对比', path: '/versionComparison' }];
            this.setBreadcrumb(breadcrumbData);            
        },
         callback(key) {
            this.currentKey = key
        },
        goBack(){
            this.$router.go(-1);//返回上一层
        }
    },
    mounted(){
         window.onresize = () => {
            return (() => {
                this.fullHeight = document.documentElement.clientHeight - 104;
            })();
        }
    },
      created() {
        this.initBreadcrumb();
    },
}
</script>

<style scoped>
.shadow-wrap {
    margin: 5px;
    background: var(--light);
    box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
    border-radius: 4px;
    position: relative;
}
</style>