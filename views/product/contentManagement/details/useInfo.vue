<template>
    <div>
      <div class="search-info">
          
          <a-input-search placeholder="请输入搜索关键字" class="search-btn" @search="onSearch" />
           <div class="classtable">
                  <a-table :columns="columns" rowKey="id" :data-source="tableData" :scroll="{y: fullHeight - 180 }" bordered  :pagination="false" >
                     <template slot-scope="text, record" slot="name">
                           <svg class="jwifont" aria-hidden="true">
                                        <use :xlink:href="record.type==='文件夹'?
                                            '#jwi-unfolder':record.type==='零件'?
                                            '#jwi-part':record.type==='成品'?
                                            '#jwi-end-product':record.type==='半成品'?
                                            '#jwi-unproduct':record.type==='文档'?
                                            '#jwi-document':'#jwi-unfolder'"></use>
                            </svg>
                            <span class="link-name" @click="getDetail(record)">{{ text }}</span>
                     </template>

                     <template slot-scope="text" slot="viewName">
                          <span class="view-text">{{ text }}</span>
                     </template>

                      <template slot-scope="text, record" slot="number">
                                  <span><a-input-number  v-model.trim="record.number"  class="number"  :disabled="!record.isnumber" :min="1"   /></span>  
                      </template>

                        <template slot-scope="text,record" slot="line">
                            <span><a-input-number v-model.trim="record.line"  class="line" :disabled="!record.isline" :min="1" /></span> 
                       </template>

                        <template slot-scope="text,record" slot="position">
                            <span><a-input-number v-model.trim="record.position"  class="position" :disabled="!record.isposition" :min="1" /></span> 
                       </template>

                  </a-table>
           </div>
      </div>
    </div>
</template>

<script>
export default {
    name: 'useInfo',
    props:['fullHeight'],
    data(){
        return {
            columns: [
                {
                title: '名称',
                dataIndex: 'name',
                key: 'name',
                sorter: true,
                scopedSlots: { customRender: 'name' },
                },
                {
                title: '编码',
                dataIndex: 'code',
                sorter: true,
                key: 'code',
                },
               
                {
                title: '视图',
                dataIndex: 'viewName',
                sorter: true,
                key: 'viewName',
                scopedSlots: { customRender: 'viewName' },
                },
                 {
                title: '数量',
                dataIndex: 'number',
                key: 'number',
                ellipsis: true,
                scopedSlots: { customRender: 'number' },
                  customCell: (record)=>{
                        return {
                        on: {
                            click: (event) => {
                                this.$set(record,'isnumber', true)
                            },
                            mouseleave: (event) => {
                                 this.$set(record,'isnumber', false)
                            }
                          }
                        }
                    },
                },
                 {
                title: '行号',
                dataIndex: 'line',
                key: 'line',
                scopedSlots: { customRender: 'line' },
                ellipsis: true,
                  customCell: (record)=>{
                        return {
                        on: {
                            click: (event) => {
                                this.$set(record,'isnumber', true)
                            },
                            mouseleave: (event) => {
                                 this.$set(record,'isnumber', false)
                            }
                          }
                        }
                    },
                },  
                {
                title: '位号',
                dataIndex: 'position',
                key: 'position',
                scopedSlots: { customRender: 'position' },
                ellipsis: true,
                  customCell: (record)=>{
                        return {
                        on: {
                            click: (event) => {
                                this.$set(record,'isnumber', true)
                            },
                            mouseleave: (event) => {
                                 this.$set(record,'isnumber', false)
                            }
                          }
                        }
                    },
                }
             ],
            tableData: [
              {
                id: '1',
                name: '风扇001',
                type: '文件夹',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: 212,
                line: 23,
                position:22,
                isnumber:false,
                isline: false,
                isposition: false
              },
              {
                id: '2',
                name: '风扇001',
                type: '零件',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: 212,
                line: 23,
                position:22,
                 isnumber:false,
                isline: false,
                isposition: false
              },
              {
                id: '3',
                name: '风扇001',
                type: '半成品',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: 212,
                line: 23,
                position:22,
                 isnumber:false,
                isline: false,
                isposition: false
              },
              {
                id: '4',
                name: '风扇001',
                type: '半成品',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: 212,
                line: 23,
                position:22,
                 isnumber:false,
                isline: false,
                isposition: false
              },
              {
                id: '5',
                name: '风扇001',
                type: '文档',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: 212,
                line: 23,
                position:22,
                isnumber:false,
                isline: false,
                isposition: false
              },
              {
                id: '6',
                name: '风扇001',
                type: '文档',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: 212,
                line: 23,
                position:22,
                isnumber:false,
                isline: false,
                isposition: false
                 
              },
              {
                id: '7',
                name: '风扇001',
                code:'FSD32',
                type: '文档',
                version:'A.3',
                viewName:'Design',
                number: 212,
                line: 23,
                position:22,
                isnumber:false,
                isline: false,
                isposition: false
              },
               {
                id: '22',
                name: '风扇001',
                type: '文档',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: 212,
                line: 23,
                position:22,
                isnumber:false,
                isline: false,
                isposition: false
                 
              },
              {
                id: '10',
                name: '风扇001',
                code:'FSD32',
                type: '文档',
                version:'A.3',
                viewName:'Design',
                number: 212,
                line: 23,
                position:22,
                isnumber:false,
                isline: false,
                isposition: false
              },
               {
                id: '11',
                name: '风扇001',
                type: '文档',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: 212,
                line: 23,
                position:22,
                isnumber:false,
                isline: false,
                isposition: false
                 
              },
              {
                id: '12',
                name: '风扇001',
                code:'FSD32',
                type: '文档',
                version:'A.3',
                viewName:'Design',
                number: 212,
                line: 23,
                position:22,
                isnumber:false,
                isline: false,
                isposition: false
              },
               {
                id: '13',
                name: '风扇001',
                type: '文档',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: 212,
                line: 23,
                position:22,
                isnumber:false,
                isline: false,
                isposition: false
                 
              },
              {
                id: '17',
                name: '风扇001',
                code:'FSD32',
                type: '文档',
                version:'A.3',
                viewName:'Design',
                number: 212,
                line: 23,
                position:22,
                isnumber:false,
                isline: false,
                isposition: false
              },
               {
                id: '16',
                name: '风扇001',
                type: '文档',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: 212,
                line: 23,
                position:22,
                isnumber:false,
                isline: false,
                isposition: false
                 
              },
              {
                id: '18',
                name: '风扇001',
                code:'FSD32',
                type: '文档',
                version:'A.3',
                viewName:'Design',
                number: 212,
                line: 23,
                position:22,
                isnumber:false,
                isline: false,
                isposition: false
              }
            ],
        }
    },
    methods: {
        onSearch(val){
           console.log('val', val)
        },
       
    }
}
</script>


<style scoped>
.view-text{
    background: rgba(30, 32, 42, 0.04);
    border-radius: 4px;
    border: 1px solid rgba(30, 32, 42, 0.15);
    height: 22px;
    line-height: 22px;
    padding: 0 7px;
}
.product-info {
  display: flex;
  align-items: center;
}
.icon-product{
    width: 15px;
    height: 15px;
    margin-right: 10.5px;
}
.link-name {
        color: #255ed7;
        cursor: pointer;
    }
.jwifont {
		width: 16px;
		min-width: 16px;
		height: 16px;
		min-height: 16px;
		margin-right: 8px;
	}
.classtable{
    margin-top: 16px;
}
.search-info{
    padding: 0 0 16px 0;
}
.search-btn{
    width: 216px;
}

</style>