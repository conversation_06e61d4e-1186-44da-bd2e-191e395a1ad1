<template>
    <div>
        <div class="classtable">
            <div class="search-info">
                <div class="search-info-left">
                    <a-button class="add-btn" @click="addInstance">添加</a-button>
                    <a-input-search placeholder="请输入搜索关键字" class="search-btn" @search="onSearch" />
                </div>
                <div class="search-info-right">
                    <span class="current"><img src="../../../../assets/image/filter.png"></span>                                
                    <span>
                        <a-dropdown :disabled="selectedRowKeys.length > 0 ? false : true">
                            <a-menu slot="overlay">
                                <a-menu-item key="1"  @click="deleteBatchData">{{$t('txt_delete')}}</a-menu-item>
                            </a-menu>
                            <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /> </a-button>
                        </a-dropdown>
                    </span>
                </div>
            </div>
            <div class="table-wrap">
                 <JwTable :options="headerlist" rowKey="id" :datasource="tableList" :height="fullHeight -500" 
                    @selectData="selectData" :pagination="pagination"  :isSelect="isSelect" :selected.sync="selectedRowKeys">
                         <template slot="filename" slot-scope="{text, record}">
                               <svg class="jwifont" aria-hidden="true">
                                    <use :xlink:href="record.type==='文件夹'?
                                        '#jwi-unfolder':record.type==='零件'?
                                        '#jwi-part':record.type==='成品'?
                                        '#jwi-end-product':record.type==='半成品'?
                                        '#jwi-unproduct':record.type==='文档'?
                                        '#jwi-document':'#jwi-unfolder'"></use>
                                </svg>
                                <span class="link-name" @click="getDetail(record)">{{ text }}</span>
                            </template> 

                            <template slot="lifecycle" slot-scope="{text}">
                                    <span class="view-text">{{text}}</span>
                            </template>

                            <template slot="version" slot-scope="{text}">
                                    <span class="view-text">{{text}}</span>
                            </template>

                            <template slot="operation" slot-scope="text, record, $index">
                                 <span class="view-text" @click="deleteData($index)"><img src="../../../../assets/image/delete-icon.png" class="delete-icon"></span>
                            </template>
                    </JwTable>                       
            </div>
         </div>

         <dialogAddInstance :visible="visible" @closed="closed"></dialogAddInstance>
    </div>
</template>

<script>
import JwTable from "jw_components/table"
import dialogAddInstance  from './dialog-add-instance'
export default {
    name: 'RelatedTable',
    props:['fullHeight'],
    components: {
       JwTable,
       dialogAddInstance,
    },
    data(){
        return {   
            childrenColumnName: 'children',
            pagination: true,   
            isSelect:true,       
            headerlist: [
            {
            title: '文件',
            dataIndex: 'filename',
            key: 'filename',
            sorter: true,
            scopedSlots: { customRender: 'filename' },
            },
            {
            title: '名称',
            dataIndex: 'name',
            sorter: true,
            key: 'name',
            },
            {
            title: '编码',
            dataIndex: 'code',
            key: 'code',
            sorter: true,
            ellipsis: true,
            align: 'center',
            scopedSlots: { customRender: 'code' },
            },
            {
            title: '版本',
            dataIndex: 'version',
            sorter: true,
            key: 'version',
            align: 'center',
            scopedSlots: { customRender: 'version' },
            },
            {
            title: '生命周期',
            dataIndex: 'lifecycle',
            key: 'lifecycle',
            sorter: true,
            align: 'center',
            scopedSlots: { customRender: 'lifecycle' },
            ellipsis: true,
            },
            {
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            sorter: true,
            align: 'center',
            scopedSlots: { customRender: 'operation' },
            ellipsis: true,
            },                
            ],
            tableList: [
            {
            id: '1',
            filename: '风扇001',
            name: '文件夹',
            version:'FSD32',
            code:'A.3',
            lifecycle:'Design',
            },
            {
            id: '2',
            filename: '风扇001',
            name: '文件夹',
            version:'FSD32',
            code:'A.3',
            lifecycle:'Design',
            },
            {
            id: '3',
            filename: '风扇001',
            name: '文件夹',
            version:'FSD32',
            code:'A.3',
            lifecycle:'Design',
            },
            ],
            visible:false,
            selectedRowKeys:[],
         }
      },
      methods: {
        selectData(selectedRowKeys, selectedRows){  //选择当前表格数据
            this.selectedRowKeys = selectedRowKeys
        },
      
        addInstance(){
           this.visible = true;
           this.visibleAddObj = true;
        },
       
        deleteBatchData(){
            this.tableList = this.tableList.filter(item=> {
                return ! this.selectedRowKeys.find(row=> {
                    return item.id==row
                })
            })
        },

      
        deleteData(index){
             this.tableData.splice(index, 1)
        },
        closed(){
            this.visible = false
        },
        onSearch(val){
             console.log('val',val)
        }
       
      }
}
</script>

<style scoped>
.jwifont{
    width: 16px;
    height: 16px;
}
.delete{
    color: #F6445A;
    cursor: pointer;
}
.search-info-right span img {
    width: 16px;
    height: 16px;
}
.search-info-right span.current {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0 7px;
}
.search-info-right span{
    height: 32px;
    line-height: 32px;
    display: block;
    box-sizing: border-box;
}
.search-info-right {
    display: flex;
}
.search-btn{
    width: 216px;
}
.add-btn {
    background: #255ED7;
    color: #fff;
    margin-right: 8px;
}
.search-info{
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    padding: 0;
}
.classtable{
    margin: 0;
    overflow-y: auto;
}
</style>