<template>
    <div>
        <div class="content-detail" :style="{height: fullHeight - 95 + 'px'}">
             <a-collapse v-model.trim="activeKey">
                <a-collapse-panel key="1">
                    <div slot="header" class="content-title">CAD动态文档</div>
                    <RelatedTable :fullHeight="fullHeight"></RelatedTable>
                </a-collapse-panel>
                <a-collapse-panel key="2">
                    <div slot="header" class="content-title">说明文档</div>
                    <RelatedTable :fullHeight="fullHeight"></RelatedTable>
                </a-collapse-panel>
             </a-collapse>

        </div>
    </div>
</template>

<script>
import RelatedTable from './RelatedTable'
export default {
    name: 'RelatedObjects',
    props: ['fullHeight'],
    data(){
        return {
             activeKey: ['1'],
           
    };
  },
  components: {
      RelatedTable
  },
};
</script>



<style scoped>

.content-detail>>>.ant-collapse-header {
  background: #fff;
}
.content-title{
    display: block;
}
.content-detail{
    padding: 5px 20px;
    margin-top: 16px;
    overflow-y: auto;
}
</style>