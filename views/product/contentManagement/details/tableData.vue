<template>
    <div>
         <a-table :columns="columns" :data-source="tableData" :pagination="false" defaultExpandAllRows :scroll="{y: fullHeight - 270 }" bordered 
                     :row-selection="{ selectedRowKeys:this.selectedRowKeys, onChange: this.onSelectChange}">
         </a-table>
    </div>
</template>

<script>
export default {
    name: 'tableData',
    props:['tableData','columns','fullHeight','isSelect'],
    data(){
        return {
            selectedRowKeys:[],
        }
    },
    methods:{
        onSelectChange(selectedRowKeys,selectedRow){
            if(this.isSelect) {
                  this.selectedRowKeys = selectedRowKeys
                  this.multipleSelection = selectedRow
                  this.$emit('getselectData', selectedRowKeys, selectedRow)
            }
        }
    }
}
</script>

<style scoped>

</style>