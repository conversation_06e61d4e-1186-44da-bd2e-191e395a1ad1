<template>
    <div>
        <div class="content-detail" :style="{height: fullHeight - 95 + 'px'}">
           <div class="detail-main">
                <div class="table-header">
                    <ul>
                        <li><label>仅显示差异</label><span><a-switch default-checked @change="onChange" /></span></li>
                        <li class="large">
                            <a-select class="select-option" default-value="**********，机柜，Design，A.3" @change="handleChange">
                                <a-select-option value="**********，机柜，Design，A.3">**********，机柜，Design，A.3</a-select-option>
                                <a-select-option value="**********，机柜，Design，A.2">**********，机柜，Design，A.2</a-select-option>
                            </a-select>
                        </li>
                        <li class="large">
                            <a-select class="select-option" default-value="**********，机柜，Design，A.13" @change="handleChange">
                                <a-select-option value="**********，机柜，Design，A.13">**********，机柜，Design，A.13</a-select-option>
                                <a-select-option value="**********，机柜，Design，A.12">**********，机柜，Design，A.12</a-select-option>
                            </a-select>
                        </li>
                         <li class="large">
                            <a-select class="select-option" default-value="**********，机柜，Design，A.13" @change="handleChange">
                                <a-select-option value="**********，机柜，Design，A.13">**********，机柜，Design，A.13</a-select-option>
                                <a-select-option value="**********，机柜，Design，A.12">**********，机柜，Design，A.12</a-select-option>
                            </a-select>
                        </li>
                        <li><label>添加对比</label><span><a-icon type="plus" @click="addcontrast" /></span></li>
                    </ul>
                </div>

                <div class="table-header-title">
                    <span>可视化和属性</span>
                </div>
                <div class="table-body">
                    <ul>
                        <li><span>名称</span></li>
                        <li class="large"><span>对象RW10101</span></li>
                        <li class="large"><span>对象RW10102</span></li>
                        <li class="large"><span>对象RW10102</span></li>
                        <li><span></span></li>
                    </ul>
                    <ul>
                        <li><span>生命周期</span></li>
                        <li class="large"><span class="lifecycle">Reviewing</span></li>
                        <li class="large"><span class="lifecycle">Reviewing</span></li>
                        <li class="large"><span class="lifecycle">Reviewing</span></li>
                        <li><span></span></li>
                    </ul>
                    <ul>
                        <li><span>修改人</span></li>
                        <li class="large">
                            <div class="founder-style">
                                  <div class="founder-icon" :style="'background:url('+headerIcon +') no-repeat left center;background-size:contain'"></div>
                                  <label>张三</label>
                             </div>
                        </li>
                        <li class="large">
                             <div class="founder-style">
                                  <div class="founder-icon" :style="'background:url('+headerIcon +') no-repeat left center;background-size:contain'"></div>
                                  <label>张三</label>
                             </div>
                        </li>
                        <li class="large">
                             <div class="founder-style">
                                  <div class="founder-icon" :style="'background:url('+headerIcon +') no-repeat left center;background-size:contain'"></div>
                                  <label>张三</label>
                             </div>
                        </li>
                        <li><span></span></li>
                    </ul>
                    <ul>
                        <li><span>修改时间</span></li>
                        <li class="large"><span>20201-08-12 12:00</span></li>
                        <li class="large"><span>20201-08-12 12:00</span></li>
                        <li class="large"><span>20201-08-12 12:00</span></li>
                        <li><span></span></li>
                    </ul>
                </div>



                <div class="table-header-title">
                    <span>常规</span>
                </div>
                <div class="table-body">
                    <ul>
                        <li><span>来源</span></li>
                        <li class="large"><span>自制</span></li>
                        <li class="large"><span>自制</span></li>
                        <li class="large"><span>自制</span></li>
                        <li><span></span></li>
                    </ul>
                    <ul>
                        <li><span>单位</span></li>
                        <li class="large"><span>kg</span></li>
                        <li class="large"><span>kg</span></li>
                        <li class="large"><span>kg</span></li>
                        <li><span></span></li>
                    </ul>
                    <ul>
                        <li><span>名称</span></li>
                        <li class="large"><span>**********</span></li>
                        <li class="large"><span>**********</span></li>
                        <li class="large"><span>**********</span></li>
                        <li><span></span></li>
                    </ul>
                    <ul>
                        <li><span>视图</span></li>
                        <li class="large"><span class="viewName">InWork</span></li>
                        <li class="large"><span class="viewName">InWork</span></li>
                        <li class="large"><span class="viewName">InWork</span></li>
                        <li><span></span></li>
                    </ul>
                </div>


                 <div class="table-header-title">
                    <span>研发属性</span>
                </div>
                <div class="table-body">
                    <ul>
                        <li><span>是否关键组件</span></li>
                        <li class="large"><span>是</span></li>
                        <li class="large"><span>是</span></li>
                        <li class="large"><span>是</span></li>
                        <li><span></span></li>
                    </ul>
                    <ul>
                        <li><span>目录价</span></li>
                        <li class="large"><span>2000.0</span></li>
                        <li class="large"><span>2000.0</span></li>
                        <li class="large"><span>2000.0</span></li>
                        <li><span></span></li>
                    </ul>
                    <ul>
                        <li><span>是否销售标识</span></li>
                        <li class="large"><span>是</span></li>
                        <li class="large"><span>是</span></li>
                        <li class="large"><span>是</span></li>
                        <li><span></span></li>
                    </ul>
                    <ul>
                        <li><span>SN/批次</span></li>
                        <li class="large"><span>第一批次</span></li>
                        <li class="large"><span>第一批次</span></li>
                        <li class="large"><span>第一批次</span></li>
                        <li><span></span></li>
                    </ul>
                     <ul>
                        <li><span>是否配件标识</span></li>
                        <li class="large"><span>否</span></li>
                        <li class="large"><span>否</span></li>
                        <li class="large"><span>否</span></li>
                        <li><span></span></li>
                    </ul>
                    <ul>
                        <li><span>成本</span></li>
                        <li class="large"><span>**********</span></li>
                        <li class="large"><span>**********</span></li>
                        <li class="large"><span>**********</span></li>
                        <li><span></span></li>
                    </ul>
                </div>

             </div>
        </div>

         <versionComparison></versionComparison>
    </div>
</template>

<script>

export default {
    name: 'historyInformation',
    props: ['fullHeight'],
    
    data(){
        return {
            headerIcon: require('../../../../assets/image/header-icon.png'),
        }
    },
    methods: {
        addcontrast(){
            this.$router.push('/versionComparison')
        },
        onChange(val){
           console.log('val', val)
        },
        handleChange(val){
           console.log('val', val)
        },
        addcontrast(){
            this.$router.push('/')
        }
    }
}
</script>

<style scoped>
.founder-icon{
    width: 16px;
    height: 16px;
    margin-right: 8px;
}
.founder-style{
    height: 32px;
    padding: 0 8px;
    background: rgba(30, 32, 42, 0.04);
    color: #255ED7;
    display: flex;
    float: left;
    align-items: center;
}
.lifecycle{
    height: 22px;
    background: #F0F7FF;
    border-radius: 4px;
    border: 1px solid #A4C9FC;
    padding: 0 8px;
    font-size: 12px;
    color: #255ED7;
}
.viewName{
    height: 22px;
    background: #FFFAF0;
    border-radius: 4px;
    border: 1px solid #FFE4BD;
    padding: 0 8px;
    color: #F69C41;
    font-size: 12px;
}
.table-body ul li.large {
    width: 30%;
}
.table-body ul li {
    border-right: 1px solid rgba(30, 32, 42, 0.06);
    width: 20%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    height: 40px;
    list-style-type: none;
}
.table-body ul {
    overflow: hidden;
    display: flex;
    margin: 0;
    padding: 0;
    border-bottom: 1px solid rgba(30, 32, 42, 0.06);
}
.table-body{
    margin: 0;
    padding: 0;
    overflow: hidden;
}
.select-option{
    width: 100%;
}
.detail-main{
    border: 1px solid rgba(30, 32, 42, 0.06);
    
    margin: 16px 20px;
    box-sizing: border-box;
}
.table-header-title span{
    font-size: 14px;
    color: rgba(30, 32, 42, 0.85);
    font-weight: bold;
}
.table-header-title{
    height: 40px;
    line-height: 40px;
    background: rgba(30, 32, 42, 0.02);
    padding: 0 16px;
    font-weight: 500;
}
.table-header ul li.large {
    width: 30%;
}
.table-header ul li label {
    font-weight: bold;
}
.table-header ul li {
    list-style-type: none;
    border-right: 1px solid rgba(30, 32, 42, 0.06);
    width: 20%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
}
.table-header ul{
    border-bottom: 1px solid rgba(30, 32, 42, 0.06);
    margin: 0;
    padding: 0;
    display: flex;
    background: rgba(30, 32, 42, 0.02);
}
 .table-header{
    height: 40px;
    line-height: 40px;
 }
</style>