<template>
    <div>
        <div  class="content-detail" :style="{height: fullHeight - 85 + 'px'}">
             <searchTop @comparisionBtn="comparisionBtn"></searchTop>
             <div class="classtable">
                <JwTable :options="columns" rowKey="id" :datasource="tableData" :height="fullHeight -170" :isSelect="isSelect" :pagination="pagination"  @selectData="selectData" :selected.sync="selectedRowKeys">
                   <template slot="name" slot-scope="{text,record}">
                        <svg class="jwifont" aria-hidden="true">
                            <use :xlink:href="record.type==='文件夹'?
                                '#jwi-unfolder':record.type==='零件'?
                                '#jwi-part':record.type==='成品'?
                                '#jwi-end-product':record.type==='半成品'?
                                '#jwi-unproduct':record.type==='文档'?
                                '#jwi-document':'#jwi-unfolder'"></use>
                        </svg>
                     <span class="link-name" @click="getDetail(record)">{{ text }}</span>
                   </template>
                   <template slot="version" slot-scope="{text}">
                        <span class="view-text">{{text}}</span>
                   </template>

                     <template slot="founder" slot-scope="{text}">
                        <div class="founder-style">
                           <div class="founder-icon" :style="'background:url('+headerIcon +') no-repeat left center;background-size:contain'"></div>
                           <label>{{text}}</label>
                          </div>
                   </template>
                </JwTable>
             </div>
        </div>
    </div>
</template>

<script>
import searchTop from './searchTop'
import JwTable from "jw_components/table"
import tableModelData from './tableData'
export default {
    name: 'historicalRecord',
    props: ['fullHeight'],
    components: {
        searchTop,
        tableModelData,
        JwTable
    },
    data(){
        return {
              isSelect:true,
              headerIcon: require('../../../../assets/image/header-icon.png'),
              pagination:true,
              columns: [
                {
                title: '名称',
                dataIndex: 'name',
                key: 'name',
                sorter: true,
                scopedSlots: { customRender: 'name' },
                },
                {
                title: '编码',
                dataIndex: 'code',
                sorter: true,
                key: 'code',
                },
                {
                title: '版本',
                dataIndex: 'version',
                key: 'version',
                sorter: true,
                ellipsis: true,
                scopedSlots: { customRender: 'version' },
                },
                {
                title: '创建人',
                dataIndex: 'founder',
                sorter: true,
                key: 'founder',
                scopedSlots: { customRender: 'founder' },
                },
               
                {
                title: '创建时间',
                dataIndex: 'creationTime',
                key: 'creationTime',
                sorter: true,
                scopedSlots: { customRender: 'creationTime' },
                ellipsis: true,
                },
             ],
              tableData: [
                {
                  id: '1',
                  name: '风扇001',
                  code: 'WT00002341',
                  version:'A.3',
                  founder:'深圳开发商',               
                  creationTime:'2021-09-21 13:00',
                },
                {
                  id: '2',
                  name: '风扇001',
                  code: 'WT00002341',
                  version:'A.3',
                  founder:'深圳开发商',               
                  creationTime:'2021-09-21 13:00',
                },
                {
                  id: '3',
                  name: '风扇001',
                  code: 'WT00002341',
                  version:'A.3',
                  founder:'深圳开发商',               
                  creationTime:'2021-09-21 13:00',
                },
                {
                  id: '4',
                  name: '风扇001',
                  code: 'WT00002341',
                  version:'A.3',
                  founder:'深圳开发商',               
                  creationTime:'2021-09-21 13:00',
                },
                {
                  id: '5',
                  name: '风扇001',
                  code: 'WT00002341',
                  version:'A.3',
                  founder:'深圳开发商',               
                  creationTime:'2021-09-21 13:00',               
                },
                {
                  id: '6',
                  name: '风扇001',
                  code: 'WT00002341',
                  version:'A.3',
                  founder:'深圳开发商',               
                  creationTime:'2021-09-21 13:00',
                },
                {
                  id: '7',
                  name: '风扇001',
                  code: 'WT00002341',
                  version:'A.3',
                  founder:'深圳开发商',               
                  creationTime:'2021-09-21 13:00',
                },
                {
                  id: '8',
                  name: '风扇001',
                  code: 'WT00002341',
                  version:'A.3',
                  founder:'深圳开发商',               
                  creationTime:'2021-09-21 13:00',
                },
                {
                  id: '9',
                  name: '风扇001',
                  code: 'WT00002341',
                  version:'A.3',
                  founder:'深圳开发商',               
                  creationTime:'2021-09-21 13:00',
                },
                {
                  id: '10',
                  name: '风扇001',
                  code: 'WT00002341',
                  version:'A.3',
                  founder:'深圳开发商',               
                  creationTime:'2021-09-21 13:00',
                },
                {
                  id: '11',
                  name: '风扇001',
                  code: 'WT00002341',
                  version:'A.3',
                  founder:'深圳开发商',               
                  creationTime:'2021-09-21 13:00',
                },
                 {
                  id: '12',
                  name: '风扇001',
                  code: 'WT00002341',
                  version:'A.3',
                  founder:'深圳开发商',               
                  creationTime:'2021-09-21 13:00',
                },
                {
                  id: '13',
                  name: '风扇001',
                  code: 'WT00002341',
                  version:'A.3',
                  founder:'深圳开发商',               
                  creationTime:'2021-09-21 13:00',
                }
              ],
              selectedRowKeys:[],
              
        }
    },
    methods: {
      comparisionBtn(){
           if(this.selectedRowKeys.length > 0) {
              this.$router.push('/versionComparison')
           }else {
              this.$warning('请至少选择一条数据!')
           }
      },
      selectData(selectedRowKeys, selectedRow){
            this.selectedRowKeys = selectedRowKeys
        },
    
    },
    created(){
     
    }
}
</script>

<style scoped>
.founder-icon{
    width: 16px;
    height: 16px;
    margin-right: 8px;
}
.founder-style{
    height: 32px;
    padding: 0 8px;
    background: rgba(30, 32, 42, 0.04);
    color: #255ED7;
    display: flex;
    float: left;
    align-items: center;
}
.view-text{
    background: rgba(30, 32, 42, 0.04);
    border-radius: 4px;
    border: 1px solid rgba(30, 32, 42, 0.15);
    height: 22px;
    line-height: 22px;
    padding: 0 7px;
}
.link-name {
        color: #255ed7;
        cursor: pointer;
    }
.jwifont {
		width: 16px;
		min-width: 16px;
		height: 16px;
		min-height: 16px;
		margin-right: 8px;
	}
.content-detail{
    padding: 5px 20px;
    margin-top: 0px;
}
.classtable{
    margin-top: 16px;
}
.page-wrap {
    padding: 20px 0 20px 20px;
}
</style>