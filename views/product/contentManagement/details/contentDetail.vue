<template>
    <div class="content-detail">
        <back-header :title="backTitle" @back="goBack"></back-header>
        <div class="shadow-wrap">
            
            <div class="operation-btn" v-if="currentKey=='2'">
                <a-button class="BOM-export">BOM导入</a-button>
                <a-button class="BOM-export">BOM导出</a-button>
            </div>


            <a-tabs @change="callback">
                <a-tab-pane key="1" tab="详情">
                    <detail :fullHeight="fullHeight"></detail>
                </a-tab-pane>
                <a-tab-pane key="2" tab="结构">
                   <structure :fullHeight="fullHeight"></structure>
                </a-tab-pane>
                <a-tab-pane key="3" tab="关系链">
                   <relationChain :fullHeight="fullHeight"></relationChain>
                </a-tab-pane>
                <a-tab-pane key="4" tab="相关对象">
                    <RelatedObjects :fullHeight="fullHeight"></RelatedObjects>
                </a-tab-pane>
                <a-tab-pane key="5" tab="Where Use">
                     <WhereUse :fullHeight="fullHeight"></WhereUse>
                </a-tab-pane>
                <a-tab-pane key="6" tab="AML/AVL">
                     <bomAvlAml :fullHeight="fullHeight"></bomAvlAml>
                </a-tab-pane>
                 <a-tab-pane key="7" tab="变更记录">
                     <changeRecord></changeRecord>
                </a-tab-pane>
                <a-tab-pane key="8" tab="历史记录">
                     <historicalRecord :fullHeight="fullHeight"></historicalRecord>
                </a-tab-pane>
            </a-tabs>
        </div>
    </div>
</template>

<script>
import backHeader from '../../back-header';
import detail from './detail' //详情
import structure from './structure'  //结构
import relationChain from './relationChain'  //关系链
import RelatedObjects from './RelatedObjects'  //相关对象
import WhereUse from './WhereUse'  //WhereUse
import bomAvlAml from './bom-avl-aml'  //   Avl/Aml
import historicalRecord  from './historicalRecord'  //历史记录
import changeRecord  from '../../changeManage/index'  //变更记录
export default {
    name: 'contentDetail',
    props: [
        'fullHeight',
        'backTitle',
    ],
    components: {
        backHeader,
        detail,
        structure,
        relationChain,
        RelatedObjects,
        WhereUse,
        bomAvlAml,
        historicalRecord,
        changeRecord
    },
    data() {
        return {
            currentKey: null
        };
    },
    mounted() {

    },
    methods: {
        callback(key) {
            console.log(key);
            this.currentKey = key
        },
        goBack() {
            this.$emit('back');
        },
        onSearch() {
           
        },
    },
};
</script>

<style lang="less" scoped>
/deep/.ant-tabs-bar{
    margin:0;
}
.BOM-export{
    margin-left: 8px;
    color: rgba(30, 32, 42, 0.65);
    font-size: 14px;
}
.operation-btn {
    position: absolute;
    right: 20px;
    height: 53px;
    display: flex;
    align-items: center;
}
.operation{
    background: #f00;
}
.shadow-wrap {
    margin: 5px;
    background: var(--light);
    box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
    border-radius: 4px;
    position: relative;
}
.jwifont {
    width: 16px;
    height: 16px;
}
.content-detail {
    /deep/.ant-tabs-bar {
        padding-right: 20px;
    }
    /deep/.ant-tabs-nav-container {
        line-height: 29px;
    }
    /deep/.ant-tabs-extra-content {
        line-height: 52px;
    }
    /deep/.ant-tabs-nav /deep/.ant-tabs-tab {
        margin: 0;
    }
    .detail-input {
        width: 216px;
    }
}
</style>
