<template>
    <div>
        <div class="operation">
            <div class="operation-left">
              <a-button class="replacement" @click="addReplacement">添加替代件</a-button>
              <a-input-search placeholder="请输入搜索关键字" class="search-btn"  @search="onSearch" />
            </div>
            <div class="operation-right">
                <a-dropdown>
                <a-menu slot="overlay" @click="handleMenuClick">
                    <a-menu-item key="1" @click="deleteData"> 删除 </a-menu-item>
                </a-menu>
                <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /> </a-button>
                </a-dropdown>
            </div>
        </div>

        <div class="classtable">
                <a-table :columns="columns" :data-source="tableData" :scroll="{y: fullHeight - 210 }" bordered :row-selection="{ selectedRowKeys:this.selectedRowKeys, onChange: this.onSelectChange}" :pagination="false">
                    <template slot-scope="text, record" slot="name">
                           <svg class="jwifont" aria-hidden="true">
                                        <use :xlink:href="record.type==='文件夹'?
                                            '#jwi-unfolder':record.type==='零件'?
                                            '#jwi-part':record.type==='成品'?
                                            '#jwi-end-product':record.type==='半成品'?
                                            '#jwi-unproduct':record.type==='文档'?
                                            '#jwi-document':'#jwi-unfolder'"></use>
                            </svg>
                            <span class="link-name" @click="getDetail(record)">{{ text }}</span>
                     </template>

                     <template slot="type" slot-scope="text">
                         <span class="view-text">{{ text }}</span>
                     </template>

                     <template slot="twoway" slot-scope="text, record, $index">
                         <span>
                             <a-switch v-if="text" checked @change="onChange(text, $index)">
                                <a-icon slot="checkedChildren" type="check" />
                                <a-icon slot="unCheckedChildren" type="close" />
                              </a-switch>
                               <a-switch v-else  @change="onChange(text, $index)">
                                <a-icon slot="checkedChildren" type="check" />
                                <a-icon slot="unCheckedChildren" type="close" />
                              </a-switch>
                            </span>
                     </template>

                       <template slot="operation" slot-scope="text, record, $index">
                           <span class="delete" @click="singleData($index)">{{$t('txt_delete')}}</span>
                       </template>
                </a-table>
        </div>

         <a-modal
            title="添加替代件"
            :visible="visible"
            class="model"
            @ok="handleOk"
            :width="width"
            @cancel="handleCancel"
            >
            <dialogReplacementPart></dialogReplacementPart>
         </a-modal>

    </div>
</template>

<script>
import dialogReplacementPart from './dialog-replacement-part'
export default {
    name:'replacementParts',
    props:['fullHeight'],
    components: {
        dialogReplacementPart
    },
    data(){
        return {
              visible: false,
              width:'960px',
              selectedRowKeys: [],
              multipleSelection: [],
              columns: [
                {
                title: '名称',
                dataIndex: 'name',
                key: 'name',
                sorter: true,
                scopedSlots: { customRender: 'name' },
                },
                {
                title: '编码',
                dataIndex: 'code',
                sorter: true,
                key: 'code',
                },
               
                {
                title: '替代类型',
                dataIndex: 'alternativetypes',
                sorter: true,
                key: 'alternativetypes',
                scopedSlots: { customRender: 'alternativetypes' },
                },
                 {
                title: '类型',
                dataIndex: 'type',
                key: 'type',
                sorter: true,
                ellipsis: true,
                align:"center",
                scopedSlots: { customRender: 'type' },
                },
                {
                title: '双向替代',
                dataIndex: 'twoway',
                key: 'twoway',
                sorter: true,
                align: "center",
                width: '130px',
                scopedSlots: { customRender: 'twoway' },
                ellipsis: true,
                },
                 {
                title: '操作',
                dataIndex: 'operation',
                key: 'operation',
                align: "center",
                sorter: true,
                scopedSlots: { customRender: 'operation' },
                ellipsis: true,
                }
              ],
              tableData: [
              {
                key: '1',
                name: '风扇001',
                code:'**********',
                alternativetypes: 'Part',
                type: '文件夹',
                twoway: true,
              },
              {
                key: '2',
                name: '风扇001',
                code:'**********',
                alternativetypes: 'Part',
                type: '文件夹',
                twoway: false,
              },
              {
                key: '3',
                name: '风扇001',
                code:'**********',
                alternativetypes: 'Part',
                type: '文件夹',
                twoway: true,
              },
              {
                key: '4',
                name: '风扇001',
                code:'**********',
                alternativetypes: 'Part',
                type: '文件夹',
                twoway: false,
              },
              {
                key: '5',
                name: '风扇001',
                code:'**********',
                alternativetypes: 'Part',
                type: '文件夹',
                twoway: true,
              },
              {
                key: '6',
                name: '风扇001',
                code:'**********',
                alternativetypes: 'Part',
                type: '文件夹',
                twoway: false,
              },
              {
                key: '7',
                name: '风扇001',
                code:'**********',
                alternativetypes: 'Part',
                type: '文件夹',
                twoway: false,
              },
             ],
        }
    },
    methods: {
        addReplacement(){
            this.visible = true
        },
        handleCancel(){
           this.visible = false
        },
        handleOk(){
           this.visible = true
        },
        onChange(text, index) {
            if(text) {
                this.$set(this.tableData[index], 'twoway', false)
            }else {
                this.$set(this.tableData[index], 'twoway', true)
            }
        },
        deleteData(){
            if(this.multipleSelection.length > 0){
                 this.tableData = this.tableData.filter(item => {
                    return !this.multipleSelection.find(row => {
                        return row.key == item.key
                    })
               })

            }else {
                this.$warning('请至少选择一条数据!')
            }
        },
        singleData(index){
           this.tableData.splice(index, 1)
        },
        onSearch(val){
           console.log('val', val)
        },
        handleMenuClick(val){
           console.log('val', val)
        },
        onChange(checked){
           console.log('checked', checked)
        },
         onSelectChange(selectedRowKeys, selectedRows) {
            this.selectedRowKeys = selectedRowKeys
            this.multipleSelection = selectedRows
        },
    }
}
</script>

<style scoped>
.model>>>.ant-modal-footer{
    border-top: none;
}
.model>>>.ant-modal-body {
    padding: 0px 20px;
}
.delete{
    color: #F6445A;
    cursor: pointer;
}
.replacement{
    background: #255ED7;
    color: #fff;
}
.view-text{
    background: rgba(30, 32, 42, 0.04);
    border-radius: 4px;
    border: 1px solid rgba(30, 32, 42, 0.15);
    height: 22px;
    line-height: 22px;
    padding: 0 7px;
}
.product-info {
  display: flex;
  align-items: center;
}
.icon-product{
    width: 15px;
    height: 15px;
    margin-right: 10.5px;
}
.link-name {
        color: #255ed7;
        cursor: pointer;
    }
.jwifont {
		width: 16px;
		min-width: 16px;
		height: 16px;
		min-height: 16px;
		margin-right: 8px;
	}
.operation{
    display: flex;
    margin-bottom: 16px;
    justify-content: space-between;
}
.search-btn{
    width: 216px;
    margin-left: 8px;
}

</style>