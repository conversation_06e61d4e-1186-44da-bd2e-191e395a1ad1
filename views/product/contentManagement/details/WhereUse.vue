<template>
    <div>
        <div class="content-detail" :style="{height: fullHeight - 85 + 'px'}">
          
             <search-structure :isDelete="selectedRowKeys.length > 0 ? false : true" :selectedRowKeys="selectedRowKeys"
             @deleteBatchData="deleteBatchData"></search-structure>
                
                 <div class="classtable" :style="{height: fullHeight - 225 + 'px'}">
                   <JwTable :options="columns" rowKey="id" :datasource="tableData" :height="fullHeight -170" 
                      @selectData="selectData" :pagination="pagination" :isSelect="isSelect" :childrenColumnName="childrenColumnName" :selected.sync="selectedRowKeys">
                     <template slot-scope="{text, record}" slot="name">                              
                              <svg class="jwifont" aria-hidden="true">
                                  <use :xlink:href="record.type==='文件夹'?
                                      '#jwi-unfolder':record.type==='零件'?
                                      '#jwi-part':record.type==='成品'?
                                      '#jwi-end-product':record.type==='半成品'?
                                      '#jwi-unproduct':record.type==='文档'?
                                      '#jwi-document':'#jwi-unfolder'"></use>
                              </svg>
                              <span class="link-name" @click="getDetail(record)">{{ text }}</span>
                              <span class="operation-btn" v-if="record.isoperation">
                                  <img src="../../../../assets/image/replacement.png" class="part-icon">
                              </span>
                        </template>

                      <template slot="viewName" slot-scope="{text}">
                          <span class="view-text">{{ text }}</span>
                      </template>

                        <template slot="version" slot-scope="{text}">
                          <span class="view-text">{{ text }}</span>
                      </template>
                   </JwTable>
              </div>

                  
            
        </div>
    </div>
</template>

<script>
import searchStructure from './search-structure'
import JwTable from "jw_components/table"
export default {
    name: 'WhereUse',
    props:['fullHeight'],
    components: {
        searchStructure,
        JwTable
    },
    data(){
        return {
           pagination:true,
           childrenColumnName: 'children',
           multipleSelection:[],
           selectedRowKeys:[],
           isSelect:true,
           columns: [
                {
                title: '名称',
                dataIndex: 'name',
                key: 'name',
                sorter: true,
                scopedSlots: { customRender: 'name' },
                },
                {
                title: '物料号',
                dataIndex: 'MaterialNo',
                sorter: true,
                key: 'MaterialNo',
                },
                {
                title: '视图',
                dataIndex: 'viewName',
                sorter: true,
                key: 'viewName',
                scopedSlots: { customRender: 'viewName' },
                },
                 {
                title: '版本',
                dataIndex: 'version',
                key: 'version',
                sorter: true,
                ellipsis: true,
                scopedSlots: { customRender: 'version' },
                },
                {
                title: '创建时间',
                dataIndex: 'creationTime',
                key: 'creationTime',
                sorter: true,
                scopedSlots: { customRender: 'creationTime' },
                ellipsis: true,
                },
                {
                title: '最后修改时间',
                dataIndex: 'modifyTime',
                key: 'modifyTime',
                sorter: true,
                scopedSlots: { customRender: 'modifyTime' },
                ellipsis: true,
                },
             ],
            tableData: [
              {
                id: '1',
                name: '风扇001',
                MaterialNo: 'WT00002341',
                viewName:'Design',
                version:'A.3',
                creationTime:'2021-09-21 13:00',
                modifyTime:'2021-09-21 13:00',
              },
              {
                id: '2',
                name: '风扇001',
                MaterialNo: 'WT00002341',
                viewName:'Design',
                version:'A.3',
                creationTime:'2021-09-21 13:00',
                modifyTime:'2021-09-21 13:00',
                children: [{
                    id: '112',
                    name: '风扇001',
                    MaterialNo: 'WT00002341',
                    viewName:'Design',
                    version:'A.3',
                    creationTime:'2021-09-21 13:00',
                    modifyTime:'2021-09-21 13:00',
                },{
                    id: '156',
                    name: '风扇001',
                    MaterialNo: 'WT00002341',
                    viewName:'Design',
                    version:'A.3',
                    creationTime:'2021-09-21 13:00',
                    modifyTime:'2021-09-21 13:00',
                }]
              },
              {
                id: '3',
                name: '风扇001',
                MaterialNo: 'WT00002341',
                viewName:'Design',
                version:'A.3',
                creationTime:'2021-09-21 13:00',
                modifyTime:'2021-09-21 13:00',
              },
              {
                id: '4',
                name: '风扇001',
                MaterialNo: 'WT00002341',
                viewName:'Design',
                version:'A.3',
                creationTime:'2021-09-21 13:00',
                modifyTime:'2021-09-21 13:00',
              },
              {
                id: '5',
                name: '风扇001',
                MaterialNo: 'WT00002341',
                viewName:'Design',
                version:'A.3',
                creationTime:'2021-09-21 13:00',
                modifyTime:'2021-09-21 13:00',
              },
              {
                id: '6',
                name: '风扇001',
                MaterialNo: 'WT00002341',
                viewName:'Design',
                version:'A.3',
                creationTime:'2021-09-21 13:00',
                modifyTime:'2021-09-21 13:00',
              },
              {
                id: '7',
                name: '风扇001',
                MaterialNo: 'WT00002341',
                viewName:'Design',
                version:'A.3',
                creationTime:'2021-09-21 13:00',
                modifyTime:'2021-09-21 13:00',
              },
              {
                id: '8',
                name: '风扇001',
                MaterialNo: 'WT00002341',
                viewName:'Design',
                version:'A.3',
                creationTime:'2021-09-21 13:00',
                modifyTime:'2021-09-21 13:00',
              },
               {
                id: '9',
                name: '风扇001',
                MaterialNo: 'WT00002341',
                viewName:'Design',
                version:'A.3',
                creationTime:'2021-09-21 13:00',
                modifyTime:'2021-09-21 13:00',
              },
              {
                id: '10',
                name: '风扇001',
                MaterialNo: 'WT00002341',
                viewName:'Design',
                version:'A.3',
                creationTime:'2021-09-21 13:00',
                modifyTime:'2021-09-21 13:00',
              },
              {
                id: '11',
                name: '风扇001',
                MaterialNo: 'WT00002341',
                viewName:'Design',
                version:'A.3',
                creationTime:'2021-09-21 13:00',
                modifyTime:'2021-09-21 13:00',
              }, {
                id: '12',
                name: '风扇001',
                MaterialNo: 'WT00002341',
                viewName:'Design',
                version:'A.3',
                creationTime:'2021-09-21 13:00',
                modifyTime:'2021-09-21 13:00',
              },
              {
                id: '13',
                name: '风扇001',
                MaterialNo: 'WT00002341',
                viewName:'Design',
                version:'A.3',
                creationTime:'2021-09-21 13:00',
                modifyTime:'2021-09-21 13:00',
              },
              {
                id: '14',
                name: '风扇001',
                MaterialNo: 'WT00002341',
                viewName:'Design',
                version:'A.3',
                creationTime:'2021-09-21 13:00',
                modifyTime:'2021-09-21 13:00',
              },
            ],
        }
    },
    methods: {
       selectData(selectedRowKeys, selectedRows){
          console.log(selectedRowKeys,selectedRows)
          this.selectedRowKeys = selectedRows
      },
   
        deleteBatchData(selectData){
              this.tableData = this.tableData.filter(item=> {
                return !selectData.find(row=> {
                    return item.id==row.id
                })
            })
        }
         
    }
}
</script>

<style scoped>
.content-detail{
    padding: 5px 20px;
    margin-top: 0px;
}
.table-wrap {
		margin: 0;
 }
.page-wrap {
    padding: 20px 0 20px 20px;
}
.link-name {
        color: #255ed7;
        cursor: pointer;
    }
.jwifont {
		width: 16px;
		min-width: 16px;
		height: 16px;
		min-height: 16px;
		margin-right: 8px;
}

 .view-text{
    background: rgba(30, 32, 42, 0.04);
    border-radius: 4px;
    border: 1px solid rgba(30, 32, 42, 0.15);
    height: 22px;
    line-height: 22px;
    padding: 0 7px;
}
</style>