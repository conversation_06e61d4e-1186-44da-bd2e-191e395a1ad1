<template>
    <div class="content-detail" :style="{height: fullHeight - 95 + 'px'}">
          <search-structure :isDelete="selectedRowKeys.length > 0 ? false : true"  :selectedRowKeys="selectedRowKeys" 
               @deleteBatchData="deleteBatchData"></search-structure>

               <!-- <JwTable :options="columns" rowKey="id" :datasource="tableData" :height="fullHeight - 190" 
                 :pagination="pagination" :defaultExpandAllRows="defaultExpandAllRows" @selectData="selectData" :isSelect="isSelect"  :childrenColumnName="childrenColumnName" :selected.sync="selectedRowKeys">
                         <template slot-scope="{text, record}" slot="name">                              
                                   <svg class="jwifont" aria-hidden="true">
                                        <use :xlink:href="record.type==='文件夹'?
                                            '#jwi-unfolder':record.type==='零件'?
                                            '#jwi-part':record.type==='成品'?
                                            '#jwi-end-product':record.type==='半成品'?
                                            '#jwi-unproduct':record.type==='文档'?
                                            '#jwi-document':'#jwi-unfolder'"></use>
                                    </svg>
                                    <span class="link-name" @click="getDetail(record)">{{ text }}</span>
                                    <span class="operation-btn" v-if="record.isoperation">
                                        <img src="../../../../assets/image/replacement.png" class="part-icon" >
                                    </span>
                           </template>
                               
                            <template slot-scope="{ text }" slot="viewName">
                                  <span class="view-text">{{text}}</span>  
                            </template>

                            <template slot-scope="{ text }" slot="version">
                                  <span class="view-text">{{text}}</span>  
                            </template>

                             <template slot-scope="{text, record}" slot="number">
                                  <span><a-input-number  v-model.trim="record.number"  class="number" :disabled="!record.isnumber" :min="1"   /></span>  
                            </template>

                             <template slot-scope="{text, record}" slot="line">
                                   <span><a-input-number v-model.trim="record.line"  class="line" :disabled="!record.isline" :min="1" /></span> 
                            </template>

                              <template slot-scope="{text, record}" slot="position">
                                   <span><a-input-number v-model.trim="record.position"  class="position" :disabled="!record.isposition" :min="1" /></span> 
                            </template>

                            <template slot="operation" slot-scope="text,record,$index">
                                <a class="add-btn" @click="addChildObjects">添加子对象</a>
                                <a class="delete-btn" @click="singleData($index)">删除</a>
                            </template>
               </JwTable> -->


                <JwTable :columns="columns" rowKey="id" :showPagination="pagination" :height="fullHeight - 100"  :tableData="tableData">
                           <template slot="name" slot-scope="text, record">                              
                                   <svg class="jwifont" aria-hidden="true">
                                        <use :xlink:href="record.type==='文件夹'?
                                            '#jwi-unfolder':record.type==='零件'?
                                            '#jwi-part':record.type==='成品'?
                                            '#jwi-end-product':record.type==='半成品'?
                                            '#jwi-unproduct':record.type==='文档'?
                                            '#jwi-document':'#jwi-unfolder'"></use>
                                    </svg>
                                    <span class="link-name" @click="getDetail(record)">{{ text }}</span>
                           </template>
                               
                             <template slot-scope="{ text }" slot="viewName">
                                  <span class="view-text">{{text}}</span>  
                            </template>

                          <!--   <template slot-scope="{ text }" slot="version">
                                  <span class="view-text">{{text}}</span>  
                            </template>

                             <template slot-scope="{text, record}" slot="number">
                                  <span><a-input-number  v-model.trim="record.number"  class="number" :disabled="!record.isnumber" :min="1"   /></span>  
                            </template>

                             <template slot-scope="{text, record}" slot="line">
                                   <span><a-input-number v-model.trim="record.line"  class="line" :disabled="!record.isline" :min="1" /></span> 
                            </template>

                              <template slot-scope="{text, record}" slot="position">
                                   <span><a-input-number v-model.trim="record.position"  class="position" :disabled="!record.isposition" :min="1" /></span> 
                            </template>-->

                            <template slot="operation" slot-scope="text,record,$index">
                                <a class="add-btn" @click="addChildObjects">添加子对象</a>
                                <a class="delete-btn" @click="singleData($index)">删除</a>
                            </template> 
                </JwTable>

                <a-drawer
                    placement="right"
                    :closable="false"
                    :visible="visible"
                    class="model"
                    width="840"
                    :after-visible-change="afterVisibleChange"
                    @close="onClose"
                    >
                        <div slot="title" class="title-name">
                                <div class="title-icon">
                                    <svg class="jwifont" aria-hidden="true"><use :xlink:href="'#jwi-part'"></use></svg>
                                    <p>对象，WT000002342，Design，A.2</p>
                                </div>
                                <div class="operation-btn">
                                    <span @click="refreshBtn"><img src="../../../../assets/image/refresh.png"></span>
                                    <span @click="closedObject"><img src="../../../../assets/image/delete.png"></span>
                                </div>
                        </div>

                        <a-tabs default-active-key="1" @change="callback">
                                <a-tab-pane key="1" tab="详细信息">
                                    <strctureInfo></strctureInfo>
                                </a-tab-pane>
                                <a-tab-pane key="2" tab="使用">
                                    <useInfo :fullHeight="fullHeight"></useInfo>
                                </a-tab-pane>
                                <a-tab-pane key="3" tab="替代件">
                                    <replacementParts :fullHeight="fullHeight"></replacementParts>
                                </a-tab-pane>
                        </a-tabs>
                </a-drawer>

                <a-modal
                    title="添加对象"
                    class="model"
                    :visible="objectvisible"
                    @ok="handleOk"
                    :width="width"
                    @cancel="handleCancel"
                    >
                    <dialogAddObject></dialogAddObject>
                </a-modal>

       </div>
</template>

<script>
import JwTable from '../../../../components/table/index'
import searchStructure from './search-structure'
import dialogAddObject from './dialog-add-Object'
import strctureInfo from './strctureInfo'
import useInfo from './useInfo'
import replacementParts from './replacementParts'
// import JwTable from "jw_components/table"
export default {
    name: 'structure',
    props: ['fullHeight'],
    data(){
        return {
           isSelect: true,
           width: '960px',
           objectvisible: false,
           defaultExpandAllRows: true,
           pagination: false,
           childrenColumnName:'children',
           visible: false,
           columns: [
                {
                title: '名称',
                prop: 'name',
                key: 'name',
                sorter: true,
                display:'名称',
                scopedSlots: { customRender: 'name' },
                formatter: (row, value) => {
                        let h = this.$createElement   
                        let iconType = this.conversionTo(row.type)   
                        return (
                            <span style="display:flex">
                               <a-icon type={iconType} />
                               <span class="link-name">{value}</span>
                            </span>
                        );
                    }
                },
                {
                title: '编码',
                prop: 'code',
                sorter: true,
                key: 'code',
                display:'编码',
                },
                {
                title: '视图',
                prop: 'viewName',
                sorter: true,
                key: 'viewName',
                display:'视图',
                scopedSlots: { customRender: 'viewName' },
                formatter: (row, value) => {
                     return (
                         <span class="view-text">{value}</span>
                     )
                  }
                },
                 {
                title: '版本',
                prop: 'version',
                key: 'version',
                sorter: true,
                ellipsis: true,
                display:'版本',
                scopedSlots: { customRender: 'version' },
                formatter: (row, value) => {
                     return (
                         <span class="view-text">{value}</span>
                     )
                  }
              
                },
                {
                title: '数量',
                prop: 'number',
                key: 'number',
                sorter: true,
                display:'数量',
                width: '130px',
                scopedSlots: { customRender: 'number' },
                 formatter: (row, value) => {
                         return (<span><a-input-number  v-model={row.number}  class="number" disabled={!row.isnumber} /></span>)
                  },
                customCell: (record)=>{
                        return {
                        on: {
                            mouseenter: (event) => {
                                this.$set(record,'isnumber', true)
                            },
                            mouseleave: (event) => {
                                 this.$set(record,'isnumber', false)
                            }
                          }
                        }
                    },
                ellipsis: true,
                },
                {
                title: '行号',
                display:'行号',
                prop: 'line',
                key: 'line',
                sorter: true,
                width: '130px',
                scopedSlots: { customRender: 'line' },
                 formatter: (row, value) => {
                       return (<span><a-input-number  v-model={row.line}  class="number" disabled={!row.isline} /></span>)
                  },
                customCell: (record)=>{
                        return {
                        on: {
                            mouseenter: (event) => {
                                this.$set(record,'isline', true)                            
                            },
                             mouseleave: (event) => {
                                 this.$set(record,'isnumber', false)
                            }
                          }
                        }
                    },
                ellipsis: true,
                },
                {
                title: '位号',
                prop: 'position',
                key: 'position',
                sorter: true,
                width: '130px',
                display:'位号',
                scopedSlots: { customRender: 'position' },
                formatter: (row, value) => {
                        return (<span><a-input-number  v-model={row.position}  class="number" disabled={!row.isposition} /></span>)
                  },
                customCell: (record)=>{
                        return {
                        on: {
                            mouseenter: (event) => {
                                this.$set(record,'isposition', true)                            
                            },
                            mouseleave: (event) => {
                               this.$set(record,'isnumber', false)
                            }
                          }
                        }
                    },
                ellipsis: true,
                },
                 {
                title: '操作',
                prop: 'operation',
                key: 'operation',
                sorter: true,
                display:'操作',
                scopedSlots: { customRender: 'operation' },
                 formatter: (row, value) => {
                     return (
                         <span>
                          <a class="add-btn" on-click={ () => this.addChildObjects()}>添加子对象</a>
                         
                          <a class="delete-btn" on-click={ () => this.singleData()}>删除</a>
                          </span>
                     )

                 },
                ellipsis: true,
                }
             ],
            tableData: [
              {
                id: '1',
                name: '风扇001',
                type: '文件夹',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: '12',
                line:'22',
                position:'32',
                isnumber: false,
                isline: false,
                isposition: false,
              },
              {
                id: '2',
                name: '风扇001',
                type: '零件',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: '102',
                line:'228',
                position:'302',
                isnumber: false,
                isline: false,
                isposition: false,
                 children: [{
                    id: '112',
                    name: '风扇001',
                    type: '文档',
                    code:'FSD32',
                    version:'A.3',
                    viewName:'Design',
                    number: '102',
                    line:'228',
                    isnumber: false,
                    isline: false,
                    isposition: false,
                    position:'302',
                },{
                    id: '113',
                    name: '风扇001',
                    type: '文档',
                    code:'FSD32',
                    version:'A.3',
                    viewName:'Design',
                    number: '102',
                    line:'228',
                    isnumber:false,
                    isline:false,
                    isposition:false,
                    position:'302',
                }]
              },
              {
                id: '3',
                name: '风扇001',
                type: '半成品',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: '102',
                line:'228',
                isnumber:false,
                isline:false,
                isposition:false,
                position:'302',
              },
              {
                id: '4',
                name: '风扇001',
                type: '半成品',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: '102',
                line:'228',
                isnumber:false,
                isline:false,
                isposition:false,
                position:'302',
              },
              {
                id: '5',
                name: '风扇001',
                type: '文档',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: '102',
                line:'228',
                isnumber:false,
                isline:false,
                isposition:false,
                position:'302',
              },
              {
                id: '6',
                name: '风扇001',
                type: '文档',
                code:'FSD32',
                version:'A.3',
                viewName:'Design',
                number: '102',
                line:'228',
                isnumber:false,
                isline:false,
                isposition:false,
                position:'302',
                children: [{
                    id: '12',
                    name: '风扇001',
                    type: '文档',
                    code:'FSD32',
                    version:'A.3',
                    viewName:'Design',
                    number: '102',
                    line:'228',
                    isnumber:false,
                    isline:false,
                    isposition:false,
                    position:'302',
                },{
                    id: '13',
                    name: '风扇001',
                    type: '文档',
                    code:'FSD32',
                    version:'A.3',
                    viewName:'Design',
                    number: '102',
                    line:'228',
                    isnumber:false,
                    isline:false,
                    isposition:false,
                    position:'302',
                }]
              },
              {
                id: '7',
                name: '风扇001',
                code:'FSD32',
                type: '文档',
                version:'A.3',
                viewName:'Design',
                number: '102',
                line:'228',
                isnumber:false,
                isline:false,
                isposition:false,
                position:'302',
              },
              
            ],
            selectedRowKeys:[],
            multipleSelection:[]
        }
    },
   components: {
		searchStructure,
        dialogAddObject,
        strctureInfo,
        useInfo,
        replacementParts,
        JwTable
	},
    methods: {
        conversionTo(iconType){
            switch(iconType){
                case '文件夹':
                    return 'unfolder';
                    break;
                case '成品':
                    return 'part';
                    break;
                case '零件':
                    return 'end-product';
                    break;
                case '半成品':
                    return 'unproduct';
                    break;
                case '文档':
                    return 'document';
                    break;
                     default:
                     return '';
            }
        },
         selectData(selectedRowKeys, selectedRows){  //选择当前表格数据
             this.selectedRowKeys = selectedRowKeys
         },
        closedObject(){
            this.visible = false
        },
        refreshBtn(){

        },
        singleData(index){
            this.tableData.splice(index, 1)
        },
        deleteBatchData(selectData){
            this.tableData = this.tableData.filter(item=> {
                return !selectData.find(row=> {
                    return item.id==row
                })
            })
        },
         callback(val){
           console.log('val', val)
        },
        addChildObjects(){
           this.objectvisible = true
        },
        handleOk(){
           this.objectvisible = false
        },
        handleCancel(){
           this.objectvisible = false
        },
        onClose(){
            this.visible = false
        },
        getDetail(row) {
            this.visible = true
            console.log('row', row)
        },
        getfocus(record){
            this.$set(record, 'isoperation', true)
        },
        afterVisibleChange(val) {
            console.log('visible', val);
         }       
    },
}
</script>

<style scoped>
.model>>>.ant-drawer-header{
    border-bottom: none;
}
.model>>>.ant-drawer-body{
    padding: 0 24px;
}
.operation-btn span {
    margin-left: 10px;
    cursor: pointer;
}
.operation-btn span img {
    width: 16px;
    height: 16px;
}
.operation-btn{
    display: flex;
}
.title-name{
    display: flex;
    justify-content: space-between;
}
.title-icon{
    display: flex;
    align-items: center;
}
.title-icon p{
    margin: 0;
    font-size: 14px;
}
.model>>>.ant-modal-body{
    padding: 16px 20px;
}
.model>>>.ant-modal-footer {
    border-top: none;
}
.add-btn,.detail-btn,.delete-btn{
    margin-right: 10px;
    color: #255ED7;
}
.view-text{
    background: rgba(30, 32, 42, 0.04);
    border-radius: 4px;
    border: 1px solid rgba(30, 32, 42, 0.15);
    height: 22px;
    line-height: 22px;
    padding: 0 7px;
}
.delete-btn{
    color: #F6445A
}
.part-icon{
    width: 16px;
    height: 16px;
}
.operation-btn {
    float: right;
}
.product-info {
  display: flex;
  align-items: center;
}
.icon-product{
    width: 15px;
    height: 15px;
    margin-right: 10.5px;
}
.content-detail{
    padding: 5px 20px;
    margin-top: 0px;
}
.link-name {
        color: #255ed7;
        cursor: pointer;
    }
.jwifont {
		width: 16px;
		min-width: 16px;
		height: 16px;
		min-height: 16px;
		margin-right: 8px;
	}
</style>