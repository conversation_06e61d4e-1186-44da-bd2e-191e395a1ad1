<template>
    <div>
        <div  class="content-detail" :style="{height: fullHeight - 85 + 'px'}">
              <div class="search-info">
                  <span>
                      <a-button class="new-add" @click="newAml">新建AML</a-button>
                      <a-button class="new-add" @click="newAvl">新建AVL</a-button>
                      <a-input-search placeholder="请输入搜索关键字" class="search-btn" @search="onSearch" />
                  </span>
                  <div class="operation-btn">
                      <span><img src="../../../../assets/image/filter.png"></span>
                      <a-dropdown :disabled="selectedRowKeys.length > 0 ? false : true"> 
                        <a-menu slot="overlay">
                            <a-menu-item key="1"  @click="deleteBatchData">{{$t('txt_delete')}}</a-menu-item>
                        </a-menu>
                        <a-button style="margin-left: 8px">批量删除<a-icon type="down" /> </a-button>
                        </a-dropdown>
                  </div>
              </div>

              <div class="classtable" :style="{height: fullHeight - 220 + 'px'}">
                   <JwTable :options="columns" rowKey="id" :datasource="tableData" :height="fullHeight -170" :isSelect="isSelect"  @selectData="selectData" :pagination="pagination" :selected.sync="selectedRowKeys">
                           <template  slot="name" slot-scope="{text, record}">  
                                <svg class="jwifont" aria-hidden="true">
                                    <use :xlink:href="record.type==='文件夹'?
                                        '#jwi-unfolder':record.type==='零件'?
                                        '#jwi-part':record.type==='成品'?
                                        '#jwi-end-product':record.type==='半成品'?
                                        '#jwi-unproduct':record.type==='文档'?
                                        '#jwi-document':'#jwi-unfolder'"></use>
                                </svg>
                                <span class="link-name" @click="getDetail(record)">{{ text }}</span>
                            </template>

                            <template slot="operation" slot-scope="text, record, $index">
                                  <span class="view-text" @click="deleteData($index)"><img src="../../../../assets/image/delete-icon.png" class="delete-icon"></span>
                            </template>
                   </JwTable>
              </div>

              
               <dialogAddAml :visible="visible" @closed="closed" :title="title"></dialogAddAml>
                
        </div>
    </div>
</template>

<script>
import newAml from './newAml'
import dialogAddAml from './dialog-add-aml'
import JwTable from "jw_components/table"
export default {
    name: 'bom-avl-aml',
    props: ['fullHeight'],
    components:{
        newAml,
        dialogAddAml,
        JwTable
    },
    data(){
        return {
          title:'新建AML',
          isSelect:true,
          pagination:true,
          visible:false,
          columns: [
              {
                title: '名称',
                dataIndex: 'name',
                key: 'name',
                sorter: true,
                scopedSlots: { customRender: 'name' },
                },
                {
                title: '编码',
                dataIndex: 'code',
                sorter: true,
                key: 'code',
                },
                {
                title: '供应商',
                dataIndex: 'supplier',
                sorter: true,
                key: 'supplier',
                },
                 {
                title: '类型',
                dataIndex: 'modelType',
                sorter: true,
                key: 'modelType',
                },
                 {
                title: '版本',
                dataIndex: 'version',
                key: 'version',
                sorter: true,
                ellipsis: true,
                scopedSlots: { customRender: 'version' },
                },
                {
                title: '创建时间',
                dataIndex: 'creationTime',
                key: 'creationTime',
                sorter: true,
                ellipsis: true,
                },
                {
                title: '操作',
                dataIndex: 'operation',
                key: 'operation',
                sorter: true,
                align: 'center',
                scopedSlots: { customRender: 'operation' },
                ellipsis: true,
                },
             ],
          tableData: [
              {
                id: '1',
                name: '风扇001',
                code: 'WT00002341',
                supplier: '深圳开发商',
                modelType: 'Part',
                version:'A.3',
                creationTime:'2021-09-21 13:00',
                type:'文件夹'
              },
               {
                id: '2',
                name: '风扇001',
                code: 'WT00002341',
                supplier: '深圳开发商',
                modelType: 'Part',
                version:'A.3',
                creationTime:'2021-09-21 13:00',
                type:'零件'
              },
               {
                id: '3',
                name: '风扇001',
                code: 'WT00002341',
                supplier: '深圳开发商',
                modelType: 'Part',
                version:'A.3',
                creationTime:'2021-09-21 13:00',
                type:'成品'
              }

            ],
            selectedRowKeys:[],
            multipleSelection: []
        }
    },
    methods: {
        selectData(selectedRowKeys, selectedRows){
            this.selectedRowKeys = selectedRowKeys
        },
   
          closed(){
             this.visible = false
          },
          newAml(){
              this.visible = true
              this.title = '新建AML'
          },
          newAvl(){
              this.visible = true
              this.title = '新建AVL'
          },
          getDetail(record){
              console.log('record', record)
          },
      
        onSearch(val){
             console.log('val', val)
        },
        deleteBatchData(){
              this.tableData = this.tableData.filter(item=> {
                return !this.selectedRowKeys.find(row=> {
                    return item.id==row
                })
            })
        },
        deleteData(index){
            this.tableData.splice(index, 1)
        }
    }
}
</script>

<style scoped>
.delete-icon{
  width: 16px;
  height: 16px;
}
.table-wrap {
		margin: 0;
 }
.page-wrap {
    padding: 20px 0 20px 20px;
}
.delete{
    color: #F6445A;
    cursor: pointer;
}
.link-name {
        color: #255ed7;
        cursor: pointer;
    }
.jwifont {
		width: 16px;
		min-width: 16px;
		height: 16px;
		min-height: 16px;
		margin-right: 8px;
}
.classtable {
    margin-top: 16px;
}
.operation-btn span img {
    width: 16px;
    height: 16px;
}
.operation-btn span {
    border: 1px solid rgba(30, 32, 42, 0.15);
    padding: 0 8px;
    height: 32px;
    line-height: 32px;
    border-radius: 4px;
    display: block;
}
.operation-btn{
    height: 32px;
    line-height: 32px;
    margin-top: 14px;
    display: flex;
}
.search-btn{
    width: 216px;
}
.new-add {
    background: #255ED7;
    color: #fff;
    font-size: 14px;
    margin-right: 8px;
}
.search-info{
    height: 32px;
    line-height: 32px;
    display: flex;
    margin-top: 14px;
    align-items: center;
    justify-content: space-between;
    border-bottom: none;
    padding: 0;
}
.content-detail{
    padding: 5px 20px;
    margin-top: 0px;
}
</style>