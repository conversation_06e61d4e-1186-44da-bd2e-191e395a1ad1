<template>
    <div>
     <a-modal
      :title="title"
      :visible="visible"
      class="model"
      @ok="handleOk"
      @cancel="handleCancel"
    >
           <a-form-model
            ref="ruleForm"
            :model="form"
            :rules="rules"
            :layout="form.layout"
            :label-col="labelCol"
            labelAlign="left"
            :wrapper-col="wrapperCol"
             >
            <a-form-model-item ref="name" label="名称" prop="name">
                 <a-input v-model.trim="form.name" placeholder="请输入" />
            </a-form-model-item>

            <a-form-model-item ref="unit" label="单位" prop="unit">
                <a-select v-model.trim="form.unit" placeholder="请选择">
                        <a-select-option value="shanghai">Zone one </a-select-option>
                        <a-select-option value="beijing"> Zone two </a-select-option>
                </a-select>
            </a-form-model-item>

            <a-form-model-item ref="unit" label="供应商" prop="supplier">
                <a-select v-model.trim="form.supplier" placeholder="请选择">
                        <a-select-option value="shanghai">Zone one </a-select-option>
                        <a-select-option value="beijing"> Zone two </a-select-option>
                </a-select>
            </a-form-model-item>

          </a-form-model>
    </a-modal>
    </div>
</template>

<script>
export default {
    name:'dialog-add-aml',
    props:['visible','title'],
    data(){
        return {
            labelCol: { span: 3 },
            wrapperCol: { span: 24 },
            form: {
               layout:'horizontal',
               name: '',
               unit: '',
               supplier:''
            },
             rules: {

             }
        }
    },
    methods: {
         handleOk(){
            this.$emit('closed')
         },
         handleCancel(){
            this.$emit('closed')
         }
    }
}
</script>

<style scoped>
.model>>>.ant-modal-body{
    padding: 10px 20px;
}
.model>>>.ant-modal-footer{
   border-top: none;
}
</style>