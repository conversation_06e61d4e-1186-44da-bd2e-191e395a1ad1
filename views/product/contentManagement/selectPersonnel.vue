<template>
    <div>
        <div class="add-personnel">

             <a-popover v-model.trim="visible" title="选择人员" trigger="click">
                <div slot="content" class="select-content">
                      <a-input-search placeholder="请输入姓名、手机号邮箱" class="search-btn" @search="onSearch" />
                      <div class="select-personnel-list">
                          <ul>
                              <li>
                                  <div class="header-list">
                                      <div :style="'background:url('+headerIcon +') no-repeat left center;width: 16px;height: 16px;background-size:contain'"></div>
                                       <span>李四</span>
                                  </div>
                                  <div><a-checkbox @change="onChange"></a-checkbox></div>
                               </li>

                               <li>
                                  <div class="header-list">
                                      <div :style="'background:url('+headerIcon +') no-repeat left center;width: 16px;height: 16px;background-size:contain'"></div>
                                       <span>李四</span>
                                  </div>
                                  <div><a-checkbox @change="onChange"></a-checkbox></div>
                               </li>

                               <li>
                                  <div class="header-list">
                                      <div :style="'background:url('+headerIcon +') no-repeat left center;width: 16px;height: 16px;background-size:contain'"></div>
                                       <span>李四</span>
                                  </div>
                                  <div><a-checkbox @change="onChange"></a-checkbox></div>
                               </li>

                                <li>
                                  <div class="header-list">
                                      <div :style="'background:url('+headerIcon +') no-repeat left center;width: 16px;height: 16px;background-size:contain'"></div>
                                       <span>李四</span>
                                  </div>
                                  <div><a-checkbox @change="onChange"></a-checkbox></div>
                               </li>

                               <li>
                                  <div class="header-list">
                                      <div :style="'background:url('+headerIcon +') no-repeat left center;width: 16px;height: 16px;background-size:contain'"></div>
                                       <span>李四</span>
                                  </div>
                                  <div><a-checkbox @change="onChange"></a-checkbox></div>
                               </li>

                               <li>
                                  <div class="header-list">
                                      <div :style="'background:url('+headerIcon +') no-repeat left center;width: 16px;height: 16px;background-size:contain'"></div>
                                       <span>李四</span>
                                  </div>
                                  <div><a-checkbox @change="onChange"></a-checkbox></div>
                               </li>

                                 <li>
                                  <div class="header-list">
                                      <div :style="'background:url('+headerIcon +') no-repeat left center;width: 16px;height: 16px;background-size:contain'"></div>
                                       <span>李四</span>
                                  </div>
                                  <div><a-checkbox @change="onChange"></a-checkbox></div>
                               </li>
                          </ul>
                      </div>
                 </div>
                <span>+</span>
            </a-popover>
           
            <ul>
                <li><div :style="'background:url('+headerIcon +') no-repeat left center;width: 32px;height: 32px;background-size:contain'"></div></li>
            </ul>
        </div>
    </div>
</template>

<script>
export default {
    name: 'selectPersonel',
    data(){
        return {
           headerIcon: require('../../../assets/image/header-icon.png'),
           visible: false,
        }
    },
    methods: {
         handleChange(info) {
           console.log('info', info)
         },
         onChange(){

         },
         onSearch(val){
             console.log('val',val)
         }
    }
}
</script>


<style scoped>
.header-list{
    display: flex;
    align-items: center;
}
.select-personnel-list{
    margin-top: 8px;
}
.select-personnel-list ul {
    margin: 0;
    padding: 0 10px 0 0;
    overflow-y: auto;
    height: 280px;

}
.select-personnel-list ul li span {
    font-size: 14px;
    color: rgba(30, 32, 42, 0.85);
    margin-left: 8px;
}
.select-personnel-list ul li {
    height: 40px;
    line-height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.search-btn{
    width: 216px;
}
.select-content{
    padding: 4px 0px;
}
.add-personnel ul li {
    list-style-type: none;
    width: 32px;
    height: 34px;
    border: 1px solid #A4C9FC;
    margin-right: 8px;
    border-radius: 50%;
}
.add-personnel ul {
    overflow: hidden;
    margin: 0;
}
.workflow-table>>>.ant-form-item{
    margin-bottom: 0;
}
.add-personnel span {
    width: 32px;
    height: 32px;
    border: 1px solid #A4C9FC;
    background: #F0F7FF;
    border-radius: 50%;
    font-weight: bold;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #255ED7;
    margin-right: 16px;
}
 .add-personnel{
     display: flex;
 }
 .add-upload{
     font-size: 20px;
     display: flex;
     align-items: center;
     justify-content: center;
     height: 32px;
     line-height: 32px;
 }
</style>