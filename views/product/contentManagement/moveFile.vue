<template>
  <div>
    <a-modal
      :title="title"
      class="model"
      :visible="visible"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <div class="tree-content">
        <a-tree
          checkable
          :tree-data="treeData"
          @select="onSelect"
          @check="onCheck"
        >
        </a-tree>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { search, searchProduct } from "apis/product/contentManage";
export default {
  name: "moveFile",
  props: ["visible"],
  data() {
    return {
      title: "选择文件路径",
      treeData: [
        {
          title: "parent 1",
          key: "0-0",
          children: [
            {
              title: "parent 1-0",
              key: "0-0-0",
              children: [
                { title: "leaf", key: "0-0-0-0" },
                { title: "leaf", key: "0-0-0-1" },
              ],
            },
            {
              title: "parent 1-1",
              key: "0-0-1",
              children: [
                {
                  key: "0-0-1-0",
                  title: "leaf001",
                  slots: { title: "title0010" },
                },
              ],
            },
          ],
        },
      ],
    };
  },
  methods: {
    handleCancel() {
      this.$emit("closed");
    },
    onSelect(val) {
        console.log('val', val)
    },
    onCheck(val){
       console.log('val', val)
    },
    handleOk() {
      this.$emit("closed");
    },
    fetch() {
      let searchReq = this.notProduct() ? search : searchProduct;

      this.loading = true;
      searchReq
        .execute({ size: 100 })
        .then((data) => {
          this.loading = false;
          this.list = data.result.rows;
        })
        .catch(() => {
          this.loading = false;
          this.$alert(lang["loadingFailAgain"], "Error").then(() => {
            this.fetch({});
          });
        });
    },

    hide() {
      this.dialogFormVisible = false;
    },

    show(title) {
      this.title = title;
      this.dialogFormVisible = true;
      this.fetch();
      return new Promise((resolve, reject) => {
        this.onClose = () => {
          this.hide();
          reject("cancel");
        };

        this.onSave = () => {
          let node = this.$refs.tree.getCurrentNode();
          let newArr = this.$refs.tree.getCheckedNodes();
          console.log("this.$refs.tree.getNode", newArr);
          if (!node) {
            this.$error(
               "请选择目标文件夹"
            );
            return;
          } else {
            if (this.oldMasterOid === node.oid) {
              this.$error(
                
                  "无法在当前目录移动"
              );
            } else {
              this.hide();
              this.$emit("msg-from-child", node);
              resolve(node);
            }
          }
        };
      });
    },

    notProduct() {
      let path = this.$route.path;
      return path.match("product") ? false : true;
    },

    loadNode(node, resolve) {
      if (node.level === 0) {
        return resolve(this.list);
      }
      if (node.level > 1) {
        this.searchFolder(node, resolve);
      }

      _.delay(() => {
        this.searchFolder(node, resolve);
      }, 500);
    },

    searchFolder(node, resolve) {
      let data = this.setData(node);
      let searchRequest = this.setRequest();
      searchRequest
        .execute(data)
        .then((data) => {
          let result = data.rows ? data.rows : data;
          let list = _.filter(result, (n) => {
            return n.modelType === "Folder" && n.oid !== this.oid;
          });
          resolve(list);
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.$alert(lang["loadingFailAgain"], "Error").then(() => {
            this.fetch({});
          });
        });
    },

    setData(node) {
      let relationName;
      if (node.data.modelType === "Product") {
        relationName = "ProductFolderLink";
      }
      if (node.data.modelType === "Drive") {
        relationName = "DriveFolderLink";
      }
      if (node.data.modelType === "Folder") {
        relationName = "FolderFolderLink";
      }
      let result = {
        modelType: node.data.modelType,
        oid: node.data.oid,
        page: 1,
        searchKey: "",
        relationName,
        size: 100,
      };
      return result;
    },
  },
};
</script>
<style scoped>
.model >>> .ant-modal-body {
  padding: 10px 20px;
}
.tree-content {
  height: 350px;
  overflow-y: auto;
}
</style>