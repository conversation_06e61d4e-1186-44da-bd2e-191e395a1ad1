<template>		
  <div class="h-full">
    <div class="workflow-title">
      <svg class="jwifont bind-icon-back" aria-hidden="true" @click="goback">
        <use xlink:href="#jwi-back"></use>
      </svg>
      <span>启动工作流</span>
    </div>

    <div class="workflow-main">
      <div class="step-info">
        <a-steps :current="currentVal">
          <a-step>
            <template slot="title">选择对象</template>
          </a-step>
          <a-step title="选择工作流" />
          <a-step title="启动" />
        </a-steps>
      </div>

      <objectTable
        v-if="currentVal == 0"
        :objectDataArr="objectDataArr"
        :fullHeight="fullHeight"
        @nextStep="nextStep"
        @cancer="cancer"
      ></objectTable>
      <workTable
        v-if="currentVal == 1"
        :fullHeight="fullHeight"
        @Step="Step"
        @onGetModelKey="onGetModelKey"
      ></workTable>
      <workStart
        v-if="currentVal == 2"
        :fullHeight="fullHeight"
        :processDefinitionKey="processDefinitionKey"
        @prevBtn="prevBtn"
      ></workStart>
    </div>
  </div>
</template>

<script>
import workTable from "../workfTable";
import objectTable from "../objectTable";
import workStart from "../workStart";
import { jwToolbar, jwPage } from "jw_frame"

export default {
  name: "workflow",
  components: {
    workTable,
    objectTable,
    workStart,
    // jwToolbar,
		// jwPage,
  },
  inject:['setBreadcrumb','displayType'],
  data() {
    return {
      currentVal: 0,
      fullHeight: document.documentElement.clientHeight - 234,
      objectDataArr: [],
      processDefinitionKey:''
    };
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        this.fullHeight = document.documentElement.clientHeight - 234;
      })();
    };
    let breadcrumbData = [{ name: '任务管理', path: '' }];
    this.displayType('')
    this.setBreadcrumb(breadcrumbData);
    this.fetchWorkList();
  },
  methods: {
    cancer() {
      this.goback()
    },
    fetchWorkList() {
      const data = sessionStorage.getItem('workflowData') || '{}';
      this.objectDataArr = JSON.parse(data)
    },
    
    goback() {
      this.$router.back();
    },
    prevBtn(stepval) {
      this.currentVal = stepval;
    },
    nextStep(stepval) {
      this.currentVal = stepval;
    },
    Step(stepval) {
      this.currentVal = stepval;
    },

    onGetModelKey(val){
      this.processDefinitionKey = val[0].key
    }
    
  },
};
</script>

<style scoped lang="less">
.workflow-main {
  background: var(--light);
  // box-shadow: 0 2px 8px 0 rgb(30 32 42 / 25%);
  border-radius: 4px;
  padding: 0;
  margin-top: 10px;
  .step-info {
    width: 680px;
    height: 80px;
    display: flex;
    align-items: center;
    margin: 0 auto;
  }
}
.workflow-title {
  height: 22px;
  line-height: 22px;
  display: flex;
  align-items: center;
  span {
    font-size: 14px;
    color: #1e202a;
    font-weight: 600;
  }
}
.jwifont {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  cursor: pointer;
}
</style>