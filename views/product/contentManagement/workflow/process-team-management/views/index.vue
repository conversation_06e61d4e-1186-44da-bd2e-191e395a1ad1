<template>
  <div class="all-background">
    <jw-table ref="ref_table" disableCheck="disableCheck" :data-source.sync="tableData" :columns="getHeader" :selectedRows.sync="selectedRows" :fetch="fetchTable" :toolbars="toolbars" @onToolClick="onToolClick" @onToolInput="onToolInput" @checkbox-change="onSelectChange" @onOperateClick="onOperateClick">
      <template #containerModelType="{ row }">
        {{ row.containerModelType == "Site" ? $t("txe_site") : $t("txe_tenant") }}
      </template>
      <template slot="tool-after-start">
        <a-button v-show="selectedRows.length > 0" style="background: #ffffff; color: #f81d22; text-shadow: none" type="danger" @click="onToolClick({ key: 'delete' })">
          {{ $t("btn_batch_delete") }}
        </a-button>
      </template>
      <template #updateBy="{ row }">
        <userInfo :accounts="[row.updateBy]" :showname="true" />
      </template>
    </jw-table>
    <!-- 新建团队 -->
    <formModal ref="ref_form_modal"></formModal>

  </div>
</template>

<script>
//接口

import formModal from "./form-dialog";
import userInfo from "components/user-info";

import { jwModalForm, jwAvatar, jwIcon } from "jw_frame";

import { getCookie } from "jw_utils/cookie";
import rules from "jw_frame/utils/rules.js";

import ModelFactory from "jw_apis/model-factory";
const findTeamApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/pdmFindProcessTeamPage`,
  method: "post"
});

export default {
  components: {
    jwModalForm,
    jwAvatar,
    jwIcon,
    formModal,
    userInfo
  },
  inject: ["setBreadcrumb", "addBreadcrumb"],
  data() {
    return {
      activeTab: "1",
      businessType: 1, //1为站点管理，2为组织管理
      drawerStatus: "",

      rules,
      // 角色列表信息
      ischeckBox: true,
      isRoleVisible: false,
      roleTableData: [],
      searchKey: "",
      tableData: [],
      selectedRows: [],

      tableLoading: false,

      currentRow: {},
      // 查询当前团队下的角色
      currentRowData: {},
      currentRowRole: [],
      //当前默认选中user
      currentCheckUser: []
    };
  },
  computed: {
    getHeader() {
      return [
        {
          field: "name",
          title: this.$t("btn_team_name"),
          sortable: true // 开启排序
        },
        {
          field: "privateFlag",
          title: this.$t("是否私有"),
          sortable: true, // 开启排序
          width: "140",
          formatter: ({ text, row }) => (row.privateFlag ? "是" : "否")
        },
        {
          field: "description",
          title: this.$t("txt_description"),
          sortable: true // 开启排序
        },
        // {
        //   field: "createBy",
        //   title: this.$t("创建人"),
        //   width: "160",
        //   sortable: true // 开启排序
        // },
        // {
        //   field: "createDate",
        //   title: "创建时间",
        //   width: "160",
        //   formatter: "date"
        // },
        {
          field: "updateBy",
          title: this.$t("修改人"),
          width: "160",
          sortable: true, // 开启排序
          slots: {
            default: 'updateBy',
          }
        },
        {
          field: "updateDate",
          title: "修改时间",
          width: "160",
          formatter: "date"
        },
        {
          // 操作列定义
          field: "operation", //关键字
          title: this.$t("txt_operation"),
          width: "100",
          btns: [
            {
              icon: "jwi-iconedit",
              title: this.$t("btn_edit"),
              key: "edit"
            },
            {
              icon: "jwi-iconsave-as",
              title: this.$t("btn_copy_the_team"),
              key: "saveAs"
            },
            {
              icon: "jwi-icondelete",
              title: this.$t("btn_delete"),
              key: "delete"
            }
          ]
        }
      ];
    },
    toolbars() {
      return [
        {
          name: this.$t("btn_new_create"),
          position: "before",
          type: "primary",
          key: "create"
          // prefixIcon: "jwi-plus",
        },
        {
          name: this.$t("btn_search"),
          position: "before",
          display: "input",
          value: this.searchKey,
          placeholder: this.$t("search_text"),
          key: "search"
        }
      ];
    }
  },
  created() {
    // 输入回调去抖动
    this.businessType = this.$route.query.type || 0;
    this.delaySearch = _.debounce(this.onSearch, 500);
    this.initBreadcrumb();
  },

  methods: {
    // 数据请求函数
    fetchTable({ searchKey, current, pageSize }) {
      let param = {
        searchKey,
        index: current,
        size: pageSize
        // containerOid: getCookie("tenantOid"),
        // containerType: "Tenant",
        // modelDefinition: "PdmTeamTemplate"
      };
      this.tableLoading = true;
      return findTeamApi
        .execute(param)
        .then(res => {
          this.tableLoading = false;
          return {
            data: res.rows,
            total: res.count
          };
        })
        .catch(err => {
          this.tableLoading = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    // 团队启用
    switchClick(row) {
      let param = {
        oid: row.oid,
        disabled: row.disabled,
        containerOid: getCookie("tenantOid"),
        containerModelType: "Tenant"
      };
      ModelFactory.create({
        url: `${Jw.gateway}/${Jw.containerCatalogService}/team/setDisabled`,
        method: "post"
      })
        .execute(param)
        .then(data => {
          this.$success(this.$t("msg_save_success"));
          //刷新列表
          this.$refs.ref_table.reFetchData();
        })
        .catch(err => {
          //刷新列表

          this.$error(err.msg || this.$t("msg_failed"));
        });
    },

    // 选择列回调
    onSelectChange(args) {
      this.selectedRows = [...args];
    },

    // 操作列回调
    onOperateClick(key, row) {
      this.currentRow = { ...row };
      if (key === "edit") {
        this.$router.push({
          path: "site-team-management-view",
          query: {
            drawerStatus: key,
            type: "team",
            team: JSON.stringify(this.currentRow)
          }
        });
      } else if (key === "detail") {
        this.showDrawer();
        this.activeTab = "1";
        this.drawerStatus = "detail";
        this.getCurrentRowRole(row);
      } else if (key === "addRole") {
        this.showDrawer();
        this.activeTab = "2";
        this.drawerStatus = "detail";
        this.getCurrentRowRole(row);
      } else if (key === "saveAs") {
        this.$refs.ref_form_modal
          .show({ title: "复制", row: _.clone(row) })
          .then(res => {
            this.submitSaveAs(res);
          });
      } else if (key === "delete") {
        this.onDelete(row);
      }
    },
    // 工具栏点击回调
    onToolClick({ key }) {
      if (key === "create") {
        this.$refs.ref_form_modal.show().then(res => {
          this.$refs.ref_table.reFetchData();
        });
      }
    },
    // 工具栏输入回调
    onToolInput({ key }, value) {
      if (key === "search") {
        this.searchKey = value;
        this.delaySearch();
      }
    },
    // 删除团队
    onDelete(row) {
      this.fetchDelete([row]);
    },

    initBreadcrumb() {
      let breadcrumbData = [
        {
          name: this.$t("page_team_management"),
          path: "/site-team-management"
        }
      ];
      this.setBreadcrumb(breadcrumbData);
    },
    // 删除团队
    fetchDelete(row) {
      let paramData = row.map(item => item.oid);
      let param = {
        teamTemplateOid: paramData[0]
      };
      this.$confirm({
        width: "280px",
        class: "deleteModal",
        closable: true,
        mask: false,
        title: (
          <p style="font-size:16px;font-weight:500;color:rgba(30, 32, 42, 0.85);">
            {this.$t("team_delete")}
          </p>
        ),
        content: (
          <p style="font-size:14px;font-weight:400;color:rgba(30, 32, 42, 0.45);">
            {this.$t("msg_confirm_delete")}
          </p>
        ),
        cancelText: this.$t("btn_cancel"),
        okText: this.$t("btn_confirm"),
        onOk: () => {
          ModelFactory.create({
            url: `${Jw.gateway}/${
              Jw.accountMicroServer
            }/team/template/delete?teamTemplateOid=${paramData[0]}`,
            method: "post"
          })
            .execute(param)
            .then(data => {
              this.$success(this.$t("txt_delete_success"));

              this.$refs.ref_table.reFetchData();
              this.selectedRows = [];
            })
            .catch(err => {
              this.$error(err.msg || this.$t("msg_failed"));
            });
        }
      });
    },
    // 输入回调刷新表格数据
    onSearch() {
      this.$refs.ref_table.reFetchData(this.searchKey);
    },
    // 团队另存为
    submitSaveAs(data) {
      let { currentRow } = this;
      let tenantOid = getCookie("tenantOid");
      let param = {
        ...data,
        oldTeamTemplateOid: currentRow.oid,
        teamTemplateName: data.name,
        containerOid: getCookie("tenantOid"),
        containerModelType: "Tenant"
      };
      return ModelFactory.create({
        url: `${Jw.gateway}/${
          Jw.accountMicroServer
        }/team/template/saveas?oldTeamTemplateOid=${
          currentRow.oid
        }&teamTemplateName=${data.name}`,
        method: "post"
      })
        .execute(param)
        .then(data => {
          this.$success(this.$t("msg_save_success"));

          this.$refs.ref_table.reFetchData();
        })
        .catch(err => {
          this.tableLoading = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    }
  }
};
</script>

<style lang="less" scoped>
.bindGroupArea {
  display: flex;
  justify-content: end;
  .bindroleBtn {
    margin-bottom: 10px;
  }
}

.openBtn {
  float: right;
  margin: 10px 20px 0 0;
}
</style>
<style>
.deleteModal .ant-modal-body {
  padding: 24px;
}

.deleteModal .ant-modal {
  /* top: 112px;
  left: 42%; */
}

.deleteModal .ant-modal-close-x {
  line-height: 69px;
}

.deleteModal
  .ant-modal-confirm-body
  > .anticon
  + .ant-modal-confirm-title
  + .ant-modal-confirm-content {
  margin-left: 0;
}

.deleteModal .ant-modal-confirm-btns .ant-btn {
  width: 75px;
  float: right;
}

.deleteModal .ant-modal-confirm-btns .ant-btn.ant-btn-primary {
  margin-right: 8px;
  background-color: rgba(37, 94, 215, 1);
  /* background-color: #1890ff; */
  border-color: rgba(37, 94, 215, 1);
}

/* 抽屉相关样式重置 */
</style>
