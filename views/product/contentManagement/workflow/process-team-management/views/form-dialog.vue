<template>
  <div>
    <a-modal v-dialogDrag :title="title" :visible="visible" :confirm-loading="confirmLoading" width='50%' @cancel="handleCancel">
      <a-form-model ref="ref_model_form" :model="formData" :rules="rules" class="sub-model-form" :label-position="'right'" label-width="100px">
        <a-form-model-item label="名称" prop="name">
          <a-input v-model="formData.name" placeholder="请输入" allowClear></a-input>
        </a-form-model-item>
        <a-form-model-item v-if="title=='新增'" label="是否私有" prop="privateFlag">

          <a-radio-group v-model="formData.privateFlag">
            <a-radio :value="true">
              是
            </a-radio>
            <a-radio :value="false">
              否
            </a-radio>
          </a-radio-group>

        </a-form-model-item>
      </a-form-model>

      <div slot="footer">
        <a-button type="primary" @click="handleOk">{{$t('btn_save')}}</a-button>
        <a-button @click="handleCancel">{{$t('btn_cancel')}}</a-button>
      </div>
    </a-modal>

  </div>
</template>
<script>
import { getCookie } from "jw_utils/cookie";

import ModelFactory from "jw_apis/model-factory";

// 新增团队
const createTeamApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.accountMicroServer}/team/template/create`,
  method: "post"
});
export default {
  components: {},
  props: {
    delayHide: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,

      confirmLoading: false,
      title: "新增",
      rules: {
        name: { required: true, message: "请选择模型", trigger: "change" },
        privateFlag: {
          required: true,
          message: "请选择模型",
          trigger: "change"
        }
      },
  
      formData: {
        privateFlag:false
      }
    };
  },
  methods: {
    show(options = {}) {
      let { title ,row} = options;
      if (title) {
        this.title = title;
      }
      if (row) {
        this.formData = row;
      }

      this.visible = true;
      _.delay(() => {
        // this.$refs.ref_base_form.clearValidate();
      });
      return new Promise((resolve, reject) => {
        this.handleCancel = () => {
          this.hide();
          reject("cancel");
        };

        this.handleOk = () => {
          let formElement = this.$refs.ref_model_form;

          formElement
            .validate()
            .then(async () => {
              this.confirmLoading = true;
              if (!this.formData.oid) {
                await this.handlerFun();
              }

              this.hide();
              resolve(_.clone(this.formData));
            })
            .catch(err => {});
        };
      });
    },

    handlerFun() {
      let params = {
        containerOid: getCookie("tenantOid"),
        containerModelType: "Tenant",
        modelDefinition: "PdmTeamTemplate",
        ...this.formData
      };
      return createTeamApi.execute(params).catch(err => {
        throw new Error("操作失败...");
      });
    },

    hide() {
      this.visible = false;
      _.delay(() => {
        this.$refs.ref_model_form.resetFields();
      }, 500);
    },
    handleCancel: _.noop,
    handleOk: _.noop
  }
};
</script>