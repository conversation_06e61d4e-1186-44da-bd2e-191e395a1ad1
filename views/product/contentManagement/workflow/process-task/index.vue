<!--
* @Author:<EMAIL>
* @Date: 2022/04/28 17:43:18
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/04/28 17:43:18
* @Description: 
-->
<template>
  <div class="process-task">
    <header>
      <div class="header-left">
        <a-breadcrumb separator=">">
          <a-breadcrumb-item>
            <router-link to="/wait-tasks">
              {{ $t("txt_my_task") }}
            </router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>{{ data.name }}</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </header>
    <a-spin :spinning="mainLoading" :tip="$t('txt_data_loading')">
      <component v-if="!_.isEmpty(data)" :is="renderForm" />
    </a-spin>
  </div>
</template>
<script>
import { EventBus, getTaskDetails } from "../units/api"
import { getCookie } from "jw_utils/cookie"
export default {
  name: "ProcessTask",
  inject: ["setBreadcrumb"],
  data() {
    return {
      mainLoading: false,
      component: null,
      data: {},
    }
  },
  computed: {
    renderForm() {
      if (this?.data?.taskForms?.length && this.data.taskForms[0].code) {
        return async () => import(`../form-task/${this.data.taskForms[0].code}`)
      } else {
        return async () => import(`../form-task/default-form`)
      }
    },
  },
  created() {
    let breadcrumbData = []
    this.setBreadcrumb(breadcrumbData)
    this.init()
  },
  mounted() {},
  watch: {
    $route(to, from) {
      this.init()
    },
  },
  destroyed() {
    //eventBus销毁
    //暂时取消销毁  因导致浏览器退回数据丢失
    // EventBus.$off("FlowChartHistoryInit");
    // EventBus.data = undefined;
  },
  methods: {
    init() {
      this.mainLoading = true
      let params = {
        tenantId: getCookie("tenantOid"),
        taskId: this.$route.query.taskId,
      }
      getTaskDetails(params)
        .then((res) => {
          res.processDefinitionType = res.processDefinitionId.split(":")[0]
          EventBus.data = this.data = res
        })
        .catch((err) => {
          this.$error(err.msg || err.message)
        })
        .finally(() => {
          this.mainLoading = false
          EventBus.$emit("FlowChartInit", this.data)
        })
    },
  },
}
</script>

<style lang="less">
.process-task {
  background-color: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  > header {
    display: flex;
    justify-content: space-between;
    height: 60px;
    // background-color: #fff;
    align-items: center;
    padding: 0 110px 0 24px;
    > .header-left {
      font-size: 20px;
      display: flex;
      a {
        color: #40a9ff;
      }
    }
  }
  > .ant-spin,
  .ant-spin-nested-loading {
    height: 20px;
    flex-grow: 1;
    .ant-spin-container {
      height: 100%;
    }
  }
}
</style>
