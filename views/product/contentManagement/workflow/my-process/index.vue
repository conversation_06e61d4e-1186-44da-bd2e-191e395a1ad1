<template>
    <div class="my-process-wrap">
        <workflow-page
            :pageCode="'myProcess'"
            :listApi="listApi"    
        ></workflow-page>
    </div>
</template>

<script>
import workflowPage from '/views/product-content/process-manage/workflow-page';
export default {
    name: 'myProcess',
    inject: ['setBreadcrumb'],
    data() {
        return {
            listApi: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/fuzzyPage/byCreator`,
		}
    },
    created() {
        this.setBreadcrumb([{name:this.$t('txt_my_work')}]);
        window.localStorage.setItem("index_", 1)
    },
    components: {
        workflowPage,
    },
    methods: {
        
    },
};
</script>

<style lang="less">
.my-process-wrap {
    height: 100%;
}
</style>
