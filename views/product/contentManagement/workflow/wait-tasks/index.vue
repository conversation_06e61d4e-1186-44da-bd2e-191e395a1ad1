<!--
* @Author:<EMAIL>
* @Date: 2022/04/27 18:19:51
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/04/27 18:19:51
* @Description: 
-->
<template>
  <div class="wait-tasks">
    <a-tabs
      :default-active-key="activeKey"
      v-model.trim="activeKey"
      @change="tabChange"
    >
      <a-tab-pane
        key="wait"
        :tab="$t('txt_to_do_tasks')"
      >
        <jw-table
          ref="refTable"
          :panel="true"
          :tooltip-config="{}"
          :loading="wait.loading"
          :pagerConfig="wait.pagerConfig"
          @onPageChange="onPageChange"
          @onSizeChange="onSizeChange"
          :columns="wait.columns"
          :data-source.sync="wait.data"
        >
          <template slot="toolbar">
            <!-- 处理空数据搜索不回调 -->
            <a-input-search
              :placeholder="$t('search_text')"
              style="width: 220px"
              v-model.trim="wait.nameLike"
              :loading="wait.searchLoading"
              @search="onWaitSearch"
            />
          </template>
          <template #name="{ row }">
            <a-button
              type="link"
              @click="routerLink(row)"
            >{{row.name}}</a-button>
          </template>
        </jw-table>
      </a-tab-pane>
      <a-tab-pane
        key="complete"
        :tab="$t('txt_completed_task')"
        force-render
      >
        <jw-table
          ref="refTable"
          :panel="true"
          :tooltip-config="{}"
          :loading="complete.loading"
          :pagerConfig="complete.pagerConfig"
          @onPageChange="onPageChange"
          @onSizeChange="onSizeChange"
          :columns="complete.columns"
          :data-source.sync="complete.data"
        >
          <template slot="toolbar">
            <!-- 处理空数据搜索不回调 -->
            <a-input-search
              :placeholder="$t('search_text')"
              style="width: 220px"
              v-model.trim="complete.nameLike"
              :loading="complete.searchLoading"
              @search="onCompleteSearch"
            />
          </template>
          <template #name="{ row }">
            <a-button
              type="link"
              @click="routerLink(row)"
            >{{row.name}}</a-button>
          </template>
        </jw-table>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script>
import { getWaitData, getCompleteData } from "../units/api";
import { getCookie } from "jw_utils/cookie";

export default {
  name: "WaitTasks",
  inject: ["setBreadcrumb"],
  data() {
    return {
      activeKey: "wait",
      top1: "txt_contact",
      wait: {
        loading: false,

        searchLoading: false,
        nameLike: undefined,
        attttt: this.$t("txt_name"),
        bttt: this.attttt,
        pagerConfig: {
          current: 1,
          pageSize: 20,
          total: 0,
        },
        data: [],
        columns: [
          {
            field: "name",
            title: this.$t("task_name"),
            width: 150,
            slots: {
              default: "name",
            },
          },
          {
            field: "processInstanceName",
            title: this.$t("txt_to_process"),
            minWidth: 210,
          },
          {
            field: "description",
            title: this.$t("txt_operation_guide"),
            minWidth: 210,
          },
          {
            field: "createTime",
            title: this.$t("txt_create_date"),
            width: 180,
            formatter: ({ cellValue, column, columnIndex, row, rowIndex }) => {
              if (cellValue) {
                return cellValue.split(".")[0].split("T").join(" ");
              }
              return "";
            },
          },
          {
            field: "dueDate",
            title: this.$t("txt_stop_time"),
            width: 180,
            formatter: ({ cellValue, column, columnIndex, row, rowIndex }) => {
              if (cellValue) {
                return cellValue.split(".")[0].split("T").join(" ");
              }
              return "";
            },
          },

          // {
          //   field: "delegationState",
          //   title: "委派状态",
          //   minWidth: 210,
          //   formatter: "date",
          // },
          // {
          //   title: "操作",
          //   dataIndex: "operation",
          //   align: "center",
          //   key: "operation",
          //   slots: { default: "operation" },
          // },
        ],
      },
      complete: {
        loading: false,
        searchLoading: false,
        nameLike: undefined,
        pagerConfig: {
          current: 1,
          pageSize: 20,
          total: 0,
        },
        data: [],
        columns: [
          {
            field: "name",
            title: this.$t("task_name"),
            minWidth: 210,
            slots: {
              default: "name",
            },
          },
          {
            field: "processInstanceName",
            title: this.$t("txt_to_process"),
            minWidth: 210,
          },
          {
            field: "description",
            title: this.$t("txt_operation_guide"),
            minWidth: 210,
          },
          {
            field: "startTime",
            title: this.$t("txt_create_date"),
            minWidth: 240,
            formatter: ({ cellValue, column, row, rowIndex }) => {
              if (
                cellValue &&
                cellValue.split(".") &&
                cellValue.split(".").length &&
                cellValue.split(".")[0].split("T") &&
                cellValue.split(".")[0].split("T").length
              )
                return cellValue.split(".")[0].split("T").join(" ");
              else return "";
            },
          },
          {
            field: "endTime",
            title: this.$t("txt_done_time"),
            minWidth: 170,
            // formatter: "date",
            formatter: ({ cellValue, column, row, rowIndex }) => {
              if (
                cellValue &&
                cellValue.split(".") &&
                cellValue.split(".").length &&
                cellValue.split(".")[0].split("T") &&
                cellValue.split(".")[0].split("T").length
              )
                return cellValue.split(".")[0].split("T").join(" ");
              else return "";
            },
          },

          // {
          //   field: "delegationState",
          //   title: "委派状态",
          //   minWidth: 210,
          //   formatter: "date",
          // },
          // {
          //   title: "操作",
          //   dataIndex: "operation",
          //   align: "center",
          //   key: "operation",
          //   slots: { default: "operation" },
          // },
        ],
      },
    };
  },
  computed: {},
  components: {},
  created() {
    this.setBreadcrumb([{ name: this.$t("txt_my_task") }]);
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.activeKey = "wait";
      this.tabChange();
    },
    onWaitSearch() {
      this.wait.searchLoading = true;
      this.getWaitData();
    },
    onCompleteSearch() {
      this.complete.searchLoading = true;
      this.getCompleteData();
    },
    onSizeChange(pageSize, page) {
      if (this.activeKey === "wait") {
        this.wait.pagerConfig.current = page;
        this.wait.pagerConfig.pageSize = pageSize;
        this.getWaitData();
      } else {
        this.complete.pagerConfig.current = page;
        this.complete.pagerConfig.pageSize = pageSize;
        this.getCompleteData();
      }
    },
    onPageChange(page, pageSize) {
      if (this.activeKey === "wait") {
        this.wait.pagerConfig.current = page;
        this.wait.pagerConfig.pageSize = pageSize;
        this.getWaitData();
      } else {
        this.complete.pagerConfig.current = page;
        this.complete.pagerConfig.pageSize = pageSize;
        this.getCompleteData();
      }
    },
    tabChange(tab = "wait") {
      if (tab === "wait") {
        this.getWaitData();
      } else {
        this.getCompleteData();
      }
    },
    /**
     * 获取待办任务
     */
    getWaitData() {
      this.wait.loading = true;
      let params = {
        assignee: Jw.getUser().account,
        index: this.wait.pagerConfig.current,
        size: this.wait.pagerConfig.pageSize,
        nameLike: this.wait.nameLike,
      };
      getWaitData(params)
        .then((res) => {
          this.wait.data = res.rows;
          this.wait.pagerConfig.total = res.count;
        })
        .catch((err) => {
          this.$error(err.msg || err.message);
        })
        .finally(() => {
          this.wait.loading = false;
          this.wait.searchLoading = false;
        });
    },
    /**
     * 获取已完成任务
     */
    getCompleteData() {
      this.complete.loading = true;
      let params = {
        taskAssignee: Jw.getUser().account,
        index: this.complete.pagerConfig.current,
        size: this.complete.pagerConfig.pageSize,
        taskNameLike: this.complete.nameLike,
        finished: true,
        tenantId: getCookie("tenantOid"),
      };
      getCompleteData(params)
        .then((res) => {
          this.complete.data = res.rows;
          this.complete.pagerConfig.total = res.count;
        })
        .catch((err) => {
          this.$error(err.msg || err.message);
        })
        .finally(() => {
          this.complete.loading = false;
          this.complete.searchLoading = false;
        });
    },
    routerLink(row) {
      // if (text.row.assignee === Jw.getUser().account   ) {
      //   this.routerLinkWait(text, record);
      // } else {
      //   this.routerLinkComplete(text, record);
      // }
      this.$router.push({
        path: "/process-task",
        query: {
          taskId: row.id,
          processInstanceId: row.processInstanceId,
        },
      });
    },
    // routerLinkWait(text, record) {
    //   this.$router.push({
    //     path: "/process-task",
    //     query: {
    //       taskId: text.row.id,
    //       processInstanceId: text.row.processInstanceId,
    //     },
    //   });
    // },
    // routerLinkComplete(text, record) {
    //   this.$router.push({
    //     path: "/process-task-show",
    //     query: {
    //       taskId: text.row.id,
    //       processInstanceId: text.row.processInstanceId,
    //     },
    //   });
    // },
  },
};
</script>
<style lang="less">
.wait-tasks {
  background-color: #fff;
  height: 100%;
  > .ant-tabs {
    display: flex;
    flex-direction: column;
    height: 100%;
    .ant-tabs-nav .ant-tabs-tab {
      padding: 12px 0px;
    }
    .ant-tabs-nav-scroll {
      padding-left: 24px;
    }
    .ant-tabs-content {
      height: 20px;
      flex-grow: 1;
    }
    .vxe-grid--toolbar-wrapper {
      padding: 0  24px 16px !important;
    }
    .jw-table.is-panel .vxe-table {
      margin: 0 24px;
    }
    .vxe-grid--pager-wrapper {
      padding: 0 24px !important;
    }
  }
}
</style>