<!--
* @Author:<EMAIL>
* @Date: 2022/05/17 09:50:06
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/05/17 09:50:06
* @Description: 
-->
<template>
  <div class="detailed-task">
    <header>
      <div class="header-left">
        <a-breadcrumb separator=">">
          <a-breadcrumb-item>
            <a href="javascript:void(0)" @click="routerBack">
              {{ $route.query.backName || $t("txt_my_task") }}
            </a>
          </a-breadcrumb-item>
          <a-breadcrumb-item>{{ form.name }}</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </header>
    <main>
      <multipane class="custom-resizer" layout="vertical">
        <div class="pane">
          <a-form
            :form="form"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-form-item :label="$t('txt_base_info')">
              <jw-layout-builder
                ref="ref_appBuilder"
                v-if="form.modelDefinition"
                type="Model"
                layoutName="show"
                :modelName="form.modelDefinition"
                :instanceData="form"
              >
              </jw-layout-builder>
            </a-form-item>
            <a-form-item
              :label="
                $t('txt_inspection_object') +
                (form.bizObjects.length
                  ? ' (' + form.bizObjects.length + ')'
                  : '')
              "
              class="review-object"
            >
              <jw-table
                ref="refTable"
                :maxHeight="500"
                :panel="true"
                :tooltip-config="{}"
                :columns="columns"
                :showPage="false"
                :data-source.sync="form.bizObjects"
              >
                <template #numberSlot="{ row }">
                  <span
                    style="color: #255ed7; cursor: pointer"
                    @click="routerLink(row)"
                  >
                    <jw-icon :type="row.modelIcon" />
                    {{ row.number }}

                    <a-tag v-if="row.primary" color="blue" style="margin-left: 8px">
                      {{ $t('txt_object_main') }}
                    </a-tag>
                  </span>
                </template>
                <template #updateDate="{ row }">
                  <span style="color: #255ed7">
                    {{ row.updateDate | formatDateFn }}
                  </span>
                </template>
                <template #isLock="{ row }">
                  <a-tooltip>
                    <template slot="title">
                      {{ row.lockOwnerAccount }} {{ $t("txt_check_out") }}
                    </template>
                    <jw-icon
                      v-if="row.lockOwnerOid"
                      :type="
                        row.lockOwnerOid === jwUser.oid
                          ? '#jwi-beiwojianchu'
                          : '#jwi-bierenjianchu'
                      "
                    />
                  </a-tooltip>
                </template>
                <template #loadStatus="{ row }">
                  <jw-icon v-if="!row.loadStatus" type="#jwi-liuchengzhong" />
                </template>
                <template #versionSlot="{ row }">
                  <span>
                    {{ row.displayVersion }}
                  </span>
                </template>
                  <template #operation="{ row }">
                    <div>
                      <a-tooltip title="对比">
                        <span @click="gethistorydata(row)" style="cursor: pointer">
                          <jw-icon type="jwi-iconreturn"> </jw-icon>
                        </span>
                      </a-tooltip>
                    </div>
                </template>
              </jw-table>
            </a-form-item>
            <a-form-item :label="$t('txt_flow_team')" class="review-object">
              <jw-table
                :maxHeight="500"
                row-id="oid"
                ref="tree"
                :showPage="false"
                :columns="roleGetHeader"
                :tree-config="{ rowId: 'oid', children: 'users' }"
                :dataSource="form.teamRoleWithUsers"
              >
                <div name="empty" slot="empty"></div>
                <template #tree-content="{ row }">
                  <span v-if="row.root">
                    <jw-icon type="#jwi-wenjianga-youneiyong" />
                    {{ row.displayName || row.name }}
                  </span>
                  <span v-else>
                    <jw-avatar :showName="true" :tag="true" :data="row" />
                  </span>
                </template>
              </jw-table>
            </a-form-item>
          </a-form>
        </div>
        <multipane-resizer></multipane-resizer>
        <div class="pane">
          <FlowChart
            :processInstanceName="$route.query.processInstanceName"
            ref="flowChart"
            :showIcon="false"
            :processModelId="form.processModelId"
            :processOrderOid="$route.query.processOrderOid"
          ></FlowChart>
        </div>
      </multipane>
    </main>
    <create-problem
      ref="createProblemEl"
      :visible.sync="problemVisible"
      @close="problemVisible = false"
    ></create-problem>
  </div>
</template>
<script>
import { jwLayoutBuilder, jwAvatar, jwIcon } from "jw_frame";
import { findDetail } from "../units/api";
import { Multipane, MultipaneResizer } from "vue-multipane";
import { formatDate } from "jw_utils/moment-date";
import ElementDrag from "../components/element-drag.vue";
import FlowChart from "../components/flow-chart.vue";
import { getHistoryList, findVersionRule } from "apis/part";
import ModelFactory from 'jw_apis/model-factory'
import { getCookie } from 'jw_utils/cookie'
import createProblem from "/views/problem-manage/problem-list/create-problem.vue";
/**
 * 获取cadfile
 */
const getCadFiles = function (oid) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.cadService}/mcad/findCADFile?Oid=${oid}`,
    method: 'post',
  }).execute()
}

const findSecBom = function (oid) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.cadService}/mcad/useTree/findUseAllOrLevel`,
    method: 'get',
  }).execute({ rootOid: oid })
}
export default {
  name: "DetailedTask",
  props: {},
  components: {
    ElementDrag,
    FlowChart,
    Multipane,
    jwLayoutBuilder,
    MultipaneResizer,
    jwAvatar,
    jwIcon,
    createProblem
  },
  inject: ["setBreadcrumb"],
  computed: {
    processInstanceName() {
      return this.$route.query.processInstanceName;
    },
    processOrderOid() {
      return this.$route.query.processOrderOid;
    },
  },
  data() {
    return {
      problemVisible: false,
      hasBaforeRoute: false,
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      columns: [
        {
          field: "number",
          title: this.$t("txt_number_of"),
          sortable: true,
          width: 280,
          slots: {
            default: "numberSlot",
          },
        },
        {
          field: "name",
          title: this.$t("txt_name"),
          sortable: true,
          formatter: ({ text, row }) => {
            return row.cname || row.name
          }
        },
        {
          field: "modelDefinition",
          title: this.$t("txt_type"),
          sortable: true,
        },
        {
          field: "lifecycleStatus",
          title: this.$t("txt_plan_lifecycle"),
          sortable: true,
        },
        {
          field: "displayVersion",
          title: this.$t("txt_version"),
          sortable: true,
          slots: {
            default: "versionSlot",
          },
        },
        {
          field: "updateDate",
          title: this.$t("txt_update_date"),
          sortable: true, // 开启排序
          slots: {
            default: "updateDate",
          },
        },
        {
          // 操作列定义
          field: "operation", //关键字
          slots: {
            default: "operation",
          },
        },
      ],
      roleGetHeader: [
        {
          field: "displayName",
          title: this.$t("txt_name"),
          treeNode: true,
          slots: {
            default: "tree-content",
          },
        },
        {
          field: "description",
          title: this.$t("txt_description"),
        },
      ],
      currentRowRole: [],
      form: {
        name: undefined,
        modelDefinition: undefined,
        bizObjects: [],
        teamRoleWithUsers: [],
      },
    };
  },

  filters: {
    formatDateFn(date) {
      return formatDate(date);
    },
  },
  created() {
    let breadcrumbData = [];
    this.setBreadcrumb(breadcrumbData);
  },
  mounted() {
    this.init();
  },
  beforeRouteEnter(to, from, next) {
    console.log(from);
    if (
      from.name == "product-content" ||
      from.name == "my-process" ||
      from.name == "object-details"
    ) {
      next((vm) => {
        vm.hasBaforeRoute = false;
      });
    } else {
      next((vm) => {
        vm.hasBaforeRoute = true;
      });
    }
    next();
  },
  methods: {
    async opensolidwork(row) {
      if (row.masterType !== 'MCAD') {
        this.$warning('只可以打开MCAD类型')
        return
      }
      if (!SpaceSDK.setSpaceCache) {
        this.$warning('请在Space中使用此功能')
        return
      }
      if (row.modelDefinition === 'CADAssembly') {
        let boms = await findSecBom(row.oid)
        let allbom = this.listbomtotable(boms[0])
        let files = []
        for (let i = 0; i < allbom.length; i++) {
          const element = allbom[i];
           let file = await getCadFiles(element.oid)
          files = [...files, ...file]
        }
        if (files.length > 0) {
          this.openwindows(files)
        }
      } else {
        let files = await getCadFiles(row.oid)
        if (files.length > 0) {
          this.openwindows(files)
        }
      }
    },
     listbomtotable(data) {
      let res = []
      let loadchildren = function (val) {
        res.push(val)
        if (val && val.children && val.children.length !== 0) {
          val.children.forEach((item) => {
            item.preSourceOid = val.oid
            loadchildren(item)
          })
        }
      }
      loadchildren(data)
      return res
    },
    openwindows(filesall) {
      let files = filesall.filter(item => item.primary)
      console.log('打开solidwork', files)
      SpaceSDK.setSpaceCache({
        key: '3dx-plugin-environment',
        value: {
          appName: 'pdm',
          tenantOid: getCookie('tenantOid'),
        },
      })

      var result = files.map((item) => {
        return {
          fileOid: item.url,
          fileName: item.fileName,
          fileLastModified: item.lastModified,
          dirPath: [this.$route.query.backName],
        }
      })
      SpaceSDK.next('download-file', {
        fileInfoList: result,
        appName: 'CAD',
        hasMessage: false,
        openFileInfo: {
          isCADOpen: true, // 是否打开
          execPath: [this.$route.query.backName],
          isCadName: result[0].fileName,
        },
      })
    },
    contrastbom(source, target) {
      let url = this.$router.resolve({
        name: 'baseline-contrast',
        query: {
          containerOid: source.containerOid,
          objectType: 'object',
          sourceType: source.type,
          sourceOid: source.oid,
          sourceModelType: source.modelDefinition,
          targetOid: target.oid,
          targetType: target.type,
          targetModelType: target.modelDefinition,
        },
      })
      window.open(url.href, '_blank')
    },
    //查找历史版本
    gethistorydata(row){
      const { modelDefinition, oid, type, masterType, tenantOid } = row
      if(masterType !== 'Part'){
        this.$warning("只有Part类型可以查看版本对比")
        return
      }
      Promise.all([getHistoryList.execute({
        modelDefinition,
        oid,
        type,
      }), findVersionRule.execute({
        modelCode: modelDefinition,
        containerOid: tenantOid,
      })]).then((resp) => {
        let resp1 = resp[0]
        let resp2 = resp[1]
        let target = null
        if(resp2){
          let rules = resp2.rule.split(',')
          let ruleIndex = rules.findIndex(item => item === row.version)
          if(ruleIndex > 0){
            //找到上个版本的最新版
            let rule = rules[ruleIndex - 1]
            target = resp1.filter(item => item.version === rule).reduce((prev, curr) => prev.versionSortId > curr.versionSortId ? prev : curr)
          }else{
            //未找到上个最大版本使用当前版本比较
            target = resp1.filter(item => item.version === row.version).reduce((prev, curr) => prev.versionSortId < curr.versionSortId ? prev : curr)
          }
        }
        if(!target){
          target = resp1[resp1.length - 1]
        }

        if(target){
          this.contrastbom(row, target)
        }else{
          this.$warning("未找到之前的版本")
        }
      })
    },
    init() {
      let params = {
        oid: this.$route.query.processOrderOid,
      };
      findDetail(params)
        .then((res) => {
          this.form = res;
          this.form.teamRoleWithUsers=this.form.teamRoleWithUsers.map(ele=>{
            return {
              ...ele,
              users:ele.users||[],
              root:true
            }
          })
        })
        .catch((err) => {
          this.$error(err.msg || err.message);
        })
        .finally(() => {});
    },
    routerLink(row) {
      if (row.type === "Issue") {
        let issueUrl = this.$router.resolve({
          name: `problem-detail`,
          path: `/problem-detail`,
          query: {
            oid: row.oid,
          },
        });
        window.open(issueUrl.href, "_bank");
      } else {
        row.masterType = row.masterType || row.type;
        row.processDefinitionId=this.form.processDefinitionId
        Jw.jumpToDetail(row, { blank: true });
      }
    },
    routerBack() {
      if (this.hasBaforeRoute === false) {
        this.$router.back();
      } else {
        this.$router.push({ path: "/my-process" });
      }
    },
  },
};
</script>
<style lang="less">
.detailed-task {
  background-color: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  > header {
    display: flex;
    justify-content: space-between;
    height: 60px;
    // background-color: #fff;
    align-items: center;
    padding: 0 110px 0 24px;
    > .header-left {
      font-size: 20px;
      display: flex;
      a {
        color: #40a9ff;
      }
    }
  }
  .multipane-resizer {
    z-index: 1;
    margin: 0;
    left: 0;
    position: relative;
    background: #eee;
    &:before {
      display: block;
      content: "";
      width: 3px;
      height: 40px;
      position: absolute;
      top: 50%;
      left: 50%;
      margin-top: -20px;
      margin-left: -1.5px;
      border-left: 1px solid #ccc;
      border-right: 1px solid #ccc;
    }
    &:hover {
      &:before {
        border-color: #999;
      }
    }
  }
  .custom-resizer {
    height: 20px;
    flex-grow: 1;
    display: flex;
    width: 100%;
    > .pane {
      text-align: left;
      // padding: 15px;
      overflow: hidden;
    }
    > div {
      &:first-child {
        width: 75%;
        display: flex;
        flex-direction: column;
        padding-right: 8px;
        .review-object {
          .jw-table.vxe-grid.is--animat.is-panel {
            padding: 0;
          }
          .vxe-table.vxe-table--render-default.border--full.row--highlight.is--header.is--animat {
            margin: 0;
          }
        }
      }
      &:last-child {
        width: 20px;
        flex-grow: 1;
      }
    }
  }
  > main {
    display: flex;
    flex-direction: column;
    padding: 0 20px;
    height: 20px;
    flex-grow: 1;
    .header {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
    }
    .ant-form {
      overflow: auto;
      > .ant-form-item {
        > .ant-form-item-label {
          font-size: 18px;
          font-weight: bold;
          &:before {
            padding-right: 0px;
            content: "*";
            color: #fff;
            border-left: 4px solid #255ed7;
          }
        }
      }
    }
  }
}
</style>