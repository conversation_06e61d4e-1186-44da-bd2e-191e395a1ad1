
<template>
  <div class="review-object">
    <template v-for="(panel) in relatedObjList">
      <header :key="panel.oid">
        {{ panel.relationDisplayName }}
        <template v-if="isEdit">
          <span class="handle-btn" @click.stop="onAdd(panel)">{{$t('添加')}}</span>
          <span class="handle-btn" @click.stop="onConfirmDelete(panel)">{{$t('删除')}}</span>
        </template>
      </header>
      <jwTable :key="panel.oid" :panel="true" :maxHeight="500" :ref="`ref_${[panel.oid]}`" :columns="_columns" :dataSource="data[panel.oid]" :showPage="false" :selectedRows.sync="tableSelectedRows[panel.oid]" />
    </template>
    <jw-search-engine-modal :title="$t('txt_add_c')" only-search-object :visible.sync="visibleDialog" :model-list='typeList' @ok='onOK' />
  </div>
</template>

<script>
import { EventBus, getTaskDetails } from "../units/api";
import { jwTable, jwSearchEngineModal } from "jw_frame";
// import DialogTable from "components/add-object-modal";
import ModelFactory from "jw_apis/model-factory";

// 获取相关对象
const relationObjsModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.sysconfigServer}/collectionRule/findByAppliedType`,
  method: "get"
});

// 获取对象列表
const relationSearchModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/relatedObject/fuzzy`,
  method: "post"
});

// 添加对象
const batchAddModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/relatedObject/batchAdd`,
  method: "post"
});

// 删除对象
const batchDeleteModel = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.foundationServer
  }/instance/relatedObject/batchDelete`,
  method: "post"
});

//查看列表
const searchModelTable = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/fuzzyPage`,
  method: "post"
});

export default {
  props: {
    isEdit: { type: Boolean, default: false }
  },

  data() {
    this.selections = null;
    this._initData();
    this._currentObj = {};

    return {
      isBtns: false,
      isSelection: false,
      data: {},
      loadingMap: {},
      activeNames: [],
      tableSelectedRows: [],
      relatedObjList: [],
      visibleDialog: false,
      typeList: [],
      objectDetailsData: {}
    };
  },

  components: {
    jwTable,
    // DialogTable,
    jwSearchEngineModal
  },

  async created() {
    let d = EventBus.data || {};
    this.objectDetailsData = d.bizObjects[0];
    await this.initFetch();
    this.relatedObjList.forEach(item => {
      this.loadFetch(item.oid);
    });
  },

  methods: {
    searchModelTable(params) {
      return searchModelTable.execute(params);
    },
    initFetch() {
      let params = {
        appliedType: `Task_Related_Object`,
        mainObjectType: this.objectDetailsData.modelDefinition
      };
      return relationObjsModel
        .execute(params)
        .then(res => {
          if (this.isEdit) {
            res.forEach(item => {
              this.tableSelectedRows[item.oid] = [];
            });
          }

          this.relatedObjList = res;
        })
        .catch(err => {
          this.$error(this.$t("txt_get_object_list"));
        });
    },
    onExpandPanel(key) {
      if (!this.activeNames.includes(key)) {
        this.activeNames.push(key);
      }
    },
    onAdd(itemObj) {
      let code = itemObj.slaveObjectClassName;
      let name = itemObj.relationDisplayName;
      let value;

      this.onExpandPanel(itemObj.oid);
      this.visibleDialog = true;
      this._currentObj = itemObj;

      if (
        code == "MCADDocumentIteration" ||
        code == "MCADIteration" ||
        code == "ECADDocumentIteration" ||
        code == "ECADIteration"
      ) {
        value =
          code == "MCADDocumentIteration" || code == "MCADIteration"
            ? "MCADIteration"
            : "ECADIteration";
        // code = 'CAD';
      } else if (code == "ECR" || code == "ECO" || code == "ECA") {
        value = code;
        code = "Change";
      }

      this.typeList = [
        {
          name,
          code,
          value
        }
      ];
      // this.$refs.ref_dialog_table.show();
    },
    onOK(rows) {
      this.visibleDialog = false;
      let slaveObjectOids = rows.map(row => {
        return row.oid;
      });
      let params = {
        ...this._currentObj,
        mainObjectOid: this.objectDetailsData.oid,
        slaveObjectOids: slaveObjectOids
      };

      batchAddModel.execute(params).then(res => {
        this.loadFetch(this._currentObj.oid);
      });
    },
    addObjectCancel() {
      this.visibleDialog = false;
    },
    onConfirmDelete(itemObj) {
      let key = itemObj.oid;
      let rows = this.tableSelectedRows[key];

      this.onExpandPanel(key);
      if (_.isEmpty(rows)) {
        return this.$warning(this.$t("txt_pls_data"));
      }
      this.$confirm({
        title: "Wraining",
        content: this.$t("confirm_deletion"),
        okText: this.$t("btn_ok"),
        cancelText: this.$t("btn_cancel"),
        onOk: () => {
          this.onDelete(itemObj, rows);
        }
      });
    },
    onDelete(itemObj, rows) {
      this._currentObj = itemObj;
      let slaveObjectOids = rows.map(row => {
        return row.oid;
      });
      let params = {
        ...this._currentObj,
        mainObjectOid: this.objectDetailsData.oid,
        slaveObjectOids: slaveObjectOids
      };
      batchDeleteModel.execute(params).then(res => {
        this.loadFetch(this._currentObj.oid);
      });
    },
    _initData() {
      this._columns = [
        {
          field: "name",
          title: this.$t("txt_name"),
          cellRender: {
            name: "link",
            showLock: true,
            events: {
              click: ({ row }) => {
                this.onJumpTo(row);
              }
            }
          }
        },
        {
          field: "modelDefinition",
          title: this.$t("txt_type")
        },

        {
          field: "number",
          title: this.$t("txt_number")
        },
        {
          field: "lifecycleStatus",
          title: this.$t("txt_plan_lifecycle")
        },

        {
          field: "displayVersion",
          title: this.$t("txt_version")
        },

        {
          field: "updateDate",
          title: this.$t("txt_update_date"),
          width: 180,
          cellRender: {
            name: "date"
          }
        }
      ];
    },

    //打开文件夹或文档
    onJumpTo(data) {
      Jw.jumpToDetail(data, {
        blank: true
      });
      // window.open(`#/detailPage?oid=${data.oid}&type=${data.type}&masterType=${data.masterType}&modelDefinition=${data.modelDefinition}`, "_blank");
    },

    loadFetch(activeName) {
      let row = this.relatedObjList.find(item => {
        return item.oid == activeName;
      });
      relationSearchModel
        .execute({
          ...row,
          mainObjectOid: this.objectDetailsData.oid
        })
        .then(data => {
          this.data[activeName] = data;

          this.data = _.clone(this.data);
        })
        .catch(() => {
          this.data[activeName] = [];
        });
    }
  }
};
</script>

<style lang="less" scoped>
.review-object {
  .handle-btn {
    font-size: 14px;
    &:hover {
      cursor: pointer;
      color: @primary-color;
    }
  }
  .jw-table {
    height: auto;
    border: none;
  }
}
</style>
