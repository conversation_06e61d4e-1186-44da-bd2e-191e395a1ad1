<!--
* @Author:<EMAIL>
* @Date: 2022/04/28 18:48:03
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/04/28 18:48:03
* @Description: 
-->
<template>
  <div class="layout-form" v-if="data && data.component">
    <header>{{$t('txt_handle_from')}}</header>
    <main>
      <component :is="data.component"
                 v-if="data.component"
                 :class="layoutFormClass"
                 :ref="data.code" />
      <div v-else>
        {{$t('txt_from_not')}}
      </div>
    </main>
  </div>
</template>
<script>
import { EventBus } from "../units/api";
export default {
  name: "FormTask",
  props: {
    disabled: { type: Boolean, default: false },
  },
  components: {},
  watch: {
    form: {
      handler(newValue, oldValue) {
        this.$nextTick(() => {
          if (newValue && newValue.length) {
            this.activeKey = newValue.at(0).oid;
            this.data = newValue.map((p) => {
              try {
                p.component = () => import(`../form-task/${p.code}.vue`);
              } catch (error) {
                p.component = false;
                console.error(error.message);
              }
              return p;
            })[0];
          }
        });
      },
      immediate: true,
    },
  },
  data() {
    return {
      activeKey: undefined,
      data: {},
    };
  },
  computed: {
    layoutFormClass() {
      if (this.disabled) return "disabled";
      else return "";
    },
  },
  mounted() {
    try {
      let data = EventBus.data.taskForms[0];
      data.component = () => import(`../form-task/${data.code}.vue`);
      this.data = data;
    } catch (error) {
      this.data.component = false;
      // console.error(error.message);
    }
  },
  methods: {},
};
</script>
<style lang="less">
.layout-form {
  > header {
    padding: 12px 20px;
    font-size: 16px;
    font-weight: bold;
    position: relative;
    padding-left: 35px;
    &:before {
      position: absolute;
      content: "*";
      color: #fff;
      left: 20px;
      height: 22px;
      top: 9px;
      border-left: 4px solid #255ed7;
    }
  }
  > main {
    margin: 0 20px 20px;
    .ant-collapse {
      border: 0px;
      background-color: #fff;
      // border: 0px;
      .ant-collapse-header {
        border: 0px;
        padding: 12px 25px !important;
      }
      .ant-collapse-arrow {
        left: 0 !important;
      }
      .ant-collapse-item,
      .ant-collapse-content {
        border-top: 0px;
        // margin: 0 20px;
        .ant-collapse-content-box {
          padding: 0;
          .disabled {
            pointer-events: none;
          }
        }
      }
    }
  }
}
</style>