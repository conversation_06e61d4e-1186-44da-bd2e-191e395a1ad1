<!--
* @Author:<EMAIL>
* @Date: 2022/04/28 18:48:03
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/04/28 18:48:03
* @Description: 
-->
<template>
  <div class="review-object">
    <header>
      {{ $t("评审问题") }}
      <a-tooltip :title="$t('txt_add_object')">
        <span v-if="isEdit" @click="objVisible = true">
          <jw-icon type="jwi-iconadd-circle"></jw-icon>
        </span>
      </a-tooltip>
    </header>
    <jw-table ref="refTable" :maxHeight="500" :panel="true" :tooltip-config="{}" :columns="columns" :showPage="false"
      :data-source.sync="problemList">
      <template #numberSlot="{ row }">
        <span style="color: #255ed7; cursor: pointer" @click="routerLink(row)">
          <jw-icon :type="row.modelIcon" /> {{ row.number }}
          <a-tag v-if="row.primary" color="blue" style="margin-left: 8px">
            {{ $t("txt_object_main") }}
          </a-tag>
        </span>
      </template>
      <template #updateDate="{ row }">
        <span style="color: #255ed7">
          {{ row.updateDate | formatDateFn }}
        </span>
      </template>
      <template #isLock="{ row }">
        <a-tooltip>
          <template slot="title">
            {{ row.lockOwnerAccount }} {{ $t("txt_check_out") }}
          </template>
          <jw-icon v-if="row.lockOwnerOid" :type="row.lockOwnerOid === jwUser.oid
            ? '#jwi-beiwojianchu'
            : '#jwi-bierenjianchu'
            " />
        </a-tooltip>
      </template>
      <template #loadStatus="{ row }">
        <jw-icon v-if="!row.loadStatus" type="#jwi-liuchengzhong" />
      </template>
      <template #versionSlot="{ row }">
        <span>
          {{ row.displayVersion }}
        </span>
      </template>
      <template #proposedBy="{ row }">
        <span>
          {{ row.proposedBy.name }}
        </span>
      </template>
      <template #personLiable="{ row }">
        <span>
          {{ row.personLiable.name }}
        </span>
      </template>
    </jw-table>
    <create-problem ref="createProblemEl" :visible.sync="problemVisible" @getTableData="loadProblemList"
      @close="problemVisible = false"></create-problem>

    <a-modal title="选择部件" :visible.sync="objVisible" @ok="confirmProblem" width="50%" @cancel="objVisible = false">
      <jw-table ref="jwTable" :height="500" :tooltip-config="{}" :selectedRows.sync="selectedRows" :columns="partColumns" :showPage="false"
        :data-source.sync="data.bizObjects">
        <template #updateDate="{ row }">
          <span style="color: #255ed7">
            {{ row.updateDate | formatDateFn }}
          </span>
        </template>
      </jw-table>
    </a-modal>
  </div>
</template>
<script>
import { formatDate } from "jw_utils/moment-date"
import { EventBus } from "../units/api"
import { jwSearchEngineModal } from "jw_frame"
import ModelFactory from "jw_apis/model-factory"
import createProblem from "/views/problem-manage/problem-list/create-problem.vue";
/**
 * 获取问题列表
 */
const getProblemList = function (param) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.customerServer}/common/issue/findIssueByEntity?onlyUnClosed=false`,
    method: "post",
  }).execute(param)
}

export default {
  name: "ReviewObject",
  components: {
    jwSearchEngineModal, createProblem
  },
  props: {
    isEdit: { type: Boolean, default: false },
  },
  data() {
    return {
      data: {},
      problemList: [],
      objVisible: false,
      loading: false,
      problemVisible: false,
      objVisible: false,
      //当前选择列
      selectedRows: [],
    }
  },
  filters: {
    formatDateFn(date) {
      return formatDate(date)
    },
  },
  computed: {
    partColumns() {
      return [
        {
          field: "number",
          title: this.$t("txt_number_of"),
          sortable: true,
          minWidth: 180,
        },
        {
          field: "name",
          title: this.$t("txt_name"),
          sortable: true,
        },
        {
          field: "modelDefinition",
          title: this.$t("txt_type"),
          sortable: true,
        },
        {
          field: "lifecycleStatus",
          title: this.$t("txt_plan_lifecycle"),
          sortable: true,
        },
        {
          field: "displayVersion",
          title: this.$t("txt_version"),
          sortable: true,
        },
        {
          field: "updateDate",
          title: this.$t("txt_update_date"),
          sortable: true, // 开启排序
          slots: {
            default: "updateDate",
          },
        },
      ]
    },
    columns() {
      return [
        {
          field: "number",
          title: this.$t("txt_number_of"),
          sortable: true,
          minWidth: 180,
          slots: {
            default: "numberSlot",
          },
        },
        {
          field: "name",
          title: this.$t("txt_name"),
          sortable: true,
        },
        {
          field: "modelDefinition",
          title: this.$t("txt_type"),
          sortable: true,
        },
        {
          field: "lifecycleStatus",
          title: this.$t("txt_plan_lifecycle"),
          sortable: true,
        },
        {
          field: 'proposedBy',
          title: '提出人',
          slots: {
            default: "proposedBy"
          }
        },
        {
          field: 'personLiable',
          title: '责任人',
          slots: {
            default: "personLiable"
          }
        },
        {
          field: "updateDate",
          title: this.$t("txt_update_date"),
          sortable: true, // 开启排序
          slots: {
            default: "updateDate",
          },
        },
      ]
    },
  },
  mounted() {
    this.data = EventBus.data || {}
    this.loadProblemList()
  },
  methods: {
    //确认问题
    confirmProblem() {
      this.$refs['createProblemEl'].defaultTableList = [...this.selectedRows]
      this.objVisible = false
      this.problemVisible = true
    },
    //加载问题列表
    loadProblemList() {
      this.loading = true
      getProblemList(this.data.bizObjects).then((resp) => {
        this.problemList = resp
      })
    },
    routerLink(row) {
      if (row.type === "Issue") {
        let issueUrl = this.$router.resolve({
          name: `problem-detail`,
          path: `/problem-detail`,
          query: {
            oid: row.oid,
          },
        })
        window.open(issueUrl.href, "_bank")
      } else {
        Jw.jumpToDetail(row, { blank: true })
      }
    },
  },
}
</script>
<style lang="less">
.review-object {
  >header {
    padding: 12px 26px;
    font-size: 16px;
    font-weight: bold;
    position: relative;
    padding-left: 40px;

    &:before {
      position: absolute;
      content: "*";
      color: #fff;
      left: 24px;
      height: 22px;
      top: 9px;
      border-left: 4px solid #255ed7;
    }
  }

  .jw-table.is-panel {
    padding-top: 16px;
  }
}
</style>
