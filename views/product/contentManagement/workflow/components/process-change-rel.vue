<!--
* @Author:<EMAIL>
* @Date: 2022/04/28 18:48:03
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/04/28 18:48:03
* @Description: 
-->
<template>
  <div class="review-object">
    <header>
      {{ $t('rel_part_prod') }}
      <span v-if="listData && listData.length">({{ listData.length }})</span>
    </header>
    <jw-table ref="refTable" :maxHeight="500" :panel="true" :tooltip-config="{}" :columns="columns" :showPage="false" :data-source.sync="listData">
      <template #number="{ row }">
        <span style="color: #255ed7; cursor: pointer" @click="routerLink(row)">
          <jw-icon :type="row.modelIcon" /> {{ row.number }}
          <a-tag v-if="row.primary" color="blue" style="margin-left: 8px">
            {{ $t('txt_object_main') }}
          </a-tag>
        </span>
      </template>
      <template #influence="{row}">
        <a-radio-group v-model.trim="row.influence" v-if="isEdit">
          <a-radio :value="true">{{$t('txt_yes')}}</a-radio>
          <a-radio :value="false">{{$t('txt_no')}}</a-radio>
        </a-radio-group>
        <template v-else>{{row.influence?$t('change_is_influnence'):$t('change_no_influnence')}}</template>
      </template>
    </jw-table>
  </div>
</template>
<script>
import { EventBus } from "../units/api";
import { getProdateData } from 'views/change-management/components/structure/apis'
export default {
  name: "ReviewObject",
  props: {
    isEdit: { type: Boolean, default: false }
  },
  data() {
    return {
      data: {},
      objVisible: false,
      listData: [],
    };
  },

  computed: {
    columns() {
      return[
          {
          field:'number',
          title:this.$t('txt_number'),
          slots:{
            default:'number'
          }
        },
        {
          field:'name',
          title:this.$t('txt_name'),
          showOverflow:'ellipsis',
        },
        {
          field:'modelDefinition',
          title:this.$t('txt_type')
        },
        {
          field:'displayVersion',
          title:this.$t('txt_version')
        },
        {
          field:'lifecycleStatus',
          title:this.$t('txt_lifecycle')
        },
        {
          field:'influence',
          title:this.$t('is_change_data'),
          showOverflow:'ellipsis',
          slots:{
            default:'influence'
          }
        }
      ]
    }
  },
  mounted() {
    this.data = EventBus.data || {};
    let type =this.data.bizObjects[0]?.type;
    if (type == "TaskReceiving") {
      this.data.bizObjects.forEach(item => {
        let keys = [
          "name",
          "projectName",
          "taskStartTime",
          "taskEndTime",
          "taskOwner"
        ];
        keys.forEach(key => {
          item[key] = this.data.processOrderInfo?.extensionContent[key];
        });
      });
    }

    this.loadTableData()
  },
  methods: {
    routerLink(row) {
      if (row.type === "Issue") {
        let issueUrl = this.$router.resolve({
          name: `problem-detail`,
          path: `/problem-detail`,
          query: {
            oid: row.oid
          }
        });
        window.open(issueUrl.href, "_bank");
      } else {
        Jw.jumpToDetail(row, { blank: true });
      }
    },
    loadTableData() {
      this.listData.length = 0
      this.data.bizObjects.forEach((row) => {
        getProdateData({ partOid: row.oid })
          .then((data) => {
            this.listData.push(...data)
          })
      })
     
    },
   
  }
};
</script>
<style lang="less">
.review-object {
  > header {
    padding: 12px 26px;
    font-size: 16px;
    font-weight: bold;
    position: relative;
    padding-left: 40px;
    &:before {
      position: absolute;
      content: "*";
      color: #fff;
      left: 24px;
      height: 22px;
      top: 9px;
      border-left: 4px solid #255ed7;
    }
  }
  .jw-table.is-panel {
    padding-top: 16px;
  }
}
</style>