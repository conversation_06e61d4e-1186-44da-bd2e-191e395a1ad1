<!--
* @Author:<EMAIL>
* @Date: 2022/05/06 17:01:13
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/05/06 17:01:13
* @Description: 
-->
<template>
  <div class="essential-info">
    <a-collapse defaultActiveKey="essential-info" :bordered="false">
      <a-collapse-panel key="essential-info">
        <div slot="header">任务基本信息</div>
        <div class="essential-info-main">
          <a-form-model
            :model="data"
            :label-col="labelCol"
            :wrapper-col="wrapperCol"
          >
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-model-item :label="$t('task_name')">
                  <a-input v-model.trim="data.name" placeholder="" disabled />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item :label="$t('txt_assignee')">
                  <a-input v-model.trim="data.assignee" placeholder="" disabled />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-model-item :label="$t('txt_start_time_enble')">
                  <a-date-picker
                    v-model.trim="data.startTime"
                    placeholder=""
                    disabled
                    show-time
                    type="date"
                    style="width: 100%"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item :label="$t('txt_end_time')">
                  <a-date-picker
                    v-model.trim="data.dueDate"
                    placeholder=""
                    disabled
                    show-time
                    type="date"
                    style="width: 100%"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-form-model-item :label="$t('txt_operation_guide')" v-if="data.documentation">
              <div class="request-approval-main">
                {{ data.documentation }}
              </div>
            </a-form-model-item>
          </a-form-model>
        </div>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>
<script>
import { EventBus } from "../units/api";
export default {
  name: "essential-info",
  computed: {},
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      data: {
        dueDate:null,
        startTime:null
      },
    };
  },
  methods: {},
  mounted() {
    this.data = EventBus.data || {};
  },
};
</script>
<style lang="less">
.essential-info {
  .ant-collapse {
    background-color: #fff;
    // border: 0px;
    .ant-collapse-item,
    .ant-collapse-content {
      border: 0px;
    }
    .ant-collapse-header {
      padding:0 24px 16px !important;
      &:before {
        position: absolute;
        content: "*";
        color: #fff;
        border-left: 4px solid #255ed7;
      }
      > i.ant-collapse-arrow {
        right: 10px;
        width: 20px;
        left: inherit !important;
      }
      > div {
        font-size: 16px;
        margin-left: 15px;
        font-weight: bold;
      }
    }
    .ant-collapse-content > .ant-collapse-content-box {
      padding: 0px 24px 0px;
    }
  }
  .request-approval-main {
    border: 1px solid #a4c9fc;
    border-radius: 8px;
    padding: 15px;
    background-color: #f0f7ff;
  }
  .review-object {
    .jw-table.vxe-grid.is--animat.is-panel {
      padding: 0;
    }
    .vxe-table.vxe-table--render-default.border--full.row--highlight.is--header.is--animat {
      margin: 0;
    }
  }
  .essential-info-main {
  }
}
</style>