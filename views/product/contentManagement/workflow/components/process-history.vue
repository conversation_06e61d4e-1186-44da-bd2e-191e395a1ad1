<!--
* @Author:<EMAIL>
* @Date: 2022/04/28 19:08:42
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/04/28 19:08:42
* @Description: 
-->
<template>
  <div class='process-history'>
    <a-collapse defaultActiveKey='process-history'
                :bordered='false'>
      <a-collapse-panel key="process-history">
        <div slot="header">
        {{$t('txt_processing_record')}} 
        </div>
        <main class="process-history-main">
          <a-timeline>
            <a-timeline-item v-for="item in data"
                             :key="item.oid">
              <div class="main">
                <div class="ant-popover-arrow"></div>
                <div class="main-left">
                  <a-tag color="green">
                    {{item.subjectDisplayName}}
                  </a-tag>
                  <div>
                    {{item.content}}
                  </div>
                </div>
                <div class="main-right">
                  <span class="date">{{item.createDate|formatDateFn}}</span>
                  <div class="avatar">
                    <jw-avatar :title="item.createBy"
                               :data="item.createBy|avatarName" />
                    <span v-show="item.extensionContent.targetAssign">
                       &nbsp; <i class="jwi-iconarrow-right" style="font-size:25px" /> &nbsp;
                       </span>
                       <user-info :accounts="[item.extensionContent.targetAssign]"></user-info>
                       
                    <!-- <span class="text"
                          :title="item.userId">{{item.userId && item.userId.substr(0,1)}}</span> -->

                    <!-- <span class="text"
                          :title="item.targetAssign"
                          v-show="item.targetAssign">{{item.targetAssign && item.targetAssign.substr(0,1)}}</span> -->
                    <!-- <a-avatar class="user-avatar"
                              :size="26"
                              :src="userAvater" /> =>
                    <a-avatar class="user-avatar"
                              :size="26"
                              :src="userAvater" /> -->
                  </div>
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </main>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>
<script>
import { jwAvatar } from "jw_frame";
import { getTaskHistory,getTaskHistoryData } from "../units/api";
import { formatDate } from "jw_utils/moment-date";
import userInfo from "components/user-info";
export default {
  name: "ProcessHistory",
  computed: {},
  data() {
    return {
      data: [],
    };
  },
  created() {},
  mounted() {
    this.init();
  },
  watch: {
    $route(to, from) {
      this.init();
    },
  },
  components: { jwAvatar, userInfo },
  methods: {
    init() {
      getTaskHistoryData(this.$route.query.taskId)
        .then((res) => {
          this.data = res;
        })
        .catch((err) => {
          this.$error(err.msg || err.message);
        })
        .finally(() => {});
    },
  },
  filters: {
    avatarName(name) {
      return {
        name: name,
      };
    },
    formatDateFn(date) {
      return formatDate(date);
    },
  },
};
</script>
<style lang="less">
.process-history {
  .ant-collapse {
    background-color: #fff;
    // border: 0px;
    .ant-collapse-item,
    .ant-collapse-content {
      border: 0px;
    }
    .ant-collapse-header {
      padding: 12px 24px !important;
      &:before {
        position: absolute;
        content: "*";
        color: #fff;
        border-left: 4px solid #255ed7;
      }
      > i.ant-collapse-arrow {
        right: 10px;
        width: 20px;
        left: inherit !important;
      }
      > div {
        font-size: 16px;
        margin-left: 15px;
        font-weight: bold;
      }
    }
    .ant-collapse-content > .ant-collapse-content-box {
      padding: 0px 20px 0px;
    }
  }
  .process-history-main {
    padding-top: 20px;
    .ant-timeline-item-content {
      margin-left: 25px;
    }
    .ant-timeline-item {
      padding: 0 0 10px;
      &:hover,
      &.curter {
        .content {
          cursor: pointer;
          background-color: #f0f7ff;
          // box-shadow: 0 2px 8px #acbafc;
          border: 1px solid #aac4ff;
          .ant-popover-arrow {
            border-bottom: 1px solid #aac4ff;
            border-left: 1px solid #aac4ff;
            background-color: #f0f7ff;
            // box-shadow: 0 2px 8px #acbafc;
          }
        }
      }
    }
    .main {
      background-clip: padding-box;
      border-radius: 4px;
      // box-shadow: 0 2px 8px #d0d0d0;
      border: 1px solid #d0d0d0;
      padding: 10px;
      background-color: #fffaff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .main-left {
        > span {
          margin-bottom: 10px;
        }
        > div {
          min-height: 32px;
        }
      }
      .main-right {
        display: flex;
        align-items: center;
        > span {
          margin-right: 20px;
        }
        > .avatar {
          display: flex;
          align-items: center;
          span.text {
            display: inline-block;
            width: 32px;
            height: 32px;
            line-height: 32px;
            font-size: 16px;
            border-radius: 50%;
            text-align: center;
            color: #fff;
            background-color: #c8b5e5;
            cursor: pointer;
          }
        }
      }
      .ant-popover-arrow {
        transform: translateY(-50%) rotate(45deg);
        top: 10px;
        left: -3px;
        display: block;
        width: 10px;
        height: 10px;
        background: transparent;
        border-style: solid;
        border-width: 4px;
        background-color: #fffaff;
        // position: absolute;
        border-color: transparent transparent #d0d0d0 #d0d0d0;
        border-left: 1px solid #d0d0d0;
        border-bottom: 1px solid #d0d0d0;
      }
    }
  }
}
</style>