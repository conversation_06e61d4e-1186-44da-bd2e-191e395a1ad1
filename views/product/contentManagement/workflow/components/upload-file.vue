<template>
  <div class="service-uploader-wrapper">
    <a-upload-dragger v-if="layoutName!='show'"
                      ref="ref_upload"
                      class="upload-file-cont"
                      action="#"
                      :accept='_accept'
                      :multiple="multiple"
                      :showUploadList='false'
                      :beforeUpload='beforeUpload'>
      <p class="ant-upload-drag-icon">
        <a-icon type="inbox" />
      </p>
      <p class="ant-upload-text">
        <em class="link">{{$t('txt_fiel_click')}}  </em>
      </p>
      <p class="ant-upload-hint">
        <span v-if="_accept">{{$t('txt_only_upload')}}{{_accept}}{{$t('txt_type_file')}},</span> {{$t('model.file.sizelimited')}}
      </p>
    </a-upload-dragger>

    <div class="file-item"
         :class="{'error-tip':item.status=='error',add:item.type==='add',delete:item.type==='delete'}"
         :title="item.status=='error'?$t('txt_upload_feil_faile'):''"
         v-for="(item,i) in showFileList"
         :key="i">
      <span class="file-status"
            @click="onDownload(item)">
        {{item.name}}
        <a-icon type="loading"
                style="margin-left:20px"
                v-if="item.status==='uploading'" />
        <a-icon type="check"
                style="margin-left:20px;color:green"
                v-if="item.status==='done'" />
        <a-icon type="close"
                style="margin-left:20px;color:green"
                v-if="item.status==='error'" />
      </span>
      <template v-if="item.status=='error'">
        <span>
          <i :title="$t('txt_delete')"
             class="jwi-icondelete"
             @click="onDelete(item,i)"></i>
        </span>
      </template>
      <span v-else
            class="file-hanld-btn">
        <i  :title="$t('txt_preview')"
           class="jwi-visible file-handle-btn"
           @click="onPreview(item)"></i>
        <i  :title="$t('btn_download')"
           class="jwi-icondownload file-handle-btn"
           @click="onDownload(item)"></i>
        <template v-if="layoutName!='show'">
          <i :title="$t('btn_delete')"
             class="jwi-icondelete file-handle-btn"
             @click="onDelete(item,i)"></i>
        </template>
      </span>
    </div>
    <a-progress v-if="_uploadFile.status=='uploading'"
                :percent="percent"
                :strokeWidth='2' />
  </div>
</template>

<script>
import { Progress } from "ant-design-vue";
import axios from "axios";
import util from "jw_common/util";
import commonStore from "jw_stores/common";

import ModelFactory from "jw_apis/model-factory";

const preSignedPutUrl = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.fileMicroServer}/file/preSignedPutUrl`,
  method: "get",
});

const FileMetadataApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.fileMicroServer}/file/createFileMetadata`,
  method: "post",
});

const previewApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.fileMicroServer}/file/getUrlByOidForPreview`,
  method: "get",
});

export default {
  components: {
    "a-progress": Progress,
  },
  props: {
    multiple: {
      type: Boolean,
      default: false,
    },
    accept: {
      type: Array,
      default: () => [],
    },
    value: {
      type: Array,
      default: () => [],
    },
    layoutName: String,
  },

  data() {
    this._fileParams = {};
    this._uploadFile = {};
    // this._maxSize = 20;
    // this._maxMultiple = 5;
    // this._accept = ["xls", "xml", "pdf"];
    return {
      fileList: [],
      percent: 0,
    };
  },

  created() {
    this.fileList = this.getInitValue();
  },

  watch: {
    value(val) {
      this.fileList = val || [];
    },
    fileList(val) {
      // this.$parent.form.attachments = val;
    },
  },
  computed: {
    showBefore() {
      return this.layoutName == "show";
    },
    showFileList() {
      const { fileList } = this;
      if (this.showBefore) {
        //   let list = fileList.slice();
        //   const beforeData = this.formModel.beforeData || {};
        //   const beforeList = beforeData[this.prop] || [];
        //   beforeList.forEach((item) => {
        //     const isIn = fileList.find((r) => r.oid === item.oid);
        //     if (!isIn) {
        //       list.push({ ...item, type: "delete" });
        //     }
        //   });
        //   list.forEach((item) => {
        //     const isIn = beforeList.find((r) => r.oid === item.oid);
        //     if (!isIn) {
        //       item.type = "add";
        //     }
        //   });
        return fileList;
      } else {
        return fileList;
      }
    },
  },
  methods: {
    beforeUpload(file) {
      if (!this.fileCheck(file)) return false;
      this._uploadFile = { name: file.name, status: "uploading" };
      this.fileList.push(this._uploadFile);

      this.retrieveNewURL(file, (url) => {
        // 上传文件到服务器
        this.uploadFile(url, file);
      });
      return false;
    },

    fileCheck(file) {
      let isoutRange = this.fileList.length == this._maxMultiple;

      if (isoutRange) {
        this.$error(this.$t('txt_up_upload')+`${this._maxMultiple}`+this.$t('txt_a_feil'));
        return false;
      }

      let type = file.name;
      type = type.substr(type.lastIndexOf(".") + 1).toLowerCase();

      if (this._accept) {
        let isoutFormat = !this._accept.includes(type);
        if (isoutFormat) {
          this.$error(`${this.$t('txt_upload_feil_fromat')}${this._accept}`);
          return false;
        }
      } else {
        return true;
      }

      let isoutSize = file.size / 1024 / 1024 >= this._maxSize;
      if (isoutSize) {
        this.$error(this.$t('txt_feil_size_20'));
        return false;
      }
      return true;
    },

    retrieveNewURL(file, cb) {
      let timestamp = new Date().getTime();
      this._fileParams.fileSize = file.size;
      this._fileParams.fileName = timestamp + "_" + file.name;
      let param = {
        fileName: this._fileParams.fileName,
      };
      this.loading = true;
      preSignedPutUrl
        .execute(param)
        .then((res) => {
          let url = decodeURIComponent(res.url);
          this._fileParams.bucketName = res.bucketName;

          cb(url);
        })
        .catch((err) => {
          this._uploadFile.status = "error";
        });
    },
    uploadFile(url, file) {
      axios
        .put(url, file, {
          onUploadProgress: (progressEvent) => {
            file.status = "uploading";
            this.percent = file.percentage = parseInt(
              (progressEvent.loaded / progressEvent.total) * 100
            );

            if (file.percentage == 100) {
              this.percent = 0;
              file.status = "success";
            }
          },
        })
        .then((res) => {
          this.createFileMetadata(file);
        })
        .catch((err) => {
          this._uploadFile.status = "error";
        });
    },
    createFileMetadata(file) {
      FileMetadataApi.execute(this._fileParams)
        .then((res) => {
          res.name = res.fileOriginalName;
          res.description = res.oid;
          Object.assign(this._uploadFile, res, { status: "done" });
          this.$emit("change", this.fileList);
        })
        .catch((err) => {
          this._uploadFile.status = "error";
        });
    },
    handleChange(info) {
      // const status = info.file.status;
      // if (status === "done") {
      //   let response = info.file.response;
      //   if (response.code == 0) {
      //     let fileRes = response.result;
      //     this.$emit("change", fileRes);
      //   }
      // } else if (status === "error") {
      //   this.$message.error(`${info.file.name} file upload failed.`);
      // }
    },

    onDownload(item) {
      if (item.description) {
        util.download(
          `${Jw.gateway}/${Jw.fileMicroServer}/file/downloadByOid?fileOid=${item.description}`
        );
      }
    },
    onDelete(file, i) {
      this.fileList.splice(i, 1);
      if (file.oid) {
        this.$emit("change", this.fileList);
      }
    },

    onPreview(file) {
      // if (file.fileSize > 1024 * 1000 * 100) {
      //   return this.$warning("文件大小超出预览标准，请下载查看！");
      // }
      previewApi
        .execute({
          fileOid: file.description,
        })
        .then((url) => {
          commonStore.set("query", url);
          window.open("#/preview", "_blank");
        })
        .catch((err) => {
          if (err.code === -1) {
            this.$error(this.$t('nonsupport_preview'));
          }
        })
        .finally(() => {});
    },

    getInitValue() {
      let value = this.value;
      let _accept = this.accept;
      this._accept = _accept
        .map((item) => {
          return "." + item;
        })
        .join(",");
      this._maxSize = this.fileSize;
      this._maxMultiple = this.multipleNum;
      if (_.isEmpty(value)) {
        return [];
      }
      return value;
    },
    fileStatus(fileStatus) {
      let classFile = ["file-status"];
      if (fileStatus === "error") {
        classFile.push("error");
      } else if (fileStatus === "uploading") {
        classFile.push("uploading");
      } else if (fileStatus === "done") {
        classFile.push("done");
      } else if (fileStatus === "removed") {
        classFile.push("removed");
      }
      return classFile;
    },
  },
};
</script>

<style lang="less" scoped>
.service-uploader-wrapper {
  .upload-file-cont {
    width: 100%;
  }
  .file-item {
    display: flex;
    justify-content: space-between;
    line-height: 24px;
    margin-top: 5px;
    color: #40a9ff;
    &:hover {
      background: @item-hover-bg;
      .file-handle-btn {
        opacity: 1;
      }
    }
    .file-handle-btn {
      opacity: 0;
      transition: all 0.3s;
      cursor: pointer;
      color: @link-color;
    }
    &.add {
      color: #2be62b;
    }
    &.delete {
      color: @jw-block-del-bg;
      text-decoration: line-through;
    }
  }
  .file-status {
    cursor: pointer;
  }
  .error-tip {
    color: #f5222d;
  }
}
</style>


