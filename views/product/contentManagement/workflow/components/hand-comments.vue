<!--
* @Author:<EMAIL>
* @Date: 2022/04/28 18:48:03
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/04/28 18:48:03
* @Description: 
-->
<template>
  <div class="hand-comments">
    <header>{{$t('txt_processing_opinion')}}</header>
    <div>
      <header>{{$t('txt_opinion')}}</header>
      <main>
        <a-textarea
          v-model.trim="comment"
          @change="changeComment"
          allow-clear
          rows="6"
          placeholder=""
          :maxLength="2048"
          :disabled="disabled"
        />
      </main>
    </div>
    <div>
      <header>{{$t('txt_attachment')}}</header>
      <main>
        <jwUploadFile
          v-model.trim="data.attachments"
          :layoutName="disabled ? 'show' : 'update'"
        />
        <!-- :multiple="true" -->
      </main>
    </div>
  </div>
</template>
<script>
import jwUploadFile from "./upload-file.vue";
import { EventBus } from "../units/api";
import ModelFactory from "jw_apis/model-factory"

const integrationResult = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/integration/queryIntegrationResult`,
  method: "get",
})
export default {
  name: "hand-comments",
  components: {
    jwUploadFile,
  },
  props: {
    disabled: { type: Boolean, default: false },
    processData:{type:Object}
  },
  computed: {
    layoutFormClass() {
      if (this.disabled) return "disabled";
      else return "";
    },
  },
  data() {
    return {
      comment: "5555",
      data: {},
    };
  },
  methods: {
    changeComment() {
      //处理首次输入会卡屏
      this.data["comment"] = this.comment;
    },
  },
  mounted() {
    this.data = EventBus.data || {};
    console.log('900000',this.data)
    if (this.data.name==='集成异常处理' && this.data.variables.length>0){
     let variable = this.data.variables.filter(item=>item.name==='unReleaseEntity');
     if (variable.length>0){
       integrationResult
           .execute({
             unReleaseEntity: variable[0].value,
           })
           .then((res) => {
             this.comment=res
           })
           .catch((err) => {
             this.$error(err.msg)
           })
     }
    }else {
      this.comment = EventBus.data.comment || "";
    }
  },
};
</script>
<style lang="less">
.hand-comments {
  > header {
    padding: 12px 20px;
    font-size: 16px;
    font-weight: bold;
    position: relative;
    padding-left: 40px;
    &:before {
      position: absolute;
      content: "*";
      color: #fff;
      left: 24px;
      height: 22px;
      top: 9px;
      border-left: 4px solid #255ed7;
    }
  }
  > div {
    margin: 0 20px;
    > header {
      padding: 10px 0;
    }
  }
}
</style>