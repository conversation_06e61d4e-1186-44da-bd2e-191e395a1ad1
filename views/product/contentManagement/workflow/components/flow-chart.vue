<!--
* @Author:<EMAIL>
* @Date: 2022/04/28 17:02:02
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/04/28 17:02:02
* @Description: 
-->
<template>
  <div class="flow-chart">
    <header v-if="showIcon" @click="routerLinkDetailed">
      {{ data && data.name }} &nbsp;
      <a-icon
        type="exclamation-circle"
        :title="$t('txt_work_task_flow')"
        theme="filled"
      />
    </header>
    <a-tabs default-active-key="handle" @change="tabChange">
      <a-tab-pane key="handle" :tab="$t('txt_processing_flow')" force-render>
        <a-timeline class="process-history">
          <a-timeline-item
            :color="item.color"
            v-for="item in processHistory"
            :key="item.id"
          >
            <!-- <div slot="dot">123</div> -->
            <!-- <i class="iconchat-full"></i> -->
            <header>
              {{ item.name }}（{{
                (item.comment && item.comment.selectRouting) ||
                $t("txt_unfinished")
              }}）
            </header>
            <main @click="routerLink(item)" :class="classes(item)">
              <div>{{ item.comment && item.comment.comment }}</div>
              <footer>
                <span>{{ formatDate(item.createTime) }}</span>
                <user-info :accounts="[item.assignee]"></user-info>
              </footer>
            </main>
          </a-timeline-item>
        </a-timeline>
      </a-tab-pane>
      <a-tab-pane key="overview" :tab="$t('txt_flow_view')" force-render>
        <ElementDrag>
          <img
            :src="diagramIMG"
            @dblclick="showFullscreen = true"
            :alt="$t('txt_flow_view')"
          />
        </ElementDrag>
      </a-tab-pane>
    </a-tabs>
    <div class="fullscreen-img" v-if="showFullscreen === true">
      <div>
        <a-icon type="close-circle" @click="showFullscreen = false" />
        <ElementDrag :outerOptions="outerOptions">
          <img :src="diagramIMG" :alt="$t('txt_flow_view')" />
        </ElementDrag>
      </div>
    </div>
  </div>
</template>
<script>
import { EventBus, getTaskComment, getDiagramImg } from "../units/api";
import { formatDate } from "jw_utils/moment-date";
import ElementDrag from "./element-drag.vue";
import { jwAvatar } from "jw_frame";
import userInfo from "components/user-info";
export default {
  name: "FlowChart",
  props: {
    showIcon: { type: Boolean, default: true },
    processModelId: { type: String, default: "" },
  },
  computed: {
    // diagramIMG() {},
    classes() {
      return (item) => [
        {
          cur: item.id === this.$route.query.taskId,
        },
      ];
    },
  },
  components: {
    ElementDrag,
    jwAvatar,
    userInfo
  },
  data() {
    return {
      processHistory: [],
      showFullscreen: false,
      outerOptions: {
        background: "rgba(0,0,0,.45)",
      },
      data: {},
      diagramIMG: "",
    };
  },
  created() {    
    EventBus.$off("FlowChartHistoryInit");
    EventBus.$off("FlowChartInit");
    EventBus.$on("FlowChartHistoryInit", this.init);
    EventBus.$on("FlowChartInit", (data) => {
      this.data = data;
    });
  },
  destroyed() {
    EventBus.$off("FlowChartHistoryInit");
    EventBus.$off("FlowChartInit");
  },
  mounted() {
    this.init();
    this.data = EventBus.data || this.$parent.data;
  },
  filters: {
    avatarName(name) {
      return {
        name: name,
      };
    },
  },
  methods: {
    tabChange(name) {
      if (name === "handle") {
        this.init();
      } else {
        let params = {};
        this.diagramIMG = "";
        if (this.$route.query.processInstanceId) {
          params = {
            type: true,
            id: this.$route.query.processInstanceId,
          };
        } else {
          params = {
            type: false,
            id: this.processModelId,
          };
        }
        this.diagramIMG = getDiagramImg(params);
      }
    },
    init() {
      if (!this.$route.query.processInstanceId) return;
      getTaskComment({ processInstanceId: this.$route.query.processInstanceId })
        .then((res) => {
          this.processHistory = res;
        })
        .catch((err) => {
          this.$error(err.msg || err.message);
        })
        .finally(() => {});
    },
    formatDate(date) {
      return formatDate(date);
    },
    routerLink(row) {
      this.$router.push({
        path: "/process-task",
        query: {
          taskId: row.id,
          processInstanceId: row.processInstanceId,
        },
      });
    },
    routerLinkDetailed() {
      this.$router.push({
        path: "/particulars-task",
        query: {
          processOrderOid: this.data.processOrderOid,
          processInstanceId: this.$route.query.processInstanceId,
          processInstanceName: this.data.processInstanceName,
          formName: this.$t("txt_rask_flow"),
        },
      });
    },
  },
};
</script>
<style lang="less">
.flow-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
  > header {
    font-size: 16px;
    font-weight: bold;
    margin-left: 24px;
    > i {
      cursor: pointer;
      :hover {
        color: #1890ff;
      }
    }
    // display: flex;
    // justify-content: space-between;
    // align-items: center;
    // padding: 0 20px;
    // > span {
    //   color: #000;
    //   font-weight: bold;
    // }
  }
  .ant-tabs {
    height: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    .ant-tabs-nav .ant-tabs-tab {
      padding: 12px 0px;
    }
    .ant-tabs-nav-scroll {
      padding-left: 24px;
    }
    .ant-tabs-content {
      height: 20px;
      flex-grow: 1;
      .ant-tabs-tabpane {
        height: 100%;
        > main {
          height: 100%;
          .process-item {
            width: 200px;
            border-radius: 6px;
            border: 1px solid #ddd;
            > header {
              padding: 15px 15px;
              display: flex;
              color: #fff;
              justify-content: space-between;
              background-color: rgb(29, 182, 2);
              border-top-left-radius: 6px;
              border-top-right-radius: 6px;
            }
            > main {
              img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                margin: 10px 15px;
              }
              > div {
                padding: 10px 0;
                margin: 0 15px;
                color: rgb(97, 155, 255);
                border-top: 1px solid #ddd;
              }
            }
          }
        }
      }
    }
  }
  .process-history {
    height: 100%;
    overflow: auto;
    padding: 0 24px;
    .ant-timeline-item-content {
      > header {
        font-size: 14px;
        font-weight: bold;
        padding: 5px 0;
      }
      > main {
        background-color: rgba(30, 32, 42, 0.04);
        padding: 10px 16px 12px 16px;
        border-radius: 7px;
        cursor: pointer;
        &.cur,
        &:hover {
          background-color: #f0f7ff;
        }
        > div {
          font-size: 12px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2; //行数
          -webkit-box-orient: vertical;
          min-height: 20px;
          color: rgba(30, 32, 42, 0.85);
        }
        > footer {
          border-top: 1px solid #ddd;
          margin-top: 8px;
          display: flex;
          padding-top: 10px;
          align-items: center;
          font-size: 12px;
          color: rgba(30, 32, 42, 0.45);
          line-height: 20px;
          justify-content: space-between;
        }
      }
    }
  }
  .fullscreen-img {
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 999;
    overflow: auto;
    outline: 0;
    > div {
      height: 100%;
      width: 100%;
      position: relative;
      z-index: 999;
      > i {
        position: absolute;
        right: 10%;
        top: 10%;
        z-index: 999;
        font-size: 30px;
        color: #fff;
      }
    }
  }
}
</style>
