<!--
* @Author:<EMAIL>
* @Date: 2022/04/28 18:48:03
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/04/28 18:48:03
* @Description: 
-->
<template>
  <div class="review-object">
    <header>
      {{ $t("txt_inspection_object") }}
      <span v-if="data.bizObjects && data.bizObjects.length"
        >({{ data.bizObjects.length }})</span
      >
      <a-tooltip :title="$t('txt_add_object')">
        <span v-if="isEdit" @click="objVisible = true">
          <jw-icon type="jwi-iconadd-circle"></jw-icon>
        </span>
      </a-tooltip>
    </header>
    <jw-table
      ref="refTable"
      :maxHeight="500"
      :panel="true"
      :tooltip-config="{}"
      :columns="columns"
      :showPage="false"
      :data-source.sync="data.bizObjects"
      @onOperateClick="onOperateClick"
    >
      <template #numberSlot="{ row }">
        <span style="color: #255ed7; cursor: pointer" @click="routerLink(row)">
          <jw-icon :type="row.modelIcon" /> {{ row.number }}
          <a-tag v-if="row.primary" color="blue" style="margin-left: 8px">
            {{ $t("txt_object_main") }}
          </a-tag>
        </span>
      </template>
      <template #updateDate="{ row }">
        <span style="color: #255ed7">
          {{ row.updateDate | formatDateFn }}
        </span>
      </template>
      <template #isLock="{ row }">
        <a-tooltip>
          <template slot="title">
            {{ row.lockOwnerAccount }} {{ $t("txt_check_out") }}
          </template>
          <jw-icon
            v-if="row.lockOwnerOid"
            :type="
              row.lockOwnerOid === jwUser.oid
                ? '#jwi-beiwojianchu'
                : '#jwi-bierenjianchu'
            "
          />
        </a-tooltip>
      </template>
      <template #loadStatus="{ row }">
        <jw-icon v-if="!row.loadStatus" type="#jwi-liuchengzhong" />
      </template>
      <template #versionSlot="{ row }">
        <span>
          {{ row.displayVersion }}
        </span>
      </template>
      <template #operation="{ row }">
        <div>
          <a-tooltip title="创建问题" v-if="data.processDefinitionType === 'ECAD_review'">
            <span @click="createProblemFun(row)" style="cursor: pointer">
              <jw-icon type="jwi-wentifengxian2x"> </jw-icon>
            </span>
          </a-tooltip>
          <a-tooltip title="对比">
            <span @click="gethistorydata(row)" style="cursor: pointer">
              <jw-icon type="jwi-iconreturn"> </jw-icon>
            </span>
          </a-tooltip>
        </div>
      </template>
    </jw-table>
    <jw-search-engine-modal
      ref="addObjectContent"
      :title="$t('txt_add_object')"
      :onlySearchObject="true"
      :visible.sync="objVisible"
      :model-list="modelList"
      @ok="onAddObj"
    />
    <create-problem
      ref="createProblemEl"
      :visible.sync="problemVisible"
      @getTableData="fetchTable"
      @close="problemVisible = false"
    ></create-problem>
  </div>
</template>
<script>
import { formatDate } from "jw_utils/moment-date"
import { EventBus, getTaskDetails } from "../units/api"
import { getCookie } from "jw_utils/cookie"
import { jwSearchEngineModal } from "jw_frame"
import ModelFactory from "jw_apis/model-factory"
import { getHistoryList, findVersionRule } from "apis/part"
import createProblem from "/views/problem-manage/problem-list/create-problem.vue";

/**
 * 获取cadfile
 */
const getCadFiles = function (oid) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.cadService}/mcad/findCADFile?Oid=${oid}`,
    method: "post",
  }).execute()
}

// 添加评审对象
const addBizObject = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/addBizObject`,
  method: "post",
})

// 删除评审对象
const deleteBizObject = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/deleteBizObject`,
  method: "post",
})

const findSecBom = function (oid) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.cadService}/mcad/useTree/findUseAllOrLevel`,
    method: "get",
  }).execute({ rootOid: oid })
}

export default {
  name: "ReviewObject",
  components: {
    jwSearchEngineModal,createProblem
  },
  props: {
    isEdit: { type: Boolean, default: false },
  },
  watch: {
    isEdit: {
      handler(val) {
        if (val) {
          this.columns.push({
            field: "operation",
            title: this.$t("txt_operation"),
            btns: [
              {
                icon: "jwi-icondelete",
                title: this.$t("txt_delete"),
                key: "delete",
                isShow: function (row) {
                  return !row.primary
                },
              },
            ],
          })
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      data: {},
      objVisible: false,
      problemVisible: false,
      modelList: [
        {
          name: this.$t("txt_part"),
          code: "PartIteration",
        },
        {
          name: this.$t("txt_document"),
          code: "DocumentIteration",
        },
        {
          name: "MCAD",
          code: "MCADIteration",
        },
        {
          name: "ECAD",
          code: "ECADIteration",
        },
        {
          name: this.$t("txt_baseline"),
          code: "Baseline",
        },
        {
          name: this.$t("Issue"),
          code: "Issue",
        },
      ],
    }
  },
  filters: {
    formatDateFn(date) {
      return formatDate(date)
    },
  },
  computed: {
    columns() {
      return this.data.bizObjects &&
        this.data.bizObjects[0]?.type == "TaskReceiving"
        ? [
            {
              field: "projectName",
              title: this.$t("项目名称"),
              sortable: true,
            },
            {
              field: "name",
              title: this.$t("任务名称"),
              sortable: true,
              formatter: ({ text, row }) => {
                return row.cname || row.name
              }
            },
            {
              field: "taskStartTime",
              title: this.$t("任务开始时间"),
              sortable: true,
            },
            {
              field: "taskEndTime",
              title: this.$t("任务结束时间"),
              sortable: true,
            },
            {
              field: "taskOwner",
              title: this.$t("任务负责人"),
              sortable: true,
            },
          ]
        : [
            {
              field: "number",
              title: this.$t("txt_number_of"),
              sortable: true,
              minWidth: 180,
              slots: {
                default: "numberSlot",
              },
            },
            {
              field: "name",
              title: this.$t("txt_name"),
              sortable: true,
              formatter: ({ text, row }) => {
                return row.cname || row.name
              }
            },
            {
              field: "modelDefinition",
              title: this.$t("txt_type"),
              sortable: true,
            },
            {
              field: "lifecycleStatus",
              title: this.$t("txt_plan_lifecycle"),
              sortable: true,
            },
            {
              field: "displayVersion",
              title: this.$t("txt_version"),
              sortable: true,
              slots: {
                default: "versionSlot",
              },
            },
            {
              field: "updateDate",
              title: this.$t("txt_update_date"),
              sortable: true, // 开启排序
              slots: {
                default: "updateDate",
              },
            },
            {
              // 操作列定义
              field: "operation", //关键字
              title: "对比",
              slots: {
                default: "operation",
              },
            },
          ]
    },
  },
  mounted() {
    this.data = EventBus.data || {}
    console.log(this.data)
    let type = this.data.bizObjects[0]?.type
    if (type == "TaskReceiving") {
      this.data.bizObjects.forEach((item) => {
        let keys = [
          "name",
          "projectName",
          "taskStartTime",
          "taskEndTime",
          "taskOwner",
        ]
        keys.forEach((key) => {
          item[key] = this.data.processOrderInfo?.extensionContent[key]
        })
      })
    }
  },
  methods: {
    //加载table
    fetchTable(){
      this.$emit('reloadProblem')
    },
    createProblemFun(row){
      this.problemVisible = true
      this.$refs['createProblemEl'].defaultTableList = [row]
      console.log(row, '创建问题')
    },
    async opensolidwork(row) {
      if (row.masterType !== "MCAD") {
        this.$warning("只可以打开MCAD类型")
        return
      }
      if (!SpaceSDK.setSpaceCache) {
        this.$warning("请在Space中使用此功能")
        return
      }
      if (row.modelDefinition === "CADAssembly") {
        let boms = await findSecBom(row.oid)
        let allbom = this.listbomtotable(boms[0])
        let files = []
        for (let i = 0; i < allbom.length; i++) {
          const element = allbom[i]
          let file = await getCadFiles(element.oid)
          files = [...files, ...file]
        }
        if (files.length > 0) {
          this.openwindows(files)
        }
      } else {
        let files = await getCadFiles(row.oid)
        if (files.length > 0) {
          this.openwindows(files)
        }
      }
    },
    listbomtotable(data) {
      let res = []
      let loadchildren = function (val) {
        res.push(val)
        if (val && val.children && val.children.length !== 0) {
          val.children.forEach((item) => {
            item.preSourceOid = val.oid
            loadchildren(item)
          })
        }
      }
      loadchildren(data)
      return res
    },
    openwindows(filesall) {
      let files = filesall.filter((item) => item.primary)
      console.log("打开solidwork", files)
      SpaceSDK.setSpaceCache({
        key: "3dx-plugin-environment",
        value: {
          appName: "pdm",
          tenantOid: getCookie("tenantOid"),
        },
      })

      var result = files.map((item) => {
        return {
          fileOid: item.url,
          fileName: item.fileName,
          fileLastModified: item.lastModified,
          dirPath: [this.data.displayName],
        }
      })
      SpaceSDK.next("download-file", {
        fileInfoList: result,
        appName: "CAD",
        hasMessage: false,
        openFileInfo: {
          isCADOpen: true, // 是否打开
          execPath: [this.data.displayName],
          isCadName: result[0].fileName,
        },
      })
    },
    contrastbom(source, target) {
      let url = this.$router.resolve({
        name: "baseline-contrast",
        query: {
          containerOid: source.containerOid,
          objectType: "object",
          sourceType: source.type,
          sourceOid: source.oid,
          sourceModelType: source.modelDefinition,
          targetOid: target.oid,
          targetType: target.type,
          targetModelType: target.modelDefinition,
        },
      })
      window.open(url.href, "_blank")
    },
    //查找历史版本
    gethistorydata(row) {
      const { modelDefinition, oid, type, masterType, tenantOid } = row
      if (masterType !== "Part") {
        this.$warning("只有Part类型可以查看版本对比")
        return
      }
      Promise.all([
        getHistoryList.execute({
          modelDefinition,
          oid,
          type,
        }),
        findVersionRule.execute({
          modelCode: modelDefinition,
          containerOid: tenantOid,
        }),
      ]).then((resp) => {
        let resp1 = resp[0]
        let resp2 = resp[1]
        let target = null
        if (resp2) {
          let rules = resp2.rule.split(",")
          let ruleIndex = rules.findIndex((item) => item === row.version)
          if (ruleIndex > 0) {
            //找到上个版本的最新版
            let rule = rules[ruleIndex - 1]
            target = resp1
              .filter((item) => item.version === rule)
              .reduce((prev, curr) =>
                prev.versionSortId > curr.versionSortId ? prev : curr
              )
          } else {
            //未找到上个最大版本使用当前版本比较
            target = resp1
              .filter((item) => item.version === row.version)
              .reduce((prev, curr) =>
                prev.versionSortId < curr.versionSortId ? prev : curr
              )
          }
        }
        if (!target) {
          target = resp1[resp1.length - 1]
        }

        if (target) {
          this.contrastbom(row, target)
        } else {
          this.$warning("未找到之前的版本")
        }
      })
    },
    beforeUpload() {},
    onRemoveFile() {},
    routerLink(row) {
      if (row.type === "Issue") {
        let issueUrl = this.$router.resolve({
          name: `problem-detail`,
          path: `/problem-detail`,
          query: {
            oid: row.oid,
          },
        })
        window.open(issueUrl.href, "_bank")
      } else {
        Jw.jumpToDetail(row, { blank: true })
      }
    },
    onOperateClick(key, row) {
      if (key === "delete") {
        this.onDeleteObj(row)
      }
    },
    onAddObj(selectedRows) {
      let params = {
        oid: this.data.processOrderOid,
      }
      let bizObjects = []
      selectedRows.forEach((item) => {
        let temp = {
          oid: item.oid,
          type: item.type,
        }
        bizObjects.push(temp)
      })
      params.bizObjects = bizObjects
      addBizObject
        .execute(params)
        .then((res) => {
          this.$success(this.$t("txt_add_success"))
          this.objVisible = false
          this.getDetails()
        })
        .catch((err) => {
          this.$error(err.msg)
        })
    },
    onDeleteObj(row) {
      this.$confirm({
        title: this.$t("txt_comfirm_remove"),
        okText: this.$t("btn_ok"),
        cancelText: this.$t("btn_cancel"),
        onOk: () => {
          return deleteBizObject
            .execute({
              oid: this.data.processOrderOid,
              bizObjects: [
                {
                  oid: row.oid,
                  type: row.type,
                },
              ],
            })
            .then((res) => {
              this.$success(this.$t("txt_remove_success"))
              this.getDetails()
            })
            .catch((err) => {
              this.$error(err.msg)
            })
        },
      })
    },
    getDetails() {
      let params = {
        tenantId: getCookie("tenantOid"),
        taskId: this.$route.query.taskId,
      }
      getTaskDetails(params)
        .then((res) => {
          this.data.bizObjects = res.bizObjects
          EventBus.$emit("changeBizObjects", res.bizObjects)
        })
        .catch((err) => {
          this.$error(err.msg)
        })
    },
  },
}
</script>
<style lang="less">
.review-object {
  > header {
    padding: 12px 26px;
    font-size: 16px;
    font-weight: bold;
    position: relative;
    padding-left: 40px;
    &:before {
      position: absolute;
      content: "*";
      color: #fff;
      left: 24px;
      height: 22px;
      top: 9px;
      border-left: 4px solid #255ed7;
    }
  }
  .jw-table.is-panel {
    padding-top: 16px;
  }
}
</style>
