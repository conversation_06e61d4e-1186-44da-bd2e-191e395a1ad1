<template>

    <div class="essential-info">
        <a-collapse defaultActiveKey="essential-info" :bordered="false">
            <a-collapse-panel key="essential-info">
                <div slot="header">流程申请单信息</div>
                <div class="essential-info-main">
                    <jw-layout-builder ref="ref_appBuilder" view type="Model" :layoutName="'create'" :modelName="processOrderInfo.modelDefinition" :instanceData="processOrderInfo">
                    </jw-layout-builder>
                </div>
            </a-collapse-panel>
        </a-collapse>
    </div>

</template>
<script>
import { jwLayoutBuilder } from "jw_frame";

import { EventBus } from "../units/api";
export default {
  components: {
    jwLayoutBuilder
  },
  data() {
    return {
      processOrderInfo: {}
    };
  },
  mounted() {
    this.processOrderInfo = EventBus.data.processOrderInfo || {};
  }
};
</script>

