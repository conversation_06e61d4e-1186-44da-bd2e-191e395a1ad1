<template>
  <div class="essential-info">
    <a-collapse defaultActiveKey="essential-info" :bordered="false">
      <a-collapse-panel key="essential-info">
        <div slot="header">流程申请单信息</div>
        <div class="essential-info-main">
          <jw-layout-builder ref="ref_appBuilder" type="Model" :instanceData="instanceData" :layoutName="layoutName" :modelName="modelName" @initModel='onInitModel'>

          </jw-layout-builder>
        </div>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>
<script>
import { jwLayoutBuilder } from "jw_frame";
import { EventBus } from "../units/api";
export default {
  props: ["layoutName", "modelName", "partInfo"],
  components: {
    jwLayoutBuilder
  },
  data() {
    return {
       instanceDataNew: {}
    };
  },
  computed: {
    instanceData() {
      if (!_.isEmpty(this.partInfo)) {
        if (this.partInfo.levelForSecrecy){
          this.partInfo.levelForSecrecy = parseInt(this.partInfo.levelForSecrecy)
          if (this.partInfo.extensionContent  && this.partInfo.extensionContent.thumbnailFile){
            this.partInfo.extensionContent.thumbnailFile = this.partInfo.extensionContent.thumbnailFile.map(item=>{
              return{
                oid: item.oid,
                name: item.name,
              }
            })
          }
        }
        return this.partInfo;
      }
      if (EventBus.data?.processOrderInfo && EventBus.data?.processOrderInfo.levelForSecrecy){
        this.instanceDataNew = EventBus.data?.processOrderInfo
        this.instanceDataNew.levelForSecrecy = parseInt(this.instanceDataNew.levelForSecrecy)
        if (this.instanceDataNew.extensionContent  && this.instanceDataNew.extensionContent.thumbnailFile){
          this.instanceDataNew.extensionContent.thumbnailFile = this.instanceDataNew.extensionContent.thumbnailFile.map(item=>{
            return{
              oid: item.oid,
              name: item.name,
            }
          })
        }
      }
       return this.instanceDataNew || {};
    }
  },
  mounted() {},
  methods: {
    onInitModel({ layout }) {
      if (this.modelName == "ElectricalPart") {
        let rows = layout?.content?.layout;
        rows.forEach(row => {
          row.forEach(item => {
            if (item.fieldName == "classificationInfo") {
              item.disabled = true;
            }
          });
        });
      }
    },
    getLayoutInfo() {
      let appBuilder = this.$refs.ref_appBuilder;
      return appBuilder.validate().then(() => {
        return appBuilder.getValue();
      });
    }
  }
};
</script>
<style lang="less">
.essential-info {
  .ant-collapse {
    background-color: #fff;
    // border: 0px;
    .ant-collapse-item,
    .ant-collapse-content {
      border: 0px;
    }
    .ant-collapse-header {
      padding: 0 24px 16px !important;
      &:before {
        position: absolute;
        content: "*";
        color: #fff;
        border-left: 4px solid #255ed7;
      }
      > i.ant-collapse-arrow {
        right: 10px;
        width: 20px;
        left: inherit !important;
      }
      > div {
        font-size: 16px;
        margin-left: 15px;
        font-weight: bold;
      }
    }
    .ant-collapse-content > .ant-collapse-content-box {
      padding: 0px 24px 0px;
    }
  }
  .request-approval-main {
    border: 1px solid #a4c9fc;
    border-radius: 8px;
    padding: 15px;
    background-color: #f0f7ff;
  }
}
</style>

