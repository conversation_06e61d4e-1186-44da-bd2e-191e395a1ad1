<!--
* @Author:<EMAIL>
* @Date: 2022/05/16 14:48:59
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/05/16 14:48:59
* @Description: 
-->
<template>
  <div class="submit-edit">
    <div>
      <!-- 基本信息 -->
      <EssentialInfo></EssentialInfo>
      <!-- 评审对象 -->
      <ReviewObject></ReviewObject>
      <!-- 表单 -->
      <!-- <FormTask></FormTask> -->
      <!-- 处理意见 -->
      <HandComments></HandComments>
      <!-- 流程历史 -->
      <ProcessHistory></ProcessHistory>
    </div>
    <!-- 动态按钮 -->
    <FotterButton></FotterButton>
  </div>
</template>
<script>
import EssentialInfo from "../../components/essential-info";
import HandComments from "../../components/hand-comments";
import ReviewObject from "../../components/review-object.vue";
import ProcessHistory from "../../components/process-history";
// import FormTask from "../components/form-task.vue";
import FotterButton from "./footer-button.vue";
export default {
  name: "SubmitEdit",
  data() {
    return {};
  },
  components: {
    EssentialInfo,
    HandComments,
    ReviewObject,
    ProcessHistory,
    // FormTask,
    FotterButton,
  },
  created() {},
  methods: {},
};
</script>
<style lang="less" scoped>
.submit-edit {
  height: 100%;
  display: flex;
  flex-direction: column;
  > div:first-child {
    height: 20px;
    flex-grow: 1;
    overflow: auto;
  }
  > div:last-child {
  }
}
</style>