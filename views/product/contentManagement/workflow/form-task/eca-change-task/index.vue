<!--
* @Author:<EMAIL>
* @Date: 2022/05/16 17:54:54
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/05/16 17:54:54
* @Description: 
-->
<template>
  <multipane
    class="custom-resizer default-form"
    layout="vertical"
  >
    <div class="pane">
      <component :is="renderForm" />
    </div>
    <multipane-resizer></multipane-resizer>
    <div class="pane">
      <FlowChart ref="flowChart"></FlowChart>
    </div>
  </multipane>
</template>
<script>
import { Multipane, MultipaneResizer } from "vue-multipane";
import { EventBus } from "../../units/api";
import FlowChart from "../../components/flow-chart";
export default {
  name: "DefaultForm",
  data() {
    return {
      data: {},
    };
  },
  computed: {
    renderForm() {
      //根据权限动态加载组件
      let type =
        this.data.assignee === Jw.getUser().account && !this.data.endTime
          ? "submit-edit"
          : "submit-view";
      return async () => import(`/${type}.vue`);
    },
  },
  components: {
    Multipane,
    MultipaneResizer,
    FlowChart,
  },
  mounted() {
    this.data = EventBus.data || {};
  },
  methods: {},
};
</script>
<style lang="less" scoped>
.multipane-resizer {
  height: 100%;
  z-index: 1;
  margin: 0;
  left: 0;
  position: relative;
  background: #eee;
  &:before {
    display: block;
    content: "";
    width: 3px;
    height: 40px;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -20px;
    margin-left: -1.5px;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
  }
  &:hover {
    &:before {
      border-color: #999;
    }
  }
}
.custom-resizer.default-form {
  height: 100%;
  display: flex;
  width: 100%;
  > .pane {
    text-align: left;
    // padding: 15px;
    overflow: hidden;
  }
  > div {
    &:first-child {
      width: 75%;
      display: flex;
      flex-direction: column;
      padding-right: 8px;
      > main {
        height: 100%;
      }
    }
    &:last-child {
      width: 20px;
      flex-grow: 1;
    }
  }
}
</style>