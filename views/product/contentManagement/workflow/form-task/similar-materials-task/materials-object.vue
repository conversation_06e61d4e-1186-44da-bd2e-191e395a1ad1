<template>
  <div class="review-object">
    <header v-if="titleShow">
      {{ $t("txt_materials") }}
    </header>
    <jw-table
      ref="refTable"
      :maxHeight="500"
      :panel="true"
      :tooltip-config="{}"
      :columns="columns"
      :showPage="true"
      :pagerConfig="pagerConfig"
      :data-source.sync="tableList"
      @onPageChange="onPageChange"
      @onSizeChange="onSizeChange"
    >
      <template #numberSlot="{ row }">
        <span style="color: #255ed7; cursor: pointer" @click="routerLink(row)">
          <jw-icon :type="row.modelIcon" /> {{ row.number }}
          <a-tag v-if="row.primary" color="blue" style="margin-left: 8px">
            {{ $t("txt_object_main") }}
          </a-tag>
        </span>
      </template>
      <template #versionSlot="{ row }">
        <span>
          {{ row.displayVersion }}
        </span>
      </template>
      <template #materialsNumberSlot="{ row }">
        <span
          style="color: #255ed7; cursor: pointer"
          @click="routerLink(row.materialsObj)"
        >
          <jw-icon :type="row.materialsObj.modelIcon" />
          {{ row.materialsObj.number }}
          <a-tag
            v-if="row.materialsObj.primary"
            color="blue"
            style="margin-left: 8px"
          >
            {{ $t("txt_object_main") }}
          </a-tag>
        </span>
      </template>
      <template #materialsNameSlot="{ row }">
        <span>
          {{ row.materialsObj.name }}
        </span>
      </template>
      <template #materialsVersionSlot="{ row }">
        <span>
          {{ row.materialsObj.displayVersion }}
        </span>
      </template>
      <template #materialsLifecycleStatusSlot="{ row }">
        <span>
          {{ $t(row.materialsObj.lifecycleStatus) }}
        </span>
      </template>
    </jw-table>
  </div>
</template>
<script>
import { formatDate } from "jw_utils/moment-date"
import { EventBus } from "../../units/api"
import { getCookie } from "jw_utils/cookie"
import ModelFactory from "jw_apis/model-factory"
// 判断物料去重
const findSamePartData = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/findSamePartData`,
  method: "post",
})
export default {
  name: "ReviewObject",
  components: {},
  props: {
    titleShow: { type: Boolean, default: true },
    createPartList: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  watch: {
    dataList: {
      handler() {
        this.tableData()
      },
      deep: true,
    },
    createPartList: {
      handler() {
        this.tableData()
      },
      deep: true,
    },
  },
  data() {
    return {
      dataList: [],
      objVisible: false,
      loading: false,
      pagerConfig: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      tableList: [],
    }
  },
  filters: {},
  computed: {
    columns() {
      return this.titleShow
        ? [
            {
              field: "number",
              title: this.$t("txt_number_of"),
              // sortable: true,
              minWidth: 180,
              slots: {
                default: "numberSlot",
              },
            },
            {
              field: "name",
              title: this.$t("txt_name"),
              // sortable: true,
            },
            {
              field: "modelDefinition",
              title: this.$t("txt_type"),
              // sortable: true,
            },
            {
              field: "lifecycleStatus",
              title: this.$t("txt_plan_lifecycle"),
              formatter: ({ row }) => this.$t(row.lifecycleStatus),
              // sortable: true,
            },
            {
              field: "displayVersion",
              title: this.$t("txt_version"),
              // sortable: true,
              slots: {
                default: "versionSlot",
              },
            },

            {
              title: this.$t("txt_materials_number"),
              minWidth: 180,
              slots: {
                default: "materialsNumberSlot",
              },
            },
            {
              title: this.$t("txt_materials_name"),
              minWidth: 150,

              slots: {
                default: "materialsNameSlot",
              },
            },
            {
              title: this.$t("txt_materials_version"),
              minWidth: 150,

              slots: {
                default: "materialsVersionSlot",
              },
            },
            {
              title: this.$t("txt_materials_stats"),
              minWidth: 150,
              slots: {
                default: "materialsLifecycleStatusSlot",
              },
            },
          ]
        : [
            {
              field: "number",
              title: this.$t("txt_number_of"),
              minWidth: 180,
              slots: {
                default: "numberSlot",
              },
            },
            {
              field: "name",
              title: this.$t("txt_name"),
            },
            {
              field: "extensionContent.cn_jwis_xqr.displayName",
              title: '需求人',
            },
            {
              field: "extensionContent.cn_jwis_gg",
              title: '图号/规格',
            },
            {
              field: "clsProperty.cn_jwis_sccj",
              title: '生产厂家',
            },
            {
              field: "clsProperty.cn_jwis_zldj",
              title: '质量等级',
            },
            {
              field: "clsProperty.cn_jwis_fzxs",
              title: '封装形式',
            },
            {
              field: "displayVersion",
              title: this.$t("txt_version"),

              slots: {
                default: "versionSlot",
              },
            },
            {
              field: "lifecycleStatus",
              title: this.$t("txt_plan_lifecycle"),
              formatter: ({ row }) => this.$t(row.lifecycleStatus),
            },
          ]
    },
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.titleShow ? this.getFindSamePartData() : ""
    })
    EventBus.$on("changeBizObjects", this.getFindSamePartData)
  },
  methods: {
    beforeUpload() {},
    onRemoveFile() {},
    routerLink(row) {
      if (row.OID) {
        row.oid = row.OID
      }
      if (row.type === "Issue") {
        let issueUrl = this.$router.resolve({
          name: `problem-detail`,
          path: `/problem-detail`,
          query: {
            oid: row.oid,
          },
        })
        window.open(issueUrl.href, "_bank")
      } else {
        Jw.jumpToDetail(row, { blank: true })
      }
    },
    tableData() {
      this.pagerConfig.total === 0
        ? (this.pagerConfig.total = this.dataList.length)
        : ""
      this.tableList = []
      if (this.titleShow) {
        this.tableList = this.dataList.slice(
          (this.pagerConfig.current - 1) * this.pagerConfig.pageSize,
          this.pagerConfig.current * this.pagerConfig.pageSize
        )
      } else {
        this.tableList = this.createPartList.slice(
          (this.pagerConfig.current - 1) * this.pagerConfig.pageSize,
          this.pagerConfig.current * this.pagerConfig.pageSize
        )
      }
    },

    async getFindSamePartData() {
      this.dataList = []

      let arr = [...EventBus.data.bizObjects]
      findSamePartData.execute(EventBus.data?.bizObjects).then(async (res) => {
        if (res && !(JSON.stringify(res) === "{}")) {
          let arr2 = Object.keys(res)
          await arr.forEach(async (elem) => {
            elem
            for await (let item of arr2) {
              if (elem.oid === item) {
                res[item].length != 0
                  ? res[item].forEach((el, index) => {
                      let obj = {
                        ...elem,
                        materialsObj: { ...el },
                      }
                      obj.OID = obj.oid
                      obj.oid = obj.oid + Math.random() + index
                      this.dataList.push(obj)
                    })
                  : ""
              }
            }
          })
        }
        // // this.dataList = Array.Set(Array.from(this.dataList))
        // this.dataList = JSON.parse(JSON.stringify(this.dataList))
        // console.log(this.dataList)
        this.tableData()
        // console.log(this.dataList)
      })
    },
    onPageChange(page) {
      this.pagerConfig.current = page
      this.tableData()
      // this.getFindSamePartData()
    },
    onSizeChange(pageSize) {
      this.pagerConfig.pageSize = pageSize
      this.tableData()
      // this.getFindSamePartData()
    },
    show() {
      this.pagerConfig.total = this.createPartList.length
      this.pagerConfig.current = 1
      this.pagerConfig.pageSize = 10
      this.tableData()
    },
  },
  beforeDestroy() {
    EventBus.$off("changeBizObjects", this.getFindSamePartData)
  },
}
</script>
<style lang="less">
.review-object {
  > header {
    padding: 12px 26px;
    font-size: 16px;
    font-weight: bold;
    position: relative;
    padding-left: 40px;
    &:before {
      position: absolute;
      content: "*";
      color: #fff;
      left: 24px;
      height: 22px;
      top: 9px;
      border-left: 4px solid #255ed7;
    }
  }
  .jw-table.is-panel {
    padding-top: 16px;
  }
}
</style>
