<!--
* @Author:<EMAIL>
* @Date: 2022/05/16 14:48:59
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/05/16 14:48:59
* @Description: 
-->
<template>
  <div class="submit-edit">
    <div>
      <!-- 流程申请单信息 -->
      <ProcessOrderInfo
        v-if="processDefinitionType == 'Dwg_change_Workflow'"
      ></ProcessOrderInfo>
      <!-- 基本信息 -->
      <EssentialInfo></EssentialInfo>
      <ChangeBefore
        v-if="processDefinitionType == 'Dwg_change_Workflow'"
      ></ChangeBefore>

      <!-- 评审对象 -->
      <ReviewObject :isEdit="true"></ReviewObject>
      <!-- 相似物料 -->
      <materialsObject></materialsObject>
      <!-- 评审受影响对象 -->
      <ProcessChangeRel
        :isEdit="true"
        v-if="processDefinitionType == 'Dwg_change_Workflow'"
      />
      <!-- 相关对象 -->
      <RelatedObject
        :isEdit="true"
        v-if="processDefinitionType == 'PPMRWCL'"
      ></RelatedObject>
      <!-- 表单 -->
      <!-- <FormTask></FormTask> -->
      <!-- 处理意见 -->
      <HandComments></HandComments>
      <!-- 流程历史 -->
      <ProcessHistory></ProcessHistory>
    </div>
    <!-- 动态按钮 -->
    <FotterButton></FotterButton>
  </div>
</template>
<script>
import ProcessOrderInfo from "../../components/processOrder-info"
import ProcessChangeRel from "../../components/process-change-rel.vue"
import EssentialInfo from "../../components/essential-info"
import HandComments from "../../components/hand-comments"
import ReviewObject from "./review-object.vue"
import RelatedObject from "../../components/related-object.vue"
import ChangeBefore from "../../components/change-before.vue"
import ProcessHistory from "../../components/process-history"
// import FormTask from "../components/form-task.vue";
import FotterButton from "../../components/footer-button.vue"
import materialsObject from "./materials-object.vue"
export default {
  name: "SubmitEdit",
  props: ["processDefinitionType"],
  data() {
    return {}
  },
  components: {
    ProcessOrderInfo,
    EssentialInfo,
    HandComments,
    ReviewObject,
    ProcessHistory,
    RelatedObject,
    // FormTask,
    FotterButton,
    ChangeBefore,
    ProcessChangeRel,
    materialsObject,
  },
  created() {},
  methods: {},
}
</script>
<style lang="less" scoped>
.submit-edit {
  height: 100%;
  display: flex;
  flex-direction: column;
  > div:first-child {
    height: 20px;
    flex-grow: 1;
    overflow: auto;
  }
  > div:last-child {
  }
}
</style>
