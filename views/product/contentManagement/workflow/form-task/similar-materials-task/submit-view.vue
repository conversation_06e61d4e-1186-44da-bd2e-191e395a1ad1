<!--
* @Author:<EMAIL>
* @Date: 2022/05/16 14:48:59
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/05/16 14:48:59
* @Description: 
-->
<template>
  <div class="submit-view">
    <!-- 流程申请单信息 -->
    <ProcessOrderInfo
      v-if="processDefinitionType == 'Dwg_change_Workflow'"
    ></ProcessOrderInfo>
    <!-- 任务信息 -->
    <EssentialInfo></EssentialInfo>
    <ChangeBefore
      v-if="processDefinitionType == 'Dwg_change_Workflow'"
    ></ChangeBefore>
    <!-- 评审对象 -->
    <ReviewObject></ReviewObject>
    <!-- 相似物料 -->
    <materialsObject></materialsObject>
    <ProcessChangeRel
      :isEdit="false"
      v-if="processDefinitionType == 'Dwg_change_Workflow'"
    />
    <!-- 相关对象 -->
    <RelatedObject
      v-if="
        processDefinitionType == 'PPMRWCL' ||
        processDefinitionType == 'Dwg_change_Workflow'
      "
    ></RelatedObject>
    <!-- 表单 -->
    <!-- <FormTask :disabled="true"
              ref="LayoutForm"></FormTask> -->
    <!-- 处理意见 -->
    <HandComments :disabled="true"></HandComments>
    <!-- 流程历史 -->
    <ProcessHistory></ProcessHistory>
  </div>
</template>
<script>
import ProcessOrderInfo from "../../components/processOrder-info"
import EssentialInfo from "../../components/essential-info"
import HandComments from "../../components/hand-comments"
import ReviewObject from "./review-object.vue"
import RelatedObject from "../../components/related-object.vue"
import ChangeBefore from "../../components/change-before.vue"
import ProcessHistory from "../../components/process-history"
import materialsObject from "./materials-object.vue"

// import FormTask from "../components/form-task.vue";

export default {
  name: "SubmitEdit",
  props: ["processDefinitionType"],
  data() {
    return {}
  },
  components: {
    ProcessOrderInfo,
    EssentialInfo,
    HandComments,
    ReviewObject,
    ProcessHistory,
    ChangeBefore,
    RelatedObject,
    // FormTask,
    materialsObject,
  },
  created() {},
  methods: {},
}
</script>
<style lang="less" scoped>
.submit-view {
  height: 100%;
  overflow: auto;
}
</style>
