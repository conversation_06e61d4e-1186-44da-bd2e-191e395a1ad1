<template>
  <div class="review-object2">
    <header v-if="titleShow">
      {{ $t("txt_materials") }}
    </header>
    <vxe-table
      class="vxeTable"
      border
      show-overflow
      ref="xTable"
      :maxHeight="500"
      :span-method="rowspanMethod"
      :data="dataList"
      stripe
    >
      <vxe-column :title="$t('txt_inspection_object')" header-align="center">
        <vxe-column field="number" :title="$t('txt_number_of')"> </vxe-column>
        <vxe-column field="name" :title="$t('txt_name')"> </vxe-column>
        <vxe-column field="modelDefinition" :title="$t('txt_type')">
        </vxe-column>
        <vxe-column field="lifecycleStatus" :title="$t('txt_plan_lifecycle')">
        </vxe-column>
        <vxe-column field="displayVersion" :title="$t('txt_version')">
        </vxe-column>
      </vxe-column>

      <vxe-column :title="$t('txt_materials')" header-align="center">
        <vxe-column field="materialsObj.number" :title="$t('txt_number_of')">
        </vxe-column>
        <vxe-column field="materialsObj.name" :title="$t('txt_name')">
        </vxe-column>
        <vxe-column
          field="materialsObj.displayVersion"
          :title="$t('txt_version')"
        >
        </vxe-column>
        <vxe-column
          field="materialsObj.lifecycleStatus"
          :title="$t('txt_plan_lifecycle')"
        >
        </vxe-column>
      </vxe-column>
    </vxe-table>
  </div>
</template>
<script>
import { formatDate } from "jw_utils/moment-date"
import { EventBus } from "../../units/api"
import { getCookie } from "jw_utils/cookie"
import ModelFactory from "jw_apis/model-factory"
// 判断物料去重
const findSamePartData = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/findSamePartData`,
  method: "post",
})
export default {
  name: "ReviewObject",
  components: {},
  props: {
    titleShow: { type: Boolean, default: true },
    createPartList: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  watch: {},
  data() {
    return {
      dataList: [],
      objVisible: false,
      loading: false,
    }
  },
  filters: {},
  computed: {
    columns() {
      return this.titleShow
        ? [
            {
              field: "number",
              title: this.$t("txt_number_of"),
              // sortable: true,
              minWidth: 180,
              slots: {
                default: "numberSlot",
              },
            },
            {
              field: "name",
              title: this.$t("txt_name"),
              // sortable: true,
            },
            {
              field: "modelDefinition",
              title: this.$t("txt_type"),
              // sortable: true,
            },
            {
              field: "lifecycleStatus",
              title: this.$t("txt_plan_lifecycle"),
              // sortable: true,
            },
            {
              field: "displayVersion",
              title: this.$t("txt_version"),
              // sortable: true,
              slots: {
                default: "versionSlot",
              },
            },

            {
              title: this.$t("txt_materials_number"),
              minWidth: 180,
              slots: {
                default: "materialsNumberSlot",
              },
            },
            {
              title: this.$t("txt_materials_name"),
              minWidth: 150,

              slots: {
                default: "materialsNameSlot",
              },
            },
            {
              title: this.$t("txt_materials_version"),
              minWidth: 150,

              slots: {
                default: "materialsVersionSlot",
              },
            },
            {
              title: this.$t("txt_materials_stats"),
              minWidth: 150,

              slots: {
                default: "materialsLifecycleStatusSlot",
              },
            },
          ]
        : [
            {
              field: "number",
              title: this.$t("txt_number_of"),
              minWidth: 180,
              slots: {
                default: "numberSlot",
              },
            },
            {
              field: "name",
              title: this.$t("txt_name"),
            },
            {
              field: "displayVersion",
              title: this.$t("txt_version"),

              slots: {
                default: "versionSlot",
              },
            },
            {
              field: "lifecycleStatus",
              title: this.$t("txt_plan_lifecycle"),
            },
          ]
    },
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.titleShow ? this.getFindSamePartData() : ""
    })
  },
  methods: {
    rowspanMethod({ row, _rowIndex, column, visibleData }) {
      let fields = [
        "number",
        "name",
        "modelDefinition",
        "lifecycleStatus",
        "displayVersion",
      ]
      let cellValue = row[column.property]
      if (cellValue && fields.includes(column.property)) {
        let prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && prevRow[column.property] === cellValue) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    beforeUpload() {},
    onRemoveFile() {},
    routerLink(row) {
      if (row.type === "Issue") {
        let issueUrl = this.$router.resolve({
          name: `problem-detail`,
          path: `/problem-detail`,
          query: {
            oid: row.oid,
          },
        })
        window.open(issueUrl.href, "_bank")
      } else {
        Jw.jumpToDetail(row, { blank: true })
      }
    },

    async getFindSamePartData() {
      let arr = [...EventBus.data.bizObjects]
      findSamePartData.execute(EventBus.data?.bizObjects).then((res) => {
        if (!(JSON.stringify(res) === "{}")) {
          let arr2 = Object.keys(res)

          arr.forEach(async (elem) => {
            elem
            for await (let item of arr2) {
              if (elem.oid === item) {
                res[item].length != 0
                  ? res[item].forEach((el) => {
                      this.dataList.push({
                        ...elem,
                        materialsObj: { ...el },
                      })
                    })
                  : ""
              }
            }
          })
        }
        this.dataList = Array.from(this.dataList)
        // this.dataList = [
        //   {
        //     id: 10001,
        //     name: "Test1",
        //     nickname: "T1",
        //     role: "Designer",
        //     sex: "0",
        //     sex2: ["0"],
        //     num1: 40,
        //     age: 28,
        //     address: "Shenzhen",
        //     date12: "",
        //     date13: "",
        //   },
        //   {
        //     id: 10002,
        //     name: "Test2",
        //     nickname: "T2",
        //     role: "Designer",
        //     sex: "1",
        //     sex2: ["0", "1"],
        //     num1: 20,
        //     age: 22,
        //     address: "Guangzhou",
        //     date12: "",
        //     date13: "2020-08-20",
        //   },
        //   {
        //     id: 10003,
        //     name: "Test3",
        //     nickname: "T3",
        //     role: "Test",
        //     sex: "0",
        //     sex2: ["1"],
        //     num1: 200,
        //     age: 32,
        //     address: "Shanghai",
        //     date12: "2020-09-10",
        //     date13: "",
        //   },
        //   {
        //     id: 10004,
        //     name: "Test4",
        //     nickname: "T4",
        //     role: "Designer",
        //     sex: "1",
        //     sex2: ["0"],
        //     num1: 30,
        //     age: 23,
        //     address: "Shenzhen",
        //     date12: "",
        //     date13: "2020-12-04",
        //   },
        //   {
        //     id: 10005,
        //     name: "Test5",
        //     nickname: "T5",
        //     role: "Test",
        //     sex: "1",
        //     sex2: ["1"],
        //     num1: 18,
        //     age: 26,
        //     address: "Shenzhen",
        //     date12: "",
        //     date13: "",
        //   },
        //   {
        //     id: 10006,
        //     name: "Test6",
        //     nickname: "T6",
        //     role: "Test",
        //     sex: "1",
        //     sex2: ["1"],
        //     num1: 35,
        //     age: 28,
        //     address: "BeiJing",
        //     date12: "",
        //     date13: "2020-09-04",
        //   },
        //   {
        //     id: 10007,
        //     name: "Test7",
        //     nickname: "T7",
        //     role: "Test",
        //     sex: "1",
        //     sex2: ["1"],
        //     num1: 11,
        //     age: 24,
        //     address: "BeiJing",
        //     date12: "2020-08-10",
        //     date13: "2020-04-22",
        //   },
        //   {
        //     id: 10008,
        //     name: "Test8",
        //     nickname: "T8",
        //     role: "Develop",
        //     sex: "0",
        //     sex2: ["1"],
        //     num1: 30,
        //     age: 21,
        //     address: "Shenzhen",
        //     date12: "",
        //     date13: "",
        //   },
        //   {
        //     id: 10009,
        //     name: "Test9",
        //     nickname: "T9",
        //     role: "Develop",
        //     sex: "1",
        //     sex2: ["0"],
        //     num1: 25,
        //     age: 31,
        //     address: "Guangzhou",
        //     date12: "",
        //     date13: "2020-04-10",
        //   },
        //   {
        //     id: 100010,
        //     name: "Test10",
        //     nickname: "T10",
        //     role: "Develop",
        //     sex: "1",
        //     sex2: ["1"],
        //     num1: 30,
        //     age: 29,
        //     address: "BeiJing",
        //     date12: "2020-04-18",
        //     date13: "2020-04-10",
        //   },
        //   {
        //     id: 100011,
        //     name: "Test11",
        //     nickname: "T11",
        //     role: "Test",
        //     sex: "0",
        //     sex2: ["1"],
        //     num1: 33,
        //     age: 30,
        //     address: "Shenzhen",
        //     date12: "",
        //     date13: "",
        //   },
        //   {
        //     id: 100012,
        //     name: "Test12",
        //     nickname: "T12",
        //     role: "Designer",
        //     sex: "1",
        //     sex2: ["1"],
        //     num1: 22,
        //     age: 20,
        //     address: "Guangzhou",
        //     date12: "",
        //     date13: "2020-04-11",
        //   },
        //   {
        //     id: 100013,
        //     name: "Test13",
        //     nickname: "T13",
        //     role: "Designer",
        //     sex: "1",
        //     sex2: ["1"],
        //     num1: 19,
        //     age: 34,
        //     address: "BeiJing",
        //     date12: "",
        //     date13: "2020-01-10",
        //   },
        // ]
        // console.log(this.dataList)
      })
    },
  },
}
</script>
<style lang="less">
.review-object2 {
  padding: 0px 20px;

  > header {
    padding: 12px 26px;
    font-size: 16px;
    font-weight: bold;
    position: relative;
    padding-left: 20px;
    &:before {
      position: absolute;
      content: "*";
      color: #fff;
      left: 4px;
      height: 22px;
      top: 9px;
      border-left: 4px solid #255ed7;
    }
  }
  .jw-table.is-panel {
    padding-top: 16px;
  }
}
.vxeTable {
}
</style>
