<!--
* @Author:<EMAIL>
* @Date: 2022/05/16 14:48:59
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/05/16 14:48:59
* @Description: 
-->
<template>
  <div class="submit-view">
    <!-- 基本信息 -->
    <EssentialInfo></EssentialInfo>
    <!-- 评审对象 -->
    <ReviewObject></ReviewObject>
    <!-- 表单 -->
    <!-- <FormTask :disabled="true"
              ref="LayoutForm"></FormTask> -->
    <!-- 处理意见 -->
    <HandComments :disabled="true"></HandComments>
    <!-- 流程历史 -->
    <ProcessHistory></ProcessHistory>
  </div>
</template>
<script>
import EssentialInfo from "../../components/essential-info";
import HandComments from "../../components/hand-comments";
import ReviewObject from "../../components/review-object.vue";
import ProcessHistory from "../../components/process-history";
// import FormTask from "../components/form-task.vue";
export default {
  name: "SubmitEdit",
  data() {
    return {};
  },
  components: {
    EssentialInfo,
    HandComments,
    ReviewObject,
    ProcessHistory,
    // FormTask,
  },
  created() {},
  methods: {},
};
</script>
<style lang="less" scoped>
.submit-view {
  height: 100%;
  overflow: auto;
}
</style>