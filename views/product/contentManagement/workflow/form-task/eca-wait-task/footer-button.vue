<!--
* @Author:<EMAIL>
* @Date: 2022/05/18 19:09:45
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/05/18 19:09:45
* @Description: 
-->
<template>
  <footer class="footer-button">
    <a-button
      type="primary"
      v-for="item in data.outgoingFlows"
      :loading="ability === item.id && saveLoading"
      @click="beforeHandClick(item)"
      :disabled="ability !== item.id && ability ? true : false"
      :key="item.id"
    >
      {{ item.name }}
    </a-button>
    <a-button
      @click="saveTask"
      :disabled="ability !== 'save' && ability ? true : false"
      :loading="ability === 'save' && saveLoading"
    >{{$t('btn_save')}}</a-button>
    <a-button
      @click="delegateShow"
      :loading="ability === 'delegate' && saveLoading"
      :disabled="ability !== 'delegate' && ability ? true : false"
    >{{$t('txt_task_assigned')}}</a-button>
    <jw-user-modal
      ref="user-modal"
      :isCheckbox="ischeckBox"
      :visible="isVisible"
      @handleSubmit="modalCallback"
      @closeUserModal="closeUserModal"
    />
  </footer>
</template>
<script>
import { jwUserModalV2 } from "jw_frame";
import { setDelegate, finishTask, saveTask, EventBus } from "../../units/api";
import { beforeHandClick } from "./api";
export default {
  name: "FooterButton",
  data() {
    return {
      saveLoading: false,
      ability: undefined,
      ischeckBox: false,
      isVisible: false,
      data: {},
    };
  },
  components: {
    jwUserModal: jwUserModalV2,
  },
  mounted() {
    this.data = EventBus.data || {};
  },
  methods: {
    closeUserModal() {},
    /**
     * 任务委派
     */
    modalCallback(user) {
      //先保存
      delete EventBus.data.variables;
      let params = {
        ...EventBus.data,
        taskId: this.$route.query.taskId,
      };
      this.saveLoading = true;
      this.ability = "delegate";
      saveTask(params)
        .then((res) => {
          //再委派
          setDelegate({
            consignee: user.account,
            taskId: this.$route.query.taskId,
          })
            .then((res) => {
              //如果执行成功,退回至上一个页面
              this.$router.push("./wait-tasks");
            })
            .catch((err) => {
              this.$error(err.msg || err.message);
            })
            .finally(() => {
              this.saveLoading = false;
              this.ability = undefined;
            });
        })
        .catch((err) => {
          this.$error(err.msg || err.message);
        })
        .finally(() => {});
    },
    delegateShow() {
      this.$refs["user-modal"]
        .show({
          type: "User",
        })
        .then((data) => {
          this.modalCallback(data);
        });
      this.isVisible = true;
    },
    beforeHandClick(item) {
      let { processInstanceId, processOrderType } = this.data;
      let params = { processInstanceId, processOrderType };
      beforeHandClick(params)
        .then((res) => {
          res && this.handClick(item);
        })
        .catch((err) => {
          this.$error(err.msg || err.message);
          this.saveLoading = false;
          this.ability = undefined;
        });
    },
    handClick(item) {
      //修复variables保存报错
      delete EventBus.data.variables;
      let params = {
        ...EventBus.data,
        selectRouting: EventBus.data.outgoingFlows.find(
          (p) => p.id === item.id
        ),
        taskId: this.$route.query.taskId,
      };
      this.saveLoading = true;
      this.ability = item.id;
      finishTask(params)
        .then((res) => {
          //如果执行成功,退回至上一个页面
          this.$router.push("./wait-tasks");
        })
        .catch((err) => {
          this.$error(err.msg || err.message);
        })
        .finally(() => {
          this.saveLoading = false;
          this.ability = undefined;
        });
    },
    /**
     * 保存
     */
    saveTask() {
      //修复variables保存报错
      delete EventBus.data.variables;
      let params = {
        ...EventBus.data,
        taskId: this.$route.query.taskId,
      };
      this.saveLoading = true;
      this.ability = "save";
      saveTask(params)
        .then((res) => {
          this.$success(this.$t("msg_save_success"));
          //刷新流程历史数据
          EventBus.$emit("FlowChartHistoryInit");
        })
        .catch((err) => {
          this.$error(err.msg || err.message);
        })
        .finally(() => {
          this.saveLoading = false;
          this.ability = undefined;
        });
    },
  },
};
</script>
<style lang="less" scoped>
.footer-button {
  text-align: center;
  padding: 16px;
  box-shadow: 0 -3px 6px 0 rgba(81, 81, 108, 0.07);
  button {
    margin-right: 8px;
  }
}
</style>