<template>
  <div class="eca-change-info">
    <a-collapse defaultActiveKey="eca-change-info" :bordered="false">
      <a-collapse-panel key="eca-change-info">
        <div slot="header">任务审核人员</div>
        <div class="eca-change-info-main">
          <div class="handler-item" v-for="item in variableList" :key="item.name">
            <div class="item-head">
              {{ item.displayName }}
              <i class="jwi-iconrefresh"></i>
            </div>
            <div class="item-body">
              <div class="handler-add-btn" v-if="!disabled" @click="onAddUser(item)">
                <i class="jwi-iconuser-add"></i>
              </div>
              <div class="handlers" v-show="!!item.users.length">
                <div class="handler" v-for="(val) in item.users" :key="val.account">
                  <i class="jwi-iconclose-circle-full close-icon" v-if="!disabled" @click="onDeleteUser(item.users, val)"></i>
                  <div class="avatar">
                    <img v-if="val.avatar" :src="val.avatar" alt="" />
                    <a-avatar v-else>{{ val.name | filterName }}</a-avatar>
                  </div>
                  <div>{{ val.name }}（{{ val.account }}）</div>
                </div>
              </div>
            </div>
          </div>

        </div>
      </a-collapse-panel>
    </a-collapse>
    <jw-user-modal-v2 ref="user-modal" :isCheckbox="ischeckBox" />
  </div>
</template>

<script>
import { EventBus } from "../../units/api";
import { jwUserModalV2 } from "jw_frame";
import ModelFactory from "jw_apis/model-factory";

// 获取需要配置的角色
const fetchVariables = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.workflowMicroServer
  }/workflow/repository/start/variables`,
  method: "get"
});

// 查询已部署的最新流程模板
const fetchDeployed = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.workflowMicroServer
  }/workflow/model/deployed/latest/fuzzy`,
  method: "get"
});

//自动填充人员
const findAutoRole = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/process/team/autoUser`,
  method: "post"
});

export default {
  components: {
    jwUserModalV2
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  filters: {
    filterName: function(name) {
      if (!name) {
        return name;
      }
      // 是否含有中文
      const hasCh = /[\u4E00-\u9FA5]/.test(name);
      let showName = "";
      if (hasCh) {
        // 用户 含有中文取后两个字符
        showName = name.slice(-2);
      } else {
        // 没有中文
        showName = name.slice(-2).toLocaleUpperCase();
      }
      return showName;
    }
  },
  data() {
    return {
      data: {},
      ischeckBox: true,
      deploymentId: null,
      variableList: []
    };
  },
  watch: {
    variableList: function(val) {
      this.data.variableList = val;
    }
  },
  mounted() {
    this.data = EventBus.data || {};
    this.fetchDeployedFun();
  },
  methods: {
    fetchDeployedFun() {
      fetchDeployed.execute({ searchKey: "" }).then(resp => {
        const { processModelId } = this.data.processOrderInfo;
        const model = resp.find(item => item.id === processModelId);
        this.deploymentId = model.deploymentId;
        this.getVariables();
      });
    },
    getVariables() {
      if (!this.deploymentId) {
        return;
      }
      fetchVariables
        .execute({
          deploymentId: this.deploymentId
        })
        .then(res => {
          this._startUser = Jw.getUser();
          this.variableList = res.map(item => {
            if (item.name == "owner") {
              item.users = [{ ...this._startUser }];
            } else {
              item.users = [];
            }

            return item;
          });
          this.findAutoRoleFun();
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },
    //自动填充角色
    findAutoRoleFun() {
      const { bizObjects } = this.data;
      findAutoRole.execute(bizObjects).then(resp => {
        this.variableList.forEach(row => {
          let role = resp.find(item => item.name === row.name);
          if (role && _.isEmpty(row.users)) {
            row.users = role.users;
          }
        });
      });
    },
    onAddUser(item) {
      this.$refs["user-modal"]
        .show({
          type: "User"
        })
        .then(data => {
          let oids = item.users.map(item => item.oid);
          data.forEach(val => {
            if (!oids.includes(val.oid)) {
              item.users.push(val);
            }
          });
        });
    },
    onDeleteUser(list, item) {
      let index = list.indexOf(item);
      list.splice(index, 1);
    }
  }
};
</script>

<style lang="less">
.handler-item {
  margin-bottom: 16px;
  margin-top: 10px;

  .item-head {
    margin-bottom: 8px;

    i {
      margin-left: 8px;
    }
  }

  .item-body {
    display: flex;
  }

  .handler-add-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    min-width: 32px;
    height: 32px;
    margin-right: 8px;
    background: #f0f7ff;
    border: 1px solid #a4c9fc;
    border-radius: 50%;
    cursor: pointer;
  }

  .handlers {
    display: flex;
    flex-wrap: wrap;
  }

  .handler {
    position: relative;
    display: flex;
    align-items: center;
    padding: 3px 8px;
    margin: 0 8px 8px 0;
    background: rgba(30, 32, 42, 0.04);
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;

    .close-icon {
      position: absolute;
      top: -13px;
      right: -8px;
      visibility: hidden;
    }

    .avatar {
      width: 24px;
      height: 24px;
      margin-right: 8px;

      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }

      .ant-avatar {
        width: 24px;
        height: 24px;
        line-height: 24px;
      }
    }

    &:hover {
      background: #f0f7ff;
      border: 1px solid #a4c9fc;

      .close-icon {
        visibility: visible;
      }
    }
  }
}

.eca-change-info {
  .ant-collapse {
    background-color: #fff;

    // border: 0px;
    .ant-collapse-item,
    .ant-collapse-content {
      border: 0px;
    }

    .ant-collapse-header {
      padding: 0 24px 16px !important;

      &:before {
        position: absolute;
        content: "*";
        color: #fff;
        border-left: 4px solid #255ed7;
      }

      > i.ant-collapse-arrow {
        right: 10px;
        width: 20px;
        left: inherit !important;
      }

      > div {
        font-size: 16px;
        margin-left: 15px;
        font-weight: bold;
      }
    }

    .ant-collapse-content > .ant-collapse-content-box {
      padding: 0px 24px 0px;
    }
  }

  .request-approval-main {
    border: 1px solid #a4c9fc;
    border-radius: 8px;
    padding: 15px;
    background-color: #f0f7ff;
  }
}
</style>