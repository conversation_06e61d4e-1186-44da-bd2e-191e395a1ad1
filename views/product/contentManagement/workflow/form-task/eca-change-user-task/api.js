
import ModelFactory from "jw_apis/model-factory";

/**
 * 变更前判断状态
 * @param {*} params 
 * @returns 
 */
 const beforeHandClick = function (params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.changeServer}/eca/workflow/checkBefore`,
        method: "post",
    }).execute(params)
}
/**
 * 更新用户人
 * @param {*} params 
 */
const updateUsers = function(params) {
    return ModelFactory.create({
        url: `${Jw.gateway}/${Jw.customerServer}/process/team/update`,
        method: 'post',
    }).execute(params)
}


export {
    beforeHandClick,
    updateUsers
}