
<template>
  <div class="submit-edit">
    <div>
      <!-- 基本信息 -->
      <EssentialInfo></EssentialInfo>
      <LayoutForm ref="ref_layout_form" :layoutName="layoutName" :modelName="modelName"></LayoutForm>
      <!-- 处理意见 -->
      <HandComments></HandComments>
      <!-- 流程历史 -->
      <ProcessHistory></ProcessHistory>
    </div>
    <!-- 动态按钮 -->
    <!-- <MaterialButton></MaterialButton> -->
  </div>
</template>
<script>
import EssentialInfo from "../../components/essential-info";
import LayoutForm from "./layout-form";
import HandComments from "../../components/hand-comments";
import ReviewObject from "../../components/review-object.vue";

import ProcessHistory from "../../components/process-history";
// import FormTask from "../components/form-task.vue";
import MaterialButton from "./material-apply-button";
export default {
  
  data() {
    return {
      layoutName: "create",
      modelName: "Material_Apply_Order"
    };
  },
  components: {
    LayoutForm,
    EssentialInfo,
    HandComments,
    ReviewObject,
    ProcessHistory,

    // FormTask,
    MaterialButton
  },
  created() {},

  methods: {
    getLayoutValue() {
      return this.$refs.ref_layout_form.getLayoutInfo();
    }
  }
};
</script>
<style lang="less" scoped>
.submit-edit {
  height: 100%;
  display: flex;
  flex-direction: column;
  > div:first-child {
    height: 20px;
    flex-grow: 1;
    overflow: auto;
  }
  > div:last-child {
  }
}
</style>