<!--
* @Author:<EMAIL>
* @Date: 2022/05/18 19:09:45
* @LastEditors:  <EMAIL>
* @LastEditTime: 2022/05/18 19:09:45
* @Description: 
-->
<template>
  <footer class="footer-button">
    <a-button type="primary" v-for="item in data.outgoingFlows" :loading="saveLoading" @click="handClick(item)" :key="item.id">
      {{ item.name }}
    </a-button>
    <a-button @click="saveTask" :loading="saveLoading">{{$t('btn_save')}}</a-button>
    <jw-user-modal ref="user-modal" :isCheckbox="ischeckBox" :visible="isVisible" @handleSubmit="modalCallback" @closeUserModal="closeUserModal" />
  </footer>
</template>
<script>
import { jwUserModalV2 } from "jw_frame";
import { setDelegate, finishTask, saveTask } from "../../units/api";
import { EventBus } from "../../units/api";

import ModelFactory from "jw_apis/model-factory";

// 创建part
const createPartApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/createThenUseOrder`,
  method: "post"
});

const findFolderApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/folder/findFolderByCLS`,
  method: "get"
});

const updateOrderPartApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.workflowMicroServer}/process/order/updateOrderPart`,
  method: "post"
});

export default {
  props: ["modelName"],
  data() {
    return {
      saveLoading: false,

      ischeckBox: false,
      isVisible: false,
      data: {}
    };
  },
  components: {
    jwUserModal: jwUserModalV2
  },
  mounted() {
    this.data = EventBus.data || {};
  },
  methods: {
    closeUserModal() {},
    /**
     * 任务委派
     */
    modalCallback(user) {
      //先保存
      delete EventBus.data.variables;
      let params = {
        ...EventBus.data,
        taskId: this.$route.query.taskId
      };
      this.saveLoading = true;

      saveTask(params)
        .then(res => {
          //再委派
          setDelegate({
            consignee: user.account,
            taskId: this.$route.query.taskId
          })
            .then(res => {
              //如果执行成功,退回至上一个页面
              this.$router.push("./wait-tasks");
            })
            .catch(err => {
              this.$error(err.msg || err.message);
            })
            .finally(() => {
              this.saveLoading = false;
            });
        })
        .catch(err => {
          this.$error(err.msg || err.message);
        })
        .finally(() => {});
    },
    delegateShow() {
      this.$refs["user-modal"]
        .show({
          type: "User"
        })
        .then(data => {
          this.modalCallback(data);
        });
      this.isVisible = true;
    },
    async handClick(item) {
      let taskData = EventBus.data;

      let processType = taskData.processDefinitionId.split(":")[0];

      //修复variables保存报错
      delete EventBus.data.variables;

      let selectRouting = EventBus.data.outgoingFlows.find(
        p => p.id === item.id
      );
      let params = {
        ...EventBus.data,
        selectRouting,
        taskId: this.$route.query.taskId
      };

      let instanceData = await this.$parent.getLayoutValue();
      // if (taskData.name == "产品经理审核") {
      //   let { classificationInfo } = instanceData;
      //   if (!classificationInfo) {
      //     return this.$error("请选择分类信息");
      //   }
      // }

      await this.updateOrderPartInfo(instanceData);
      this.saveLoading = true;
      finishTask(params)
        .then(res => {
          //如果执行成功,退回至上一个页面
          // if (taskData.name == "产品经理审核") {
          //   this.createPart(instanceData);
          // }
        })
        .then(res => {
          this.$router.push("./wait-tasks");
        })
        .catch(err => {
          this.$error(err.msg || err.message);
        })
        .finally(() => {
          this.saveLoading = false;
        });
    },

    updateOrderPartInfo(instanceData) {
      let { classificationInfo } = instanceData;
      if (classificationInfo) {
        classificationInfo.children = null;
      }

      return updateOrderPartApi.execute(instanceData).catch(err => {
        this.$error(err.msg);
        throw new Error(err.msg);
      });
    },
    findFolder(instanceData) {
      let { classificationInfo } = instanceData;
      return findFolderApi
        .execute({
          clsOid: classificationInfo.oid,
          containerOid: instanceData.containerOid
        })
        .catch(err => {
          this.saveLoading = false;
          this.$error(err.msg);
        });
    },

    async createPart(instanceData) {
      let folder = await this.findFolder(instanceData);
      let taskData = EventBus.data;
      instanceData.locationInfo = {
        catalogOid: folder.oid,
        catalogType: folder.type,
        containerOid: instanceData.containerOid,
        containerType: instanceData.containerType,
        containerModelDefinition: folder.containerModelDefinition
      };

      let {
        name,
        source,
        preferenceLevel,
        locationInfo,
        classificationInfo,
        defaultUnit,
        extensionContent,
        levelForSecrecy,
        datasheet,
        schematic,
        symbol
      } = instanceData;

      createPartApi.execute({
        modelDefinition: "ElectricalPart",
        name,
        source,
        preferenceLevel,
        locationInfo,
        classificationInfo,
        defaultUnit,
        extensionContent,
        levelForSecrecy,
        datasheet,
        schematic,
        symbol,
        targetOid: taskData.processOrderOid
      });
    },

    /**
     * 保存
     */
    saveTask() {
      //修复variables保存报错
      delete EventBus.data.variables;
      let params = {
        ...EventBus.data,
        taskId: this.$route.query.taskId
      };
      this.saveLoading = true;

      saveTask(params)
        .then(res => {
          this.$success(this.$t("msg_save_success"));
          //刷新流程历史数据
          EventBus.$emit("FlowChartHistoryInit");
        })
        .catch(err => {
          this.$error(err.msg || err.message);
        })
        .finally(() => {
          this.saveLoading = false;
        });
    }
  }
};
</script>
<style lang="less" scoped>
.footer-button {
  text-align: center;
  padding: 16px;
  box-shadow: 0 -3px 6px 0 rgba(81, 81, 108, 0.07);
  button {
    margin-right: 8px;
  }
}
</style>