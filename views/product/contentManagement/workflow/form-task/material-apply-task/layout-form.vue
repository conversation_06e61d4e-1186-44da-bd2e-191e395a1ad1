<template>
  <div class="essential-info">
    <a-collapse defaultActiveKey="essential-info" :bordered="false">
      <a-collapse-panel key="essential-info">
        <div slot="header">流程申请单信息</div>
        <div class="essential-info-main">
          <jw-layout-builder
            ref="ref_appBuilder"
            type="Model"
            :instanceData="instanceData"
            :layoutName="layoutName"
            :modelName="modelName"
            @initModel="onInitModel"
          >
            <template #areMaterialsSlots="{ formData, onUpdate }">
              <div class="materials-info">
                <div class="title">相似物料</div>
              </div>
              <jw-table
                ref="refTable"
                :maxHeight="500"
                :panel="true"
                :tooltip-config="{}"
                :columns="columns"
                :showPage="false"
                :data-source.sync="areMaterialsList"
              >
                <template #numberSlot="{ row }">
                  <span
                    style="color: #255ed7; cursor: pointer"
                    @click="routerLink(row)"
                  >
                    <jw-icon :type="row.modelIcon" /> {{ row.number }}
                    <a-tag
                      v-if="row.primary"
                      color="blue"
                      style="margin-left: 8px"
                    >
                      {{ $t("txt_object_main") }}
                    </a-tag>
                  </span>
                </template>
                <template #updateDate="{ row }">
                  <span style="color: #255ed7">
                    {{ row.updateDate | formatDateFn }}
                  </span>
                </template>
                <template #isLock="{ row }">
                  <a-tooltip>
                    <template slot="title">
                      {{ row.lockOwnerAccount }} {{ $t("txt_check_out") }}
                    </template>
                    <jw-icon
                      v-if="row.lockOwnerOid"
                      :type="
                        row.lockOwnerOid === jwUser.oid
                          ? '#jwi-beiwojianchu'
                          : '#jwi-bierenjianchu'
                      "
                    />
                  </a-tooltip>
                </template>
                <template #loadStatus="{ row }">
                  <jw-icon v-if="!row.loadStatus" type="#jwi-liuchengzhong" />
                </template>
                <template #versionSlot="{ row }">
                  <span>
                    {{ row.displayVersion }}
                  </span>
                </template>
                <template #operation="{ row }">
                  <div>
                    <a-tooltip title="对比">
                      <span
                        @click="gethistorydata(row)"
                        style="cursor: pointer"
                      >
                        <jw-icon type="jwi-iconreturn"> </jw-icon>
                      </span>
                    </a-tooltip>
                  </div>
                </template>
              </jw-table>
            </template>
          </jw-layout-builder>
        </div>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>
<script>
import { jwLayoutBuilder } from "jw_frame"
import { EventBus } from "../../units/api"

import ModelFactory from "jw_apis/model-factory"

//
const findClsApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/folder/findClsByContainer`,
  method: "get",
})

// 判断物料去重
const findSamePartData = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/findSamePartData`,
  method: "post",
})
export default {
  props: ["layoutName", "modelName"],
  components: {
    jwLayoutBuilder,
  },
  data() {
    return {
      // instanceData: {},
      instanceDataNew: {},
      areMaterialsList: [],
    }
  },
  computed: {
    instanceData() {
      if (EventBus.data?.processOrderInfo) {
        this.instanceDataNew = EventBus.data?.processOrderInfo

        if (
          this.instanceDataNew.extensionContent &&
          this.instanceDataNew.extensionContent.thumbnailFile
        ) {
          this.instanceDataNew.extensionContent.thumbnailFile =
            this.instanceDataNew.extensionContent.thumbnailFile.map((item) => {
              return {
                oid: item.oid,
                name: item.name,
              }
            })
        }
      }
      // findSamePartData.execute(EventBus.data?.processOrderInfo).then((res) => {
      //   if (res?.length != 0) {
      //     this.areMaterialsList = res
      //   } else {
      //     this.areMaterialsList = []
      //   }
      // })
      return this.instanceDataNew || {}
    },

    columns() {
      return [
        {
          field: "number",
          title: this.$t("txt_number_of"),
          sortable: true,
          minWidth: 180,
          slots: {
            default: "numberSlot",
          },
        },
        {
          field: "name",
          title: this.$t("txt_name"),
          sortable: true,
        },
        {
          field: "modelDefinition",
          title: this.$t("txt_type"),
          sortable: true,
        },
        {
          field: "lifecycleStatus",
          title: this.$t("txt_plan_lifecycle"),
          sortable: true,
        },
        {
          field: "displayVersion",
          title: this.$t("txt_version"),
          sortable: true,
          slots: {
            default: "versionSlot",
          },
        },
        {
          field: "updateDate",
          title: this.$t("txt_update_date"),
          sortable: true, // 开启排序
          slots: {
            default: "updateDate",
          },
        },
      ]
    },
  },

  created() {},

  // mounted() {
  //   this.instanceData = EventBus.data?.processOrderInfo || {};
  // },
  methods: {
    findCls(clsCom) {
      let order = EventBus.data?.processOrderInfo || {}
      findClsApi
        .execute({ containerOid: order.containerOid })
        .then((res) => {
          let clsOid = res?.clsOid
          clsCom.show(res.clsOid)
        })
        .catch(() => {
          clsCom.show()
        })
    },
    onInitModel({ layout }) {
      let rows = layout?.content?.layout
      rows.forEach((row) => {
        row.forEach((item) => {
          if (item.fieldName == "classificationInfo") {
            item.callback = (clsCom) => {
              this.findCls(clsCom)
            }
          }
        })
      })
    },
    getLayoutInfo() {
      let appBuilder = this.$refs.ref_appBuilder
      return appBuilder.validate().then(() => {
        return appBuilder.getValue()
      })
    },
  },
}
</script>
<style lang="less">
.essential-info {
  .ant-collapse {
    background-color: #fff;
    // border: 0px;
    .ant-collapse-item,
    .ant-collapse-content {
      border: 0px;
    }
    .ant-collapse-header {
      padding: 0 24px 16px !important;
      &:before {
        position: absolute;
        content: "*";
        color: #fff;
        border-left: 4px solid #255ed7;
      }
      > i.ant-collapse-arrow {
        right: 10px;
        width: 20px;
        left: inherit !important;
      }
      > div {
        font-size: 16px;
        margin-left: 15px;
        font-weight: bold;
      }
    }
    .ant-collapse-content > .ant-collapse-content-box {
      padding: 0px 24px 0px;
    }
  }
  .request-approval-main {
    border: 1px solid #a4c9fc;
    border-radius: 8px;
    padding: 15px;
    background-color: #f0f7ff;
  }
  .materials-info {
    // text-indent: 24px;
    &:before {
      position: absolute;
      content: "*";
      color: #fff;
      border-left: 4px solid #255ed7;
    }

    .title {
      font-size: 16px;
      margin-left: 15px;
      font-weight: bold;
    }
  }
}
</style>
