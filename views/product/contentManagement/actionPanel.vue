<template>
    <div>
        <div class="content-action flex justify-between align-center">
        <div class="flex">
            <template v-for="item in actionBtnList">
                <a-dropdown :key="item.icon" @visibleChange="((visible)=>{visibleChange(visible, item)})">
                    <a-button :class="[item.visible?'header-text-btn':'header-little-btn']" @click="handleCreate">
                        <div class="flex justify-center align-center">
                            <svg class="jwifont" aria-hidden="true">
                                <use :xlink:href="item.icon"></use>
                            </svg>
                            <span v-if="item.visible">{{item.text}}</span>
                        </div>
                    </a-button>
                    <a-menu slot="overlay" v-if="item.icon==='#jwi-document'">
                        <a-menu-item v-for="item of docList" :key="item.name">{{item.actionLabel}}</a-menu-item>
                    </a-menu>
                </a-dropdown>
            </template>
            <a-input-search class="header-input" placeholder="请输入关键词搜索" @search="onSearch" />
        </div>
        <div class="flex">
              <a-dropdown  class="header-batch-btn">
                <a-menu slot="overlay" @click="handleMenuClick">
                   <a-menu-item v-for="item of batchList" :key="item.name">{{item.actionLabel}}</a-menu-item>
                </a-menu>
                <a-button style="margin-left: 8px"> 批量操作<a-icon type="down" /> </a-button>
            </a-dropdown>
         
            <a-button class="header-view-btn" @click="switchView">
                <svg class="jwifont" aria-hidden="true" style="margin-right:0">
                    <use :xlink:href="isList?'#jwi-appstore':'#jwi-list'"></use>
                </svg>
            </a-button>
        </div>
        </div>

        <create-template :visible="visible" @close="onClose"></create-template>

        <addedToBase :visibleBase="visibleBase" @closed="closedBase"></addedToBase>
     
       <a-drawer
                placement="right"
                :closable="false"
                :visible="visiblewofkflow"
                class="model"
                width="840"
                @close="onCloseDrawer"
                >
              <startWorkFlow @closed="closed"></startWorkFlow>
       </a-drawer>

    </div>
</template>

<script>
import createTemplate from './createTemplate';
import addedToBase from '../addedToBase'
import startWorkFlow from './startWorkFlow'
export default {
    name: 'contentAction',
    props: [
        'isList',
        'selectData'
    ],
    components: {
        createTemplate,
        addedToBase,
        startWorkFlow
    },
    data() {
        return {
            visiblewofkflow: false,
            visibleBase: false,
            actionBtnList: [
                {icon: '#jwi-unfolder', text: '文件夹', visible: false},
                {icon: '#jwi-part', text: '零件', visible: false},
                {icon: '#jwi-end-product', text: '成品', visible: false},
                {icon: '#jwi-unproduct', text: '半成品', visible: false},
                {icon: '#jwi-document', text: '文档', visible: false},
            ],
            docList: [
                {name: 'document', actionLabel: '文档'},
                {name: 'designDocument', actionLabel: '设计文档'},
                {name: 'projectDocument', actionLabel: '项目文档'},
                {name: 'detailedDesign', actionLabel: '详细设计'},
                {name: 'projectApplication', actionLabel: '项目申请表'},
            ],
            batchList: [
                {name: 'move', actionLabel: '移动'},
                {name: 'checkOut', actionLabel: '检出'},
                {name: 'workflow', actionLabel: '工作流启动'},
                {name: 'addTo', actionLabel: '添加至基线'},
                {name: 'delete', actionLabel: '删除'},
            ],
            visible: false,
        }
    },
    methods: {
        onSearch(val) {
          console.log('val', val)
        },

        onCloseDrawer(){
            this.visiblewofkflow = false
        },
        
        closedBase(flag){
             this.visibleBase = flag
        },
        closed(){
             this.visiblewofkflow = false
        },
        deletelistData(){  //删除数据
            console.log('this.selectData',this.selectData)
            if(this.selectData.length >0) {
                this.$emit('deleteData',this.selectData)
            }else {
                this.$warning('至少选择一条数据!')
            }
        },
        checkOutList(){  //检出数据
              if(this.selectData.length >0) {
                this.$emit('checkOutData',this.selectData)
            }else {
                this.$warning('至少选择一条数据!')
            }
        },
        moveData(){  //移动文件夹
              if(this.selectData.length >0) {
                this.$emit('moveFileData',this.selectData)
            }else {
                this.$warning('至少选择一条数据!')
            }
        },
        handleMenuClick(val){
           
           switch(val.key) {
               case 'move':
               this.moveData();
               break;
               case 'checkOut': //检出
               this.checkOutList();
               break;
               case 'workflow':
                this.visiblewofkflow = true;
               break;
               case 'addTo':
               this.visibleBase = true;
               break;
               case 'delete':
               this.deletelistData();
               break;
           }
        },
     
        switchView() {
            this.$emit('changeView');
        },
        visibleChange(visible, item) {
            if (visible) {
                item.visible = true;
            } else {
                item.visible = false;
            }
        },
        handleCreate() {
            this.visible = true;
        },
        onClose() {
            this.visible = false;
        },
    },
    mounted() {

    },
}
</script>

<style lang="less" scoped>

.content-action {
    height: 52px;
    padding: 0 20px;
    border-bottom: 1px solid rgba(30,32,42,0.15);
    .jwifont {
        width: 16px;
        height: 16px;
    }
    .header-text-btn {
        margin-right: 6px;
        padding: 0 8px;
        background-color: #f8f8f8;
        .jwifont {
            margin-right: 5px;
        }
    }
    .header-little-btn {
        width: 32px;
        margin-right: 8px;
        padding: 0;
    }
    .header-input {
        width: 216px;
    }
    .header-batch-btn {
        width: 96px;
        padding: 0;
    }
    .header-view-btn {
        width: 32px;
        line-height: 35px;
        margin-left: 8px;
        padding: 0;
    }
}
</style>
