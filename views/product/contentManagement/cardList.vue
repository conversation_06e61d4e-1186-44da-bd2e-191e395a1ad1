<template>
    <div class="content-card" :style="{ height: fullHeight - 54 + 'px'}">
        <div class="card-list-wrap folder-wrap">
            <div class="card-list-title">文件夹
                <span class="card-total">（{{folderTotal}}个文件夹）</span>
            </div>
            <a-checkbox-group @change="onChangeFile" class="card-checkbox-group">
                <a-row :gutter="[16, 16]">
                    <a-col :xs="8" :sm="8" :md="8" :lg="8" :xl="6" :xxl="5"
                        v-for="item of folderData" :key="item.id">
                        <div class="card-wrap">
                            <div class="folder-header">
                                <div v-if="item.children" class="folder-has-content"></div>
                            </div>
                            <div class="card-header flex align-center">
                                <a-checkbox :value="item.id"></a-checkbox>
                                <div class="card-title">{{item.code}}</div>
                            </div>
                            <div class="card-body flex justify-between align-center">
                                <div class="flex justify-between">
                                    <div class="body-icon flex">
                                        <svg class="jwifont" aria-hidden="true">
                                            <use xlink:href="#jwi-unfolder"></use>
                                        </svg>
                                    </div>
                                    <div class="flex flex-column justify-around">
                                        <div class="info-type">{{ item.type }}</div>
                                        <div class="info-time">{{ item.createTime }}</div>
                                    </div>
                                </div>
                                <div class="body-right">
                                    <div v-if="item.status" class="info-status">{{item.status}}</div>
                                </div>
                            </div>
                        </div>
                    </a-col>
                </a-row>
            </a-checkbox-group>
        </div>
        <div class="card-list-wrap file-wrap">
            <div class="card-list-title">文件对象
                <span class="card-total">（{{fileTotal}}个对象）</span>
            </div>
            <a-checkbox-group @change="onChangeFile" class="card-checkbox-group">
                <a-row :gutter="[16, 16]">
                    <a-col :xs="8" :sm="8" :md="8" :lg="8" :xl="6" :xxl="5"
                        v-for="item of fileData" :key="item.id">
                        <div class="card-wrap">
                            <div class="card-header flex align-center">
                                <a-checkbox :value="item.id"></a-checkbox>
                                <div class="card-title">{{item.code}}</div>
                            </div>
                            <div class="card-body flex justify-between align-center">
                                <div class="flex justify-between">
                                    <div class="body-icon flex">
                                        <svg class="jwifont" aria-hidden="true">
                                            <use :xlink:href="item.type==='文件夹'?
                                                '#jwi-unfolder':item.type==='零件'?
                                                '#jwi-part':item.type==='成品'?
                                                '#jwi-end-product':item.type==='半成品'?
                                                '#jwi-unproduct':item.type==='文档'?
                                                '#jwi-document':item.type==='装配图纸'?
                                                '#jwi-assembly':item.type==='零件图纸'?
                                                '#jwi-part-drawing':'#jwi-unfolder'">
                                            </use>
                                        </svg>
                                    </div>
                                    <div class="flex flex-column justify-around">
                                        <div class="info-type">{{ item.type }}</div>
                                        <div class="info-time">{{ item.createTime }}</div>
                                    </div>
                                </div>
                                <div class="body-right">
                                    <div v-if="item.status" class="info-status">{{item.status}}</div>
                                </div>
                            </div>
                        </div>
                    </a-col>
                </a-row>
            </a-checkbox-group>
        </div>
    </div>
</template>

<script>
export default {
    name: 'contentTable',
    props: [
        'fullHeight',
    ],
    data(){
		return {
            folderTotal: 0,
			folderData: [],
            fileTotal: 0,
            fileData: [],
		}
    },
	computed: {

  	},
	methods: {
		fetchCardData(){
			this.fileData = [
                {
                    id: 1,
                    code: 'AJ001231',
                    type: '零件',
                    createTime:'2021/07/31',
                    status: '被检出',
                },
                {
                    id: 2,
                    code: 'AJ001231',
                    type: '成品',
                    createTime:'2021/07/31',
                    status: '',
                },
                {
                    id: 3,
                    code: 'AJ001231',
                    type: '成品',
                    createTime:'2021/07/31',
                    status: '',
                },
                {
                    id: 4,
                    code: 'AJ001231',
                    type: '半成品',
                    createTime:'2021/07/31',
                    status: '',
                },
                {
                    id: 5,
                    code: 'AJ001231',
                    type: '文档',
                    createTime:'2021/07/31',
                    status: '他人检出',
                },
                {
                    id: 6,
                    code: 'AJ001231',
                    type: '零件',
                    createTime:'2021/07/31',
                    status: '',
                },
                {
                    id: 7,
                    code: 'AJ001231',
                    type: '成品',
                    createTime:'2021/07/31',
                    status: '',
                },
                {
                    id: 8,
                    code: 'AJ001231',
                    type: '装配图纸',
                    createTime:'2021/07/31',
                    status: '',
                },
                {
                    id: 9,
                    code: 'AJ001231',
                    type: '文档',
                    createTime:'2021/07/31',
                    status: '',
                },
                {
                    id: 10,
                    code: 'AJ001231',
                    type: '零件图纸',
                    createTime:'2021/07/31',
                    status: '',
                },
            ];
            this.fileTotal = 10;
            this.folderData = [
                {
                    id: 1,
                    code: 'AJ001231',
                    type: '文件夹1',
                    createTime:'2021/07/31',
                    status: '',
                    children: true,
                },
                {
                    id: 2,
                    code: 'AJ001231',
                    type: '文件夹2',
                    createTime:'2021/07/31',
                    status: '',
                    children: false,
                },
                {
                    id: 3,
                    code: 'AJ001231',
                    type: '文件夹3',
                    createTime:'2021/07/31',
                    status: '',
                    children: false,
                },
                {
                    id: 4,
                    code: 'AJ001231',
                    type: '文件夹4',
                    createTime:'2021/07/31',
                    status: '',
                    children: false,
                },
            ];
            this.folderTotal = 4;
		},
        onChangeFile(val) {
            console.log(val)
        },
  	},
	mounted() {
		this.fetchCardData();
	},
}
</script>

<style lang="less" scoped>
@media (min-width: 1600px) {
    .ant-col-xxl-5 {
        width: 20%;
    }
}
.content-card {
    padding: 16px 20px;
    overflow-y: auto;
    &::-webkit-scrollbar{
        width: 2px;
    }
	.jwifont {
		width: 42px;
		min-width: 42px;
		height: 42px;
		min-height: 42px;
		margin-right: 12px;
	}
    .card-list-wrap {
        margin-bottom: 20px;
        .card-list-title {
            margin-bottom: 16px;
            color: #1e202a;
            .card-total {
                color: rgba(30, 32, 42, 0.45);
            }
        }
    }
    .card-checkbox-group {
        width: 100%;
    }
    .card-wrap {
        border: 1px solid rgba(30, 32, 42, 0.06);
        box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
        border-radius: 4px;
        cursor: pointer;
        &:hover {
            border: 1px solid #3282fa;
        }
        .card-header {
            height: 40px;
            line-height: 40px;
            padding: 0 16px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            .card-title {
                margin-left: 8px;
                color: #1e202a;
                font-weight: 700;
            }
        }
        .card-body {
            padding: 16px;
            .info-type {
                color: rgba(30, 32, 42, 0.85);
            }
            .info-time {
                font-size: 12px;
                color: rgba(30, 32, 42, 0.45);
            }
            .info-status {
                padding: 1px 8px;
                font-size: 12px;
                color: #f34f63;
                background: #fff1F0;
                border: 1px solid #ffc2c3;
                border-radius: 4px;
                white-space: nowrap;
            }
        }
    }
    .folder-wrap {
        .card-wrap {
            position: relative;
            background: #a4c9fc;
            &:hover {
                border: 1px solid #3282fa;
            }
            .folder-header {
                position: absolute;
                top: 0;
                right: 0;
                width: 57%;
                border-width: 27px 0 0 27px;
                border-style: solid none none solid;
                border-color: #255ed7 transparent transparent transparent;
                border-top-right-radius: 4px;;
            }
            .folder-has-content {
                position: absolute;
                top: -13px;
                right: 13px;
                width: 100%;
                border-width: 13px 0 0 13px;
                border-style: solid none none solid;
                border-color: #fff transparent transparent transparent;
                border-top-right-radius: 4px;
            }
        }
    }
    .file-wrap {
        .card-header {
            background: rgba(30, 32, 42, 0.06);
        }
    }
}
</style>
