<template>
    <div>
        <div class="content-management" v-if="isContent">
            <action-panel :isList="isList" @changeView="changeView" :selectData="selectData" @moveFileData="moveFileData" @checkOutData="checkOutData" @deleteData="deleteData"></action-panel>
            <table-list v-if="isList" :fullHeight="fullHeight" @showDetail="showDetail" :tableData="tableData" @getSelect="getSelect"></table-list>
            <card-list v-else :fullHeight="fullHeight"></card-list>
        </div>
        <content-detail
            v-else
            :fullHeight="fullHeight"
            :backTitle="backTitle"
            @back="goBack"
        ></content-detail>
	    <move-file :visible="visible" @closed="closed"></move-file>
	</div>
</template>

<script>
import actionPanel from './actionPanel';
import tableList from './tableList';
import cardList from './cardList';
import contentDetail from './details/contentDetail';
import moveFile from './moveFile';
export default {
    name: 'contentManagement',
    components: {
        actionPanel,
        tableList,
        cardList,
        contentDetail,
		moveFile,
    },
    inject: ['setBreadcrumb', 'addBreadcrumb'],
    data() {
        return {
			visible: false,
            isList: true,
            isContent: true,
            backTitle: '',
            fullHeight: document.documentElement.clientHeight - 104,
            selectData:[],
            tableData: [
				{
					id:'1',
					name:'文件夹1',
					code: 'AJ001231',
					type:'文件夹',
					version:'A.2',
					view:'Design',
					lifecycle:'-',
					status: '-',
					createTime:'2021-07-31 13:14:15',
				},
				{
					id:'2',
					name:'文件夹2',
					code: 'AJ001232',
					type:'文件夹',
				    version:'A.2',
					view:'Design',
					lifecycle:'-',
					status: '-',
					createTime:'2021-07-31 13:14',
				},
				{
					id:'3',
					name:'文件夹3',
					code: 'AJ001233',
					type:'文件夹',
					version:'A.2',
					view:'Design',
					lifecycle:'MFSD112O13',
					status: '-',
					createTime:'2021-07-31 13:14',
				},
				{
					id:'4',
					name:'文件夹4',
					code: 'AJ001234',
					type:'文件夹',
				    version:'A.2',
					view:'Design',
					lifecycle:'MFSD112O13',
					status: '-',
					createTime:'2021-07-31 13:14',
				},
				{
					id:'5',
					name:'文件夹5',
					code: 'AJ001235',
					type:'文件夹',
					version:'-',
					view:'-',
					lifecycle:'-',
					status: '-',
					createTime:'2021-07-31 13:14',
				},
				{
					id:'6',
					name:'文件夹6',
					code: 'AJ001236',
					type:'文件夹',
					version:'-',
					view:'-',
					lifecycle:'-',
					status: '-',
					createTime:'2021-07-31 13:14',
				},
				{
					id:'7',
					name:'文件夹7',
					code: 'AJ001237',
					type:'文件夹',
					version:'-',
					view:'-',
					lifecycle:'-',
					status: '-',
					createTime:'2021-07-31 13:14',
				},
				{
					id:'8',
					name:'文件夹8',
					code: 'AJ001238',
					type:'文件夹',
					version:'-',
					view:'-',
					lifecycle:'-',
					status: '-',
					createTime:'2021-07-31 13:14',
				},
				{
					id:'9',
					name:'产品文档',
					code: 'AJ001239',
					type:'文档',
					version:'A.1',
					view:'-',
					lifecycle:'-',
					status: '1',
					createTime:'2021-07-31 13:14',
				},
				{
					id:'10',
					name:'风扇001',
					code: 'AJ001230',
					type:'零件',
					version:'A.2',
					view:'-',
					lifecycle:'-',
					status: '2',
					createTime:'2021-07-31 13:14',
				},
			],
        }
    },
    methods: {
        deleteData(selectData){
            this.tableData = this.tableData.filter(item => {
                return !selectData.find(row => {
                    return row == item.id;
                })
            })
        },
		checkOutData(selectData){
            selectData.map(item => {
                item.status = '1';
            })
		},
		closed(){
            this.visible = false
		},
		moveFileData(){
            this.visible = true;
		},
        initBreadcrumb() {
            let breadcrumbData = [{ name: '产品库', path: '/product' }];
            this.setBreadcrumb(breadcrumbData);
            this.addBreadcrumb(
                {
                    name: '内容管理',
                    children: [
                        { id: '1', title: '内容管理', link: `/contentManage/${this.$route.params.oid}/${this.$route.params.type}` }, 
                        { id: '2', title: '产品结构', link: `/productStructure/${this.$route.params.oid}/${this.$route.params.type}` }, 
                        { id: '3', title: '基线管理', link: `/baseline/${this.$route.params.oid}/${this.$route.params.type}` },
                        { id: '4', title: '变更管理', link: `/changeManage/${this.$route.params.oid}/${this.$route.params.type}` },
                    ]
                },
            );
        },
        changeView() {
            this.isList = !this.isList;
        },
        showDetail(row) {
            this.isContent = false;
            this.backTitle = row.name;
        },
        goBack() {
            this.isContent = true;
        },
        getSelect(selectData){
            this.selectData = selectData
        }
    },
    mounted() {
        window.onresize = () => {
            return (() => {
                this.fullHeight = document.documentElement.clientHeight - 104;
            })();
        }
    },
    created() {
        this.initBreadcrumb();
    },
}
</script>

<style lang="less" scoped>
.content-management {
    margin: 5px;
    background: var(--light);
    box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
    border-radius: 4px;
}
</style>
