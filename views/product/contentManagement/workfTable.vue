<template>
  <div>
    <div :style="{ height: fullHeight - 46 + 'px' }" class="maintable">
      <a-table
        :columns="columns"
        :pagination="false"
        rowKey="id"
        :scroll="{ y: fullHeight - 100 + 'px' }"
        :data-source="dataArr"
        :row-selection="rowSelection"
        @change="handleChange"
      >
      </a-table>
    </div>

    <div class="next-btn">
      <a-button class="cancer" @click="prevBtn">{{$t('btn_pre_step')}}</a-button>
      <a-button class="next" type="primary" @click="nextBtn">{{$t('btn_next_step')}}</a-button>
    </div>
  </div>
</template>

<script>
import { searchModelList } from "apis/product/contentManage";
export default {
  name: "workTable",
  props: ["fullHeight"],
  data() {
    let _this = this;
    return {
      filteredInfo: null,
      sortedInfo: null,
      dataArr: [],
      rowSelection:{
        onSelect:_this.onTableSelect,
        onSelectAll:_this.onTableSelectAll,
        type:'radio'
      },
      selectData:[]
    };
  },

  methods: {
    fetchSearchWork() {
      searchModelList.execute({
        start:0,
        size:50,
        tenantId:'pdm',
        nameLike:''
      }).then(res=>{
        this.dataArr = res.data
      }).catch(err=>{
        if (err.msg) {
          this.$error(err.msg)
        }
      })
      //  searchWorkListApi
      // .execute()
      // .then(data => {
      //     this.$success('工作流启动成功！')
      // })
      // .catch(err => {
      //  if(err.msg) {
      //    this.$error(err.msg)
      //  }
      // });
    },
    prevBtn() {
      this.$emit("Step", 0);
    },
    nextBtn() {
      if(this.selectData.length < 1){
        return this.$warning('请至少选择一条工作流！')
      }
      this.$emit("Step", 2);
      this.$emit('onGetModelKey',this.selectData)
    },
    handleChange(pagination, filters, sorter) {
      console.log("pagination", pagination);
      this.filteredInfo = filters;
      this.sortedInfo = sorter;
    },

    onTableSelect(record, selected, selectedRows){
      this.selectData = selectedRows;
    },

    onTableSelectAll(selected, selectedRows, changeRows){
      this.selectData = selectedRows;
    },
  },
  computed: {
    columns() {
      let { sortedInfo, filteredInfo } = this;
      sortedInfo = sortedInfo || {};
      filteredInfo = filteredInfo || {};
      const columns = [
        {
          title: "模板ID",
          dataIndex: "id",
          key: "id",
          // filters: [
          //   { text: "Joe", value: "Joe" },
          //   { text: "Jim", value: "Jim" },
          // ],
          // filteredValue: filteredInfo.name || null,
          // onFilter: (value, record) => record.name.includes(value),
          // sorter: (a, b) => a.name.length - b.name.length,
          // sortOrder: sortedInfo.columnKey === "modelid" && sortedInfo.order,
          ellipsis: true,
          scopedSlots: { customRender: "modelid" },
        },
        {
          title: "模板名称",
          dataIndex: "name",
          key: "name",
          // filters: [
          //   { text: "Joe", value: "Joe" },
          //   { text: "Jim", value: "Jim" },
          // ],
          // filteredValue: filteredInfo.name || null,
          // onFilter: (value, record) => record.name.includes(value),
          // sorter: (a, b) => a.name.length - b.name.length,
          // sortOrder: sortedInfo.columnKey === "modelname" && sortedInfo.order,
          ellipsis: true,
        },
        {
          title: "流程唯一标识",
          dataIndex: "key",
          key: "key",
          // filters: [
          //   { text: "London", value: "London" },
          //   { text: "New York", value: "New York" },
          // ],
          // filteredValue: filteredInfo.address || null,
          // onFilter: (value, record) => record.address.includes(value),
          // sorter: (a, b) => a.address.length - b.address.length,
          // sortOrder: sortedInfo.columnKey === "uniqueidentifier" && sortedInfo.order,
          ellipsis: true,
        },
        {
          title: "所属应用",
          dataIndex: "tenantId",
          key: "tenantId",
          // filters: [
          //   { text: "London", value: "London" },
          //   { text: "New York", value: "New York" },
          // ],
          // filteredValue: filteredInfo.address || null,
          // onFilter: (value, record) => record.address.includes(value),
          // sorter: (a, b) => a.address.length - b.address.length,
          // sortOrder:
          //   sortedInfo.columnKey === "creationtime" && sortedInfo.order,
          ellipsis: true,
        },
        {
          title: "分类",
          dataIndex: "category",
          key: "category",
          // filters: [
          //   { text: "London", value: "London" },
          //   { text: "New York", value: "New York" },
          // ],
          // filteredValue: filteredInfo.address || null,
          // onFilter: (value, record) => record.address.includes(value),
          // sorter: (a, b) => a.address.length - b.address.length,
          // sortOrder: sortedInfo.columnKey === "category" && sortedInfo.order,
          ellipsis: true,
        },
        {
          title: this.$t('txt_version'),
          dataIndex: "version",
          key: "version",
          // filters: [
          //   { text: "London", value: "London" },
          //   { text: "New York", value: "New York" },
          // ],
          // filteredValue: filteredInfo.address || null,
          // onFilter: (value, record) => record.address.includes(value),
          // sorter: (a, b) => a.address.length - b.address.length,
          // sortOrder: sortedInfo.columnKey === "version" && sortedInfo.order,
          ellipsis: true,
        },
      ];
      return columns;
    },
  },
  mounted() {
    this.fetchSearchWork();
  },
};
</script>

<style scoped>
.next-btn {
  height: 72px;
  display: flex;
  align-items: center;
  border-top: 1px solid rgba(30, 32, 42, 0.15);
  justify-content: center;
}
.cancer {
  margin-right: 8px;
  width: 66px;
  padding: 0;
}
.next {
  width: 66px;
  padding: 0;
}
.maintable {
  padding: 0 20px;
}
</style>