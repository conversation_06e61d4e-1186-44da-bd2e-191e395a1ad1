<template>
  <div class="workflow-start">
    <div v-if="startFlow" class="start-header">
      <a-collapse expand-icon-position="right">
        <a-collapse-panel key="1" :header="flowModel && flowModel.processDefinitionName">
          <p>内容内容...</p>
        </a-collapse-panel>
      </a-collapse>
    </div>
    <div class="maintable">
      <a-form-model
        ref="ruleForm"
        :model="form"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <div :style="{ height: fullHeight - 46 + 'px' }" class="start-main">
          <a-form-model-item 
            v-for="(item,index) in flowModel.userTasks" 
            :label="`${item.name}：`" 
            :prop="item.assignee[0]" 
            :key="index+'_flow'"
            :rules="[{ required: true, message: '审核人不能为空!' }]"
          >
            <a-select
              show-search
              label-in-value
              placeholder="请输入用户姓名"
              :v-model.trim="form[item.assignee[0]]"
              :filter-option="false"
              :show-arrow="false"
              :not-found-content="null"
              @search="handleSearchUser"
              @change="(evt)=>handleChange(evt,item.assignee[0])"
              @focus="handleFocus"
            >
              <a-select-option v-for="d in searchUser" :key="d.account">
                {{ d.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <!-- <a-form-model-item label="提交审核：" prop="audit">
            <a-input
              v-model.trim="form.audit"
              :disabled="disabled"
              class="work-int"
            />
          </a-form-model-item>

          <a-form-model-item label="标准化审核：" prop="standard">
             <a-select
                show-search
                label-in-value
                placeholder="请输入用户姓名"
                style="width: 100%"
                :v-model.trim="form.standard"
                :filter-option="false"
                :show-arrow="false"
                :not-found-content="null"
                @search="handleSearchUser"
                @change="(evt)=>handleChange(evt,'standard')"
                @focus="handleFocus"
              >
                <a-select-option v-for="d in searchUser" :key="d.account">
                  {{ d.name }}
                </a-select-option>
              </a-select>
          </a-form-model-item>

          <a-form-model-item label="工艺审核：" prop="processor">
            <a-select
              show-search
              label-in-value
              placeholder="请输入用户姓名"
              style="width: 100%"
              :v-model.trim="form.processor"
              :filter-option="false"
              :show-arrow="false"
              :not-found-content="null"
              @search="handleSearchUser"
              @change="(evt)=>handleChange(evt,'processor')"
              @focus="handleFocus"
            >
              <a-select-option v-for="d in searchUser" :key="d.account">
                {{ d.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item> -->
        </div>
      </a-form-model>
      <div class="next-btn">
          <a-button  class="cancer" v-if="startFlow" @click="gotoBack">{{$t('btn_cancel')}}</a-button>
          <a-button class="cancer" @click="prevBtn" v-else>{{$t('btn_pre_step')}}</a-button>
          <a-button class="next-start" type="primary" @click="nextStep">启动</a-button>
      </div>
    </div>
  </div>
</template>

<script>
import { 
  searchProcessDef, 
  searchUser, 
  fetchStartWorkflow,
  fetchWorkflowVerify,
  fetchStartListWorkflow,
  fetchStartReviseWorkflow 
} from "apis/product/contentManage";
import { getCookie } from 'jw_utils/cookie'
export default {
  name: "start",
  props:[
    'processDefinitionKey',
    'fullHeight'
  ],
  data() {
    return {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
      disabled: true,
      form: {
        // audit: Jw.getUser().name,
        // standard: undefined,
        // processor: undefined,
      },
      startFlow:false,
      flowModel:{
        userTasks:[]
      },
      fetching:false,
      searchUser:[]
    };
  },
  methods: {
    prevBtn() {
      this.$emit("prevBtn", 1);
    },
    nextStep() {
      //启动工作流
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const {　params } = this.$route;
          const fetchList = 　params.id ? fetchStartWorkflow:fetchStartListWorkflow;
          fetchList.execute({...this.getParams()}).then(res=>{
              this.$success('工作流启动成功！')
              this.gotoBack()
            }).catch(err=>{
              if (err.msg) {
                this.$error(err.msg)
              }
            })
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    

    handleFocus(){
      this.fetchSearchUser()
    },

    handleSearchUser(val){
      clearTimeout(this.timeout)
      this.timeout = setTimeout(() => {
        this.fetchSearchUser(val)
      }, 500);
    },

    handleChange(value,type) {
      this.form[type] = value.key;
      this.form = Object.assign({},this.form,this.form)
    },

    fetchSearchProcessDef(){
      const {　params, query } = this.$route;
      searchProcessDef.execute({
        modelType: query.type,
        instanceOid:　params.id,
      }).then(res=>{
        this.flowModel = res;
      }).catch(err=>{
        if (err.msg) {
          this.$error(err.msg)
          this.gotoBack()
        }
      })
    },

    fetchSearchUser(val){
      searchUser.execute({
        searchKey:val || '',
        pageNum:1,
        pageSize:50,
        tenantId:1,
        tenantOid:Jw.getUser().tenantOid
      }).then(res=>{
        this.searchUser = res.rows
      }).catch(err=>{
        if (err.msg) {
          this.$error(err.msg)
        }
      })
    },

    fetchWorkflowVerify(){
      fetchWorkflowVerify.execute({
        processDefinitionKey:this.processDefinitionKey
      }).then(res=>{
        this.flowModel = res;
      }).catch(err=>{
        if (err.msg) {
          this.$error(err.msg)
        }
      })
    },

    fetchReviseWorkflow(){
      const {　params, query } = this.$route;
      fetchStartReviseWorkflow.execute({
        modelType: query.type,
        instanceOid: params.id,
        workflowKeyWord:'Quick_Revision_Workflow',
      }).then(res=>{
        this.flowModel = res;
      }).catch(res=>{
        if (err.msg) {
          this.$error(err.msg)
        }
      })
    },

    getParams(){
      const { flowModel, form } = this;
      const {　params, query } = this.$route;
      const otherParams=[];
      const modelsCache = sessionStorage.getItem('workflowData') || '{}';
      let formVal = {
        ...form,
        gateway:Jw.gateway,
        accesstoken:getCookie('token'),
        tenantAlias: getCookie("tenantAlias"),
        tenantOid: getCookie("tenantOid")
      }
      if(params.id){
        formVal = {
          ...formVal,
          oid:params.id,
          modelType:query.type,
        }
      }
      for(let key in formVal){
        otherParams.push({
          name:key == 'audit'?'creator':key,
          value: key == 'audit'? Jw.getUser().account :formVal[key]
        })
      }
      return {
        processDefinitionKey:flowModel.processDefinitionKey,
        tenantId:'pdm',
        models:params.id?null:JSON.parse(modelsCache).map(item=>{return {modelType:item.modelType,oid:item.oid}}),
        variables:otherParams,
      }
    },

    gotoBack(){
      this.$router.back()
    },
  },
  created(){
    const {　params, query } = this.$route;
    this.startFlow = params && params.id ? true:false;
    if(query.lifecycle){
      return this.fetchReviseWorkflow()
    }
    if(this.startFlow){
      this.fetchSearchProcessDef()
    }else{
      this.fetchWorkflowVerify()
    }
  }
};
</script>

<style lang="less" scoped>
.workflow-start{
  background: #fff;
  padding: 12px 18px;
  .start-header{
    border-top:1px solid #d9d9d9;
    border-bottom:1px solid #d9d9d9;
    margin-bottom: 12px;
    p{
      margin: 0;
      padding: 10px 0;
    }
  }
}
.next-btn {
  height: 72px;
  display: flex;
  align-items: center;
  border-top: 1px solid rgba(30, 32, 42, 0.15);
  justify-content: center;
}
.cancer {
  margin-right: 8px;
  width: 66px;
  padding: 0;
}
.next-start {
  width: 52px;
  padding: 0;
}
.work-int {
  width: 100%;
}
.start-main {
  width: 680px;
  margin: 0 auto;
  overflow-y: auto;
}

/deep/ .ant-collapse{
  border:none;
  background: transparent;
}

/deep/ .ant-collapse > .ant-collapse-item{
  border-bottom: none;
}

</style>