<template>
  <div>
    <div :style="{ height: fullHeight - 46 + 'px' }" class="maintable">
      <a-table
        :columns="columns"
        :pagination="false"
        rowKey="id"
        :scroll="{ y: fullHeight - 100 + 'px' }"
        :data-source="objectDataArr"
        @change="handleChange"
      >
        <template slot="name" slot-scope="text, record">
          <div class="product-info">
            <svg class="icon" aria-hidden="true">
              <use
                :xlink:href="
                  record.type === '文件夹'
                    ? '#jwi-unfolder'
                    : record.type === '零件'
                    ? '#jwi-part'
                    : record.type === 'part'
                    ? '#jwi-end-product'
                    : record.type === '半成品'
                    ? '#jwi-unproduct'
                    : record.type === 'Document'
                    ? '#jwi-document'
                    : '#jwi-unfolder'
                "
              ></use>
            </svg>
            <router-link to="/">{{ text }}</router-link>
          </div>
        </template>
        <template slot="operation" slot-scope="text, record, $index">
          <div class="operation-btn">
            <a @click="delData($index, record)">{{$t('txt_delete')}}</a>
          </div>
        </template>
      </a-table>
    </div>

    <div class="next-btn">
      <a-button class="cancer" @click="cancer">{{$t('btn_cancel')}}</a-button>
      <a-button type="primary" class="next" @click="nextStep">{{$t('btn_next_step')}}</a-button>
    </div>
  </div>
</template>

<script>
import { deleteObjectListApi } from "apis/product/contentManage";
export default {
  name: "objectTable",
  props: ["fullHeight", "objectDataArr"],
  data() {
    return {
      sortedInfo: null,
      filteredInfo: null,
    };
  },
  computed: {
    columns() {
      let { sortedInfo, filteredInfo } = this;
      sortedInfo = sortedInfo || {};
      filteredInfo = filteredInfo || {};
      const columns = [
        {
          title: this.$t('txt_name'),
          dataIndex: "name",
          key: "name",
          filters: [
            { text: "Joe", value: "Joe" },
            { text: "Jim", value: "Jim" },
          ],
          filteredValue: filteredInfo.name || null,
          onFilter: (value, record) => record.name.includes(value),
          sorter: (a, b) => a.name.length - b.name.length,
          sortOrder: sortedInfo.columnKey === "name" && sortedInfo.order,
          ellipsis: true,
          scopedSlots: { customRender: "name" },
        },
        {
          title: this.$t('txt_number_of'),
          dataIndex: "number",
          key: "number",
        //   filters: [
        //     { text: "Joe", value: "Joe" },
        //     { text: "Jim", value: "Jim" },
        //   ],
        //   filteredValue: filteredInfo.name || null,
        //   onFilter: (value, record) => record.name.includes(value),
        //   sorter: (a, b) => a.name.length - b.name.length,
        //   sortOrder: sortedInfo.columnKey === "number" && sortedInfo.order,
          ellipsis: true,
        },
        {
          title: this.$t('txt_type'),
          dataIndex: "modelType",
          key: "modelType",
        //   filters: [
        //     { text: "London", value: "London" },
        //     { text: "New York", value: "New York" },
        //   ],
        //   filteredValue: filteredInfo.address || null,
        //   onFilter: (value, record) => record.address.includes(value),
        //   sorter: (a, b) => a.address.length - b.address.length,
        //   sortOrder: sortedInfo.columnKey === "type" && sortedInfo.order,
          ellipsis: true,
        },
        {
          title: "操作",
          dataIndex: "operation",
          key: "operation",
          scopedSlots: { customRender: "operation" },
          ellipsis: true,
        },
      ];
      return columns;
    },
  },
  methods: {
    cancer() {
      this.$emit("cancer", 1);
    },
    nextStep() {
      this.$emit("nextStep", 1);
    },
    delData(index, record) {
      console.log("record", record);
      let that = this;
      this.$confirm({
        title: "确认提示",
        content: `是否确认删除此数据？`,
        onOk() {
          that.fetchDeleteData(record.id);
          that.objectDataArr.splice(index, 1);
        },
        onCancel() {
          return false;
        },
        class: "test",
      });
    },
    fetchDeleteData(id) {
      //to do 联调接口, 待做
      //  deleteObjectListApi
      // .execute({id: id})
      // .then(data => {
      //    this.$success(err.msg)
      // })
      // .catch(err => {
      //  if(err.msg) {
      //    this.$error(err.msg)
      //  }
      // });
    },
    handleChange(pagination, filters, sorter) {
      this.filteredInfo = filters;
      this.sortedInfo = sorter;
    },
  },
};
</script>

<style scoped>
.next {
  width: 66px;
  padding: 0;
}
.next-btn {
  height: 72px;
  display: flex;
  align-items: center;
  border-top: 1px solid rgba(30, 32, 42, 0.15);
  justify-content: center;
}
.cancer {
  margin-right: 8px;
  width: 52px;
  padding: 0;
}
.product-info {
  display: flex;
  align-items: center;
}
.maintable {
  padding: 0 20px;
}
.icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
}
</style>