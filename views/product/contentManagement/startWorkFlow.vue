<template>
  <div>
    <div slot="title" class="title-name">
      <div class="title-icon">
        <p>启动工作流</p>
      </div>
      <div class="operation-btn">
        <span @click="refreshBtn"
          ><img src="../../../assets/image/refresh.png"
        /></span>
        <span @click="closedObject"
          ><img src="../../../assets/image/delete.png"
        /></span>
      </div>
    </div>

    <div class="workflow-table">
      <div class="title">已选对象</div>
      <div class="addObject">
        <span>
          <a-button class="addObject-btn">添加对象</a-button>
          <a-input-search
            placeholder="请输入搜索关键字"
            class="search-btn"
            @search="onSearch"
          />
        </span>
        <span class="btn" @click="deleteBatchData">批量删除</span>
      </div>

      <div class="classtable">
        <a-table
          :columns="columns"
          :data-source="tableData"
          :scroll="{ y: 555 }"
          bordered
          :pagination="false"
          :row-selection="{
            selectedRowKeys: this.selectedRowKeys,
            onChange: this.onSelectChange,
          }"
        >
          <template slot-scope="text, record" slot="name">
            <svg class="jwifont" aria-hidden="true">
              <use
                :xlink:href="
                  record.type === '文件夹'
                    ? '#jwi-unfolder'
                    : record.type === '零件'
                    ? '#jwi-part'
                    : record.type === '成品'
                    ? '#jwi-end-product'
                    : record.type === '半成品'
                    ? '#jwi-unproduct'
                    : record.type === '文档'
                    ? '#jwi-document'
                    : '#jwi-unfolder'
                "
              ></use>
            </svg>
            <span class="link-name" @click="getDetail(record)">{{ text }}</span>
          </template>

          <template slot-scope="text" slot="viewName">
            <span class="view-text">{{ text }}</span>
          </template>

          <template slot-scope="text" slot="version">
            <span class="view-text">{{ text }}</span>
          </template>

          <template slot-scope="text, record, $index" slot="operation">
            <span @click="deleteData($index)"
              ><img src="../../../assets/image/delete-icon.png"
            /></span>
          </template>
        </a-table>
      </div>

      <div class="workflow-table">
        <div class="title">选择工作流</div>
        <a-select
          show-search
          class="select-workflow"
          placeholder="变更任务流程"
          :filter-option="filterOption"
          @focus="handleFocus"
          @blur="handleBlur"
          @change="handleChange"
        >
          <a-select-option value="jack"> Jack </a-select-option>
          <a-select-option value="lucy"> Lucy </a-select-option>
          <a-select-option value="tom"> Tom </a-select-option>
        </a-select>
      </div>

      <div class="select-person">
        <a-form-model :layout="form.layout" :model="form">
          <a-form-model-item label="审核人" prop="reviewer">
            <selectPersonel></selectPersonel>
          </a-form-model-item>

          <a-form-model-item label="会签人" prop="reviewer">
            <selectPersonel></selectPersonel>
          </a-form-model-item>

          <a-form-model-item label="批准人" prop="reviewer">
            <selectPersonel></selectPersonel>
          </a-form-model-item>
        </a-form-model>
      </div>
    </div>

    <div class="drawer-footer">
      <a-button class="comfirm-start">确认启动</a-button>
      <a-button class="cancer-btn" @click="closedObject">{{$t('btn_cancel')}}</a-button>
    </div>
  </div>
</template>


<script>
import selectPersonel from "./selectPersonnel";
export default {
  name: "startWorkFlow",
  props: ["fullHeight"],
  components: {
    selectPersonel,
  },
  data() {
    return {
      filterOption: true,
      selectedRowKeys: [],
      multipleSelection: [],
      form: {
        layout: "horizontal",
      },
      rules: {
        reviewer: [
          { required: true, message: "请选择审核人", trigger: "blur" },
        ],
      },
      visible: false,
      columns: [
        {
          title: this.$t('txt_name'),
          dataIndex: "name",
          key: "name",
          sorter: true,
          scopedSlots: { customRender: "name" },
        },
        {
          title:this.$t('txt_number'),
          dataIndex: "code",
          sorter: true,
          key: "code",
        },
        {
          title: this.$t('txt_type'),
          dataIndex: "modelType",
          key: "modelType",
          sorter: true,
          scopedSlots: { customRender: "modelType" },
          ellipsis: true,
        },
        {
          title: "视图",
          dataIndex: "viewName",
          sorter: true,
          key: "viewName",
          scopedSlots: { customRender: "viewName" },
        },
        {
          title: this.$t('txt_version'),
          dataIndex: "version",
          key: "version",
          sorter: true,
          ellipsis: true,
          scopedSlots: { customRender: "version" },
        },
        {
          title: "操作",
          dataIndex: "operation",
          key: "operation",
          sorter: true,
          align: "center",
          scopedSlots: { customRender: "operation" },
          ellipsis: true,
        },
      ],
      tableData: [
        {
          key: "1",
          name: "风扇001",
          modelType: "文件夹",
          code: "**********",
          version: "A.3",
          viewName: "Design",
        },
        {
          key: "2",
          name: "风扇001",
          modelType: "文件夹",
          code: "**********",
          version: "A.3",
          viewName: "Design",
        },
        {
          key: "3",
          name: "风扇001",
          modelType: "文件夹",
          code: "**********",
          version: "A.3",
          viewName: "Design",
        },
        {
          key: "4",
          name: "风扇001",
          modelType: "文件夹",
          code: "**********",
          version: "A.3",
          viewName: "Design",
        },
        {
          key: "5",
          name: "风扇001",
          modelType: "文件夹",
          code: "**********",
          version: "A.3",
          viewName: "Design",
        },
        {
          key: "6",
          name: "风扇001",
          modelType: "文件夹",
          code: "**********",
          version: "A.3",
          viewName: "Design",
        },
        {
          key: "7",
          name: "风扇001",
          modelType: "文件夹",
          code: "**********",
          version: "A.3",
          viewName: "Design",
        },
        {
          key: "8",
          name: "风扇001",
          modelType: "文件夹",
          code: "**********",
          version: "A.3",
          viewName: "Design",
        },
        {
          key: "9",
          name: "风扇001",
          modelType: "文件夹",
          code: "**********",
          version: "A.3",
          viewName: "Design",
        },
        {
          key: "10",
          name: "风扇001",
          modelType: "文件夹",
          code: "**********",
          version: "A.3",
          viewName: "Design",
        },
        {
          key: "11",
          name: "风扇001",
          modelType: "文件夹",
          code: "**********",
          version: "A.3",
          viewName: "Design",
        },
      ],
    };
  },
  methods: {
    getDetail(record) {
      console.log("record", record);
    },
    handleChange(value) {
      console.log(`selected ${value}`);
    },
    handleBlur() {
      console.log("blur");
    },
    handleFocus() {
      console.log("focus");
    },
    deleteData(index) {
      this.tableData.splice(index, 1);
    },
    deleteBatchData() {
      //批量删除数据
      if (this.multipleSelection.length > 0) {
        this.tableData = this.tableData.filter((item) => {
          return !this.multipleSelection.find((row) => {
            return item.key == row.key;
          });
        });
      } else {
        this.$warning("请至少选择一条数据!");
      }
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.multipleSelection = selectedRows;
    },
    onSearch(val) {
      console.log("val", val);
    },
    onClose(flag) {
      this.visiblewofkflow = flag;
    },
    refreshBtn() {},
    closedObject() {
      this.$emit("closed");
    },
  },
};
</script>

<style scoped>
.select-person {
  margin-bottom: 62px;
}
.cancer-btn {
  width: 52px;
  height: 32px;
  margin: 0 0 0 8px;
  padding: 0;
}
.comfirm-start {
  width: 80px;
  height: 32px;
  background: #255ed7;
  color: #fff;
  font-size: 14px;
  margin: 0;
  padding: 0;
}
.drawer-footer {
  position: fixed;
  bottom: 0;
  height: 72px;
  z-index: 100;
  border-top: 1px solid rgba(30, 32, 42, 0.06);
  background: #fff;
  width: 100%;
  display: flex;
  align-items: center;
}
.select-workflow {
  margin: 16px 0 0 0;
  width: 100%;
}
.view-text {
  background: rgba(30, 32, 42, 0.04);
  border-radius: 4px;
  border: 1px solid rgba(30, 32, 42, 0.15);
  height: 22px;
  line-height: 22px;
  padding: 0 7px;
}
.link-name {
  color: #255ed7;
  cursor: pointer;
}
.jwifont {
  width: 16px;
  min-width: 16px;
  height: 16px;
  min-height: 16px;
  margin-right: 8px;
}
.classtable {
  margin-top: 16px;
}
.btn {
  color: #f6445a;
  font-size: 14px;
  padding: 0;
  cursor: pointer;
}
.search-btn {
  width: 216px;
  align-items: center;
  margin-top: 8px;
}
.addObject-btn {
  background: #255ed7;
  color: #fff;
}
.addObject {
  margin: 10px 0;
  height: 32px;
  line-height: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.title {
  display: flex;
  height: 18px;
  line-height: 18px;
  padding-left: 10px;
  color: rgba(30, 32, 42, 0.85);
  font-size: 16px;
  box-sizing: border-box;
  font-weight: 500;
  border-left: 3px solid #255ed7;
}

.workflow-table {
  padding: 0;
  margin-top: 16px;
}
.title-name {
  display: flex;
  justify-content: space-between;
}
.title-icon {
  display: flex;
  align-items: center;
}
.title-icon p {
  margin: 0;
  font-size: 14px;
}

.model >>> .ant-modal-footer {
  border-top: none;
}
.add-btn,
.detail-btn,
.delete-btn {
  margin-right: 10px;
  color: #255ed7;
}
.jwifont {
  width: 16px;
  min-width: 16px;
  height: 16px;
  min-height: 16px;
  margin-right: 8px;
}
.operation-btn {
  display: flex;
}
.title-name {
  display: flex;
  justify-content: space-between;
}
.operation-btn span {
  margin-left: 10px;
  cursor: pointer;
}
.operation-btn span img {
  width: 16px;
  height: 16px;
}
</style>