<template>
    <div>
     <a-modal
      :visible="visibleVal"
      :confirm-loading="confirmLoading"
      class="alert-model"
      :footer="null"
      centered
      @cancel="cancerBtn"
    >
      <span slot="title" class="tltles">{{ title }}</span>
      <a-form-model
        ref="ruleForm"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
        labelAlign="left"
      >
      <a-form-model-item prop="name">
         <a-radio-group
              default-value="0"
              button-style="solid"
              @change="switchdRadio"
              class="radio-group"
            >
              <template v-if="radioval == 0">
                <a-radio-button value="0" style="background: #255ed7"
                  >{{$t('txt_existing_baseline')}}</a-radio-button
                >
                <a-radio-button value="1">{{$t('txt_create_baseline')}}</a-radio-button>
              </template>
              <template v-else>
                <a-radio-button value="0">{{$t('txt_existing_baseline')}}</a-radio-button>
                <a-radio-button value="1" style="background: #255ed7"
                  >{{$t('txt_create_baseline')}}</a-radio-button
                >
              </template>
            </a-radio-group>
        </a-form-model-item>
     

       
            <div v-if="haveGroup">
                <div class="base-content">
                 <a-form-model-item prop="viewoid" :label="$t('txt_seletct_baseline')">
                    <a-select
                        :placeholder="$t('msg_select')"
                        v-model.trim="form.viewoid"
                        class="baseline-select">
                        <a-select-option value="0">{{$t('txt_green')}}=</a-select-option>
                        <a-select-option value="1">{{$t('txt_bule')}}</a-select-option>
                        <a-select-option value="2">{{$t('txt_red')}}</a-select-option>
                    </a-select>
                </a-form-model-item>

                 <a-form-model-item prop="viewcatetory" :label="$t('txt_baseline_type')">
                    <a-select
                        :placeholder="$t('msg_select')"
                        v-model.trim="form.viewcatetory"
                        disabled
                        class="baseline-select">
                        <a-select-option value="">{{$t('msg_select')}}</a-select-option>
                        <a-select-option value="0">{{$t('txt_green')}}</a-select-option>
                        <a-select-option value="1">{{$t('txt_bule')}}</a-select-option>
                        <a-select-option value="2">{{$t('txt_red')}}</a-select-option>
                    </a-select>
                </a-form-model-item>
                </div>
                  <div class="comfirm-btn-info">
                    <a-button type="primary" class="comfirm-btn" @click="comfirmAdd">{{$t('btn_ok')}}</a-button>
                    <a-button class="cancer" @click="cancerBtn">{{$t('btn_cancel')}}</a-button>
                </div>
            </div>
            <div v-else>
                <a-form-model-item prop="oid" :label="$t('txt_baseline_name')">
                 <a-input :placeholder="$t('txt_baseline_name')" v-model.trim="form.oid" class="baseline-select" />
                </a-form-model-item>

                <a-form-model-item prop="catetory" :label="$t('txt_baseline_type')">
                    <a-select
                        :placeholder="$t('msg_select')"
                        v-model.trim="form.catetory"
                        class="baseline-select">
                       <a-select-option value="0">{{$t('txt_green')}}</a-select-option>
                        <a-select-option value="1">{{$t('txt_bule')}}</a-select-option>
                        <a-select-option value="2">{{$t('txt_red')}}</a-select-option>
                    </a-select>
                </a-form-model-item>

                <a-form-model-item prop="effectdate" :label="$t('txt_start_date')">
                    <a-date-picker @change="ondateChange" class="date" v-model.trim="form.effectdate"/>
                </a-form-model-item>

                <div class="comfirm-btn-info">
                <a-button type="primary" class="comfirm-btn" @click="comfirmAdd">{{$t('btn_ok')}}</a-button>
                <a-button class="cancer" @click="cancerBtn">{{$t('btn_cancel')}}=</a-button>
                </div>
            </div>

        </a-form-model>
    </a-modal>
    </div>
</template>


<script>
export default {
    name:'addedToBase',
    props:['visibleBase'],
    data(){
        return {
          visibleVal: null,
          confirmLoading: true,
          labelCol: { span: 24 },
          wrapperCol: { span: 24 },
          title:this.$t('txt_add_to_baseline'),
          haveGroup: true,
          radioval: 0,
          neweffectdate:null,  
          form: {
            oid:'',
            catetory:undefined,
            effectdate:'',
            viewoid:undefined,
            viewcatetory:'1',
          },
          rules:{
             oid: [{ required: true, message: this.$t('txt_baseline_null'), trigger: "change" }],
             effectdate: [{ required: true, message: this.$t('txt_start_date_null'), trigger: "change" }],
             catetory: [{ required: true, message: this.$t('txt_baseline_type_null'), trigger: "change" }],
             viewoid: [{ required: true, message: this.$t('txt_select_base_name'), trigger: "change" }],
             viewcatetory: [{ required: true, message: this.$t('txt_select_base_type'), trigger: "change" }],
          }
        }
    },
    watch: {
        visibleBase(newV){
            this.visibleVal = newV
        }
    },
    methods: {
         ondateChange(date, dateString) {
            console.log(dateString);
            this.neweffectdate = dateString
        },
        comfirmAdd(){  //to do 联调接口, 待做
          this.$refs.ruleForm.validate((valid) => {
            if (valid) {

            let param = {}
            if(this.radioval == '1') {//新建基线
               param = {
                  oid: this.form.oid,
                  catetory: this.form.catetory,
                  effectdate: this.neweffectdate,
               }
            }else {//已有基线
                 param = {
                  viewoid: this.form.viewoid,
                  viewcatetory: this.form.viewcatetory,
               }
            }

            console.log('param',param)
            this.$emit('closed',false)
             // createBaseToApi
                // .execute(param)
                // .then(data => {
               //   this.$emit('closed',false)
                //   
                // })
                // .catch(err => {
               //  if(err.msg){
               //    this.$error(err.msg)
                 //    }
                // });
              
            }
         })
            
          
        },
         cancerBtn() {
            this.$refs.ruleForm.resetFields();
            this.$emit('closed',false)
         },
           switchdRadio(val) {
            let setval = val.target.value;
            console.log('setval',setval)
            this.radioval = setval;
            this.haveGroup = setval == "0" ? true : false;
         }
    }
}
</script>


<style scoped>
.base-content {
    height: 252px;
}
.tltles {
  font-size: 16px;
  color: #292a2c;
  font-weight: 700;
}
.date{
    width: 100%;
}
.baseline-select {
  width: 100%;
}
.comfirm-btn {
  background: #255ed7;
  margin: 0 8px 0 0;
  width: 80px;
  padding: 0;
}
.comfirm-btn-info {
  display: flex;
  justify-content: flex-end;
}

.alert-model >>> .ant-modal-body {
  padding: 0 20px 22px 20px;
}
.alert-model >>> .ant-form-item-label {
  height: 20px;
  line-height: 20px;
}
.alert-model >>> .ant-modal-header {
  border-bottom: none;
  padding: 17px 24px;
}

</style>