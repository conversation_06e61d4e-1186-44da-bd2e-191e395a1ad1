<template>
    <div class="back-header flex justify-start align-center">
        <svg class="jwifont icon-back" aria-hidden="true" @click="goBack">
            <use xlink:href="#jwi-back"></use>
        </svg>
        <span>{{ title }}</span>
    </div>
</template>

<script>
export default {
    name: 'backHeader',
    props: [
        'title',
    ],
    data() {
        return {
            
        };
    },
    methods: {
        goBack() {
            this.$emit('back', false);
        },
    },
    mounted() {

    },
};
</script>

<style lang="less" scoped>
.back-header {
    padding-left: 5px;
    margin-bottom: 10px;
    color: #1e202a;
    .icon-back {
        width: 16px;
        height: 16px;
        margin-right: 5px;
        cursor: pointer;
    }
}
</style>
