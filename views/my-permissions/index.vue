<template>
  <jw-page>
    <div slot="header-left">
      <div style="display: flex; align-items: center">
        <span style="color: #000; font-size: 20px">我的权限</span>
      </div>
    </div>
    <div class="all-background product-container produce-container">
      <div class="product-filter">
        <div>
          <a-button type="primary" @click="toRequest()">权限申请</a-button>
<!--          <a-input-search-->
<!--              :placeholder="$t('search_text')"-->
<!--              style="width: 200px; margin-right: 8px; margin-left: 8px"-->
<!--              v-model.trim="searchKey"-->
<!--              @change="onSearch"-->
<!--          />-->
          <span
          >{{ $t("txt_total") }}{{ pagerConfig.total }}
            {{ $t("txt_pieces") }}</span
          >
        </div>
      </div>
      <div class="permission-list">
        <jw-table
            v-if="!switchType"
            ref="ref_table"
            disableCheck="disableCheck"
            :data-source.sync="tableData"
            :columns="getHeader"
            :selectedRows.sync="selectedRows"
            :fetch="fetchTable"
            :pagerConfig="pagerConfig"
            @onPageChange="onPageChange"
            @onSizeChange="onSizeChange"
        >
          <template #approvedTime="{ row }">
            <span style="color: #255ed7">{{
                formatDateFn(row.approvedTime)
              }}</span>
          </template>
        </jw-table>
      </div>
    </div>
  </jw-page>
</template>

<script>
//接口
import ModelFactory from "jw_apis/model-factory";
import {jwAvatar, jwIcon, jwModalForm, jwPage, jwSimpleTabs,} from "jw_frame";
import {formatDate} from "jw_utils/moment-date";
import userInfo from "components/user-info";

const fetchPermissionsList = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/customerContainer/queryPerm`,
  method: 'post',
})

export default {
  components: {
    jwSimpleTabs,
    jwAvatar,
    jwIcon,
    jwModalForm,
    // jwToolbar,
    jwPage,
    userInfo
  },
  inject: ["setBreadcrumb", "addBreadcrumb"],
  data() {
    return {
      cardSpinning: true,
      currentCreateRole: false,
      confirmText: this.$t("btn_ok"),
      productRow: {},
      // 数据处理
      flatData: [],
      // 表单弹窗
      containerTemplateOid: "",
      formModalVisible: false,
      pageSizeOptions: ["10", "20", "50", "100"],
      templateVisible: false, // 模板选择模态框
      switchType: false,
      deleteModal: false,
      searchKey: "",
      tableData: [],
      selectedRows: [],
      confirmLoadingStatus: false,
      pagerConfig: {
        current: 1,
        pageSize: 20,
        total: 0,
      }, //分页配置
      tableLoading: false,
      selectRow: [],
    };
  },
  computed: {
    getHeader() {
      return [
        {
          field: "ddUserName",
          title:"用户",
          width: "120",
        },
        {
          field: "path",
          title: "节点",
          slots: {
            default: ({ row }) => {
                if(row.folderName) {
                  return {
                    text: row.folderName.join("，"),
                  }
                } else {
                  return ""
                }
            }
          },
        },
        {
          field: "permission",
          title: "权限",
          width: 100,
        },
        {
          field: "approvedTime",
          title: "开通时间",
          width: 160,
          slots: {
            default: "approvedTime",
          },
        },
        {
          field: "status",
          title: "状态",
          width: 120,
        }
      ];
    },
    toRequest(){
      let routeUrl = this.$router.resolve({
        path: "/perApp"
      })
      window.open(routeUrl.href, "_blank")
    }
  },

  created() {
    this.searchKey = localStorage.getItem('container-search') ? localStorage.getItem('container-search') : ''
    // 输入回调去抖动
    this.delaySearch = _.debounce(this.onSearch, 500);
  },
  watch: {
    searchKey: function(val){
      if(val){
        localStorage.setItem('container-search', val)
      }else{
        localStorage.removeItem('container-search')
      }

    },
    switchType(){
      this.selectedRows=[]
    },
    tableData(){
      this.selectedRows=[]
    }
  },
  methods: {
    // 时间格式化转换
    formatDateFn(date) {
      if(!date) {
        return ""
      }
      return formatDate(date,'YYYY-MM-DD HH:mm:ss');
    },
    // 获取 row  part 操作下拉列表
    handleSwitchType() {
      this.switchType = !this.switchType;
    },
    // 选择列回调
    onSelectChange(args) {
      // console.log(args);
      this.selectRow = args;
    },
    // 操作列回调
    onToolInput({ key }, value) {
      // console.log(value);
      if (key === "search") {
        this.searchKey = value;
        this.delaySearch();
      }
    },
    // 删除
    onDelete(row) {
      this.fetchDelete([row]);
    },
    // 数据请求函数
    fetchTable() {
      let { searchKey, pagerConfig } = this;
      let { current, pageSize } = pagerConfig;
      let param = {
        index: current,
        size: pageSize,
        createBy:Jw.getUser().account
      };
      this.tableLoading = true;
      if (this.switchType === true) {
        this.cardSpinning = true;
      }
      return fetchPermissionsList
          .execute(param)
          .then((data) => {
            this.tableLoading = false;
            this.tableData = data.rows;
            this.cardSpinning = false;
            this.pagerConfig.total = data.count;
            return { data: data.rows };
          })
          .catch((err) => {
            this.tableLoading = false;
            this.$error(err.msg || this.$t("msg_failed"));
          });
    },
    //分页操作
    onPageChange(page, pageSize) {
      let { switchType } = this;
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;
      // console.log(page, pageSize);
      if (switchType) {
        this.fetchTable({ current: page, pageSize: pageSize });
      }
    },
    onSizeChange(pageSize, page) {
      let { switchType } = this;
      if (switchType) {
        this.pagerConfig.current = pageSize;
        this.pagerConfig.pageSize = page;
        this.fetchTable();
      } else {
        this.pagerConfig.current = page;
        this.pagerConfig.pageSize = pageSize;
      }
    },
    // 输入回调刷新表格数据
    onSearch() {
      (this.pagerConfig = {
        current: 1,
        pageSize: 20,
        total: 0,
      }), //分页配置
          this.fetchTable({ current: 1, pageSize: 10 });
    }
  },
};
</script>

<style lang="less">
.product-container.produce-container {
  padding: 0;
  box-shadow: none;
  display: flex;
  flex-direction: column;

  .permission-list {
    padding: 0 5px 0px 12px;
    flex-grow: 1;
    height: 20px;
    .card-list-pagination {
      padding: 10px 8px 10px 0;
      text-align: right;
    }

    .ant-spin-nested-loading {
      height: 100%;
      > .ant-spin-container {
        height: 100%;
        > .card-list {
          height: 100%;
          display: flex;
          flex-direction: column;
          .ant-row {
            flex-grow: 1;
            height: 20px;
            overflow: auto;
            margin: 0 !important;
            // 卡片样式
            .card-item {
              border: 8px solid #fff;
              &:nth-of-type(4n + 0) {
                margin-right: 0;
                padding-right: 0!important;
              }
              .card-content {
                height: 140px;
                padding: 16px;
                background: rgba(30, 32, 42, 0.04);
                border: 1px solid rgba(30, 32, 42, 0.04);
                border-radius: 4px 4px 4px 4px;
                &:hover {
                  background: #f0f7ff;
                  border: 1px solid #a4c9fc;
                  cursor: pointer;
                }
                .item-info {
                  margin: 0;
                  .item-title {
                    position: relative;
                    display: inline-block;
                    width: 100%;
                    color: rgba(30, 32, 42, 0.85);
                    .item-title-name {
                      width: 86%;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      overflow: hidden;
                      word-break: break-all;
                      font-weight: 500;
                      font-size: 14px;
                      color: rgba(30, 32, 42, 0.85);
                      cursor: pointer;
                    }
                    .item-name {
                      margin-left: 8px;
                      font-weight: 500;
                    }
                    .item-more {
                      position: absolute;
                      right: 5px;
                      top: 0px;
                      z-index: 10;
                    }
                  }
                  .item-description {
                    height: 26px;
                    line-height: 26px;
                    font-size: 12px;
                    color: rgba(30, 32, 42, 0.45);
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  }
                  .item-productCatalogName {
                    height: 28px;
                    line-height: 27px;
                    font-size: 12px;
                    font-weight: 500;
                    color: #1e202a;
                  }
                  .item-avatar {
                    margin-top: 4px;
                    height: 40px;
                    .avatar {
                      float: left;
                    }
                    .private {
                      line-height: 40px;
                      float: right;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    .jw-table {
      padding: 10px 15px 0 10px;
    }
  }
  .product-filter {
    padding: 0 18px;
    margin-bottom: 4px;
    display: flex;
    justify-content: space-between;
  }
  .no-data-wrap {
    height: 100%;
    .no-data-con {
      height: calc(~"100vh - 130px");
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      img {
        margin-bottom: 25px;
      }
      span {
        color: #255ed7;
        cursor: pointer;
      }
      i {
        cursor: pointer;
      }
    }
  }
}
/deep/.form-model-item {
  &:nth-of-type(2) {
    margin-right: 30px;
  }
}
// tab页签样式
.wrap-class {
  /deep/.item-class {
    margin: 0 20px 0 0;
    padding: 4px 0;
    color: rgba(30, 32, 42, 0.65);
  }
  /deep/.item-class-active {
    font-size: 14px;
    font-weight: 500;
    color: rgba(30, 32, 42, 0.85);
  }
}
</style>
<style>
.deleteModal .ant-modal-body {
  padding: 24px;
}
.deleteModal .ant-modal {
  /* top: 112px;
  left: 42%; */
}
.deleteModal .ant-modal-close-x {
  line-height: 69px;
}
.deleteModal
.ant-modal-confirm-body
> .anticon
+ .ant-modal-confirm-title
+ .ant-modal-confirm-content {
  margin-left: 0;
}
.deleteModal .ant-modal-confirm-btns .ant-btn {
  width: 75px;
  float: right;
}
.deleteModal .ant-modal-confirm-btns .ant-btn.ant-btn-primary {
  margin-right: 8px;
  /* background-color: rgba(37, 94, 215, 1);
  border-color: rgba(37, 94, 215, 1); */
  /* background-color: #1890ff; */
}
.operation-dropdown-overlay {
  /* width: 150px; */
}
</style>
