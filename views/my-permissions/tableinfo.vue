<template>
    <div class="table-wrap">
        <a-table
            class="table-con"
            rowKey="oid" 
            :columns="columns" 
            :data-source="tableData"
            :pagination="false"
            :scroll="{ x: 800, y: 'calc(100vh - 309px)' }"
            :row-selection="{selectedRowKeys: selectedRowKeys, onChange: onSelectionChange}"
            :loading="loading"
        >
            <template slot="name" slot-scope="text, record">
                <div class="flex align-center">
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#jwi-product"></use>
                    </svg>
                    <span class="pro-name" @click="goContentPage(record)">{{ text }}</span>
                </div>
            </template>
            <template slot="createdTime" slot-scope="text">
                <div>{{ formatDateFn(text) }}</div>
            </template>
            <template slot="operation" slot-scope="text, record">
                <a-popover
                    trigger="click"
                    placement="bottomLeft"
                >
                    <div slot="title" class="font-700">{{$t('txt_rename')}}</div>
                    <template slot="content">
                        <a-form-model
                            ref="renameForm"
                            :model="form"
                            :rules="rules"
                        >
                            <a-form-model-item
                                :label="$t('txt_group_name')"
                                prop="name">
                                <a-input
                                    v-model.trim="form.name"
                                    :max-length="50"
                                    allow-clear
                                    :placeholder="$t('txt_enter_group_name')" />
                            </a-form-model-item>
                            <a-form-model-item class="text-right">
                                <a-button type="primary" @click="onSubmit">
                                   {{$t('btn_ok')}} 
                                </a-button>
                            </a-form-model-item>
                        </a-form-model>
                    </template>
                    <span class="ope-btn">{{$t('txt_rename')}} </span>
                </a-popover>
                <span class="ope-btn" @click="openBinding">{{$t('txt_binding_team')}} </span>
                <a-dropdown>
                    <a-icon type="ellipsis" />
                    <a-menu slot="overlay">
                        <a-menu-item>{{$t('txt_characteristics')}} </a-menu-item>
                        <a-menu-item @click="goStructurePage(record)">{{$t('txt_product_structure')}} </a-menu-item>
                        <a-menu-item>{{$t('txt_save_template')}} </a-menu-item>
                    </a-menu>
                </a-dropdown>
            </template>
       </a-table>
       <div class="foot-wrap">
            <a-pagination
                show-size-changer
                :default-current="page.currenrPage"
                :pageSize.sync="page.pageSize"
                :total="page.total"
                :show-total="total => $t('txt_total')+` ${total} `+$t('txt_pieces')"
                @change="onCurrentChange"
                @showSizeChange="onSizeChange"
            />
        </div>
    </div>
</template>

<script>
import { formatDate } from 'jw_utils/moment-date';
import { fetchProductList,getDataRueture } from 'apis/product/';
export default {
    name: 'tableInfo',
    props: [

    ],
    components: {

    },
    data() {
        return {
            searchKey: '',
            loading: true,
            columns: [
                {
                    title: this.$t('txt_username'),
                    dataIndex: 'name',
                    key: 'name',
                    scopedSlots: { customRender: 'name' },
                },
                {
                    title: this.$t('txt_product_manager'),
                    dataIndex: 'creatorName',
                    key: 'creatorName',
                },
                {
                    title: this.$t('txt_create_date'),
                    dataIndex: 'createdTime',
                    key: 'createdTime',
                    scopedSlots: { customRender: 'createdTime' },
                },
                {
                    title: this.$t('txt_operation'),
                    dataIndex: 'operation',
                    key: 'operation',
                    scopedSlots: { customRender: 'operation' },
                },
            ],
            tableData: [],
            selectedRowKeys: [],
            page: {
                currentPage: 1,
                pageSize: 20,
                total: 0,
            },
            form: {
                name: '',
            },
            rules: {
                name: [
                    { required: true, message: this.$t('txt_enter_agin_name'), trigger: 'blur' },
                ],
            },
        };
    },
    mounted(){
        
    },
    methods: {
        getProductList() {
            this.loading = true;
            fetchProductList.execute({
                searchKey: this.searchKey,
                page: this.page.currentPage,
                size: this.page.pageSize,
            }).then((res) => {
                this.tableData = res.rows;
                this.page.total = res.count;
                this.loading = false;
            }).catch((err) => {
                this.loading = false;
                if (err.msg) {
                    this.$error(err.msg);
                }
            });
        },
        // getDataRueture(data){
        //     getDataRueture.execute(data).then((res)=>{

        //     })
        // },
        formatDateFn(date) {
            return formatDate(date);
        },
        onSelectionChange(selectedRowKeys) {
            this.selectedRowKeys = selectedRowKeys;
        },
        onCurrentChange(page, pageSize) {
            this.page.currentPage = page;
            this.getProductList();
        },
        onSizeChange(current, size) {
            this.page.pageSize = size;
            this.getProductList();
        },
        goContentPage(row) {
            this.$router.push(`/contentManage/${row.oid}/${row.modelType}`);
        },
        goStructurePage(row) {
            this.$router.push(`/productStructure/${row.oid}/${row.modelType}`);
        },
        openBinding() {
            this.$emit('switchBinding', true);
        },
        onSubmit(index) {     
            this.$refs.renameForm.validate((valid) => {
                if (valid) {
                    this.form.name = '';
                } else {
                    return false;
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
.table-wrap {
    .icon {
        width: 16px;
        min-width: 16px;
        height: 16px;
        min-height: 16px;
    }
    .table-con {
        height: calc(100vh - 64px - 40px - 52px - 73px);
        padding: 13px 20px;
        .icon {
            margin-right: 8px;
        }
        .anticon {
            color: #255ed7;
        }
        .pro-name {
            color: #255ed7;
            cursor: pointer;
        }
        .ope-btn {
            margin-right: 8px;
            cursor: pointer;
            color: #255ed7;
        }
    }
    .foot-wrap {
        padding: 20px 12px 20px 20px;
		border-top: 1px solid rgba(30, 32, 42, 0.15);
        text-align: right;
    }
}
</style>
