<template>
  <div class="perApp" v-loading="loading">
    <h3 class="title">权限申请</h3>
    <a-form-model :model="form" ref="form" :rules="rules">
      <a-form-model-item label="型号" prop="containerOid">
        <a-select 
          v-model="form.containerOid" 
          @change="change"
          show-search
          :filter-option="filterOption"
          placeholder="请选择或搜索型号"
        >
          <a-select-option
            v-for="(ele, index) in containerList"
            :value="ele.oid"
            :key="index"
          >
            {{ ele.name }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item label="权限" prop="permission">
        <a-radio-group v-model="form.permission">
          <a-radio value="0">只读</a-radio>
          <a-radio value="1">可下载</a-radio>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item label="权限路径" prop="folderOid">
        <a-tree
          ref="folderTree"
          class="foldertree"
          :defaultExpandAll="defaultExpandAll"
          :replaceFields="replaceFields"
          :selectedKeys="selectedKeys"
          :expanded-keys="expandedKeys"
          :auto-expand-parent="autoExpandParent"
          :tree-data="treeData"
          checkable
          @expand="onExpand"
          @check="onCheck"
        >
          <template #title="{ title,dataRef }">
            <div class="tree-self">
              <jw-icon
                  :type="
                  dataRef.childType === 'child'
                    ? '#jwi-wenjianga-youneiyong'
                    : '#jwi-chanpin'
                "
              />
              <span class="tree-self-title" :title="title">
                {{ title }}
              </span>
            </div>
          </template>
        </a-tree>
        <div class="selected-paths" v-if="permissionUrl">
          <div class="path-title">已选择的路径：</div>
          <div class="path-content" :title="permissionUrl">{{ permissionUrl }}</div>
        </div>
        <a-input v-model="permissionUrl" style="display: none" />
      </a-form-model-item>
      <a-form-model-item label="申请事由" prop="reason">
        <a-textarea v-model="form.reason" />
      </a-form-model-item>
      <a-form-model-item style="text-align: right">
        <a-button type="primary" @click="submit">提交</a-button>
      </a-form-model-item>
    </a-form-model>
  </div>
</template>
<script>
import ModelFactory from "jw_apis/model-factory";
const submit = ModelFactory.create({
  url: `${Jw.gateway}/customer/customerContainer/submit`,
  method: "post",
});
import { fetchContainerList } from "apis/product-container";
const fetchfolderTree = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/pdm-folder/searchTreeWithOutPermission`,
  method: "get",
});
export default {
  data() {
    return {
      form: {
        containerOid: "",
        containerName:"",
        permission: "0",
        reason: "",
        folderOid: [],
        folderName: [],
        ddCode:""
      },
      rules: {
        containerOid: [{ required: true, message: "请选择类型" }],
        permission: [{ required: true, message: "请选择权限" }],
        reason: [{ required: true, message: "请输入申请事由" }],
        folderOid: [{ type:'array',required: true, message: "请选择目录" }],
      },
      treeData: [],
      treeData1: [
        {
          name: "parent 1",
          oid: "0-0",
          children: [
            {
              name: "parent 1-0",
              oid: "0-0-0",
              children: [
                { name: "leaf", oid: "0-0-0-0" },
                { name: "leaf", oid: "0-0-0-1" },
              ],
            },
            {
              name: "parent 1-1",
              oid: "0-0-1",
              children: [{ oid: "0-0-1-0", name: "sss" }],
            },
          ],
        },
      ],
      replaceFields: {
        key: "oid",
        title: "name",
        children: "children",
      },
      expandedKeys: [],
      selectedKeys: [],
      autoExpandParent: false,
      currentTree: {},
      containerList: [],
      flattenData:[],
      container: {},
      loading: false,
      defaultExpandAll: true
    };
  },
  created() {
    this.getConList();
  },
  mounted() {
    // this.initdd();
  },
  computed: {
    permissionUrl() {
      let url = "";
      if (this.form.folderName?.length) {
        url = `${this.form.folderName.join("，")}`;
      }
      return url;
    },
  },
  methods: {
    getQuery(url) {
        url=url?url:location.href
        const paramsRegex = /[?&]+([^=&]+)=([^&]*)/gi;
        const params = {};
        let match;
        while (match = paramsRegex.exec(url)) {
            params[match[1]] = match[2];
        }
        return params;
    },
    getConList() {
      let param = {
        index: 1,
        size: 10000,
      };
      this.loading=true
      fetchContainerList
        .execute(param)
        .then((res) => {
          this.containerList = res.rows || [];
          this.loading=false
        })
        .catch((err) => {
          this.loading=false
          this.$error(err?.msg || this.$t("msg_failed"));
        });
    },
    onContextMenuClick(treeKey, menuKey) {
      console.log(`treeKey: ${treeKey}, menuKey: ${menuKey}`);
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
    },
    selectFn(node){
      let { dataRef } = node.node;
      this.currentTree = dataRef;
      this.selectedKeys=[this.currentTree.oid]
      this.$refs.form.clearValidate("folderOid");
    },
    onSelect(value, node, extra) {
      this.selectFn(node)
    },
    onCheck(checkedKeys,node) {
      this.form.folderOid = this.findCheckFolder(node);
      console.log("folderOid:",this.form.folderOid)
      this.form.folderName=this.getNames()
      this.$refs.form.clearValidate("folderOid");
    },
    findCheckFolder(node) {
      let checkOidList = [];
      let checkedNodesPositions = node.checkedNodesPositions
      let posList = [];
      checkedNodesPositions.forEach(p=>{
         if(p.pos) {
           posList.push(p.pos);
         }
      })

      checkedNodesPositions.forEach(p=>{
        if(p.pos) {
          let index = p.pos.lastIndexOf("-");
          let parentPath = p.pos.substring(0, index);
          if(!posList.includes(parentPath)) {
            checkOidList.push(p.node.key);
          }
        }
      })
      return checkOidList
    },
    unquie(arr){
      let newArr = [],obj={}
      arr.forEach(ele=>{
        if(!obj[ele]){
          obj[ele]=true
          newArr.push(ele)
        }
      })
      return newArr
    },
    getNames(){
      let arr = []
      this.form.folderOid.forEach(ele=>{
        let obj=this.flattenData.find(e=>ele===e.oid)
        if(obj){
          arr.push(obj.names.join('-'))
        }
      })
      return arr
    },
    deepData(data, scopedSlots, parent) {
      let _this = this;
      let names = [], oids = [];
      if (parent) {
        names = parent.names || [parent.name];
        oids = parent.oids || [parent.oid];
      }
      data.forEach((item, index) => {
        item.title = item.name;
        item.key = item.oid;
        item.value = item.oid;
        item.names = names.concat([item.name]);
        item.oids = oids.concat([item.oid]);
        if (scopedSlots) {
          item.scopedSlots = scopedSlots;
          if (scopedSlots.title) delete item.title;
        }
        if (item.children && item.children.length > 0) {
          item.children.forEach((i) => {
            i.childType = "child";
            i.names = item.names.concat([i.name]);
            i.oids = item.oids.concat([i.oid]);
          });
          _this.deepData(item.children, scopedSlots, item);
        } else {
          item.disabled = false; // 叶子节点不禁用
        }
      });
      // 确保顶级节点不可勾选
      if (!parent) {
        data.forEach((item) => {
          item.disabled = true; // 顶级节点禁用
        });
      }
      return data;
    },
    flatten(array) {
      var flattend = []
      ;(function flat(array) {
        array.forEach(function (el) {
          if(el.children?.length){
            flat(el.children)
          }
          flattend.push(el)
        })
      })(array)
      return flattend
    },
    change(val) {
      let index = this.containerList.findIndex((ele) => ele.oid === val);
      this.container = this.containerList[index] || {};
      this.form.containerName=this.container.name
      this.form.folderName=[]
      this.form.folderOid=[]
      this.expandedKeys=[]
      this.selectedKeys=[]
      this.checkedKeys=[]
      this.fetchFold();
    },
    submit() {
      let _this = this
      this.$refs.form.validate((valid) => {
        if(!this.form.folderOid || this.form.folderOid.length === 0) {
          _this.$error('请先勾选申请目录');
          return
        }
            if (valid && _this.permissionUrl) {
              let data = _this.form;
              _this.loading = true;
              submit
                  .execute(data)
                  .then((res) => {
                    _this.loading = false;
                    _this.$success(_this.$t("msg_success") || "操作成功");
                    window.close();
                  })
                  .catch((err) => {
                    _this.loading = false;
                    _this.$error(err?.msg || _this.$t("msg_failed"));
                  });

            }
      });
    },
    fetchFold() {
      let container = this.container;
      if (!container.oid) {
        return;
      }
      let param = {
        containerOid: container.oid,
        containerModel:
          container.masterType ||
          container.modelDefinition ||
          container.containerModel,
      };
      this.loading = true;
      fetchfolderTree
        .execute(param)
        .then((data) => {
          data = data || [];
          this.treeData = this.deepData(data);
          this.flattenData = this.flatten(this.treeData)
          this.loading = false;
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"));
          this.loading = false;
        });
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
  },
};
</script>
<style lang="less" scoped>
.perApp {
  padding: 20px;
  height: 100%;
  overflow: auto;
  .foldertree {
    overflow: auto;
    max-height: 300px;
  }
  .title {
    font-weight: bold;
    text-align: center;
    padding-bottom: 20px;
  }
  .tree-self {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    min-width: auto;
    .tree-self-title {
      margin-left: 4px;
      flex: 1;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
    }
    .tree-self-opration {
      margin-left: 27px;
      display: none;
    }
    &:hover {
      .tree-self-opration {
        display: initial;
      }
    }
  }
  .selected-paths {
    margin-top: 10px;
    padding: 8px;
    background-color: #f5f5f5;
    border-radius: 4px;
    .path-title {
      font-weight: bold;
      margin-bottom: 4px;
    }
    .path-content {
      color: #666;
      word-break: break-all;
    }
  }
}
</style>