<template>
  <div class="base-panel page-container">
    <header>
      <div class="header-left">
        <a-breadcrumb separator=">">
          <a-breadcrumb-item>
            <a href="javascript:void(0)" @click="routerBack">
              {{ $t("txt_supplier") }}
            </a>
          </a-breadcrumb-item>
          <a-breadcrumb-item>{{
              currentRow.manufacturerName
            }}
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </header>
    <div class="base-panel page-container">
      <div class="basic-info">
        <jw-layout-builder ref="ref_appBuilder" type="Model" :layoutName="showEdit ? 'update' : 'show'"
                           modelName="Supplier"
                           :instanceData="currentRow">
          <!--        <template #logoData="{formData}">-->
          <!--          <div>-->
          <!--            <img :src="formData.manufacturerLogo">-->
          <!--&lt;!&ndash;            <uploadImage ref="dialog" v-model.trim="formData.manufacturerLogo" :disable="!showEdit"&ndash;&gt;-->
          <!--&lt;!&ndash;                         :accept="'.jpg,.png,.gif,.mp4,.txt,.doc,.docx'"/>&ndash;&gt;-->
          <!--          </div>-->
          <!--        </template>-->
        </jw-layout-builder>
      </div>
      <div class="issue-footer">
        <a-button v-if="showEdit" type="primary" style="margin-right: 12px; margin-left: 40%" @click="">
          {{ $t('btn_save') }}
        </a-button>
        <a-button v-if="showEdit" @click="handleEdit">{{ $t('btn_cancel') }}</a-button>
      </div>
    </div>
  </div>
</template>

<script>
import {
  jwLayoutBuilder
} from "jw_frame";
import { findWithCrumbs } from "apis/change";
export default {
  name: "create-supplier",
  props: [],
  components: {
    jwLayoutBuilder
  },
  data() {
    return {
      inject: ["setBreadcrumb", "addBreadcrumb"],
      showEdit: this.$route.query.edit || false,
      currentRow: {}
    }
  },
  created() {
    // this.setBreadcrumb([]);
    console.log("this.$route.query.edit", this.$route.query.edit)
    this.fetchSupplierDate();
    this.initBreadcrumb();
  },
  methods: {
    handleEdit() {
      this.showEdit = false;
    },
    routerBack() {
      this.$router.go(-1);
    },
    fetchSupplierDate() {
      findWithCrumbs
          .execute({
            oid: this.$route.query.oid,
          })
          .then((res) => {
            console.log("Supplier详细信息", res);
            this.currentRow = res;
          });
    },
    initBreadcrumb() {
      let breadcrumbData = [];
      this.setBreadcrumb(breadcrumbData);
    },
  }
}
</script>

<style scoped>

</style>