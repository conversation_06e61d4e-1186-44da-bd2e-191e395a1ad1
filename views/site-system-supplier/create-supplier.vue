<template>
  <a-modal v-if="visible" :visible="visible" :title="$t('txt_create_supplier')" width="67%" :mask-closable="false" :footer="null" @cancel="onCancel">
    <div>
      <jw-layout-builder ref="ref_appBuilder" type="Model" :layoutName="'create'" modelName="Supplier">
        <!--        <template #logoData="{formData}">-->
        <!--          <div>-->
        <!--            <uploadImage ref="dialog" v-model.trim="formData.manufacturerLogo" :accept="'.jpg,.png,.gif,.mp4,.txt,.doc,.docx'"/>-->
        <!--          </div>-->
        <!--        </template>-->
      </jw-layout-builder>
    </div>
    <div :style="{
					textAlign: 'center'
				}">
      <a-button type="primary" @click="onSave"> {{ $t('btn_save') }}</a-button>
      <a-button :style="{ marginRight: '8px' }" @click="onCancel">
        {{ $t('btn_cancel') }}
      </a-button>
    </div>

  </a-modal>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import {
  jwLayoutBuilder
} from "jw_frame";

const createSupplierLine = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/supplier/create`,
  method: "post",
});

export default {
  name: "create-supplier",
  props: ["visible"],
  components: {
    jwLayoutBuilder,
  },
  data() {
    return {
      file: {},
      fileList: ""
    }
  },
  created() {
    console.log("file1", this.file)
  },
  methods: {
    onCancel() {
      let {$refs: {ref_appBuilder}} = this;
      ref_appBuilder && ref_appBuilder.reset();
      this.$emit("close");
    },
    onSave() {
      let appBuilder = this.$refs.ref_appBuilder;
      console.log('appBuilder', appBuilder.getValue())
      appBuilder.validate().then(() => {
        let params = appBuilder.getValue();
        createSupplierLine
            .execute({
              ...params,
            })
            .then((res) => {
              this.$success('添加成功！');
              this.onCancel();
              this.$emit('getTableData');
            })
            .catch((err) => {
              this.$error(err.msg || this.$t("txt_create_supplier_fail"));
            });
      });
    }
  }
}
</script>

<style scoped>

</style>