<template>
  <div class="all-background">
    <jw-table
        ref="ref_table"
        disableCheck="disableCheck"
        :data-source.sync="tableData"
        :columns="getHeader"
        :selectedRows.sync="selectedRows"
        :fetch="fetchTable"
        :toolbars="toolbars"
        :pagerConfig="pagerConfig"
        @onPageChange="onPageChange"
        @onSizeChange="onSizeChange"
        @onToolClick="onToolClick"
        @onToolInput="onToolInput"
        @checkbox-change="onSelectChange"
        @onOperateClick="onOperateClick"
    >
      <template #logoData="{ row }">
        <!--        <uploadImage ref="dialog" v-model.trim="row.manufacturerLogo[0].oid" :disable="!showEdit"/>-->
        <img v-if="row.logo" :src="row.logo" width="30px" height="30px" style="object-fit: cover"/>
      </template>
      <template slot="tool-after-start">
        <a-button
            v-show="selectedRows.length > 0"
            style="background: #ffffff; color: #f81d22; text-shadow: none"
            type="danger"
            @click="onToolClick({ key: 'delete' })"
        >
          {{ $t("btn_batch_delete") }}
        </a-button>
      </template>
    </jw-table>
    <create-supplier
        :visible="visible"
        @close="onCloseModal"
        @getTableData="getAllTableData"
    ></create-supplier>

    <a-drawer
        width="55%"
        :visible="visibleDraw"
        :body-style="{ paddingBottom: '80px' }"
        @close="onClose">
      <jw-layout-builder ref="ref_appBuilder" type="Model" :layoutName="showEdit ? 'update' : 'show'"
                         modelName="Supplier"
                         :instanceData="currentRow">
        <!--        <template #logoData="{formData}">-->
        <!--          <div>-->
        <!--            <img :src="formData.manufacturerLogo">-->
        <!--&lt;!&ndash;            <uploadImage ref="dialog" v-model.trim="formData.manufacturerLogo" :disable="!showEdit"&ndash;&gt;-->
        <!--&lt;!&ndash;                         :accept="'.jpg,.png,.gif,.mp4,.txt,.doc,.docx'"/>&ndash;&gt;-->
        <!--          </div>-->
        <!--        </template>-->
      </jw-layout-builder>
      <div>
        <a-button v-if="showEdit" type="primary" style="margin-right: 12px; margin-left: 40%" @click="onEdit">
          {{ $t('btn_save') }}
        </a-button>
        <a-button v-if="showEdit" @click="onClose">{{ $t('btn_cancel') }}</a-button>
      </div>
    </a-drawer>

  </div>
</template>

<script>

import ModelFactory from "jw_apis/model-factory";
import createSupplier from "./create-supplier"
import {getCookie} from "jw_utils/cookie";
import {
  jwLayoutBuilder
} from "jw_frame";
//获取列表
const supplierTableModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/supplier/fuzzyPage`,
  method: "post",
});

const updateSupplier = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/supplier/update`,
  method: "post"
});

const deleteSupplier = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/supplier/deleteByOid`,
  method: "get"
});

export default {
  components: {
    createSupplier,
    jwLayoutBuilder
  },
  inject: ["setBreadcrumb"],
  data() {
    return {
      visibleDraw: false,
      visible: false,
      currentRow: {},
      showEdit: false,
      searchKey: "",
      tableData: [],
      selectedRows: [],
      pagerConfig: {
        current: 1,
        pageSize: 20,
        total: 0
      }, //分页配置
      tableLoading: false,
      total: 0,
      selectRow: [],
    };
  },
  computed: {
    getHeader() {
      return [
        {
          field: "manufacturerName",
          title: this.$t("txt_supplier_name"),
          // title: "厂商名称",
          sortable: true, // 开启排序
          width: 150,
          fixed: "left",
          slots: {
            // 插槽形式
            default: "",
          },
        },
        //厂商LOGO
        {
          field: "manufacturerLogo",
          title: this.$t("txt_supplier_logo"),
          slots: {
            // 插槽形式
            default: "logoData",
          },
          width: 150,
        },
        {
          field: "number",
          title: this.$t("txt_supplier_number"),
          width: 150,
        },
        {
          field: "abbreviation",
          title: this.$t("txt_supplier_abbreviation"),
          width: 150,
        },
        {
          field: "manufacturerType",
          title: this.$t("txt_supplier_type"),
          width: 150,
        },
        {
          field: "address",
          title: this.$t("txt_address"),
        },
        {
          field: "contacts",
          title: this.$t("txt_contacts"),
          width: 150,
        },
        {
          field: "contactInformation",
          title: this.$t("txt_contacts_information"),
          width: 150,
        },
        {
          field: "certificationLevel",
          title: this.$t("txt_certification_level"),
          width: 150,
        },
        // {
        //   field: "createDate",
        //   title: this.$t("sys_create_Date"),
        //   sortable: true, // 开启排序
        //   formatter: 'date',
        // },
        {
          // 操作列定义
          field: "operation", //关键字
          title: this.$t('txt_operation'),
          btns: [
            {
              icon: "jwi-iconedit", // 图标显示
              title: this.$t('btn_edit'),
              key: "edit",
            },
            {
              icon: "jwi-iconinfo-circle", // 图标显示
              title: this.$t('detailed_info'),
              key: "detail",
            },
            {
              icon: "jwi-icondelete", // 图标显示
              title: this.$t('btn_delete'),
              key: "delete",
            }
          ],
        },
      ];
    },
    toolbars() {
      return [
        {
          name: this.$t("btn_new_create"),
          position: "before",
          type: "primary",
          key: "create",
          // prefixIcon: "jwi-plus",
        },
        {
          name: this.$t("btn_search"),
          position: "before",
          display: "input",
          value: this.searchKey,
          allowClear: false,
          placeholder: this.$t("txt_search"),
          suffixIcon: 'jwi-iconsearch',
          key: "search",
        },
      ];
    },
  },
  created() {
    console.log('row', this.row)
    // 输入回调去抖动
    this.delaySearch = _.debounce(this.onSearch, 500);
    this.initBreadcrumb();
    // window.localStorage.setItem('deep',true)
  },

  methods: {
    initBreadcrumb() {
      let breadcrumbData = [
        {
          name: this.$t("txt_supplier"),
          path: "/site-supplier",
        },
      ];
      this.setBreadcrumb(breadcrumbData);
    },
    onCloseModal() {
      this.visible = false;
    },

    onClose() {
      this.visibleDraw = false
      this.showEdit = false
    },

    onEdit() {
      let appBuilder = this.$refs.ref_appBuilder;
      console.log('appBuilder', appBuilder.getValue())
      appBuilder.validate().then(() => {
        let params = appBuilder.getValue();
        // 创建基线
        updateSupplier
            .execute({
              ...params,
            })
            .then((res) => {
              this.visibleDraw = false
              this.showEdit = false
              this.fetchTable({});
            })
            .catch((err) => {
              this.$error(err.msg || this.$t("txt_create_supplier_fail"));
            });
      });
    },

    // 选择列回调
    onSelectChange(args) {
      // console.log(args);
      this.selectRow = args;
    },
    // 操作列回调
    onOperateClick(key, row) {
      if (key === "edit") {
        this.visibleDraw = true
        this.showEdit = true
        this.currentRow = row
      } else if (key === "detail") {
        this.visibleDraw = true
        this.showEdit = false
        this.currentRow = row
      } else if (key === "delete") {
        // console.log(row);
        this.onDelete(row);
      }
    },
    // 工具栏点击回调
    onToolClick({key, row}) {
      if (key === "create") {
        this.visible = true;
      } else if (key === "detail") {
        this.visibleDraw = true
        this.currentRow = row
      } else if (key === "edit") {
        this.visibleDraw = true
        this.showEdit = true
        this.currentRow = row
      } else if (key === "delete") {
        // console.log("this.selectedRows", this.selectedRows);
        this.fetchDelete(this.selectedRows);
      }
    },
    onPageChange(page, pageSize) {
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;
      this.fetchTable();
    },
    onSizeChange(pageSize, page) {
      this.pagerConfig.current = page;
      this.pagerConfig.pageSize = pageSize;
      this.fetchTable();
    },

    handleSupplierEdit(row) {
      this.$router.push({
        name: `supplier-detail`,
        path: `/supplier-detail`,
        query: {
          oid: row.oid,
          edit: true,
        },
      });
    },
    handleSupplierDetail(row) {
      this.$router.push({
        name: `supplier-detail`,
        path: `/supplier-detail`,
        query: {
          oid: row.oid,
          edit: false,
        },
      });
    },
    // 工具栏输入回调
    onToolInput({key}, value) {
      if (key === "search") {
        this.searchKey = value;
        this.pagerConfig = {
          current: 1,
          pageSize: 20,
          total: 0
        };
        this.delaySearch();
      }
    },
    // 删除
    onDelete(row) {
      this.fetchDelete([row]);
    },
    getAllTableData() {
      this.fetchTable()
    },
    // 数据请求函数
    fetchTable() {
      let {current, pageSize} = this.pagerConfig;
      let param = {
        searchKey: this.searchKey,//关键字（name/number）
        //containerOid: getCookie("tenantOid"),
        // pageNum: current,
        // pageSize: pageSize,
        index: current,
        size: pageSize
      };
      this.tableLoading = true;
      return supplierTableModel
          .execute(param)
          .then((data) => {
            console.log(data);
            this.tableLoading = false;
            this.getImgData(data.rows);
            this.tableData = data.rows;
            this.pagerConfig.total = data.count;
            this.total = data.count;
            return {data: data.rows, total: data.length};
          })
          .catch((err) => {
            this.tableLoading = false;
            this.$error(err.msg || this.$t("msg_failed"));
          });
    },

    getImgData(table) {
      table.forEach(item => {
        try{
          if (item.manufacturerLogo !== null && item.manufacturerLogo.length) {
            fetch(
                `${Jw.gateway}/${Jw.fileMicroServer}/file/downloadByOid?fileOid=${item.manufacturerLogo[0].oid}`,
                {
                  method: "get",
                  headers: {
                    "Content-Type": "application/json;charset=utf8",
                    appName: Jw.appName,
                    accesstoken: getCookie('token'),
                    tenantAlias: getCookie("tenantAlias"),
                    tenantOid: getCookie("tenantOid"),
                  },
                }
            )
                .then((response) => {
                  return response.blob();
                })
                .then((data) => {
                  let url = window.URL.createObjectURL(
                      new Blob([data], {
                        type: "application/vnd.ms-excel",
                      })
                  );
                  item.logo = url;
                  this.$forceUpdate();
                })
                .catch((err) => {
                  console.log(err)
                });
          }
        }catch(e){
          console.error(e)
        }
      })
    },
    // 删除列操作
    fetchDelete(row) {
      // console.log("当前删除操作的数据", row);
      let {tableData} = this;
      let oids = row.map((item) => item.oid);
      let param = oids
      this.$confirm({
        width: "280px",
        class: "deleteModal",
        closable: true,
        mask: false,
        title: (
            <p style="font-size:16px;font-weight:500;color:rgba(30, 32, 42, 0.85);">
              {this.$t("txt_delete")}
            </p>
        ),
        content: (
            <p style="font-size:14px;font-weight:400;color:rgba(30, 32, 42, 0.45);padding-right: 10px;">
              {tableData.length === 1 ? this.$t("txt_delete_supplier") : this.$t("txt_delete_supplier")}
            </p>
        ),
        cancelText: this.$t("btn_cancel"),
        okText: this.$t("btn_confirm"),
        onOk: () => {
          ModelFactory.create({
            url: `${Jw.gateway}/${Jw.partBomMicroServer}/supplier/batchDeleteByOid`,
            method: "post",
          })
              .execute(param)
              .then((data) => {
                // console.log(data);
                this.$success(this.$t("msg_success"));
                this.selectedRows = [];
                //刷新列表
                this.fetchTable({current: 1, pageSize: 10});
              })
              .catch((err) => {
                this.$error(err.msg || this.$t("msg_failed"));
              });
        },
      });
    },
    // 输入回调刷新表格数据
    onSearch() {
      this.$refs.ref_table.reFetchData();
    },

  },
};
</script>

<style lang="less" scoped>
.openBtn {
  float: right;
  margin: 10px 20px 0 0;
}

.delteIcon {
  cursor: pointer;
}
</style>
<style>
.deleteModal .ant-modal-body {
  padding: 24px;
}

.deleteModal .ant-modal {
  /* top: 112px;
  left: 42%; */
}

.deleteModal .ant-modal-close-x {
  line-height: 69px;
}

.deleteModal
.ant-modal-confirm-body
> .anticon
+ .ant-modal-confirm-title
+ .ant-modal-confirm-content {
  margin-left: 0;
}

.deleteModal .ant-modal-confirm-btns .ant-btn {
  width: 75px;
  float: right;
}

.deleteModal .ant-modal-confirm-btns .ant-btn.ant-btn-primary {
  margin-right: 8px;
  /* background-color: rgba(37, 94, 215, 1);
  border-color: rgba(37, 94, 215, 1); */
  /* background-color: #1890ff; */
}
</style>
