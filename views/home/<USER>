<template>
	<a-layout>
		<div class="header-left" v-if="setTitle&&setTitle.length>0">
			<a-breadcrumb separator=">">
				<a-breadcrumb-item v-for="(item, index) in setTitle" :key="index">
					<span style="font-size: 20px; color: rgba(30, 32, 42, 0.85); font-weight: 500;">
						{{ item.name }}
					</span>
					<span slot="headerIcon"></span>
				</a-breadcrumb-item>
			</a-breadcrumb>
		</div>
		<div :class="[setTitle&&setTitle.length>0?'fixed-header':'fixed-header']">
			<i class="jwi-iconsearch" @click="globalSearchVisible = true"></i>
		</div>
		<a-config-provider :locale="locale">
			<router-view />
		</a-config-provider>
		<jw-search-engine-modal :visible.sync="globalSearchVisible" />
	</a-layout>
</template>

<script>
import { jwHeader, jwPage, jwSearchEngineModal } from "jw_frame";
// import searchEngineModal from 'components/search-engine-modal';
// import Header from './header';
// import Userinfo from './userinfo';
// import More from './more';
// import Search from './search';
// import Breadcrumb from 'components/breadcrumb'
// import {getUserMenuPromisson} from '../../apis/view-permission/index';
// import routerList from "../../router/router-list"
// import { getCookie } from 'jw_utils/cookie';
// import {
//     fetchAccessToken,
//     fetchCurrentUser,
// } from 'apis/user/index';
import locale from "ant-design-vue/es/locale/zh_CN";

export default {
  name: "Main",
  components: {
    jwHeader,
    jwPage,
    jwSearchEngineModal
    // Userinfo,
    // More,
    // Breadcrumb,
    // Search
  },
  provide() {
    return {
      addBreadcrumb: this.addBreadcrumb,
      setBreadcrumb: this.setBreadcrumb,
      displayType: this.displayType
    };
  },
  watch: {
    // $route(newValue,value){
    //     if (newValue.fullPath != 'no-permission' && this.routerArr.every(v => v.path != newValue.fullPath))
    //     this.$router.push('/no-permission');
    // }
  },
  data() {
    return {
      breadcrumbData: [],
      setTitle: [],
      displayState: "",
      userInfo: null,
      listArr: [],
      routerArr: [],
      locale: locale,
      globalSearchVisible: false // 全局搜索弹窗
    };
  },
  created() {},
  methods: {
    displayType(data) {
      this.displayState = data;
    },
    addBreadcrumb(newItem) {
      this.breadcrumbData.push(newItem);
    },
    setBreadcrumb(data) {
      this.setTitle = data;
    },
    getAccessToken() {
      fetchAccessToken
        .execute()
        .then(data => {
          this.getCurrentUser(data.oid);
        })
        .catch(err => {
          if (err.msg) {
            this.$error(err.msg);
            if (err.code == "100011") {
              this.$router.push("/login");
            }
          }
        });
    },
    getCurrentUser(oid) {
      fetchCurrentUser
        .execute({ oid: oid })
        .then(data => {
          this.userInfo = data;
        })
        .catch(err => {
          if (err.msg) {
            this.$error(err.msg);
          }
        });
    }
  }
};
</script>
<style scoped>
/deep/.ant-layout {
  height: 100%;
}
</style>
<style scoped lang="less">
.header-left {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  min-height: 60px;
  line-height: 60px;
  padding: 0 24px;
  padding-right: 40px;
  background: #fff;
}
@fix-header-width: 380px;
.layout-content {
  margin-top: 1px;
}
.fixed-header {
  position: fixed;
  display: flex;
  align-items: center;
  top: 0;
  // right: 0;
  right: 28px;
  z-index: 100;
  // width: @fix-header-width;
  height: 60px;
  & > * {
    margin-left: 16px;
  }

  i {
    cursor: pointer;
    &::before {
      font-size: 20px;
    }
  }
}
.fixed-none-header {
  position: fixed;
  display: flex;
  align-items: center;
  top: 0;
  right: 0;
  z-index: 100;
  width: 110px;
  height: 60px;
  & > * {
    margin-left: 16px;
  }

  i {
    cursor: pointer;
    &::before {
      font-size: 20px;
    }
  }
}
.inners {
  display: inline-block;
}
</style>
