<template>
    <a-layout class="jw-height">
        <jw-aside :menuData="menuData"
                  class="theme"
                  :iconfont="true" />
        <Main />
    </a-layout>
</template>

<script>
import { jwAside } from "jw_frame";

import Main from "./main";

export default {
    name: "Home",
    components: {
        jwAside,
        Main,
    },
    data() {
        return {
            menuData: JSON.parse(sessionStorage.getItem("navigation")),
        };
    },
    created() {},
    methods: {},
};
</script>

<style scoped></style>
