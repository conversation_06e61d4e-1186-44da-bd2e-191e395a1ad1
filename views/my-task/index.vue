<template>
    <div class="my-task-wrap">
        <div class="head-wrap flex justify-between">
            <div class="flex">
                <a-input-search v-model.trim="searchKey" class="search-input" allow-clear
                    placeholder="输入关键词搜索" @search="onSearch" @blur="onSearch" />
            </div>
        </div>
        <div class="body-wrap">
            <a-table
                rowKey="oid" 
                :columns="columns" 
                :data-source="tableData"
                :pagination="false"
                :scroll="{ x: 800, y: 'calc(100vh - 300px)' }"
                bordered
                :loading="loading"
            >
                <template slot="status" slot-scope="text, record">
                    <div v-if="!record.isEdit"
                        :class="['status-tag',
                        text === 'overdue' ? 'status-red' :
                        text === 'doing' ? 'status-blue' :
                        text === 'notStarted' || text === 'cancel' ? 'status-primary' :
                        text === 'finished' || text === 'overdueFinished' ? 'status-green':
                        '']">
                        {{ text === 'notStarted' ? '未开始' :
                            text === 'doing' ? '进行中' :
                            text === 'finished' ? '已完成' :
                            text === 'overdueFinished' ? '逾期完成' :
                            text === 'overdue' ? '已逾期' :
                            text === 'cancel' ? '已取消' :
                            ''
                        }}
                    </div>
                    <div v-else>
                        <a-select v-model.trim="record.status" size="small" placeholder="请选择">
                            <a-select-option
                                v-for="item in statusList"
                                :key="item.value"
                                :value="item.value">
                                {{ item.label }}
                            </a-select-option>
                        </a-select>
                    </div>
                </template>
                <template slot="operation" slot-scope="text, record">
                    <div v-if="!record.isEdit" title="修改状态"
                        class="flex justify-center align-center">
                        <svg class="jwifont" aria-hidden="true"
                            @click="onEditStatus(record)">
                            <use xlink:href="#jwi-edit"></use>
                        </svg>
                    </div>
                    <div v-else class="flex align-center">
                        <div title="确认修改" class="flex align-center">
                            <svg class="jwifont" aria-hidden="true"
                                @click="onConfirmEdit(record)">
                                <use xlink:href="#jwi-check"></use>
                            </svg>
                        </div>
                        <div title="取消修改" class="flex align-center">
                            <svg class="jwifont" aria-hidden="true"
                                @click="onCancelEdit(record)">
                                <use xlink:href="#jwi-close"></use>
                            </svg>
                        </div>
                    </div>
                </template>
            </a-table>
        </div>
        <div class="foot-wrap">
            <a-pagination
                show-size-changer
                :default-current="page.currenrPage"
                :pageSize.sync="page.pageSize"
                :total="page.total"
                :show-total="total => `共 ${total} 条`"
                @change="onCurrentChange"
                @showSizeChange="onSizeChange"
                />
        </div>
    </div>
</template>

<script>
import {
    fetchTaskList,
    updateTaskStatus,
} from 'apis/myTask';
export default {
    name: 'myTaskList',
    components: {

    },
    inject: ['setBreadcrumb', 'addBreadcrumb'],
    data() {
        return {
            searchKey: '',
            loading: true,
            columns: [
				{
					title: '任务名称',
					dataIndex: 'name',
					key: 'name',
                    ellipsis: true,
				},
				{
					title: '任务描述',
					dataIndex: 'description',
					key: 'description',
					ellipsis: true,
				},
                {
					title: '处理人',
					dataIndex: 'handlerName',
					key: 'handlerName',
				},
                {
					title: '截止日期',
					dataIndex: 'endTime',
					key: 'endTime',
				},
				{
					title: '状态',
					dataIndex: 'status',
					key: 'status',
                    scopedSlots: { customRender: 'status' },
				},
				{
					title: '操作',
					dataIndex: 'operation',
					key: 'operation',
					width: 100,
                    align: 'center',
                    fixed: 'right',
					scopedSlots: { customRender: 'operation' },
				},
			],
            tableData: [],
            page: {
                currentPage: 1,
                pageSize: 20,
                total: 0,
            },
            statusList: [
                { label: '未开始' , value: 'notStarted' },
                { label: '进行中' , value: 'doing' },
                { label: '已完成' , value: 'finished' },
                { label: '逾期完成' , value: 'overdueFinished' },
                { label: '已逾期' , value: 'overdue' },
                { label: '已取消' , value: 'cancel' },
            ],
        }
    },
    mounted() {
        this.initBreadcrumb();
        this.getTaskList();
    },
    methods: {
        initBreadcrumb() {
            let breadcrumbData = [{ name: '我的任务', path: `/myTask` }];
            this.setBreadcrumb(breadcrumbData);
        },
        onSearch() {
            this.page.currentPage = 1;
            this.page.total = 0;
            this.getTaskList();
        },
        getTaskList() {
            this.loading = true;
            fetchTaskList.execute({
                oid: Jw.getUser().oid,
                searchKey: this.searchKey,
                currentPage: this.page.currentPage,
                pageSize: this.page.pageSize,
            }).then((res) => {
                this.tableData = res.data.map(item => {
                    item.isEdit = false;
                    item.statusCopy = item.status;
                    return item;
                });
                this.page.total = res.count;
                this.loading = false;
            }).catch((err) => {
                this.loading = false;
                if (err.msg) {
                    this.$error(err.msg);
                }
            });
        },
        onCurrentChange(page, pageSize) {
            this.page.currentPage = page;
            this.getTaskList();
        },
        onSizeChange(current, size) {
            this.page.pageSize = size;
            this.page.currentPage = 1;
            this.getTaskList();
        },
        onEditStatus(row) {
            row.isEdit = true;
        },
        onConfirmEdit(row) {
            updateTaskStatus.execute({
                oid: row.oid,
                modelType: row.modelType,
                status: row.status,
            }).then((res) => {
                this.$success('状态修改成功！');
                this.getTaskList();
            }).catch((err) => {
                if (err.msg) {
                    this.$error(err.msg);
                }
            });
        },
        onCancelEdit(row) {
            row.isEdit = false;
            row.status = row.statusCopy;
        },
    },
}  
</script>

<style lang="less" scoped>
.my-task-wrap {
    margin: 5px;
    height: calc(100vh - 64px - 40px);
    background: var(--light);
    box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
    border-radius: 4px;
    .jwifont {
        width: 16px;
        min-width: 16px;
        height: 16px;
        min-height: 16px;
    }
    .head-wrap {
        padding: 16px 20px;
        .search-input {
            width: 216px;
        }
    }
    .body-wrap {
        height: calc(100vh - 64px - 40px - 64px - 73px);
        padding: 0 20px 20px;
        /deep/.ant-table-thead > tr > th {
            padding: 9px 16px;
        }
        /deep/.ant-table-tbody > tr > td {
            padding: 12px 16px;
        }
        .jwifont {
            margin: 0 9px;
            cursor: pointer;
        }
        /deep/.ant-select {
            width: 110px;
        }
        /deep/.ant-select-sm .ant-select-selection--single {
            height: 22px;
        }
        /deep/.ant-select-sm .ant-select-selection__rendered {
            line-height: 20px;
        }
        .status-tag {
            width: fit-content;
            padding: 1px 8px;
            border-radius: 4px;
            font-size: 12px;
            &.status-primary {
                color: rgba(30, 32, 42, 0.65);
                border: 1px solid rgba(30, 32, 42, 0.15);
                background: rgba(30, 32, 42, 0.04);
            }
            &.status-green {
                color: #69c833;
                background: #f8fff0;
                border: 1px solid #ccedaf;
            }
            &.status-red {
                color: #f6445a;
                background: #fff0f0;
                border: 1px solid #ffc2c3;
            }
            &.status-blue {
                color: #255ed7;
                background: #f0f7ff;
                border: 1px solid #a4c9fc;
            }
        }
    }
    .foot-wrap {
        padding: 20px 12px 20px 20px;
		border-top: 1px solid rgba(30, 32, 42, 0.15);
        text-align: right;
    }
}
</style>
