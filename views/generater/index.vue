/**
 *@description ServiceComponentBuilder预览页面
 *
 *<AUTHOR>
 *@email <EMAIL>
 */

<template>
  <div class="component-generater-demo">
    <a-modal
      title="测试"
      width="800px"
      class="category-pop"
      :modal="false"
      :visible="true"
      @ok="ok"
    >
      <ServiceComponentBuilder
        :layoutName="getLayoutName()"
        :modelName="getModelName()"
        :type="getType()"
        ref="componentBuilder"
      >
      </ServiceComponentBuilder>
    </a-modal>
  </div>
</template>
  
<script>
import ServiceComponentBuilder from 'components/model-propertie/components/service-component-builder'

export default {
  components: {
    ServiceComponentBuilder
  },

  data() {
    return {
    }
  },
  methods: {
    getLayoutName() {
      return this.$route.params.layoutName
    },

    getModelName() {
      return this.$route.params.modelName
    },

    isModel() {
      let isClassify = this.$route.query.classify

      return _.isEmpty(isClassify)
    },

    getType() {
      return this.isModel() ? 'model' : 'classify'
    },
    ok() {
      let componentBuilder = this.$refs.componentBuilder
      componentBuilder.validate().then(() => {
        let value = componentBuilder.getValue()
        componentBuilder.clear()
      })
    }
  }
}
</script>
<style lang="less">
.component-generater-demo {
  height: 100%;
  padding: 15px;
  position: relative;
  box-sizing: border-box;

  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow-y: auto;
  background: #888;
}
</style>
