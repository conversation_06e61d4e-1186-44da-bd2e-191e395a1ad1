import ModelFactory from "jw_apis/model-factory"
//加载下拉列表
export const fetchGroupList = function (param) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.sysconfigServer}/preferences/setting/query-config`,
    method: "get",
  }).execute(param)
}


//检验value
export const validValue = (val) => {
  let pass = true
  val.split("|").forEach(item => {
    let value = item.split(";;;")
    if(value.length === 1 && !value[0]){
      pass = false
    }else if(value.length === 2 && !value[0] && !value[1]){
      pass = false
    }
  })
  return pass
}

