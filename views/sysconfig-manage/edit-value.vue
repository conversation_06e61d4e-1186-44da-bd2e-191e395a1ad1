<template>
  <a-drawer
    :title="$t('btn_edit')"
    :bodyStyle="bodyStyle"
    width="50%"
    :visible="visible"
    @close="onClose"
  >
    <div class="detail-drawer-wrap">
      <a-tabs>
        <a-tab-pane key="basicInfo" :tab="$t('detailed_info')"></a-tab-pane>
        <span
          style="cursor: pointer"
          slot="tabBarExtraContent"
          v-show="isEdit"
          @click="onEdit"
        >
          <jw-icon type="jwi-iconedit" />
        </span>
      </a-tabs>
      <div class="detail-drawer-body-wrap">
        <a-form-model
          ref="updateForm"
          class="form-container"
          layout="vertical"
          :model="form"
          :rules="rules"
        >
          <a-form-model-item label="名称" prop="name">
            <a-input
              v-model.trim="form.name"
              :disabled="isEdit"
              allow-clear
              placeholder="请输入名称"
              :maxLength="100"
            />
          </a-form-model-item>
          <a-form-model-item label="唯一标识符" prop="code">
            <a-input
              v-model.trim="form.code"
              allow-clear
              placeholder="请输入"
              :disabled="isEdit"
              :maxLength="64"
            />
          </a-form-model-item>
          <a-form-model-item label="值" prop="value">
            <div>
              <div
                v-for="(item, index) in valueobjlist"
                :key="index"
                class="value-line"
              >
                <a-input
                  size="small"
                  class="valueinput"
                  @blur="parsestr"
                  :disabled="isEdit"
                  v-model.trim="item.value"
                />-
                <a-input
                  size="small"
                  class="valueinput"
                  @blur="parsestr"
                  :disabled="isEdit"
                  v-model.trim="item.key"
                />
                <div
                  v-if="index < valueobjlist.length - 1 && !isEdit"
                  @click="deleteline(index)"
                  class="delete-btn"
                >
                  <jw-icon
                    type="jwi-icondelete"
                    style="cursor: pointer"
                  ></jw-icon>
                </div>
                <div class="delete-btn" v-else-if="!isEdit">
                  <span @click="addline">
                    <jw-icon
                      type="jwi-iconadd-circle"
                      style="cursor: pointer"
                    ></jw-icon>
                  </span>
                </div>
              </div>
            </div>
          </a-form-model-item>
          <a-form-model-item label="说明" prop="description">
            <a-input
              v-model.trim="form.description"
              allow-clear
              :disabled="isEdit"
              :maxLength="255"
            />
          </a-form-model-item>
          <a-form-model-item label="上下文" prop="containerModelType">
            <a-radio-group v-model.trim="form.containerModelType" :disabled="isEdit">
              <a-radio value="Site"> 站点 </a-radio>
              <a-radio value="Tenant"> 组织 </a-radio>
            </a-radio-group>
          </a-form-model-item>
        </a-form-model>
      </div>
    </div>
    <div class="detail-drawer-foot-wrap" v-show="!isEdit">
      <a-button type="primary" @click="onSave">
        {{ $t('btn_save') }}
      </a-button>
      <a-button class="btn-cancel" @click="onCancel">
        {{ $t('btn_cancel') }}
      </a-button>
    </div>
  </a-drawer>
</template>

<script>
import ModelFactory from 'jw_apis/model-factory'

// 更新
const updateMember = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.sysconfigServer}/preferences/setting/update-config`,
  method: 'post',
})
import { validValue } from "./api";
export default {
  name: 'memberDetail',
  props: ['visible', 'detailInfo', 'toEdit', 'childInfo'],
  data() {
    return {
      productList: [],
      bodyStyle: { padding: 0 },
      form: {},
      rules: {
        name: [
          {
            required: true,
            message: '请输入规则名称',
            trigger: 'change',
          },
        ],
        code: [
          {
            required: true,
            message: '请输入唯一标识符',
            trigger: 'change',
          },
        ],
        value: [
          {
            required: true,
            message: '请输入值',
            trigger: 'blur',
          },
        ],
        description: [
          {
            required: true,
            message: '请输入说明',
            trigger: 'change',
          },
        ],
        valueDataType: [
          {
            required: true,
            message: '请输入值类型',
            trigger: 'change',
          },
        ],
        containerModelType: [
          {
            required: true,
            message: '请选择上下文类型',
            trigger: 'change',
          },
        ],
      },
      isEdit: true,

      valueobjlist: [],
    }
  },
  watch: {
    visible(val) {
      if (val) {
        if (this.toEdit) this.isEdit = false
        this.form = { ...this.childInfo }
      }
    },
    'form.value': function (val) {
      console.log(val)
      this.parsearr()
    },
  },
  mounted() {},
  methods: {
    addline() {
      this.valueobjlist.push({ value: '', key: '' })
    },
    deleteline(index) {
      this.valueobjlist.splice(index, 1)
    },
    parsearr() {
      let val = this.form.value
      let valarr = val.split('|')
      let res = valarr.map((item) => {
        if (item.includes(';;;')) {
          let line = item.split(';;;')
          return {
            value: line[0],
            key: line[1],
          }
        } else {
          return { value: item, key: '' }
        }
      })
      this.valueobjlist = res
    },
    parsestr() {
      let strres = this.valueobjlist.map((item) => {
        let res = []
        if (item.value) {
          res.push(item.value)
        }
        if (item.key) {
          res.push(item.key)
        }
        return res.join(';;;')
      })
      let str = strres.join('|')
      return str
    },
    handleSelectChange(e) {},
    onClose() {
      this.isEdit = true
      this.$refs.updateForm.clearValidate()
      this.$emit('close')
    },
    onEdit() {
      this.isEdit = false
    },
    onCancel() {
      this.isEdit = true
      this.$refs.updateForm.clearValidate()
      this.$emit('close')
    },
    onSave() {
      let { detailInfo, childInfo, form } = this
      this.$refs.updateForm.validate((valid) => {
        if (valid) {
          if (!validValue(this.parsestr())) {
            this.$warning('值不能有空行')
            return
          }
          let itemIndex = detailInfo.itemDTOList.findIndex(
            (items) => items.oid == childInfo.oid
          )
          detailInfo.itemDTOList[itemIndex].name = form.name
          detailInfo.itemDTOList[itemIndex].value = this.parsestr()
          detailInfo.itemDTOList[itemIndex].description = form.description
          detailInfo.itemDTOList[itemIndex].code = form.code
          detailInfo.itemDTOList[itemIndex].hasSystem = true
          detailInfo.itemDTOList[itemIndex].status = 'enable'
          detailInfo.itemDTOList[itemIndex].containerModelType =
            form.containerModelType
          let params = {
            ...detailInfo,
          }
          updateMember
            .execute(params)
            .then((res) => {
              this.$success(this.$t('msg_update_success'))
              this.isEdit = true
              this.$emit('getTableData')
            })
            .catch((err) => {
              this.$emit('getTableData')
              this.isEdit = false
              if (err.msg) {
                this.$error(err.msg)
              }
            })
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
.value-line {
  display: flex;
  margin-bottom: 6px;
  .valueinput {
  }
  .delete-btn {
    margin-left: 10px;
  }
}

.addlinebtn {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
</style>
