<template>
  <div class="base-panel page-container">
    <div class="product-config">
      <modular-list></modular-list>
    </div>
  </div>
</template>

<script>
import modularList from './modular-list.vue'

export default {
  name: 'product-config',
  inject: ['setBreadcrumb'],
  components: {
    modularList,
  },
  data() {
    return {}
  },
  created() {
    this.setBreadcrumb([{ name: '系统配置管理', path: '/sysconfig-manage' }])
  },
  computed: {},
  mounted() {},
  methods: {},
}
</script>

<style lang="less" scoped>
.page-container {
  width: 100%;
  .product-config {
    display: flex;
    justify-content: flex-start;
    flex-wrap: nowrap;
    height: calc(~'100% - 53px');
  }
}
</style>
