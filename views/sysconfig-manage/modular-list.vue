<template>
    <div class="main-group-page">
        <modular-group ref="group" class="group"/>
        <modular-table ref="modtable" class="item"></modular-table>
    </div>
</template>

<script>
import ModularGroup from './modular-group.vue';
import modularTable from "./modular-table.vue";

export default {
    name: "modular-list",
    components: {
        modularTable,
        ModularGroup,
    },
    data() {
        return {};
    },
    created() {},
    computed: {},
    mounted() {},
    methods: {},
};
</script>

<style lang="less" scoped>
.main-group-page{
    display: flex;
    width: 100%;
}
.group{
    width: 280px;
}
.item{
    width: calc(~"100% - 280px");
}
</style>
