<template>
  <div class="main-page-table">
    <div style="margin-left: 16px; display: flex; justify-content: flex-start">
      <a-button
        style="margin-right: 12px"
        type="primary"
        @click.stop="createChild"
      >
        新建
      </a-button>
    </div>
    <jw-table
      ref="refTable"
      row-id="oid"
      class="table-height"
      :panel="true"
      :columns="columns"
      :showPage="false"
      :data-source="tableData"
      @onToolClick="onToolClick"
      @onToolInput="onToolInput"
      @onOperateClick="onOperateClick"
    >
      <template #name="{ row }">
        {{ row.name }}
      </template>
      <template #isStatus="{ row }">
        {{ row.status === 'enable' ? '否' : '是' }}
      </template>
      <template #modelType="{ row }">
        {{ row.containerModelType === 'Site' ? '站点' : '组织' }}
      </template>
      <template #valueslot="{ row }">
        {{ formattervalue(row.value) }}
      </template>
    </jw-table>
    <create-value
      :visible="createVisible"
      :detailInfo="detailInfo"
      @getTableData="reloaddatalist"
      @close="onCreateClose"
    ></create-value>
    <edit-value
      :visible="eidtVisible"
      :detailInfo="detailInfo"
      :childInfo="childInfo"
      :toEdit="toValueEdit"
      @getTableData="reloaddatalist"
      @close="onEditClose"
    ></edit-value>
  </div>
</template>

<script>
import { jwAvatar, jwIcon } from 'jw_frame'
import createValue from './create-value.vue'
import editValue from './edit-value.vue'
import { baseDelete } from '@jw/jw-frame/packages/utils/baseaction'
export default {
  name: 'jwMember',
  components: {
    jwAvatar,
    jwIcon,
    createValue,
    editValue,
  },
  data() {
    return {
      searchKey: '',
      tableData: [],
      isShow: true,
      tableLoading: false,
      detailVisible: false,
      detailInfo: {},
      childInfo: {},
      createVisible: false,
      eidtVisible: false,
      toValueEdit: false,
    }
  },
  computed: {
    columns() {
      return [
        {
          field: 'name',
          title: '规则名称',
        },
        {
          field: 'code',
          title: '唯一标识符',
        },
        {
          field: 'value',
          title: '值',
          slots: {
            default: 'valueslot',
          },
        },
        {
          field: 'containerModelType',
          title: '上下文',
          slots: {
            default: 'modelType',
          },
        },
        {
          field: 'description',
          title: '说明',
        },
        {
          field: 'operation',
          title: this.$t('txt_operation'),
          btns: [
            {
              icon: 'jwi-iconedit',
              title: this.$t('btn_edit'),
              key: 'edit',
            },
            {
              title: this.$t('txt_delete'),
              key: 'delete',
              icon: 'jwi-icondelete',
            },
          ],
        },
      ]
    },
  },
  created() {},
  mounted() {},
  methods: {
    formattervalue(val) {
      if (val) {
        return val.replace(/;;;/g, '-').replace(/\|/g, ',\n')
      }
      return ''
    },
    reloaddatalist() {
      this.$parent.$refs.group.onSearch()
    },
    onToolClick({ key }) {
      if (key === 'create') {
        this.visible = true
      }
    },
    onOperateClick(key, row) {
      switch (key) {
        case 'edit':
          this.childInfo = Object.assign({}, row)
          this.eidtVisible = true
          break
        case 'delete': {
          baseDelete({oid: row.oid, type: 'ConfigItem'}, this.reloaddatalist)
          break;
        }
        default:
          break
      }
    },
    handleEdit(detailInfo) {
      this.detailInfo = detailInfo
      this.detailVisible = true
    },
    createChild() {
      this.createVisible = true
    },
    onToolInput({ key }, value) {
      if (key === 'search') {
        this.searchKey = value
        this.onSearch()
      }
    },
    onCreateClose() {
      this.createVisible = false
    },
    onEditClose() {
      this.eidtVisible = false
    },
  },
}
</script>

<style lang="less" scoped>
.main-page-table {
}
</style>
