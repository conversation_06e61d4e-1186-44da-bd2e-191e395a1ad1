<template>
  <div class="page-index">
    <a-input-search
      style="width: 100%"
      v-model.trim="searchKey"
      placeholder="请输入组名，规则名称或唯一标识符"
      @search="onSearch"
    />
    <div class="btn-group">
      <div class="groupName">
        {{ $t('txt_group') }}
      </div>

      <div class="add-btn-line">
        <excelimport
          type="preferences"
          :search="searchKey"
          @reloadListData="onSearch"
        />
        <a-button shape="circle" @click="addgroup">
          <jw-icon type="jwi-iconadd"></jw-icon>
        </a-button>
      </div>
    </div>
    <div class="list-data">
      <a-spin
        :spinning="spining"
        v-if="spining"
        style="margin-top: 50px; margin-left: 130px"
      >
      </a-spin>
      <a-menu v-else mode="vertical" @click="handlemenuClick" v-model.trim="menukey">
        <a-menu-item v-for="item in datalist" :key="item.oid">
          <div class="menuline">
            <a-tooltip :title="item.name">
            <div class="menuName">{{ item.name }}</div>
            </a-tooltip>
            <span class="menu-icon" @click="editinfo(item)">
              <jw-icon type="jwi-iconedit"></jw-icon>
            </span>
            &nbsp;
            <span class="menu-icon" @click="deleteFun(item)">
              <jw-icon type="jwi-icondelete"></jw-icon>
            </span>
          </div>
        </a-menu-item>
      </a-menu>
    </div>

    <create-modular
      :visible="visible"
      @getTableData="onSearch"
      @close="visible = false"
    ></create-modular>
    <edit-modular
      :visible="detailVisible"
      :detailInfo="detailInfo"
      :toEdit="toEdit"
      @getTableData="onSearch"
      @close="detailVisible = false"
    ></edit-modular>
  </div>
</template>

<script>
import excelimport from '@jw/jw-frame/packages/modules/excelimport'
import { baseDelete } from '@jw/jw-frame/packages/utils/baseaction'
import { fetchGroupList } from './api'
import createModular from './create-modular.vue'
import editModular from './edit-modular.vue'
export default {
  components: {
    excelimport,
    createModular,
    editModular,
  },
  data() {
    return {
      searchKey: '',
      visible: false,
      detailVisible: false,
      toEdit: false,
      detailInfo: null,
      spining: false,

      datalist: [],
      menukey: null,
    }
  },
  created() {
    this.loadgroup()
  },
  methods: {
    addgroup() {
      this.visible = true
    },
    editinfo(row) {
      this.detailInfo = Object.assign({}, row)
      this.detailVisible = true
    },
    //删除
    deleteFun(row){
      baseDelete({oid: row.oid, type: 'ConfigGroup'}, this.loadgroup)
    },
    handlemenuClick({ key }) {
      let row = this.datalist.find((item) => item.oid === key)
      if (row) {
        this.$parent.$refs.modtable.tableData = row.itemDTOList
        this.$parent.$refs.modtable.detailInfo = row
      }else{
        this.$parent.$refs.modtable.tableData = []
        this.$parent.$refs.modtable.detailInfo = null
      }
    },
    validHaveKey(){
      if(this.menukey){
        let row = this.datalist.find((item) => item.oid === this.menukey[0])
        if(row){
          return this.menukey
        }
      }
    },
    onSearch() {
      this.loadgroup()
    },
    loadgroup() {
      this.spining = true
      let param = {
        searchKey: this.searchKey,
        keyword: this.searchKey,
      }
      fetchGroupList(param)
        .then((resp) => {
          this.datalist = resp
          if (this.datalist.length > 0) {
            this.menukey = this.validHaveKey() ?? [this.datalist[0].oid]
            this.handlemenuClick({ key: this.menukey[0] })
          }else{
            this.handlemenuClick({ key: null })
          }
        })
        .catch((e) => {
          console.error(e)
          this.$error(e.msg)
        })
        .finally(() => {
          this.spining = false
        })
    },
  },
}
</script>

<style scoped lang="less">
.menuline{
  display: flex;
  justify-content: space-between;
}
.menuline:hover{
  .menu-icon{
    display: flex;
  }
}
.menu-icon{
  display: none;
}
.list-data {
  height: calc(~'100vh - 150px');
  overflow: scroll;
}
.page-index {
  padding-left: 10px;
}
.groupName {
  font-size: 17px;
  margin-left: 8px;
}

.btn-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 5px;
  width: 100%;
  border-bottom: 1px solid #ddd;
}

.add-btn-line {
  display: flex;

  /deep/ .ant-btn {
    border: 0;
    box-shadow: 0 0;
  }
}

.menuName{
  width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/deep/ .ant-menu-inline,
.ant-menu-vertical,
.ant-menu-vertical-left {
  border: 0;
}
</style>