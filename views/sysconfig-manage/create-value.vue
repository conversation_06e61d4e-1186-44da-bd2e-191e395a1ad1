<template>
  <a-modal
    :visible="visible"
    title="添加配置项规则"
    :mask-closable="false"
    :footer="null"
    @cancel="onCancel"
  >
    <a-form-model
      ref="addForm"
      class="form-container"
      layout="vertical"
      :model="form"
      :rules="rules"
    >
      <a-form-model-item label="规则名称" prop="name">
        <a-input v-model.trim="form.name" allow-clear placeholder="请输入" :maxLength="100"/>
      </a-form-model-item>
      <a-form-model-item label="唯一标识符" prop="code">
        <a-input v-model.trim="form.code" allow-clear placeholder="请输入" :maxLength="64"/>
      </a-form-model-item>
      <a-form-model-item label="值" prop="value">
        <div>
          <div
            v-for="(item, index) in valueobjlist"
            :key="index"
            class="value-line"
          >
            <a-input
              size="small"
              class="valueinput"
              @blur="parsestr"
              v-model.trim="item.value"
            />-
            <a-input
              size="small"
              class="valueinput"
              @blur="parsestr"
              v-model.trim="item.key"
            />
            <div
              v-if="index < valueobjlist.length - 1"
              @click="deleteline(index)"
              class="delete-btn"
            >
              <jw-icon type="jwi-icondelete" style="cursor: pointer"></jw-icon>
            </div>
            <div v-else class="delete-btn">
              <span @click="addline">
                <jw-icon
                  type="jwi-iconadd-circle"
                  style="cursor: pointer"
                ></jw-icon>
              </span>
            </div>
          </div>
        </div>
      </a-form-model-item>
      <a-form-model-item label="说明" prop="description">
        <a-input v-model.trim="form.description" allow-clear placeholder="请输入" :maxLength="255"/>
      </a-form-model-item>
      <a-form-model-item label="上下文" prop="containerModelType">
        <a-radio-group v-model.trim="form.containerModelType">
          <a-radio value="Site"> 站点 </a-radio>
          <a-radio value="Tenant"> 组织 </a-radio>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item class="form-item-btns">
        <a-button type="primary" @click="onCreate"> 新建 </a-button>
        <a-button class="form-btn-cancel" @click="onCancel"> 取消 </a-button>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
import ModelFactory from 'jw_apis/model-factory'
import { validValue } from './api'
// 添加售后产品
const createMember = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.sysconfigServer}/preferences/setting/update-config`,
  method: 'post',
})

export default {
  name: 'addMemberModal',
  props: ['visible', 'detailInfo'],
  data() {
    return {
      form: {
        name: '',
        code: '',
        value: '',
        description: '',
        valueDataType: 'input',
        containerModelType: 'Site',
      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入规则名称',
            trigger: 'change',
          },
        ],
        code: [
          {
            required: true,
            message: '请输入唯一标识符',
            trigger: 'change',
          },
          {
            pattern: /^[\w_\-][\w_\-.]*[\w_\-]+$/,
            message: this.$t('code_placeholder'),
            trigger: 'change',
          },
        ],
        value: [
          {
            required: true,
            message: '请输入值',
            trigger: 'blur',
          },
        ],
        description: [
          {
            required: true,
            message: '请输入说明',
            trigger: 'change',
          },
        ],
        valueDataType: [
          {
            required: true,
            message: '请输入值类型',
            trigger: 'change',
          },
        ],
        containerModelType: [
          {
            required: true,
            message: '请选择上下文类型',
            trigger: 'change',
          },
        ],
      },
      productList: [],

      valueobjlist: [{ value: '', key: '' }],
    }
  },
  mounted() {},
  methods: {
    parsearr() {
      let val = this.form.value
      let valarr = val.split('|')
      let res = valarr.map((item) => {
        if (item.includes(';;;')) {
          let line = item.split(';;;')
          return {
            value: line[0],
            key: line[1],
          }
        } else {
          return { value: item, key: '' }
        }
      })
      this.valueobjlist = res
    },
    parsestr() {
      let strres = this.valueobjlist.map((item) => {
        let res = []
        if (item.value) {
          res.push(item.value)
        }
        if (item.key) {
          res.push(item.key)
        }
        return res.join(';;;')
      })
      let str = strres.join('|')
      this.form.value = str
    },
    addline() {
      this.valueobjlist.push({ value: '', key: '' })
    },
    deleteline(index) {
      this.valueobjlist.splice(index, 1)
    },
    handleSelectChange(e) {},
    onCreate() {
      let { detailInfo } = this
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          if (!validValue(this.form.value)) {
            this.$warning('值不能有空行')
            return
          }
          let newChild = {
            name: this.form.name,
            value: this.form.value,
            code: this.form.code,
            containerModelType: this.form.containerModelType,
            valueDataType: this.form.valueDataType,
            description: this.form.description,
            hasSystem: true,
            status: 'enable',
          }
          if (detailInfo.itemDTOList) {
            detailInfo.itemDTOList.push(newChild)
          } else {
            detailInfo.itemDTOList = [newChild]
          }
          let params = {
            ...detailInfo,
          }
          createMember
            .execute(params)
            .then((res) => {
              this.$success(this.$t('msg_save_success'))
              this.onCancel()
              this.$emit('getTableData')
            })
            .catch((err) => {
              this.$emit('getTableData')
              if (err.msg) {
                this.$error(err.msg)
              }
            })
        } else {
          return false
        }
      })
    },
    onCancel() {
      this.$refs.addForm.resetFields()
      this.$refs.addForm.clearValidate()
      ;(this.valueobjlist = [{ value: '', key: '' }]), this.$emit('close')
    },
  },
}
</script>

<style lang="less" scoped>
.value-line {
  display: flex;
  margin-bottom: 6px;
  .valueinput {
  }
  .delete-btn {
    margin-left: 10px;
  }
}
</style>
