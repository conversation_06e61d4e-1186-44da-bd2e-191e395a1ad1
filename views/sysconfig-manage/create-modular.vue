<template>
  <a-modal
    :visible="visible"
    title="新建首选项"
    :mask-closable="false"
    :footer="null"
    @cancel="onCancel"
  >
    <a-form-model
      ref="addForm"
      class="form-container"
      layout="vertical"
      :model="form"
      :rules="rules"
    >
      <a-form-model-item label="名称" prop="name">
        <a-input v-model.trim="form.name" allow-clear placeholder="请输入" :maxLength="100"/>
      </a-form-model-item>
      <a-form-model-item label="备注" prop="description">
        <a-input v-model.trim="form.description" allow-clear placeholder="请输入" :maxLength="255"/>
      </a-form-model-item>
      <a-form-model-item class="form-item-btns">
        <a-button type="primary" @click="onCreate"> 新建 </a-button>
        <a-button class="form-btn-cancel" @click="onCancel"> 取消 </a-button>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
import ModelFactory from 'jw_apis/model-factory'
// 添加售后产品
const createMember = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.sysconfigServer}/preferences/setting/create`,
  method: 'post',
})

export default {
  name: 'addMemberModal',
  props: ['visible'],
  data() {
    return {
      form: {
        name: '',
        containerModelType: 'Site',
      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入首选项名称',
            trigger: 'change',
          },
        ],
      },
      productList: [],
    }
  },
  mounted() {},
  methods: {
    handleSelectChange(e) {},
    onCreate() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          let params = {
            name: this.form.name,
            description: this.form.description,
            status: this.form.status === true ? 'disable' : 'enable',
            itemDTOList: [],
            // hasSystem: true,
            // containerModelType: this.form.containerModelType,
            // code: this.form.code,
          }
          createMember
            .execute(params)
            .then((res) => {
              this.$success(this.$t('msg_save_success'))
              this.onCancel()
              this.$emit('getTableData')
            })
            .catch((err) => {
              console.log(err)
              if (err.msg) {
                this.$error(err.msg)
              }
            })
        } else {
          return false
        }
      })
    },
    onCancel() {
      this.$refs.addForm.resetFields()
      this.$refs.addForm.clearValidate()
      this.$emit('close')
    },
  },
}
</script>

<style lang="less" scoped>
</style>
