<template>
    <a-drawer
        :title="$t('btn_edit')"
        :bodyStyle="bodyStyle"
        width="50%"
        :visible="visible"
        @close="onClose"
    >
        <div class="detail-drawer-wrap">
            <a-tabs>
                <a-tab-pane
                    key="basicInfo"
                    :tab="$t('detailed_info')"
                ></a-tab-pane>
                <span
                    style="cursor: pointer"
                    slot="tabBarExtraContent"
                    v-show="isEdit"
                    @click="onEdit"
                >
                    <jw-icon type="jwi-iconedit" />
                </span>
            </a-tabs>
            <div class="detail-drawer-body-wrap">
                <a-form-model
                    ref="updateForm"
                    class="form-container"
                    layout="vertical"
                    :model="form"
                    :rules="rules"
                >
                    <a-form-model-item label="名称" prop="name">
                        <a-input
                            v-model.trim="form.name"
                            :disabled="isEdit"
                            allow-clear
                            placeholder="请输入名称"
                            :maxLength="100"
                        />
                    </a-form-model-item>
                    <!-- <a-form-model-item label="上下文" prop="containerModelType">
                        <a-radio-group
                            v-model.trim="form.containerModelType"
                            :disabled="isEdit"
                        >
                            <a-radio value="Site"> 站点 </a-radio>
                            <a-radio value="Tenant"> 组织 </a-radio>
                        </a-radio-group>
                    </a-form-model-item> -->
                    <!-- <a-form-model-item label="值" prop="code">
                        <a-input
                            v-model.trim="form.code"
                            allow-clear
                            placeholder="请输入"
                            :disabled="isEdit"
                        />
                    </a-form-model-item> -->
                    <!-- <a-form-model-item label="已锁定" prop="status">
                        <a-checkbox
                            :value="form.status === 'enable'"
                            :disabled="isEdit"
                            :checked="form.status === 'enable'"
                        />
                    </a-form-model-item> -->
                    <a-form-model-item label="备注" prop="description">
                        <a-input
                            v-model.trim="form.description"
                            allow-clear
                            :disabled="isEdit"
                            :maxLength="255"
                        />
                    </a-form-model-item>
                </a-form-model>
            </div>
        </div>
        <div class="detail-drawer-foot-wrap" v-show="!isEdit">
            <a-button type="primary" @click="onSave">
                {{ $t("btn_save") }}
            </a-button>
            <a-button class="btn-cancel" @click="onCancel">
                {{ $t("btn_cancel") }}
            </a-button>
        </div>
    </a-drawer>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";

// 更新
const updateMember = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.sysconfigServer}/preferences/setting/update-config  `,
    method: "post",
});

export default {
    name: "memberDetail",
    props: ["visible", "detailInfo", "toEdit"],
    data() {
        return {
            productList: [],
            bodyStyle: { padding: 0 },
            form: {},
            rules: {
                name: [
                    {
                        required: true,
                        message: this.$t("msg_input"),
                        trigger: "change",
                    },
                    {
                        max: 50,
                        message: this.$t("txt_prompt"),
                        trigger: "change",
                    },
                ],
            },
            isEdit: true,
        };
    },
    watch: {
        visible(val) {
            if (val) {
                if (this.toEdit) this.isEdit = false;
                this.form = { ...this.detailInfo };
            }
        },
    },
    mounted() {},
    methods: {
        handleSelectChange(e) {},
        onClose() {
            this.isEdit = true;
            this.$refs.updateForm.clearValidate();
            this.$emit("close");
        },
        onEdit() {
            this.isEdit = false;
        },
        onCancel() {
            this.isEdit = true;
            this.$refs.updateForm.clearValidate();
            this.form = { ...this.detailInfo };
        },
        onSave() {
            this.$refs.updateForm.validate((valid) => {
                if (valid) {
                    updateMember
                        .execute(this.form)
                        .then((res) => {
                            this.$success(this.$t("msg_update_success"));
                            this.isEdit = true;
                            this.$emit("getTableData");
                        })
                        .catch((err) => {
                            this.isEdit = false;
                            if (err.msg) {
                                this.$error(err.msg);
                            }
                        });
                } else {
                    return false;
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
</style>
