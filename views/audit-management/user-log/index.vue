<template>
	<div class="baselist-info">
		<div class="search-main">
			<a-date-picker
				v-model.trim="startValue"
				:disabled-date="disabledStartDate"
				show-time
				format="YYYY-MM-DD"
				placeholder="起始时间"
				@openChange="handleStartOpenChange"
			/>
			～
			<a-date-picker
				v-model.trim="endValue"
				:disabled-date="disabledEndDate"
				show-time
				format="YYYY-MM-DD"
				placeholder="结束时间"
				:open="endOpen"
				@openChange="handleEndOpenChange"
			/>
			<!-- <a-range-picker :size="size" 
      @change="selectDate"
       :disabledDate="disabledDate" /> -->
			<div class="search-content">
				<a-input-search
					placeholder="输入搜索关键词"
					class="seach-input"
					@search="onSearch"
				/>
			</div>
			<a-button type="">导出</a-button>
		</div>
		<div class="maintable">
			<a-table
				:columns="columns"
				:row-selection="rowSelection"
				rowKey="oid"
				:pagination="pagination"
				:style="{ height: fullHeight - 125 + 'px' }"
				:scroll="{ y: fullHeight - 190 }"
				:data-source="logList"
			>
				<template slot="permissionsVoList" slot-scope="permissionsVoList">
					<span>{{ permissionsVoList }}</span>
				</template>
			</a-table>

			<div class="card-pagination text-center">
				<a-pagination
					:default-current="pages.pageSize"
					:total="total"
					:show-total="(total) => `共 ${total} 条`"
					show-size-changer
					@change="handleCurrentChange"
					@showSizeChange="handleSizeChange"
				/>
			</div>
		</div>
	</div>
</template>

<script>
import moment from "moment"
import formModal from "components/form-modal.vue"
import { getLoglist } from "apis/log/index"

export default {
	name: "todoTasks",
	inject: ["setBreadcrumb", "addBreadcrumb"],
	components: {
		formModal,
	},
	data() { 
		return {
			logList: [],
			startValue: null,
			endValue: null,
			endOpen: false,
			size: "default",
			pagination: false,
			modalVisible: false,
			pages: {
				page: 1,
				size: 10,
				searchKey: "",
				type: "user",
				startTime: "",
				endTime: "",
			},

			total: 0,
			oid: "",
			columns: [
				{
					title: "登录名",
					width: 100,
					dataIndex: "creatorName",
					key: "creatorName",
				},
				{
					title: "模块名称",
					dataIndex: "moduleName",
					key: "moduleName",
					sorter: true,
					ellipsis: true,
					width: 150,
				},
				{
					title: "操作名称",
					dataIndex: "menuName",
					key: "menuName",
					sorter: true,
					ellipsis: true,
					scopedSlots: { customRender: "name" },
				},
				{
					title: "操作描述",
					dataIndex: "menuDescription",
					key: "menuDescription",
					sorter: true,
				},
				{
					title: "操作结果",
					dataIndex: "accessResult",
					key: "accessResult",
					sorter: true,
					scopedSlots: { customRender: "permissionsVoList" },
				},
				{
					title: "操作时间",
					dataIndex: "accessTime",
					key: "accessTime",
					sorter: true,
				},
				{
					title: "访问IP地址",
					dataIndex: "ip",
					key: "ip",
					sorter: true,
				},
				{
					title: "异常信息",
					dataIndex: "errorMessage",
					key: "errorMessage",
					sorter: true,
				},
			],
			tableData: [],
			fullHeight: document.documentElement.clientHeight - 104,
		}
	},
	watch: {
		startValue(startValue) {
			this.pages.startTime = this.time(startValue._d)
		},
		endValue(endValue) {
			this.pages.endTime = this.time(endValue._d)
		},
	},

	computed: {
		rowSelection() {
			return {
				onChange: (selectedRowKeys, selectedRows) => {
					this.selectArrId = selectedRowKeys
					console.log(selectedRowKeys)
					console.log(
						`selectedRowKeys: ${selectedRowKeys}`,
						"selectedRows: ",
						selectedRows
					)
				},
				getCheckboxProps: (record) => ({
					props: {
						disabled: record.name === "Disabled User", // Column configuration not to be checked
						name: record.name,
					},
				}),
			}
		},
	},
	methods: {
		initBreadcrumb() {
			let breadcrumbData = [{ name: "普通用户日志", path: "/user-average/userLog" }]
			this.setBreadcrumb(breadcrumbData)
		},
		disabledStartDate(startValue) {
			const endValue = this.endValue
			if (!startValue || !endValue) {
				return false
			}
			return startValue.valueOf() > endValue.valueOf()
		},
		disabledEndDate(endValue) {
			const startValue = this.startValue
			if (!endValue || !startValue) {
				return false
			}
			return startValue.valueOf() >= endValue.valueOf()
		},
		handleStartOpenChange(open) {
			if (!open) {
				this.endOpen = true
			}
		},
		handleEndOpenChange(open) {
			this.endOpen = open
			if (this.startValue && this.endValue) {
				this.getPermisssion()
			}
		},

		//选择日期
		// selectDate(date, dateString) {
		//   this.pages.startTime=dateString[0]
		//   this.pages.endTime=dateString[1]
		//   this.getPermisssion();
		// },
		// //禁用今天之后的日期
		// disabledDate(current) {
		//     return current > moment().subtract(0, 'day')//今天之后的年月日不可选，不包括今天
		// },

		//获取日志列表
		getPermisssion() {
			console.log(this.pages)
			getLoglist
				.execute(this.pages)
				.then((res) => {
					this.logList = res.rows
					this.total = res.count
				})
				.catch((err) => {
					this.$error(err.msg || "请求失败")
				})
		},

		//搜索
		onSearch(val) {
			this.pages.searchKey = val
			this.getPermisssion()
		},

		handleCurrentChange(page, size) {
			this.pages.page = page
			this.pages.size = size
			this.getPermisssion()
		},

		handleSizeChange(curren, size) {
			this.pages.page = curren
			this.pages.size = size
			this.getPermisssion()
		},
		time(date) {
			let y = date.getFullYear()
			let m = date.getMonth() + 1
			m = m < 10 ? "0" + m : m
			let d = date.getDate()
			d = d < 10 ? "0" + d : d
			return y + "-" + m + "-" + d
		},
	},

	created() {
    this.initBreadcrumb();
		this.getPermisssion()
		//   this.getPermissionList()
	},
}
</script>

<style scoped>
.search-info-right span img {
	width: 16px;
	height: 16px;
}
.search-info-right span.current {
	border: 1px solid #ddd;
	border-radius: 4px;
	padding: 0 7px;
}
.search-info-right span {
	height: 32px;
	line-height: 32px;
	display: block;
	box-sizing: border-box;
}
.search-info-right {
	display: flex;
}
.workflow-icon {
	margin-right: 8px;
}
.link-name {
	color: #255ed7;
	cursor: pointer;
}
.page-list {
	display: flex;
	justify-content: flex-end;
}
.page-info {
	margin: 0;
	padding: 19px 16px 3px 42px;
	box-sizing: border-box;
	border-top: 1px solid rgba(30, 32, 42, 0.15);
	display: flex;
	justify-content: flex-end;
	height: 70px;
}
.operation-btn {
	display: flex;
	align-items: center;
}
.operation-btn a {
	margin-right: 16px;
}
.ellipsis-icon {
	width: 16px;
	height: 16px;
	cursor: pointer;
}
.ellipsis {
	width: 16px;
	height: 16px;
}
.add-new-btn {
	margin-bottom: 0px;
	margin-top: 0px;
	float: right;
}
.group-titles {
	font-size: 16px;
	color: #292a2c;
	font-weight: 600;
	height: 46px;
	line-height: 46px;
	display: block;
}
.product-info {
	display: flex;
	align-items: center;
}
.icon-product {
	width: 15px;
	height: 15px;
	margin-right: 10.5px;
}
.maintable {
	padding: 0px 20px 15px 20px;
	box-sizing: border-box;
	flex: 1;
	overflow-y: auto;
}
.export-btn {
	display: flex;
}
.seach-input {
	width: 216px;
}
.search-content {
	width: 100%;
	display: flex;
	align-items: center;
	margin-left: 10px;
}
.search-main {
	display: flex;
	/* justify-content: space-between; */
	align-items: center;
	box-sizing: border-box;
	padding: 10px 20px;
	height: 52px;
}
.baselist-info {
	padding: 0;
	display: flex;
	flex-direction: column;
	margin: 5px;
	background: var(--light);
	box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
	border-radius: 4px;
}
</style>
