<template>
  <div class="all-background">
    <jw-table ref="ref_table" disableCheck="disableCheck"
              :data-source.sync="tableData" :columns="getHeader"
              :selectedRows.sync="selectedRows" :fetch="fetchTable"
              :toolbars="toolbars" @onToolClick="onToolClick"
              @onToolInput="onToolInput" @checkbox-change="onSelectChange"
              @onOperateClick="onOperateClick" >
      <template #containerModelType="{ row }">
        {{ row.containerModelType == "Site" ? $t("txe_site") : $t("txe_tenant") }}
      </template>
      <!--  <template slot="tool-after-start">
          <a-button v-show="selectedRows.length > 0" style="background: #ffffff; color: #f81d22; text-shadow: none" type="danger" @click="onToolClick({ key: 'delete' })">
            {{ $t("btn_batch_delete") }}
          </a-button>
        </template>-->
      <template #updateBy="{ row }">
        <userInfo :accounts="[row.updateBy]" :showname="true" />
      </template>
      <!-- 拆分写出teamRole的名称 -->
      <template #role="{ row }">
        <div>
          {{ row.teamRoles.map((item) => item.displayName).toString() }}
        </div>
      </template>
    </jw-table>
    <!-- 新建团队 -->
    <formModal ref="ref_form_modal"></formModal>
<!--    这里的数据源来自 iam-->
<!--    <userModalV3 ref="user_modal" :isCheckbox="ischeckBox"/>-->
    <!--    这里的数据源来自 pdm-->
    <jw-user-modal ref="user_modal" :isCheckbox="ischeckBox" />
  </div>
</template>

<script>
//接口

import formModal from "./form-dialog";
import userInfo from "components/user-info";

import { jwModalForm, jwAvatar, jwIcon } from "jw_frame";

import { getCookie } from "jw_utils/cookie";
import rules from "jw_frame/utils/rules.js";

import ModelFactory from "jw_apis/model-factory";
import userModalV3 from "../../../components/user-modal-v3";
import jwUserModalV2 from "components/user-modal-v2";
// 查询用户TeamRole
const findTeamApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/team/searchAllTeamRole`,
  method: "post"
});
//  取消用户产品容器-文件夹下teamRole的角色绑定
const deleteAllTeamRoleUserAndChangeUser = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/team/deleteAllTeamRoleUser`,
  method: "post"
});

//  批量处理用户的 TeamRole 权限（删除/转移）
const batchDeleteAllTeamRoleUser = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/team/batchHandleTeamRoleUser`,
  method: "post"
});

export default {
  components: {
    jwUserModal: jwUserModalV2,
    userModalV3,
    jwModalForm,
    jwAvatar,
    jwIcon,
    formModal,
    userInfo
  },
  inject: ["setBreadcrumb", "addBreadcrumb"],
  data() {
    return {
      activeTab: "1",
      businessType: 1, //1为站点管理，2为组织管理
      drawerStatus: "",

      rules,
      // 角色列表信息
      ischeckBox: false,
      isRoleVisible: false,
      roleTableData: [],
      searchKey: "",
      userName: "",
      changeUserOid: "",
      tableData: [],
      selectedRows: [],

      tableLoading: false,

      currentRow: {},
      // 查询当前团队下的角色
      currentRowData: {},
      currentRowRole: [],
      //当前默认选中user
      currentCheckUser: [],
      containerOid: '', // 新增产品容器选择
      productList: [], // 产品列表
    };
  },
  computed: {
    getHeader() {
      return [
        {
          field: "container.name",
          title: this.$t("产品容器名称"),
          sortable: true // 开启排序
        },
        {
          field: "folderNames",
          title: this.$t("文件夹名称"),
          sortable: true // 开启排序
        },
        {
          field: "role",
          title: this.$t("btn_users"),
          slots: {
            default: "role",
          },
        },
        {
          field: "container.owner",
          title: this.$t("产品经理"),
          width: "160",
          sortable: true, // 开启排序
          slots: {
            default: 'updateBy',
          }
        },
        {
          field: "updateDate",
          title: "修改时间",
          width: "160",
          formatter: "date"
        },
        {
          // 操作列定义
          field: "operation", //关键字
          title: this.$t("txt_operation"),
          width: "100",
          btns: [
            /*{
              icon: "jwi-iconedit",
              title: this.$t("btn_edit"),
              key: "edit"
            },*/
            {
              icon: "jwi-iconsave-as",
              title: this.$t("替换用户"),
              key: "saveAs"
            },
            {
              icon: "jwi-icondelete",
              title: this.$t("btn_delete"),
              key: "delete"
            }
          ]
        }
      ];
    },
    toolbars() {
      const hasContainer = !!this.containerOid && !!this.searchKey;

      return [
        {
          name: this.$t("btn_search"),
          position: "before",
          display: "input",
          value: this.userName,
          placeholder: this.$t("请输入用户信息查询"),
          key: "search",
        },
        {
          name: this.containerOid ? this.productList.find(item => item.oid === this.containerOid)?.name : this.$t("选择产品"),
          display: "input",
          key: "selectProduct",
          placeholder: this.$t("选择产品"),
          value: this.containerOid ? this.productList.find(item => item.oid === this.containerOid)?.name : '',
        },
        {
          name: this.$t("批量转移"),
          key: "batchTransfer",
          type: "primary",
          disabled: !hasContainer // 需要选择 产品和人
        },
        {
          name: this.$t("批量删除"),
          key: "batchDelete",
          type: "danger",
          disabled: !hasContainer // 需要选择 产品和人
        },

      ];
    }
  },
  created() {
    // 输入回调去抖动
    this.businessType = this.$route.query.type || 0;
    this.delaySearch = _.debounce(this.onSearch, 500);
    this.initBreadcrumb();
    this.getProductList(); // 获取产品列表
  },

  methods: {
    // 数据请求函数
    fetchTable({  current, pageSize }) {
      let param = {
        searchKey: this.searchKey,
        index: current,
        size: pageSize,
        containerOid: this.containerOid // 添加产品容器筛选
      };
      this.tableLoading = true;
      return findTeamApi
          .execute(param)
          .then(res => {
            this.tableLoading = false;
            return {
              data: res.rows,
              total: res.count
            };
          })
          .catch(err => {
            this.tableLoading = false;
            this.$error(err.msg || this.$t("msg_failed"));
          });
    },
    // 团队启用
    switchClick(row) {
      let param = {
        oid: row.oid,
        disabled: row.disabled,
        containerOid: getCookie("tenantOid"),
        containerModelType: "Tenant"
      };
      ModelFactory.create({
        url: `${Jw.gateway}/${Jw.containerCatalogService}/team/setDisabled`,
        method: "post"
      })
          .execute(param)
          .then(data => {
            this.$success(this.$t("msg_save_success"));
            //刷新列表
            this.$refs.ref_table.reFetchData();
          })
          .catch(err => {
            //刷新列表

            this.$error(err.msg || this.$t("msg_failed"));
          });
    },

    // 选择列回调
    onSelectChange(args) {
      this.selectedRows = [...args];
    },

    // 操作列回调
    onOperateClick(key, row) {
      this.currentRow = { ...row };
      if (key === "edit") {
        this.$router.push({
          path: "site-team-management-view",
          query: {
            drawerStatus: key,
            type: "team",
            team: JSON.stringify(this.currentRow)
          }
        });
      } else if (key === "detail") {
        this.showDrawer();
        this.activeTab = "1";
        this.drawerStatus = "detail";
        this.getCurrentRowRole(row);
      } else if (key === "addRole") {
        this.showDrawer();
        this.activeTab = "2";
        this.drawerStatus = "detail";
        this.getCurrentRowRole(row);
      } else if (key === "saveAs") {
        this.$refs.user_modal
            .show({type: "User",})
            .then(res => {
              this.changeUserOid = res.oid;
              this.changeUser(row);
              // this.$refs.ref_table.reFetchData();
            });
      } else if (key === "delete") {
        this.onDelete(row);
      }
    },
    // 工具栏点击回调
    onToolClick(item) {
      if (item.key === "batchTransfer") {
        this.$refs.user_modal.show({
          type: "User",
        }).then(res => {
          this.batchChangeUser(res.oid);
        });
      } else if (item.key === "batchDelete") {
        this.batchDelete();
      }
    },
    // 工具栏输入回调
    onToolInput({ key }, value) {
      console.log("search");
      if (key === "search") {
        // this.searchKey = value;
        // this.delaySearch();
        this.$refs.user_modal.show({
          type: "User",
        }).then(res => {
          console.log(res);
          this.searchKey = res.oid;
          this.userName = res.name + " (" + res.account + ")";
          this.$refs.ref_table.reFetchData();
        });

      }
      else if (key === "selectProduct") {
        this.$confirm({
          title: this.$t("选择产品"),
          width: 500,
          content: h => {
            return (
                <a-select
                    style={{width: '100%'}}
                    value={this.containerOid}
                    allowClear={true} // 允许清空
                    showSearch // 启用搜索功能
                    placeholder="请选择" // 可以添加一个占位符
                    onChange={(value) => {
                      this.containerOid = value;
                      // 强制更新 toolbars
                      this.$forceUpdate();
                    }}
                    filterOption={(input, option) => {
                      return (
                          option.componentOptions.children[0].text
                              .toLowerCase()
                              .indexOf(input.toLowerCase()) >= 0
                      );
                    }}
                >
                  {this.productList.map(item => (
                      <a-select-option key={item.oid} value={item.oid}>
                        {item.name}
                      </a-select-option>
                  ))}
                </a-select>
            );
          },
          onOk: () => {
            this.$refs.ref_table.reFetchData();
          }
        });
      }
    },
    // 删除团队
    onDelete(row) {
      this.fetchDelete(row);
    },

    initBreadcrumb() {
      let breadcrumbData = [
        {
          name: this.$t("产品用户权限管理"),
          path: "/user-product-permission"
        }
      ];
      this.setBreadcrumb(breadcrumbData);
    },
    // 取消用户产品容器-文件夹下teamRole的角色绑定
    fetchDelete(row) {
      let param = {
        userOid: row.userOid,
        teamRoles: Array.isArray(row.teamRoles) ? row.teamRoles.map(item => item.oid) : [],
      };
      this.$confirm({
        width: "280px",
        class: "deleteModal",
        closable: true,
        mask: false,
        title: (
            <p style="font-size:16px;font-weight:500;color:rgba(30, 32, 42, 0.85);">
              {this.$t("")}
            </p>
        ),
        content: (
            <p style="font-size:14px;font-weight:400;color:rgba(30, 32, 42, 0.45);">
              {this.$t("msg_confirm_delete")}
            </p>
        ),
        cancelText: this.$t("btn_cancel"),
        okText: this.$t("btn_confirm"),
        onOk: () => {
          deleteAllTeamRoleUserAndChangeUser
              .execute(param)
              .then(data => {
                this.$success(this.$t("txt_delete_success"));
                this.$refs.ref_table.reFetchData();
                this.selectedRows = [];
              })
              .catch(err => {
                this.$error(err.msg || this.$t("msg_failed"));
              });
        }
      });
    },
    changeUser(row) {
      let param = {
        userOid: row.userOid,
        teamRoles: Array.isArray(row.teamRoles) ? row.teamRoles.map(item => item.oid) : [],
        replaceUserOid: this.changeUserOid,
      };
      deleteAllTeamRoleUserAndChangeUser
          .execute(param)
          .then(data => {
            this.$success(this.$t("操作成功"));
            this.$refs.ref_table.reFetchData();
            this.selectedRows = [];
          })
          .catch(err => {
            this.$error(err.msg || this.$t("msg_failed"));
          });
    },
    // 输入回调刷新表格数据
    onSearch() {
      this.$refs.ref_table.reFetchData(this.searchKey);
    },
    // 团队另存为
    submitSaveAs(data) {
      let { currentRow } = this;
      let tenantOid = getCookie("tenantOid");
      let param = {
        ...data,
        oldTeamTemplateOid: currentRow.oid,
        teamTemplateName: data.name,
        containerOid: getCookie("tenantOid"),
        containerModelType: "Tenant"
      };
      return ModelFactory.create({
        url: `${Jw.gateway}/${
            Jw.accountMicroServer
        }/team/template/saveas?oldTeamTemplateOid=${
            currentRow.oid
        }&teamTemplateName=${data.name}`,
        method: "post"
      })
          .execute(param)
          .then(data => {
            this.$success(this.$t("msg_save_success"));

            this.$refs.ref_table.reFetchData();
          })
          .catch(err => {
            this.tableLoading = false;
            this.$error(err.msg || this.$t("msg_failed"));
          });
    },
    // 获取产品列表
    async getProductList() {
      try {
        const res = await ModelFactory.create({
          url: `${Jw.gateway}/${Jw.containerService}/container/product/search`,
          method: 'post',
        }).execute({
          index: 1,
          size: 1000,
          searchKey: '',
        }).then((data) => {
          this.productList = data.rows || [];
        });

      } catch (err) {
        this.$error(err.msg || this.$t("msg_failed"));
      }
    },
    // 批量转移权限
    batchChangeUser(replaceUserOid) {
      console.log(replaceUserOid);
      const param = {
        userOid: this.searchKey,
        containerOid: this.containerOid,
        replaceUserOid:replaceUserOid
      };

      batchDeleteAllTeamRoleUser
        .execute(param)
        .then(() => {
          this.$success(this.$t("操作成功"));
          this.$refs.ref_table.reFetchData();
          this.selectedRows = [];
        })
        .catch(err => {
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    // 批量删除权限
    batchDelete() {
      const param = {
        userOid: this.searchKey,
        containerOid: this.containerOid
      };

      this.$confirm({
        title: this.$t("确认删除"),
        content: this.$t("msg_confirm_delete"),
        onOk: () => {
          batchDeleteAllTeamRoleUser
            .execute(param)
            .then(() => {
              this.$success(this.$t("txt_delete_success"));
              this.$refs.ref_table.reFetchData();
              this.selectedRows = [];
            })
            .catch(err => {
              this.$error(err.msg || this.$t("msg_failed"));
            });
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.bindGroupArea {
  display: flex;
  justify-content: end;
  .bindroleBtn {
    margin-bottom: 10px;
  }
}

.openBtn {
  float: right;
  margin: 10px 20px 0 0;
}
</style>
<style>
.deleteModal .ant-modal-body {
  padding: 24px;
}

.deleteModal .ant-modal {
  /* top: 112px;
  left: 42%; */
}

.deleteModal .ant-modal-close-x {
  line-height: 69px;
}

.deleteModal
.ant-modal-confirm-body
> .anticon
+ .ant-modal-confirm-title
+ .ant-modal-confirm-content {
  margin-left: 0;
}

.deleteModal .ant-modal-confirm-btns .ant-btn {
  width: 75px;
  float: right;
}

.deleteModal .ant-modal-confirm-btns .ant-btn.ant-btn-primary {
  margin-right: 8px;
  background-color: rgba(37, 94, 215, 1);
  /* background-color: #1890ff; */
  border-color: rgba(37, 94, 215, 1);
}

/* 抽屉相关样式重置 */
</style>
