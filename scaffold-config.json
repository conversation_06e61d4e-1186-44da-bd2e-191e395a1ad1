{"nodeWrapperPort": 80, "nodeConfigServiceName": "pdm-application", "title": "Digital PDM", "loginMethods": ["account"], "version": "V4.0.0", "logoUrl": "", "domain": "", "multiLanguage": [{"name": "简体", "value": "zh-CN"}, {"name": "English", "value": "en-US"}], "dataI18n": true, "appIcon": "#jwi-pdmbeifen3", "appName": "pdm", "appKey": "", "appSecret": "", "copyright": "", "service": "http://172.16.9.91:5003", "gateway": "http://172.16.9.91:5003/pdm-standalone-mac", "gateway_new": "http://172.16.9.91:5003/pdm-standalone-mac/customer", "configMicroService": "config-micro", "dingCorPid": "ding53c2a71231b4fe2935c2f4657eb6378f", "partBomMicroServer": "part-bom-micro", "foundationServer": "foundation-micro", "accountMicroServer": "account-micro", "iamServer": "../iam-server-mac", "customerServer": "customer", "fileMicroServer": "file-micro", "docMicroServer": "doc-micro", "cadService": "cad-micro", "securityService": "security-service", "containerService": "container", "productDeliveryServer": "delivery", "sysconfigServer": "sysconfig", "mpmLibraryServer": "mpm-library-micro", "permissionServer": "permission-service", "baselineServer": "baseline-service", "configServer": "config-management", "filePreviewServer": "file-preview-online", "authServer": "jic-authentication", "accountServer": "jic-account", "workflowServer": "workflow", "workflowMicroServer": "workflow-micro", "workflowDesignDomain": "http://172.16.9.91:14002/workflow-modeler-micro", "changeServer": "pdm-change-micro", "//9": "cad转换服务", "cadConverterMicroServer": "http://172.16.9.91:5003/pdm-standalone-mac/cad-converter-micro", "auditService": "audit", "iamFileServer": "file-micro", "baseServer": "foundation", "partBomServer": "part-bom", "fileServer": "file", "ieconfigService": "ieconfig", "permission": "permission", "workbenchWorkflowService": "workbench-workflow", "containerCatalogService": "containerCatalog", "fileOnlineService": "http://172.16.9.91:19403", "ecService": "ec", "dictServer": "dict", "ruleEngineServer": "rule-engine-micro", "docServer": "doc", "issueServer": "issue", "projectServer": "project", "searchEngineServer": "search-engine-micro", "i18nServer": "i18n-micro", "navigationViewCode": "NAVIGATION", "setlServer": "setl", "templateTabs": [{"name": "txt_product_temp", "category": "product"}, {"name": "txt_resources_temp", "category": "resource"}, {"name": "txt_sign_temp", "category": "signTemplate", "fileType": ".pdf,.doc,.docx"}], "creatorTabOption": [{"value": "ProductContainerCreator", "label": "txt_product_createby"}, {"value": "ResourceContainerCreator", "label": "txt_resources_createby"}, {"value": "TypeSpectrumCreator", "label": "txt_spectrum_createby"}], "modelMapviewCode": {"BasicContainer": "CONTAINERINSTANCE", "Part": "PARTINSTANCE", "Document": "DOCUMENTINSTANCE", "DocumentTemplateMaster": "DOCUMENTTEMPLATEINSTANCE", "Baseline": "BASELINEINSTANCE", "ECR": "ECRINSTANCE", "ECO": "ECOINSTANCE", "ECA": "ECAINSTANCE", "MCAD": "MCADDOCUMENTINSTANCE", "ECAD": "ECADINSTANCE", "ProcessOrder": "PROCESSORDER", "Issue": "ISSUEINSTANCE"}, "ContainerMapRoute": {"ProductContainer": "/product-content", "ResourceContainer": "/product-content"}, "instanceMapRoute": {"ProductContainer": "/object-details", "ResourceContainer": "/object-details", "DocumentTemplateMaster": "/object-details", "Document": "/object-details", "Part": "/object-details", "MCAD": "/object-details", "ECAD": "/object-details", "ProcessOrder": "/detailed-task", "Baseline": "/baseline-details", "ECR": "/ecr-details", "ECO": "/eco-details", "ECA": "/eca-details", "Issue": "/problem-details"}, "deactivate": ["Obsoleted"]}