import ModelFactory from "jw_apis/model-factory";
import Vue from "vue";
//删除功能
const baseDeleteApi = (params, callback) => {
  let _this = Vue.prototype
  ModelFactory.create({
    url: `${Jw.gateway}/${Jw.foundationServer}/instance/delete?oid=${params.oid}&type=${params.type}`,
    method: "delete"
  }).execute().then(() => {
    _this.$success(_this.$t('txt_delete_success'))
    callback && callback()
  })
}

//删除方法
export const baseDelete =  (params, callback) => {
  let _this = Vue.prototype

  _this.$confirm({
    title: _this.$t('txt_delete'),
    type: 'warning',
    okText: _this.$t('btn_ok'),
    cancelText: _this.$t('btn_cancel'),
    content: _this.$t('txt_delete_content'),
    onOk: () => {
      baseDeleteApi(params, callback)
    }
  })
}