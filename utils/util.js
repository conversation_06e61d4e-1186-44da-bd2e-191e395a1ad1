import { v4 as uuidv4 } from 'uuid'
import { getCookie } from "jw_utils/cookie"
import { fetchContainerTemplate } from "apis/product-container"
import Vue from 'vue';
/**
 * 在 树结构 tree 中，查找 prop 属性值为 value 的元素
 * @param {array} tree 
 * @param {any} value 
 * @param {string} prop 查找的属性，默认为 'oid'
 * @returns 
 */
export const findItem = (tree, value, prop = 'oid') => {
    let item = null;

    for (let index = 0; index < tree.length; index++) {
        const node = tree[index];
        
        if (node[prop] == value)
            return item = node;
        else if (node.children instanceof Array)
        {
            item = findItem(node.children, value, prop);

            if (item)
                return item;
        }
    }

    return item;
}

/**
 * 根据 prop 属性和 value 值，查找其在 tree 树结构中的直接父级元素
 * @param {array} tree 
 * @param {any} value 
 * @param {string} prop 查找的属性，默认为 'oid'
 * @returns 
 */
export const getParent = (tree, value, prop = 'oid') => {
    let parent = null;
    for (let index = 0; index < tree.length; index++) {
        const current = tree[index];
        if (!current.children) continue;

        let item = current.children.find(t => t[prop] == value);
        if (item)
            return parent = current;
        else
        {
            item = getParent(current.children, value, prop);
            
            if (item)
                return item;
        }
    }

    return parent;
}


/**
 * 在 树结构 tree 中，根据 prop 属性及 value 值（整个 tree 中唯一），查找其链式父级
 * @param {array} tree 
 * @param {any} value 
 * @param {string} prop 查找的属性，默认为 'oid'
 * @param {string} split 拼接 prop、name 的分割字符，默认以 '/' 拼接 
 * @param {Boolean} contain 拼接 prop、name 的分割字符，默认以 '/' 拼接 包含自身节点
 * @returns 
 */
export const getChainParent = (tree, value, prop = 'oid', split = '/',contain) => {
    const parentArr = [];

    const self = tree.find(v => v[prop] == value);
    if (self)
        return {
            keyStr: '',
            nameStr: self.name,
        };

    const loop = (id, data) => {
        const parent = getParent(data, id, prop);

        if (parent)
        {    
            parentArr.unshift(parent);
            
            loop(parent[prop], data);
        }
    }

    loop(value, tree);
    if(contain){
         return { nameStr: parentArr.concat(findItem(tree, value, prop)).map(v => v.name).join(split)} 
    }else{
         return {
        keyStr: parentArr.map(v => v[prop]).join(split),
        // nameStr: parentArr.map(v => v.name).join(split),
        nameStr: parentArr.concat(findItem(tree, value, prop)).map(v => v.name).join(split),
    }
    }
   
}

/**
 * 给 树形数据 tree 每一项设置 scopedSlots
 * @param {array} tree 
 * @param {object} scopedSlots 
 */
export const setScopedSlotse = (tree, scopedSlots = {}) => {
    const loop = data => {
        data.forEach(v => {
            v.scopedSlots = scopedSlots;

            if (v.children instanceof Array)
                loop(v.children, scopedSlots)
        })
    }

    loop(tree);
} 

/**
 * 递归遍历 树形数据 tree 
 * @param {Array} tree 
 * @param {Function} callback 
 */
 export const recursiveTree = (tree, callback) => {
    const loop = data => {
        data.forEach(v => {
            callback(v);
            if (v.children instanceof Array)
                loop(v.children);
        })
    }

    loop(tree);
} 

/**
 * 时间戳转换
 */
export const formatDate =(time)=> {
    let date = new Date(time)
    //  var date = new Date(timestamp * 1000);//时x/间戳为10位需*1000，时间戳为13位的话不需乘1000
    let Y = date.getFullYear() + "-"
    let M =
        (date.getMonth() + 1 < 10
            ? "0" + (date.getMonth() + 1)
            : date.getMonth() + 1) + "-"
    let D =( date.getDate() + 1 < 10
            ? "0" + ( date.getDate() + 1)
            :  date.getDate() + 1) + " "
    let H = date.getHours() + ":"
    //获得系统分钟;
    let F = date.getMinutes() + ":"
    //获得系统秒数;
    let S = date.getSeconds()
    return Y + M + D + H + F + S
}

/**
 * 导出文件
 * @param {*} url 
 * @param {*} method 
 * @param {*} fileName 
 * @param {*} filteType 
 * @returns 
 */
export const exportFile = (url, method = 'POST', params = {}, fileName, filteType = '.xlsx') => {
    const formData = new FormData();
    // for (const key in params) {
    //     if (Object.hasOwnProperty.call(params, key)) {
    //         const element = params[key];
    //         formData.append(key, element);
    //     }
    // }
    return new Promise((resolve, reject) => {
        let xhr = new XMLHttpRequest();
        xhr.open(method, url, true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.setRequestHeader('accesstoken', getCookie('token'));
        xhr.setRequestHeader('tenantAlias', getCookie("tenantAlias"));
        xhr.setRequestHeader('tenantOid', getCookie("tenantOid"));
        xhr.setRequestHeader('appName', 'pdm');
        xhr.responseType = 'blob';
        xhr.onload = () => {
            console.log(xhr)
            if (xhr.status == 200)
            {
                if (xhr.response.type == "application/json")
                    return reject(xhr.responseText);

                var blob = new Blob([xhr.response]);
                var objectUrl = URL.createObjectURL(blob);
                var a = document.createElement('a');
                a.href = objectUrl;
                a.download = `${fileName || '导出结果'}${filteType}`;
                document.body.appendChild(a);
                a.click();
                a.remove(); 
                resolve();
            }
            else
                reject(xhr.responseText);
        };
        xhr.onerror = err => {
            reject(err);
        };
        xhr.send(JSON.stringify(params));
    });
}

/**
 * 为数据添加唯一值
 * @param {*} data 
 */
 export const generateUUID = function (data) {
    if (data.length) {
        data.forEach((element, index) => {
            element.uuid = uuidv4() + index;
            element.id = uuidv4() + index;
            if (element.children && element.children.length) {
                generateUUID(element.children)
            }
        });
    }
    else {
        data.uuid = uuidv4();
        data.id = uuidv4();
        if (data.children && data.children.length) {
            generateUUID(data.children)
        }
    }
}

// 去重数组
export const reductionArr = (arr, key = 'oid') => {
    let obj = {}
    let res = arr.reduce((a,b) => {
        obj[b[key]] ? '' : obj[b[key]] = true && a.push(b)
        return a
    }, [])
    return res
}


//下载文件获取文件对象
export const downloadFile = (fileOid, signal) => {
    return fetch(
        `${Jw.gateway}/${Jw.fileMicroServer}/file/downloadByOid?fileOid=${fileOid}`,
        {
          method: "get",
          headers: {
            "Content-Type": "application/json;charset=utf8",
            appName: Jw.appName,
            accesstoken: getCookie('token'),
            tenantAlias: getCookie("tenantAlias"),
            tenantOid: getCookie("tenantOid"),
          },
          signal
        }
      )
        .then((response) => {
          return response.blob();
        })
}

//属性里面枚举转换成对象
export const changtoJsonValue = (strCode) => {
    var reg = /{(txt|val):(.+?),(txt|val):(.+?)\}/;
    var reg_g = /{(txt|val):(.+?),(txt|val):(.+?)\}/g;
    var result = []
    if(strCode){
        let strarr = strCode.match(reg_g);
        if(strarr.length>0){
            for (var i = 0; i < strarr.length; i++) {
                var item = strarr[i]
                var testval = {}
                testval[item.match(reg)[1]] = item.match(reg)[2].replaceAll(/'|"/g, "")
                testval[item.match(reg)[3]] = item.match(reg)[4].replaceAll(/'|"/g, "")
                result.push(testval)
            }
        }
    }
    return result
  }

//下载模板文件
export const downloadTempFile = (category,searchKey) => {
    let params = {
        category,
        containerOid: getCookie('tenantOid'),
        containerType: 'Tenant',
        index: 1,
        searchKey: searchKey,
        size: 20
    }
    fetchContainerTemplate.execute(params).then((resp) => {
        const { rows } = resp
        let _this = Vue.prototype
        if(rows.length === 0){
            _this.$error(_this.$t('txt_download_temp_err'))
        }
        rows.forEach(row => {
            const { name, oid } = row.file
            downloadFile(oid, null).then(blob => {
                let url = window.URL.createObjectURL(new Blob([blob]))
                let link = document.createElement('a')
                link.href = url
                link.style.display = 'none'
                link.setAttribute('download', name)
                document.body.appendChild(link)
                link.click()
                link.remove()
            })
        })

    })
}

