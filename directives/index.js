
import {getUserPermission} from "../apis/view-permission/index"
// getUserPermission.execute().then(res => {
//     window.localStorage.setItem('permission',JSON.stringify(res.permissions))
// });

Jw.Vue.directive('permission', {
    inserted (el, binding, vnode, oldVnode) {
        const { arg, value } = binding;
        // console.log('binding:', ...arguments)
        let currentRole = JSON.parse(window.localStorage.getItem('permission'))||[]
        Jw.Vue.nextTick(function(){
            var role = binding.value
            if(!Jw.Vue.prototype.$_permission(role,currentRole)){
                el.parentNode.removeChild(el);
            } 
        })
    },
    // inserted () {},
    update () {},
    componentUpdated () {},
    unbind () {},
});

// console.log("当前角色的按钮权限：",currentRole)
Jw.Vue.prototype.$_permission = function(role,currentRole){
     //当前角色可以从缓存中获取
    // if(currentRole.length<=0){
    //     currentRole = JSON.parse(window.localStorage.getItem('permission'))||[]
        
    //     return
    // }
    if(Array.isArray(role)){
        return currentRole.some(function(ele){
            return role.indexOf(ele) >= 0
        })

    }else{
        return currentRole.indexOf(role) >= 0;
    }
}



