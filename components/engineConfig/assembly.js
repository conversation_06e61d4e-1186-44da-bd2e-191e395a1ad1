import { useDrawerStore } from "jw_engine/stores/drawer";
import { STRUCTURE_TREE_RELOAD } from "jw_engine/listener-events";

import Blast from "jw_engine/plugins/snapshot/index.vue?_=Blast";
import Clipping from "jw_engine/plugins/snapshot/index.vue?_=Clipping";
import { SNAPSHOT_TYPE } from "jw_engine/apis/pdm";
Blast.name = "Blast";
Clipping.name = "Clipping";

const layout = {
  topLeft: [
    {
      component: require("jw_engine/plugins/float-button-group/photograph")
        .default,
    },
  ],
  topRight: [
    {
      component: require("jw_engine/plugins/float-button-group/center").default,
    },
    {
      component: require("jw_engine/plugins/float-button-group/camera-vision")
        .default,
    },
    {
      component: require("jw_engine/plugins/float-button-group/model-display")
        .default,
    },
    {
      component: require("jw_engine/plugins/float-button-group/camera-toggle")
        .default,
    },
  ],
  leftCenter: [
    {
      component:
        require("jw_engine/plugins/float-button-group/transform/translation")
          .default,
    },
    {
      component:
        require("jw_engine/plugins/float-button-group/transform/rotate")
          .default,
    },
    {
      component: require("jw_engine/plugins/float-button-group/blast").default,
    },
    {
      component: require("jw_engine/plugins/float-button-group/clipping")
        .default,
    },
    {
      component: require("jw_engine/plugins/float-button-group/measure")
        .default,
    },
    {
      component: require("jw_engine/plugins/float-button-group/magnifier")
        .default,
    },
    {
      component: require("jw_engine/plugins/float-button-group/issues").default,
    },
    {
      component:
        require("jw_frame/components/engine3d-3rd/buttons/assembly-pmi")
          .default,
    },
  ],
};

export const workbench = {
  components: {
    left: [
      {
        name: "StructureTree",
        component: require("jw_engine/plugins/structure-tree").default,
      },
      {
        name: "PMITree",
        component: require("jw_engine/plugins/pmi-tree/index.vue").default,
      },
    ],
    center: [
      {
        name: "FloatButtonGroup",
        component: require("jw_engine/plugins/float-button-group/index.vue")
          .default,
        config: { layout },
      },
      {
        name: "Viewport",
        component: require("jw_engine/plugins/viewport").default,
      },
      {
        name: "DrawingBoard",
        component: require("jw_engine/plugins/drawing-board/index.vue").default,
      },
      {
        name: "Magnifier",
        component: require("jw_engine/plugins/magnifier/index.vue").default,
      },
    ],
    right: [
      {
        name: "Attribute",
        component: require("jw_engine/plugins/attribute/index.vue").default,
      },
      {
        name: "Attachment",
        component: require("jw_engine/plugins/attachment/index.vue").default,
      },
      {
        name: "AssetLibrary",
        component: require("jw_engine/plugins/asset-library/index.vue").default,
      },
      {
        name: "Issues",
        component: require("jw_engine/plugins/issues/index.vue").default,
      },
      { name: "Blast", component: Blast, type: SNAPSHOT_TYPE.BLAST },
      { name: "Clipping", component: Clipping, type: SNAPSHOT_TYPE.CLIPPING },
      {
        name: "Measure",
        component: require("jw_engine/plugins/measure/index.vue").default,
      },
      {
        name: "PMI",
        component: require("jw_engine/plugins/pmi/index.vue").default,
      },
      // test
      {
        name: "CSG",
        component: require("jw_engine/plugins/csg/index.vue").default,
      },
      {
        name: "Constraint",
        component: require("jw_engine/plugins/constraint").default,
      },
    ],
    bottom: [
      {
        name: "BlueprintScript",
        component: require("jw_engine/plugins/blueprint/blueprint-script")
          .default,
      },
      {
        name: "Animation",
        component: require("jw_engine/plugins/animation").default,
      },
    ],
  },
  async onToggleWorkbench() {
    const root = this.getRoot();
    root?.startLoadingTasks();
    await root?.onRendered();
    const listener = this.getListener();
    listener.emit(STRUCTURE_TREE_RELOAD);
    useDrawerStore().cache.left = "StructureTree";
    useDrawerStore().cache.right = "Attribute";
  },
};
