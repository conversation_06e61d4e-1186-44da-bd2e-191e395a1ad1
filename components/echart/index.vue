<template>
    <div :class="['jw-echart', {'border': border}]">
        <ToolBar :class="['jw-echart-header', {'border-bottom': border}]" v-if="title">
            <div slot="before-start">{{ title }}</div>
            <slot name="tool" slot="after-start" />
        </ToolBar>
        <div class="relative">
            <Spin :spinning="loading">
                <div ref="echart" :style="echartCss"></div>
                <div class="absolute text-center width-full" :style="emptyCss" v-if="!options">
                    {{ $t("msg_nodata") }}
                </div>
                <slot name="footer"></slot>
            </Spin>
        </div>
    </div>
</template>

<script>
import { init, use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { BarChart, LineChart, PieChart } from "echarts/charts";
import {
    TooltipComponent,
    GridComponent,
    LegendComponent,
    DatasetComponent,
    TitleComponent,
    ToolboxComponent,
} from "echarts/components";
import { Spin } from "ant-design-vue";
import ToolBar from "jw_components/toolbar";
export default {
    components: {
        ToolBar,
        Spin
    },
    props: {
        title: {
            type: String,
            default: null
        },
        height: {
            type: Number,
            default: 300
        },
        border: {
            type: Boolean,
            default: false
        },
        options: { type: Object, required: false, default: () => null },
        duration: { type: Number, required: false, default: 150 },
        maxLength: { type: Number, required: false, default: 0 },
    },
    data() {
        return {
            loading: true,
            echartInstance: null,
            defaultOptions: {
                tooltip: {
                    trigger: 'item'
                },
                grid: {
                    top: 30,
                    bottom: 12,
                    left: 15,
                    right: 20,
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    axisLine: false
                },
                yAxis: {
                    axisLine: false
                },
                series: null
            }
        };
    },
    computed: {
        echartCss() {
            return {
                height: `${this.height}px`
            }
        },

        emptyCss() {
            return {
                top: `${this.height/2}px`
            }
        },

        useOptions() {
            if (!this.options) return this.defaultOptions;
            return this.mergeOption(this.options)
        }
    },
    // watch: {
    //     useOptions: {
    //         deep: true,
    //         handler(newVal) {
    //             if (!this.echartInstance || !newVal) return;
    //             this.echartInstance.setOption(newVal, { notMerge: false });
    //         },
    //     },
    // },
    methods: {
        register() {
            use([
                CanvasRenderer,
                TooltipComponent,
                GridComponent,
                LegendComponent,
                DatasetComponent,
                TitleComponent,
                ToolboxComponent,
                BarChart,
                LineChart,
                PieChart,
            ]);
        },

        initEchart() {
            this.echartInstance = init(this.$refs.echart);
            this.echartInstance.setOption(this.useOptions);
            window.addEventListener("resize", () => this.echartInstance&&this.echartInstance.resize());
            this.loading = false;
        },

        setMaxLength(target) {
            let {dimensions, keys, source} = target;
            if (this.maxLength === 0 ) return target;
            // 非echarts方式数据限制
            if (dimensions && keys && source) {
                keys = keys.slice(keys.length - this.maxLength);
                for (let key of Object.keys(source)) {
                    source[key] = source[key].slice(source[key].length - this.maxLength);
                }
                return {
                    ...target,
                    keys,
                    source,
                };       
            } else {
                // echarts方式数据限制
                const {xAxis, series} = target;
                if (xAxis.data) {
                    xAxis.data = xAxis.data.slice(keys.length - this.maxLength);
                }
                
                for (let [index] of series.entries()){
                    let len = series[index].data.length;
                    if (len > this.maxLength) {
                        series[index].data = series[index].data.slice(len - this.maxLength);
                    }
                }

                return {
                    ...target,
                    xAxis,
                    series
                }
            }
        },

        mergeOption(target) {
            let newOpt = Object.assign({}, this.defaultOptions, target);
            const { type, dimensions, keys, source, series } = this.setMaxLength(newOpt);
            if (!dimensions && !keys && !source) return newOpt;
            return {
                ...newOpt,
                dimensions: ["keys", ...dimensions],
                dataset: {
                    source: {
                        keys,
                        ...source
                    },
                },
                series: series ? series : dimensions.map(() => ({ type })),
            }
        }
    },
    mounted() {
        this.register();
        setTimeout(()=>{
            this.initEchart();
        },this.duration)
    },
    beforeDestroy() {
        this.echartInstance = null;
    },
}
</script>

<style lang="less" scoped>
.jw-echart {
    &.border {
        border-radius: var(--radius);
    }
    .jw-echart-header {min-height: 54px;}
}
</style>