 <!--
  基本使用 this.$refs.组件.show()
  @description: 用户选择弹框组件
  @param { isCheckbox          | Boolean  } 单选 多选切换
           type                | User/Rolw  切换用户 和角色
  @子项插槽

  @触发事件 暂无实际用途

  resolve()// 传递选择的数据项

 -->
 <template>
  <div>
    <a-modal class="model-warp" :title="title" width="700px" :visible.sync="visible" @ok="onConfirm" @cancel="onClose" :afterClose="onAfterClose">
      <div class="left-box">
        <div class="handle">
          <a-input v-model.trim="searchKey" placeholder="请输入名称或账号,多个使用逗号或顿号分割" allowClear @input="onSeach($event)">
            <a-icon slot="prefix" type="search" />
          </a-input>
        </div>
        <div class="user-list" v-loading="loading">
          <template v-if="dataList.length || loading">
            <a-tree v-model="selectList" :replaceFields="replaceFields" :checkable="isCheckbox" :treeData="dataList" @select="onChangeRadio" v-if="dataList.length > 0" :defaultExpandAll="true">
              <template #title="{ dataRef }">
                <div class="useravatar" data-labelgroup="yes" :data-type="dataRef.type" :data-name="dataRef.fullname"  :data-uesr="dataRef.name + ',' + dataRef.account" v-if="dataRef.current && type === 'User'">
                  <jw-icon v-if="dataRef.current && dataRef.current.type !== 'Position'" :type="getiClass(dataRef.current.type)"></jw-icon>
                  <a-avatar :size="20" v-else-if="dataRef.current.avatar" :src="dataRef.current.avatar"></a-avatar>
                  <a-avatar v-else :size="20" icon="user"></a-avatar>
                  <div class="username" v-text="getUserName(dataRef.current)"></div>
                </div>
                <div v-else data-labelgroup="yes" :data-name="dataRef.fullname">
                  <div class="username" v-text="dataRef.displayName"></div>
                </div>
              </template>
            </a-tree>
          </template>
          <div v-else class="empty-box">
            <div class="empty-image">
              <img src="../../assets/image/empty.png" alt="" />
            </div>
            <p class="empty-description">{{ $t("no_search") }}</p>
          </div>
        </div>
      </div>
      <div v-if="isCheckbox" class="right-box selected-box">
        <div class="handle">
          <span>{{ $t("txt_added") }}{{ addedList.length }}{{ $t("txt_item") }}</span>
          <span class="_mpm-link" @click="onDelect()">{{ $t("txt_clear") }}
          </span>
        </div>
        <ul class="user-list" style="overflow: scroll">
          <li class="selected-item" v-for="(item, i) in addedList" :key="i">
            <jw-avatar :data="item" :type="type" />
            <div class="item-info">
              <div class='_mpm_text'>
                {{getUserName(item)}}
              </div>
              <div class='_mpm_text' v-title='item.description'>
                {{item.description}}
              </div>
            </div>
            <a-icon :title="$t('txt_delete')" class="delect-icon jw-link" type="close" @click="onDelect(i)" />
          </li>
        </ul>
      </div>
      <div slot="footer">
        <a-button @click="onClose">{{ $t("btn_cancel") }}</a-button>
        <a-button type="primary" @click="onConfirm">{{ $t("btn_save") }}
        </a-button>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { memberListApi, roleListApi } from "./apis";
// import { jwAvatar } from "jw_frame";
import { getCookie } from "jw_utils/cookie";
import jwAvatar from "../avatar";
export default {
  name: "jwUserModal",
  props: {
    isCheckbox: {
      type: Boolean,
      default: true
    },
    defaultLanguage: {
      type: String
    },
    //默认选中值，角色sourceOid，人员oid
    defaultOidValue: {
      type: Array,
      default: () => []
    }
  },
  components: {
    jwAvatar
  },
  data() {
    this.userInfo = {};
    this.customFetch = null;
    return {
      loading: false,
      dataList: [],
      searchKey: "",
      type: "Role",
      visible: false,
      selectKey: "",
      selectList: [],
      addedList: [],
      replaceFields: { children: "children", title: "title", key: "oid" },
      defaultOids:[]
    };
  },
  watch: {
    defaultLanguage: {
      deep: true,
      handler(val) {
        console.log("defaultLanguage=>:", val);
      }
    }
  },
  created() {},
  watch: {
    selectList: function(val) {
      setTimeout(() => {
        this.checkusers(val);
      }, 0);
    }
  },
  computed: {
    title() {
      if (this.defaultLanguage == "en-US") {
        return this.type == 'User'
          ? this.$t("Add_personnel")
          : this.$t("Add_role");
      } else {
        return this.type == 'User'
          ? this.$t("add_personnel")
          : this.$t("add_role");
      }
    }
  },

  methods: {
    getUserName(row) {
      if (!row) return ''
      let name
      if (this.type === 'User') {
        name = row.name
      } else if (this.type === "Role") {
        name = row.displayName
      }
      if (row.positions) {
        name += "（" + row.positions.join(",") + "）"
      } else if (row.positionDisplayName) {
        name += "（" + row.positionDisplayName + "）"
      }
      return name
    },
    getUserAccount(row) {
      if (!row) return ''
      if (this.type === 'User') {
        return row.account || row.associatedAccount
      }
      return ''
    },

    checkusers(val) {
      let objList = [];
      this.getTreeObj(val, this.dataList, objList);
      let allShowNode = this.getIsNotHiddenRow()
      
      // 过滤掉不可见的节点
      objList = objList.filter(item => {
        const account = this.getUserAccount(item.current || item);
        return this.validHiddenRow(allShowNode, account);
      });

      objList.forEach(item => {
        const itemOid = item.userOid || item.oid;
        if (
          this.type === 'User' &&
          item.type === 'Position' &&
          !this.addedList.map(list => list.userOid || list.oid).includes(itemOid)
        ) {
          this.addedList.push(item.current);
        } else if (
          this.type === "Role" &&
          item.type === "Role" &&
          !this.addedList.map(list => list.userOid || list.oid).includes(itemOid)
        ) {
          this.addedList.push(item);
        }
      });

      // 同步 selectList 和 addedList
      if(this.addedList.length !== this.selectList.length){
        this.selectList = this.addedList.map(item => item.oid);
      }
    },
    getTreeObj(oids, arr, resarr) {
      arr.forEach(item => {
        if (oids.includes(item.oid)) {
          // 检查节点是否可见
          const element = document.querySelector(`div[data-labelgroup="yes"][data-name="${item.fullname}"]`);
          const isVisible = !element || element.parentElement.parentElement.parentElement.style.display !== 'none';
          
          if (isVisible) {
            resarr.push(item);
          }
        }
        if (item.children && item.children.length > 0) {
          this.getTreeObj(oids, item.children, resarr);
        }
      });
    },
    getChildren(oid, arr, resarr) {
      arr.forEach(item => {
        if (item.oid === oid) {
          resarr.push(oid);
          this.pushChildren(item, resarr);
        }
        if (item.children && item.children.length > 0) {
          this.getChildren(oid, item.children, resarr);
        }
      });
    },
    pushChildren(obj, resarr) {
      if (obj.children && obj.children.length > 0) {
        obj.children.forEach(item => {
          resarr.push(item.oid);
          this.pushChildren(item, resarr);
        });
      }
    },
    //是否是隐藏的节点
    getIsNotHiddenRow(){
      let allele = document.querySelectorAll('div[data-labelgroup="yes"]')
      let allFullName = []
      allele.forEach(item => {
        let nameAccount = item.getAttribute("data-uesr")
        let dom = item.parentElement.parentElement.parentElement
        //未隐藏的节点
        if(dom.style.display !== 'none' && nameAccount){
          allFullName.push(nameAccount)
        }
      })
      return allFullName
    },
    //验证是否是隐藏列
    validHiddenRow(allshowNode, account){
      let have = allshowNode.find(item => item.indexOf(account) !== -1)
      return !!have || this.type === 'Role'
    },
    getiClass(type) {
      // console.log(type)
      switch (type) {
        case "Company":
          return "#jwi-5jieweilogo";
        case "Position":
        case "Department":
          return "#jwi-wenjianga-youneiyong";
        case 'Position':
          return "#jwi-yonghu";
        default:
          break;
      }
    },
    formatName(name) {
      if (name && name.length > 12) {
        return `${name.substring(0, 12)}...`;
      } else {
        return name || "";
      }
    },
    initFetch(flag) {
      this.loading = true;
      let api, params;
      if (this.type == 'User') {
        api = memberListApi;
        params = {
          searchKey: this.searchKey
        };
      } else {
        api = roleListApi;
        params = {
          pageNum: 1,
          // pageSize: this.pageSize,
          keyword: this.searchKey,
          containerOid: getCookie("tenantOid") || Jw.getUser().tenantOid,
          containerType: "Tenant",
          enable: true,
          pageSize: 500
        };
      }
      api
        .execute(params)
        .then(res => {
          if (this.type == 'User') {
            this.dataList = res ? [res] : [];
          } else {
            const { rows } = res;
            this.dataList = rows;
          }
          this.executeListdata(this.dataList, flag);
        })
        .catch(err => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    executeListdata(list, flag) {
      list.forEach(item => {
        if (item.current) {
          for (const param in item.current) {
            item[param] = item.current[param];
          }
          if(item.associatedAccount){
            item.account = item.associatedAccount
            item.current.account = item.associatedAccount
          }
        }
        this.setAllChildrenName(item)

        if (item.children) {
          this.executeListdata(item.children, flag);
        }
        if (flag && ((item.current && this.type === 'User') || this.type === "Role")) {
          this.addDefault(item);
        }
      });
    },

    //将子节点所有名称合并的父节点，搜索过滤使用
    setAllChildrenName(obj){
      let fullname = []
      // 添加当前节点的名称
      if (obj.current) {
        fullname.push(this.getUserName(obj.current ),this.getUserAccount(obj.current ))
      } else {
        fullname.push(this.getUserName( obj),this.getUserAccount( obj))
      }

      // 收集所有子节点的 name 和 account
      this.pushChildrenName(obj.children, fullname)
      obj.fullname = fullname.filter(Boolean).join(",") // 过滤掉空值
    },

    pushChildrenName(children, fullname){
      if (children && children.length > 0) {
        children.forEach(item => {
          const dataObj = item.current || item
          if (dataObj.type === 'Position') {
            // 对于职位节点，添加名称（包括职位）和账号
            const name = this.getUserName(dataObj)
            const account = this.getUserAccount(dataObj)
            if (name) fullname.push(name)
            if (account) fullname.push(account)
          }
          // 递归处理子节点
          this.pushChildrenName(item.children, fullname)
        })
      }
    },

    addDefault(item) {
      let defaultSelected = this.defaultOidValue.length ? this.defaultOidValue : this.defaultOids;
      const itemOid = item.userOid || item.oid;

      if (defaultSelected.some(id => id === itemOid)) {
        this.selectList.push(itemOid);
        // 添加到已选列表
        const itemToAdd = this.type === 'User' ? item.current : item;
        if (!this.addedList.some(added => (added.userOid || added.oid) === itemOid)) {
          this.addedList.push(itemToAdd);
        }
      }
    },
    onChangeRadio(selectedKeys) {
      if (this.isCheckbox) {
        if (selectedKeys.length > 0) {
          let oid = selectedKeys[0];
          let alloid = [];
          // 获取所有可见的节点OID
          let visibleNodes = this.getVisibleNodes(oid, this.dataList);
          this.selectList = [...this.selectList, ...visibleNodes];
        }
      } else {
        this.selectList = selectedKeys;
      }
    },

    // 新增方法：获取可见节点的OID
    getVisibleNodes(oid, arr) {
      let visibleOids = [];
      
      const processNode = (node) => {
        // 检查节点是否可见（通过DOM）
        const element = document.querySelector(`div[data-labelgroup="yes"][data-name="${node.fullname}"]`);
        const isVisible = element && element.parentElement.parentElement.parentElement.style.display !== 'none';
        
        if (isVisible) {
          visibleOids.push(node.oid);
        }
        
        // 只处理可见节点的子节点
        if (isVisible && node.children && node.children.length > 0) {
          node.children.forEach(child => processNode(child));
        }
      };

      // 找到目标节点并处理
      const findAndProcess = (nodes) => {
        for (let node of nodes) {
          if (node.oid === oid) {
            processNode(node);
            return;
          }
          if (node.children) {
            findAndProcess(node.children);
          }
        }
      };

      findAndProcess(arr);
      return visibleOids;
    },

    onDelect(i) {
      if (i > -1) {
        let removeOid = this.addedList[i].oid;
        this.addedList.splice(i, 1)
        this.selectList = this.addedList
          .filter(item => item.oid !== removeOid)
          .map(item => item.oid);
      } else {
        this.selectList = [];
        this.addedList = []
      }
    },
    show(options) {
      if (options) {
        this.customFetch = options.customFetch;

        if (options.title) {
          this.title = options.title;
        }
        if (options.selectRow) {
          if (this.isCheckbox) {
            this.defaultOids = options.selectRow.map(item => {
              return item.oid;
            });
          } else {
            this.selectKey = options.selectRow.oid;
          }
        }
        if (options.type) {
          this.type = options.type;
        }
      }
      if (this.customFetch) {
        this.loading = true;
        this.customFetch().then(rows => {
          this.loading = false;
          if (rows) {
            this.dataList = rows;
            this.executeListdata(this.dataList, true);
          } else {
            this.dataList = [];
          }
        });
      } else {
        this.initFetch(true);
      }
      this.visible = true;

      return new Promise((resolve, reject) => {
        this.onCancel = () => {
          this.hide();
          reject("cancel");
        };

        this.onClose = () => {
          this.hide();
          reject("close");
        };

        this.onConfirm = () => {
          let data = null;
          if (this.isCheckbox) {
            data = this.addedList;
          } else {
            data = this.addedList.length > 0 ? this.addedList[0] : {};
          }
          if (_.isEmpty(data)) {
            return this.$error(this.$t("msg_select_data"));
          }
          this.hide();
          resolve(_.clone(data));
        };
      });
    },

    onSeach(key) {
      if (this.customFetch) {
        this.loading = true;
        this.customFetch({ searchKey: this.searchKey }).then(rows => {
          this.loading = false;
          if (rows) {
            this.dataList = rows;
            this.executeListdata(this.dataList, true);
          } else {
            this.dataList = [];
          }
        });
      } else {
        let allele = document.querySelectorAll('div[data-labelgroup="yes"]')
        let searchAll = this.searchKey.toLowerCase().split(/,|、/g)
        allele.forEach(item => {
          let fullname = item.getAttribute("data-name").toLowerCase()
          let dom = item.parentElement.parentElement.parentElement

          if(fullname.indexOf(this.searchKey.toLowerCase()) !== -1 || this.validSearchKey(searchAll, fullname)){
            dom.style.display = ''
          }else{
            dom.style.display = 'none'
          }
        })
      }
    },
    validSearchKey(searchList, fullname){
      return searchList.find(item => {
        let arr = item.split(/\(|（/g)
        let userAcc = arr.map(val => val.replaceAll(/\)|）/g, "").trim())
        return !!userAcc.find(acc => fullname.indexOf(acc) !== -1)
      })

    },
    hide() {
      this.visible = false;
    },
    onAfterClose() {
      this.searchKey = "";
      this.selectKey = "";
      this.userInfo = {};
      this.addedList = [];
      this.selectList = [];
      this.dataList = [];
      this.customFetch = null;
    },
    onCancel() {
      this.$emit("closeUserModal", false);
    },
    onConfirm() {
      let data = null;
      if (this.isCheckbox) {
        data = this.addedList;
      } else {
        data = data = this.addedList.length > 0 ? this.addedList[0] : {};
      }
      if (_.isEmpty(data)) {
        return this.$error($t("msg_select_data"));
      }
      this.$emit("handleSubmit", data);
    },
    onClose() {
      this.$emit("closeUserModal", false);
    }
  }
};
</script>
<style>
.jw-table.is-tree .vxe-table .col--checkbox .vxe-cell {
  width: 30px !important;
}
</style>
<style lang="less" scoped>
.flex-column {
  display: flex;
  flex-direction: column;
}

.height38 {
  height: 42px;
}

.base-box {
  height: 100%;
  overflow: hidden;
  flex: 1;
  .flex-column;
}

.content-box {
  margin-top: 10px;
  flex: 1;
  overflow-y: auto;
}

.user-info {
  line-height: normal;
  display: flex;
  align-items: center;
  .text {
    margin-left: 10px;
    flex: 1;
    .text-name {
      width: 200px;
      .activa {
        color: @primary-color;
      }
      .account {
        margin-left: 5px;
        color: @text-color-secondary;
      }
    }
    .text-sub {
      color: @text-color-secondary;
      font-size: 14px;
      width: 200px;
    }
  }
}
//////////////////////////////
.model-warp {
  ::v-deep .ant-modal-content {
    height: 70vh;
    .flex-column;
    .ant-modal-body {
      display: flex;
      overflow: hidden;
      flex: 1;
      padding-bottom: 0px;
      .left-box {
        padding-right: 10px;
        .base-box;
        .user-list {
          .content-box;
          .ant-radio-group,
          .ant-checkbox-group {
            width: 100%;
          }
          .ant-checkbox-wrapper {
            margin-left: 0px;
            &:hover {
              background: @item-hover-bg;
            }
          }
          .ant-radio-wrapper,
          .ant-checkbox-wrapper {
            display: block;
            .height38;
            line-height: 38px;
            > span {
              display: inline-block;
            }
            .user-info;
          }
          .empty-box {
            .flex-column;
            height: 100%;
            align-items: center;
            justify-content: center;
          }
        }
      }
      .right-box {
        padding-left: 10px;
        border-left: 1px solid @border-color-base;
        .base-box;

        .handle {
          height: 32px;
          line-height: 32px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .user-list {
          // .content-box;
          .selected-item {
            .height38;
            .user-info;
            .item-info{
              flex: 1;
              overflow: hidden;
              margin-left: 10px;
              >div{
                width: 100%
              }
            }
            .delect-icon {
              margin-right: 10px;
            }
            &:hover {
              cursor: pointer;
              background: @item-hover-bg;
            }
          }
        }
      }
    }
  }
}

.useravatar {
  display: flex;
  align-items: center;
}

.username {
  margin-left: 3px;
}
</style>
