<template>
  <div class="avatar-group">
    <a-popover v-for="(userInfo, index) in infolist" :key="index">
      <template slot="title">
        <div class="title">
          <div class="title-info">
            <div class="title-info-name">
              {{ userInfo.name }}
            </div>
            <div class="title-info-position">
              {{ userInfo.positions.join(",") }}
            </div>
          </div>
          <div class="title-avatar">
            <a-avatar :src="userInfo.avatar"></a-avatar>
          </div>
        </div>
      </template>
      <template slot="content">
        <div class="content">
          <!-- <div class="content-line">
            <div class="content-line-label">
              手机
            </div>
            <div class="content-line-val">
              {{ userInfo.phone }}
            </div>
          </div>

          <div class="content-line">
            <div class="content-line-label">
              邮箱
            </div>
            <div class="content-line-val">
              {{ userInfo.email }}
            </div>
          </div> -->

          <div class="content-line">
            <div class="content-line-label">
              部门
            </div>
            <div class="content-line-val">
              {{ userInfo.departments.join(",") }}
            </div>
          </div>
        </div>
      </template>
      <div :class="{avatararea: showname, avatarnoavatar: !showname}">
        <a-avatar class="avatar" :size="showname ? 20 : 25" :src="userInfo.avatar">{{ userInfo.name }}</a-avatar>
        <span class="user-avatar" v-if="showname">{{ userInfo.name }}</span>
      </div>
    </a-popover>
  </div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
//列表数 search 搜索
export const getUserList = () => {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.customerServer}/user/list`,
    method: 'get'
  }).execute()
}
import pako from "pako";
import localforage from 'localforage'
export default {
  props: {
    accounts: {
      type: Array,
      default: () => []
    },
    showname: { 
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      infolist: [],
      allUserInfo: [],
    }
  },
  created() {
    this.initCachData()
  },
  watch: {
    accounts: {
      handler(val) {
        this.infolist = this.allUserInfo.filter(item => this.accounts?.includes(item.account))
      },
      immediate: true
    },
    allUserInfo: {
      handler(val) {
        this.infolist = this.allUserInfo.filter(item => this.accounts?.includes(item.account))
      },
      immediate: true
    }
  },
  methods: {
    //缓存user
    catchUsers(value) {

      localforage.setItem("user-list-info-" + location.hostname, value, (err, val) => {
        if (!err) {
          localStorage.setItem('user-list-info-time-' + location.hostname, new Date().getTime())
          console.log('存储成功')
        } else {
          console.log(err, '出错')
        }
      })
    },
    //获取初始缓存数据
    initCachData() {
      localforage.getItem("user-list-info-" + location.hostname).then(value => {
        // console.log("缓存的人员信息值", value.length)
        let preTime = localStorage.getItem("user-list-info-time-" + location.hostname)
        let nowTime = new Date().getTime()
        //超过10 小时重新拉取
        if(!value || !preTime || (nowTime - preTime) > 10 * 60 * 60 * 1000 ){
          //重新获取
          this.list()
        }else{
          let json = this.unzip(value)
          let list = JSON.parse(json)
          this.allUserInfo = list
        }
      }).catch(err => {
        console.error(err, '取值出错')
      })
    },
    list() {
      getUserList().then(resp => {
        this.catchUsers(resp)
        let json = this.unzip(resp)
        let list = JSON.parse(json)
        this.allUserInfo = list
      })
    },

    //解压
    unzip(strData) {
      // Convert binary string to character-number array
      var charData = strData.split('').map(function (x) { return x.charCodeAt(0); });
      // Turn number array into byte-array
      var binData = new Uint8Array(charData);
      // // unzip
      var data = pako.inflate(binData);
      // Convert gunzipped byteArray back to ascii string:
      return this.Utf8ArrayToStr(data);
    },
    //解决数据过大和中文乱码
    Utf8ArrayToStr(array) {
      var out, i, len, c;
      var char2, char3;

      out = "";
      len = array.length;
      i = 0;
      while (i < len) {
        c = array[i++];
        switch (c >> 4) {
          case 0: case 1: case 2: case 3: case 4: case 5: case 6: case 7:
            // 0xxxxxxx
            out += String.fromCharCode(c);
            break;
          case 12: case 13:
            // 110x xxxx   10xx xxxx
            char2 = array[i++];
            out += String.fromCharCode(((c & 0x1F) << 6) | (char2 & 0x3F));
            break;
          case 14:
            // 1110 xxxx  10xx xxxx  10xx xxxx
            char2 = array[i++];
            char3 = array[i++];
            out += String.fromCharCode(((c & 0x0F) << 12) |
              ((char2 & 0x3F) << 6) |
              ((char3 & 0x3F) << 0));
            break;
        }
      }
      return out;
    }
  },
}
</script>

<style lang="less" scoped>

.avatararea{
  margin-right: 5px;
  border: 1px solid #ddd;
  padding: 3px 6px 3px 3px;
  border-radius: 20px;

  display: flex;
  align-items: center;
  height: 25px;
}

.avatar-group{
  display: flex;
}
.user-avatar {
  font-family: HarmonyOS Sans SC;
  font-size: 12px;
  font-weight: normal;
  letter-spacing: 0px;
  color: #272B34;
}

.title {
  display: flex;
  justify-content: space-between;
  align-items: center;

  padding: 5px 0 5px 0;

  .title-info {

    .title-info-name {
      opacity: 1;
      /* H3/常规 */
      font-family: HarmonyOS Sans SC;
      font-size: 16px;
      font-weight: normal;
      letter-spacing: 0px;
      /* Light 浅色模式/中性色/Gary-9 */
      color: #272B34;
    }

    .title-info-position {
      font-family: HarmonyOS Sans SC;
      font-size: 14px;
      font-weight: normal;
      letter-spacing: 0px;
      color: #6F7686;
      margin-top: 4px;
    }
  }

  .title-avatar {}
}

.avatarnoavatar:not(:first-child) {
  cursor: pointer;
  margin-left: -5px;
}

.avatar {
  border: 1px solid #fff;
}

.avatar:hover {
  z-index: 2;
  border: 1px solid #6392FF;
}

.content {

  .content-line {
    display: flex;
    margin-bottom: 16px;

    .content-line-label {
      /* Body/常规 */
      font-family: HarmonyOS Sans SC;
      font-size: 14px;
      font-weight: normal;
      letter-spacing: 0px;
      color: #6F7686;
      width: 50px;
    }

    .content-line-val {
      font-family: HarmonyOS Sans SC;
      font-size: 14px;
      font-weight: normal;
      letter-spacing: 0px;
      color: #272B34;
    }
  }
}
</style>