<template>
  <div class="a-table-content">
    <slot name="handle"></slot>
    <a-table ref="ref_table" :scroll="scroll" :rowKey="rowKey" :data-source="tableData" :childrenColumnName="childrenColumnName" :row-selection="selectionOption" :pagination="false" :bordered="border" @change="handleTableChange">
      <a-table-column v-if="showIndex" key="DataIndex" title="序号" :width='60'>
        <template slot-scope="text, record, index">
          <span>{{index + 1}}</span>
        </template>
      </a-table-column>
      <template v-for="(item,i) in activeColumns">
        <a-table-column :key="i" :dataIndex="item.prop" :title="item.display" :sorter="item.sorter" :width="item.width"
         :ellipsis='item.width' :customRender="formatter(item)"></a-table-column>
      </template>
      <!-- 操作按钮 -->
      <a-table-column v-if="opsButton && opsButton.length > 0" :width="opsButton.length>_maxButtonSum?'150px':opsButton.length*40+'px'">
        <span slot="title"> 操作
          <a-dropdown placement='bottomRight' v-model.trim="visible">
            <a-icon type="setting" style="margin-left:10px;" />
            <a-menu slot="overlay">
              <a-checkbox-group v-model.trim="checkboxColumns">
                <a-menu-item class="ant-dropdown-menu-item" v-if="item.hide!=false" v-for="(item,i) in columns" :key="i">
                  <a-checkbox :value='item.display'>{{item.display}}</a-checkbox>
                </a-menu-item>
              </a-checkbox-group>
            </a-menu>
          </a-dropdown>
          <!-- <span class="min-box"></span> -->
        </span>
        <template slot-scope="row">
          <div class="table-operate-button">
            <template v-for="(btn,i) in getFilterOpsButton(row)">
              <a-icon class="handle-icon" v-if="i < _maxButtonSum" :key="i" :type="btn.icon" :title="btn.display" @click="onMenuOperateHandler(btn,row)" />
            </template>

            <a-dropdown placement='bottomRight' v-if="row.isMoreButton">

              <a-icon type="more"></a-icon>

              <a-menu slot="overlay">
                <a-menu-item v-for="(btn,j) in getMoreBtns(row)" :key="j" @click="onMenuOperateHandler(btn,row)">
                  <a href="javascript:;">{{btn.display}}</a>
                </a-menu-item>

              </a-menu>

            </a-dropdown>
          </div>
        </template>
      </a-table-column>
      <!-- 页脚 -->
      <template v-if="isPagination" slot="footer">
        <span v-show="selectedRowKeys && selectedRowKeys.length">选择了 {{ selectedRowKeys.length }} 数据
        </span>
        <a-pagination v-if="isPagination" v-model.trim="currentPage" :pageSize='pageSize' size="small" show-size-changer :page-size-options="_pageSizeOptions" :total="total" :show-total="total => `共 ${total} 条`" @change="onPageChange" @showSizeChange="onSizeChange" />
      </template>
    </a-table>
  </div>
</template>
<script>
const MAX_BUTTON_SUM = 4;
export default {
  props: {
    rowKey: {
      type: String,
      default: "oid"
    },
    columns: {
      type: Array,
      default: () => []
    },
    tableData: {
      type: Array,
      default: () => []
    },
    showSelection: {
      // 是否显示多选框
      type: Boolean,
      default: false
    },
    showPagination: {
      // 是否显示分页
      type: Boolean,
      default: true
    },
    // showFooter: {
    //   type: Boolean,
    //   default: true
    // },
    showColumnFilter: {
      type: Boolean,
      default: true
    },
    opsButton: {
      type: Array,
      default: () => {
        return [];
      }
    },
    total: {
      type: Number,
      default: 0
    },
    currentPage: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 10
    },
    childrenColumnName: {
      type: String,
      default: "children"
    },
    showIndex: {
      type: Boolean,
      default: false
    },
    border: {
      type: Boolean,
      default: false
    },
    height: {
      type: Number
      // default: 600
    },

    fetchApi: {
      type: Function
    }
  },

  data() {
    this._pageSizeOptions = ["10", "20", "30", "40", "50"];
    this._maxButtonSum = MAX_BUTTON_SUM;

    return {
      selectedRowKeys: [],
      selectedRows:[],
      checkboxColumns: [],
      visible: false,
      scroll: { y: this.height }
    };
  },
 

  created() {
    this.getShowColumns();
  },
  mounted() {
    // _.delay(() => {
    //   console.log(this.$refs.ref_table.$el.offsetHeight, 222);
    // }, 0);
  },
  computed: {
    selectionOption() {
      return this.showSelection
        ? {
            selectedRowKeys: this.selectedRowKeys,
            onChange: this.onSelectChange,
            type: "checkbox"
          }
        : null;
    },
    isPagination() {
      return this.showPagination && this.total > 0;
    },

    activeColumns() {
      // 动态列
      return this.columns.filter(item => {
        return (
          item.hide === false || this.checkboxColumns.includes(item.display)
        );
      });
    }
  },
  methods: {
    getTableHeight() {
      if (_.isUndefined(this.height) || _.isNull(this.height)) {
        return "null";
      }

      if (this.height === "auto") {
        return null;
      }

      return this.height || 0;
    },
    onSizeChange(page, pageSize) {
      this.currentPage = 1;
      this.pageSize = pageSize;
      if (_.isFunction(this.fetchApi)) {
        this.fetchApi(this.currentPage, this.pageSize);
      } else {
        this.$emit("sizeChange", page, pageSize);
      }
    },
    onPageChange(page, pageSize) {
      this.$emit("pageChange", page, pageSize);
    },
    onMenuOperateHandler(btn, row) {
      this.$emit("on-operate-click", btn, row);
    },
    getShowColumns() {
      this.checkboxColumns = this.columns.map(item => {
        return item.display;
      });
    },
    isVisible(item, row) {
      let visible = true;

      if (this.isPermissionCheck && item.permissionKey) {
        visible = this.$hasPermission(item.permissionKey);
      }

      if (_.isFunction(item.isVisible)) {
        visible = item.isVisible(row);
      }
      return visible;
    },
    getFilterOpsButton(row) {
      let buttons = _.filter(this.opsButton, item => {
        return this.isVisible(item, row);
      });
      row.isMoreButton = false;
      row.isMoreButton = buttons.length > this._maxButtonSum;
      return buttons;
    },

    getMoreBtns(row) {
      let buttons = this.getFilterOpsButton(row);

      if (row.isMoreButton) {
        return buttons.slice(this._maxButtonSum, buttons.length);
      }
    },

    onSelectChange(selectedRowKeys,selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectedRows=selectedRows
      this.$emit("selectChange",selectedRows)
    },
    handleTableChange(pagination, filters, sorter) {
      const pager = { ...this.pagination };
      pager.current = pagination.current;
      this.pagination = pager;
      this.fetch({
        results: pagination.pageSize,
        page: pagination.current,
        sortField: sorter.field,
        sortOrder: sorter.order,
        ...filters
      });
    },
    formatter(item) {
      let innerElement = null;
      let copyElement = null;
      let isCopy = item.isCopy;
      let isDrag = item.isDrag;
      let formatterFun = item.formatter;

      if (_.isFunction(formatterFun) || isCopy || isDrag) {
        return (value, row, index) => {
          if (_.isFunction(formatterFun)) {
            innerElement = formatterFun.call(this, row, value);
          }
          return innerElement || value;
        };
      }

      return null;
    },
    onDrag(event, row) {
      event.dataTransfer.setData("text", JSON.stringify(row));
    }
  }
};
</script>
<style lang="less">
.a-table-content {
  height: calc(~"100% - 0px");
  colgroup {
    col {
      min-width: 80px;
    }
  }
  // .ant-table-wrapper {
  //   height: 100%;
  //   .ant-spin-nested-loading {
  //     height: 100%;
  //     .ant-spin-container {
  //       height: 100%;
  //       .ant-table {
  //         height: 100%;
  //         .ant-table-content {
  //           height: 100%;
  //           .ant-table-scroll {
  //             height: 100%;
  //             display: flex;
  //             flex-direction: column;
  //             > .ant-table-body {
  //               flex: 1;
  //               max-height: 100% !important;
  //             }
  //           }
  //         }
  //       }
  //     }
  //   }
  // }
}

.a-table-content {
  position: relative;
  .select-columns {
    position: absolute;
    right: 10px;
    top: 20px;
    z-index: 10;
  }
  // .min-box {
  //   display: inline-block;
  //   height: 100%;
  //   position: absolute;
  //   right: 0;
  //   top: 0;
  //   width: 7px;
  //   background: #fafafa;
  //   z-index: 10;
  //   border-left: 1px solid #e8e8e8;
  // }
  .ant-spin-container {
    .handle-icon {
      cursor: pointer;
      margin-right: 5px;
      &:hover {
        color: blue;
      }
    }
    .ant-table-footer {
      margin-top: 1px;
      height: 54px;
      background: #fff;
      border: none;
    }
    .ant-pagination {
      position: absolute;
      right: 0;
      bottom: 12px;
    }
  }
}
</style>
