<template>
	<base-color-modal
		:visible.sync="ownerVisible"
		:title="title"
		:width="1280"
		:ok-btn-loading="okBtnLoading"
		:ok-text="okText"
		@cancel="cancelFn"
		@ok="onOk"
		:z-index="zIndex"
	>
		<add-object-content
			ref="addObjectContent"
			:showContext="showContext"
			:showPage="showPage"
			:tabList="tabList"
			:typeList="typeList"
			:columns="columns"
			:fetch="fetch"
		></add-object-content>
	</base-color-modal>
</template>

<script>
import baseColorModal from "./base-color-modal"
import addObjectContent from "./add-object-content"
import systemLanguage from '@jw/scaffold/src/jw.language';

export default {
	name: "structureAddSubObjModal",
	components: {
		baseColorModal,
		addObjectContent,
	},
	props: {
		title: {
			type: String,
		},
		zIndex: {
			type: Number,
			default: 1100,
		},
		showContext: {
			type: Boolean,
			default: true,
		},
		showPage: {
			type: Boolean,
			default: true,
		},
		visible: {
			type: Boolean,
		},
		okBtnLoading: {
			type: Boolean,
			default: false,
		},
		okText: {
			type: String,
			// default: 'Ok',
			default: systemLanguage['btn_confirm'],
		},
		cancelText: {
			type: String,
			// default: "Cancel",
            default: systemLanguage['btn_cancel'],
		},
		fetch: {
			type: Function,
		},
		columns: {
			type: Array,
		},
		tabList: {
			type: Array,
			default: () => [
				/*
                    {
                        key: '',
                        tab: '',
                    }
                    // */
			],
		},
		typeList: {
			type: Array,
		},
	},
	data() {
		return {
			ownerVisible: false,
            Cancel:this.$t('btn_cancel'),
            Ok:this.$t('btn_confirm')
		}
	},
	mounted() {},
	watch: {
		visible(val) {
			this.ownerVisible = val
		},
		ownerVisible(val) {
			if (!val) {
				this.$refs.addObjectContent.onReset()
				this.$emit("update:visible", false)
			}
		},
	},
	methods: {
		onOk() {
			this.$emit("ok", this.$refs.addObjectContent.selectedData)
		},
		cancelFn() {
			this.$emit("cancel")
		},
	},
}
</script>

<style lang="less" scoped></style>
