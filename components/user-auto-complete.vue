<!--
  @description: 用户远程搜索组件 v-model显示 用户名 ( 账号 ) v-model 获取值为 账号
  @example 
  和平台表单组件集成
  <From>
    <template v-slot:name="{option:{opt,form}}">
      <UserAutoComplete v-model.trim="form[opt.prop]"/>
    </template>
  </From>
  普通集成
  <UserAutoComplete v-model.trim="obj.key"/>
 -->
<template>
  <a-auto-complete 
  ref="ref_auto_complete" 
  class="user-auto-complete" 
  allowClear 
  :placeholder='$t("msg_input")' 
  option-label-prop="value" 
  @select="onSelect" 
  @search="dbSearch" 
  @change="onChange">
    <a-input>
      <a-icon slot="prefix" type="user" />
    </a-input>
    <a-spin v-if="fetching" slot="notFoundContent" size="small" />
    <template slot="dataSource">
      <a-select-option v-for="(item) in result" :key="item.oid" :value="`${item.name} ( ${item.account} )`">
        <div class="option-item">
          <span>{{ item.name}} ({{item.account}})</span>
          <span v-if="item.department" class="right">{{item.department}}</span>
        </div>
      </a-select-option>
    </template>
  </a-auto-complete>
</template>
<script>
//接口
import { userListApi } from "apis/user";

export default {
  name: "UserAutoComplete",
  props: {
    value: {
      type: String
    }
  },
  data() {
    this.dbSearch = _.debounce(this.onSearch, 500);
    return {
      result: [],
      searchParam: {
        pageNum: 1,
        pageSize: 30,
        tenantOid: Jw.getUser().tenantOid,
        keyword: ""
      },
      fetching: false
    };
  },
  created() {},
  methods: {
    onSearch(searchText) {
      this.searchParam.keyword = searchText ? searchText : "";
      userListApi
        .execute(this.searchParam)
        .then(data => {
          _.delay(() => {
            this.fetching = false;
          }, 500);
          this.result = data.rows;
        })
        .catch(err => {
          this.$error(err.msg || $t("msg_failed"));
        });
    },
    onSelect(value) {
      let userAccount = /(?<=\().+?(?=\))/
        .exec(value)[0]
        .replace(/(^\s*)|(\s*$)/g, "");
      let user = this.result.filter(item => item.account === userAccount)[0];
      this.$emit("input", user);
      this.$refs.ref_auto_complete.blur();
    },
    onChange(value) {
      this.fetching = true;
    }
  }
};
</script>
<style lang="less" scoped>
.ant-select-dropdown {
  .option-item {
    .right {
      color: #d9d9d9;
    }
  }
}
</style>
