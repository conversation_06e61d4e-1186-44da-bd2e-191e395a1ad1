
// import ModelFactory from 'jw_apis/model-factory';
import ModelFactory from '../../apis/model-factory';
let viewId = window.localStorage.getItem("viewId")


const getAction =data=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.fileMicroServer}/file/preSignedPutUrl`,
    method: "get",
    customHeader:{viewId:viewId}
})

const bindFile =data=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.fileMicroServer}/file/createFileMetadata`,
    method: "POST",
    customHeader:{viewId:viewId}
})

// 绑定多个文件
const bindFile2 =data=> ModelFactory.create({
    url: `${Jw.gateway}/${Jw.fileMicroServer}/filebatchCreateFileMetadata`,
    method: "POST",
    customHeader:{viewId:viewId}
})


export {
    getAction,
    bindFile,
    bindFile2
}