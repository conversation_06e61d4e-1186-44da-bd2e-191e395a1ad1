<template>
<div class="clearfix">
    <a-upload
      v-if="multiple"
      :file-list="fileList"
      :accept="accept"
      :multiple="multiple"
      :listType="listType"
      :disabled="disabled"

      :beforeUpload="beforeUploadHandle"
      @change="handleChange"
      @preview="handlePreview"
    >
      <div v-if="fileList.length < 8">
        <a-icon type="plus" />
        <div class="ant-upload-text">上传</div>
      </div>
    </a-upload>

    <a-modal v-if="multiple" :visible="previewVisible" :footer="null" @cancel="handleCancel">
      <img alt="example" style="width: 100%" :src="previewImage" />
    </a-modal>



    <a-upload
      v-if="!multiple"
      name="avatar"
      class="avatar-uploader"
      :show-upload-list="false"

      :file-list="fileList"
      :accept="accept"
      :multiple="multiple"
      :listType="listType"
      :disabled="disabled"

      :beforeUpload="beforeUploadHandle"
      @change="handleChange"
      @preview="handlePreview"
    >
      <img v-if="imageUrl" :src="imageUrl" alt="avatar" style="width:128px; height:128px;" />
      <div v-else>
        <a-icon :type="loading ? 'loading' : 'plus'" />
        <div class="ant-upload-text">
          上传
        </div>
      </div>
    </a-upload>

</div>
</template>

<script>
import {getAction,bindFile} from './apis.js'
import axios from 'axios'
function isJson(obj){
  let isjson = typeof(obj) == "object" && Object.prototype.toString.call(obj).toLowerCase() == "[object object]" && !obj.length;
  return isjson;
}

function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}


// {
//   appId: null
//   checkSum: "BkBDZ9sMHHECATzLJ7urJuHfwVxyKIXVfkY8ypN1ghs="
//   createdTime: 1646820067488
//   creatorId: null
//   creatorName: "管理员"
//   fileName: "featureManageExport.xls"
//   filePath: "group1/M00/06/A9/wKgCEGIoeaeAWxKCAABUAKtTYdg593.xls"
//   fileSize: 21504
//   modelType: null
//   name: null
//   oid: "daa1903eaec843f9a329cab3c80cb43b"
//   suffix: "xls"
//   tenantId: "administrator"
//   updatedTime: 1646820067488
//   updatorId: null
//   updatorName: "管理员"
//   withDoc: false

//   uid: '1',
//   name: 'xxx.png',
//   // status: 'done',
//   url: 'http://www.baidu.com/xxx.png',
// },


export default {
  name:'jwUploadImage',
  props:{
    value:{  default:()=>({})  },   // 当个文件数据传json，多个文件数据传数组
    accept:{ type:String, default:'.jpg,.png,.gif'},  // 上传文件的格式  
    multiple:{  type:Boolean, default:false},
    maxSize:{ type:Number, default:20 },  // 单位为M
    disabled:{ type:Boolean, default:false },
    listType:{ type:String, default:'picture-card' },   // text, picture 和 picture-card
  },
  data() {
    let fileList = null;
    if( this.multiple === false ){
      fileList = (this.value && isJson(this.value) && Object.keys(this.value).length>0)  ?  [this.value]  :  [];
    }
    else{
      fileList = (this.value && Array.isArray(this.value) && this.value.length>0)  ?  this.value  :  [];
    }
    // console.log("fileList: ",fileList);
    let imageUrl = fileList.length>0  ?  fileList[0].url  : '';

    return {
      fileList,
      bindParams:{
        // bucketName:'',  // 获取上传action时，获取到的
        // fileName:"1647311325999_企业微信截图_1631501964858.png",
        // fileSize:5414930,
      },
      sts:'',

      // 上传多个图片
      previewVisible: false,
      previewImage: '',

      // 上传当个图片
      loading: false,
      imageUrl,
    };
  },
  watch:{
    fileList(newVal){
      // console.log("newVal: ",newVal);

      if(  this.multiple  ){
        var value = newVal
      }
      else{
        var value = newVal.length>0  ?  newVal[0]  : {};
      }
      console.log("value: ",value);
      this.$emit('input',value);

      this.imageUrl = newVal.length>0  ?  newVal[0].url  : '';
    },
  },

  methods: {
    handleCancel() {
      this.previewVisible = false;
    },
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj);
      }
      this.previewImage = file.url || file.preview;
      this.previewVisible = true;
    },


    // 检查大小
    checkSize(size,maxSize=this.maxSize) {
      if(!(size / 1024 / 1024 / 1024 < maxSize)) {
        this.$error("上传文件大小不能超过 " + maxSize + "M")
        return false;
      }
      else{
        return true;        
      }
    },
    // 文件发生变化  状态有：uploading done error removed
    handleChange(info) {
      if (info.file.status === 'removed') {
        let index = this.fileList.findIndex((c,index,arr) =>c.oid === info.file.oid);
        // console.log("index: ",index);
        if(  index >= 0  ) this.fileList.splice(index,1);
      }
      else if (info.file.status === 'uploading') {
        // console.log(`${info.file.name} 上传中`,info.file.uid);
      }
      else if (info.file.status === 'error') {
        // console.log(`${info.file.name} 上传错误.`);
      }
      else if (info.file.status === 'done') {
        // console.log(`${info.file.name} 上传成功`);
      }
    },


    // 上传之前的操作
    async beforeUploadHandle(file, fileList){
      if(!this.checkSize(file.size)) return false

      this.sts = new Date().getTime();
      await this.getAction({fileName:this.sts + '_' + file.name,file:file});
    },
    // 获取上传action
    async getAction(params){
      await getAction(params).execute(params)
      .then(res=>{
        this.bindParams = {
          bucketName : res.bucketName,
          fileSize : params.file.size,
          fileName : params.fileName,
        };
        this.uploadFile(params.file,res.url)
      })
      .catch(error=>{
        console.log("error: ",error);
      });
    },
    // 上传文件
    uploadFile(file,url) {
      axios.put(url, file, {
        //原生获取上传进度的事件
        onUploadProgress: function (progressEvent) {
          file.status = 'uploading'
          file.percentage = (progressEvent.loaded / progressEvent.total) * 100
          // console.log('%c [ file.percentage ]-137', 'font-size:13px; background:pink; color:#bf2c9f;', file.percentage)

          if(file.percentage == 100){
              file.status = 'success'
          }
        }
      }).then((res) => {
        this.bindFile(this.bindParams);
      })
      .catch((err) => {
          console.log(err)
      })
    },    
    // 绑定文件
    bindFile(params){
      bindFile().execute(params)
      .then(res=>{
        // 生产组件中需要的展示数据
        res = {
          ...res,
          uid:res.oid,
          name:res.fileName.split('_')[1],
          url:res.filePath,
        };
        // console.log("res: ",res);

        this.multiple  ?  this.fileList.push(res)  :  this.fileList = [res];
      })
      .catch(error=>{
        console.log("error: ",error);
      });
    },
  }
};
</script>

<style>
/* 上传多张图片 */
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}

/* 上传一张图片 */
.avatar-uploader > .ant-upload {
  width: 128px;
  height: 128px;
}
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}


/* 上传文件 */
/* .ant-upload-list-item-error, .ant-upload-list-item-error .anticon-paper-clip, .ant-upload-list-item-error .ant-upload-list-item-name{
  color: #999999;
}
.ant-upload-list-item-error .ant-upload-list-item-card-actions .anticon {
    color: #999999;
} */
</style>