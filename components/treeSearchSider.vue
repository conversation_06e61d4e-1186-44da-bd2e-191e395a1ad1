<template>
	<div class="group-custom">
		<div class="views-cust">
			<h1>{{ titleText }}</h1>
			<div class="uploads-btn" v-if="headerBtnShow">
				<a-button v-permission="importInto" @click="importModel">导入</a-button>
				<a-button
					type=""
					class="ant-btn"
					v-permission="exportOut"
					style="padding-top: 0"
					:loading="iconLoading"
					@click="importExport"
				>
					导 出</a-button
				>
			</div>
		</div>

		<div class="search-group">
			<div class="search-group-btn" v-if="createSearchShow">
				<div class="gutter-example">
					<div class="search-info-btn">
						<!-- <div class="new-group">
              <div class="gutter-box">
                <a-button
                  type="primary"
                  @click="updateViews"
                  class="new-add"
                  >{{ updetaText }}</a-button
                >
              </div>
            </div> -->
						<popover-button
							v-permission="addButton"
							:dataSource="popoverData"
							:title="updetaText"
              :addLaoding="addLaoding"
							@ok="createOk"
						/>
						<div class="new-search-btn">
							<div class="gutter-box">
								<a-auto-complete
									placeholder="请输入搜索关键字"
									@change="onChange"
								>
									<template slot="dataSource">
										<a-select-option
											v-for="item in dataSource"
											:key="item.id"
											>{{ item.name }}</a-select-option
										>
									</template>
									<a-input
										><a-icon
											slot="suffix"
											type="search"
											class="certain-category-icon" /></a-input
								></a-auto-complete>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="page-header-index-wide">
				<a-card :bordered="false">
					<a-spin :spinning="loading" style="margin-top: 40px">
						<a-tree
							v-if="replaceData.length"
							:replaceFields="replaceFields"
							:treeData="replaceData"
							checkable
							:expandedKeys="expandedKeys"
							class="odiv"
							:style="{ height: screenHeight - 30 + 'px' }"
							:selected-keys.sync="selectedKeys"
							@check="checkSelect"
							@select="nodeSelect"
						>
							<template slot="title" slot-scope="item">
								<span
									class="formations"
									style="
                    width: 80%;
										display: flex;
										align-items: center;
										justify-content: flex-start;
									"
									v-if="item.title.indexOf(searchValue) > -1"
								>
									{{ item.title.substr(0, item.title.indexOf(searchValue)) }}
									<span style="color: #f50">{{ searchValue }} </span>
									{{
										item.title.substr(
											item.title.indexOf(searchValue) + searchValue.length
										)
									}}&nbsp&nbsp&nbsp
									<a-tag
										v-if="item.type == 'userName'"
										:color="item.del ? 'green' : 'red'"
									>
										{{ item.del ? "有效" : "失效" }}</a-tag
									>
								</span>
								<span style="width: 180px" v-else>{{ item.title }} </span>
								<span class="over-flow" style="width: 40px; height: 25px;">
									<a-tooltip v-permission="openUpdate">
										<template slot="title"> 编辑</template>
										<div
											class="btns"
											@click.stop="(e) => edit(item)"
											style="right: 80px;padding:0 5px 0 5px"
										>
											<a-icon type="edit" />
										</div>
									</a-tooltip>
									<span style="width: 20px;margin-right 10px;" v-permission="Delete">
										<a-tooltip>
											<template slot="title"> 删除</template>
											<div class="btns" @click.stop="(e) => remove(item)">
												<a-icon type="delete" />
											</div>
										</a-tooltip>
									</span>
								</span>
							</template>
						</a-tree>
						<a-empty v-else />
					</a-spin>
				</a-card>
			</div>
		</div>
	</div>
</template>

<script>
const generateData = (_level, _preKey, _tns) => {
	const preKey = _preKey || "0"
	const tns = this.viewList

	const children = []
	for (let i = 0; i < x; i++) {
		const key = `${preKey}-${i}`
		tns.push({ title: key, key, scopedSlots: { title: "title" } })
		if (i < y) {
			children.push(key)
		}
	}
	if (_level < 0) {
		return tns
	}
	const level = _level - 1
	children.forEach((key, index) => {
		tns[index].children = []
		return generateData(level, key, tns[index].children)
	})
}

const getParentKey = (key, tree) => {
	let parentKey
	for (let i = 0; i < tree.length; i++) {
		const node = tree[i]
		if (node.children) {
			if (node.children.some((item) => item.title === key)) {
				parentKey = node.title
			} else if (getParentKey(key, node.children)) {
				parentKey = getParentKey(key, node.children)
			}
		}
	}
	return parentKey || key
}

import popoverButton from "components/popover-button.vue"
import { downloadFile } from "../apis/uploud-file/index"
import { getCookie } from "jw_utils/cookie";
export default {
	name: "treeSearchSider",
	components: {
		popoverButton,
	},
	props: {
		downloadCode: {
			type: String,
		},
    //按钮提交状态
    addLaoding:{
      type:Boolean
    },
		//导入权限
		importInto: {
			type: String,
		},
		//导出权限
		exportOut: {
			type: String,
		},
		//导出地址
		importExportUrl: {
			type: String,
		},
		//新建菜单
		addButton: {
			type: String,
		},
		//删除
		Delete: {
			type: String,
		},
		viewList: {
			type: Array,
			require: true,
		},
		titleText: {
			type: String,
			default: "功能视图",
		},
		updetaText: {
			type: String,
			default: "创建视图",
		},
		//是否开启编辑
		openUpdate: {
			type: String,
		},
		treeData: {
			//树状列表
			type: Array,
			default: () => {
				return []
			},
		},
		// 替换 treeNode 中 title,key,children 字段为 treeData 中对应的字段
		replaceFields: {
			type: Object,
			default: () => ({
				key: "oid",
				title: "name",
				children: "children",
			}),
		},
		searchVal: {
			//搜索框的值
			type: String,
			value: "",
		},
		popoverData: {
			type: Array,
			default: function () {
				return [
					{
						label: "视图名称",
						type: "input",
						codeType: "envValidEz",
						prop: "name",
					},
				]
			},
		},
		// 导入导出按钮是否显示
		headerBtnShow: {
			type: Boolean,
			default: true,
		},
		// 创建、搜索框 是否展示
		createSearchShow: {
			type: Boolean,
			default: true,
		},
		loading: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			dataSource: [],
			downloadData: [], //当前选择勾选的模版id集
			visible: false,
			disabled: true,
			iconLoading: false,
			searchValue: "",
			groupArr: [],
			replaceData: this.viewList,
			screenHeight: document.body.clientHeight / 1.5, //(页面头部有固定高度)
			// popoverData: [
			//   {
			//       label: '视图名称',
			//       type: 'input',
			//       prop: 'name',
			//   },
			// ],
			selectedKeys: [], // 选中的树节点key
			expandedKeys: [], //
		}
	},
	watch: {
		screenHeight(val) {
			// 为了避免频繁触发resize函数导致页面卡顿，使用定时器
			if (!this.timer) {
				// 一旦监听到的screenWidth值改变，就将其重新赋给data里的screenWidth
				this.screenHeight = val
				this.timer = true
				setTimeout(function () {
					// 打印screenWidth变化的值
					this.timer = false
				}, 300)
			}
		},
		viewList(val) {
			this.replaceData = val
		},
		treeData(val) {
			// 默认选中第一个树节点
			if (val.length && !this.selectedKeys.length) {
				this.selectedKeys = [val[0].oid]
				this.nodeSelect(this.selectedKeys)
			}
		},
	},
	mounted() {
		const that = this
		// window.onresize = () => {
		// 	return (() => {
		// 		// 可以限制最小高度
		// 		// if (document.body.clientHeight - 240 < 450) {
		// 		//   return
		// 		// }
		// 		window.screenHeight = document.body.clientHeight - 110
		// 		that.screenHeight = window.screenHeight
		// 	})()
		// }
	},
	methods: {
		onChange(searchText) {
			// return
			let newArr = []
			const value = searchText
			let dataArr = this.viewList
			const expandedKeys = this.viewList
				.map((item) => {
					//  console.log(item)
					if (item.title.indexOf(value) > -1) {
						// console.log(getParentKey(item.title, this.viewList));
						return getParentKey(item.title, this.viewList)
					}
					return null
				})
				.filter((item, i, self) => {
					// console.log( this.viewList,self)
					return item && self.indexOf(item) === i
				})
			dataArr.map((v) => {
				if (expandedKeys) {
					expandedKeys.map((k) => {
						if (v.title == k) {
							newArr.push(v)
						}
					})
				}
			})
			this.replaceData = newArr
			// console.log( expandedKeys,newArr)
			Object.assign(this, {
				expandedKeys,
				searchValue: value,
				autoExpandParent: true,
			})
			this.$emit("serchClick", searchText)
		},

		// popover-button 确认回调
		createOk(model) {
			this.$emit("create-ok", model)
		},
		// 树节点选中
		nodeSelect(selectedKeys, event) {
			console.log(selectedKeys, event)
			this.$emit("node-select", ...arguments)
		},
		//点击复选框
		checkSelect(selectedKeys, event) {
			this.downloadData = selectedKeys
			// this.$emit("node-select", ...arguments);
		},
		newgroupBtn() {
			this.$emit("addGroup", true)
		},

		//导入
		importModel() {
			this.$emit("importModel")
		},
		//导出
		importExport() {
			let query = {}
			if (this.downloadCode == "groupExport") {
				query = {
					code: this.downloadCode,
					param: { oids: this.downloadData },
				}
			} else {
				query = {
					code: this.downloadCode,
					oids: this.downloadData,
				}
			}

			if (this.downloadData.length <= 0) {
				this.$error("请选择需要导出的模版")
			} else {
				this.iconLoading = true
				const accesstoken = getCookie('token')
				// console.log(accesstoken)
				// /permissions/v1/viewPermissionsFile/export
				// /ieconfig/export/exportExcel
				fetch(`${Jw.gateway}${this.importExportUrl}`, {
					method: "post",
					body: JSON.stringify(query),
					headers: {
						appName: Jw.appName,
            accesstoken,
            tenantAlias: getCookie("tenantAlias"),
            tenantOid: getCookie("tenantOid"),
					},
				})
					.then((response) => {
						return response.blob()
					})
					.then((res) => {
						// this.$success("导出成功")
						// if (this.downloadCode == "groupExport") {
							let url = window.URL.createObjectURL(
								new Blob([res], {
									type: "application/vnd.ms-excel",
								})
							)
							let link = document.createElement("a")
							link.href = url
							link.setAttribute("download", this.downloadCode)
							document.body.appendChild(link)
							link.click()
							this.iconLoading = false
						// } else {
            //   console.log(res)
						// 	window.location.href = `${Jw.gateway}${res}`
						// }
					})
					.catch((err) => {
						console.log(err)
						this.iconLoading = false
						this.$error(err.msg || "导出错误，请重试!")
					})
			}
			this.$emit("importExport")
		},
		//新建视图
		updateViews() {
			this.$emit("updateViews")
		},
		//删除
		remove(data) {
			this.$emit("remove", data)
		},
		// 编辑
		edit(data) {
			this.$emit("edit", data)
		},
	},
}
</script>
<style lang="less" scoped>
.over-flow {
	width: 80px;
	display: inline-block;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.title {
	float: left;
}
.ant-card-body {
	:global {
		.ant-tree {
			line-height: 3;
			li {
				position: relative;
			}
		}
	}
}
.ant-card-body .btns {
	float: right;
	// position: absolute;
	right: 10px;
}
.ant-card-body .but_type {
	float: right;
	position: absolute;
	right: 40px;
}
</style>
<style scoped>
.odiv {
	height: 100%;
	width: 100%;
	overflow-x: hidden;
	overflow-y: auto;
}

.views-cust {
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px solid rgba(30, 32, 42, 0.15);
	margin-bottom: 13px !important;
	height: 52px;
	line-height: 52px;
	padding: 0 20px;
}
.groupname {
	font-size: 14px;
	color: rgba(30, 32, 42, 0.85);
}
.add-new-btn {
	margin-bottom: 10px;
	margin-top: 33px;
}
.group-titles {
	font-size: 16px;
	color: #292a2c;
	font-weight: 600;
	height: 46px;
	line-height: 46px;
	display: block;
}

div >>> .ant-col-24 {
	margin-top: -8px;
}

.new-group {
	width: 80px;
	margin-right: 8px;
}
.new-search-btn {
	flex: 1;
	margin-left: 10px;
}
.search-info-btn {
	display: flex;
}
.search-info-btn.newline {
	flex-direction: column;
}
.gutter-example >>> .ant-row > div {
	background: transparent;
	border: 0;
}
.gutter-box {
	padding: 0;
	/* line-height: 40px; */
}

.search-group-btn >>> .ant-select-auto-complete {
	height: 32px;
	flex: 1;
	width: 100%;
	box-sizing: border-box;
}
.ant-popover-inner {
	border-radius: 8px;
}

.new-comfirm-btn {
	float: right;
}
.popovergroup >>> .ant-popover-title {
	font-size: 16px;
	color: #292a2c;
	font-weight: 600;
	height: 56px;
	line-height: 56px;
}
.icon {
	width: 16px;
	height: 16px;
	vertical-align: 0px;
	margin-right: 12px;
}

.search-group ul {
	margin: 10px 0px;
	display: flex;
	flex-direction: column;
	/* padding: 0 20px; */
	overflow-y: auto;
}
.formations {
	font-size: 14px;
	color: rgba(30, 32, 42, 0.65);
	margin-left: 0;
	display: inline-block;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	flex: 1;
}
/deep/ .ant-tree-title {
	display: inline-block;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: flex-start;
}
/deep/ .ant-tree-node-content-wrapper{
  width: 85%;
}
.search-group ul li {
	list-style-type: none;
	height: 32px;
	line-height: 32px;
	display: flex;
	align-items: center;
}
.new-add {
	background: #255ed7;
	width: 100%;
	margin-right: 8px;
	padding: 0;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.search-group-btn {
	/* line-height: 36px; */
	margin: 0 20px;
}
.search-group {
	margin: 0;
	overflow: hidden;
}

.search-group >>> .ant-card-body {
	padding: 0px !important;
}
.group-custom {
	/* background: var(--light);
  box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
  border-radius: 4px;
  padding: 0; */
}

.group-custom h1 {
	height: 52px;
	line-height: 52px;
	font-size: 14px;
	box-sizing: border-box;
	/* padding: 0 20px; */
}

@media screen and (max-width: 1235px) {
	.views-cust {
		display: block;
		height: auto;
	}

	.search-info-btn {
		/* display: block; */
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
}
</style>
