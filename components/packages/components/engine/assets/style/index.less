@import "../../style.less";

.page-container {
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.page-wrapper {
  padding: 0 @space;
}

.group-title {
  font-size: 20px;
  color: @color-font;
  margin-top: @space;
  margin-bottom: @space-large;
}

.header-has-divider {
  /deep/ .ant-layout-content {
    border-top: solid 1px @color-border;
  }
}

.common-hover {
  border-color: @color-active;
  background-color: @color-hover;
}

.common-item {
  box-sizing: border-box;
  border: solid 1px @color-border;
  background-color: @color-background-dark;
  &:hover {
    .common-hover();
  }
}

.common-shadow {
  box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05),
    0 12px 48px 16px rgba(0, 0, 0, 0.03);
}
