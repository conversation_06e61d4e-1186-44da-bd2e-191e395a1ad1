@import "../../style.less";

.button-bar {
  display: flex;
  padding: @space-small;
  border-bottom: solid 1px @color-border;
  box-sizing: border-box;
  .ant-btn {
    padding: @space-mini;
    border-radius: 8px;
    transition: none;
    line-height: 1em;
    & ~ .ant-btn {
      margin-left: @space-mini;
    }
    &:hover {
      background-color: #f0f7ff;
      border-color: #a4c9fc;
    }
    &:focus {
      background-color: #fff;
      border-color: #d9d9d9;
      color: #545454;
    }
  }
  /deep/ .jw-icon {
    font-size: 18px;
  }
}

.empty-info {
  user-select: none;
  flex: 0 0 70%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  color: rgba(30, 32, 42, 0.85);
  img {
    margin-bottom: 20px;
    -webkit-user-drag: none;
  }
  .tips {
    color: rgba(30, 32, 42, 0.45);
    font-size: 12px;
    margin-top: 8px;
    margin-bottom: 16px;
    opacity: 0.8;
  }
}

@plugin-row-height: 32px;
@plugin-content-height: 26px;
@plugin-row-padding: (@plugin-row-height - @plugin-content-height) * 0.5;

.scroll-wrapper {
  flex: 1;
  position: relative;
  padding: 0 @space-mini;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  .item {
    flex: 0 0 auto;
    position: relative;
    border-bottom: solid 1px @color-background-dark;
    padding: 4px 0;
    overflow: hidden;
    .row {
      min-height: @plugin-row-height;
      padding: @plugin-row-padding @space-mini;
      display: flex;
      align-items: center;
      font-size: 12px;
      .label {
        flex: 0 0 60px;
        height: @plugin-content-height;
        padding-right: @space-mini;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        align-self: flex-start;
      }
      .content {
        flex: 1;
        font-size: 12px;
      }
    }
    .title {
      .label {
        cursor: pointer;
        flex: 1;
        justify-content: flex-start;
      }
      .operation {
        cursor: pointer;
        user-select: none;
        width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        &:hover {
          color: @color-active;
        }
      }
    }
  }
  .item.highlight {
    .title .label {
      color: @color-active;
    }
  }
}

.content-selector,
.content-text {
  cursor: pointer;
  background-color: #fff;
  border: solid 1px @color-border;
  border-radius: @radius;
  display: flex;
  align-items: center;
  min-height: @plugin-content-height;
  padding: @space-mini @space-mini;
  &:hover {
    border-color: @color-active;
    color: @color-active;
  }
  &.selecting {
    border-color: @color-active !important;
    color: @color-active !important;
  }
}

.content-text {
  cursor: not-allowed;
  &:hover {
    border-color: @color-border;
  }
}
