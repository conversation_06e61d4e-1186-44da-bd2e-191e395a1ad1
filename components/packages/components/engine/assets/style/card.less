@import "../../style.less";

.card-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  padding-top: @space-large;
  padding-bottom: @space-large;
}

.common-card {
  .common-item();
  height: 160px;
  padding: 20px 20px @space-large;
  border-radius: 16px;
  overflow: hidden;
  background-size: auto 80%;
  background-repeat: no-repeat;
  background-position: center 45%;
}

.flex-card(@n: 1) {
  cursor: pointer;
  position: relative;
  margin-left: @space-small;
  margin-bottom: @space-small;
  flex: 0 0 calc(~"(100% - @{space-small} * (@{n} - 1)) / @{n}");
  @nth: ~":nth-child(@{n}n + 1)";
  &@{nth} {
    margin-left: 0;
  }
}

.page-pagination {
  position: relative;
  padding: @space-small @space;
  display: flex;
  justify-content: flex-end;
  border-top: solid 1px @color-border;
}
