@import "../../style.less";

@cad-button-space-top: @space;
@cad-button-space-left: @space - 2;
@cad-button-space-right: @space - 2 + 120;
@cad-button-space-bottom: @space;
@cad-button-space: 12px;

// cad widget
.dropdown-card {
  .common-shadow();
  position: relative;
  min-width: 105px;
  box-sizing: border-box;
  background: #f3f3f7;
  border-radius: 8px;
  display: flex;
  align-items: center;
  overflow: hidden;
  flex-direction: column;
  padding: @space-mini 0 @space-small;
  .ant-btn {
    margin-top: @space-mini;
    width: 88px;
    display: flex;
    align-items: center;
    padding: 0;
    .icon {
      margin-right: @space-mini;
    }
  }
  /deep/ .dropdown-title {
    align-self: stretch;
    color: rgba(30, 32, 42, 0.45);
    font-size: 12px;
    margin-bottom: @space-mini;
    padding-left: 8px;
    height: 20px;
    display: flex;
    align-items: center;
  }
  /deep/ .dropdown-button {
    display: flex;
    align-self: stretch;
    align-items: center;
    padding: 0 @space-large;
    height: 32px;
    line-height: 0;
    cursor: pointer;
    user-select: none;
    &:hover {
      color: @color-active;
    }
  }
  /deep/ .jw-icon {
    font-size: 18px;
    margin-right: @space-mini;
  }
}
