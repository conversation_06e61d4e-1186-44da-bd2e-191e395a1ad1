@import "../font/iconfont.css";
@import "../mpmFont/iconfont.css";
[class^="jwi-"]:before,
[class*=" jwi-"]:before {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-size: 16px;
    font-family: "jwfont" !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    /* line-height: 1; */

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.base-panel {
    background: #fff;
}
// 报错信息支持\r\n换行
.ant-message-error {
    span {
        white-space: pre-line;
    }
}

.all-background {
    height: 100%;
    background: #fff;
    // box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
    border-radius: 4px;
    padding: 20px;
}

.page-container {
    height: 100%;
}

.detail-drawer-wrap {
    .ant-tabs {
        padding: 5px 24px 0;
    }

    .ant-tabs-nav .ant-tabs-tab {
        padding: 10px 0px;
    }

    .ant-tabs-extra-content {
        line-height: 34px;
    }

    .detail-drawer-body-wrap {
        height: calc(~"100vh - 187px");
        padding: 0 24px;
        overflow: auto;
    }
}

.detail-drawer-foot-wrap {
    height: 70px;
    line-height: 70px;
    text-align: center;
    border-top: 1px solid rgba(30, 32, 42, 0.06);

    .btn-cancel {
        margin-left: 8px;
    }
}

.form-container {
    .ant-form-item {
        margin-bottom: 10px;
    }

    .textarea-input .ant-input {
        height: 110px;
    }

    .form-item-btns {
        margin: 15px 0 0;
        text-align: right;
    }

    .form-btn-cancel {
        margin-left: 8px;
    }
}


.ant-notification-bottomLeft {
    left: 240px !important;
    margin-left: 0 !important;
}


.ant-modal {
    font-family: PingFangSC-Medium;
    box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);
    border-radius: 8px;
    padding-bottom: 0;

    .ant-modal-content {
        background-image: linear-gradient(180deg, #EAF1FF 0%, #FFFFFF 15%);

        .ant-input {
            background: transparent;
        }

        .ant-modal-header {
            background: transparent;
            // height: 60px;
            border-bottom: none;

            .ant-modal-title {
                font-weight: 500;
            }
        }

        .ant-modal-body {
            padding: 10px 24px;

            .layout-body-generated {
                background: transparent;
                padding: 0;

                .column {
                    margin: 0;
                }
            }

            .jw-table .vxe-table--main-wrapper .vxe-table .body--wrapper {
                background: transparent;
            }
        }

        .ant-modal-footer {
            // height: 60px;
            // padding: 14px 24px;
            border-top: none;
            &::after {
                content: '';
                display: block;
                clear: both;
            }
        }
    }
}

.ant-modal {
    .ant-modal-confirm-btns {
        >button {
            float: right;

            &:not(:first-child) {
                margin-right: 15px;
            }
        }
    }
}

.ant-modal {
    .ant-modal-footer>div {
        >button {
            float: right;

            &:not(:first-child) {
                margin-right: 15px;
            }
        }
    }
}