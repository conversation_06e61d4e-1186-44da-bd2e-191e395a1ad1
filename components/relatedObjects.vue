
<template>
  <div class="relation-model-map-wrapper">
    <a-collapse v-model.trim="activeNames" accordion @change="loadCollapseData">
      <a-collapse-panel v-for="(panel) in relatedObjList" :key="panel.oid" :header="panel.relationDisplayName" class="relation-model-tabel">
        <template v-if="isOperable" slot="extra">
          <span class="handle-btn" v-if="ecadList.includes(panel.slaveObjectType)" @click.stop="addECAD(panel)">新增</span>
          <span class="handle-btn" v-if="mcadList.includes(panel.slaveObjectType)" @click.stop="addMCAD(panel)">新增</span>
          <span class="handle-btn" @click.stop="onAdd(panel)">{{$t('txt_add_c')}}</span>
          <span class="handle-btn" @click.stop="onConfirmDelete(panel)">{{$t('txt_delete')}}</span>
        </template>
        <jwTable :height="'270px'" :ref="`ref_${[panel.oid]}`" v-loading='loadingMap[panel.oid]' :columns="_columns" :dataSource="data[panel.oid]" :showPage="false" :selectedRows.sync="tableSelectedRows[panel.oid]" />
      </a-collapse-panel>
    </a-collapse>
    <!-- <DialogTable ref="ref_dialog_table" :visible.sync='visibleDialog' :fetch='searchModelTable' :typeList='typeList' title='添加' @ok="onOK" @cancel='addObjectCancel' /> -->
    <jw-search-engine-modal
      :title="$t('txt_add_c')"
      only-search-object
      :visible.sync="visibleDialog"
      :model-list='typeList'
      @ok='onOK'
    />
    <create-drawer ref="cerateDrawer" @onRefresh="onRefresh"></create-drawer>
  </div>
</template>

<script>
import createDrawer from "/views/product-content/dz-content-manage/create-drawer.vue";
import { jwTable, jwSearchEngineModal } from "jw_frame";
// import DialogTable from "components/add-object-modal";
import ModelFactory from "jw_apis/model-factory";

// 批量添加全局替代件
const addGlobalSubstitutes = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/addGlobalSubstitutes`,
  method: 'post',
});

// 移除全局替代件
const deleteGlobalSubstitute = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/deleteGlobalSubstitute`,
  method: 'post',
});

// 获取相关对象
const relationObjsModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.sysconfigServer}/collectionRule/findByAppliedType`,
  method: "get"
});

// 获取对象列表
const relationSearchModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/relatedObject/fuzzy`,
  method: "post"
});

// 添加对象
const batchAddModel = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/relatedObject/batchAdd`,
  method: "post"
});

// 删除对象
const batchDeleteModel = ModelFactory.create({
  url: `${Jw.gateway}/${
    Jw.foundationServer
  }/instance/relatedObject/batchDelete`,
  method: "post"
});

//查看列表
const searchModelTable = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/instance/versionAble/fuzzyPage`,
  method: "post"
});

export default {
  props: {
    latestVersion: {
      type: String,
      default: ""
    },
    objectDetailsData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  // inject: ["detailsData"],
  // computed: {
  //   objectDetailsData() {
  //     return this.detailsData();
  //   },
  // },
  data() {
    this.selections = null;
    this._initData();
    this._currentObj = {};

    return {
      isBtns: false,
      isSelection: false,
      data: {},
      loadingMap: {},
      activeNames: '',
      tableSelectedRows: [],
      relatedObjList: [],
      visibleDialog: false,
      // 是否为添加借用关系
      isReferenceDoc:false,
      typeList: [],
      ecadList: ['Encapsulation', 'Symbol', 'Datasheet'],
      mcadList: ['MCAD'],
    };
  },

  components: {
    jwTable,
    // DialogTable,
    jwSearchEngineModal,
    createDrawer,
  },

  created() {
    // this.initFetch();
  },

  computed: {
    isOperable() {
      return this.objectDetailsData.displayVersion == this.latestVersion;
    }
  },

  methods: {
    addMCAD(item){
      this._currentObj = item;
      let {catalogOid, catalogType, containerOid, containerType, containerModelDefinition} = this.objectDetailsData
      let locationInfo={
        disabled:true,
        selectDisabled: true,
        catalogOid,
        catalogType,
        containerOid,
        containerType,
        containerModelDefinition,
      }
      this.$refs.cerateDrawer.show({
        title: "新建MCAD",
        modelInfo: {
          layoutName: "create",
          modelName: "MCAD"
        },
        params: {
          url: `${Jw.gateway}/${Jw.customerServer}/mcad/create`,
          locationInfo,
          // activeModle: item.slaveObjectType
          activeModle: 'CADPart'
        }
      });
    },
    addECAD(item){
      this._currentObj = item;
      let {catalogOid, catalogType, containerOid, containerType, containerModelDefinition} = this.objectDetailsData
      let locationInfo={
        disabled:true,
        selectDisabled: true,
        catalogOid,
        catalogType,
        containerOid,
        containerType,
        containerModelDefinition,
      }
      this.$refs.cerateDrawer.show({
        title: "新建ECAD",
        modelInfo: {
          layoutName: "create",
          modelName: "ECAD"
        },
        params: {
          url: `${Jw.gateway}/${Jw.cadService}/ecad/create`,
          locationInfo,
          activeModle: item.slaveObjectType
        }
      });
    },
    onRefresh(data){
      let params = {
        ...this._currentObj,
        mainObjectOid: this.objectDetailsData.oid,
        slaveObjectOids: [data.itemObj.oid]
      };

      batchAddModel.execute(params).then(res => {
        this.loadFetch(this._currentObj.oid);
      });
    },
    searchModelTable(params) {
      return searchModelTable.execute(params);
    },
    initFetch() {
      let params = {
        appliedType: `${this.objectDetailsData.modelDefinition}_Related_Object`,
        mainObjectType: this.objectDetailsData.modelDefinition
      };
      relationObjsModel
        .execute(params)
        .then(res => {
          if (this.isOperable) {
            res.forEach(item => {
              this.tableSelectedRows[item.oid] = [];
            });
          }

          this.relatedObjList = res;
        })
        .catch(err => {
          this.$error(this.$t('txt_get_object_list'));
        });
    },
    onExpandPanel(key) {
      this.activeNames = key
    },
    onAdd(itemObj) {
      this.isReferenceDoc = "Document_Related_Object" === itemObj.appliedType
      let code = itemObj.slaveObjectClassName;
      let name = itemObj.relationDisplayName;
      let value;

      this.onExpandPanel(itemObj.oid);
      this.visibleDialog = true;
      this._currentObj = itemObj;
      if (code == 'MCADDocumentIteration' || code == 'MCADIteration' || code == 'ECADDocumentIteration' || code == 'ECADIteration')
      {
        value = code == 'MCADDocumentIteration' || code == 'MCADIteration' ? 'MCADIteration' : 'ECADIteration';
        // code = 'CAD';
      }
      else if (code == 'ECR' || code == 'ECO' || code == 'ECA')
      {
        value = code;
        code = 'Change';
      }

      this.typeList = [
        {
          name,
          code,
          value,
        }
      ];
      // this.$refs.ref_dialog_table.show();
    },
    onOK(rows) {
      let hasError;
      let hasWLError;

      let relationDisPlayName = this._currentObj.relationDisplayName;
      let slaveObjectOids = rows.map(row => {
        if(this.isReferenceDoc && 'Released' !== row.lifecycleStatus) {{
          hasError = true
        }}
        if (
            relationDisPlayName &&
            ['图符维护', '封装维护', '相关MCAD图档'].includes(relationDisPlayName) &&
            'Released' !== row.lifecycleStatus
        ) {
          hasWLError = true;
        }
        return row.oid;
      });
      if(hasError) {
        this.$error("被借用的文档必须是已发布状态");
        return
      }
      if(hasWLError) {
        this.$error("被关联的对象必须是已发布状态");
        return
      }

      this.visibleDialog = false;
      // 特殊处理 GLOBAL_SUBSTITUTE
      if (this._currentObj.relationshipName === 'GLOBAL_SUBSTITUTE') {
        let selected = rows.map(item => ({
          fromOid: this.objectDetailsData.masterOid, // 主对象信息取 detail
          fromType: this.objectDetailsData.masterType,
          toOid: item.masterOid,
          toType: item.masterType,
          mutual: true,
          type: 'GLOBAL_SUBSTITUTE',
        }));

        addGlobalSubstitutes.execute(selected).then(res => {
          // this.$success(this.$t('txt_add_success'));
          // this.$emit('getTableData'); // 如果有必要触发表格刷新
          this.loadFetch(this._currentObj.oid);
        }).catch(err => {
          this.$error(err.msg || '添加失败');
        });
      } else {
        // 默认走原始逻辑
        let params = {
          ...this._currentObj,
          mainObjectOid: this.objectDetailsData.oid,
          slaveObjectOids: slaveObjectOids,
        };

        batchAddModel.execute(params).then(res => {
          this.loadFetch(this._currentObj.oid);
        });
      }
    },
    addObjectCancel() {
      this.visibleDialog = false;
    },
    onConfirmDelete(itemObj) {
      let key = itemObj.oid;
      let rows = this.tableSelectedRows[key];

      this.onExpandPanel(key);
      if (_.isEmpty(rows)) {
        return this.$warning(this.$t('txt_pls_data'));
      }
      this.$confirm({
        title: "Wraining",
        content: this.$t('confirm_deletion'),
        okText: this.$t('btn_ok'),
        cancelText: this.$t('btn_cancel'),
        onOk: () => {
          this.onDelete(itemObj, rows);
        }
      });
    },
    onDelete(itemObj, rows) {
      this._currentObj = itemObj;
      if (itemObj.relationshipName === 'GLOBAL_SUBSTITUTE') {
        // 特殊处理全局替代删除
        let deleteRequests = rows.map(row => {
          return deleteGlobalSubstitute.execute({
            oid: row.oid,
            fromOid: this.objectDetailsData.masterOid,  // 取主对象master信息
            fromType: this.objectDetailsData.masterType,
            toOid: row.masterOid,
            toType: row.masterType,
            mutual: row.relationship?.mutual ?? true,
            type: itemObj.relationshipName,
          });
        });

        Promise.all(deleteRequests)
            .then(() => {
              this.$success(this.$t('txt_delete_success'));
              this.loadFetch(itemObj.oid);
            })
            .catch(err => {
              this.$error(err.msg || '删除失败');
            });

      } else {
        // 默认关系删除逻辑
        let slaveObjectOids = rows.map(row => row.oid);
        let params = {
          ...itemObj,
          mainObjectOid: this.objectDetailsData.oid,
          slaveObjectOids: slaveObjectOids,
        };

        batchDeleteModel.execute(params).then(res => {
          this.loadFetch(itemObj.oid);
        });
      }
    },
    _initData() {
      this._columns = [
        {
          field: "name",
          title: this.$t('txt_name'),
          cellRender: {
            name: "link",
            showLock: true,
            events: {
              click: ({ row }) => {
                this.onJumpTo(row);
              }
            }
          },
          formatter: ({ text, row }) => {
            return row.cname || row.name
          }
        },
        {
          field: "modelDefinition",
          title: this.$t('txt_type')
        },

        {
          field: "number",
          title: this.$t('txt_number')
        },
        {
          field: "lifecycleStatus",
          title: this.$t('txt_plan_lifecycle')
        },

        {
          field: "displayVersion",
          title: this.$t('txt_version')
        },

        {
          field: "updateDate",
          title: this.$t('txt_update_date'),
          width: 180,
          cellRender: {
            name: "date"
          }
        }
      ];
    },

    //打开文件夹或文档
    onJumpTo(data) {
      Jw.jumpToDetail(data,{
        blank:true
      });
      // window.open(`#/detailPage?oid=${data.oid}&type=${data.type}&masterType=${data.masterType}&modelDefinition=${data.modelDefinition}`, "_blank");
    },

    loadCollapseData(activeName) {
      if(activeName){
        this.loadingMap[activeName] = true;
        this.loadFetch(activeName);
      }
    },

    loadFetch(activeName) {
      this.loadingMap[activeName] = true;
      let row = this.relatedObjList.find(item => {
        return item.oid == activeName;
      });
      relationSearchModel
        .execute({
          ...row,
          mainObjectOid: this.objectDetailsData.oid
        })
        .then(data => {
          this.data[activeName] = data;
          this.loadingMap[activeName] = false;
          this.data = _.clone(this.data);
        })
        .catch(() => {
          this.data[activeName] = [];
          this.loadingMap[activeName] = false;
        });
    }
  }
};
</script>

<style lang="less" scoped>
.relation-model-map-wrapper {
  height: 100%;
  overflow-y: auto;
  ::v-deep.ant-collapse {
    border: none;
    .ant-collapse-content {
      height: 300px;
      .ant-collapse-content-box {
        height: 100%;
      }
    }
    .handle-btn {
      &:hover {
        cursor: pointer;
        color: @primary-color;
      }
    }
  }

  .relation-model-tabel {
    position: relative;
    border: 1px solid #d5d5d5;

    border-radius: 5px;
    margin-bottom: 10px;

    .btn-tool {
      position: absolute;
      right: 30px;
      top: 0;
    }
  }

  .jw-table {
    height: auto;
    border: none;
  }
}
</style>
