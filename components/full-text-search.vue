<template>
  <div class="es-wrapper ">
    <a-spin class="loading-icon" v-if="_.isEmpty(modelDatas)" />
    <div v-else class="model-tabs">
      <a-tabs :default-active-key="activeTag" @change="callback">
        <a-tab-pane v-if="modelDatas[modelTab.tag]" :tab="modelTab.displayTag" v-for="(modelTab) in modelTags" :key="modelTab.tag">
          <div class="scroll-box" @scroll="onScroll($event,modelTab)">
            <div class="model-item" v-for="(item,i) in modelDatas[modelTab.tag]" :key="i" @click="toModelInfoPage(modelTab,item)">
              <svg class="icon model-icon" aria-hidden="true">
                <use xlink:href="#jwi-tag"></use>
              </svg>
              <div class="model-text">
                <div v-html="item[modelTab['名称']]"></div>
                <span class="create-by-text">{{item.createBy}}</span>
                <span class="create-date-text">{{item.createDate}}</span>
              </div>
            </div>
            <div class="no-more-text" v-if="noMoreFlag">没有更多了</div>
            <a-spin class="loading-more" :spinning='loadingMore' />
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>
<script>
import { formatDate } from "jw_utils/moment-date";
import { getLocal } from "jw_utils/cookie";
//接口
import { searchApi, indexConfigApi } from "apis/full-text-search";


let dataxs = [
  "npi_task",
  "npi_project",
  "npi_issue",
  "npi_risk",
  "npi_checklist",
  "npi_template",
  "npi_template_task"
];

export default {
  data() {
    this._searchKey = getLocal("searchKey");
    return {
      modelTags: [],
      modelDatas: {},
      activeTag: "",
      currentPage: 1,
      pageSize: 50,
      loadFlag: true,
      noMoreFlag: false,
      loadingMore: false
    };
  },

  created() {
    this.fetchIndexConfig();
  },

  mounted() {
    Jw.EventBus.once("change-searchKey", () => {
      this.currentPage = 1;
      this.modelDatas = {};
      this.fetchSearch();
    });
    this.fetchSearch();
  },

  methods: {
    async onScroll(e, modelTab) {
      if (this.noMoreFlag) return;
      const ele = e.srcElement ? e.srcElement : e.target;
      let criticalValue =
        ele.scrollTop + ele.offsetHeight > ele.scrollHeight - 100;

      if (criticalValue && this.loadFlag) {
        this.loadFlag = false;
        this.currentPage++;
        await this.fetchSearch();
        _.delay(() => {
          this.loadFlag = true;
        }, 100);
      }
    },
    toModelInfoPage(modelTab, row) {
      //1 获取ES数据 包含
      //  项目名称
      //  增加url字段
      //  增加页签显示字段
      //  增加icon字段
      //  增加mapping映射字段
      //  增加数据关联字段
      //2.路由参数字段和数据字段保持一直 前端替换
      let url = modelTab.url;
      url = url.replace(/:(\w+)(?=\/?)/g, param => {
        return row[param.slice(1)];
      });
      window.open(`/#${url}`);
    },
    fetchIndexConfig() {
      indexConfigApi.execute().then(res => {
        res.forEach(item => {
          if (item.displayAttributes) {
            item.displayAttributes.forEach(field => {
              item[field.displayName] = field.attribute;
            });
          }
        });
        this.modelTags = res;
        this.activeTag = this.modelTags[0].tag;
      });
    },

    callback() {},
    fetchSearch() {
      this.loadingMore = true;
      return searchApi
        .execute({
          currentPage: this.currentPage,
          pageSize: this.pageSize,
          search: this._searchKey,
          indexs: dataxs,
          fields: ["name", "createBy", "templateName"]  //后台从数据字典获取
        })
        .then(res => {
          this.loadingMore = false;
          if (_.isEmpty(res.recordList)) {
            this.noMoreFlag = true;
            return;
          }
          res.recordList.forEach(item => {
            item.createDate = formatDate(
              item.createDate,
              "YYYY-MM-DD HH-mm:ss"
            );
            if (this.modelDatas[item.tableTag]) {
              this.modelDatas[item.tableTag].push(item);
            } else {
              this.modelDatas[item.tableTag] = [item];
            }
          });
          this.modelDatas = Object.assign({}, this.modelDatas);
        })
        .catch(error => {
          this.loadingMore = false;
          this.$error(error.msg || $t("msg_failed"));
        });
    }
  },
  beforeDestroy() {
    Jw.EventBus.emit("clear-searchKey");
  }
};
</script>
<style lang="less" scoped>
.es-wrapper {
  height: 100%;
  background: #fff;
  .loading-icon {
    text-align: center;
    width: 100%;
    border-radius: 4px;
    margin-bottom: 20px;
    padding: 30px 50px;
    margin: 20px 0;
  }
  .model-tabs {
    height: 100%;
    /deep/ .ant-tabs {
      height: 100%;
      .ant-tabs-content {
        height: calc(~"100% - 60px");
        .ant-tabs-tabpane {
          overflow-y: auto;
          .scroll-box {
            height: 100%;
            overflow-y: auto;
            .no-more-text {
              text-align: center;
              margin: 10px 0;
              line-height: 25px;
            }
            .loading-more {
              width: 100%;
            }
          }
        }
      }
    }

    .model-item {
      position: relative;
      padding: 10px;
      background: #f7f5f0;
      width: 60%;
      margin: 0 auto 10px;
      cursor: pointer;
      .model-icon {
        height: 40px;
        width: 40px;
      }
      .model-text {
        position: absolute;
        top: 10px;
        left: 60px;

        .create-by-text {
          margin-right: 10px;
          color: #ccc;
        }
        .create-date-text {
          color: #ccc;
        }
      }
    }
  }
}
</style>

