<template>
  <a-popover
    :overlayClassName="'thumbnail-container'"
    :placement="thumbPosition || 'left'"
    trigger="hover"
  >
    <template slot="content">
      <p
        class="thumbnail-content"
        :style="{ backgroundImage: thumbnail ? `url(${thumbnail})` : '' }"
        @click="handleShowModal"
      >
        <!-- <img width="200px" height="200px" :src="thumbnail" /> -->
        <span class="modal-text">
          点击用3DXViewer打开
        </span>
      </p>
    </template>
    <div
      :style="{ backgroundImage: !thumbnailLoading ? `url(${thumbnail})` : '', ...miniStyle }"
      class="thumb-background"
    >
      <div v-if="thumbnailLoading" class="loadingspin">
        <a-spin class="thumb-loading" :spinning="thumbnailLoading">
          <!-- <img class="thumb-mini" :style="miniStyle" :src="thumbnail" /> -->
        </a-spin>
      </div>
    </div>
  </a-popover>
</template>

<script>
import ModelFactory from "jw_apis/model-factory";
import { getCookie } from "jw_utils/cookie";
import { getDropdownList } from "/apis/part/index";

// 通过part获取CAD的Oid
const fetchCadOid = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/findBeDescribed`,
  method: "post",
});

/**
 * @param currentRow
 *
 * 当前列的数据
 *
 * @param miniStyle
 *
 * 缩略图的样式
 *
 * @param thumbPosition
 *
 * 大图在缩略图的位置
 * 参考：https://1x.antdv.com/components/popover-cn/#API
 *
 *
 * */
export default {
  name: "cadThumbnail",
  props: ["currentRow", "miniStyle", "thumbPosition"],
  data() {
    return {
      thumbnail: "",
      thumbnailLoading: false,
      controller: null,
    };
  },
  watch: {
    currentRow: {
      handler() {
        this.thumbnail = ''
        this.init();
      },
      immediate: true,
      deep: true,
    },
  },
  created() {},
  destroyed(){
    if(this.controller){
      this.controller.abort()
    }
  },
  methods: {
    handleShowModal() {
      let { currentRow } = this;
      let viewCode = currentRow.masterType === "Part" ? 'PARTINSTANCE' : 'MCADDOCUMENTINSTANCE'
      getDropdownList.execute({viewCode,objectOid: currentRow.oid}).then(resp => {
        let detail = resp.find(row => row.code === "details")
        if(detail.status === 'enable'){
          //跳转
          this.jumpDetail()
        }else{
          this.$error("无查看详情权限")
        }
      })
    },
    jumpDetail(){
      let { currentRow } = this;
      let param = {
        fromType: "MCADIteration",
        toOid: currentRow.oid,
        toType: "PartIteration",
      };
      if (currentRow.masterType === "Part") {
        fetchCadOid
          .execute(param)
          .then((data) => {
            if(data.length > 0){
              let maxLeavedata = null
              data.forEach(item => {
                if((maxLeavedata === null || maxLeavedata.versionSortId < item.versionSortId) && item.modelDefinition !== 'CADDrawing'){
                  maxLeavedata = item
                }
              })
              if(maxLeavedata){
                let router = this.$router.resolve({
                  path: `/3d-model-show/${maxLeavedata.oid}`,
                });
                window.open(router.href, "_blank");
              }else{
                console.error("未找到对应mcad")
              }
            }else{
              this.$error(this.$t('no_cad_file'));
            }
          })
          .catch((err) => {
            this.$error(err.msg || this.$t("msg_failed"));
          });
      } else {
        let router = this.$router.resolve({
          path: `/3d-model-show/${currentRow.oid}`,
        });
        window.open(router.href, "_blank");
      }
    },
    init() {
      let { currentRow } = this;
      // 使用图片缩略图oid获取图片地址
      let thumbnailFile = currentRow?.extensionContent?.thumbnailFile
      let fileOid = currentRow.thumbnailOid || (thumbnailFile && thumbnailFile.length ? thumbnailFile[0].oid : null)
      if(!fileOid){
        return
      }
      this.thumbnailLoading = true;
      const accesstoken = getCookie('token');
      //取消请求时使用
      if(this.controller){
        this.controller.abort()
      }
      this.controller = new AbortController();
      const { signal } = this.controller;

      fetch(
        `${Jw.gateway}/${Jw.fileMicroServer}/file/downloadByOid?fileOid=${fileOid}`,
        {
          method: "get",
          headers: {
            "Content-Type": "application/json;charset=utf8",
            appName: Jw.appName,
            accesstoken,
            tenantAlias: getCookie("tenantAlias"),
            tenantOid: getCookie("tenantOid"),
          },
          signal
        }
      )
        .then((response) => {
          return response.blob();
        })
        .then((data) => {
          let url = window.URL.createObjectURL(
            new Blob([data], {
              type: "application/vnd.ms-excel",
            })
          );
          this.thumbnail = url;
          this.thumbnailLoading = false;
        })
        .catch((err) => {
          this.thumbnailLoading = false;
          console.log(err)
        });
    },
  },
};
</script>
<style lang="less" scoped>
/deep/.ant-popover-inner-content {
  padding: 0;
}
</style>
<style lang="less" scoped>
.thumbnail-content {
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  width: 200px;
  height: 200px;
  background-position: center center;
  background-size: contain;
  background-repeat: no-repeat;
  cursor: pointer;
  .modal-text {
    position: absolute;
    bottom: -10px;
    font-size: 12px;
    text-align: right;
    color: rgb(180, 184, 194);
  }
}
.thumb-loading {
}
.loadingspin{
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
.thumb-background {
  background-position: center center;
  background-size: auto 100%;
  background-repeat: no-repeat;
  cursor: pointer;
  height: 100%;
}
.thumb-mini {
  border-radius: 4px;
  cursor: pointer;
}
</style>
