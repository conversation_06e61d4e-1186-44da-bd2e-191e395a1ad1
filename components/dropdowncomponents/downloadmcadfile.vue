<template>
  <a-modal
    title="下载文件"
    :visible="visible"
    :confirm-loading="loading"
    @ok="download"
    @cancel="handleCancel"
    okText="确认"
    cancelText="取消"
  >
    <a-radio-group v-model.trim="type">
      <a-radio value="primary"> 下载原模型文件 </a-radio>
      <a-radio value="stp"> 下载原模型STP格式文件 </a-radio>
    </a-radio-group>
  </a-modal>
</template>

<script>
import { downloadstepZip } from './api'
export default {
  data() {
    return {
      oid: '',
      type: 'primary',
      loading: false,
      visible: false,
    }
  },
  methods: {
    show(oid) {
      this.oid = oid
      this.visible = true
    },
    handleCancel() {
      this.oid = ''
      this.visible = false
    },
    download() {
      this.loading = true
      downloadstepZip(this.oid, this.type)
        .then((resp) => {
          if(resp.type === 'application/json'){
            var reader = new FileReader();
            reader.readAsText(resp , 'utf-8');
            reader.onload = (e) => {
              let readerres = reader.result;
              let parseObj = JSON.parse(readerres);
              this.$error(parseObj.msg)
            }
          }else{
            let url = window.URL.createObjectURL(new Blob([resp]))
            let link = document.createElement('a')
            let fileName = (this.type === 'stp' ? 'stp' : '原模型') + '.zip'
            link.href = url
            link.style.display = 'none'
            link.setAttribute('download', fileName)
            document.body.appendChild(link)
            link.click()
          }
        })
        .catch((e) => {
          console.error(e)
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style lang="less" scoped>
</style>