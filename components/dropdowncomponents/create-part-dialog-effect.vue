<template>
  <a-modal
    width="65%"
    v-model.trim="showdialog"
    :title="cadtopart ? $t('txt_generating_unit') : $t('txt_parttocad')"
    :okText="$t('btn_ok')"
    :cancelText="$t('btn_cancel')"
    @ok="confirm"
    @cancel="cancel"
    :confirmLoading="confirmLoading"
  >
    <div class="content-head">
      <div class="head-line">
        <div class="head-label">{{ $t("tabel_context") }}：</div>
        <contexttree :defaultvalue="record.catalogOid" :containerOid="record.containerOid" @input="selectTree" />
      </div>

      <div class="head-line">
        <div class="head-label">
          {{ $t("txt_effectivity_create_sub") }}
          {{ cadtopart ? "Part" : "CAD" }}？
        </div>
        <a-radio-group
          :options="[
            { label: $t('txt_yes'), value: true },
            { label: $t('txt_no'), value: false },
          ]"
          v-model.trim="createsub"
        />
      </div>
      <div class="head-line">
        <vxe-switch
          v-model.trim="relationupgrade"
          :open-label="$t('txt_effectivity_upgrade')"
          :open-value="1"
          :close-label="$t('txt_effectivity_relation')"
          :close-value="0"
        ></vxe-switch>
      </div>
    </div>
    <div class="content-table-effect">
      <vxe-table
        border
        show-overflow
        highlight-hover-row
        height="700"
        ref="xTree"
        :tree-config="{ children: childrenkey, expandAll: true }"
        :data="tableData"
        :loading="tableloading"
      >
        <template v-if="cadtopart">
          <vxe-table-column
            field="number"
            :title="'CAD' + $t('txt_number_of')"
            tree-node
          >
            <template #default="{ row }">
              <div>
                <jw-icon :type="row.modelIcon"></jw-icon>
                <span> {{ row.number }} </span>
              </div>
            </template>
          </vxe-table-column>
          <vxe-table-column
            field="displayVersion"
            :title="'CAD' + $t('txt_version')"
          ></vxe-table-column>
          <vxe-table-column
            field="relationParts"
            :title="'Part' + $t('txt_number_of')"
          >
            <template v-slot="{ row }">
              <div v-if="row.relationParts">
                <div v-for="item in row.relationParts" :key="item.oid">
                  {{ item.number }}
                </div>
              </div>
            </template>
          </vxe-table-column>
          <vxe-table-column
            field="relationParts"
            :title="'Part' + $t('txt_version')"
          >
            <template v-slot="{ row }">
              <div v-if="row.relationParts">
                <div v-for="item in row.relationParts" :key="item.oid">
                  {{ item.displayVersion }}
                </div>
              </div>
            </template>
          </vxe-table-column>
        </template>
        <template v-else>
          <vxe-table-column
            field="number"
            :title="'Part' + $t('txt_number_of')"
            tree-node
          >
            <template #default="{ row }">
              <div>
                <jw-icon :type="row.modelIcon"></jw-icon>
                <span> {{ row.number }} </span>
              </div>
            </template>
          </vxe-table-column>
          <vxe-table-column
            field="displayVersion"
            :title="'Part' + $t('txt_version')"
          ></vxe-table-column>
          <vxe-table-column
            field="relationMcad.number"
            :title="'CAD' + $t('txt_number_of')"
          ></vxe-table-column>
          <vxe-table-column
            field="relationMcad.displayVersion"
            :title="'CAD' + $t('txt_version')"
          ></vxe-table-column>
        </template>
        <vxe-table-column
          v-if="!cadtopart"
          field="relationMcad.action"
          :title="$t('txt_operation')"
        >
          <template v-slot="{ row }">
            <vxe-switch
              v-if="row.relationMcad.oid && row.relationMcad.action !== 2"
              v-model.trim="row.relationMcad.action"
              :open-label="$t('txt_effectivity_upgrade')"
              :open-value="1"
              :close-label="$t('txt_effectivity_relation')"
              :close-value="0"
            ></vxe-switch>
            <div v-else>
              <vxe-switch
                v-model.trim="row.relationMcad.action"
                :open-label="$t('txt_link')"
                :open-value="2"
                :close-label="$t('btn_create')"
                :close-value="null"
                @change="(check) => changeadddata(row, check)"
              ></vxe-switch>
            </div>
          </template>
        </vxe-table-column>

        <vxe-table-column
          v-else
          field="relationParts.action"
          :title="$t('txt_operation')"
        >
          <template v-slot="{ row }">
            <div v-if="row.relationParts">
              <div v-for="item in row.relationParts" :key="item.oid">
                <vxe-switch
                  v-if="item.oid && row.hasLinkedPart !== false && item.action !== 2"
                  v-model.trim="item.action"
                  :open-label="$t('txt_effectivity_upgrade')"
                  :open-value="1"
                  :close-label="$t('txt_effectivity_relation')"
                  :close-value="0"
                ></vxe-switch>
                <div v-else-if="row.hasLinkedPart === false && item.oid && item.action !== 2">
                  {{ item.action ? $t('txt_effectivity_upgrade') : $t('txt_effectivity_relation') }}
                </div>
                <div v-else>
                  <vxe-switch
                    v-model.trim="item.action"
                    :open-label="$t('txt_link')"
                    :open-value="2"
                    :close-label="$t('btn_create')"
                    :close-value="null"
                    @change="(check) => changeadddata(row, check)"
                  ></vxe-switch>
                </div>
              </div>
            </div>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
  </a-modal>
</template>

<script>
import contexttree from "./subcomponents/contexttree.vue";
import {
  getlistPartToCad,
  savePartToCad,
  getlistCadToPart,
  saveCadToPart,
  getDefaultCad,
  getDefaultPart
} from "./api/index";
import { jwIcon } from "jw_frame";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    record: {
      type: Object,
      default: () => {},
    },
    cadtopart: {
      type: Boolean,
      default: true,
    },
  },
  components: { contexttree, jwIcon },
  data() {
    return {
      tableloading: false,
      //是否创建子结构
      createsub: true,
      //关联不升版 ，升版
      relationupgrade: this.cadtopart ? 1 : 0,
      relationupgradeoption: [
        { value: 1, text: this.$t("txt_effectivity_upgrade") },
        { value: 0, text: this.$t("txt_effectivity_relation") },
      ],

      childrenkey: "children",
      tableData: [],

      confirmLoading: false,

      locationdata: {},
    };
  },

  computed: {
    showdialog: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
  watch: {
    relationupgrade: function (val) {
      this.initrelationupgrade();
    },
    createsub: function (val) {
      this.loadlistdata();
    },
  },
  created() {
    this.loadlistdata();
  },
  methods: {
    changeadddata(row, { value }){
      if(value === 2){
        let api = this.getDefaultApi()
        api(row.oid).then(resp => {
          if(!resp){
            this.$warning(this.$t('txt_not_link'))
            return
          }
          resp.action = 2
          if(!this.cadtopart){
            row.relationMcad = Object.assign({}, resp)
          }else{
            row.relationParts = [Object.assign({}, resp)]
          }
        }).catch(e => {
          console.error(e)
        })
      }else{
        if(!this.cadtopart){
            row.relationMcad = Object.assign({}, { action: null })
          }else{
            row.relationParts = [Object.assign({}, { action: null })]
          }
      }
    },
    getDataKey() {
      if (this.cadtopart) {
        return "relationParts";
      } else {
        return "relationMcad";
      }
    },
    getDefaultApi(){
      if(this.cadtopart){
        return getDefaultPart
      }else{
        return getDefaultCad
      }
    },
    //获取列表api
    getlistApi() {
      if (this.cadtopart) {
        return getlistCadToPart;
      } else {
        return getlistPartToCad;
      }
    },
    // 获取保存接口
    getSaveApi() {
      if (this.cadtopart) {
        return saveCadToPart;
      } else {
        return savePartToCad;
      }
    },
    loadlistdata() {
      let req = this.getlistApi();
      this.tableloading = true;
      req(this.record.oid, this.createsub)
        .then((resp) => {
          this.tableData = [resp];
          this.initrelationupgrade();
          this.$nextTick(() => {
            this.autoTriggerLinkSwitch();
          });
        })
        .catch((e) => {
          console.error(e);
          this.$error(e.msg);
        })
        .finally(() => {
          this.tableloading = false;
        });
    },
    initrelationupgrade() {
      this.tableData.forEach((item) => {
        this.childreninit(
          item,
          this.childrenkey,
          "action",
          this.relationupgrade
        );
      });
    },
    childreninit(data, childrenkey, paramkey, value) {
      this.bindvalue(data, paramkey, value);
      if (data[childrenkey]) {
        this.childrenset(data[childrenkey], childrenkey, paramkey, value);
      }
    },

    childrenset(val, childrenkey, paramkey, value) {
      val.forEach((item) => {
        this.bindvalue(item, paramkey, value);
        if (item[childrenkey]) {
          this.childrenset(item[childrenkey], childrenkey, paramkey, value);
        }
      });
    },

    bindvalue(data, paramkey, value) {
      // cad生成part时值为false时必须要升级版本
      if(data.hasLinkedPart === false && this.cadtopart){
        value = 1
      }

      if (!data[this.getDataKey()]) {
        data[this.getDataKey()] = this.cadtopart ? [{}] : {};
      }
      if (Array.isArray(data[this.getDataKey()])) {
        data[this.getDataKey()].forEach((item1) => {
          this.$set(item1, paramkey, value);
        });
      } else {
        this.$set(data[this.getDataKey()], paramkey, value);
      }
    },

    selectTree(val, objdata) {
      this.locationdata = objdata;
    },
    confirm() {
      this.confirmLoading = true;
      let req = this.getSaveApi();
      const { containerOid, containerType, oid, type } = this.locationdata;
      let param = {
        locationInfo: {
          containerOid,
          containerType,
          catalogOid: oid,
          catalogType: type,
        },
      };
      if (this.cadtopart) {
        param.mcadbomNode = this.tableData[0];
      } else {
        param.bomNode = this.tableData[0];
      }
      req(param)
        .then((resp) => {
          this.$success(this.$t("msg_success"));
          this.showdialog = false;
          this.$emit("reloadlistdata");
        })
        .catch((e) => {
          console.error(e);
          this.$error(e.msg);
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    cancel() {
      this.showdialog = false;
    },
    autoTriggerLinkSwitch() {
      if (!this.cadtopart) return;

      const processNode = (node) => {
        console.log(node)
        if (node.relationParts && node.relationParts.length > 0) {
          node.relationParts.forEach(item => {
            console.log(item.number+"---"+item.action)
            if (!item.number) {
              this.changeadddata(node, { value: 2 });
            }
          });
        }

        if (node[this.childrenkey] && node[this.childrenkey].length > 0) {
          node[this.childrenkey].forEach(childNode => processNode(childNode));
        }
      };

      this.tableData.forEach(node => processNode(node));
    },
  },
};
</script>
<style>
.content-table-effect .vxe-table--render-default .vxe-body--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default .vxe-header--column.col--ellipsis > .vxe-cell {
  max-height: none;
}
</style>

<style lang="less" scoped>
.head-line {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.head-label {
}

.content-head {
  display: flex;
}

.content-table {
  margin-top: 15px;
}

.selectupgrade {
  width: 80px;
}
</style>