import ModelFactory from "jw_apis/model-factory"

// 文件夹目录
export const customerFetchFolderTree = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.customerServer}/pdm-folder/searchTree`,
  method: "get",
})


// 文件夹目录
export const fetchfolderTree = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/folder/searchTree`,
  method: "get",
})

export const fetchRootFolder = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.containerService}/folder/searchRootFolder`,
  method: "get",
})

// MCAD  生成Part
export const MCAD_createPart = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/generateMCADBOM`,
  method: "post",
})

// 获取 part转cad列表
export const getlistPartToCad = function (partOid, isBom) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.cadService}/mcad/collectPartCad?partOid=${partOid}&isBom=${isBom}`,
    method: "get",
  }).execute()
}

//下载文件
export const downloadstepZip = function (oid, type) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.cadService}/mcad/download/CADFile?oid=${oid}&type=${type}`,
    method: "post",
    responseType: "blob",
  }).execute()
}

// 保存part转cad
export const savePartToCad = function (data) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.cadService}/mcad/generateMCADByPart`,
    method: "post",
  }).execute(data)
}

// 获取 cad转part列表
export const getlistCadToPart = function (mcadOid, isBom) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/collectCadPart?mcadOid=${mcadOid}&isBom=${isBom}`,
    method: "get",
  }).execute()
}

//保存mcad转part
export const saveCadToPart = function (data) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/generatePartByMcad`,
    method: "post",
  }).execute(data)
}

//无对应cad时获取默认的cad
export const getDefaultCad = function (partOid) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/generateMCADByMappingRule`,
    method: "get",
  }).execute({ partOid })
}

//无对应part时获取默认的part
export const getDefaultPart = function (cadOid) {
  return ModelFactory.create({
    url: `${Jw.gateway}/${Jw.cadService}/mcad/generatePartByMappingRule`,
    method: "get",
  }).execute({ cadOid })
}

// 判断物料去重
export const findSamePartData = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.partBomMicroServer}/part/findSamePartData`,
  method: "post",
})
