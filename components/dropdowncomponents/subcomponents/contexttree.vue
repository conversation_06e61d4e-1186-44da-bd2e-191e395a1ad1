<template>
  <a-tree-select
    v-model.trim="contentvalue"
    :defaultValue="defaultvalue"
    :show-search="true"
    :placeholder="$t('msg_select')"
    style="width: 250px"
    :tree-node-filter-prop="treeNodeFilterProp"
    :filter-tree-node="filterTreeNode"
    :dropdown-style="dropdownStyle"
    :tree-data="treeData"
    @change="treeSelect"
    treeDefaultExpandAll
  >
    <template slot="folderIconTitle" slot-scope="{ name, childType }">
      <div>
        <jw-icon
          style="margin-right: 8px"
          :type="
            childType === 'child' ? '#jwi-wenjianga-youneiyong' : '#jwi-chanpin'
          "
        />
        <span>{{ name }}</span>
      </div>
    </template>
  </a-tree-select>
</template>

<script>
import { fetchfolderTree } from "../api/index";
export default {
  props: {
    defaultvalue: {
      type: String,
      default: "",
    },
    containerOid: {
      type: String,
      default: "",
    }
  },
  data() {
    return {
      label: this.$t("tabel_context"),
      type: "tree",
      prop: "position",
      showSearch: true,
      treeData: [],
      block: true,
      dropdownStyle: {
        height: "400px",
        overflowY: "auto",
      },
      treeNodeFilterProp: "title",
      contentvalue: "",

      listdata: [],
    };
  },
  created() {
    this.openDialogLoadTree();
  },
  watch: {
    defaultvalue: function (val) {
      this.contentvalue = val;
      this.treeSelect(this.contentvalue);
    },
  },
  methods: {
    treeSelect(val) {
      let objdata = this.findtreeobjdata(val);
      this.$emit("input", val, objdata);
    },
    openDialogLoadTree() {
      fetchfolderTree
        .execute({
          containerModel: "",
          containerOid: this.containerOid
        })
        .then((data) => {
          this.listdata = data;
          this.treeData = this.deepData(data);
          this.$nextTick(() => {
            this.contentvalue = this.defaultvalue;
            this.treeSelect(this.contentvalue);
          });
        })
        .catch((err) => {
          console.error(err);
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    filterTreeNode(value, treeNode, field = "name") {
      const title =
        treeNode.componentOptions.propsData.dataRef[field].toLowerCase();
      return title.indexOf((value || "").trim().toLowerCase()) > -1;
    },
    findtreeobjdata(oid) {
      let findchildrem = function (val, padoid) {
        for (let i = 0; i < val.length; i++) {
          const children = val[i];
          if (children.oid === padoid) {
            return children;
          }
          if (children.children) {
            let res = findchildrem(children.children, padoid);
            if (res) {
              return res;
            }
          }
        }
      };

      for (let j = 0; j < this.listdata.length; j++) {
        const item = this.listdata[j];
        if (item.oid === oid) {
          return item;
        }
        if (item.children) {
          let res = findchildrem(item.children, oid);
          if (res) {
            return res;
          }
        }
      }
    },
    //加载下层树
    deepData(data, onlyDirectChildren = false) {
      const scopedSlots = { title: "folderIconTitle" };

      let arr = [];
      const loop = (tree) => {
        tree.map((item, index) => {
          item.key = item.oid;
          item.value = item.oid;
          item.scopedSlots = scopedSlots;

          // 只获取第一层
          if (onlyDirectChildren) {
            const temp = {
              ...item,
            };
            delete temp.children;
            arr.push(temp);
            return;
          }

          if (item.children && item.children.length > 0) {
            item.children.map((item) => (item.childType = "child"));
            loop(item.children);
          }
        });
      };

      loop(data);

      return onlyDirectChildren ? arr : data;
    },
  },
};
</script>

<style lang="less" scoped>
</style>