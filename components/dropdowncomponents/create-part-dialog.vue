<template>
  <div>
    <form-modal
      ref="formmodal"
      :width="512"
      :title="title"
      :ok-text="$t('btn_save')"
      confirm-btn-position="left"
      :data-source="formModalData"
      :visible.sync="formModalVisible"
      :okBtnLoading="buttonloading"
      @confirm="formModalConfirm"
      @cancelBack="formModalCancel"
    >
    </form-modal>
    <a-modal
      :title="$t('txt_materials')"
      width="65%"
      :visible="visibleMaterals"
      :okText="$t('txt_continue_save')"
      :cancelText="$t('txt_save_cancel')"
      @ok="confirmMaterals"
      @cancel="cancelMaterals"
      :confirmLoading="confirmLoading"
    >
      <materialsObject
        ref="materialsObjectS"
        :titleShow="false"
        :createPartList="createPartMaterialsList"
      />
    </a-modal>
  </div>
</template>

<script>
import { findItem } from "utils/util"
import { customerFetchFolderTree,fetchRootFolder,fetchfolderTree, MCAD_createPart, findSamePartData } from "./api/index"
import formModal from "components/form-modal.vue"
import materialsObject from "views/product/contentManagement/workflow/form-task/similar-materials-task/materials-object.vue"
import { findDetail } from "apis/baseapi"

export default {
  props: {
    defaultposition: {
      type: Object,
    },
    //是否是批量移动
    batchOperatorType: {
      type: String,
      default: "",
    },
  },
  components: {
    formModal,
    materialsObject,
  },
  data() {
    return {
      formModalData: [],
      formModalVisible: false,
      selectedTreeNode: {},
      modalAction: "mcadCreatePart",
      currentRecord: {},
      modalSelectedRows: [],
      buttonloading: false,

      title: "",
      visibleMaterals: false,
      confirmLoading: false,
      createPartMaterialsList: [],
      treeData: [], // 添加一个新的数据属性来存储树数据
    }
  },
  watch: {
    formModalVisible: function (val) {
      if (val) {
        this.titleInit()
      }
    },
  },
  created() {
    this.titleInit()
  },
  methods: {
    titleInit() {
      switch (this.batchOperatorType) {
        case "move": {
          this.title = this.$t("txt_mobile")
          break
        }
        case "changestage":
        case "saveAs": {
          this.title = this.$t("txt_save_as")
          break
        }
        default: {
          this.title = this.$t("txt_generating_unit")
          break
        }
      }
    },
    init(val) {
      this.currentRecord = Object.assign({}, val)
      this.openDialogLoadTree()
    },
    init2(val, modalSelectedRows) {
      this.currentRecord = Object.assign({}, val)
      this.modalSelectedRows = modalSelectedRows
      this.openDialogLoadTree()
    },
    formModalConfirm(val) {
      const { createChildren } = val
      this.buttonloading = true
      if (this.batchOperatorType) {
        if (this.batchOperatorType === "saveAs") {
          findDetail
            .execute({
              oid: this.modalSelectedRows[0].oid,
              type: this.modalSelectedRows[0].type,
            })
            .then((item) => {
              let data = item
              delete item.oid
              findSamePartData
                .execute([{ ...data }])
                .then((el) => {
                  if (el && !(JSON.stringify(el) === "{}")) {
                    // let obj = Object.keys(el)[0]
                    this.createPartMaterialsList = el.create
                    this.visibleMaterals = true
                    this.$nextTick(() => {
                      this.$refs.materialsObjectS.show()
                    })
                  } else {
                    this.actionMove(this.selectedTreeNode)
                  }
                })
                .catch((err) => {
                  this.$error(err.msg)
                  this.buttonloading = false
                })
            })
            .catch((err) => {
              this.$error(err.msg)
              this.buttonloading = false
            })
        } else {
          this.actionMove(this.selectedTreeNode)
        }
      } else {
        this.createPart(createChildren)
      }
    },

    formModalCancel() {
      this.formModalVisible = false;
      // 不需要清空 treeData，因为下次打开时会重新加载
      this.formModalData = [];
    },

    //批量移动
    actionMove(selectTree) {
      this.$emit("batchOperator", selectTree)
      this.buttonloading = false
      this.formModalVisible = false
      this.cancelMaterals()
    },

    // MCAD 生成 Part
    createPart(generateBOMFlag) {
      const {
        catalogType,
        oid,
        containerOid,
        containerType,
        containerModelDefinition,
      } = this.selectedTreeNode
      return MCAD_createPart.execute({
        mcadOids: [this.currentRecord.oid],
        generateBOMFlag,
        locationInfo: {
          catalogType,
          catalogOid: oid,
          containerOid,
          containerType,
          containerModelDefinition,
        },
      })
        .then((res) => {
          this.$success(this.$t("msg_success"))
          this.formModalVisible = false
          this.noticeParent(this.modalAction, res)
        })
        .catch((err) => {
          this.$error(err.msg || this.$t("msg_failed"))
        })
        .finally(() => {
          this.buttonloading = false
        })
    },

    noticeParent(key, data) {
      this.$emit("complete", key)
    },

// 加载根节点并构建 formModalData
    openDialogLoadTree() {
      fetchRootFolder
        .execute({
          containerModel: "",
        })
        .then((data) => {
          let defaultoption = this.defaultposition
            ? this.defaultposition.oid
            : "";

          // 处理根节点数据
          const initialTreeData = this.formatInitialTreeData(data);

          // 保存到 treeData 中
          this.treeData = initialTreeData;

          this.formModalData = [
            {
              label: this.$t("tabel_context"),
              type: "tree",
              prop: "position",
              showSearch: true,
              value: defaultoption,
              treeData: this.treeData, // 使用 treeData
              block: true,
              change: this.treeSelectChange,
              dropdownStyle: {
                height: "400px",
                overflowY: "auto",
              },
              treeNodeFilterProp: "name",
              filterTreeNode: this.filterTreeNode,
              loadData: this.loadTreeChildren,
            },
          ];

          if (this.currentRecord.modelDefinition === "CADAssembly") {
            this.formModalData.push({
              label: this.$t("txt_is_generate"),
              type: "radio",
              prop: "createChildren",
              value: true,
              block: true,
            });
          }

          if (defaultoption) {
            this.treeSelectChange(defaultoption);
          }
          this.formModalVisible = true;
        })
        .catch((err) => {
          console.error(err);
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },


    // 修改 loadTreeChildren 方法
    loadTreeChildren(treeNode) {
      return new Promise(resolve => {
        const { key, dataRef } = treeNode;

        // 如果节点已经加载过子节点，则直接返回
        if (dataRef.children && dataRef.children.length > 0) {
          this.$set(dataRef, 'loading', false);
          resolve();
          return;
        }

        // 标记节点正在加载子节点
        this.$set(dataRef, 'loading', true);

        customerFetchFolderTree.execute({
          containerModel: dataRef.containerModelDefinition || 'ProductContainer',
          containerOid: dataRef.containerOid
        })
        .then(data => {
          let originalChildrenData = [];

          if (Array.isArray(data) && data.length > 0) {
            const parentNodeFromApi = data[0];
            if (parentNodeFromApi && Array.isArray(parentNodeFromApi.children)) {
              originalChildrenData = parentNodeFromApi.children;
            }
          }

          const childrenNodes = this.formatFetchedSubtree(originalChildrenData);

          // 更新节点的子节点
          this.$set(dataRef, 'children', childrenNodes);
          this.$set(dataRef, 'isLeaf', childrenNodes.length === 0);
          this.$set(dataRef, 'loading', false);

          resolve();
        })
        .catch(err => {
          console.error('加载子节点失败:', err);
          this.$error(err.msg || this.$t("msg_failed"));
          this.$set(dataRef, 'loading', false);
          resolve();
        });
      });
    },

    // 修改 formatFetchedSubtree 方法，添加日志
    formatFetchedSubtree(nodes) {
      const scopedSlots = { title: "folderIconTitle" };

      if (!nodes || !Array.isArray(nodes)) {
        return [];
      }

      return nodes.map(item => {
        if (!item) return null;

        const formattedItem = {
          ...item,
          key: item.oid,
          value: item.oid,
          title: item.name,
          children: (item.children && Array.isArray(item.children))
            ? this.formatFetchedSubtree(item.children)
            : [],
          // 修改这里的判断逻辑
          // 只有当确实没有子节点时才设置为叶子节点
          isLeaf: !item.children || (Array.isArray(item.children) && item.children.length === 0),
          loading: false,
          scopedSlots: scopedSlots,
          modelIcon: item.modelIcon
        };

        delete formattedItem.loadData;
        return formattedItem;
      }).filter(item => item !== null);
    },


    // 修改 formatInitialTreeData 方法
    formatInitialTreeData(data) {
      const scopedSlots = { title: "folderIconTitle" };
      if (!data || !Array.isArray(data)) {
        return [];
      }

      return data.map(item => {
        const formattedItem = {
          ...item,
          key: item.oid,
          value: item.oid,
          title: item.name,
          scopedSlots: scopedSlots,
          isLeaf: false, // 默认所有节点都不是叶子节点
          loading: false,
          loadData: this.loadTreeChildren,
        };

        // 不要删除 children 属性，而是保留它
        if (item.children) {
          formattedItem.children = this.formatFetchedSubtree(item.children);
        } else {
          formattedItem.children = [];
        }

        return formattedItem;
      });
    },


    //加载下层树
    deepData(data, onlyDirectChildren = false) {
      const scopedSlots = { title: "folderIconTitle" }

      let arr = []
      const loop = (tree) => {
        tree.map((item, index) => {
          // item.title = item.name;
          item.key = item.oid
          item.value = item.oid
          item.scopedSlots = scopedSlots

          // 只获取第一层
          if (onlyDirectChildren) {
            const temp = {
              ...item,
            }
            delete temp.children
            arr.push(temp)
            return
          }

          if (item.children && item.children.length > 0) {
            item.children.map((item) => (item.childType = "child"))
            loop(item.children)
          }
        })
      }

      loop(data)

      return onlyDirectChildren ? arr : data
    },
    treeSelectChange(nodeOid) {
      switch (this.batchOperatorType) {
        case "move": {
          this.selectTree(0, nodeOid)
          break
        }
        case "saveAs": {
          this.selectTree(0, nodeOid)
          break
        }
        default: {
          this.selectTree(0, nodeOid)
          break
        }
      }
      console.log("所选树", this.selectedTreeNode)
    },

    selectTree(index, nodeOid) {
      if (this.formModalData[index]) {
        this.selectedTreeNode = findItem(
          this.formModalData[index].treeData,
          nodeOid
        )
      }
    },
    confirmMaterals() {
      this.buttonloading = false
      this.confirmLoading = true
      this.actionMove(this.selectedTreeNode)
    },
    cancelMaterals() {
      this.buttonloading = false
      this.confirmLoading = false
      this.visibleMaterals = false
      this.createPartMaterialsList = []
    },
    filterTreeNode(inputVal, treeNode) {
      // 获取 dataRef
      const dataRef = treeNode.componentOptions?.propsData?.dataRef;
      if (!dataRef || !this.treeData) return false;

      // 检查顶级节点是否匹配
      function isNodeMatch(node) {
        const name = node.name || '';
        return name.toLowerCase().indexOf((inputVal || "").trim().toLowerCase()) !== -1;
      }

      // 判断当前节点是否为顶级节点
      const isTopLevel = this.treeData.some(node => node.oid === dataRef.oid);

      if (isTopLevel) {
        // 如果是顶级节点，直接判断是否匹配
        return isNodeMatch(dataRef);
      } else {
        // 如果不是顶级节点，找到它所属的顶级节点
        function findTopLevelNode(nodes, targetOid) {
          for (const node of nodes) {
            if (node.oid === targetOid) return node;
            if (node.children && node.children.length > 0) {
              // 在子节点中查找
              const found = findTopLevelNode(node.children, targetOid);
              if (found) return node; // 只要在当前顶级节点的子树中找到，就返回当前顶级节点
            }
          }
          return null;
        }

        let belongTopNode = null;
        for (const topNode of this.treeData) {
          if (findTopLevelNode([topNode], dataRef.oid)) {
            belongTopNode = topNode;
            break;
          }
        }
        // 判断所属顶级节点是否匹配
        return belongTopNode ? isNodeMatch(belongTopNode) : false;
      }
    },
  },
}
</script>

<style lang="less" scoped></style>
