

<template>
    <div
        style="background:#fff;height:100%;"
    >
        <div class="search-main">
          <div class="search-info-right">
            <a-button type="primary" @click="headerBtnEvent">{{ btnName }}</a-button>
          </div>
          <div 
            class="search-content"
            v-if="showSearch"
          >
            <a-input-search
              placeholder="输入搜索关键词"
              class="seach-input"
              @search="onSearch"
              v-model.trim="searchKey"
            />
          </div>
          <div 
            class="header-extra"
          >
            <!-- 头部右侧区域内容插槽 -->
            <slot name="header-extra" :ref="this"></slot>
          </div>
        </div>
        <div class="table-box">
          <a-table
            :rowKey='rowKey'
            :columns="columns"
            :data-source="tableData"
            :scroll="{ x: 1300, y: 'calc(100vh - 64px - 30px - 10px - 52px - 40px - 54px - 64px)' }"
            :row-selection="rowSelection ? { selectedRowKeys: selected_row_keys, onChange: selectChange } : null"
            :pagination='comp_pg'
            :expanded-row-keys.sync='expande_row_keys'
            :selected-row-keys='selected_row_keys'
            :expand-icon='expandIcon'
            :indent-size='20'
            :loading='tableLoading'
            @change="change"
          >
            <!-- 操作 列 -->
            <template
              #action='text, record'
            >
              <!-- 默认 -->
              <!-- 父组件中可通过 v-slot:cover-action 或 #cover-action 重新赋值此处内容 -->
              <slot name="cover-action" :record='record'>
                <a @click="tableItemAction('update', record)">编辑</a>
                <a @click="tableItemAction('delete', record)" style="margin-left: 20px">{{$t('txt_delete')}}</a>
              </slot>
            </template>
            <!-- Switch -->
            <template
              #switch='text, record'
            >
              <a-switch v-model.trim="record.del" @click="checked => tableItemAction('switch', record, checked)" />
            </template>
            <!-- 可编辑行 -->
            <template #sortNumber="text,record" >
              <a-input-number v-if="record.type!='button'" :precision="0" v-model.trim="record.sort"  @blur="editors=>editorTable('editor',record, editors)" />
                <span v-else>-</span>
            </template>
            <!-- 多数据渲染显示 -->
            <template
              #userRoleName="record"
            > 
              <a-tooltip>
                <template slot="title">
                  <span v-for="(val,ind) in record " :key="ind">{{ val.orgName
						}}{{ record.length - 1 > ind ? "、" : "" }} </span>
                </template>
                <span v-for="(val,ind) in record " :key="ind">{{ val.orgName
						}}{{ record.length - 1 > ind ? "、" : "" }}</span>
              </a-tooltip>
            </template>
            <!-- 视图模块时 菜单按钮 名称 -->
            <template
              slot="viewModuleName"
              slot-scope="text, record"
            >
              <!-- 功能模块（菜单）图标 -->
              <module-component 
                v-if="(record.type || '').indexOf('menu') > -1"
                style="margin:0 8px;color:#1890ff" 
              />
              <span v-else style="padding-left:20px"></span>
              {{ text }}
            </template>
          </a-table>
          <slot name="pagination"></slot>
        </div>
    </div>
</template>

<script>
// 功能模块 图标
const moduleComponent = {
  template: `<a-icon :component='ImgComponent' :rotate='180' />`,
  data() {
    return {
      ImgComponent: {
        // 只支持 svg ？
        template: `<svg viewBox="64 64 896 896" data-icon="container" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" class=""><path d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-40 824H232V687h97.9c11.6 32.8 32 62.3 59.1 84.7 34.5 28.5 78.2 44.3 123 44.3s88.5-15.7 123-44.3c27.1-22.4 47.5-51.9 59.1-84.7H792v-63H643.6l-5.2 24.7C626.4 708.5 573.2 752 512 752s-114.4-43.5-126.5-103.3l-5.2-24.7H232V136h560v752zM320 341h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zm0 160h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"></path></svg>`
      }
    }
  },
}
export default {
    name: 'search-table',

    components: {
      moduleComponent,
    },
    /**
     * 可在父组件监听的事件：
     * header-btn-event: () => void 搜索按钮旁的按钮点击事件
     * select-change: (selectedRowKeys, selectedRows) => void 选中项变化事件
     * row-action: (type, record) => void  某一条 row 的某列操作事件
     * 
     */

    props: {
      btnName: {
        type: String,
        default: '新建',
      },
      // 是否展示搜索框
      showSearch: {
          type: Boolean,
          default: true,
      },
      columns: {
        type: Array,
        default: () => [],
      },
      rowKey: {
        default: 'oid',
      },
      dataSource: {
        type: Array,
        default: () => [],
      },
      loading: {
        type: Boolean,
        default: false,
      },
      // 控制展开项
      expandedRowKeys: {
        type: Array,
      },
      // 表格选中项
      selectedRowKeys: {
        type: Array,
      },
      // 当为 false 时，不展示勾选框
      rowSelection: {
        type: [Boolean, Object],
        default: true,
      },
      // 当为 false 时，不展示分页
      pagination: {
        type: [Boolean, Object],
        default: true,
      },
      // 分页字段 current、pageSize、total 和 pagination 中的字段对应关系
      replaceFields: {
        default: Object,
        default: () => ({
          current: 'current',
          pageSize: 'pageSize',
          total: 'total',
        })
      }
    },
    data () {
        return {
            tableData: [],
            searchKey: '',
            tableLoading: false,
            selected_row_keys: [], // 选中项 key
            expande_row_keys: [], // 展开项
        }
    },
    computed: {
      comp_pg () {
        if (!this.pagination)
          return false;

        const defaultPg = {
          current: 1,
          pageSize: 10,
          total: 0,
          showSizeChanger: true,
          showTotal: total => `共${total}条`,
        }
        
        if (this.pagination instanceof Object)
        {
          defaultPg.current = this.pagination[this.replaceFields.current] || 1;
          defaultPg.pageSize = this.pagination[this.replaceFields.pageSize] || 10;
          defaultPg.total = this.pagination[this.replaceFields.total] || 0;
        }
        
        return defaultPg;
      }
    },
    watch: {
      dataSource: {
        // immediate: true,
        handler (val) {
          this.tableData = val;
        }
      },
      // 展开项
      expandedRowKeys (val) {
        this.expande_row_keys = val;
      },
      expande_row_keys (val) {
        this.$emit('update:expandedRowKeys', val);
      },
      // 选中项
      selectedRowKeys (val) {
        this.selected_row_keys = val;
      },
      selected_row_keys (val) {
        this.$emit('update:selectedRowKeys', val);
      },
      loading: {
        immediate: true,
        handler (val) {
          console.log('loading: ',val);
          this.tableLoading = val;
        },
      },
    },
    created () {
      // console.log(this.$props, this.$attrs, this.$listeners)
    },
    methods: {
      editorTable(val,e,data){
        this.$emit('editorTable',e)
      },
      onSearch (value) {
        this.$emit('search', { searchKey: value, [this.replaceFields.current]: 1, [this.replaceFields.pageSize]: 10 });
      },
      headerBtnEvent (e) {
        console.log(e)
          this.$emit('header-btn-event')
      },
      // 列表 分页、排序、筛选 等变化时
      change (pagination, filters, sorter, { currentDataSource }) {
        this.$emit('search', { searchKey: this.searchKey, [this.replaceFields.current]: pagination.current, [this.replaceFields.pageSize]: pagination.pageSize, filters, sorter, currentDataSource });
      },
      // row 某列的操作
      tableItemAction (type, record, value) {
        console.log(type, record, value)
          this.$emit('row-action', type, record, value);
      },
      // row 选中事件
      selectChange (selectedRowKeys, selectedRows) {
        this.selected_row_keys = selectedRowKeys;
        this.$emit('select-change', selectedRowKeys, selectedRows);
      },

      // 渲染 table-expandIcon 展开图标
      expandIcon ({ expanded, record, onExpand }) {
        // console.log(expanded, record, this.expande_row_keys)
        return <a-icon type="caret-right" rotate={expanded ? 90 : 0} onClick={e => onExpand(record, e)} style={{ visibility: record.children && record.children.length ? 'visible' : 'hidden' }} />
      }
    }
}
</script>

<style lang="less" scoped>
.search-main {
  // display: flex;
  // align-items: center;
  box-sizing: border-box;
  padding: 10px 20px;
  height: 52px;
  border-bottom: 1px solid #dddedf;

  >div {
    display: inline-block;
  }
}
.search-info-right {
  margin: 0 10px 0 0;
}
.search-content {
  width: 20%;
  display: flex;
  align-items: center;
}
.header-extra {
  float: right;
}
.baselist-info {
  padding: 0;
  display: flex;
  flex-direction: column;
  margin: 5px;
  background: var(--light);
  box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
  border-radius: 4px;
}
.table-box {
  padding: 20px;
  height: calc(100% - 52px);
  overflow: auto;
}
</style>