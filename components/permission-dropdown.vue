<template>
  <a-dropdown placement="bottomRight" class="more-btn" :trigger="trigger" :overlayClassName='"dropdown-box"'>
    <a v-if="showType=='icon'" class="ant-dropdown-link jw-table-operate-btn" @click.stop="fetchBths">
      <i class="jwi-iconellipsis" :title="$t('btn_delete')+$t('btn_more')" style="padding-right:5px"/>
    </a>
    <a-button v-if="showType=='btn'" type="primary" style="margin-left: 8px" @click="fetchBths"> 操作
      <a-icon type="down" /> </a-button>
    <a-menu slot="overlay" :class="{'menu-box':!_.isEmpty(handleBths)}">
      <a-spin class="spin-icon" v-if="loading" />
      <a-menu-item v-for="(item) in handleBths" :key="item.code" :disabled="item.status === 'disable'" @click="onMenuClick($event,item)">
        <jwIcon :type='item.icon'></jwIcon>
        {{$t(item.internationalizationKey)}}
      </a-menu-item>
    </a-menu>
  </a-dropdown>
</template>
<script>
import { jwIcon } from "jw_frame";

import ModelFactory from "jw_apis/model-factory";
const permissionApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.permissionServer}/permission/filter/execute`,
  method: "post"
});
export default {
  props: {
    showType: {
      type: String,
      default: "icon"
    },
    instanceData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    permissionCode: {
      type: [String, Function],
      default: ""
    },
    callback:{
      type:Function
    },
    trigger: {
      type: Array,
      default: () => {
        return ["click"];
      }
    }
  },
  data() {
    return {
      handleBths: [],
      loading: false,
      oldInstanceDataOid: ""
    };
  },
  components: {
    jwIcon
  },
  methods: {
    onMenuClick(e, item) {
      this.$emit("click", item);
    },

    fetchBths() {
      // if (this.oldInstanceDataOid == this.instanceData.oid) {
      //   return;
      // } else {
        this.handleBths = [];
      // }
      let viewCode = null;
      if (typeof this.permissionCode == "function") {
        viewCode = this.permissionCode(this.instanceData);
      } else {
        viewCode = this.permissionCode;
      }
      let param = {
        objectOid: this.instanceData.oid,
        viewCode: viewCode
      };
      this.loading = true;
      permissionApi
        .execute(param)
        .then(res => {
          this.oldInstanceDataOid = this.instanceData.oid;
          this.loading = false;
          res.forEach(item => {
            item.handleBths = res;
          });
          if(this.callback){
           this.handleBths= this.callback(res)
          }else{
            this.handleBths = res;
          }
          
        })
        .catch(err => {
          this.oldInstanceDataOid = "";
          this.loading = false;
          this.$error(err.msg);
        });
    }
  }
};
</script>
<style lang="less">
.dropdown-box {
  width: 150px;
  .ant-dropdown-menu {
    padding: 0;
    .spin-icon {
      margin: 30px 60px;
    }
  }
  .menu-box {
    padding: 4px 0;
  }
}
</style>


