<template>
    <div class="add-obj-wrap">
        <div class="left-wrap">
            <!-- 上下文 -->
            <div class="filters" v-if="showContext" style="margin-bottom:20px">
                <a-select 
                    v-model.trim="productContainerOid" 
                    :placeholder="'请选择上下文'" 
                    allowClear
                    @change="proContainerSelect"
                    style="width:calc(100% - 20px)"
                >
                    <a-select-option
                        v-for="item in productContainerList"
                        :key="item.oid"
                    >
                        <span style="color: #255ed7">
                        <jw-icon style="margin-right: 8px" type="#jwi-chanpin" />
                            {{ item.name }}
                        </span>
                    </a-select-option>
                </a-select>
            </div>
            <!-- 文件夹 -->
            <div class="filters" v-if="showContext" style="margin-bottom:20px">
                <a-tree-select 
                    :value="catalogValue" 
                    :placeholder="'请选择文件夹'" 
                    :tree-data="treeData" 
                    allowClear
                    @change="treeSelect" 
                    treeDefaultExpandAll 
                    style="width:calc(100% - 20px)"
                >
                    <template slot="folderIconTitle" slot-scope="{ name, childType }">
                        <div>
                            <jw-icon style="margin-right: 8px" :type="
                                childType === 'child'
                                    ? '#jwi-wenjianga-youneiyong'
                                    : '#jwi-chanpin'
                                " />
                            <span>{{ name }}</span>
                        </div>
                    </template>
                </a-tree-select>
            </div>
            <div class="filters">
                <a-select v-model.trim="searchType" style="flex:1;margin-right:24px;background:transparent" placeholder='请选择' @change="onSearch">
                    <a-select-option v-for="item in typeList" :key="item.key">{{ item.label }}</a-select-option>
                </a-select>
                <a-input-search style="flex:1;margin-right:20px" v-model.trim="searchKey" placeholder="请输入关键词搜索" @search="onSearch" />
            </div>
            <a-tabs v-model.trim="activeTab" @change="onTabClick">
                <a-tab-pane v-for="item in tabList" :key="item.key" :tab="item.tab" />
            </a-tabs>
            <jw-table ref="refTable" :panel="true" :height="460" :tooltip-config="{}" :columns="columns" :data-source.sync="tableData" :fetch="fetchTable" :checkbox-config="{ reserve: true }" :selectedRows.sync="selectedRows" :showPage='showPage' :pagerConfig="pagerConfig" @checkbox-change="onCheckboxChange" @checkbox-all="onCheckboxChange">
                <template #numberSlot='{ row }'>
                    <jwIcon :type='row.modelIcon' />
                    <span style="color: #255ed7">{{ row.number }}</span>
                </template>
            </jw-table>
        </div>
        <div class="right-wrap">
            <div class="right-head-wrap">
                <div>已添加 {{selectedData.length}} 项</div>
                <a-button type="link" @click="onDeleteAll">清空</a-button>
            </div>
            <div class="right-body-wrap">
                <div class="obj-item flex" v-for="(item, index) of selectedData" :key="item.oid">
                    <div :title="`${item.number},${item.name},${item.displayVersion}`">
                        {{item.number}},{{item.name}},{{item.displayVersion}}
                        <span v-if="item.pageCode==='objectProcess'" class="main-obj">主对象</span>
                    </div>
                    <i v-if="item.pageCode!=='objectProcess'" class="jwi-iconclose-circle-full" @click="onDeleteSingle(item, index)"></i>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ModelFactory from 'jw_apis/model-factory';
import { jwIcon } from 'jw_frame';
import { findItem, getChainParent } from '../utils/util';

// 产品容器列表
const fetchContainerList = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/container/product/search`,
    method: 'post',
})

// 文件夹目录
const fetchfolderTree = ModelFactory.create({
    url: `${Jw.gateway}/${Jw.containerService}/folder/searchTree`,
    method: 'get'
});

const defaultTypeList = [
    {
        key: 'PartIteration',
        label: 'Part',
    },
    {
        key: 'DocumentIteration',
        label: 'Document',
    },
    {
        key: 'MCADIteration',
        label: 'MCAD',
    },
    {
        key: 'ECADIteration',
        label: 'ECAD',
    }
];

const recursive = tree => {
    const loop = data => {
        data.forEach(v => {
            const chainParent = getChainParent(tree, v.oid);
            v.key = v.oid;
            v.value = v.oid;
            v.pathValue = chainParent.keyStr;
            v.pathName = chainParent.nameStr;
            v.scopedSlots = {
                title: 'folderIconTitle'
            };

            if (v.children instanceof Array && v.children.length) {
                v.childType = 'child';
                loop(v.children);
            }
        });
    };
    loop(tree);
};

export default {
    name: 'structureAddSubObjContent',
    components: {
        jwIcon
    },
    props: {
        showContext: {
            type: Boolean,
            default: true,
        },
        showPage: {
            type: Boolean,
            default: true,
        },
        fetch: {
            type: Function,
        },
        pageCode: {
            type: String,
            default: '',
        },
        detailInfo: {
            type: Array,
            default: () => [],
        },
    columns: {
        type: Array,
        default: () => [
            {
                field: 'number',
                title: '编码',
                minWidth: 130,
                slots: {
                    default: 'numberSlot',
                },
            },
            {
                field: 'name',
                title: '名称',
                minWidth: 170,
            },
            {
                field: 'displayVersion',
                title: '版本',
                minWidth: 160,
                cellRender: {
                    name: 'tag',
                }
            },
            {
                field: 'lifecycleStatus',
                title: '生命周期',
                minWidth: 140,
            },
            {
                field: 'modelDefinition',
                title: '类型',
            },
            {
                field: 'updateDate',
                title: '修改时间',
                minWidth: 180,
                formatter: 'date',
            }
        ]
    },
    tabList: {
        type: Array,
        default: () => [
            // {
            //     key: '',
            //     tab: '',
            // }
        ]
    },
    typeList: {
        type: Array,
        default: () => defaultTypeList,
    },
  },
    data() {
        return {
        activeTab: '',
        searchKey: '',
        pagerConfig: {
            defaultPageSize: 10,
        },
        tableData: [],
        selectedRows: [],
        selectedData: [],
        searchType: defaultTypeList[0].key,

        productContainerList: [], // 上下文（产品容器） 下拉树结构
        productContainerOid: undefined, // 选中的 产品容器 oid
        productContainerModel: undefined, // 产品容器 modelDefinition

        treeData: [], // 文件夹 下拉树结构
        catalogOid: undefined, // 选中的 文件夹 oid
        catalogType: undefined,
        catalogValue: undefined, // 选中的 文件夹 展示内容

        };
    },
    created() {
        // 获取产品容器列表
        fetchContainerList
        .execute({
            index: 1,
            size: 100,
            searchKey: '',
        })
        .then(data => {
            this.productContainerList = data.rows.reverse();
        })
        .catch(err => {
            this.$error(err.msg || '获取上下文数据失败');
        });
    },
    mounted() {},
    watch: {
        typeList: {
            immediate: true,
            handler(val) {
                this.searchType = val[0] ? val[0].key : defaultTypeList[0].key;
            }
        },
    },
    methods: {
        onReset() {
            this.productContainerOid = undefined;
            this.productContainerModel = undefined;
            this.catalogValue = undefined;
            this.catalogOid = undefined;
            this.catalogType = undefined;
            this.searchKey = '';
            this.searchType = this.typeList[0].key;
            this.selectedRows = [];
            this.selectedData = [];
            this.searchKey = '';
            this.activeTab = 'Part';
            this.$refs.refTable.setAllCheckboxRow(false);
        },
        // 上下文 选择
        proContainerSelect (value) {
            const item = this.productContainerList.find(v => v.oid == value) || {};
            
            this.productContainerOid = item.oid;
            this.productContainerModel = item.modelDefinition;

            this.catalogValue = undefined;
            this.catalogOid = undefined;
            this.catalogType = undefined;
            this.onSearch();

            if (value)
            {
                let param = {
                containerOid: item.oid,
                containerModel: item.modelDefinition,
                };
                // 获取容器下的文件夹
                fetchfolderTree
                .execute(param)
                .then(data => {
                    recursive(data);
                    this.treeData = data;
                })
                .catch(err => {
                    console.error(err);
                    this.$error(err.msg || '获取文件夹数据失败');
                });
            }
        },
        // 文件夹 选择
        treeSelect(value) {
            if (value)
            {
                const item = findItem(this.treeData, value);
                this.catalogValue = item.pathName;
                this.catalogOid = value;
                this.catalogType = item.type;
            }
            else
            {
                this.catalogValue = '';
                this.catalogOid = undefined;
                this.catalogType = undefined;
            }
            this.onSearch();
        },
        onTabClick(key) {
            this.activeTab = key;
            this.onSearch();
        },
        onSearch() {
            this.$refs.refTable.reFetchData();
        },
        fetchTable({ current, pageSize }) {
            if (!this.searchType) return Promise.resolve();

            return this.fetch({
                index: current,
                size: pageSize,
                type: this.searchType,
                searchKey: this.searchKey,
                containerOid: this.productContainerOid,
                catalogOid: this.catalogOid,
                catalogType: this.catalogType,
            })
                .then(res => {
                    return {
                        data: res.rows || res,
                        total: res.count
                    };
                })
                .catch(err => {
                    this.$error(err.msg);
                });
        },
        onDeleteSingle(item) {
            const index = this.selectedData.indexOf(item);
            this.selectedData.splice(index, 1);
            this.$refs.refTable.setCheckboxRow(item, false);
        },
        onCheckboxChange() {
            const t0 = this.$refs.refTable.getCheckboxReserveRecords(),
                t1 = this.$refs.refTable.getCheckboxRecords();
            if (this.pageCode === 'objectProcess') {
                this.selectedData = [...this.detailInfo];
                t0.forEach(v1 => {
                    if (v1.oid !== this.detailInfo[0].oid) {
                        this.selectedData.push(v1);
                    }
                })
                t1.forEach(v2 => {
                    if (v2.oid !== this.detailInfo[0].oid) {
                        this.selectedData.push(v2);
                    }
                })
            } else {
                this.selectedData = [...t0, ...t1];
            }
        },
        onDeleteAll() {
            if (this.pageCode === 'objectProcess') {
                this.selectedData = [...this.detailInfo];
                this.selectedRows = [];
                this.$refs.refTable.setAllCheckboxRow(false);
            } else {
                this.selectedData = [];
                this.selectedRows = [];
                this.$refs.refTable.setAllCheckboxRow(false);
            }
        },
    }
};
</script>

<style lang="less" scoped>
.add-obj-wrap {
    display: flex;
    .left-wrap {
        width: 60%;
        border-right: 1px solid rgba(30, 32, 42, 0.06);
        .filters {
            display: flex;
            justify-content: space-between;
            text-align: center;
            /deep/.ant-select-selection {
                background: transparent;
            }
        }
        /deep/.ant-tabs {
            padding: 0 0 0 20px;
        }
        /deep/.ant-tabs-bar {
            margin: 0;
        }
        /deep/.ant-tabs-nav .ant-tabs-tab {
            padding: 10px 0px;
        }
        .search-input {
            width: 326px;
            margin: 16px 0 8px 20px;
        }
    }
    .right-wrap {
        width: 40%;
        padding: 0 16px 16px;
        border: 1px solid rgba(30, 32, 42, 0.06);
        .right-head-wrap {
            height: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .right-body-wrap {
            height: 495px;
            margin-top: 8px;
            overflow: auto;
            .obj-item {
                height: 48px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 16px;
                margin-bottom: 8px;
                background: rgba(30, 32, 42, 0.02);
                border: 1px solid rgba(30, 32, 42, 0.15);
                border-radius: 6px;
                div {
                    width: calc(~"100% - 25px");
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    .main-obj {
                        display: inline-block;
                        padding: 0 8px;
                        margin-left: 8px;
                        color: #255ed7;
                        background: #f0f7ff;
                        border: 1px solid #a4c9fc;
                        border-radius: 4px;
                    }
                }
            }
        }
    }
}
</style>
