<template>
  <div style="height: 100%">
    <a-dropdown
      :trigger="trigger"
      placement="bottomRight"
      overlayClassName="operation-dropdown-overlay"
      @visibleChange="dropdownVisibleChange"
    >
      <slot>
        <span
          class="jwi-iconellipsis"
          :title="$t('btn_delete') + $t('btn_more')"
          style="cursor: pointer"
        ></span>
      </slot>

      <a-menu slot="overlay" @click="({ key }) => rowOperation(key)">
        <a-spin
          v-if="currentRow.loading_status == 'loading'"
          style="margin: 20px 65px"
        />
        <a-menu-item
          v-else-if="currentRow.loading_status == 'failed'"
          style="margin: 20px auto"
          >{{ $t("txt_get_failure") }}
        </a-menu-item>
        <template v-else v-for="(item, index) in currentRow.operationList">
          <!-- <a-menu-divider
            v-if="item.code == 'delete'"
            :key="index"
            :disabled="item.status === 'disable'"
          /> -->
          <a-menu-item :key="item.code" :disabled="item.status === 'disable'" v-if="!(item.code === 'batchdownload' && currentRow.modelDefinition === 'CADDrawing')">
            <span :class="item.icon" style="margin-right: 8px"></span
            >{{ $t(item.internationalizationKey) }}
          </a-menu-item>
        </template>
        <slot name="relationList"></slot>
      </a-menu>
    </a-dropdown>
    <!-- 重命名/移动/添加至基线 -->
    <form-modal
      :width="512"
      :title="modalTitle"
      :ok-text="modalOkText"
      confirm-btn-position="left"
      :data-source="formModalData"
      :visible.sync="formModalVisible"
      @confirm="formModalConfirm"
      @cancelBack="formModalCancel"
    >
      <!-- 添加至基线--顶部切换按钮 -->
      <template #topToolbar v-if="modalAction == 'addToBaseline'">
        <a-radio-group
          button-style="solid"
          style="margin-bottom: 20px"
          :value="addToBaselineType"
          @change="(e) => addToBaselineTypeChange(e.target.value)"
        >
          <a-radio-button value="exist"
            >{{ $t("txt_existing_baseline") }}
          </a-radio-button>
          <a-radio-button
            value="create"
            :disabled="!hasCreateBaselinePermission"
          >
            {{ $t("txt_create_baseline") }}</a-radio-button
          >
        </a-radio-group>
      </template>
      <!-- 添加至基线--创建基线 -->
      <template
        #default
        v-if="addToBaselineType == 'create' && modalAction == 'addToBaseline'"
      >
        <jw-layout-builder
          ref="ref_appBuilder"
          type="Model"
          layoutName="create"
          :modelName="'Baseline'"
          :instanceData="{}"
        >
          <template
            v-for="(node, slotName) in $scopedSlots"
            :slot="slotName"
            slot-scope="slotData"
          >
            <slot :name="slotName" v-bind="slotData"></slot>
          </template>
        </jw-layout-builder>
      </template>
      <!-- 对比--添加对象 -->
      <template #openSerachObjectModal="{}">
        <div @click="addObjectModalVisible = true">
          <a-select
            :placeholder="$t('txt_add_object')"
            :open="false"
            :value="
              compareObject
                ? `${compareObject.number}，${compareObject.name}，${compareObject.displayVersion}`
                : undefined
            "
          />
        </div>
      </template>
      <!-- 对比--当前对象 -->
      <template #currentObject="{ item }" v-if="modalAction == 'contrast'">
        <div
          :title="`${item.number}，${item.name}，${item.displayVersion}`"
          :style="{
            height: '40px',
            'line-height': '40px',
            background: 'rgba(30,32,42,0.04)',
            'border-radius': '4px',
            color: 'rgba(30,32,42,0.65)',
            'white-space': 'nowrap',
            'text-overflow': 'ellipsis',
            overflow: 'hidden',
          }"
        >
          <jwIcon :type="item.modelIcon" style="margin: 0 10px" />{{
            item.number
          }}，{{ item.name }}，{{ item.displayVersion }}
        </div>
        <a-divider />
      </template>
    </form-modal>
    <jw-search-engine-modal
      v-if="modalAction == 'contrast'"
      :title="$t('txt_add_object')"
      :onlySearchObject="true"
      :visible.sync="addObjectModalVisible"
      :model-list="[
        {
          name: $t('txt_part'),
          code: 'PartIteration',
        },
      ]"
      @ok="addObjectOk"
    />
    <!-- 设置状态/修订/删除 -->
    <base-color-modal
      :title="operationTitle"
      :width="992"
      :visible.sync="operationVisible"
      dialog-class="operation-modal"
      :ok-text="
        $t('btn_ok') +
        `${
          operationTitle == $t('txt_set_status')
            ? $t('btn_set_s')
            : operationTitle
        }`
      "
      @ok="modalOk"
      :okBtnLoading="okBtnLoading"
      @cancel="operationCancel"
      :body-style="{ height: '545px', overflowY: 'scroll' }"
    >
      <!-- 设置状态 -->
      <div
        v-if="modalAction == 'setStatus'"
        class="setStatus-container"
        :style="{
          width:
            statusList.length * 120 > 992 - 24 * 2
              ? statusList.length * 120 + 'px'
              : '100%',
        }"
      >
        <div
          v-for="(item, index) in statusList"
          :key="item.code"
          :class="[
            'item-box',
            {
              current: item.code == currentRecord.lifecycleStatus,
              active: currentRow.new_lifecycleStatus == item.code,
            },
          ]"
          @click="$set(currentRow, 'new_lifecycleStatus', item.code)"
        >
          <div class="circle">
            <div v-if="index != 0" class="line left-arrow"></div>
            <div
              v-if="index != statusList.length - 1"
              class="line right-arrow"
            ></div>
          </div>
          <div class="status">
            <a-tag style="margin: 0 auto">{{ item.displayName }}</a-tag>
          </div>
          <!-- 当前状态 -->
          <div v-if="item.code == currentRecord.lifecycleStatus" class="text">
            <span
              class="jwi-iconflag"
              style="color: #f6445a; margin-right: 4px"
            ></span>
            {{ $t("txt_current_state") }}
          </div>
          <div v-else class="text">{{ $t("txt_set_current") }}</div>
        </div>
      </div>
      <!-- 修订 -->
      <div v-else-if="modalAction == 'revise'" class="revise-container">
        <jw-table
          ref="modalTableRef"
          disableCheck="disableCheck"
          :data-source.sync="modalTableData"
          :columns="reviseColumns"
          :selectedRows.sync="modalSelectedRows"
          :fetch="fetchModalTable"
          :showPage="false"
          :height="699"
        >
          <template #version="{ row }">
            <a-auto-complete
              v-model="row.version"
              :data-source="nextVersions"
              style="width: 120px"
              placeholder="请输入"
            />
        </template>
        </jw-table>
      </div>
      <!-- 删除 -->
      <div v-else class="delete-container">
        <p
          style="
            color: rgba(30, 32, 42, 0.85);
            line-height: 22px;
            font-size: 14px;
            margin-bottom: 8px;
          "
        >
          {{ $t("txt_history_version") }}
        </p>
        <jw-table
          ref="modalTableRef"
          disableCheck="disableCheck"
          :data-source.sync="modalTableData"
          :columns="modalColumns"
          :selectedRows.sync="modalSelectedRows"
          :fetch="fetchModalTable"
          :showPage="false"
          :height="500"
        >
          <template #status="{ row }">
            <a-tag>{{ $t(row.lifecycleStatus) }}</a-tag>
          </template>
          <template #updateDate="{ row }">
            <span style="color: #255ed7">
              {{ formatDateFn(row.updateDate) }}
            </span>
          </template>
        </jw-table>
      </div>
    </base-color-modal>
    <!-- 修改所有者 -->
<!--    <jw-user-modal-v2 ref="user-modal" :isCheckbox="false" type="owner" />-->
    <userModalV3 ref="user-modal" :isCheckbox="false" type="owner"/>
    <!-- 发起流程 -->
    <start-process-modal
      :visible="processVisible"
      :pageCode="pageCode"
      :detailInfo="currentRecord"
      @close="onCloseProcessModal"
      @getTableData="noticeParent('startProcess')"
    ></start-process-modal>
    <!-- 另存 -->
    <create-drawer
      ref="cerateDrawer"
      :objectDetailsData="currentRecord"
      @fetchTable="fetchTable"
      :deliverTree="deliverTree"
    ></create-drawer>

    <!-- 新版生成part或part生成cad共用 -->
    <create-part-dialog-effect
      :visible.sync="showeffectdialog"
      :record="currentRecord"
      :cadtopart="cadtopart"
      @reloadlistdata="noticeParent('move');"
      v-if="showeffectdialog"
    />

    <!-- 下载压缩文件 -->
    <downloadmcadfile ref="downloadmcadfile"/>

    <releaseDesignTaskModal ref="releaseDesignTaskModal"/>
        <!-- 问题报告 -->
    <create-problem
      ref="ref_create_problem"
      :pageCode="'processManage'"
      :selectList.sync="selectList"
      :visible="visibleProblem"
      @close="onCloseModal"
      @getTableData="fetchTable"></create-problem>
  </div>
</template>

<script>
import releaseDesignTaskModal from "./releaseDesignTaskModal";
import formModal from "components/form-modal.vue";
import baseColorModal from "components/base-color-modal.vue";
import util from "jw_common/util";
import { formatDate } from "jw_utils/moment-date";
import {
  jwLayoutBuilder,
  jwIcon,
  jwUserModalV2,
  jwSearchEngineModal,
} from "jw_frame";
import startProcessModal from "/views/product-content/process-manage/start-process-modal";
import createDrawer from "/views/product-content/content-manage/create-drawer";
import CreatePartDialogEffect from './dropdowncomponents/create-part-dialog-effect.vue';
import userModalV3 from "../components/user-modal-v3";
import  {
    fetchfolderTree,
    getDropdownList,
    part_checkIn,
    part_checkOut,
    part_undocheckout,
    part_move,
    part_rename,
    part_saveAs,
    searchStatusList,
    part_setStatus,
    part_revise,
    part_delete,
    getHistoryList,
    getBaselineList,
    createBaseline,
    batchLink,
    searchModelTable,
    getDeliveryLeafNodes,
    batchAddInstance,
} from 'apis/part'
import { findDetail, setOwner } from "apis/baseapi";
import Downloadmcadfile from './dropdowncomponents/downloadmcadfile.vue';

import CreateProblem from "views/problem-manage/problem-list/create-problem";

import ModelFactory from "jw_apis/model-factory";
const nextVersionApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.foundationServer}/version/getNextVersion`,
  method: "post",
});

export default {
  name: "operation-dropdown",
  components: {
    releaseDesignTaskModal,
    formModal,
    baseColorModal,
    jwLayoutBuilder,
    jwIcon,
    jwUserModalV2,
    startProcessModal,
    createDrawer,
    jwSearchEngineModal,
    CreatePartDialogEffect,
    Downloadmcadfile,
    CreateProblem,
    userModalV3
  },
  props: {
    currentRecord: Object,
    trigger: {
      type: Array,
      default: () => ["hover"],
    },
    treeName: {
      type: String,
      default: "",
    },
    currentTree: {
      type: Object,
      default: () => {},
    },
    location:{
      type:Object,
      default:()=>{},
    },
    addIterationAble:{
      type:Boolean,
      default:false
    },
    codeFlag: {
      type: String,
      default: "",
    },
    // （产品/资源)容器 oid
    containerOid: {
      type: String,
    },
    // （产品/资源)容器 modelDefinition: 'ProductContainer'/'ResourceContainer'
    containerModel: {
      type: String,
    },
    // （产品/资源)容器 type: 'Container'
    containerType: {
      type: String,
    },
    deliverTree: {
      type: Array
    }
  },
  data() {
    return {
      currentRow: {},
      statusList: [
      ],
      operationVisible: false,
      modalAction: "",
      modalTableData: [],
      modalSelectedRows: [],
      selectedTreeNode: {}, // 移动/另存-位置 选中的树节点

      formModalData: [],
      formModalVisible: false,
      addToBaselineType: "", // 添加至基线 -- 已有/新建--基线
      hasCreateBaselinePermission: false, // 是否有 新建基线 的权限

      addObjectModalVisible: false, // 对象搜索组件
      compareObject: undefined, // 对比--搜索后选择的对象
      processVisible: false,
      pageCode: "objectProcess",
      okBtnLoading: false,

      showeffectdialog: false,
      // cad转part或part转cad
      cadtopart: true,
      nextVersions: [],
      visibleProblem:false,
      selectList:[]
    };
  },
  watch: {
    currentRecord: {
      immediate: true,
      handler(val) {
        this.currentRow = _.cloneDeep(val);
      },
    },
    formModalVisible(val) {
      if (!val) {
        this.compareObject = undefined;
      }
    },
    visibleProblem(val){
      if(val){
        this.selectList=[this.currentRow]
      }
    }
  },
  computed: {
    modalTitle() {
      switch (this.modalAction) {
        case "checkin":
          return this.$t("txt_check_in");
        case "checkout":
          return this.$t("txt_check_out");
        case "addToBaseline":
          return this.$t("txt_add_to_baseline");
        case "addToDelivery":
          return this.$t("txt_add_to_delivery");
        case "contrast":
          return this.$t("txt_contrast");
        case "move":
          return this.$t("txt_mobile");
        case "rename":
          return this.$t("txt_rename");
        case "saveAs":
          return this.$t("txt_save");

        default:
          return this.$t("txt_create_container");
      }
    },
    modalOkText() {
      switch (this.modalAction) {
        case "checkout":
          return this.$t("txt_comfrim_out");
        case "saveAs":
          return this.$t("txt_comfrim_save");

        default:
          return this.$t("btn_done");
      }
    },

    operationTitle() {
      switch (this.modalAction) {
        case "setStatus":
          return this.$t("txt_set_status");
        case "revise":
          return this.$t("txt_revision");
        case "delete":
          return this.$t("txt_delete");

        default:
          break;
      }
    },
    modalColumns() {
      return [
        {
          field: "name",
          title: this.$t("txt_name"),
        },
        {
          field: "number",
          title: this.$t("txt_number_of"),
          sortable: true, // 开启排序
        },
        {
          field: "lifecycleStatus",
          title: this.$t("txt_plan_lifecycle"),
          sortable: true, // 开启排序
          slots: {
            default: "status",
          },
        },
        {
          field: "displayVersion",
          title: this.$t("txt_version"),
          sortable: true, // 开启排序
        },
        {
          field: "updateDate",
          title: this.$t("txt_update_date"),
          sortable: true, // 开启排序
          slots: {
            default: "updateDate",
          },
        },
      ];
    },

    reviseColumns() {
      return [
        {
          field: "name",
          title: this.$t("txt_name"),
        },
        {
          field: "number",
          title: this.$t("txt_number_of"),
          sortable: true, // 开启排序
        },
        {
          field: "lifecycleStatus",
          title: this.$t("txt_plan_lifecycle"),
          sortable: true, // 开启排序
          cellRender: {
            name: "tag",
          },
        },
        {
          field: "displayVersion",
          title: this.$t("txt_version"),
          sortable: true, // 开启排序
        },
        {
          field: "version",
          title: "修订版本",
          slots: {
            default: "version",
          },
        },
        {
          field: "updateDate",
          title: this.$t("txt_update_date"),
          sortable: true, // 开启排序
          cellRender: {
            name: "date",
          },
        },
      ];
    },
  },

  methods: {
    getNextVersion(row) {
      let { oid, type, modelDefinition } = row;
      nextVersionApi
        .execute({ oid, type, modelDefinition })
        .then(res => {
          row.version = res[0];
          this.nextVersions = res;
        })
        .catch(err => {
          this.$error(err.msg);
        });
    },
    searchModelTable(params) {
      return searchModelTable.execute(params);
    },
    // 添加基线对象
    addObjectOk(selectedRows) {
      if (!selectedRows.length)
        return this.$warning(this.$t("txt_please_seleted_data"));

      this.compareObject = selectedRows[0];
      this.addObjectModalVisible = false;
    },
    addObjectCancel() {
      this.addObjectModalVisible = false;
    },
    formatDateFn(date) {
      return formatDate(date);
    },
    deepData(data, onlyDirectChildren = false) {
      const scopedSlots = { title: "folderIconTitle" };

      let arr = [];
      const loop = (tree) => {
        tree.map((item, index) => {
          // item.title = item.name;
          item.key = item.oid;
          item.value = item.oid;
          item.scopedSlots = scopedSlots;

          // 只获取第一层
          if (onlyDirectChildren) {
            const temp = {
              ...item,
            };
            delete temp.children;
            arr.push(temp);
            return;
          }

          if (item.children && item.children.length > 0) {
            item.children.map((item) => (item.childType = "child"));
            loop(item.children);
          }
        });
      };

      loop(data);

      return onlyDirectChildren ? arr : data;
    },
    // 添加至基线 类型切换
    addToBaselineTypeChange(type, open) {
      // 已有基线
      if (type == "exist") {
        let { query } = this.$route;
        let folderTreeParams = {
          // containerOid: query.id || this.containerOid,
          containerModel: this.containerModel || query.modelDefinition,
        };
        fetchfolderTree
          .execute(folderTreeParams)
          .then((data) => {
            this.formModalData = [
              {
                label: this.$t("tabel_context"),
                type: "tree",
                placeholder: this.$t("txt_select_context"),
                prop: "context",
                treeData: this.deepData(data, true),
                block: true,
                required: false,
                showSearch: true,
                treeNodeFilterProp: "title",
                filterTreeNode: (value, treeNode, field = "name") => {
                  const title =
                    treeNode.componentOptions.propsData.dataRef[
                      field
                    ].toLowerCase();
                  return title.indexOf((value || "").trim().toLowerCase()) > -1;
                },
                change: (newVal, oldVal) => {
                  const containerOid = (
                    util.findItem(this.formModalData[0].treeData, newVal) || {}
                  ).containerOid;

                  this.$set(this.formModalData, 0, {
                    ...this.formModalData[0],
                    value: newVal,
                  });
                  this.$set(this.formModalData, 1, {
                    ...this.formModalData[1],
                    disabled: !newVal,
                    defaultOptions: [], // 切换上下文时，清空之前搜索的下拉基线列表
                    searchParams: {
                      ...this.formModalData[1].searchParams,
                      containerOid, // 设置 基线搜索 -- 产品容器参数
                    },
                  });
                },
                dropdownStyle: {
                  height: "276px",
                  overflowY: "scroll",
                },
              },
              {
                label: this.$t("txt_seletct_baseline"),
                prop: "oid",
                type: "select",
                placeholder: this.$t("txt_enter_keyword"),
                disabled: true,
                block: true,
                fetch: getBaselineList,
                value: undefined,
                showSearch: true,
                showIcon: true,
                change: (newVal, oldVal, fullItemInfo) => {},
                searchParams: {
                  index: 1,
                  size: 99999,
                  // searchKey: '',
                  containerOid: "",
                },
                // defaultOptions: [],
                dropdownStyle: {
                  height: "276px",
                  overflowY: "scroll",
                },
              },
            ];

            if (open) {
              this.formModalVisible = true;
            }
          })
          .catch((err) => {});
      }
      // 新建基线
      else {
        this.formModalData = [];
      }
      this.addToBaselineType = type;
    },

    getviewcode(masterType){
      let viewCode = ""
      if (this.codeFlag === "details") {
        //详情
        switch (masterType) {
          case "Part":
          case "EndItem":
          case "Product":
            viewCode = "PARTINSTANCEDETAILS";
            break;
          case "Document":
            viewCode = "DOCUMENTINSTANCEDETAILS";
            break;
          case "ECAD":
          case "PCB":
          case "Schematic":
          case "Datasheet":
          case "Symbol":
          case "Encapsulation":
            viewCode = "ECADINSTANCEDETAILS";
            break;
          case "MCAD":
          case "CADAssembly":
          case "CADPart":
            viewCode = "MCADDOCUMENTINSTANCEDETAILS";
            break;
          case "DocumentTemplateMaster":
            viewCode = "DOCUMENTTEMPLATEINSTANCEDETAILS";
            break;
        }
      } else if (this.codeFlag === "relation") {
        viewCode = "PARTBOMINSTANCE";
      } else {
        switch (masterType) {
          case "Document":
            viewCode = "DOCUMENTINSTANCE";
            break;
          case "Part":
            viewCode = "PARTINSTANCE";
            break;
          case "MCAD":
            viewCode = "MCADDOCUMENTINSTANCE";
            break;
          case "ECAD":
            viewCode = "ECADINSTANCE";
            break;
          case "PCB":
            viewCode = "PARTINSTANCE";
            break;
          case "Schematic":
            viewCode = "PARTINSTANCE";
            break;
          case "Datasheet":
            viewCode = "PARTINSTANCE";
            break;
          case "Symbol":
            viewCode = "PARTINSTANCE";
            break;
          case "Encapsulation":
            viewCode = "PARTINSTANCE";
            break;
          case "DocumentTemplateMaster":
            viewCode = "DOCUMENTTEMPLATEINSTANCE";
            break;
        }
      }
      return viewCode
    },
    // 获取 currentRecord  part 操作下拉列表
    dropdownVisibleChange(visible) {
      console.log('获取操作下拉列表')
      if (!this.currentRecord.oid) return;
      const row = this.currentRecord;
      let { masterType } = row;
      let viewCode = this.getviewcode(masterType)

      if (visible) {
        this.$set(this.currentRow, "loading_status", "loading");
        let params = {
          viewCode: viewCode,
          objectOid: row.oid,
        };
        if (this.codeFlag === "relation") {
          params.parentOid = row.parentOid;
        }
        getDropdownList
          .execute(params)
          .then((data) => {
            this.$set(this.currentRow, "operationList", data);
            this.$set(this.currentRow, "loading_status", "done");
            this.$emit("visibleRelationList", visible, this.currentRow);
          })
          .catch((err) => {
            this.$error(err.msg || this.$t("msg_failed"));
            this.$set(this.currentRow, "loading_status", "failed");
          });
      } else {
        this.$emit("visibleRelationList", visible);
      }
    },
    async getOperationList() {
      if (!this.currentRecord.oid) return;
      const row = this.currentRecord;
      let { masterType } = row;
      let viewCode = this.getviewcode(masterType)

      let params = {
        viewCode: viewCode,
        objectOid: row.oid,
      };
      if (this.codeFlag === "relation") {
        params.parentOid = row.parentOid;
      }
      let data = await getDropdownList.execute(params)
      return data
    },

    noticeParent(key, data) {
      this.$emit("complete", key);
      this.$emit("renderData", data, this.currentRecord);
    },
     // 列表操作
     rowOperation(key) {
      const { oid } = this.currentRecord;
      switch (key) {
        // 检入
        case "checkin":
          this.actionCheckin()
          break;
        // 检出
        case "checkout":
          this.actionCheckOut()
          break;
        // 撤销检出
        case "undoCheckOut":
          this.actionundoCheckOut(key)
          break;
        // 编辑
        case "edit":
          this.actionEdit()
          break;
        // 下载
        case "download":
          this.actionDownload()
          break;
        // 移动
        case "move":
          //走批量移动
          this.$emit("batchOperator", this.currentRecord, "move");
          break;
        // 重命名
        case "rename":
          this.actionRename()
          break;
        // 另存
        case "saveAs":
          this.actionSaveas("saveAs")
          break;
        case "screen":
          /*if(!this.addIterationAble) {
            this.$error("请选择最底层目录");
            return
          }*/
          this.actionSaveas("screen")
          break
        case "forming":
          /*if(!this.addIterationAble) {
            this.$error("请选择最底层目录");
            return
          }*/
          this.actionSaveas("forming")
          break
        // 添加至基线
        case "addToBaseline":
          //批量添加至基线
          this.$emit("batchOperator", this.currentRecord, "addBaseLine");
          break;
        // 添加至交付清单
        case "addToDelivery":
          this.actionaddToDelivery()
          break;
        // 发起流程
        case "startProcess":
          this.$emit("batchOperator", this.currentRecord, "process");
          break;
        // 发起 文件外发流程
        case "startProcess_wf":
          this.$emit("batchOperator", this.currentRecord, "process_wf");
          break;
         // 变更
        case "changeProcess":
          this.$emit("batchOperator", this.currentRecord, "changeProcess");
          break;
        // 修改所有者
        case "updateOwner":
          this.actionupdateOwner(key)
          break;
        // 设置状态
        case "setStatus":
          this.actionsetStatus()
          break;
        // 修订
        case "revise":
          {
            this.modalTableData = [this.currentRecord];
            this.modalSelectedRows = [];
            this.operationVisible = true;
            this.getNextVersion(this.currentRecord);
          }
          break;
        case "batchdownload": {
          //批量下载step文件
          this.$refs['downloadmcadfile'].show(oid)
          break;
        }
        // 删除
        case "delete":
          {
            this.modalSelectedRows = [];
            this.operationVisible = true;
          }
          break;
        // 对比
        case "contrast":
          this.actioncontrast()
          break;
        // 详情
        case "details":
          {

            const viewCodeMap = {
              MCAD: "MCADDOCUMENTINSTANCE",
              ECAD: "ECADINSTANCE",
            };
            Jw.jumpToDetail(this.currentRecord,{blank:true});
          }
          break;
        case "partDetails":
        case "partEdit":
        case "partMove":
        case "createPart":
        case "createHasPart":
          this.$emit("onRelationOpe", key, this.currentRecord);
          break;
        // MCAD 生成部件
        case "mcadCreatePart":
          //创建部件
          this.cadtopart = true
          this.showeffectdialog = true
          break;
        case "parttocad":
          // 部件生成cad
          this.cadtopart = false
          this.showeffectdialog = true
          break;
        case "releaseDesignTask":
          this.$refs['releaseDesignTaskModal'].visible = true
          this.$refs['releaseDesignTaskModal'].detail = this.currentRecord
          break
        case "createProblem":
          //问题报告
          let noRel = this.selectList.find((item) => {
            return item.lifecycleStatus != "Released" && item.lifecycleStatus != "released";
          });
          if (noRel) {
            return this.$error(
              `${noRel.number}数据状态为未发布，不能提交问题报告`
            );
          }
          this.visibleProblem = true;
        default:
          break;
      }
      this.modalAction = key;
    },
    actionCheckin(){
      if (this.$listeners.beforeCheckInFn instanceof Function)
        this.$listeners.beforeCheckInFn().then((res) => {
          this.formModalData = [
            {
              label: this.$t("txt_remark"),
              type: "textarea",
              prop: "lockNote",
              block: true,
              required: false,
            },
          ];
          this.formModalVisible = true;
        });
      else {
        this.formModalData = [
          {
            label: this.$t("txt_remark"),
            type: "textarea",
            prop: "lockNote",
            block: true,
            required: false,
          },
        ];
        this.formModalVisible = true;
      }
    },
    actionCheckOut(){
      const {
        modelDefinition,
        oid,
        type,
        masterType,
      } = this.currentRecord;
      part_checkOut(masterType)
          .execute({
            modelDefinition,
            oid,
            type,
          })
          .then((data) => {
            this.$success(this.$t("msg_checkout_success"));
            this.formModalVisible = false;
            this.noticeParent(this.modalAction, data);
          })
          .catch((err) => {
            console.error(err);
            this.$error(this.$t("msg_checkout_failed"));
          });
    },
    actionundoCheckOut(key){
      const {
        modelDefinition,
        oid,
        type,
        masterType,
      } = this.currentRecord;
      part_undocheckout(masterType)
          .execute({
            modelDefinition,
            oid,
            type,
          })
          .then((data) => {
            this.$success(this.$t("msg_success"));
            this.noticeParent(key, data);
          })
          .catch((err) => {
            console.error(err);
            this.$error(err.msg || this.$t("msg_failed"));
          });
    },
    actionEdit(){
      const {
        modelDefinition,
        oid,
        type,
        masterType,
        lockOwnerOid
      } = this.currentRecord;
      if (!lockOwnerOid) {
        part_checkOut(masterType)
          .execute({
            modelDefinition,
            oid,
            type,
          })
          .then((data) => {
            if (this.$route.path.indexOf("/object-details") > -1) {
              this.noticeParent("edit", data);
            } else {
              this.noticeParent("edit", data);
              Jw.jumpToDetail(data);
            }
          })
          .catch((err) => {
            console.error(err);
            this.$error(this.$t("check_out_failed"));
          });
      } else {
        Jw.jumpToDetail(this.currentRecord);
        this.noticeParent("edit");
      }
    },
    actionDownload(){
      if (
        this.currentRecord.secondaryFile &&
        this.currentRecord.secondaryFile.length > 0
      ) {
        util.download(
          `${Jw.gateway}/${Jw.fileMicroServer}/file/downloadByOid?fileOid=${this.currentRecord.secondaryFile[0].oid}`
        );
      }
    },
    actionRename(){
      const {
        name,
      } = this.currentRecord;
      this.formModalData = [
          {
            label: this.$t("txt_object_name"),
            type: "input",
            prop: "name",
            block: true,
            value: name,
          },
        ];
        this.formModalVisible = true;
    },
    actionSaveas(item){
      const {
        modelDefinition,
        oid,
        type,
        name,
        masterType
      } = this.currentRecord;
      //走批量另存
      if (masterType === "MCAD" || masterType === "Part") {
        if (item==='saveAs'){
          this.$emit("batchOperator", this.currentRecord, "saveAs");
          return;
        }else if (item==='screen'){
          this.currentRecord.location = {
            catalogOid: this.currentRecord.catalogOid,
            catalogType: this.currentRecord.catalogType
          }
          this.$emit("batchOperator", this.currentRecord, "screen");
          return;
        } else if (item==='forming'){
          this.currentRecord.location = {
            catalogOid: this.currentRecord.catalogOid,
            catalogType: this.currentRecord.catalogType
          }
          this.$emit("batchOperator", this.currentRecord, "forming");
          return;
        }
      }
      findDetail
        .execute({
          oid: oid,
          type: type,
        })
        .then((res) => {
          let url = "";
          console.log(masterType);
          if (masterType === "Part") {
            url = `${Jw.gateway}/${Jw.partBomMicroServer}/part/copy`;
          } else if (masterType === "Document") {
            url = `${Jw.gateway}/${Jw.docMicroServer}/document/copy`;
          } else if (masterType === "MCAD") {
            url = `${Jw.gateway}/${Jw.cadService}/mcad/copy`;
          }
          this.$refs.cerateDrawer.show({
            title: name,
            modelInfo: {
              layoutName: "create",
              modelName: masterType,
            },
            params: {
              modelDefinition: modelDefinition,
              sourceOid: oid,
              url: url,
              locationInfo: {
                catalogOid: this.currentRecord.catalogOid,
                catalogType: this.currentRecord.catalogType,
                containerOid: this.currentRecord.containerOid,
                containerType: this.currentRecord.containerType,
                containerModelDefinition:
                  this.currentRecord.containerModelDefinition,
              },
            },
            instanceData: res,
          });
        })
        .catch((err) => {
          this.$error(err.msg);
        });
    },
    actionaddToDelivery(){
      let { query } = this.$route;
      let folderTreeParams = {
        containerModel: this.containerModel || query.modelDefinition,
      };
      fetchfolderTree
        .execute(folderTreeParams)
        .then((data) => {
          let currContainer =
            util.findItem(
              this.deepData(data, true),
              this.currentRecord.containerOid,
              "containerOid"
            ) || {};
          let currVal = currContainer.oid;
          this.formModalData = [
            {
              label: this.$t("tabel_context"),
              type: "tree",
              placeholder: this.$t("txt_select_context"),
              disabled: true,
              prop: "context",
              treeData: this.deepData(data, true),
              value: currVal,
              block: true,
              required: false,
              showSearch: true,
              treeNodeFilterProp: "title",
              filterTreeNode: (value, treeNode, field = "name") => {
                const title =
                  treeNode.componentOptions.propsData.dataRef[
                    field
                  ].toLowerCase();
                return (
                  title.indexOf((value || "").trim().toLowerCase()) > -1
                );
              },
              change: (newVal, oldVal) => {
                const containerOid = (
                  util.findItem(this.formModalData[0].treeData, newVal) ||
                  {}
                ).containerOid;

                this.$set(this.formModalData, 0, {
                  ...this.formModalData[0],
                  value: newVal,
                });
                this.$set(this.formModalData, 1, {
                  ...this.formModalData[1],
                  disabled: !newVal,
                  defaultOptions: [], // 切换上下文时，清空之前搜索的下拉交付清单列表
                  searchParams: {
                    ...this.formModalData[1].searchParams,
                    containerOid, // 设置 交付清单搜索 -- 产品容器参数
                  },
                });
              },
              dropdownStyle: {
                height: "276px",
                overflowY: "scroll",
              },
            },
            {
              label: this.$t("txt_delivery_type"),
              prop: "oid",
              type: "select",
              placeholder: this.$t("txt_enter_keyword"),
              block: true,
              fetch: getDeliveryLeafNodes,
              autoFetch: true,
              value: undefined,
              showIcon: true,
              showSearch: true,
              searchParams: {
                containerOid: "",
              },
              dropdownStyle: {
                height: "276px",
                overflowY: "scroll",
              },
            },
          ];
          this.formModalVisible = true;
        })
        .catch((err) => {});
    },
    //接口复用
    actionupdateOwner(key, row, callback){
      const {
        oid,
        type,
      } = row ? row : this.currentRecord;
      this.$refs["user-modal"]
        .show({
          type: "User",
        })
        .then((res) => {
          setOwner
            .execute({
              oid: oid,
              type: type,
              ownerAccount: res.account,
            })
            .then((res) => {
              this.$success(this.$t("msg_success"));
              this.noticeParent(key);
              callback && callback()
            })
            .catch((err) => {
              console.error(err)
              this.$error(err.msg);
            });
        });
    },
    actionsetStatus(){
      const {
        lifecycleOid,
      } = this.currentRecord;
      searchStatusList
        .execute({
          oid: lifecycleOid,
        })
        .then((res) => {
          this.statusList = (res || { context: {} }).context.states || [];
          this.operationVisible = true;
        })
        .catch((err) => {
          console.error(err);
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    actioncontrast(){
      this.formModalData = [
        {
          label: "",
          prop: "currentObject",
          block: true,
          required: false,
          slotData: this.currentRecord,
          scopedSlots: {
            default: "currentObject",
          },
        },
        {
          label: "",
          prop: "targetOid",
          block: true,
          required: false,
          slotData: this.currentRecord,
          scopedSlots: {
            default: "openSerachObjectModal",
          },
        },
      ];
      this.formModalVisible = true;
    },


    operationCancel() {
      delete this.currentRow.new_lifecycleStatus;
      this.operationVisible = false;
    },
    // 设置状态/修订/删除 -- 弹窗确定
    modalOk() {
      const { modelDefinition, oid, type, masterType, version} = this.currentRecord;
      const { new_lifecycleStatus } = this.currentRow;
      this.okBtnLoading = true;
      switch (this.modalAction) {
        // 设置状态
        case "setStatus":
          {
            part_setStatus(masterType ? masterType : type)
              .execute({
                modelInfo: {
                  modelDefinition,
                  oid,
                  type,
                },
                status:
                  new_lifecycleStatus || this.currentRecord.lifecycleStatus,
              })
              .then((res) => {
                this.$success(this.$t("msg_success"));
                delete this.currentRow.new_lifecycleStatus;
                this.operationVisible = false;
                this.noticeParent(this.modalAction, res);
              })
              .catch((err) => {
                this.$error(err.msg || this.$t("msg_failed"));
              })
              .finally(() => {
                this.okBtnLoading = false;
              });
          }
          break;
        // 修订
        case "revise":
          {
            part_revise(masterType)
              .execute({
                modelDefinition,
                oid,
                type,
                version
              })
              .then((res) => {
                if (res && res.masterType==='Part') {
                  //进行设置状态为设计中
                  this.reviseThenSetStatus(res);
                }
                this.$success(this.$t("txt_revision_success"));
                this.operationVisible = false;
                this.noticeParent(this.modalAction, res);
              })
              .catch((err) => {
                this.$error(err.msg || this.$t("msg_failed"));
              })
              .finally(() => {
                this.okBtnLoading = false;
              });
          }
          break;
        // 删除
        case "delete":
          {
            if (!this.modalSelectedRows.length) {
              this.okBtnLoading = false;
              this.$error(this.$t("txt_please_seleted_data"));
              return;
            }
            part_delete(masterType)
              .execute(this.modalSelectedRows.map((v) => v.oid))
              .then((data) => {
                this.$success(this.$t("txt_delete_success"));
                this.formModalVisible = false;
                // this.$refs.modalTableRef.reFetchData();
                this.modalTableData = this.modalTableData.filter(
                  (p) => !this.modalSelectedRows.some((t) => t.oid === p.oid)
                );
                this.noticeParent(this.modalAction);
              })
              .catch((err) => {
                console.error(err);
                this.$error(err.msg || this.$t("msg_failed"));
              })
              .finally(() => {
                this.okBtnLoading = false;
              });
          }
          break;

        default:
          break;
      }
    },

    // 移动/另存--位置 树节点选中变化
    treeSelectChange(nodeOid) {
      let item = null;
      if (this.modalAction == "saveAs") {
        item = util.findItem(this.formModalData[1].treeData, nodeOid);
      } else if (this.modalAction == "move") {
        item = util.findItem(this.formModalData[0].treeData, nodeOid);
      }
      this.selectedTreeNode = item;
    },

    // 检入/移动/重命名/另存/添加至基线/对比 -- 弹窗确定
    formModalConfirm(model) {
      const {
        modelDefinition,
        oid,
        type,

        clsCode,
        clsOid,
        clsProperty,
        defaultUnit,
        description,
        extensionContent,
        genericType,
        source,
        masterType,
      } = this.currentRecord;

      const { catalogOid, catalogType, containerOid, containerType } =
        this.selectedTreeNode || {};
      // 检入
      if (this.modalAction == "checkin") {
        part_checkIn(masterType)
          .execute({
            modelDefinition,
            oid,
            type,
            ...model,
          })
          .then((data) => {
            this.$success(this.$t("txt_check_in_success"));
            this.formModalVisible = false;
            this.noticeParent(this.modalAction);
          })
          .catch((err) => {
            console.error(err);
            this.$error(err.msg || this.$t("msg_failed"));
          });
      }
      // 移动
      else if (this.modalAction == "move") {
        part_move(masterType)
          .execute({
            node: {
              modelDefinition,
              oid,
              type,
            },
            target: {
              catalogOid: this.selectedTreeNode.oid,
              catalogType: this.selectedTreeNode.type,
              containerOid,
              containerType,
            },
          })
          .then((data) => {
            this.$success(this.$t("txt_mobile_success"));
            this.formModalVisible = false;
            this.noticeParent(this.modalAction);
          })
          .catch((err) => {
            console.error(err);
            this.$error(err.msg || this.$t("msg_failed"));
          });
      }
      // 重命名
      else if (this.modalAction == "rename") {
        part_rename(masterType, oid, model.name)
          .execute({
            ...model,
            oid,
          })
          .then((data) => {
            this.$success(this.$t("msg_success"));
            this.formModalVisible = false;
            this.noticeParent(this.modalAction);
          })
          .catch((err) => {
            console.error(err);
            this.$error(err.msg || this.$t("msg_failed"));
          });
      }
      // 另存为
      else if (this.modalAction == "saveAs") {
        part_saveAs(masterType)
          .execute({
            classificationInfo: {
              clsCode,
              oid: clsOid,
              properties: clsProperty,
            },
            defaultUnit,
            description,
            extensionContent,
            genericType,
            locationInfo: {
              catalogOid: this.selectedTreeNode.oid,
              catalogType: this.selectedTreeNode.type,
              containerOid,
              containerType,
            },
            modelDefinition,
            ...model, // 包含 name
            source,
            sourceOid: oid,
            type,
          })
          .then((data) => {
            this.$success(this.$t("msg_success"));
            this.formModalVisible = false;
            this.noticeParent(this.modalAction);
          })
          .catch((err) => {
            console.error(err);
            this.$error(err.msg || this.$t("msg_failed"));
          });
      }
      // 添加至基线
      else if (this.modalAction == "addToBaseline") {
        if (this.addToBaselineType == "create") {
          let appBuilder = this.$refs.ref_appBuilder;
          appBuilder.validate().then(() => {
            let params = appBuilder.getValue();
            // 创建基线
            createBaseline
              .execute({
                ...params,
                containerOid: this.containerOid || currentRecord.containerOid,
                containerType:
                  this.containerType || currentRecord.containerType,
                catalogOid: this.containerOid || currentRecord.containerOid,
                catalogType: this.containerType || currentRecord.containerType,
              })
              .then((res) => {
                // 添加对象到该基线
                this.addObjectToBaseline(res.oid);
              })
              .catch((err) => {
                this.$error(err.msg || this.$t("txt_create_baseline_faile"));
              });
          });
        }
        // 选择 已有基线
        else {
          this.addObjectToBaseline(model.oid);
        }
      }
      // 添加至交付清单
      else if (this.modalAction == "addToDelivery") {
        let params = {
          deliveryOid: model.oid,
          secObjList: [
            {
              masterOid: this.currentRecord.masterOid,
              masterType: this.currentRecord.masterType,
              modelDefinition: this.currentRecord.modelDefinition,
            },
          ],
        };
        batchAddInstance
          .execute(params)
          .then((res) => {
            this.$success(this.$t("txt_add_success"));
            this.formModalVisible = false;
          })
          .catch((err) => {
            this.$error(err.msg);
          });
      }
      // 对比
      else if (this.modalAction == "contrast") {
        // if(!model.targetOid) return this.$error('请选择对象');
        // return
        this.$router.push({
          name: "baseline-contrast",
          query: {
            ...this.$route.query,
            containerOid: this.containerOid,
            objectType: "object",
            sourceType: this.currentRecord.type,
            sourceOid: this.currentRecord.oid,
            sourceModelType: this.currentRecord.modelDefinition,
            targetOid: this.compareObject.oid,
            targetType: this.compareObject.type,
            targetModelType: this.compareObject.modelDefinition,
          },
        });
      }
    },
    // 添加对象到基线
    addObjectToBaseline(baselineOid) {
      const v = this.currentRecord;
      const params = {
        isReplace: false,
        oid: baselineOid,
        needExecuteFilter: true,
        secObjList: [
          {
            name: v.name,
            number: v.number,
            status: v.lifecycleStatus,
            displayVersion: v.displayVersion,
            wideType: v.type,
            sourceOid: v.oid,
            modelDefinition: v.modelDefinition,
            bizMasterType: v.masterType,
            // containerName: this.$route.query.containerName,
            subject: true,
            modelIcon: v.modelIcon,
            children: [],
            lockedTime: v.lockedTime,
            lockOwnerAccount: v.lockOwnerAccount,
            lockOwnerOid: v.lockOwnerOid,
            lockSourceOid: v.lockSourceOid,
          },
        ],
      };

      // checkBeforeLink
      //     .execute(params)
      //     .then((data) => {

      batchLink
        .execute(params)
        .then((data) => {
          this.$success(this.$t("txt_add_success"));
          this.formModalVisible = false;
        })
        .catch((err) => {
          this.formModalVisible = false;
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },

    formModalCancel() {
      this.formModalVisible = false;
    },
    // 获取弹窗中额列表数据
    fetchModalTable() {
      const { modelDefinition, oid, type } = this.currentRecord;
      // 修订时目前只展示自己
      if (this.modalAction == "revise")
        return Promise.resolve({
          data: [this.currentRecord],
        });

      return getHistoryList
        .execute({
          // modelDefinition,
          oid,
          type,
        })
        .then((data) => {
          return { data: data };
        })
        .catch((err) => {
          console.error(err);
          this.$error(err.msg || this.$t("msg_failed"));
        });
    },
    onCloseProcessModal() {
      this.processVisible = false;
    },
    fetchTable(val) {
      this.$emit("fetchTable", val);
    },
    onCloseModal() {
      this.visibleProblem = false;
    },
    //Part类型的修订后，状态改为设计中
    reviseThenSetStatus(res){
      const { modelDefinition, oid, type, masterType, version} = res;
      const { new_lifecycleStatus } = this.currentRow;
      this.okBtnLoading = true;
      part_setStatus(masterType ? masterType : type)
          .execute({
            modelInfo: {
              modelDefinition,
              oid,
              type,
            },
            status: 'Design',//状态改为设计中
          })
          .then((res) => {
            // this.$success(this.$t("msg_success"));
            delete this.currentRow.new_lifecycleStatus;
            this.operationVisible = false;
            this.noticeParent(this.modalAction, res);
          })
          .catch((err) => {
            this.$error(err.msg || this.$t("msg_failed"));
          })
          .finally(() => {
            this.okBtnLoading = false;
          });
    },
  },
};
</script>

<style lang="less" scoped>
.operation-dropdown-overlay {
  //   width: 150px;
}

.operation-modal {
  .setStatus-container {
    display: flex;
    justify-content: center;
    margin-top: 300px;

    .item-box {
      //   width: 120px;
      height: 142px;
      padding: 20px 12px 8px;
      border-radius: 5px;
      margin-right: 12px;
      cursor: pointer;
      transition: all 0.3s;
      flex-basis: 120px;
      border: 1px solid transparent;

      .circle {
        width: 16px;
        height: 16px;
        margin: 0 auto;
        border: 12px solid #a4c9fc;
        border-radius: 50%;
        box-sizing: content-box;
        position: relative;

        .line {
          position: absolute;
          top: 6px;
        }

        .left-arrow,
        .right-arrow {
          height: 1px;
          background: rgba(30, 32, 42, 0.15);
        }
        .left-arrow {
          width: 38px;
          left: -58px;

          &::after {
            content: "";
            display: block;
            position: absolute;
            right: -8px;
            top: -4px;
            width: 0;
            height: 0;
            border-top: 4px solid transparent;
            border-left: 8px solid rgba(30, 32, 42, 0.15);
            border-bottom: 4px solid transparent;
          }
        }
        .right-arrow {
          width: 42px;
          right: -58px;

          &::after {
            content: "";
            display: block;
            position: absolute;
            left: -8px;
            top: -4px;
            width: 8px;
            height: 8px;
            background: rgba(30, 32, 42, 0.15);
            border-radius: 50%;
          }
        }
      }
      .status {
        text-align: center;
        margin-top: 8px;
      }
      .text {
        text-align: center;
        opacity: 0;
        transition: all 0.3s;
        font-size: 12px;
        white-space: nowrap;
      }

      &:not(.current) {
        .text {
          margin-top: 10px;
          height: 32px;
          line-height: 32px;
          background: #ffffff;
          border: 1px solid rgba(30, 32, 42, 0.15);
          border-radius: 4px;
        }
      }
      &:hover {
        background: rgba(30, 32, 42, 0.02);
        border-color: rgba(30, 32, 42, 0.15);

        .text {
          opacity: 1;
        }
      }
      &.current {
        background: #f0f7ff;
        border: 1px solid #a4c9fc;

        .text {
          opacity: 1;
          margin-top: 15px;
          height: 22px;
        }
      }
      &.active {
        background: rgba(30, 32, 42, 0.02);
        border-color: rgba(30, 32, 42, 0.15);

        .text {
          opacity: 1;
        }
      }
    }
  }
}
</style>
