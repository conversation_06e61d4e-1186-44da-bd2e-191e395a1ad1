<!--
  @description: 日历组件
  @param { calendarDatas       | Array    } 数据源 [ {
                                                    type: "warning",
                                                    content:"7778888888",
                                                    date: "2021-07-5"
                                                  }]
  @param { valueFormat         | String   } 可选，绑定值的格式，对 value、defaultValue 起作用。不指定则绑定值为 moment 对象
  @param { disabledDate        | Function } 不可选择的日期 [默认周末] 
  @param { fullscreen          | Boolean  } 是否全屏显示 
  @param { dateCellRender      | Function } 作用域插槽，用来自定义渲染日期单元格，返回内容会被追加到单元格
  @param { monthCellRender     | Function } 作用域插槽，自定义渲染月单元格，返回内容会被追加到单元格
  @example 
  <Calendar :calendarDatas="calendarDatas"/>
 -->
<template>
  <div class="jw-calendar-wrapper">
    <a-calendar ref="ref_calendar" :fullscreen="fullscreen" :disabledDate='disabledDate' :valueFormat='valueFormat'>
      <ul slot="dateCellRender" slot-scope="value" class="calendar-items">
        <li v-for="item in getDayList(value)" :key="item.content" class="item">
          <a-badge :status="item.type" :text="item.content" />
        </li>
      </ul>
      <ul slot="monthCellRender" slot-scope="value" class="calendar-items">
        <li v-for="item in getMonthList(value)" :key="item.content" class="item">
          <a-badge :status="item.type" :text="item.content" />
        </li>
      </ul>
    </a-calendar>
  </div>
</template>
<script>
import {formatDate} from "jw_utils/moment-date";
export default {
  name: "JwCalendar",
  props: {
    valueFormat: {
      type: String,
      default: "YYYY-MM-DD HH-mm-ss"
    },
    fullscreen: {
      type: Boolean,
      default: true
    },
    calendarDatas: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    disabledDate(moment) {
      let week = moment.day();
      if (week == 0 || week == 6) {
        return true;
      }
    },
    getDayList(moment) {
      return this.calendarDatas.filter(item => {
        let calendarDate = moment.format("YYYY-MM-DD");
        let itemDate = formatDate(item.date,"YYYY-MM-DD");
        return itemDate == calendarDate;
      });
    },

    getMonthList(moment) {
      return this.calendarDatas.filter(item => {
        let calendarDate = moment.format("YYYY-MM");
        let itemDate = formatDate(item.date,"YYYY-MM");
        return itemDate == calendarDate;
      });
    }
  }
};
</script>
<style lang="less" scoped>
.jw-calendar-wrapper {
  background: #fff;
  .calendar-items {
    list-style: none;
    margin: 0;
    padding: 0;
    .item {
      background: #f5f5f5;
      padding-left: 2px;
      line-height: 18px;
      margin-bottom: 2px;
      .ant-badge-status {
        overflow: hidden;
        white-space: nowrap;
        width: 100%;
        text-overflow: ellipsis;
        font-size: 12px;
      }
    }
  }
}
</style>