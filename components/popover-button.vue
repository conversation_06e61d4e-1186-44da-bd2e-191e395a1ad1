<template>
	<a-popover
		v-model.trim="visible"
		trigger="click"
		placement="bottomLeft"
		:overlayStyle="{ width: '255px' }"
	>
		<span slot="title" class="group-titles">{{ title }}</span>
		<template slot="content">
			<a-form-model
				ref="ruleForm"
				layout="vertical"
				:model="model"
				:rules="rules"
			>
				<a-form-model-item
					ref="library"
					labelAlign="left"
					class="productlabel"
					v-for="(item, index) in dataSource"
					:key="index"
					:label="item.label"
					:prop="item.prop"
				>
					<a-input
						
						v-if="item.type == 'input' || item.type == 'textarea'"
						v-model.trim="model[item.prop]"
						:type="item.type"
						@change="item.change || (() => null)"
						:disabled="item.disabled"
					/>
					<!-- select 下拉单选 -->
					<a-select
						v-if="item.type == 'select'"
						v-model.trim="model[item.prop]"
						@change="item.change || (() => null)"
						:disabled="item.disabled"
					>
						<a-select-option
							v-for="(val, index) in item.options"
							:key="index"
							:value="val.value"
						>
							{{ val.label }}
						</a-select-option>
					</a-select>
					<!-- radio单选 -->
					<a-radio-group
						v-if="item.type == 'radio'"
						v-model.trim="model[item.prop]"
						@change="item.change || (() => null)"
					>
						<a-radio
							v-for="(val, indef) in item.radios"
							:key="indef"
							:value="val.value"
						>
							{{ val.label }}
						</a-radio>
					</a-radio-group>
				</a-form-model-item>
				<a-form-model-item class="add-new-btn">
					<a-button type="primary" :loading="addLaoding" @click="onSubmit" class="new-btn"
						>确认添加</a-button
					>
				</a-form-model-item>
			</a-form-model>
		</template>

		<a-button  type="primary" class="new-product">{{ title }}</a-button>
	</a-popover>
</template>

<script>
// 汉字2-20位
let validChinese = (rule, value, callback) => {
	let reg = /^[\u4e00-\u9fa5]{2,20}$/
	// let reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{3,15}$/
	if (!reg.test(value)) {
		callback(new Error("请输入2-20位汉字"))
	} else {
		callback()
	}
}
//20位以内的字符
let NumbeValidEzLength =(rule, value, callback) => {
	let reg = /^[0-9a-zA-Z]{2,20}$/
	// let reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{3,15}$/
	if (value&&value.length>40) {
		callback(new Error("请输入不超过40个字符"))
	} else {
		callback()
	}
}
//20位以内的字符
let envValidEzLength =(rule, value, callback) => {
	let reg = /^[0-9a-zA-Z]{2,20}$/
	// let reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{3,15}$/
	if (value&&value.length>20) {
		callback(new Error("请输入不超过20个字符"))
	} else {
		callback()
	}
}
// 数组、字母2-20位
let validPassword = (rule, value, callback) => {
	let reg = /^[0-9a-zA-Z]{2,20}$/
	// let reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{3,15}$/
	if (!reg.test(value)) {
		callback(new Error("请输入2-20位字母或数字"))
	} else {
		callback()
	}
}
// 数组、字母、中文1-20位
let validEnvValidEz = (rule, value, callback) => {
	let reg = /^[\u4E00-\u9FA5A-Za-z0-9]{1,20}$/
	// let reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{3,15}$/
	if (!reg.test(value)) {
		callback(new Error("请输入1-20位字母、数字或中文"))
	} else {
		callback()
	}
}
export default {
	name: "popover-button",

	props: {
		title: {
			type: String,
		},
		okText: {
			type: String,
			default: "确认添加",
		},
		addLaoding:{
			type:Boolean
		},
		// 生成表单域的配置项
		/**
         * [
         *  {
         *      ******************** 必须包含项 ****************************
         *      label: '名称',
         *      type: 'input' || 'textarea' || 'select',
         *      prop: '', // 数据字段，根据该字段生成 数据对象 和 校验规则
         * 
         *      // select 下拉，当 type == 'select'，需传该下拉项
         *      options: [
         *          { label: '', value: xxx }
         *      ],
         *   }
         *  ]
        // */
		dataSource: {
			type: Array,
			default: () => [],
		},
	},

	data() {
		return {
			visible: false,
			model: {}, // 数据对象
		}
	},

	watch: {
		visible(val) {
			// 关闭时重置表单为初始值并且移除校验结果
			if (!val) this.$refs.ruleForm.resetFields()
		},
		dataSource: {
			immediate: true,
			deep: true,
			handler(val) {
				// 根据每个表单域的 prop 字段，生成对应的数据对象
				const obj = {}
				val.forEach((v) => (obj[v.prop] = v.value))

				this.model = obj

				console.log("formItem.dataSource change:", val, "model：", this.model)
			},
		},
	},

	computed: {
		// 根据每个表单域的 prop 字段，生成对应的 校验规则
		rules() {
			const obj = {}
			this.dataSource.forEach((v) => {
				let ruler = []
				ruler.push({
					required: v.required != false,
					message: v.type == "input" ? `请输入` : `请选择`,
				})
				if(v.codeType=='envValidEzLength'){
					ruler = [
						{
							required: v.required != false,
							message: "请输入",
							trigger: "blur",
						},
						{ validator: envValidEzLength, trigger: "blur" },
					]
				}
				if(v.codeType=="NumbeValidEzLength"){
					ruler = [
						{
							required: v.required != false,
							message: "请输入",
							trigger: "blur",
						},
						{ validator: NumbeValidEzLength, trigger: "blur" },
					]
				}
				if (v.prop == "codeNumber") {
					ruler = [
						{
							required: v.required != false,
							message: "请输入",
							trigger: "blur",
						},
						{ validator: validPassword, trigger: "blur" },
					]
				}
				if (v.codeType&&v.codeType == "envValidEz") {
					ruler = [
						{
							required: v.required != false,
							message: "请输入",
							trigger: "blur",
						},
						{ validator: validEnvValidEz, trigger: "blur" },
					]
				}
				if (v.codeType&&v.codeType == "codeNumber") {
					ruler = [
						{
							required: v.required != false,
							message: "请输入",
							trigger: "blur",
						},
						{ validator: validPassword, trigger: "blur" },
					]
				}
				if (v.prop == "roluName") {
					ruler = [
						{ required: true, message: "请输入" },
						{
							max:20,
							validator: validChinese,
							trigger: "blur",
						},
					]
				}
				if (v.type == "input")
					ruler.push({
						whitespace: true,
					})

				obj[v.prop] = ruler
			})

			console.log("%c 根据 dataSource，求取 rules：", "color:green", obj)
			return obj
		},
	},

	methods: {
		onSubmit() {
			this.$refs.ruleForm.validate((valid) => {
				if (valid) {
					this.$emit("ok", this.model)
					this.visible = false
				}
			})
		},
	},
}
</script>

<style lang="less">
.add-new-btn {
	margin-bottom: 10px;
}
.group-titles {
	font-size: 16px;
	color: #292a2c;
	font-weight: 600;
	height: 46px;
	line-height: 46px;
	display: block;
}

.productlabel {
	padding: 0;
}

.new-btn {
	margin-top: 10px;
	float: right;
}
</style>
