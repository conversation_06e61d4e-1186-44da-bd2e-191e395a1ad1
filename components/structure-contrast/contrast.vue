<!--
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2022-01-26 10:18:13
 * @LastEditTime: 2023-03-28 21:15:35
 * @LastEditors: Please set LastEditors
-->
<!--
  @description: 结构对比组件 后端进行数据对比，前端根据flag值显示差异 (默认撑满父层)
  基于jw-table组件封装
  @param { dataSource                         | Array   } 表格列数据源
    dataSource[0] { Array                     | Array   } 左边结构行数据源
    dataSource[1] { Array                     | Array   } 右边结构行数据源
  @param { columns                            | Array   } 表格列数据源
  @param { columnTitle                        | Function} 自定义表格头显示函数,columnTitle(data，position) data对应dataSource行数据，position （left,right）对应位置
  @param { title                              | String  } 页面标题
  @param { flagOption                         | Function} 标记设置 ()=>({del: DEL_FLAG_VALUE, modify: MODIFY_FLAG_VALUE, add: ADD_FLAG_VALUE,})
  @param { lazy                               | Boolean } 懒加载标记 ，开启后 数据中有hasChild 会启动懒加载方法
  @param { loadMethod                         | Object  } 懒加载方法，lazy开启后生效 {left:leftLoad,right:rightLoad} leftLoad,rightLoad 懒加载函数 ({ row }) => Promise<any[]>


  @slot  title 标题插槽
  @slot  left-table-title 左边表格标题插槽
  @slot  right-table-title 右边表格标题插槽

  @例子 example目录下有基本用法例子
-->
<template>
  <div class="jw-structure-contrast">
    <!-- 组件头部 -->
    <div class="contrast-header">
      <div class="contrast-title">
        <slot name="title">
          <a-tooltip>
            <template #title><span>{{title}}</span></template>
            <div class="text-over">{{title}}</div>
          </a-tooltip>
        </slot>
      </div>
      <div class="contrast-diff-box">
        <p>{{$t('txt_show_diff')}}</p>
<!--        <a-switch class="contrast-diff-switch" v-model="onlyShowDiff" @change="onSwitchChange">
          <a-icon slot="checkedChildren" type="check" />
          <a-icon slot="unCheckedChildren" type="close" />
        </a-switch>-->
        <a-switch class="contrast-diff-switch" :checked="onlyShowDiff" @change="value => onOnlyShowDiffChange(value)">
          <a-icon type="filter" /> 只显示差异
        </a-switch>
      </div>
      <ul class="flag-area">
        <li class="flag-item modify">{{$t('txt_update')}}</li>
        <li class="flag-item add">{{$t('btn_create')}}</li>
        <li class="flag-item del">{{$t('btn_delete')}}</li>
      </ul>
    </div>
    <div class="contrast-table-wrap">
      <!-- 左边区域 -->
      <div class="left-area">
        <jw-table ref="leftTable" row-id="_dataIndex" :tree-config="{rowField:'_dataIndex',lazy:lazy,expandAll:true,loadMethod:loadMethodLeft}" class="contrast-table" :columns="_columns" :dataSource="leftData" v-bind="defaultConfig">
          <template #toolbar>
            <div class="contrast-table-header">
              <slot name="left-table-title">
                {{columnTitle(leftData,'left')}}
              </slot>
            </div>
          </template>
          <!-- 自定义插槽 -->
          <template #[name]="scope" v-for="name in columnSlots">
            <slot :name="name" v-bind="scope"></slot>
          </template>
        </jw-table>
      </div>
      <!-- 右边区域 -->
      <div class="right-area">
        <jw-table ref="rightTable" row-id="_dataIndex" :tree-config="{rowField:'_dataIndex',lazy:lazy,expandAll:true,loadMethod:loadMethodRight}" class="contrast-table" :columns="_columns" :dataSource="rightData" v-bind="defaultConfig">
          <template #toolbar>
            <div class="contrast-table-header">
              <slot name="right-table-title">
                {{columnTitle(rightData,'right')}}
              </slot>
            </div>
          </template>
          <!-- 自定义插槽 -->
          <template #[name]="scope" v-for="name in columnSlots">
            <slot :name="name" v-bind="scope"></slot>
          </template>
        </jw-table>
      </div>
    </div>
  </div>
</template>

<script>
import XEUtils from "xe-utils";
const DEL_FLAG_VALUE = "1"; // 删除flag的值
const MODIFY_FLAG_VALUE = "3"; // 修改flag的值
const ADD_FLAG_VALUE = "2"; // 新增flag的值
const RETAIN_FLAG_VALUE = "retain"; // 保留显示的值
// 默认列配置
const columnConfig = {
  params: {
    showHeaderMore: false,
  },
  // resizable: false,
};

export default {
  name: "jwStructureContrast",
  props: {
    title: String,
    columns: {
      type: Array,
      default: () => [],
    },
    dataSource: {
      type: Array,
      default: () => [[], []],
    },
    columnTitle: {
      type: Function,
      default: function(data, postion){(postion === "left" ? this.$t('txt_contrast')+"1" : this.$t('txt_contrast')+"2")},
    },
    flagOption: {
      type: Object,
      default: () => ({
        del: DEL_FLAG_VALUE,
        modify: MODIFY_FLAG_VALUE,
        add: ADD_FLAG_VALUE,
      }),
    },
    lazy: Boolean,
    loadMethod: {
      type: Object,
      default: () => {
        return {
          left: () => [],
          right: () => [],
        };
      },
    },
  },
  data() {
    return {
      onlyShowDiff: false,
      defaultConfig: {
        // 表格默认配置集合
        "row-class-name": this.rowClassName,
        panel: false,
        showPage: false,
        showDivider: false,
        columnSelecter: false,
        "highlight-current-row": true,
        "tooltip-config": {},
      },
      leftData: [],
      rightData: [],
    };
  },
  computed: {
    // 列自定义插槽
    columnSlots() {
      let slots = [];
      this.columns.forEach((col) => {
        if (typeof col.slots === "object") {
          slots = slots.concat(
            Object.values(col.slots).filter((slot) => typeof slot === "string")
          );
        }
      });
      return slots;
    },
    // 列数据转化
    _columns() {
      console.log('_columns 计算属性的输入 columns:', this.columns);
      return this.columns.map((column, index) => {
        let extra = {};
        if (index === 0) {
          const { del, modify, add } = this.flagOption;
          extra = {
            filters: [
              { label: this.$t('txt_diff'), value: [del, modify, add, RETAIN_FLAG_VALUE] },
            ],
            filterMethod: ({ value, row }) =>  {
              console.log('filterMethod - filter value:', value, 'row.flag:', row.flag);
              return value.includes(row.flag);
            },
            treeNode: true,
          };
        }
        return {
          ...column,
          ...columnConfig,
          ...extra,
        };
      });
    },
  },
  watch: {
    dataSource: {
      handler: function () {
        this.initData();
      },
      immediate: true,
    },
  },
  mounted() {
    // 左右表格滚动、触摸、展开同步
    this.$refs.leftTable.syncTable([this.$refs.rightTable.$refs.table]);
  },
  methods: {
    // 新增：处理开关状态变化的 方法
    onOnlyShowDiffChange(value) {
      this.onlyShowDiff = value; // 手动更新 onlyShowDiff 的值
      this.onSwitchChange();      // 然后 *显式地调用* onSwitchChange 方法
    },
    initData() {
      if (this.dataSource.length > 1) {
        let leftFlagArr = [];
        let rightFlagArr = [];
        let leftData = [];
        let rightData = [];
        leftData = XEUtils.mapTree(
          this.dataSource[0],
          (item, index, items, path) => {
            const newItem = {
              ...item,
              _dataIndex: path.join("-"),
            };
            if (item.flag === this.flagOption.del) {
              leftFlagArr.push(newItem);
            }
            return newItem;
          }
        );
        rightData = XEUtils.mapTree(
          this.dataSource[1],
          (item, index, items, path) => {
            const newItem = {
              ...item,
              _dataIndex: path.join("-"),
            };
            if (
              item.flag === this.flagOption.modify ||
              item.flag === this.flagOption.add
            ) {
              rightFlagArr.push(newItem);
            }
            return newItem;
          }
        );
        leftFlagArr.forEach((flagItem) => {
          const res = XEUtils.findTree(
            rightData,
            (item) => item._dataIndex === flagItem._dataIndex
          );
          if (res && res.item && !res.item.flag) {
            res.item.flag = RETAIN_FLAG_VALUE;
          }
        });
        rightFlagArr.forEach((flagItem) => {
          const res = XEUtils.findTree(
            leftData,
            (item) => item._dataIndex === flagItem._dataIndex
          );
          if (res && res.item && !res.item.flag) {
            res.item.flag = RETAIN_FLAG_VALUE;
          }
        });
        this.rightData = rightData;
        this.leftData = leftData;
      }
    },
    // 差异开关回调
    onSwitchChange() {
      const tables = [this.$refs.leftTable, this.$refs.rightTable];
      tables.forEach((table) => {
        console.log("目标列字段: this.columns[0].field =", this.columns[0].field); // 打印目标列字段
        const column = table.getColumnByField(this.columns[0].field);
        console.log("列的过滤器数组: column.filters =", column.filters); // 打印列的过滤器数组
        // 改变过滤属性实现只显示差异
        const option = column.filters[0];
        console.log("获取到的过滤器选项: option =", option); // 打印获取到的过滤器选项
        if (option) { // 确保 option 存在，避免空指针错误
          option.checked = this.onlyShowDiff;
        } else {
          console.warn("未找到过滤器选项，请检查列配置!"); // 警告信息，如果 option 为空
        }
        table.updateData().then(() => {
          table.setAllTreeExpand(true);
        });
      });
    },
   /* onSwitchChange() {
      const tables = [this.$refs.leftTable, this.$refs.rightTable];
      tables.forEach((table) => {
        const column = table.getColumnByField(this.columns[0].field);
        // 改变过滤属性实现只显示差异
        const option = column.filters[0];
        option.checked = this.onlyShowDiff;
        table.updateData().then(() => {
          table.setAllTreeExpand(true);
        });
      });
    },*/
    // 行类名，区分差异用
    rowClassName({ row }) {
      const { flag } = row;
      const { del, modify, add } = this.flagOption;
      if (flag === del) {
        return "del";
      } else if (flag === modify) {
        return "modify";
      } else if (flag === add) {
        return "add";
      } else {
        return "";
      }
    },
    async loadMethodLeft(params) {
      const { row } = params;
      let result = await this.loadMethod.left(params);
      return this.handLoad(row, result);
    },
    async loadMethodRight(params) {
      const { row } = params;
      let result = await this.loadMethod.right(params);
      return this.handLoad(row, result);
    },
    handLoad(row, result) {
      const handel = (data) => {
        XEUtils.eachTree(data, (item, index, items, path) => {
          item._dataIndex = row._dataIndex + "-" + path.join("-");
        });
        return data;
      };
      if (result instanceof Promise) {
        return result.then((data) => {
          return handel(data);
        });
      } else {
        // 人为制造Promise
        return Promise.resolve(handel(result));
      }
    },
  },
};
</script>

<style lang="less" scoped>
.jw-structure-contrast {
  display: flex;
  height: 100%;
  padding-bottom: 18px;
  flex-direction: column;
  overflow: hidden;
  .contrast-header {
    display: flex;
    height: 54px;
    padding: 0 18px;
    align-items: center;
  }
  .contrast-title {
    flex: 1;
    font-size: 20px;
    font-weight: @jw-font-weight-semibold;
    .text-over{
      display: inline-block;
      width: 350px;
      word-break: break-all;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1; /* 这里是超出几行省略 */
      overflow: hidden;
    }
  }
  .contrast-diff-box {
    display: flex;
    align-items: center;
    margin-right: 24px;
  }
  .contrast-diff-switch {
    margin-left: 8px;
  }
  .flag-area {
    display: flex;
    align-items: center;
    margin-bottom: 0;
  }
  .flag-item {
    position: relative;
    padding-left: 20px;
    line-height: 22px;
    height: 22px;
    &:not(:last-child) {
      margin-right: 20px;
    }
    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 3px;
      width: 16px;
      height: 16px;
      border-radius: 2px;
    }
    &.add::before {
      background: @jw-block-add-bg;
    }
    &.modify::before {
      background: @jw-block-modify-bg;
    }
    &.del::before {
      background: @jw-block-del-bg;
    }
  }
  .contrast-table-wrap {
    display: flex;
    padding: 0 18px;
    flex: 1;
    min-height: 1px;
  }
  .left-area {
    flex: 1;
    height: 100%;
    margin-right: 10px;
    min-width: 1px;
  }
  .right-area {
    flex: 1;
    height: 100%;
    margin-left: 10px;
    min-width: 1px;
  }
  .contrast-table {
    /deep/.vxe-grid--toolbar-wrapper {
      padding: 9px 16px;
      background: fade(@black, 6);
      border: 1px solid @border-color-base;
      border-bottom: none;
    }
    /deep/.vxe-body--row {
      &.add {
        background: @jw-block-add-bg;
      }
      &.modify {
        background: @jw-block-modify-bg;
      }
      &.del {
        background: @jw-block-del-bg;
      }
    }
  }
  .contrast-table-header {
    font-weight: @jw-font-weight-semibold;
  }
}
</style>
