<template>
	<div>
		<a-modal
			v-model.trim="vis"
			:title="dataProps.title"
			:confirm-loading="confirmLoading"
			@ok="handleOk"
			@cancel="modelHandleCancel"
		>
			<div style="padding-top: 20px">
				<a-form-model>
					<a-form-model-item label="">
						<span class="dowloudModel" @click="dowloudModel">下载导入模版</span>
						<!-- :customRequest='customRequest' -->
						<a-upload-dragger
							name="file"
							:multiple="false"
							:fileList="fileList"
							:before-upload="() => false"
							@change="change"
						>
							<p class="ant-upload-drag-icon">
								<a-icon type="inbox" />
							</p>
							<p class="ant-upload-text">
								支持扩展名：xlsx
								<!-- 支持扩展名：.rar .zip .doc .docx .pdf .jpg ... -->
							</p>
						</a-upload-dragger>
					</a-form-model-item>
				</a-form-model>
			</div>
		</a-modal>
	</div>
</template>

<script>
import ModelFactory from "jw_apis/model-factory"
import { uploadFile } from "../apis/uploud-file"
import { getCookie } from "jw_utils/cookie";
export default {
	data() {
		return {
			// visible: true,
			confirmLoading: false,
			vis: false,
			dataProps: {
				title: "",
			},
			uploadApi: {
				url: "", //文件上传的地址
				method: "post", //上传的请求方式
			},
			formData: "",
			fileList: [],
		}
	},
	props: {
		modelDownloadCode: {
			type: String,
		},
		importCodes: {
			type: String,
		},
		visible: {
			type: Boolean,
		},
		action: {
			type: String,
			require: true,
		},
		uploadUrl: {
			type: String,
		},
		code: {
			type: String,
			require: true,
		},
		// uploadApi:{
		//     type:Array,
		//     default:() => []
		// }
	},
	watch: {
		visible(val) {
			if (this.vis != val) this.vis = val
		},
		vis(val) {
			if (!val) {
				this.$emit("update:visible", false)
			}
		},
	},
	methods: {
		//   取消上传关闭弹框
		modelHandleCancel() {
			this.fileList = []
			// this.visible = !this.visible;
		},
		showModal() {
			// this.visible = true;
		},
		handleOk(e) {
			this.ModalText = "The modal will be closed after two seconds"
			this.confirmLoading = true
			setTimeout(() => {
				// this.visible = false;
				this.confirmLoading = false
			}, 2000)
		},
		//文件类型不符合时
		onErroFile() {
			this.$message.info("暂不支持上传的文件类型，请重新选择")
		},

		uploadsChange(info) {
			//   组件方法
			this.$emit("onUploadsChange", info)
		},
		//下载模版
		dowloudModel() {
			const accesstoken = getCookie('token')
			//地址直接下载方法
			window.location.href = `${Jw.gateway}/${Jw.ieconfigService}/${Jw.ieconfigService}uration/downloadTempleByCode?code=${this.modelDownloadCode}&access_token=${accesstoken}&accesstoken=${accesstoken}&appName=pdm`

			//接口下载方法
			// let qurey = {
			// 	code: this.modelDownloadCode,
			// 	access_token:accesstoken,
			// 	accesstoken:accesstoken,
			// 	appName:'pdm'
			// }
			// 	fetch(`${Jw.gateway}ieconfig/${Jw.ieconfigService}uration/downloadTempleByCode?code=${ this.modelDownloadCode}&access_token=${accesstoken}&accesstoken=${accesstoken}&appName=pdm`, {
			// 		method: "get",
			// 		// body: JSON.stringify(qurey),
			// 		headers: {
			// 			appName: Jw.appName,
			// 			accesstoken,
			// 		},
			// 	})
			// 		.then((response) => {
			// 			if (!response.ok) {
			// 				throw new Error("Network response was not OK")
			// 			}
			// 			return response.blob()
			// 		})
			// 		.then((res) => {
			// 			let url = window.URL.createObjectURL(
			// 				new Blob([`${Jw.gateway}ieconfig/${Jw.ieconfigService}uration/downloadTempleByCode?code=${ this.modelDownloadCode}&access_token=${accesstoken}&accesstoken=${accesstoken}&appName=pdm`], {
			// 					type: "application/vnd.ms-excel",
			// 				})
			// 			)
			// 			let link = document.createElement("a")
			// 			link.href = url
			// 			link.setAttribute("download", this.downloadCode)
			// 			document.body.appendChild(link)
			// 			link.click()
			// 		})
			// 		.catch((err) => {})

			this.$emit("dowloudModel")
		},
		handleOk() {
			let type = false
			// /permissions/v1/group/afterImport
			if(this.fileList&&this.fileList.length>0){
				this.confirmLoading = true
				const userUploadFlie = (data) =>
				ModelFactory.create({
					url: `${Jw.gateway}/${this.uploadUrl}?code=${data}`,
					method: "post",
				})
				const clearLocal =
				ModelFactory.create({
					url: `${Jw.gateway}/${Jw.permissions}/v1/group/afterImport`,
					method: "post",
				})
			userUploadFlie(this.importCodes)
				.execute(this.formData)
				.then((res) => {
					
					type=true
					this.confirmLoading = false
					clearLocal.execute().then(res=>{
						console.log('执行清除redis')
					})
					this.$emit('handleOk',type)
					this.$success("导入成功")
				})
				.catch((err) => {
					this.confirmLoading = false
					this.$error(err.msg || "导入失败，请重试")
				})
			}else{
				this.$error('请选择文件！')
			}
			
		},
		//导入视图模版
		change(info) {
			const { file } = info
			let fileType = /\.[^\.]+$/.exec(file.name)
			if(fileType=='.xlsx'){
				const formData = new FormData()
			let fileList = [...info.fileList]
			// 限制选择文件的个数
			fileList = fileList.slice(-1)

			fileList = fileList.map((file) => {
				if (file.response) {
					// Component will show file.url as link
					file.url = file.response.url
				}
				return file
			})

			this.fileList = fileList
			formData.append("file", file)
			// 存储选择的文件
			console.log(formData, fileList)
			this.formData = formData
			}else{
				this.fileList =[]
				this.$error('请选择 xlsx 的文件格式上传')
			}
			
		},
	},
	mounted() {},
}
</script>
<style scoped>
.dowloudModel {
	cursor: pointer;
	color: red;
}
</style>
