<template>
  <a-modal width="65%" v-if="visible" v-model.trim="visible" :title="$t('release_design_task')" :okText="$t('btn_ok')" :cancelText="$t('btn_cancel')" @ok="confirm" @cancel="visible = false">
    <jw-table
      :data-source.sync="tableData"
      :columns="columns"
      :loading="loading"
      :height="700"
      :showPage="false"
    >
      <template #thumbSlot="{ row }">
        <!-- 3D图类型 -->
        <cad-thumbnail
          :currentRow="row"
        ></cad-thumbnail>
      </template>
      <template #numberSlot="{ row }">
        <jwIcon
          :type="row.modelIcon"
          />
        {{  row.number }}
      </template>

      <template #operation="{ row }">
        <template>
          <a-tooltip :title="$t('update_txt_owner')">
            <span @click="updateowner(row)">
              <jw-icon type="jwi-iconedit"></jw-icon>
            </span>
          </a-tooltip>
          <a-tooltip :title="$t('send_action')">
            <span @click="sendOne(row)">
              <jw-icon type="jwi-iconshare"></jw-icon>
            </span>
          </a-tooltip>
        </template>
      </template>
    </jw-table>
     <!-- 修改所有者 -->
     <jw-user-modal-v2 ref="user-modal" :isCheckbox="false" type="owner" />
  </a-modal>
</template>

<script>
import { findMCADSecBom, releaseDesignTaskApi } from "apis/part";
import cadThumbnail from "components/cad-thumbnail.vue";
import { jwUserModalV2 } from "jw_frame";
export default {
  components: {
    cadThumbnail, jwUserModalV2
  },
  data(){
    return {
      tableData: [],
      loading: false,
      visible: false,
      detail: null,
      columns: [
        {
          field: "thumbnailOid",
          title: "",
          params: {
            showHeaderMore: false,
          },
          width: '40px',
          className: 'thumbSlotclass',
          slots: {
            default: "thumbSlot",
          },
        },
        {
          field: "number",
          title: this.$t("txt_number_of"),
          slots: {
            default: "numberSlot",
          },
        },
        {
          field: "name",
          title: this.$t("txt_name"),
          sortable: true,
        },
        {
          field: "owner",
          title: this.$t("txt_owner"),
          sortable: true, // 开启排序
        },
        {
          // 操作列定义
          field: "operation", //关键字
          title: this.$t("txt_operation"),
          slots: {
            default: "operation",
          },
        },
      ]
    }
  },
  watch: {
    detail: {
      handler: function(val){
        if(val){
          this.reloadtree()
        }else{
          this.tableData = []
        }
        
      },
      immediate: true
    }
  },
  methods: {
    updateowner(row){
      this.$parent.actionupdateOwner('updateOwner', row, () => {
        this.reloadtree()
      })
    },
    //重新加载下级
    reloadtree(){
      this.loading = true
      findMCADSecBom.execute({rootOid: this.detail.oid}).then(resp => {
        this.tableData = resp.children || []
      }).finally(() => {
        this.loading = false
      })
    },
    confirm(){
      let reqs = this.tableData.map(item => {
        return releaseDesignTaskApi.execute({cadOid: item.oid})
      })
      Promise.all(reqs).then(resp => {
        this.$success(this.$t("send_success"));
        this.visible = false
      }).catch(e => {
        console.error(e)
        this.$error(e.msg || this.$t('send_error'))
      })
    },
    sendOne(row){
      releaseDesignTaskApi.execute({cadOid: row.oid}).then(resp => {
        this.$success(this.$t("send_success"));
      }).catch(e => {
        this.$error(e.msg || this.$t('send_error'))
      })
    }
  }
}
</script>

<style>

</style>