<template>
  <!-- 
  @description: 头像组件
  @param { data                  | Object，Array } 头像数据 ，值为数组时 数组项为下列属性组成的对象
    data[name] {name             | String        } 名称（与头像地址二选一）
    data[src]  {src              | String        } 头像地址（与名称二选一）
    data[loadError]  {loadError  | function      } 头像地址加载失败回调 （传入头像地址才会触发）
  @param { shape                 | String        } 指定头像的形状 默认circle  Enum{ 'circle', 'square' }
  @param { showName              | Boolean       } 是否在头像旁展示名称，默认为false
  @param { tag                   | Boolean       } 样式改为标签包裹，常配合showName使用，默认为false
-->

  <div
    class="jw-avatar"
    :class="{ 'is-group': isGroup, 'is-tag': tag && !isGroup }"
  >
    <a-tooltip>
      <template slot="title">
        {{ tooltipList }}
      </template>
      <div
        class="jw-avatar-box"
        :class="{
          extra: item.isExtra,
          'update-state': updateState(),
        }"
        v-for="(item, index) in showList"
        :key="index"
      >
        <!-- 头像 -->
        <avatar
          :shape="shape"
          :size="size"
          :src="item.avatar"
          :icon="item.icon"
          :loadError="item.loadError"
          :style="{ zIndex: showList.length - index }"
          class="jw-avatar-item"
        >
          {{ filterName(item.name) }}
        </avatar>
        <i
          v-if="updateState()"
          title="删除"
          class="jwi-icondelete delete-btn"
          @click="onDelete(index)"
        ></i>
        <!-- 名称 -->
        <span class="jw-avatar-name" v-if="showName && !isGroup">{{
          item.name
        }}</span>
      </div>
    </a-tooltip>
  </div>
</template>

<script>
import { Avatar } from "ant-design-vue";

export default {
  name: "JwAvatar",
  components: {
    Avatar,
  },
  props: {
    data: {
      type: [Object, Array],
      default: () => {},
    },
    shape: {
      type: String,
      default: "circle",
    },
    tag: {
      type: Boolean,
      default: false,
    },
    showName: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "User",
      validator: function (value) {
        return ["User", "Role"].indexOf(value) !== -1;
      },
    },
    layoutName: String,
    title: String,
  },
  filters: {
    filterName: function (name) {
      if (!name) {
        return name;
      }
      // 是否含有中文
      const hasCh = /[\u4E00-\u9FA5]/.test(name);
      let showName = "";
      if (hasCh) {
        // 用户 含有中文取后两个字符
        showName = name.slice(-2);
      } else {
        // 默认取前两个字符
        showName = name.slice(0, 2).toLocaleUpperCase();
      }
      return showName;
    },
  },
  data() {
    return {
      showList: [],
      tooltipList: "",
      user: Jw.getUser(),
    };
  },
  computed: {
    size() {
      return this.tag ? "small" : "default";
    },
    isGroup() {
      return Array.isArray(this.data);
    },
  },
  watch: {
    data: {
      handler: function () {
        this.initList();
      },
      deep: true,
    },
  },
  created() {
    this.initList();
  },
  methods: {
    updateState() {
      if (
        !this.layoutName ||
        !this.title ||
        this.layoutName === "show" ||
        this.title === "updateBy" ||
        this.title === "createBy"
      ) {
        return false;
      } else {
        if (this.showList.length === 1) {
          return false;
        } else {
          return true;
        }
      }
    },
    onDelete(i) {
      this.showList.splice(i, 1);
      this.$emit("change", this.showList);
    },
    // 初始化数据
    initList() {
      const { data } = this;
      const mergeDefault = (data, index) => {
        const defaultOption = {
          loadError: () => {
            // 默认图片加载失败显示名称
            this.$set(this.showList, index, { ...data, src: "" });
          },
          alt: data.name,
        };
        return Object.assign(defaultOption, data);
      };
      if (Array.isArray(data)) {
        this.tooltipList = data.map(item => item.name).join();
        // 数组时取前四项
        let list = data.slice(0, 4);
        const len = data.length;
        if (len > 4) {
          // 大于10项展示省略
          if (len > 10) {
            list.push({
              icon: "ellipsis",
              isExtra: true,
            });
          } else {
            // 大于4项展示 +num
            list.push({
              name: "+" + (len - 4),
              isExtra: true,
            });
          }
        }
        this.showList = list.map((item, index) => mergeDefault(item, index));
      } else {
        this.showList = [mergeDefault(data, 0)];
        this.tooltipList = data.name;
      }
      console.log("this.showList", this.showList);
    },
    filterName(name) {
      if (!name) {
        return name;
      }
      // 是否含有中文
      const hasCh = /[\u4E00-\u9FA5]/.test(name);
      let showName = "";
      if (this.type == "User") {
        // 用户 含有中文取后两个字符
        showName = name.slice(-2);
      } else {
        // 默认取前两个字符
        showName = name.slice(0, 2).toLocaleUpperCase();
      }
      return showName;
    },
  },
};
</script>

<style lang="less" scoped>
.jw-avatar {
  // width: 100%;
  display: inline-flex;
  align-items: center;
  &.is-tag {
    padding: 4px 8px;
    background: @jw-bg;
    border-radius: @border-radius-base;
    .jw-avatar-name {
      color: @primary-color;
    }
  }
  &.is-group {
    // padding-left: 6px;
    .jw-avatar-box {
      margin-left: -6px;
    }
  }
}

.jw-avatar-box {
  display: inline-flex;
  align-items: center;
  position: relative;
  &.extra {
    .jw-avatar-item {
      color: @text-color-secondary;
    }
  }
  .jw-avatar-item {
    border: 1px solid @white;
    box-sizing: content-box;
    position: relative;
    &::after {
      position: absolute;
      left: 0;
      top: 0;
      z-index: 1;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      opacity: 0;
      // transition: all 0.3s;
      content: " ";
    }
  }
  .delete-btn {
    cursor: pointer;
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 10;
    white-space: nowrap;
    transform: translate(-50%, -50%);
    opacity: 0;
    color: red;
  }
}
.update-state:hover {
  .jw-avatar-item::after {
    opacity: 1;
  }
  .delete-btn {
    opacity: 1;
  }
}
.jw-avatar-name {
  margin-left: 8px;
  line-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  // width: 100%;
}
</style>
<style>
.vxe-table .jw-avatar-box {
  /* width: 100%; */
}
</style>
