<!--
 * @Description: 
 * @Author: <EMAIL>
 * @Date: 2022-02-25 15:17:45
 * @LastEditTime: 2022-02-25 15:52:23
 * @LastEditors: <EMAIL>
-->
<template>
  <div class="example">
    <!-- 头像为图片 -->
    <jw-avatar :data="avatarData1" />
    <br>
    <!-- 头像为名称 -->
    <jw-avatar :data="avatarData2" />
    <br>
    <!-- 头像展示为tag形式并显示名称 -->
    <jw-avatar tag show-name :data="avatarData3" />
    <br>
    <!-- 头像组 -->
    <jw-avatar :data="avatarData4" />
    <br>
    <!-- 超过10个的头像组 -->
    <jw-avatar :data="avatarData5" />
  </div>
</template>

<script>
import { jwAvatar } from "jw_frame";
export default {
  name: "AvatarExample",
  components: {
    jwAvatar,
  },
  data() {
    return {
      avatarData1: {
        src: "http://gateway.dev.jwis.cn/file/file/downloadByOid?oid=e9330101d53c428db17b40f754cf70f4",
        name: "张三",
      },
      avatarData2: {
        name: "张三",
      },
      avatarData3: {
        src: "http://gateway.dev.jwis.cn/file/file/downloadByOid?oid=e9330101d53c428db17b40f754cf70f4",
        name: "张三",
      },
      avatarData4: [{ name: "张三" }, { name: "张三" }],
      avatarData5: [
        { name: "张三" },
        { name: "张三" },
        { name: "张三" },
        { name: "张三" },
        { name: "张三" },
        { name: "张三" },
        { name: "张三" },
        { name: "张三" },
        { name: "张三" },
        { name: "张三" },
        { name: "李四" },
      ],
    };
  },
};
</script>

<style lang="less" scoped>
</style>