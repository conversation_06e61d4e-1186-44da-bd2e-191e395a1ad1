
<template>
    <a-modal
        :title="title"
        :width='width'
        :visible="visible"
        :mask-closable='maskClosable'
        destroyOnClose
        :body-style='bodyStyle'
        @cancel='cancel("close")'
        :dialogClass='`${dialogClass} base-color-modal`'
    >
        <slot></slot>
		<template
			slot="footer"
		>
            <slot name="footer-before-btn"></slot>
			<a-button @click="confirm" type='primary' :loading="okBtnLoading">{{ okText }}</a-button>
			<a-button @click="cancel">{{ cancelText }}</a-button>
		</template>
    </a-modal>
</template>

<script>
export default {
    name: 'base-color-modal',
    props: {
        title: '',
        width: {
            type: Number,
            default: 512, // 512/992/1280/1514
        },
        bodyStyle: {
            type: Object,
            default: () => ({ maxHeight: '580px', overflowY: 'scroll' }),
        },
        // 浮层类名
        dialogClass: {
            type :String,
            default: '',
        },
        // 确认按钮是否处于加载状态
        okBtnLoading: {
            type: Boolean,
            default: false,
        },
		okText: {
			type: String,
			default:function(){
                return this.$t('btn_ok')
            } 
		},
		cancelText: {
			type: String,
			default: function(){
                return this.$t('btn_cancel')
            } 
		},
        visible: {
            type: Boolean,
        },
        // 点击蒙层是否允许关闭
        maskClosable: {
            type: Boolean,
            default: false,
        }
    },

    methods: {
        confirm () {
            this.$emit('ok');
        },
        cancel (flag) {
            this.$emit('cancel', flag);
        }
    }
}
</script>

<style lang="less">
.base-color-modal {
    font-family: PingFangSC-Medium;
    box-shadow: 0 6px 16px -8px rgba(0,0,0,0.08), 0 9px 28px 0 rgba(0,0,0,0.05), 0 12px 48px 16px rgba(0,0,0,0.03);
    border-radius: 8px;
    padding-bottom: 0;

    .ant-modal-content {
        background-image: linear-gradient(180deg, #EAF1FF 0%, #FFFFFF 33%);

        .ant-input {
            background: transparent;
        }
        .ant-modal-header {
            background: transparent;
            height: 60px;
            border-bottom: none;
            .ant-modal-title {
                font-weight: 500;
            }
        }
        .ant-modal-body {
            padding: 0 24px 16px;

            .layout-body-generated {
                background: transparent;
                padding: 0;
                border: none;

                .column {
                    margin: 0;
                }
            }
            .jw-table .vxe-table--main-wrapper .vxe-table .body--wrapper {
                background: transparent;
            }
        }
        .ant-modal-footer {
            height: 60px;
            padding: 14px 24px;
            border-top: none;
        }
    }
}
</style>