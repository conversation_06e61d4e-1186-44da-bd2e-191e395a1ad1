<template>
  <a-modal
    width="50%"
    v-model.trim="visible"
    :destroyOnClose="true"
    title="Basic Modal"
    @ok="onConfirm"
    @cancel="onCancel"
  >
    <a-input
      v-model.trim="searchKey"
      :placeholder="$t('msg_input')"
      @pressEnter="onSearch"
    >
      <a-icon slot="prefix" type="search" />
    </a-input>
    <a-tabs :default-active-key="activeKey" @change="callback">
      <a-tab-pane :key="item.name" :tab="item.name" v-for="item in sourceTabs">
        <JwTable
          rowKey="oid"
          :options="item.header"
          :fetch="item.fetch"
          :params="params"
          :selected.sync="selectedRowKeys"
        />
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>
<script>
//组件
import JwTable from "jw_components/table"

//接口
import ModelFactory from "jw_apis/model-factory"

const searchApi = ModelFactory.create({
  url: `${Jw.gateway}/${Jw.ecService}/v1/adapter/findAll`,
  method: "get",
})
const dictModelUrl = `${Jw.gateway}/dict/dictValueDefinition/searchByDictCodeAndLocale/`
const fetchDictCodeApi = ModelFactory.create({
  method: "get",
})
const searchDataApi = ModelFactory.create({
  method: "get",
})
export default {
  components: {
    JwTable,
  },
  props: {
    delayHide: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      sourceTabs: [],
      searchKey: "",
      activeKey: "",
      selectedRowKeys: [],
      params: {
        like: "",
      },
    }
  },
  created() {
    this.fetchSourceList()
  },
  methods: {
    show(options) {
      if (options) {
        this.title = options.title
      }

      this.visible = true

      return new Promise((resolve, reject) => {
        this.onCancel = () => {
          this.hide()
          reject("cancel")
        }

        this.onConfirm = () => {
          if (this.delayHide) {
            resolve(this.selectedRowKeys)
          } else {
            this.hide()
            resolve(this.selectedRowKeys)
          }
        }
      })
    },

    hide() {
      this.visible = false
    },

    afterClose() {
      this.searchKey = ""
    },

    onSearch() {
      this.params.like = this.searchKey
    },
    fetchSourceList() {
      searchApi.execute().then((res) => {
        this.activeKey = res[0].name
        this.sourceTabs = res
        this.fetchTableHeaders()
      })
    },
    fetchTableHeaders() {
      this.sourceTabs.forEach((item) => {
        item.fetch = this.fetchData(item.adapterUrl)
        const url = `${dictModelUrl}${item.name}DisplayField/${Jw.defaultLanguage}`

        fetchDictCodeApi.setUrl(url)
        fetchDictCodeApi.execute().then((res) => {
          item.header = res.map((item) => {
            item.title = item.value
            item.prop = item.key
            return item
          })
        })
      })
    },

    fetchData(url) {
      return (api) => {
        if (!this.params.like) return { data: [], total: 0 }
        searchDataApi.setUrl(url)
        let params = this.params
        return searchDataApi
          .execute(params)
          .then((res) => {
            return { data: res || [], total: res.length || 0 }
          })
          .catch((err) => {
            this.$error(err.msg)
            return { data: [], total: 0 }
          })
      }
    },
    callback() {},
    showModal() {
      this.visible = true
    },
    handleOk(e) {
      console.log(e)
      this.visible = false
    },
    onCancel: _.noop,
    onConfirm: _.noop,
  },
}
</script>
