/**
 *路由配置样例 
 *
 * <AUTHOR>
 * @date 2019-01-25
 */
import { useExternLanguages, useExternRouters, useRouterBeforeEach } from "jw_common/hook"
import language from '../locale'
import Vue from 'vue'
import VueRouter from 'vue-router'
import routerList from "./router-list"

const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
    return originalPush.call(this, location).catch(err => err)
}
Vue.use(VueRouter)

export let init = function () {
    useExternLanguages(language)
    useExternRouters(routerList)
    useRouterBeforeEach((router) => {
        // checkPermission(router)
    })
}

const checkPermission = (router) => {
    let { to, next } = router
    let path = to.path
    console.log('%c [ path ]-25', 'font-size:13px; background:pink; color:#bf2c9f;', path)
    if (path != "/noPermission") {
        if (path == "/login") {
            next()
        } else {
            let flatMenuDataArray = JSON.parse(sessionStorage.getItem('flatMenuDataArray'))
            if (flatMenuDataArray && flatMenuDataArray.some(item => item.path == path && item.status == 'enable')) {
                next()
            } else {
                next()
                // next({ path: "/noPermission" })
            }
        }
    } else {
        next()
    }
}

export default routerList