/**
 * 需要动态的路由列表配置
 */
const routersList = [
    {
        path: '/',
        component: () => import( /*webpackChunkName: "home"*/ 'views/home'),
        redirect: '/dashboard',
        children:
        [
            {
                path: '/dashboard',
                name: 'dashboard',
                hidden:true,
                component: () => import( /*webpackChunkName: "dashboard"*/ 'views/dashboard'),
            },
            {
                path: '/detailPage',
                name: 'detailPage',
                component: () => import( /*webpackChunkName: "detail-page"*/ 'views/detail-page'),
                children: [
                    {
                        // 产品容器/资源容器-对象详情
                        path: 'object-details',
                        name: 'object-details',
                        component: () => import( /*webpackChunkName: "dashboard"*/ 'views/product-container/object-details'),
                    },
                    {
                        // 产品容器-基线管理-基线详情
                        path: 'baseline-details',
                        name: 'baseline-details',
                        component: () => import( /*webpackChunkName: "dashboard"*/ 'views/product-content/baseline-manage/baseline-details'),
                    },
                    {
                        // 变更管理 ecr详情
                        path: 'ecr-details',
                        name: 'ecr-details',
                        component: () => import( /*webpackChunkName: "change-ecr-edit"*/ 'views/change-management/ecr/detail'),
                    },
                    {
                        // 变更管理 eco详情
                        path: 'eco-details',
                        name: 'eco-details',
                        component: () => import( /*webpackChunkName: "change-eco-detail"*/ 'views/change-management/eco/detail'),
                    },
                    {
                        // 变更管理 eca详情
                        path: 'eca-details',
                        name: 'eca-details',
                        component: () => import( /*webpackChunkName: "change-eca-detail"*/ 'views/change-management/eca/detail'),
                    },
                    {
                        // 变更管理 ecr
                        path: 'test-page',
                        component: () => import( /*webpackChunkName: "workflow"*/ 'views/test-page'),
                    },
                    {
                        // 任务详情
                        path: 'detailed-task',
                        component: () => import( /*webpackChunkName: "workflow"*/ 'views/product/contentManagement/workflow/particulars-task'),
                    },
                    {
                        // 产品容器内容管理
                        path: 'product-content',
                        name: 'product-content',
                        component: () => import( /*webpackChunkName: "product"*/ 'views/product-content'),
                    },
                    {
                        // 问题详情
                        path: 'problem-details',
                        name: 'problem-details',
                        component: () => import( /*webpackChunkName: "problem-detail"*/ 'views/problem-manage/problem-detail'),
                    },
                ]
            },
            {
                path: '/baseline/:oid/:type',
                name: 'baseline',
                component: () => import( /*webpackChunkName: "baseline"*/ 'views/product/baseline'),
            },
            {
                path: '/workflow',
                name: 'workflow',
                component: () => import( /*webpackChunkName: "workflow"*/ 'views/product/contentManagement/workflow'),
            },
            //待办任务
            {
                path: '/wait-tasks',
                name: 'wait-tasks',
                component: () => import( /*webpackChunkName: "workflow"*/ 'views/product/contentManagement/workflow/wait-tasks'),
            },
            //我的流程
            {
                path: '/my-process',
                name: 'my-process',
                component: () => import( /*webpackChunkName: "workflow"*/ 'views/product/contentManagement/workflow/my-process'),
            },
             // 团队管理
            {
                path: '/process-team-management',
                name: 'process-team-management',
                component: () => import( /*webpackChunkName: "process-team-management"*/ 'views/product/contentManagement/workflow/process-team-management/views'),
            },
            // 用户产品权限
            {
                path: '/user-product-permission',
                name: 'user-product-permission',
                component: () => import( /*webpackChunkName: "user-product-permission"*/ 'views/audit-management/user-product-permission'),
            },
            {
                path: '/process-task',
                name: 'process-task',
                component: () => import( /*webpackChunkName: "workflow"*/ 'views/product/contentManagement/workflow/process-task'),
            },
            {
                // 流程详情
                path: '/particulars-task',
                name: 'particulars-task',
                component: () => import( /*webpackChunkName: "workflow"*/ 'views/product/contentManagement/workflow/particulars-task'),
            },
            {
                path: '/contentManage/:oid/:type',
                name: 'contentManage',
                component: () => import( /*webpackChunkName: "contentManagement"*/ 'views/product/contentManagement'),
            },
            {
                path: '/changeManage/:oid/:type',
                name: 'changeManage',
                component: () => import( /*webpackChunkName: "changeManage"*/ 'views/product/changeManage/index'),
            },
            {
                path: '/changeManage/detail',
                name: 'detail',
                component: () => import( /*webpackChunkName: "changeDetail"*/ 'views/product/changeManage/changeDetail/detail'),
            },
            {
                path: '/workflow/list',
                name: 'list',
                component: () => import( /*webpackChunkName: "todoTasks"*/ 'views/product/workflow/todoTasks'),
            },
            {
                path: '/changeStep',
                name: 'changeStep',
                component: () => import( /*webpackChunkName: "changeStep"*/ 'views/product/changeManage/step/changeStep'),
            },
            {
                path: '/versionComparison',
                name: 'versionComparison',
                component: () => import( /*webpackChunkName: "versionComparison"*/ 'views/product/contentManagement/details/versionComparison'),
            },
            {
                path: '/workflow/task-audit',
                name: 'task-audit',
                component: () => import( /*webpackChunkName: "taskAudit"*/ 'views/product/workflow/task-audit'),
            },
            {
                path: '/task/:workflowId/:taskId/:processDefinitionId',
                name: 'handle-task',
                component: () => import( /*webpackChunkName: "handleTask"*/ 'views/product/workflow/handle-task'),
            },
            {
                path: '/workflow/task-sign',
                name: 'task-sign',
                component: () => import( /*webpackChunkName: "taskSign"*/ 'views/product/workflow/task-sign'),
            },
            {
                path: '/full-text-search',
                name: 'full-text-search',
                component: () => import( /*webpackChunkName: "full-text-search"*/ 'components/full-text-search'),
            },
            {
                path: '/productStructure/:oid/:type',
                name: 'productStructure',
                component: () => import( /*webpackChunkName: "productStructure"*/ 'views/product-content/product-structure'),
            },
            {
                path: '/createInstance',
                name: 'createInstance',
                component: () => import( /*webpackChunkName: "createInstance"*/ 'views/product-content/product-structure/createInstance'),
            },
            {
                path: '/featureManage',
                name: 'featureManage',
                component: () => import( /*webpackChunkName: "featureManage"*/ 'views/product-content/product-structure/featureManage'),
            },

            {
                path:'/tenant-management',
                name:'tenant-management',
                component:()=>import( 'views/tenant-management')
            },
            {
                path: '/workflow/start/:id',
                name: 'workflowStart',
                component: () => import( /*webpackChunkName: "workflowStart"*/ 'views/product/contentManagement/workStart'),
            },
            {
                path: '/myTask',
                name: 'myTask',
                component: () => import( /*webpackChunkName: "workflowStart"*/ 'views/my-task'),
            },
            {
                // 产品容器
                path: '/my-permissions',
                name: 'my-permissions',
                component: () => import( /*webpackChunkName: "my-permissions"*/ 'views/my-permissions'),
            },
            {
                // 产品容器
                path: '/product',
                name: 'product',
                component: () => import( /*webpackChunkName: "product"*/ 'views/product-container'),
            },
            {
                path: '/baseline-contrast',
                name: 'baseline-contrast',
                component: () => import( /*webpackChunkName: "dashboard"*/ 'views/product-content/baseline-manage/baseline-contrast'),
            },
            {
                // 产品库
                path: '/product-library',
                name: 'product-library',
                component: () => import( /*webpackChunkName: "product"*/ 'views/product-library'),
            },
            {
                // 产品容器内容管理
                path: '/product-content',
                name: 'productContent',
                component: () => import( /*webpackChunkName: "product"*/ 'views/product-content'),
            },
            {
                // 产品容器
                path: '/product-spectrum',
                name: 'product-spectrum',
                component: () => import( /*webpackChunkName: "dashboard"*/ 'views/product-spectrum'),
            },
            {
                // 资源容器
                path: '/resource-container',
                name: 'resource-container',
                component: () => import( /*webpackChunkName: "resourceContainer"*/ 'views/resource-container'),
            },
            {
                // 模板
                path: '/template',
                name: 'template',
                component: () => import( /*webpackChunkName: "dashboard"*/ 'views/template'),
            },
            {
                // 模板
                path: '/template-upload',
                name: 'template-upload',
                component: () => import( /*webpackChunkName: "dashboard"*/ 'views/template/upload.vue'),
            },


            {
                // 变更管理 ecr创建
                path: '/change-management/ecr/create/:oid',
                name: 'ecr-create',
                component: () => import( /*webpackChunkName: "change-ecr-create"*/ 'views/change-management/ecr/create'),
            },
            {
                // 变更管理 ecr编辑
                path: '/change-management/ecr/edit/:oid',
                name: 'ecr-edit',
                component: () => import( /*webpackChunkName: "change-ecr-edit"*/ 'views/change-management/ecr/edit'),
            },
            {
                // 变更管理 ecr详情
                path: '/change-management/ecr/detail/:oid',
                name: 'ecr-detail',
                component: () => import( /*webpackChunkName: "change-ecr-detail"*/ 'views/change-management/ecr/detail'),
            },
            {
                // 变更管理 eco创建
                path: '/change-management/eco/create/:oid',
                name: 'eco-create',
                component: () => import( /*webpackChunkName: "change-eco-create"*/ 'views/change-management/eco/create'),
            },
            {
                // 变更管理 eco编辑
                path: '/change-management/eco/edit/:oid',
                name: 'eco-edit',
                component: () => import( /*webpackChunkName: "change-eco-edit"*/ 'views/change-management/eco/edit'),
            },
            {
                // 变更管理 eco详情
                path: '/change-management/eco/detail/:oid',
                name: 'eco-detail',
                component: () => import( /*webpackChunkName: "change-eco-detail"*/ 'views/change-management/eco/detail'),
            },
            {
                // 变更管理 eca创建
                path: '/change-management/eca/create/:oid',
                name: 'eca-create',
                component: () => import( /*webpackChunkName: "change-eca-create"*/ 'views/change-management/eca/create'),
            },
            {
                // 变更管理 eca编辑
                path: '/change-management/eca/edit/:oid',
                name: 'eca-edit',
                component: () => import( /*webpackChunkName: "change-eca-edit"*/ 'views/change-management/eca/edit'),
            },
            {
                // 变更管理 eca详情
                path: '/change-management/eca/detail/:oid',
                name: 'eca-detail',
                component: () => import( /*webpackChunkName: "change-eca-detail"*/ 'views/change-management/eca/detail'),
            },
            {
                  // MCAD属性映射规则
                  path: '/mcad-attribute-mapping',
                  name: 'mcad-attribute-mapping',
                  component: () => import( /*webpackChunkName: "mcad-attribute-mapping"*/ 'views/mcad-attribute-mapping'),

            },
            {
                // 首选项配置
                path: '/sysconfig-manage',
                name: 'sysconfig-manage',
                component: () => import( /*webpackChunkName: "sysconfig-manage"*/ 'views/sysconfig-manage'),
            },
            {
                // 问题管理
                path: '/problem-manage',
                name: 'problem-manage',
                component: () => import( /*webpackChunkName: "problem-manage"*/ 'views/problem-manage'),
            },
            {
                // 问题详情
                path: '/problem-detail',
                name: 'problem-detail',
                component: () => import( /*webpackChunkName: "problem-detail"*/ 'views/problem-manage/problem-detail'),
            },
            {
                // 问题报告
                path: '/problem-report',
                name: 'problem-report',
                component: () => import( /*webpackChunkName: "problem-report"*/ 'views/problem-manage/problem-report'),
            },
            {
                //构型有效性管理
                hidden: true,
                path: '/effectivity-definition',
                name: 'effectivity-definition',
                component: () => import( /*webpackChunkName: "effectivity-definition"*/ 'views/effectivity-definition')
            },
            {
                //SETL迁移工具
                path: '/setl',
                name: 'setl',
                component: () => import( /*webpackChunkName: "setl"*/ 'views/setl')
            },
            {
                path: '/site-supplier',
                name: 'site-supplier',
                component: () => import( /*webpackChunkName: "setl"*/ 'views/site-system-supplier')
            },
            {
                path: '/supplier-detail',
                name: 'supplier-detail',
                component: () => import( /*webpackChunkName: "setl"*/ 'views/site-system-supplier/supplier-detail')
            },
            {
                path: '/batch_product_folder',
                name: 'batch_product_folder',
                component: () => import( /*webpackChunkName: "batch_product_folder"*/ 'views/site-management/batch-product-folder'),
            },
            {
                path: '/data-dictionary-config',
                name: 'dataDictionaryConfig',
                component: () => import( /*webpackChunkName: "batch_product_folder"*/ 'views/site-management/data-dictionary-config'),
            },
            {
                // 批量导入U9-BOM
                path: '/batch-import-bom',
                name: 'batchImportBom',
                component: () => import( /*webpackChunkName: "batch_product_folder"*/ 'views/site-management/batch-import-bom'),
            }
        ]
    },
    // {
    //     path: '/noPermission',
    //     name: 'no-permission',
    //     component: () => import(/*webpackChunkName: "noPermission"*/'views/no-permission'),
    // },
    {
        // hidden:true,
        path: '/3d-model-show/:oid',
        name: '3d-model-show',
        component: () => import( /*webpackChunkName: "3d-model-show"*/ 'views/model-show'),
    },

    //对接使用页面
    {
        path: '/bomview',
        name: 'bomview',
        component: () => import( /*webpackChunkName: "bomview"*/ 'views/dockingpage/bomview'),
    },
    {
        path: '/perApp',
        name: 'perApp',
        component: () => import( /*webpackChunkName: "bomview"*/ 'views/perApp'),
    }
]

export default routersList
