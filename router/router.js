/**
 *路由配置样例 
 * 
 * <AUTHOR>
 * @date 2019-01-25
 * @example
 */
import 'assets/style/index.less';
import 'assets/style/splitpanes.css'
import { init as initRouter } from './index';
import initLogin from 'views/login';

//兼容es6以上 因老版浏览器不支持的方法
import 'common/extendPrototype'


// 分类管理
import { init as initClassify } from 'jw_frame/modules/classify';
//三员管理
import { init as initPolicyManagement } from 'jw_frame/modules/policy-management';
// 系统管理
import { init as initSystemManagement } from 'jw_frame/modules/system-management';
// 安全管理
import { init as initSafetyManagement } from 'jw_frame/modules/safety-management';
// 审计管理
import { init as initAuditManagement } from 'jw_frame/modules/audit-management';
//组织管理
import { init as initSiteManagement } from 'jw_frame/modules/site-management';
initRouter();
initLogin();

initClassify();
initPolicyManagement()
initSystemManagement()
initSafetyManagement()
initAuditManagement()
initSiteManagement()
export default []
