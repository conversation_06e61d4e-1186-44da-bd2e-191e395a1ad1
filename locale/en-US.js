export default {
  btn_confirm: "Confirm",
  btn_edit: "Edit",
  btn_save: "Save",
  btn_cancel: "Cancel",
  btn_clear: "Clear",
  btn_upload: "Upload",
  btn_download: "Download",
  btn_export: "Export",
  btn_import: "Import",
  btn_create: "Create",
  btn_delete: "Delete",
  btn_search: "Search",
  btn_reset: "Reset",
  btn_on: "On",
  btn_off: "Off",
  btn_submit: "Submit",
  btn_back: "Back",
  btn_showColumn: "Column",
  btn_closePopup: "Close Popup",
  btn_more: "More",
  btn_yes: "Yes",
  btn_no: "No",
  btn_menuDashboard: "Dashboard",
  btn_menuProductLibrary: "Product Library",
  btn_menuSaveLibrary: "Save Library",
  btn_menuMySpace: "My Space",
  btn_menuTaskList: "Task List",
  btn_menuBusinessManagement: "Business Management",
  btn_menuQuotation: "Quotation",
  btn_menuBasicMessage: "Basic Message",
  btn_checkOut: "Check Out",
  btn_start: "Start",
  btn_new_add: "Add",
  msg_success: "Success",
  msg_failed: "Failed",
  msg_input: "Please Input",
  msg_select: "Please Select",
  msg_startTime: "Start time",
  msg_endTime: "End time",
  msg_required: "Required",
  msg_cannotDrop: "Cannot drag into leaf nodes",
  msg_nodata: "No data",
  msg_keyWord: "Keyword",
  txt_isAllFetchData: "Data is all loaded",
  txt_all: "Total",
  txt_alldata: "All",
  txt_multipleData: "Data",
  txt_isChoose: "Is choose",
  txt_sureCancel: "Are you sure cancel?",
  txt_sureDelete: "Are you sure delete?",
  txt_customerName: "Customer Name",
  txt_director: "Director",
  txt_quotationInformation: "Quotation Information",
  txt_quotationName: "Quotation Name",
  txt_quotationNumber: "Quotation Number",
  txt_Information: "Associated business opportunity information",
  txt_customerNumber: "Customer Number",
  txt_contactAddress: "Contact Address",
  txt_customerContact: "Customer Contact",
  txt_telephone: "Telephone",
  txt_fax: "Fax",
  txt_email: "Email",
  txt_purchaseIntention: "Purchase Intention",
  txt_enclosure: "Enclosure",
  txt_unitPrice: "Unit Price",
  txt_discount: "Discount",
  txt_total: "Total",
  txt_materialName: "Material Name",
  txt_materialNumber: "Material Number",
  txt_organizationalNumber: "Organizational Number",
  txt_organizationalName: "Organizational Name",
  txt_storageLocationNumber: "Storage Location Number",
  txt_storageLocationName: "Storage Location Name",
  txt_documentTypeName: "Document Type Name",
  txt_oddNumbers: "Odd Numbers",
  txt_creatorEmailPrefix: "Creator Email Prefix",
  txt_cataloguePrice: "Catalogue Price",
  txt_customGrouping: "Custom Grouping",
  txt_createGrouping: "Create Grouping",
  txt_existGrouping: "Exist Grouping",
  txt_groupName: "Group Name",
  txt_name: "Name",
  txt_stock_msg:"U9 Stock Message",
  txt_document_msg:"U9 Document Message",
  txt_code: "Code",
  txt_unit: "Unit",
  txt_screen_obj:"Create Screen PartIteration",
  txt_screen:"Screen PartIteration",
  txt_forming_obj:"Create Pre Forming Materials",
  txt_forming:"Create Pre Forming Materials",
  txt_origin: "Origin",
  txt_classification: "Classification code",
  txt_describe: "Describe",
  txt_type: "Type",
  txt_version: "Version",
  txt_view: "View",
  txt_lifeCycle: "Life Cycle",
  txt_status: "Status",
  txt_quantity: "Quantity",
  txt_lineNumber: "Line Number",
  txt_placeNumber: "Place Number",
  txt_twoWaySubstitution: "Two Way Substitution",
  txt_addSubassemblies: "Add Subassemblies",
  txt_confirmAdd: "Confirm Add",
  txt_contentManagement: "Content Management",
  txt_product_structure: "Product Structure",
  txt_baselineManagement: "Baseline Management",
  txt_changeManagement: "Change Management",
  txt_showContents: "Show Contents",
  txt_bomImport: "BOM Import",
  txt_bomExport: "BOM Export",
  txt_move: "Move",
  txt_rename: "Rename",
  txt_detail: "Detail",
  txt_structure: "Structure",
  txt_selectObjects: "Select Objects",
  txt_selectWorkflow: "Select Workflow",
  txt_submitForReview: "Submit For Review",
  txt_standardizationReview: "Standardization Review",
  txt_processReview: "Process Review",
  txt_lastStep: "Last Step",
  txt_globalAlternatives: "Global Alternatives",
  txt_specificAlternatives: "Specific Alternatives",
  txt_relationshipChain: "Relationship Chain",
  txt_objectList: "Object List",
  txt_historyRecord: "History Record",
  txt_workflowStart: "Workflow Start",
  txt_addToBaseline: "Add To Baseline",
  txt_file: "File",
  txt_designDocument: "Design Document",
  txt_projectDocument: "Project Document",
  txt_detailedDesign: "Detailed Design",
  txt_projectApplicationForm: "Project Application Form",
  txt_productManage: "Product Manage",
  txt_createDate: "Create Time",
  txt_operation: "Operation",
  txt_storageGroup: "Storage Group",
  txt_storageName: "Storage Name",
  txt_folder: "Folder",
  txt_fileObject: "File Object",
  txt_createGroup: "Create Group",
  txt_allStorage: "All Storage",
  txt_supplierManagement: "Supplier Management",
  txt_featureDefinition: "Feature Definition",
  txt_size: "Size",
  txt_color: "Color",
  txt_taskCount: "Task Count",
  txt_pending: "Pending",
  txt_overdue: "Overdue",
  txt_finish: "Finish",
  txt_ongoing: "Ongoing",
  txt_termination: "Termination",
  txt_cancel: "Cancel",
  txt_proportion: "Proportion",
  txt_myCollection: "My Collection",
  txt_recentBrowsing: "Recent Browsing",
  txt_recentUpdates: "Recent Updates",
  txt_myChecked: "My Checked",
  txt_taskStatistics: "Task Statistics",
  txt_changeStatistics: "Change Statistics",
  txt_myProducts: "Products I Participate In",
  txt_myLatestNews: "My Latest News",
  txt_taskTodo: "Task Todo",
  txt_myWorkflow: "My Workflow",
  txt_finishedTask: "Finish The Task",
  txt_processName: "ProcessName",
  txt_taskName: "TaskName",
  txt_processingRecords: "ProcessingRecords",
  txt_processingTime: "Processing Time",
  txt_handler: "Handler",
  txt_endTime: "End Time",
  txt_overdueTime: "Overdue Time",
  txt_duration: "Duration",
  txt_flowChart: "FlowChart",
  txt_taskInformation: "TaskInformation",
  txt_addProcessing: "AddProcessing",
  txt_maintenanceUsers: "Maintenance Users",
  txt_associatedPart: "Associated Part",
  txt_copy_mobile: "Copy move",
  txt_edtor_CEA: "Create ECA",
  txt_model_number: "Model Number",
  txt_responsible: "Responsible",
  text_genericType: "Generic Type",
  txt_batch_operation_filter:
    "Filtered data cannot be operated and checked out status data",
  txt_link: "Relevance",
  txt_not_link: "Associated object not found",
  txt_data_migration: "Data migration",
  txt_max_length_50: "The length of characters cannot exceed 50",
  txt_source_system_configuration: "Source system configuration",
  txt_target_system_configuration: "Target system configuration",
  txt_system: "System",
  txt_system_version: "System Version",
  txt_data_base: "Data base",
  txt_data_base_version: "Data base version",
  txt_data_base_url: "Data base URL",
  txt_data_base_schema: "Data base schema",
  txt_test_link: "Test Link",
  txt_create_classification: "Create classification",
  txt_edit_classification: "Edit classification",
  txt_please_create_classification: "No data, please create classification",
  txt_create_rules: "Create rules",
  txt_edit_rules: "Edit rules",
  txt_data_type: "Data type",
  txt_link_succeeded: "Link succeeded",
  txt_link_failure: "Link failure",
  txt_number_of_rows_read: "Number of rows read in a single batch",
  txt_maximum_number_of_readers: "Maximum number of readers per batch",
  txt_number_of_rows_written: "Number of rows written in a single batch",
  txt_maximum_number_of_readers: "Writer maximum quantity per batch",
  txt_number_of_pipes: "Number of pipes",
  txt_number_of_tasks_executed: "Number of tasks executed at the same time",
  txt_transmission_speed: "Transmission speed (byte)",
  txt_transmission_speed_explain:
    "Control the speed of synchronization. Under the condition of operation, datax will try to maintain the transmission speed",
  txt_maximum_number_of_dirty_data:
    "Maximum number of dirty data records threshold",
  txt_maximum_number_of_dirty_data_explain:
    "The maximum number of dirty data records threshold. If it exceeds the threshold, the DataX Job will report an error and exit",
  txt_dirty_data_percentage_threshold: "Dirty data percentage threshold",
  txt_dirty_data_percentage_threshold_explain:
    "The percentage threshold of dirty data. If it exceeds the threshold, DataX Job will report an error and exit",
  txt_migration_plan: "Migration plan",
  txt_latest_branch_version: "Latest branch version",
  txt_all_versions: "All versions",
  txt_data_source_organization: "Data source organization",
  txt_target_library_organization: "Target library organization",
  txt_data_source_container: "Data source container",
  txt_target_library_container: "Target Library Container",
  txt_migratable_data_objects: "Migratable data objects",
  txt_migration_task: "Migration task",
  txt_create_migration_task: "Create migration task",
  txt_edit_migration_task: "Edit migration task",
  txt_file_migration_configuration: "File migration configuration",
  txt_migration_scheme_configuration: "Migration rules configuration",
  txt_task_information_configuration: "Task information configuration",
  txt_you_sure_perform_task: "Are you sure you want to perform this task?",
  txt_exception_handling: "Exception handling",
  txt_group: "Divide into groups",
  txt_please_upload_the_file: "Please upload the file",
  txt_bucket_name: "Bucket name",
  txt_minio_url: "Minio URL",
  txt_visualization: "Visualization",
  txt_migration_Log: "Migration Log",
  txt_supplier: "Supplier Manage",
  txt_replacement_bom: "Replace",
  txt_upload_model_file: "Import Model File",
  txt_delete_supplier: "Make Sure Delete This Supplier Data?",
  txt_delete_supplier_part: "Make Sure Delete This Supplier Association Data",
  txt_create_supplier_part: "Related Vendor Part Number",
  txt_create_supplier: "Create Supplier",

  local_tool_open: "Local Tools Open",

  txt_view_replacement_parts: "View replacement parts",
  txt_create_supplier_fail: "Add Supplier Fail",
  txt_supplier_association: "Supplier Information",
  txt_supplier_name: "Supplier Name",
  txt_supplier_number: "Supplier Number",
  txt_supplier_logo: "Supplier Logo",
  txt_supplier_abbreviation: "Supplier Abbreviation",
  txt_address: "Address",
  txt_contacts: "Contacts",
  txt_contacts_information: "Contacts Information",
  txt_certification_level: "Certification Level",
  txt_supplier_type: "Supplier Type",
  change_stage: "Transition",
  stage_column: "Stage",
  change_stage_input: "Please select the stage to convert",
  txt_attribute: "Attribute",
  txt_attribule_value_syn: "Attribute value synthesis",
  txt_system_prop: "System properties",
  txt_extended_prop: "Extended attribute",
  txt_classification_prop: "Classification Properties",
  txt_connector: "Connector",
  txt_attribule_value: "Composite attribute",
  txt_attribute_rules: "Attribute Rules",
  txt_attribute_detail: "Attribute Details",
  part_document_import: "Import component attachments",
  txt_materials_number: "Similar material code",
  txt_materials_name: "Name of similar materials",
  txt_materials_version: "Similar material versions",
  txt_materials_stats: "Similar material status",
  txt_materials: "Similar materials",
  txt_continue_create: "Continue creating",
  txt_create_cancel: "Cancel creation",
  txt_continue_save: "Continue saving as",
  txt_save_cancel: "Cancel Save As",
  txt_is_twoway: "Two-way",
}
