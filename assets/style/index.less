// 缩略图样式
.thumbSlotclass {
    padding: 0;
    .vxe-cell {
        padding: 0 !important;
        margin: 0;
        height: 100%;
        width: 40px !important;
    }
}

/* iconfont */
.iconfont {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}

.font-700 {
    font-weight: 700;
}

.color45 {
    color: rgba(30, 32, 42, 0.45);
}

.color65 {
    color: rgba(30, 32, 42, 0.65);
}

.color85 {
    color: rgba(30, 32, 42, 0.85);
}

::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    -webkit-appearance: none;
}

::-webkit-scrollbar-track {
    background-color: transparent;
}

::-webkit-scrollbar-thumb {
    border-radius: 7px;
    background-color: #ABAEB4;
}

.scrollbar {
    overflow: overlay;
}

.scrollbar::-webkit-scrollbar-thumb {
    background-color: transparent;
}

.scrollbar:hover::-webkit-scrollbar-thumb {
    background-color: #ABAEB4;
}

.h-full {
    overflow: hidden;
    height: calc(100vh - 64px - 39px);
}

.h-full>.ant-row {
    height: 100%;
    margin: 0;
}

.h-full>.ant-row>.ant-col {
    height: 100%;
}

.h-full>.ant-row>.ant-col>.group-custom {
    height: 100%;
    background: var(--light);
    box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
    border-radius: 4px;
    padding: 0px;
}

.h-full .main-info {
    height: 100%;
    background: var(--light);
    box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
    border-radius: 4px;
    padding: 0px;
}

/*
backround-color
*/
.all-background {
    height: 100%;
    background: #fff;
    // box-shadow: 0 2px 8px 0 rgba(30, 32, 42, 0.25);
    border-radius: 4px;
    padding: 20px;
}


/* ↓↓↓↓↓↓↓↓↓老版本样式开始↓↓↓↓↓↓↓↓↓ */
/* 弹性布局 */
/* 弹性布局 */
.flex {
    display: flex;
}

.inline-flex {
    display: inline-flex;
}

.flex-fill {
    flex: 1 1 auto;
}

.flex-item {
    flex: 1;
}

/* 方向 (水平，垂直，反水平，反垂直) */
.flex-row {
    flex-direction: row;
}

.flex-column {
    flex-direction: column;
}

.flex-row-reverse {
    flex-direction: row-reverse;
}

.flex-column-reverse {
    flex-direction: column-reverse;
}

/* 对齐 (上，下，中，基线，拉伸) */
.align-start {
    align-items: flex-start;
}

.align-end {
    align-items: flex-end;
}

.align-center {
    align-items: center;
}

.align-baseline {
    align-items: baseline;
}

.align-stretch {
    align-items: stretch;
}

/* 内容对齐 (起始，结尾，中间，两端，间隔) */
.justify-start {
    justify-content: flex-start;
}

.justify-end {
    justify-content: flex-end;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-around {
    justify-content: space-around;
}

/* 定位 */
.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.fixed {
    position: fixed;
}

/* 盒子 */
.inline-block {
    display: inline-block;
}

/* 内容限制1行，超出... */
.line-clamp {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
}

/* 文本对齐 (左，中，右) */
.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}

/* 文本 (换行，溢出) */
.text-wrap {
    white-space: normal;
}

.text-nowrap {
    white-space: nowrap;
}

/* 文本，单词 (截断) */
.text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-break {
    word-break: break-word;
    overflow-wrap: break-word;
}

/* 文本转换 (大写，小写，首字母大写) */
.text-lowercase {
    text-transform: lowercase;
}

.text-uppercase {
    text-transform: uppercase;
}

.text-capitalize {
    text-transform: capitalize;
}

/* A标签 (下划线，重置颜色，手形) */
.text-reset {
    color: inherit;
}

.text-decoration-none {
    text-decoration: none;
}

.text-link {
    cursor: pointer;
}

/* 字体大小 */
.font-20 {
    font-size: 20px;
}

.font-18 {
    font-size: 18px;
}

.font-16 {
    font-size: 16px;
}

.font-14 {
    font-size: 14px;
}

.font-12 {
    font-size: 12px;
}

/* 边框 (上，右，下，左) */
.border {
    border: 1px solid #d6d7d9;
}

.border-top {
    border-top: 1px solid #d6d7d9;
}

.border-right {
    border-right: 1px solid #d6d7d9;
}

.border-bottom {
    border-bottom: 1px solid #d6d7d9;
}

.border-left {
    border-left: 1px solid #d6d7d9;
}

.border-0 {
    border: 0;
}

.border-top-0 {
    border-top: 0;
}

.border-right-0 {
    border-right: 0;
}

.border-bottom-0 {
    border-bottom: 0;
}

.border-left-0 {
    border-left: 0;
}

/* 浮动 (左，右，清除) */
.left {
    float: left;
}

.right {
    float: right;
}

.clearfix::after {
    display: block;
    content: "";
    clear: both;
}

/* 宽度 */
.width-full {
    width: 100%;
}

.height-full {
    height: 100%;
}

/* 间隔 */
.margin-z-15 {
    margin: 15px;
}

.margin-x-15 {
    margin: 0 15px;
}

.margin-y-15 {
    margin: 15px 0;
}

.margin-top-15 {
    margin-top: 15px;
}

.margin-right-15 {
    margin-right: 15px;
}

.margin-bottom-15 {
    margin-bottom: 15px;
}

.margin-left-15 {
    margin-left: 15px;
}

.padding-z-15 {
    padding: 15px;
}

.padding-x-15 {
    padding: 0 15px;
}

.padding-y-15 {
    padding: 15px 0;
}

.padding-top-15 {
    padding-top: 15px;
}

.padding-right-15 {
    padding-right: 15px;
}

.padding-bottom-15 {
    padding-bottom: 15px;
}

.padding-left-15 {
    padding-left: 15px;
}

/* 加载 */
.loading-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
}

.loading-dots {
    position: relative;
    width: 48px;
    height: 48px;
    margin: 30px 0;
    transform: rotate(45deg);
    animation: runRotate 1.2s infinite linear;
    font-size: 32px;
}

.loading-dots i {
    position: absolute;
    display: block;
    width: 20px;
    height: 20px;
    background-color: #0065cc;
    border-radius: 100%;
    opacity: 0.3;
    transform: scale(0.75);
    transform-origin: 50% 50%;
    animation: runOpacity 1s infinite linear alternate;
}

.loading-dots i:nth-child(1) {
    top: 0;
    left: 0;
}

.loading-dots i:nth-child(2) {
    top: 0;
    right: 0;
    animation-delay: 0.4s;
}

.loading-dots i:nth-child(3) {
    right: 0;
    bottom: 0;
    animation-delay: 0.8s;
}

.loading-dots i:nth-child(4) {
    left: 0;
    bottom: 0;
    animation-delay: 1.2s;
}

.loading-title {
    color: #000000d9;
    font-size: 30px;
}

@keyframes runRotate {
    to {
        -webkit-transform: rotate(405deg);
        transform: rotate(405deg);
    }
}

@keyframes runOpacity {
    to {
        opacity: 1;
    }
}


/* ↑↑↑↑↑↑老版本样式结束↑↑↑↑↑↑ */

.jw-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;

    .ant-tabs-nav {
        margin-left: 16px;
    }

    .ant-tabs-bar {
        margin-bottom: 0;

        .ant-tabs-extra-content {
            line-height: 43px;
            padding-right: 10px;
        }
    }

    .ant-tabs-content {
        flex: 1;
        min-height: 0;

        .ant-tabs-tabpane {
            overflow-y: auto;
        }
    }
}



.vxe-context-menu--link  {
    .vxe-context-menu--link-prefix,
    .vxe-context-menu--link-suffix{
        top: -1px;
    }
   
}

.ant-pagination-options-quick-jumper{
    margin-left:15px;
}
.table-tag{
    padding:5px 7px !important;
    margin-left:8px !important;
    margin-right: 0 !important;
}