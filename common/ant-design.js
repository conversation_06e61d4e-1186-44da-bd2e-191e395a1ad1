/**
 * 注入ant-design组件
 *
 * <AUTHOR>
 * @date 2020-05-26
 */
// import Antd from 'ant-design-vue';
import { 
  // Base,
  // Affix,
  // Anchor,
  AutoComplete,
  // Alert,
  Avatar,
  // BackTop,
  Badge,
  Breadcrumb,
  Button,
  Calendar,
  Card,
  Collapse,
  Carousel,
  Cascader,
  Checkbox,
  Col,
  DatePicker,
  Divider,
  Dropdown,
  // Form,
  FormModel,
  Icon,
  Input,
  InputNumber,
  Layout,
  List,
  // LocaleProvider,
  Menu,
  // Mentions,
  Modal,
  message,
  Pagination,
  Popconfirm,
  Popover,
  // Progress,
  Radio,
  // Rate,
  Row,
  Select,
  // Slider,
  Spin,
  // Statistic,
  Steps,
  Switch,
  Table,
  Transfer,
  Tree,
  TreeSelect,
  Tabs,
  Tag,
  // TimePicker,
  Timeline,
  Tooltip,
  Upload,
  Drawer,
  // Skeleton,
  // Comment,
  // ColorPicker,
  ConfigProvider,
  Empty,
  // Result,
  Descriptions,
  // PageHeader,
  // Space,
  Form,
  Anchor,
} from 'ant-design-vue';

import { jwTable,jwIcon,jwPage } from 'jw_frame'

const injectVuePlugin = (vue) => {
  vue.use(jwTable)
  vue.use(jwIcon)
  vue.use(jwPage)
  vue.use(Button)
  vue.use(Card)
  vue.use(Collapse)
  vue.use(AutoComplete)
  vue.use(Checkbox)
  vue.use(Col)
  vue.use(DatePicker)
  vue.use(Divider)
  vue.use(Dropdown)
  vue.use(FormModel)
  vue.use(Icon)
  vue.use(Input)
  vue.use(Layout)
  vue.use(List)
  vue.use(Menu)
  vue.use(Modal)
  vue.use(Breadcrumb)
  vue.use(message)
  vue.use(Radio)
  vue.use(Row)
  vue.use(Carousel)
  vue.use(Cascader)
  vue.use(Select)
  vue.use(Spin)
  vue.use(InputNumber)
  vue.use(Steps)
  vue.use(Switch)
  vue.use(Table)
  vue.use(Popover)
  vue.use(Pagination)
  vue.use(Popconfirm)
  vue.use(Tree)
  vue.use(TreeSelect)
  vue.use(Tabs)
  vue.use(ConfigProvider)
  vue.use(Empty)
  vue.use(Descriptions)
  vue.use(Tooltip)
  vue.use(Drawer)
  vue.use(Upload)
  vue.use(Tag)
  vue.use(Avatar)
  vue.use(Timeline)
  vue.use(Calendar)
  vue.use(Badge)
  vue.use(Transfer)
  vue.use(Form)
  vue.use(Anchor)
  _.extend(vue.prototype, {
    _,
    $success(msg) {
      message.success(msg || '')
    },

    $warning(msg) {
      message.warning(msg || '')
    },

    $error(msg) {
      message.error(msg || '')
    },
    $confirm(obj){
      Modal.confirm(obj)
    }
  })
}

export default injectVuePlugin