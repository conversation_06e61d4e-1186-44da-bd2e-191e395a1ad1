
// * @Author:<EMAIL>
// * @Date: 2022/06/08 11:38:18
// * @LastEditors:  <EMAIL>
// * @LastEditTime: 2022/06/08 11:38:18
// * @Description: 


if (!Array.prototype.at) {
    Array.prototype.at = function (index) {
        if (index >= 0)
            return this[index];
        else return this[this.length + index];
    }
}

if (!String.prototype.replaceAll) {
    String.prototype.replaceAll = function () {
        return this.replace(new RegExp(s1, "gm"), s2);
    }
}
